{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/product.vue?9fef", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/product.vue?fa4b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/product.vue?76f2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/product.vue?0ebd", "uni-app:///carhailing/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/product.vue?e529", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/product.vue?8f39"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "console", "imageUrl", "query", "onUnload", "clearInterval", "onReachBottom", "methods", "lower", "getdata", "that", "app", "id", "dateIndex", "uni", "showdescClick", "hidedescClick", "isagreeChange", "swiper<PERSON><PERSON>e", "payvideo", "parsevideo", "buydialogChange", "addfavorite", "proid", "type", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "onPageScroll", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata", "sharelink", "showsubqrcode", "closesubqrcode", "tobuy", "text"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AAC6M;AAC7M,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpGA;AAAA;AAAA;AAAA;AAAszB,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuK10B;AACA;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,0DACA,uDACA,kDACA,oDACA,oDACA,wDACA,kDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,uDACA,wDACA,2DACA,yDACA,mDACA,kDACA,qDACA,oDACA,wDACA,wDACA;EAEA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACAE;IACAA;IACA;MACAH;MACAI;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;UACAC;UACA;QACA;QACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UAAA;UACAA;QACA;QACA;UAAA;UACAA;QACA;QACA;UAAA;UACAA;QACA;QAEAI;UACAhB;QACA;QACAY;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAAZ;UAAAC;QAAA;MACA;IACA;IACAgB;MACA;QAAA;QACA;MACA;MACA;QAAA;QACA;MACA;MACA;QAAA;QACA;MACA;MAEA;IACA;IACAC;MACA;MACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACAhB;IACA;IACAiB;MACA;MACAR;IACA;IACAS;MACA;MACAL;IACA;IACAM;MACA;MACAN;IACA;IACAO;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAX;QAAAY;QAAAC;MAAA;QACA;UACAd;QACA;QACAC;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAjB;MACAA;MACAC;MACAA;QAAAY;MAAA;QACAZ;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IAEAkB;MACA;IACA;IACAC;MACA;MACA;MACA;QACAnB;MACA;MACA;QACAA;MACA;IACA;IAEAoB;MACAnB;MACA;IACA;IACAoB;MACA;MACArB;MACAI;QACAkB;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACArB;UACA;QACA;MACA;IACA;IACAuB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA5B;QAAA;MACA;MACA;QACA;UAAA;UACA6B;UACA;QACA;QACA;UAAA;UACAA;UACA;QACA;QACA;UAAA;UACAA;UACA;QACA;QAEA;UACA;UACA;QACA;MACA;MACA7B;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACtcA;AAAA;AAAA;AAAA;AAA6pC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAjrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "carhailing/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './carhailing/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=3e1de520&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"carhailing/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=3e1de520&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    scrolltop: function () {\n      return import(\n        /* webpackChunkName: \"components/scrolltop/scrolltop\" */ \"@/components/scrolltop/scrolltop.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null\n  var g1 =\n    _vm.isload && _vm.isplay == 0 && g0 > 1 ? _vm.product.pics.length : null\n  var m0 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    _vm.product.cid == 2 &&\n    !(_vm.product.yystatus == 0 && _vm.product.leftnum > 0) &&\n    !(_vm.product.leftnum <= 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && _vm.product.status == 1 && !(_vm.product.cid == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m2 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m3 =\n    _vm.isload && _vm.sharetypevisible && !(m2 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m4 =\n    _vm.isload && _vm.sharetypevisible && !(m2 == \"app\") && !(m3 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m5 = _vm.isload && _vm.showdesc ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && _vm.showdesc ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<scroll-view :scroll-top=\"scrollTop\" scroll-y=\"true\" class=\"scroll-Y\"  @scrolltolower=\"lower\" >\r\n\t\t<!-- <view style=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\" v-if=\"bboglist.length>0\">\r\n\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\" :vertical=\"true\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\" :data-url=\"'/pagesExt/yueke/product?id=' + item.proid\" class=\"flex-y-center\">\r\n\t\t\t\t\t<image :src=\"item.headimg\" style=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\"/>\r\n\t\t\t\t\t<view style=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">{{item.nickname}} {{item.showtime}}购买了该商品</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view> -->\r\n\t\t<!-- <view class=\"topbox\"><image :src=\"pre_url+'/static/img/goback.png'\" class=\"goback\" /></view> -->\r\n\t\t<view class=\"swiper-container\" v-if=\"isplay==0\" >\r\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"500000\" @change=\"swiperChange\">\r\n\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"imageCount\" v-if=\"(product.pics).length > 1\">{{current+1}}/{{(product.pics).length}}</view>\r\n\t\t\t<view v-if=\"product.video\" class=\"provideo\" @tap=\"payvideo\"><image :src=\"pre_url+'/static/img/video.png'\"/><view class=\"txt\">{{product.video_duration}}</view></view>\r\n\t\t</view>\r\n\t\r\n\t\t<view class=\"header\"> \r\n\t\t\t<view class=\"price_share\">\r\n\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pricebox flex\">\r\n\t\t\t\t<view class=\"sellpoint\">\r\n\t\t\t\t\t<text v-if=\"product.cid==1\">租车</text>\r\n\t\t\t\t\t<text v-if=\"product.cid==2\">拼车</text>\r\n\t\t\t\t\t<text v-if=\"product.cid==3\">包车</text>\r\n\t\t\t\t\t费用：￥{{product.sell_price}}\r\n\t\t\t\t\t <text v-if=\"product.cid ==2\"> /人</text><text v-else> /天</text> \r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\">{{product.sellpoint}}</view>\r\n\t\t\t<!-- <view class=\"sellpoint\" v-if=\"product.cid ==2\">时间：{{product.yy_date}} {{product.starttime}} ~ {{product.endtime}}</view> -->\r\n\t\t</view>\r\n\t\t<view class=\"module\" v-if=\"product.cid ==2\">\r\n\t\t\r\n\t\t\t<view class=\"module_num\">\r\n\t\t\t\t<view class=\"module_lable\">\r\n\t\t\t\t\t<view>当前</view>\r\n\t\t\t\t\t<view>预约</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"module_view\">\r\n\t\t\t\t\t<block v-for=\"(item2,index2) in yyorderlist\">\r\n\t\t\t\t\t\t<image :src=\"item2.headimg\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"module_tag\" v-if=\"product.leftnum > 0\">剩余{{product.leftnum}}个名额</view>\r\n\t\t\t\t<view class=\"module_tag module_end\" v-else>满员</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">预定须知</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t<view class=\"detail\">\r\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\r\n\t\t<view class=\"desc\" v-if=\"desc_status\">\r\n\t\t\t<checkbox-group @change=\"isagreeChange\" style=\"display: inline-block;\">\r\n\t\t\t    <checkbox style=\"transform: scale(0.6)\"  value=\"1\" :checked=\"isagree\" disabled=\"true\" />\r\n\t\t\t\t我已阅读\r\n\t\t\t    <text style=\"color: #3388FF\" @tap=\"showdescClick\">《{{product.cid==1?'租车':product.cid==2?'拼车':product.cid==3?'包车':''}}说明》</text>\r\n\t\t\t</checkbox-group>\r\n\t\t</view>\r\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':''\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'prolist?bid=' + product.bid\" >\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shou.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">首页</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\" >\r\n\t\t\t\t<block v-if=\"product.cid ==2\">\r\n\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:'#aaa'}\" v-if=\"product.yystatus ==0 && product.leftnum > 0\">非预约时间</view>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:'#aaa'}\" v-if=\"product.leftnum <=0\">满员</view>\r\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" @tap=\"tobuy\" :style=\"{background:t('color1')}\" v-else>立即预约</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" @tap=\"tobuy\" :style=\"{background:t('color1')}\" v-else>\r\n\t\t\t\t\t<text v-if=\"product.cid ==1\">立即租车</text><text v-if=\"product.cid ==3\">立即包车</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t<scrolltop :isshow=\"scrolltopshow\"></scrolltop>\r\n\r\n\t\t\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"showdesc\" class=\"xieyibox\">\r\n\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t<parse :content=\"desc\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidedescClick\">已阅读并同意</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tisload:false,\r\n\t\t\tisfavorite: false,\r\n\t\t\tcurrent: 0,\r\n\t\t\tisplay: 0,\r\n\t\t\tbusiness: \"\",\r\n\t\t\tproduct: [],\r\n\t\t\tpagecontent: \"\",\r\n\t\t\ttitle: \"\",\r\n\t\t\tsharepic: \"\",\r\n\t\t\tsharetypevisible: false,\r\n\t\t\tshowposter: false,\r\n\t\t\tposterpic: \"\",\r\n\t\t\tscrolltopshow: false,\r\n\t\t\tkfurl:'',\r\n\t\t\tworkerinfo:{},\r\n\t\t\tyyorderlist:[],\r\n\t\t\ttimeDialogShow:false,\r\n\t\t\tdateIndex:0,\r\n\t\t\tisagree:0,\r\n\t\t\tsysset:[],\r\n\t\t\tshowdesc:false,//弹窗显示\r\n\t\t\tdesc:'',//租车等内容\r\n\t\t\tdesc_status:1,//是否显示租车说明\r\n\t\t\tscrollBottom:0\r\n\t\t};\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt && this.opt.dateIndex) this.dateIndex = this.opt.dateIndex;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(shareOption){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\tconsole.log(sharewxdata)\r\n\t\tconsole.log(query)\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\tonReachBottom() {\r\n\t\tthis.scrollBottom = true;\r\n\t},\r\n\tmethods: {\r\n\t\tlower(){\r\n\t\t\tthis.scrollBottom = 1;\r\n\t\t},\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiCarHailing/product', {id:id,dateIndex:that.dateIndex}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.product = product;\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.title = product.name;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.workerinfo = res.workerinfo;\r\n\t\t\t\tthat.yyorderlist = res.yyorderlist;\r\n\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tif(that.product.cid ==1){//租车\r\n\t\t\t\t\tthat.desc_status = that.sysset.zc_desc_status;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.product.cid ==2){//拼车\r\n\t\t\t\t\tthat.desc_status = that.sysset.pc_desc_status;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.product.cid ==3){//包车\r\n\t\t\t\t\tthat.desc_status = that.sysset.bc_desc_status;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: product.name\r\n\t\t\t\t});\r\n\t\t\t\tthat.kfurl = '/pages/kefu/index?bid='+product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded({title:product.name,pic:product.pic});\r\n\t\t\t});\r\n\t\t},\r\n\t\tshowdescClick: function () {\r\n\t\t\tif(this.product.cid ==1){//租车\r\n\t\t\t\tthis.desc = this.sysset.zc_desc;\r\n\t\t\t}\r\n\t\t\tif(this.product.cid ==2){//拼车\r\n\t\t\t\tthis.desc = this.sysset.pc_desc;\r\n\t\t\t}\r\n\t\t\tif(this.product.cid ==3){//包车\r\n\t\t\t\tthis.desc = this.sysset.bc_desc;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t  this.showdesc = true;\r\n\t\t},\r\n\t\thidedescClick:function(){\r\n\t\t\tthis.showdesc = false;\r\n\t\t\tthis.isagree = 1;\r\n\t\t\r\n\t\t},\r\n\t\tisagreeChange: function (e) {\r\n\t\t  var val = e.detail.value;\r\n\t\t  if (val.length > 0) {\r\n\t\t    this.isagree = true;\r\n\t\t  } else {\r\n\t\t    this.isagree = false;\r\n\t\t  }\r\n\t\t  console.log(this.isagree);\r\n\t\t},\r\n\t\tswiperChange: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.current = e.detail.current\r\n\t\t},\r\n\t\tpayvideo: function () {\r\n\t\t\tthis.isplay = 1;\r\n\t\t\tuni.createVideoContext('video').play();\r\n\t\t},\r\n\t\tparsevideo: function () {\r\n\t\t\tthis.isplay = 0;\r\n\t\t\tuni.createVideoContext('video').stop();\r\n\t\t},\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype;\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\t//收藏操作\r\n\t\taddfavorite: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tapp.post('ApiCarHailing/addfavorite', {proid: proid,type: 'car_hailing'}, function (data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t}\r\n\t\t\t\tapp.success(data.msg);\r\n\t\t\t});\r\n\t\t},\r\n\t\tshareClick: function () {\r\n\t\t\tthis.sharetypevisible = true;\r\n\t\t},\r\n\t\thandleClickMask: function () {\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshowPoster: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.showposter = true;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tapp.showLoading('生成海报中');\r\n\t\t\tapp.post('ApiCarHailing/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tposterDialogClose: function () {\r\n\t\t\tthis.showposter = false;\r\n\t\t},\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar scrollY = e.scrollTop;     \r\n\t\t\tif (scrollY > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollY < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t},\t\r\n\t\t\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesExt/yueke/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/pagesExt/yueke/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t},\r\n\t\tshowsubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t},\r\n\t\tclosesubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t},\r\n\t\ttobuy: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar text = '';\r\n\t\t\tif(that.scrollBottom ==0){\r\n\t\t\t\tapp.error('请阅读预定须知');return;\r\n\t\t\t}\r\n\t\t\tif(that.desc_status){\r\n\t\t\t\tif(this.product.cid ==1){//租车\r\n\t\t\t\t\ttext = '租车';\r\n\t\t\t\t\tthis.desc = this.sysset.zc_desc;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.product.cid ==2){//拼车\r\n\t\t\t\t\ttext = '拼车';\r\n\t\t\t\t\tthis.desc = this.sysset.pc_desc;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.product.cid ==3){//包车\r\n\t\t\t\t\ttext = '包车';\r\n\t\t\t\t\tthis.desc = this.sysset.bc_desc;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\tif(!this.isagree){\r\n\t\t\t\t\tthis.showdesc = 1;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tapp.goto('buy?proid=' + this.product.id +'&dateIndex='+this.dateIndex);\r\n\t\t},\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\n\t.dp-cover{height: auto; position: relative;}\r\n\t.dp-cover-cover{position:fixed;z-index:99999;cursor:pointer;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color: inherit;}\r\n\t\r\n.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}\r\n.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}\r\n.follow_topbar .headimg image {height:64rpx; width:64rpx;}\r\n.follow_topbar .info {height:56rpx; padding:16rpx 0;}\r\n.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}\r\n.follow_topbar .info {height:80rpx; float:left;}\r\n.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}\r\n.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.qrcodebox .img{width:400rpx;height:400rpx}\r\n.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.goback{ position: absolute; top:0 ;width:64rpx ; height: 64rpx;z-index: 10000; margin: 30rpx;}\r\n.goback img{ width:64rpx ; height: 64rpx;}\r\n\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:80rpx;}\r\n\r\n.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\r\n.provideo image{width:50rpx;height:50rpx;}\r\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\r\n\r\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\r\n.videobox .video{width:100%;height:650rpx;}\r\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx;}\r\n\r\n.header {padding: 20rpx 3%;background: #fff; width: 94%; border-radius:10rpx; margin: auto; margin-bottom: 20rpx; margin-top: -60rpx; position: relative;}\r\n.header .pricebox{ width: 100%;border:1px solid #fff; justify-content: space-between;}\r\n.header .pricebox .price{display:flex;align-items:flex-end}\r\n.header .pricebox .price .f1{font-size:36rpx;color:#51B539;font-weight:bold}\r\n.header .pricebox .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}\r\n.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}\r\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\r\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\r\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\r\n.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}\r\n.header .sales_stock{height:60rpx;line-height:60rpx;font-size:24rpx;color:#777777; }\r\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\r\n\r\n.choosebox{margin: auto;width: 94%; border-radius:10rpx; background: #fff;  }\r\n\r\n.choose{ display:flex;align-items:center;justify-content: center; margin: auto; height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; border-bottom:1px solid #eee }\r\n.choose .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\r\n.choose .f2{ width: 32rpx; height: 32rpx;}\r\n\r\n\r\n.choosedate{ display:flex;align-items:center;ustify-content: center;   margin:auto; height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; }\r\n.choosedate .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\r\n.choosedate .f2{ width: 32rpx; height: 32rpx;}\r\n\r\n.cuxiaodiv{background:#fff;margin-top:20rpx;}\r\n\r\n.fuwupoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.fuwupoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\r\n.fuwupoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.fuwupoint .f1 .t{ padding:4rpx 20rpx 4rpx 0;color:#777;flex-shrink:0}\r\n.fuwupoint .f1 .t:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\r\n.fuwupoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.fuwupoint .f2 .img{width:32rpx;height:32rpx;}\r\n.fuwudialog-content{font-size:24rpx}\r\n.fuwudialog-content .f1{color:#333;height:80rpx;line-height:80rpx;font-weight:bold}\r\n.fuwudialog-content .f1:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\r\n.fuwudialog-content .f2{color:#777}\r\n\r\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\r\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\r\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\r\n.cuxiaopoint .f1 .t1{padding:0 4px}\r\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\r\n.cuxiaodiv .cuxiaopoint{border-bottom:1px solid #E6E6E6;}\r\n.cuxiaodiv .cuxiaopoint:last-child{border-bottom:0}\r\n\r\n.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}\r\n.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}\r\n.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}\r\n.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}\r\n.popup__title-text{font-size:32rpx}\r\n.popup__close{position:absolute;top:34rpx;right:34rpx}\r\n.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}\r\n.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.service-item .prefix{padding-top: 2px;}\r\n.service-item .suffix{padding-left: 10rpx;}\r\n.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx; width: 94%; margin: auto; border-radius: 10rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.commentbox{width:90%;background:#fff;padding:0 3%;border-radius:10rpx;margin: auto;margin-top:20rpx; }\r\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\r\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\r\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\r\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\r\n\r\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\r\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.comment .item .f2 .t2{display:flex;width:100%}\r\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\r\n\r\n.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 30rpx 0 10rpx;align-items:center;}\r\n.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}\r\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:50%;position:relative}\r\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\r\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\r\n.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; border-top-right-radius:20rpx;border-bottom-left-radius:20rpx;}\r\n.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}\r\n\r\n/*时间范围*/\r\n.datetab{ display: flex; border:1px solid red; width: 200rpx; text-align: center;}\r\n.order-tab{ }\r\n.order-tab2{display:flex;width:auto;min-width:100%}\r\n.order-tab2 .item{width:auto;font-size:28rpx;font-weight:bold;text-align: center; color:#999999;overflow: hidden;flex-shrink:0;flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center;}\r\n.order-tab2 .item .datetext{ line-height: 60rpx; height:60rpx;}\r\n.order-tab2 .item .datetext2{ line-height: 60rpx; height:60rpx;font-size: 22rpx;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;margin-left:-10rpx;bottom:5rpx;height:6rpx;border-radius:1.5px;width:70rpx}\r\n.order-tab2 .on .after{display:block}\r\n.daydate{ padding:20rpx; flex-wrap: wrap; overflow-y: scroll; height:400rpx; }\r\n.daydate .date{ width:20%;text-align: center;line-height: 60rpx;height: 60rpx; margin-top: 30rpx;}\r\n.daydate .on{ background:red; color:#fff;}\r\n.daydate .hui{ border:1px solid #f0f0f0; background:#f0f0f0;border-radius: 5rpx;}\r\n.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;border-radius:36rpx;}\r\n\r\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\r\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\r\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\r\n.cuxiaopoint .f1 .t1{padding:0 4px}\r\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\r\n.cuxiaodiv .cuxiaopoint{border-bottom:1px solid #E6E6E6;}\r\n.cuxiaodiv .cuxiaopoint:last-child{border-bottom:0}\r\n\r\n\r\n.module{position: relative;width: 700rpx;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;margin: 0 auto 30rpx auto;background: #fff;}\r\n.module_data{display: flex;}\r\n.module_img{height: 130rpx;width: 130rpx;margin-right: 30rpx;}\r\n.module_content{flex: 1;}\r\n.module_btn{height: 65rpx;padding: 0 40rpx;color: #fff;font-size: 28rpx;display: flex;align-items: center;justify-content: center;border-radius: 100rpx;background: #0993fe;}\r\n.module_title{font-size: 28rpx;color: #333;}\r\n.module_item{margin-top: 10rpx;color: #999;display: flex;align-items: center;font-size: 24rpx;}\r\n.module_time{padding: 0 10rpx;height: 35rpx;line-height: 33rpx;font-size: 22rpx;margin-left: 20rpx;color: #d55c5f;border: 1rpx solid #d55c5f;}\r\n.module_num{display: flex;align-items: center;margin-top: 20rpx;}\r\n.module_lable{font-size: 24rpx;color: #666;line-height: 24rpx;border-right: 1px solid #e0e0e0;padding: 0 15rpx 0 0;margin-right: 15rpx;}\r\n.module_view{display: flex;flex: 1;align-items: center;}\r\n.module_view image{height: 60rpx;width: 60rpx;border-radius: 100rpx;margin-right: 10rpx;}\r\n.module_tag{height: 50rpx;background: #fefae8;color: #b37e4b;font-size: 24rpx;padding: 0 10rpx;line-height: 50rpx;}\r\n.module_end{color: #999;background: #f0f0f0;}\r\n.desc{width: 100%;line-height: 80rpx;font-size: 24rpx;overflow: hidden;position: fixed;bottom: 100rpx;padding-left: 30rpx;background-color: #fff;text-align: center;}\r\n\r\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\r\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\r\n\r\n</style>", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839423422\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}