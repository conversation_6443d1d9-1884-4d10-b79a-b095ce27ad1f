{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/classify.vue?a95b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/classify.vue?511d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/classify.vue?bc37", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/classify.vue?e541", "uni-app:///carhailing/classify.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/classify.vue?5066", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/classify.vue?d679"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "bottom", "pagenum", "order", "field", "clist", "curTopIndex", "curIndex", "curIndex2", "datalist", "nopro", "curCid", "proid", "buydialogShow", "prodata", "userinfo", "nomore", "nodata", "bid", "showLinkStatus", "lx_name", "lx_bid", "lx_tel", "area", "pre_url", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "cid", "isget", "getdatalist", "wherefield", "uni", "scrolltolower", "changeCTab", "changeOrder", "switchRightTab", "switchTopTab", "buydialogChange", "showLinkChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC6M;AAC7M,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAAuzB,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0G30B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACAC;QAAAC;QAAAb;MAAA;QACAW;QACA;QACAA;QACAA;QACA;UACA;YACA;cACAA;cACAA;cACA;YACA;YACA;YACA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAG;gBACA;cACA;YACA;YACA;UACA;QACA;QACAH;QACAA;MACA;IACA;IACAI;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ;MACAA;MACAA;MACA;MACAK;MACAA;MACAA;MACAA;MACA;QACAA;MACA;QACAA;MACA;MACAJ;QACAD;QACAM;QACA;QAEA;UACA;YACAN;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MAEA;IACA;IACAO;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACAb;MACAA;MACAA;MACAA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1SA;AAAA;AAAA;AAAA;AAA8pC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAlrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "carhailing/classify.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './carhailing/classify.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./classify.vue?vue&type=template&id=51fad923&\"\nvar renderjs\nimport script from \"./classify.vue?vue&type=script&lang=js&\"\nexport * from \"./classify.vue?vue&type=script&lang=js&\"\nimport style0 from \"./classify.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"carhailing/classify.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./classify.vue?vue&type=template&id=51fad923&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 = _vm.isload && _vm.curIndex == -1 ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.clist[_vm.curTopIndex].child, function (item, index2) {\n        var $orig = _vm.__get_orig(item)\n        var m3 = _vm.curIndex == index2 ? _vm.t(\"color1\") : null\n        var m4 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m3: m3,\n          m4: m4,\n        }\n      })\n    : null\n  var m5 = _vm.showLinkStatus && _vm.lx_tel ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        l1: l1,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./classify.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./classify.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view @tap.stop=\"goto\" :data-url=\"'/carhailing/prolist?bid='+bid\" class=\"search-container\">\n\t\t\t<view class=\"search-box\">\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"\"></image>\n\t\t\t\t<view class=\"search-text\">搜索感兴趣的商品</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"order-tab\">\n\t\t\t<view class=\"order-tab2\">\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\">{{item.name}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"content-container\">\n\t\t\t<view class=\"nav_left\">\n\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == -1 ? 'active' : '')\" :style=\"{color:curIndex == -1?t('color1'):'#333'}\" @tap=\"switchRightTab\" data-index=\"-1\" :data-id=\"clist[curTopIndex].id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>全部</view>\n\t\t\t\t<block v-for=\"(item, index2) in clist[curTopIndex].child\" :key=\"index2\">\n\t\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == index2 ? 'active' : '')\" :style=\"{color:curIndex == index2?t('color1'):'#333'}\" @tap=\"switchRightTab\" :data-index=\"index2\" :data-id=\"item.id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t<view class=\"nav_right\">\n\t\t\t\t<view class=\"nav_right-content\">\n\t\t\t\t\t<!-- <view class=\"nav-pai\">\n\t\t\t\t\t\t<view class=\"nav-paili\" :style=\"{color:(!field||field=='sort')?t('color1'):'#323232'}\" @tap=\"changeOrder\" data-field=\"sort\" data-order=\"desc\">综合</view> \n\t\t\t\t\t\t<view class=\"nav-paili\" :style=\"field=='sales'?'color:'+t('color1'):''\" @tap=\"changeOrder\" data-field=\"sales\" data-order=\"desc\">销量</view> \n\t\t\t\t\t\t<view class=\"nav-paili\" @tap=\"changeOrder\" data-field=\"sell_price\" :data-order=\"order=='asc'?'desc':'asc'\">\n\t\t\t\t\t\t\t<text :style=\"field=='sell_price'?'color:'+t('color1'):''\">价格</text>\n\t\t\t\t\t\t\t<text class=\"iconfont iconshangla\" :style=\"field=='sell_price'&&order=='asc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t\t\t\t<text class=\"iconfont icondaoxu\" :style=\"field=='sell_price'&&order=='desc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t\t\t</view>  \n\t\t\t\t\t</view> -->\n\t\t\t\t\t\n\t\t\t\t\t<scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\n\t\t\t\t\t\t<view class=\"product-itemlist module\">\n\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in datalist\" :key=\"item.id\">\n\t\t\t\t\t\t\t\t<view class=\"module_data\" @tap=\"goto\" :data-url=\"'product?id='+item.id+''\">\n\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" class=\"module_img\" alt=\"\"/>\n\t\t\t\t\t\t\t\t\t<view class=\"module_content\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_title\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"module_item\"  v-if=\"item.cid ==2\"> <view class=\"module_time\">{{item.starttime}}~{{item.endtime}}</view></view> -->\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_item\" v-if=\"item.cid != 2 \">\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.cid==1\">租车</text><text v-else>包车</text>费用：<text class=\"txt-yel\">￥{{item.sell_price}} /天</text></view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_item\" v-else>拼车费用：<text class=\"txt-yel\">￥{{item.sell_price}} /人</text></view>\n\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"module_item\">所在城市：{{area}}</view> -->\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<!-- <view v-if=\"item.cid ==2\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_btn module_end\" v-if=\"item.isend\">已结束</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_btn\" v-else-if=\"item.leftnum > 0\">预约</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_btn module_end\" v-else>满员</view>\n\t\t\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t\t\t\t<!-- <view v-else-if=\"item.cid ==1\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_btn \" >租车</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t\t\t\t\t<view class=\"module_btn \" >包车</view>\n\t\t\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<!-- <view class=\"module_num\" v-if=\"item.cid ==2\">\n\t\t\t\t\t\t\t\t\t<view class=\"module_lable\">\n\t\t\t\t\t\t\t\t\t\t<view>当前</view>\n\t\t\t\t\t\t\t\t\t\t<view>预约</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"module_view\">\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(item2,index2) in item.yyorderlist\">\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item2.headimg\"/>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"module_tag\" v-if=\"item.leftnum > 0\">剩余{{item.leftnum}}个名额</view>\n\t\t\t\t\t\t\t\t\t<view class=\"module_tag module_end\" v-else>满员</view>\n\t\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\n\t\t\t\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\"></buydialog>\n    <view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\n    \t<view class=\"main\">\n    \t\t<view class=\"close\" @tap=\"showLinkChange\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\n    \t\t<view class=\"content\">\n    \t\t\t<view class=\"title\">{{lx_name}}</view>\n    \t\t\t<view class=\"row\" v-if=\"lx_bid > 0\">\n    \t\t\t\t<view class=\"f1\">店铺名称</view>\n    \t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+lx_bid\">{{lx_bname}}<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/></view>\n    \t\t\t</view>\n    \t\t\t<view class=\"row\" v-if=\"lx_tel\">\n    \t\t\t\t<view class=\"f1\">联系电话</view>\n    \t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'tel::'+lx_tel\" :style=\"{color:t('color1')}\">{{lx_tel}}<image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"lx_tel\"></image></view>\n    \t\t\t</view>\n    \t\t</view>\n    \t</view>\n    </view>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n      bottom: 0,\n      pagenum: 1,\n      order: '',\n      field: '',\n      clist: [],\n      curTopIndex: 0,\n      curIndex: -1,\n      curIndex2: -1,\n      datalist: [],\n      nopro: 0,\n      curCid: 0,\n\t\t\tproid:0,\n\t\t\tbuydialogShow: false,\n      prodata: [],\n      userinfo: [],\n      nomore: false,\n\t\t\tnodata: false,\n\t\t\tbid:'',\n            \n            showLinkStatus:false,\n            lx_name:'',\n            lx_bid:'',\n            lx_tel:'',\n\t\t\tarea:'',\n\t\t\tpre_url:app.globalData.pre_url,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.bid = this.opt.bid ? this.opt.bid  : '';\n\t\tthis.getdata();\n\t\tthis.area = app.getCache('user_current_area_show');\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tvar nowcid = that.opt.cid;\n\t\t\tif (!nowcid) nowcid = '';\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiCarHailing/classify2', {cid: nowcid,bid:that.bid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar data = res.data;\n\t\t\t\tthat.clist = data;\n\t\t\t\tthat.curCid = data[0]['id'];\n\t\t\t\tif (nowcid) {\n\t\t\t\t\tfor (var i = 0; i < data.length; i++) {\n\t\t\t\t\t\tif (data[i]['id'] == nowcid) {\n\t\t\t\t\t\t\tthat.curTopIndex = i;\n\t\t\t\t\t\t\tthat.curCid = nowcid;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar downcdata = data[i]['child'];\n\t\t\t\t\t\tvar isget = 0;\n\t\t\t\t\t\tfor (var j = 0; j < downcdata.length; j++) {\n\t\t\t\t\t\t\tif (downcdata[j]['id'] == nowcid) {\n\t\t\t\t\t\t\t\tthat.curIndex = i + 1;\n\t\t\t\t\t\t\t\tthat.curIndex2 = j;\n\t\t\t\t\t\t\t\tthat.curTopIndex = i;\n\t\t\t\t\t\t\t\tthat.curCid = nowcid;\n\t\t\t\t\t\t\t\tisget = 1;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (isget) break;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t\tthat.getdatalist();\n\t\t\t});\n\t\t},\n    getdatalist: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n\t\t\tvar that = this;\n\t\t\tvar pagenum = that.pagenum;\n\t\t\tvar cid = that.curCid;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n\t\t\tvar order = that.order;\n\t\t\tvar field = that.field; \n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\tvar wherefield = {};\n\t\t\twherefield.pagenum = pagenum;\n\t\t\twherefield.field = field;\n\t\t\twherefield.order = order;\n\t\t\twherefield.bid = bid;\n\t\t\tif(bid > 0){\n\t\t\t\twherefield.cid2 = cid;\n\t\t\t}else{\n\t\t\t\twherefield.cid = cid;\n\t\t\t}\n\t\t\tapp.post('ApiCarHailing/getprolist',wherefield, function (res) { \n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\tvar data = res.data;\n\t\t\t\t\n\t\t\t\tif (data.length == 0) {\n\t\t\t\t\tif(pagenum == 1){\n\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthat.nomore = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tvar datalist = that.datalist;\n\t\t\t\tvar newdata = datalist.concat(data);\n\t\t\t\tthat.datalist = newdata;\n\t\t\t\n\t\t\t});\n\t\t},\n\t\tscrolltolower: function () {\n\t\t\tif (!this.nomore) {\n\t\t\t\tthis.pagenum = this.pagenum + 1;    \n\t\t\t\tthis.getdatalist(true);\n\t\t\t}\n\t\t},\n\t\t//改变子分类\n\t\tchangeCTab: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\n\t\t\tthis.curIndex2 = index;\n\t\t\tthis.nodata = false;\n\t\t\tthis.curCid = id;\n\t\t\tthis.pagenum = 1;\n\t\t\tthis.datalist = [];\n\t\t\tthis.nomore = false;\n\t\t\tthis.getdatalist();\n\t\t},\n\t\t//改变排序规则\n\t\tchangeOrder: function (e) {\n\t\t\tvar t = e.currentTarget.dataset;\n\t\t\tthis.field = t.field; \n\t\t\tthis.order = t.order;\n\t\t\tthis.pagenum = 1;\n\t\t\tthis.datalist = []; \n\t\t\tthis.nomore = false;\t\n\t\t\tthis.getdatalist();\n\t\t},\n\t\t//事件处理函数\n\t\tswitchRightTab: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\n\t\t\tthis.curIndex = index;\n\t\t\tthis.curIndex2 = -1;\n\t\t\tthis.nodata = false;\n\t\t\tthis.curCid = id;\n\t\t\tthis.getdatalist();\n\t\t},\n    switchTopTab: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var index = parseInt(e.currentTarget.dataset.index);\n      this.curTopIndex = index;\n      this.curIndex = -1;\n      this.curIndex2 = -1;\n      this.prolist = [];\n      this.nopro = 0;\n      this.curCid = id;\n      this.getdatalist();\n    },\n\t\tbuydialogChange: function (e) {\n\t\t\tif(!this.buydialogShow){\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\n\t\t\t}\n\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t},\n        showLinkChange: function (e) {\n            var that = this;\n        \tthat.showLinkStatus = !that.showLinkStatus;\n            that.lx_name = e.currentTarget.dataset.lx_name;\n            that.lx_bid = e.currentTarget.dataset.lx_bid;\n            that.lx_bname = e.currentTarget.dataset.lx_bname;\n            that.lx_tel = e.currentTarget.dataset.lx_tel;\n        },\n  }\n};\n</script>\n<style>\npage {height:100%;}\n.container{width: 100%;height:100%;max-width:640px;background-color: #fff;color: #939393;display: flex;flex-direction:column}\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\n\n\n.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx}\n.order-tab2{display:flex;width:auto;min-width:100%}\n.order-tab2 .item{width:auto;padding:0 20rpx;font-size:30rpx;font-weight:bold;text-align: center; color:#999999; height:90rpx; line-height:90rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}\n.order-tab2 .on{color:#222222;}\n.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:1.5px;width:40rpx}\n.order-tab2 .on .after{display:block}\n\n.content-container{flex:1;height:100%;display:flex;overflow: hidden;}\n\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\n.nav_left .nav_left_items{line-height:50rpx;color:#333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\n.nav_left .nav_left_items.active .before{display:block}\n\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%}\n.nav-pai{ width: 100%;display:flex;align-items:center;justify-content:center;}\n.nav-paili{flex:1; text-align:center;color:#323232; font-size:28rpx;font-weight:bold;position: relative;height:80rpx;line-height:80rpx;}\n.nav-paili .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\n.nav-paili .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\n\n.classify-ul{width:100%;height:100rpx;padding:0 10rpx;}\n.classify-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:22rpx;color:#6C737F;font-size:20rpx;text-align: center;height:44rpx; line-height:44rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\n\n.classify-box{padding: 0 0 20rpx 0;width: 100%;height:calc(100% - 60rpx);overflow-y: scroll; border-top:1px solid #F5F6F8;}\n.classify-box .nav_right_items{ width:100%;border-bottom:1px #f4f4f4 solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\n\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;}\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}\n.product-itemlist .product-info .p4{width:56rpx;height:56rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:32rpx;height:56rpx;line-height:56rpx}\n::-webkit-scrollbar{width: 0;height: 0;color: transparent;}\n\n.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}\n\n.member{position: relative;border-radius: 8rpx;border: 1rpx solid #fd4a46;overflow: hidden;margin-top: 10rpx;box-sizing: content-box;}\n.member_lable{height: 100%;font-size: 22rpx;color: #fff;background: #fd4a46;padding: 0 10rpx;}\n.member_value{padding: 0 10rpx;font-size: 20rpx;color: #fd4a46;}\n.module{\n\t\tposition: relative;\n\t\tpadding: 30rpx 10rpx;\n\t\tbox-sizing: border-box;\n\t\tborder-radius: 20rpx;\n\t\tmargin: 0 auto 30rpx auto;\n\t\tbackground: #fff;\n\t}\n\t.module_data{\n\t\tdisplay: flex;\n\t}\n\t.module_img{\n\t\theight: 130rpx;\n\t\twidth: 130rpx;\n\t\tmargin-right: 30rpx;\n\t}\n\t.module_content{\n\t\tflex: 1;\n\t}\n\t.txt-yel{color:#FF9900}\n\t.module_btn{\n\t\theight: 65rpx;\n\t\tpadding: 0 40rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 100rpx;\n\t\tbackground: #0993fe;\n\t}\n\t.module_title{\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tline-height:36rpx;\n\t\theight:72rpx;\n\t\tdisplay:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;\n\t}\n\t.module_item{\n\t\tmargin-top: 10rpx;\n\t\tcolor: #999;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-size: 24rpx;\n\t}\n\t.module_time{\n\t\tpadding: 0 10rpx;\n\t\theight: 35rpx;\n\t\tline-height: 33rpx;\n\t\tfont-size: 22rpx;\n\t\tcolor: #d55c5f;\n\t\tborder: 1rpx solid #d55c5f;\n\t}\n\t.module_num{\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: 20rpx;\n\t}\n\t.module_lable{\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tline-height: 24rpx;\n\t\tborder-right: 1px solid #e0e0e0;\n\t\tpadding: 0 15rpx 0 0;\n\t\tmargin-right: 15rpx;\n\t}\n\t.module_view{\n\t\tdisplay: flex;\n\t\tflex: 1;\n\t\talign-items: center;\n\t}\n\t.module_view image{\n\t\theight: 60rpx;\n\t\twidth: 60rpx;\n\t\tborder-radius: 100rpx;\n\t\tmargin-right: 10rpx;\n\t}\n\t.module_tag{\n\t\theight: 50rpx;\n\t\tbackground: #fefae8;\n\t\tcolor: #b37e4b;\n\t\tfont-size: 24rpx;\n\t\tpadding: 0 10rpx;\n\t\tline-height: 50rpx;\n\t}\n\t.module_end{\n\t\tcolor: #999;\n\t\tbackground: #f0f0f0;\n\t}\n\t\n</style>", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./classify.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./classify.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839423445\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}