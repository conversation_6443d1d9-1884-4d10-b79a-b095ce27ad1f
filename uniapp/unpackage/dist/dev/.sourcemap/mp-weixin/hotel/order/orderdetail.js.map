{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderdetail.vue?9e71", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderdetail.vue?82ba", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderdetail.vue?9682", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderdetail.vue?f7b4", "uni-app:///hotel/order/orderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderdetail.vue?6e4c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderdetail.vue?fc38"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "prodata", "djs", "detail", "storeinfo", "lefttime", "codtxt", "hotel", "room", "roomprices", "roomprice", "text", "bannerindex", "qystatus", "fwstatus", "qyname", "fwname", "moneyunit", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "uni", "interval", "mign<PERSON><PERSON><PERSON>e", "popupClose", "getdjs", "todel", "orderid", "setTimeout", "toclose", "showhxqr", "closeHxqr", "openMendian", "openLocation", "latitude", "longitude", "name", "scale", "swiper<PERSON><PERSON>e", "phone", "phoneNumber", "fail", "doStoreShowAll", "showDetail", "popupdetailClose"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqZ71B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAC;QACAH;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAI;cACAJ;cACAA;YACA;UACA;UACAA;QACA;UACA;YACAC;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MAEA;QACAP;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAQ;MACA;MACA;MACAP;QACAA;QACAA;UAAAQ;QAAA;UACAR;UACAA;UACAS;YACAT;UACA;QACA;MACA;IACA;IACAU;MACA;MACA;MACAV;QACAA;QACAA;UAAAQ;QAAA;UACAR;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IAEAY;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAb;IACA;IACAc;MACA;MACA;MACA;MACAZ;QACAa;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAlB;QACAmB;QACAC,uBACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnkBA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "hotel/order/orderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './hotel/order/orderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetail.vue?vue&type=template&id=21b60fa5&\"\nvar renderjs\nimport script from \"./orderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"hotel/order/orderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=template&id=21b60fa5&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.detail.formdata.length : null\n  var m2 = _vm.isload && _vm.detail.status == 0 ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload ? _vm.t(\"余额\") : null\n  var m4 =\n    _vm.isload &&\n    (_vm.detail.couponmoney > 0 || _vm.detail.scoredk_money > 0) &&\n    _vm.detail.scoredk_money > 0\n      ? _vm.t(\"积分\")\n      : null\n  var g1 = _vm.isload ? _vm.room.pics.length : null\n  var g2 = _vm.isload && g1 ? _vm.room.pics.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'\">\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\r\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\r\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\r\n\t\t\t\t<view class=\"t2\">已付款，等待商家确认</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\r\n\t\t\t\t<view class=\"t2\">商家已确认，等待入住</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\r\n\t\t\t\t<view class=\"t1\">已到店</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\r\n\t\t\t\t<view class=\"t1\">已离店</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==5\">\r\n\t\t\t\t<view class=\"t1\">订单已完成</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==-1\">\r\n\t\t\t\t<view class=\"t1\">订单已关闭</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view class=\"product\">\r\n\t\t\t<view  class=\"content\">\r\n\t\t\t\t<view class=\"hotelpic\">\r\n\t\t\t\t\t<image :src=\"hotel.pic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t<text class=\"t1\">{{hotel.name}}</text>\r\n\t\t\t\t\t<text class=\"t2\" >{{hotel.address}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\" style=\" width: 80%;margin: 0 auto;display: flex; justify-content: space-between;\">\r\n\t\t\t\t<view class=\"item1\"  @tap=\"openLocation\" :data-latitude=\"hotel.latitude\" :data-longitude=\"hotel.longitude\" :data-company=\"hotel.name\" :data-address=\"hotel.address\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/add2.png'\"/><text>地图导航</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item1\" @tap.stop=\"phone\" :data-phone=\"hotel.tel\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/tel.png'\"/><text>联系{{text['酒店']}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"orderinfo1\">\r\n\t\t\t<view class=\"item flex-bt\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<label class=\"t1\">在线支付</label>\r\n\t\t\t\t\t<text class=\"price flex-bt\">￥{{detail.totalprice}} </text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cost-details flex flex-y-center\"  @click=\"mignxiChange\"  :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t费用明细\r\n\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--<view class=\"item flex-bt\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<label class=\"t1\">发票报销</label>\r\n\t\t\t\t\t<text class=\"t2\">如需发票，请向{{text['酒店']}}前台索取</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>-->\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo1\">\r\n\t\t\t<view class=\"time-view flex flex-y-center flex-bt\">\r\n\t\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"month-tetx\">{{detail.in_date}}</view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content-text\">\r\n\t\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t\t{{detail.daycount}}晚\r\n\t\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"month-tetx\">{{detail.leave_date}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\t<view class=\"name-info flex flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"name-text\">{{room.name}}</view>\r\n\t\t\t\t\t\t<view class=\"name-tisp\">{{room.tag}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click=\"showDetail\" class=\"hotel-details-view flex flex-y-center\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t查看房型\t<image :src=\"pre_url+'/static/img/arrowdown.png'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<label class=\"t1\">入住姓名</label>\r\n\t\t\t\t\t\t<text class=\"t2 flex-bt\">{{detail.linkman}} </text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<label class=\"t1\">联系手机</label>\r\n\t\t\t\t\t\t<text class=\"t2 flex-bt\">{{detail.tel}} </text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.message\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<label class=\"t1\">预定备注</label>\r\n\t\t\t\t\t\t<text class=\"t2 flex-bt\">{{detail.message}} </text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\" v-if=\"detail.isbefore==1 && (detail.real_usemoney>0 ||  detail.real_roomprice>0)\">\r\n\t\t\t<view class=\"title\">实际支付</view>\r\n\t\t\t<view class=\"item flex-bt\">\r\n\t\t\t\t<text class=\"t1\">房费</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.real_usemoney>0 && detail.real_roomprice>0\">{{detail.real_usemoney}}{{moneyunit}} + ￥{{detail.real_roomprice}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-else-if=\"detail.real_usemoney>0 && detail.real_roomprice==0\">{{detail.real_usemoney}}{{moneyunit}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-else>￥{{detail.real_roomprice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item flex-bt\" v-if=\"detail.real_fuwu_money>0\">\r\n\t\t\t\t<text class=\"t1\">{{text['服务费']}}</text>\r\n\t\t\t\t<view class=\"ordernum-info flex-bt\">\r\n\t\t\t\t\t<text class=\"t2 red\" >￥{{detail.real_fuwu_money}}</text>\r\n\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"orderinfo\" v-if=\"detail.yajin_money>0 && detail.status>2\">\r\n\t\t\t<view class=\"title\">押金信息</view>\r\n\t\t\t<view class=\"item flex-bt\">\r\n\t\t\t\t<text class=\"t1\">押金状态</text>\r\n\t\t\t\t<view class=\"ordernum-info flex-bt\">\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==0\">待申请</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==1\">审核中</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==2\">已退款</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==-1\">已驳回</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item flex-bt\" v-if=\"detail.yajin_refund_status==-1\">\r\n\t\t\t\t<text class=\"t1\">驳回原因</text>\r\n\t\t\t\t<view class=\"ordernum-info flex-bt\">\r\n\t\t\t\t\t<text class=\"t2\" >{{detail.yajin_refund_reason?detail.yajin_refund_reason:'无'}}</text>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"title\">订单信息</view>\r\n\t\t\t<view class=\"item flex-bt\">\r\n\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t<view class=\"ordernum-info flex-bt\">\r\n\t\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\r\n\t\t\t\t\t<view class=\"btn-class\" style=\"margin-left: 20rpx;\" @click=\"copy\" :data-text='detail.ordernum'>复制</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\r\n\t\t\t\t<text class=\"t1\">支付时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\r\n\t\t\t\t<text class=\"t1\">支付方式</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.daodian_time\">\r\n\t\t\t\t<text class=\"t1\">到店时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.daodian_time}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"item\" v-if=\"detail.collect_time\">\r\n\t\t\t\t<text class=\"t1\">离店日期</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.real_leavedate}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单状态</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">待确认</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">待入住</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已到店</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已离店</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==5\">已完成</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==-1\">已关闭</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t<text class=\"t1\">退款状态</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t<text class=\"t1\">退款原因</text>\r\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\r\n\t\t\t\t<text class=\"t1\">审核备注</text>\r\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\r\n\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\r\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\r\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\r\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view style=\"width:100%;height:calc(160rpx + env(safe-area-inset-bottom));\"></view>\r\n\r\n\t\t<view class=\"bottom notabbarbot\">\r\n\t\t\t<block v-if=\"detail.status==0\">\r\n\t\t\t\t<view class=\"btn2\" @tap=\"toclose\" :data-id=\"detail.id\">关闭订单</view>\r\n\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + detail.payorderid\">去付款</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"detail.status==1 && detail.isrefund==1\">\r\n\t\t\t\t\t<view v-if=\"detail.refund_status==0 || detail.refund_status==3\" class=\"btn2\" @tap=\"goto\" :data-url=\"'refund?id=' + detail.id + '&price=' + detail.totalprice\">申请退款</view>\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t<block v-if=\"detail.status==2 && detail.isrefund==1\">\r\n\t\t\t\t<block v-if=\"detail.paytypeid!='4'\">\r\n\t\t\t\t\t<view v-if=\"detail.refund_status==0 || detail.refund_status==3\" class=\"btn2\" @tap=\"goto\" :data-url=\"'refund?id=' + detail.id + '&price=' + detail.totalprice\">申请退款</view>\r\n\t\t\t\t</block>\r\n\t\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"detail.status==2\">\r\n\t\t\t\t<view class=\"btn2\" @tap=\"showhxqr\">核销码</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"detail.status==-1\">\r\n\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"detail.id\">删除订单</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\r\n\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t<image :src=\"detail.hexiao_qr\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\" class=\"img\"/>\r\n\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\r\n\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\r\n\r\n\t\t\t\t<!-- 弹窗 -->\r\n\t\t\t\t<uni-popup id=\"popup\" ref=\"popup\" type=\"bottom\">\r\n\t\t\t\t\t<view class=\"hotelpopup__content\">\r\n\t\t\t\t\t\t<view class=\"popup-close\" @click=\"popupClose\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose2.png`\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<scroll-view scroll-y style=\"height: auto;max-height: 50vh;\">\r\n\t\t\t\t\t\t\t<!-- 费用明细 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\" style=\"padding-bottom: 40rpx;\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">费用明细</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" style=\"margin-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text-title\">房费</view>\r\n\t\t\t\t\t\t\t\t\t\t<!--<view class=\"price-num-title\">￥{{roomprice}}</view>-->\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<!--<view class=\"price-view flex flex-bt flex-y-center\" v-for=\"(item,index) in roomprices\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{item.datetime}} </view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{item.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">优惠券抵扣</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{detail.coupon_money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{t('余额')}}抵扣</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">{{detail.use_money?detail.use_money:0}}{{moneyunit}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">现金支付</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{detail.leftmoney}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" style=\"margin-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text-title\">其他</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num-title\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"detail.yajin_money>0\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">押金(可退)</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{detail.yajin_money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"detail.fuwu_money>0\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{text['服务费']}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{detail.fuwu_money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-col\" v-if=\"detail.couponmoney>0 || detail.scoredk_money>0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" style=\"margin-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text-title\">优惠</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num-title\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"detail.couponmoney>0\" >\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">优惠券抵扣</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">-￥{{detail.couponmoney}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"detail.scoredk_money>0\" >\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{t('积分')}}抵扣</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">-￥{{detail.scoredk_money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 详情弹窗 -->\r\n\t\t\t\t<uni-popup id=\"popupDetail\" ref=\"popupDetail\" type=\"bottom\">\r\n\t\t\t\t\t<view class=\"hotelpopup__content\">\r\n\t\t\t\t\t\t<view class=\"popup-close\" @click=\"popupdetailClose\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose.png`\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<scroll-view scroll-y style=\"height: auto;max-height: 70vh;\">\r\n\t\t\t\t\t\t\t<view class=\"popup-banner-view\" style=\"height: 450rpx;\">\r\n\t\t\t\t\t\t\t\t<swiper class=\"dp-banner-swiper\" :autoplay=\"true\" :indicator-dots=\"false\" :current=\"0\" :circular=\"true\" :interval=\"3000\" @change='swiperChange'>\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in room.pics\" :key=\"index\"> \r\n\t\t\t\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t\t\t\t<view @click=\"viewPicture(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item\" class=\"dp-banner-swiper-img\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t\t\t<view class=\"popup-numstatistics flex flex-xy-center\" v-if='room.pics.length'>\r\n\t\t\t\t\t\t\t\t\t{{bannerindex}} / {{room.pics.length}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"hotel-details-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-title\">{{room.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"introduce-view flex \">\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\"  v-if=\"room.bedxing!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedxing}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/pingfang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.square}}m²</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedwidth}}米</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.ischuanghu!='0'\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/youchuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.ischuanghu}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.breakfast!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/zaocan.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.breakfast}}早餐</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"other-view flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"other-title\">特色</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"other-text\" style=\"white-space: pre-line;\">{{room.tese}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 酒店权益 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\" v-if=\"qystatus == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">{{ qyname }}</view>\r\n\t\t\t\t\t\t\t\t  <!--<view class=\"equity-title-tisp\">填写订单时兑换</view>-->\r\n\t\t\t\t\t\t\t\t</view>\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<parse :content=\"hotel.hotelquanyi\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 政策服务 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\"   v-if=\"fwstatus == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">{{ fwname }}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<parse :content=\"hotel.hotelfuwu\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      prodata: '',\r\n      djs: '',\r\n      detail: \"\",\r\n      storeinfo: \"\",\r\n      lefttime: \"\",\r\n      codtxt: \"\",\r\n\t\t\thotel:[],\r\n\t\t\troom:[],\r\n\t\t\troomprices:[],\r\n\t\t\troomprice:0,\r\n\t\t\ttext:[],\r\n\t\t\tbannerindex:1,\r\n\t\t\tqystatus:0,\r\n\t\t\t\tfwstatus:0,\r\n\t\t\t\tqyname:'',\r\n\t\t\t\tfwname:'',\r\n\t\t\t\tmoneyunit:'元'\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onUnload: function () {\r\n    clearInterval(interval);\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiHotel/orderdetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\tthat.loading = false;\r\n        if(res.status == 1){\r\n          that.detail = res.detail;\r\n          that.hotel = res.storeinfo;\r\n          that.lefttime = res.lefttime;\r\n          that.codtxt = res.codtxt;\r\n\t\t\t\t\tthat.room = res.room\r\n          that.isload = 1;\r\n\t\t\t\t\tthat.roomprice =res.totalroomprice\r\n\t\t\t\t\tthat.roomprices = res.roomprices\r\n\t\t\t\t\tthat.text = res.text\r\n\t\t\t\t\tthat.qystatus = res.storeinfo.qystatus\r\n\t\t\t\t\tthat.fwstatus = res.storeinfo.fwstatus\r\n\t\t\t\t\tthat.qyname = res.storeinfo.qyname\r\n\t\t\t\t\tthat.moneyunit = res.moneyunit\r\n          if (res.lefttime > 0) {\r\n            interval = setInterval(function () {\r\n              that.lefttime = that.lefttime - 1;\r\n              that.getdjs();\r\n            }, 1000);\r\n          }\r\n          that.loaded();\r\n        } else {\r\n          if (res.msg) {\r\n            app.alert(res.msg, function() {\r\n              if (res.url) app.goto(res.url);\r\n            });\r\n          } else if (res.url) {\r\n            app.goto(res.url);\r\n          } else {\r\n            app.alert('您无查看权限');\r\n          }\r\n        }\r\n\t\t\t});\r\n\t\t},\r\n\t\tmignxiChange(){\r\n\t\t\tthis.$refs.popup.open();\r\n\t\t},\r\n\t\tpopupClose(){\r\n\t\t\tthis.$refs.popup.close();\r\n\t\t},\r\n    getdjs: function () {\r\n      var that = this;\r\n      var totalsec = that.lefttime;\r\n\r\n      if (totalsec <= 0) {\r\n        that.djs = '00时00分00秒';\r\n      } else {\r\n        var houer = Math.floor(totalsec / 3600);\r\n        var min = Math.floor((totalsec - houer * 3600) / 60);\r\n        var sec = totalsec - houer * 3600 - min * 60;\r\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n        that.djs = djs;\r\n      }\r\n    },\r\n    todel: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要删除该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('删除中');\r\n        app.post('ApiHotel/delOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            app.goback(true);\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    toclose: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要关闭该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiHotel/closeOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n\r\n\t\tshowhxqr:function(){\r\n\t\t\tthis.$refs.dialogHxqr.open();\r\n\t\t},\r\n\t\tcloseHxqr:function(){\r\n\t\t\tthis.$refs.dialogHxqr.close();\r\n\t\t},\r\n\t\topenMendian: function(e) {\r\n\t\t\tvar storeinfo = e.currentTarget.dataset.storeinfo;\r\n\t\t\tapp.goto('/pages/shop/mendian?id=' + storeinfo.id);\r\n\t\t},\r\n\t\topenLocation:function(e){\r\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\r\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\r\n\t\t\tvar address = e.currentTarget.dataset.address;\r\n\t\t\tuni.openLocation({\r\n\t\t\t latitude:latitude,\r\n\t\t\t longitude:longitude,\r\n\t\t\t name:address,\r\n\t\t\t scale: 13\r\n\t\t\t})\r\n\t\t},\r\n\t\tswiperChange(event){\r\n\t\t\t\tthis.bannerindex = event.detail.current+1;\r\n\t\t\t},\r\n\t\tphone:function(e) {\r\n\t\t\tvar phone = e.currentTarget.dataset.phone;\r\n\t\t\tuni.makePhoneCall({\r\n\t\t\t\tphoneNumber: phone,\r\n\t\t\t\tfail: function () {\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tdoStoreShowAll:function(){\r\n\t\t\tthis.storeshowall = true;\r\n\t\t},\r\n\t\tshowDetail:function(e){\r\n\t\t\t\t// 房型详情-------------------------------------------------------------------------------------------\r\n\t\t\t\tthis.$refs.popupDetail.open();\r\n\t\t},\r\n\t\tpopupdetailClose(){\r\n\t\t\tthis.$refs.popupDetail.close();\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\r\n.ordertop .f1{color:#fff}\r\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\r\n.ordertop .f1 .t2{font-size:24rpx}\r\n\r\n.product{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\r\n.product .content:last-child{ border-bottom: 0; }\r\n.product .content .hotelpic image{ width: 120rpx; height: 100rpx;}\r\n.product .content .item1{ display: flex; align-items: center;}\r\n.product .content .item1 image{ width: 40rpx; height:40rpx}\r\n\r\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.product .content .detail .t1{font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;color: #000;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.product .content .detail .x1{ flex:1}\r\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n\r\n.orderinfo1{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo1 .item { padding:20rpx 0}\r\n.orderinfo1 .item .f1{ display: flex; font-size: 24rpx; }\r\n.orderinfo1 .item .f1 .t1{ color: #999; width: 150rpx;}\r\n.orderinfo1 .item .f1 .price{ color: red; }\r\n.orderinfo1 .cost-details{color: #06D470;font-size: 24rpx;font-weight: bold;}\r\n.orderinfo1 .cost-details image{width:24rpx;height: 24rpx;transform: rotate(90deg);margin: 0rpx 20rpx 0rpx 10rpx;}\r\n/*入住时间样式*/\r\n.orderinfo1 .time-view{width: 100%;padding-top: 10rpx;}\r\n.orderinfo1 .time-view .time-options{}\r\n.orderinfo1 .time-view .time-options .month-tetx{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n.orderinfo1 .time-view .content-text{width: 46px;height: 40rpx;line-height: 39rpx;text-align: center;border-radius: 20px;color: #000;font-size: 20rpx;position: relative;border: 1px #000 solid;}\r\n.orderinfo1 .time-view .content-text .content-decorate{width: 13rpx;height: 2rpx;background: red;position: absolute;top: 50%;}\r\n.orderinfo1 .time-view .content-text .left-c-d{left: -13rpx;background: #000;}\r\n.orderinfo1 .time-view .content-text .right-c-d{right: -13rpx;background: #000;}\r\n.orderinfo1 .name-info{width: 100%;padding: 30rpx 0rpx;}\r\n.orderinfo1 .name-info .name-text {color: #1E1A33;font-size: 30rpx;font-weight: bold;}\r\n.orderinfo1 .name-info .name-tisp{color: #A5A3AD;font-size: 24rpx;margin-top: 15rpx;}\r\n.orderinfo1 .hotel-details-view image{width: 22rpx;height: 22rpx;margin-left: 10rpx;}\r\n\r\n\r\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .title{ font-size: 28rpx; font-weight: bold; margin-bottom: 20rpx;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx; font-size: 24rpx;}\r\n.orderinfo .item .t2{flex:1;text-align:right; font-size: 24rpx;}\r\n.orderinfo .item .red{color:red}\r\n.order-info-title{align-items: center;}\r\n.btn-class{height:45rpx;line-height:45rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;padding: 0 15rpx;flex-shrink: 0;margin: 0 0 0 10rpx;font-size:24rpx;}\r\n.ordernum-info{align-items: center;}\r\n\r\n.bottom{ width: 100%;height:calc(92rpx + env(safe-area-inset-bottom));padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\r\n\r\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\r\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n\r\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\r\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.hxqrbox .img{width:400rpx;height:400rpx}\r\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;padding:20rpx 20rpx;border-bottom:1px dotted #f1f1f1}\r\n.radio-item:last-child {border: 0}\r\n.radio-item .f1 {color: #333;font-size:30rpx;flex: 1}\r\n.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}\r\n\r\n\r\n\t\r\n\t/* 费用明细 */\r\n.hotelpopup__content{width: 100%;height:auto;position: relative;}\r\n.hotelpopup__content .popup-close{position: fixed;right: 20rpx;top: 20rpx;width: 56rpx;height: 56rpx;z-index: 11;}\r\n.hotelpopup__content .popup-close image{width: 100%;height: 100%;}\r\n.hotelpopup__content .hotel-equity-view{width: 100%;padding:30rpx 40rpx 40rpx;background: #fff;margin-top: 20rpx;}\r\n.hotelpopup__content .hotel-equity-view{width: 100%;padding:30rpx 40rpx 40rpx;background: #fff;}\r\n.hotel-equity-view .equity-title-view{align-items: center;justify-content: flex-start; padding: 20rpx 0;}\r\n.hotel-equity-view .equity-title-view .equity-title{color: #1E1A33;font-size: 32rpx;font-weight: bold;}\r\n.hotel-equity-view .equity-title-view .equity-title-tisp{color: #A5A3AD;font-size: 24rpx;margin-left: 28rpx;}\r\n.hotel-equity-view .equity-options{margin-top: 40rpx;}\r\n.hotel-equity-view .equity-options .options-title-view{align-items: center;justify-content: flex-start;}\r\n.hotel-equity-view .equity-options .options-title-view image{width: 28rpx;height: 28rpx;margin-right: 20rpx;}\r\n.hotel-equity-view .equity-options .options-title-view  .title-text{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n.hotel-equity-view .equity-options .options-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;padding: 15rpx 0rpx;line-height: 40rpx;margin-left: 50rpx;margin-right: 50rpx;}\r\n\r\n.hotel-equity-view  .cost-details{width: 100%;padding-bottom: 30rpx;border-bottom: 1px #efefef solid;margin-top: 40rpx;}\r\n.hotel-equity-view  .cost-details .price-view{padding-bottom: 10rpx;}\r\n.hotel-equity-view  .cost-details .price-view .price-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;}\r\n.hotel-equity-view  .cost-details .price-view .price-num{color: #1E1A33;font-size: 24rpx;}\r\n.hotel-equity-view  .cost-details .price-view .price-text-title{color: rgba(30, 26, 51, 0.8);font-size: 30rpx;font-weight: bold;}\r\n.hotel-equity-view  .cost-details .price-view .price-num-title{color: #1E1A33;font-size: 30rpx;font-weight: bold;}\r\n\t/*  */\r\n\t/* 房型详情 */\r\n\t.dp-banner{width: 100%;height: 250px;}\r\n\t.dp-banner-swiper{width:100%;height:100%;}\r\n\t.dp-banner-swiper-img{width:100%;height:auto}\r\n\t.hotelpopup__content .popup-banner-view{width: 100%;height: 500rpx;position: relative;}\r\n\t.hotelpopup__content .popup-banner-view .popup-numstatistics{position: absolute;right: 20rpx;bottom: 20rpx;background: rgba(0, 0, 0, 0.3);\r\n\tborder-radius: 28px;width: 64px;height: 28px;text-align: center;line-height: 28px;color: #fff;font-size: 20rpx;}\r\n\t.hotelpopup__content .hotel-details-view{width: 100%;padding: 30rpx 40rpx;background: #fff;}\r\n\t.hotelpopup__content .hotel-details-view\t.hotel-title{color: #1E1A33;font-size: 40rpx;}\r\n\t.hotelpopup__content .hotel-details-view\t.introduce-view{width: 100%;align-items: center;flex-wrap: wrap;justify-content: flex-start;padding: 20rpx 10rpx;}\r\n\t.hotelpopup__content .hotel-details-view\t.introduce-view .options-intro{padding: 15rpx 0rpx;margin-right: 20rpx;width: auto;}\r\n\t.hotel-details-view\t.introduce-view .options-intro image{width: 32rpx;height: 32rpx;}\r\n\t.hotel-details-view\t.introduce-view .options-intro .options-title{color: #1E1A33;font-size: 24rpx;margin-left: 15rpx;}\r\n\t.hotel-details-view .other-view{width: 100%;justify-content: flex-start;padding: 12rpx 0rpx;}\r\n\t.hotel-details-view .other-view .other-title{color: #A5A3AD;font-size: 24rpx;margin-right: 40rpx;}\r\n\t.hotel-details-view .other-view .other-text{color: #1E1A33;font-size: 24rpx;}\t\r\n\t\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839388351\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}