{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderlist.vue?29a7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderlist.vue?daa7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderlist.vue?6ef1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderlist.vue?8244", "uni-app:///hotel/order/orderlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderlist.vue?66fd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/order/orderlist.vue?5234"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nodata", "nomore", "codtxt", "keyword", "text", "hotelid", "yajin", "orderid", "yajininfo", "set", "showyajin", "tmplids", "moneyunit", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "getdata", "that", "app", "uni", "title", "changetab", "scrollTop", "duration", "toclose", "setTimeout", "todel", "searchConfirm", "refu<PERSON><PERSON><PERSON>", "console", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClGA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmK31B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAA1B;QAAAE;QAAAI;QAAAE;MAAA;QACAiB;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAE;YACAC;UACA;UACAH;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACAF;QACAG;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAN;QACAA;QACAA;UAAAhB;QAAA;UACAgB;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACAR;QACAA;QACAA;UAAAhB;QAAA;UACAgB;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IAEAU;MACA;MACA;IACA;IACAC;MACA;MACA;MACAX;MACA;MACAA;MACAC;QAAAhB;MAAA;QACA;UACAe;UACAA;UACAY;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAb;QACAA;QACAA;UAAAhB;QAAA;UACAgB;UACAA;UACAD;YACAQ;cACAR;cACAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5UA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "hotel/order/orderlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './hotel/order/orderlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderlist.vue?vue&type=template&id=3ddbd872&\"\nvar renderjs\nimport script from \"./orderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./orderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"hotel/order/orderlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=template&id=3ddbd872&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.status == 0 ? _vm.t(\"color1\") : null\n        var m1 =\n          item.status == 4 &&\n          !(item.yajin_refund_status == 2) &&\n          item.yajin_money > 0\n            ? _vm.t(\"color1rgb\")\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var m2 =\n    _vm.isload && _vm.showyajin && _vm.yajininfo.refund_status > 0\n      ? _vm.dateFormat(_vm.yajininfo.apply_time)\n      : null\n  var m3 =\n    _vm.isload && _vm.showyajin && _vm.yajininfo.refund_status == -1\n      ? _vm.dateFormat(_vm.yajininfo.apply_time)\n      : null\n  var m4 =\n    _vm.isload && _vm.showyajin && _vm.yajininfo.refund_status < 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<dd-tab :itemdata=\"['全部','待入住','已到店','已离店','退款']\" :itemst=\"['all','2','3','4','10']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\n\t\t<view style=\"width:100%;height:100rpx\"></view>\n\t\t<!-- #ifndef H5 || APP-PLUS -->\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\n\t\t\t</view>\n\t\t</view>\n\t\t<!--  #endif -->\n\t\t<view class=\"order-content\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'orderdetail?id=' + item.id\">\n\t\t\t\t\t<view class=\"head\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/ico-shop.png'\"></image> {{item.hotel.name}}</view>\n\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t<text v-if=\"item.status==0\" class=\"st0\">待付款</text>\n\t\t\t\t\t\t<text v-if=\"item.status==1\" class=\"st1\">待确认</text>\n\t\t\t\t\t\t<text v-if=\"item.status==2\" class=\"st2\">待入住</text>\n\t\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st3\">已到店</text>\n\t\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">已离店</text>\n\t\t\t\t\t\t<text v-if=\"item.status==-1\" class=\"st4\">已关闭</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"content\" >\n\t\t\t\t\t\t<view >\n\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.totalnum}}{{text['间']}}, {{item.title}}</text>\n\t\t\t\t\t\t\t<text class=\"t2\" style=\"color:#666\">{{item.in_date}} - {{item.leave_date}}</text>\n\t\t\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t\t\t<block v-if=\"item.isbefore==1\">\n\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\" v-if=\"item.real_usemoney>0 && item.real_roomprice>0\">实付房费：{{item.real_usemoney}}{{moneyunit}} + ￥{{item.real_roomprice}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\" v-else-if=\"item.real_usemoney>0 && item.real_roomprice==0\">实付房费：￥{{item.real_usemoney}}{{moneyunit}}</text>\n\t\t\t\t\t\t\t\t\t<text  class=\"x1 flex1\" v-else>实付房费：￥{{item.real_roomprice}}</text>\n \t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t<text  class=\"x1 flex1\" v-if=\"item.use_money>0 && item.leftmoney>0\">房费：{{item.use_money}}{{moneyunit}} + ￥{{item.leftmoney}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\" v-else-if=\"item.use_money>0 && item.leftmoney==0\">房费：￥{{item.use_money}}{{moneyunit}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\" v-else>房费：￥{{item.sell_price}}</text>\n \n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"item.coupon_money > 0\">\n\t\t\t\t\t\t\t\t<block>\n\t\t\t\t\t\t\t\t\t<text  class=\"x1 flex1 \">优惠券:￥{{ item.coupon_money }}</text>\n\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"bottom\" style=\"display:flex; justify-content: space-between;\">\n\t\t\t\t\t\t<text>共{{item.daycount}}晚 \n\t\t\t\t\t\t\t<block v-if=\"item.use_money>0 && item.leftmoney>0\">\n\t\t\t\t\t\t\t\t\t实付: 押金￥{{item.yajin_money}}+{{text['服务费']}}￥{{item.fuwu_money}}+房费￥{{item.leftmoney}}+{{item.use_money?item.use_money:0}}{{moneyunit}}\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else-if=\"item.use_money>0 && item.leftmoney==0\">\n\t\t\t\t\t\t\t\t\t实付: 押金￥{{item.yajin_money}}+{{text['服务费']}}￥{{item.fuwu_money}}+房费{{item.use_money?item.use_money:0}}{{moneyunit}}\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t实付:￥{{item.totalprice}}\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</text>\n\t\t\t\t\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bottom\" v-if=\"item.refund_status>0\">\n\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red\"> 退款中￥{{item.refund_money}}</text>\n\t\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red\"> 已退款￥{{item.refund_money}}</text>\n\t\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red\"> 退款申请已驳回</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"op\">\n\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'orderdetail?id=' + item.id\" class=\"btn2\">详情</view>\n\t\t\t\t\t\t<block v-if=\"item.status==0\">\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"toclose\" :data-id=\"item.id\">关闭订单</view>\n\t\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + item.payorderid\">去付款</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.status==1 && item.isrefund==1\">\n\t\t\t\t\t\t\t\t<view v-if=\"item.refund_status==0 || item.refund_status==3\" class=\"btn2\"  @tap.stop=\"goto\" :data-url=\"'refund?id='+item.id\">申请退款</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.status==2 && item.isrefund==1\">\n\t\t\t\t\t\t\t\t<view v-if=\"item.refund_status==0 || item.refund_status==3\" class=\"btn2\"  @tap.stop=\"goto\" :data-url=\"'refund?id='+item.id\">申请退款</view>\n\t\t\t\t\t\t\t\t<!--<view  class=\"btn2\"  @tap=\"goto\" :data-url=\"'befor_out?id='+item.id\">提前离店</view>-->\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.status==4\">\n\t\t\t\t\t\t\t\t<block v-if=\"item.yajin_refund_status==2\">\n\t\t\t\t\t\t\t\t\t<text class=\"btn2 color1\" :style=\"'background:#1BA035;color:#FFF'\">押金已退</text> \n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t<text class=\"btn2 color1\" :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\"  @tap.stop=\"refundyajin\" v-if=\"item.yajin_money>0\" :data-index=\"index\" >退押金</text> \t\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<view v-if=\"item.hotel.comment == 1\"  class=\"btn2 color1\"  :style=\"'background:#FCC421;color:#FFF'\" @tap.stop=\"goto\" :data-url=\"'comment?oid='+item.id\">评价</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.status==-1\">\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"todel\" :data-id=\"item.id\">删除订单</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t\n\t\t\t\n\t\t\t<view class=\"model_yajin\"  v-if=\"showyajin\">\n\t\t\t\t<view class=\"yajinbox\">\n\t\t\t\t\t<view class=\"yajincontent\">\n\t\t\t\t\t\t<view class=\"title\">押金详情</view>\n\t\t\t\t\t\t<!--<view class=\"item\"><label>应退押金：</label>\n\t\t\t\t\t\t\t<view class=\"f2\"><text style=\"color: red; font-weight: bold;\">{{yajin}}</text>元</view>\n\t\t\t\t\t\t</view>-->\n\t\t\t\t\t\t<view class=\"desc\" v-if=\"yajininfo.refund_status==0 && set.yajin_desc\">\n\t\t\t\t\t\t\t{{set.yajin_desc}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<block v-if=\"yajininfo.refund_status>0\">\n\t\t\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t\t\t<label>申请时间：</label>\t\n\t\t\t\t\t\t\t\t<view class=\"f2\"><text>{{dateFormat(yajininfo.apply_time)}}</text></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item\"><label>退款状态：</label>\n\t\t\t\t\t\t\t\t<text class=\"t2 red\" v-if=\"yajininfo.refund_status==1\">审核中</text>\n\t\t\t\t\t\t\t\t<text class=\"t2 red\" v-if=\"yajininfo.refund_status==2\">已退款</text>\n\t\t\t\t\t\t\t\t<text class=\"t2 red\" v-if=\"yajininfo.refund_status==-1\">已驳回</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item\"  v-if=\"yajininfo.refund_status==2\"><label>退款时间：</label><text>{{yajininfo.refund_time}}</text></view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"yajininfo.refund_status==-1\">\n\t\t\t\t\t\t\t<view class=\"item\"><label>申请时间：</label><text>{{dateFormat(yajininfo.apply_time)}}</text></view>\n\t\t\t\t\t\t\t<view class=\"item\"><label>退款状态：</label>\n\t\t\t\t\t\t\t\t<text class=\"t2 red\">已驳回</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item\"  v-if=\"yajininfo.refund_status==2\"><label>退款时间：</label><text >{{yajininfo.refund_time}}</text></view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view class=\"item\" v-if=\"yajininfo.refund_status==-1\"><label>驳回原因：</label><text >{{yajininfo.refund_reason}}</text></view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"yajinbtn\" v-if=\"yajininfo.refund_status<1\">\n\t\t\t\t\t\t\t<view class=\"btn1\" @tap=\"closeYajin\"  :style=\"'background:#FCC421;color:#FFF; border:none'\">稍后申请</view>\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap=\"yajinsubmit\"  :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF;border:none'\" >立即申请</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"close\" @tap=\"closeYajin\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t<nodata v-if=\"nodata\"></nodata>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      nodata: false,\n      nomore: false,\n      codtxt: \"\",\n\t\t\tkeyword:'',\n\t\t\ttext:[],\n\t\t\thotelid:0,\n\t\t\tyajin:0,\n\t\t\torderid:0,\n\t\t\tyajininfo:[],\n\t\t\tset:[],\n\t\t\tshowyajin:false,\n\t\t\ttmplids:[],\n\t\t\tmoneyunit:'元',\n\t\t\tpre_url:app.globalData.pre_url,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tif(this.opt && this.opt.st){\n\t\t\tthis.st = this.opt.st;\n\t\t}\n\t\tif(this.opt && this.opt.hotelid){\n\t\t\tthis.hotelid = this.opt.hotelid;\n\t\t}\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n\tonNavigationBarSearchInputConfirmed:function(e){\n\t\tthis.searchConfirm({detail:{value:e.text}});\n\t},\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\tthat.loading = true;\n      app.post('ApiHotel/orderlist', {st: st,pagenum: pagenum,keyword:that.keyword,hotelid:that.hotelid}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tthat.set = res.set\n\t\t\t\t\tthat.moneyunit = res.moneyunit\n\t\t\t\t\tthat.text = res.text\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: '订单列表'\n\t\t\t\t\t});\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    toclose: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiHotel/closeOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n    todel: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiHotel/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n  \n\t\tsearchConfirm:function(e){\n\t\t\tthis.keyword = e.detail.value;\n      this.getdata(false);\n\t\t},\n\t\trefundyajin:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tthat.yajin = that.datalist[index].yajin_money\n\t\t\tvar orderid =  that.datalist[index].id\n\t\t\tthat.orderid =  orderid\n\t\t\tapp.get('ApiHotel/refundYajin', {orderid: orderid}, function (data) {\n\t\t\t\tif(data.status==1){\n\t\t\t\t\t\tthat.yajininfo = data.detail\n\t\t\t\t\t\tthat.tmplids = data.tmplids;\n\t\t\t\t\t\tconsole.log(that.tmplids);\n\t\t\t\t}\n\t\t\t});\n\t\t\t//this.$refs.dialogYajin.open();\n\t\t\tthis.showyajin = true;\n\t\t},\n\t\tcloseYajin:function(){\n\t\t\t//this.$refs.dialogYajin.close();\n\t\t\tthis.showyajin = false;\n\t\t},\n\t\tyajinsubmit: function (e) {\n\t\t  var that = this;\n\t\t  var orderid = that.orderid;\n\t\t  app.confirm('确定要申请退押金吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiHotel/refundYajin', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tthat.subscribeMessage(function () {\n\t\t\t\t\t  setTimeout(function () {\n\t\t\t\t\t   that.showyajin=false;\n\t\t\t\t\t   that.getdata();\n\t\t\t\t\t  }, 1000);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t  });\n\t\t},\n  }\n};\n</script>\n<style>\n.container{ width:100%;}\n.topsearch{width:94%;margin:10rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.order-content{display:flex;flex-direction:column}\n.order-content{display:flex;flex-direction:column}\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\n.order-box .head .f1{display:flex;align-items:center;color:#333}\n.order-box .head .f1 image{width:34rpx;height:34rpx;margin-right:4px}\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\n.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\n.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\n\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}\n.order-box .content:last-child{ border-bottom: 0; }\n.order-box .content image{ width: 140rpx; height: 140rpx;}\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.order-box .content .detail .t1{font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.order-box .content .detail .x1{ flex:1}\n.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n\n.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n.order-box .op{ display:flex;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn2.color1{ background-color: #ff8758; color: #fff; border:none}\n\n\n.yajinbox{background:#fff;padding:30rpx 50rpx ;position:relative;border-radius:20rpx;width: 80%; margin: 0 auto; top: 30%; }\n.yajinbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n.yajincontent{ margin: 0 20rpx; width: 100%;}\n.yajincontent .title{ font-weight: bold;  margin-bottom: 30rpx; font-size: 32rpx; text-align: center; }\n.yajincontent .desc{ width: 100%;}\n.yajincontent .item{ display: flex; justify-content: space-between;align-items: center; padding:15rpx 0 }\n.yajincontent .yajinprice{ margin-bottom: 50rpx;}\n.yajinbtn{ display: flex; margin-top: 30rpx;justify-content: flex-end;}\n.yajinbtn .btn1{ background-color:#f3f3f3 ;  color: #999;}\n.yajinbox .title{ font-weight: bold;}\n.yajincontent .f2{ width: 75%; display: flex; justify-content: flex-end;}\n.yajincontent .item .red{color:red}\n.yajincontent .item label{ width: 160rpx;}\n.yajincontent .item .t2{ width: 75%;    text-align: right;}\n.model_yajin{ background-color: rgba(0,0,0,0.5); position: fixed; width: 100%; height:100%}\n\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839388439\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}