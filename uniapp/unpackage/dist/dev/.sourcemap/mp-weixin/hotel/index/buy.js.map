{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/buy.vue?b8dc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/buy.vue?0afa", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/buy.vue?7a10", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/buy.vue?817b", "uni-app:///hotel/index/buy.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/buy.vue?eae8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/buy.vue?da7a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "navigationMenu", "statusBarHeight", "platform", "pre_url", "startDate", "endDate", "dayCount", "startweek", "endweek", "room", "bannerindex", "hotel", "isagree", "isagree1", "<PERSON><PERSON><PERSON><PERSON>", "showxieyi2", "changebookIndex", "set", "text", "num", "yhm<PERSON>", "totalprice", "couponmoney", "service_money", "yajin", "roomprice", "totalroomprice", "roomprices", "userinfo", "moneydec", "moneyrate", "dec_money", "<PERSON><PERSON><PERSON>", "dikoutext", "yjcount", "showmoneyrate", "dkmoney", "usemoney", "leftmoney", "editorFormdata", "name", "tel", "scoredkmaxmoney", "isdisabled", "usescore", "scoredk_money", "couponList", "couponvisible", "couponrid", "couponnametext", "coupon_money", "tmplids", "qystatus", "fwstatus", "qyname", "fwname", "ysfont_size", "ydfont_size", "moneyunit", "startTime", "endTime", "onLoad", "console", "methods", "getdata", "app", "id", "startdate", "enddate", "that", "moneydk", "showCouponList", "handleClickMask", "chooseCoupon", "scoredk", "calculatePrice", "createorder", "formname", "formdata", "roomid", "inputname", "changebook", "chooseTime", "date", "proid", "editorChooseImage", "removeimg", "pics", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "event", "clearInterval", "goback", "getApp"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3KA;AAAA;AAAA;AAAA;AAAi0B,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACidr1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IAEA;IACA;IAEA;IACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;EACA;;EACAC;IACAC;MACA;MACAC;QAAAC;QAAA5D;QAAA6D;QAAAC;MAAA;QACA;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;UACA;UACAA;UACAA;UACAA;QACA;UACA;YACAJ;cACA;gBACAA;cACA;gBACAA;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;UACA;QACA;MACA;IACA;IACAK;MACA;MACA;MACA;QACAzC;MACA;QACAA;MACA;MACAwC;MACAA;IACA;IACAE;MACA;IACA;IACAC;MACA;IACA;IAEAC;MAAA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACAvB;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAwB;MACA;MACA;QACAT;QAAA;MACA;MACA;MACA;MACA;MACA;IACA;IAIA;IACAU;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;QACA;QACA;QACA;UACA;QACA;QACA;UACA;UACA;UACAvC;UACA;UACA;YAAA;YACAC;YACA;cACAA;cACAD;cACA;YACA;UACA;YAAA;YACAC;YACA;cACAA;cACAD;cACA;YACA;UACA;QACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;QACAE;MACA;QACAA;MACA;;MAGA;MACA;QACA+B;QACAA;QACAA;QACAnB;QACAmB;QACAA;MACA;QACAA;MACA;MACA;QACA;QACA;QACA,6EACAxB;UACAA;UAAA;QACA;QACAwB;QACA/B;QACA;MACA;QACA;MACA;MACA+B;;MAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACAA;MACAA;MACAA;MAEA;MACA;MACA;QACAjC;QACAL;QAEA;UACA;YACA;cACAA;YACA,QAEA;UACA;QACA;MACA;MACAsC;MAEA;MACA;MACA;MACA;MACA;MACA;QACA9C;MACA;MACA8C;MACA;MACA;MACA;QACA;UACA7C;QACA;UACAA;QACA;UACAA;QACA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;MACA;MACA6C;MAEAA;MACAA;IACA;IACAO;MACA;MAEA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MAGA;MACA;MACA;MACA;QACA;QACA;UACAX;UAAA;QACA;QACAY;MACA;MACAC;MACA;QACA;MACA;MAEA;QACA;MACA;MAEA;MACAhB;MACA;MAEAA;MAEA;QACA;QACAA;QACA;UACAG;UAAA;QACA;QACA;UACAa;QACA;MACA;MAIA;MACA;MAEAb;MACAA;QAAAa;QAAAC;QAAAzE;QAAAa;QAAAf;QAAAC;QAAA2B;QAAAH;QAAAe;QAAAI;QAAAW;QAAAC;MAAA;QACAK;QACA;UACAI;YACAJ;UACA;QACA;UACA;YACAA;UACA;UACAA;UACA;QACA;MACA;IAEA;IACAe;MACA;MACA;MACAlB;IACA;IACAmB;MACA;MACA;MACAZ;MACA;MACA;MAGA;MACAJ;QAAA9C;QAAA4D;QAAAZ;MAAA;QACA;UACAE;UACAA;UACAA;QACA;UACAJ;UACA;QACA;MACA;IAGA;IACA;IACA;IACAiB;MACA;MACA;MACAb;MACAA;MACAA;MACA;MACAA;MACAA;MACAJ;QAAAkB;QAAAC;MAAA;QACAf;QACAA;MACA;IACA;IACAgB;MACA;MACA;MACA;MACA;MACA;MACApB;QACA1B;QACA;QACA8B;QACAA;QACAA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACAC;MACAlB;MACAA;IACA;IACAmB;MACA;MACA;MACA;MACA;MAEA;MACA;MACAjD;MAEA8B;MAEA;MACAA;MACAA;MAEAA;IACA;EAAA,wDACA;IACA;IACA;IACA;IACA;IACAkB;IACAlB;IACAA;EACA,4DAGA;IACA;IACA;MACAJ;MAAA;IACA;IACA;EACA,yDACA;IACA;IACA;IACAI;EACA,0DACA;IACA;IACA;EACA,6DACA;IACA;EACA,gFAEA;IACA;IACA;IACA;EACA,6DACA;IACA;IACA;MACAA;MACAA;IACA;MACAA;MACAA;IACA;IACAP;EACA,0DACA;IAEA;IACA;EACA,sFACA;IACA;EACA,oFACA;IACA;EACA,gFACA;IACA;EACA,4FACA;IACA;EACA,kFACA2B;IACA;EACA,sEACAvE;IACA;IACA;MACA;QACA;UACAmD;QACA;QACAqB;MACA;QACA;MACA;IACA;MACA;IACA;MACA5B;MACA;QACA;UACAO;QACA;QACAqB;MACA;QACA;QACA;MACA;IACA;MACA;MACA;IACA;MACA;IACA;EACA,mEACA;IACA;MACA;MACA;IACA;EACA,aACA;EACAC;IACA;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvkCA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "hotel/index/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './hotel/index/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=45bd7de0&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"hotel/index/buy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=45bd7de0&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.tColor(\"color1rgb\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload && _vm.hotel.isrefund ? _vm.t(\"color1rgb\") : null\n  var m4 = _vm.isload && _vm.hotel.isrefund ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.room.limitnums, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m6 = item == _vm.changebookIndex ? _vm.t(\"color1rgb\") : null\n        var m7 = item == _vm.changebookIndex ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m6: m6,\n          m7: m7,\n        }\n      })\n    : null\n  var m8 = _vm.isload ? _vm.t(\"优惠券\") : null\n  var g0 = _vm.isload\n    ? _vm.couponList.length > 0 && _vm.leftmoney > 0 && !_vm.moneydec\n    : null\n  var m9 = _vm.isload && g0 ? _vm.t(\"color1\") : null\n  var g1 =\n    _vm.isload && g0 && !(_vm.couponrid != 0) ? _vm.couponList.length : null\n  var m10 = _vm.isload && !g0 ? _vm.t(\"优惠券\") : null\n  var m11 =\n    _vm.isload && _vm.hotel.money_dikou == 1 && _vm.userinfo.money > 0\n      ? _vm.t(\"余额\")\n      : null\n  var m12 =\n    _vm.isload && _vm.hotel.money_dikou == 1 && _vm.userinfo.money > 0\n      ? _vm.t(\"余额\")\n      : null\n  var m13 =\n    _vm.isload &&\n    _vm.hotel.score_dikou == 1 &&\n    _vm.userinfo.score > 0 &&\n    _vm.hotel.scoredkmaxpercent > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m14 =\n    _vm.isload && _vm.hotel.xystatus == 1 && _vm.isagree && _vm.isagree1\n      ? _vm.t(\"color1\")\n      : null\n  var m15 =\n    _vm.isload && _vm.hotel.xystatus == 1 && _vm.isagree && _vm.isagree1\n      ? _vm.t(\"color1\")\n      : null\n  var m16 = _vm.isload && _vm.moneydec ? _vm.t(\"color1\") : null\n  var m17 = _vm.isload && !_vm.moneydec ? _vm.t(\"color1\") : null\n  var m18 = _vm.isload ? _vm.t(\"color1\") : null\n  var m19 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m20 =\n    _vm.isload && _vm.showxieyi && _vm.hotel.isqianzi == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m21 =\n    _vm.isload && _vm.showxieyi && _vm.hotel.isqianzi == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m22 =\n    _vm.isload && _vm.showxieyi && !(_vm.hotel.isqianzi == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m23 =\n    _vm.isload && _vm.showxieyi && !(_vm.hotel.isqianzi == 1)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m24 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1\") : null\n  var m25 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1rgb\") : null\n  var m26 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var m27 = _vm.isload ? _vm.t(\"余额\") : null\n  var m28 =\n    _vm.isload && (_vm.coupon_money > 0 || _vm.usescore > 0) && _vm.usescore > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m29 = _vm.isload && _vm.moneydec ? _vm.t(\"color1\") : null\n  var m30 = _vm.isload && !_vm.moneydec ? _vm.t(\"color1\") : null\n  var m31 = _vm.isload ? _vm.t(\"color1\") : null\n  var m32 = _vm.isload ? _vm.t(\"color1\") : null\n  var m33 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var g2 = _vm.isload ? _vm.room.pics.length : null\n  var g3 = _vm.isload && g2 ? _vm.room.pics.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        l0: l0,\n        m8: m8,\n        g0: g0,\n        m9: m9,\n        g1: g1,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"createorder\">\r\n\t\t\t\t<view class=\"navigation\" :style=\"'background:rgba('+tColor('color1rgb')+')'\">\r\n\t\t\t\t\t<view class='navcontent' :style=\"{marginTop:navigationMenu.top+'px',width:(navigationMenu.right)+'px'}\">\r\n\t\t\t\t\t\t<view class=\"header-location-top\" :style=\"{height:navigationMenu.height+'px'}\">\r\n\t\t\t\t\t\t\t<view class=\"header-back-but\"  @tap=\"goback\">\r\n\t\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/fanhui.png`\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"header-page-title\" style=\"color:#fff;\">订单详情</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #ifndef H5 || APP-PLUS -->\r\n\t\t\t\t<!--  -->\r\n\t\t\t\t<view style=\"height: 140rpx;\"></view>\r\n\t\t\t\t<!--  #endif -->\r\n\t\t\t\t<view style=\"height: 80rpx;\"></view>\r\n\t\t\t\t<view class=\"titlebg-view\" :style=\"'background:rgba('+t('color1rgb')+')'\"></view>\r\n\t\t\t\t<view class=\"position-view\">\r\n\t\t\t\t\t<view class=\"order-time-details flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"time-view flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t<view class=\"month-tetx\">{{startDate}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"day-tetx\">{{startweek}}入住</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content-text\">\r\n\t\t\t\t\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t\t\t\t\t共{{dayCount}}晚\r\n\t\t\t\t\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t<view class=\"month-tetx\">{{endDate}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"day-tetx\">{{endweek}}离店</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name-info flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t<view class=\"flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"name-text\">{{room.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"name-tisp\">{{room.tag}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view @click=\"showDetail\" class=\"hotel-details-view flex flex-y-center\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t房型详情<image :src=\"pre_url+'/static/img/arrowright.png'\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"time-warning flex flex-y-center\" :style=\"'background: rgba('+t('color1rgb')+',0.2);color:'+t('color1')+''\" v-if=\"hotel.isrefund\"> \r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/error.png`\" ></image>\r\n\t\t\t\t\t\t\t限时取消 {{startDate}} {{hotel.refund_hour}}点前免费取消\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 订房信息 -->\r\n\t\t\t\t\t<view class=\"order-options-view flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"options-title flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t<view>订房信息</view>\r\n\t\t\t\t\t\t\t<view class=\"right-view flex flex-y-center\" @click=\"bookingChange\" :style=\"{color:t('color1')}\">订房说明<image :src=\"`${pre_url}/static/img/hotel/shuom.png`\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"booking-options flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t<view class=\"book-title\">\r\n\t\t\t\t\t\t\t\t{{text['间']}}数\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"room-number-view\">\r\n\t\t\t\t\t\t\t\t<scroll-view scroll-x style=\"width: 100%;white-space: nowrap;\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in room.limitnums\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"room-options\"  :style=\"item == changebookIndex ?'background: rgba('+t('color1rgb')+',0.2);color:'+t('color1')+'':''\" @click=\"changebook(item)\">{{item}}{{text['间']}}</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in num\">\r\n\t\t\t\t\t\t\t<view class=\"booking-options flex flex-y-center flex-bt form-item\" style=\"padding: 0;\">\r\n\t\t\t\t\t\t\t\t<view class=\"book-title\">\r\n\t\t\t\t\t\t\t\t\t住客姓名\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"room-form\">\r\n\t\t\t\t\t\t\t\t\t\t<input :name=\"'name'+index\" placeholder=\"仅填写1人姓名(姓名不可重复)\" style=\"text-align: right;\"/>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<view class=\"booking-options flex flex-y-center flex-bt form-item\" >\r\n\t\t\t\t\t\t\t<view class=\"book-title\">\r\n\t\t\t\t\t\t\t\t联系方式\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"room-form\">\r\n\t\t\t\t\t\t\t\t<input name=\"tel\" v-model=\"tel\"  placeholder=\"请填写联系方式\" style=\"text-align: right;\"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"form-item booking-options flex flex-y-center flex-bt\" v-for=\"(item,idx) in hotel.formdata\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"book-title\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+'_'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t<textarea :name=\"'form'+'_'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+'_'+idx\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form'+'_'+idx\" class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t \r\n\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+'_'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"hotel.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t <view v-if=\"(hotel.editorFormdata[idx]) || (hotel.editorFormdata[idx]===0)\"> {{item.val2[hotel.editorFormdata[idx]]}}</view>\r\n\r\n\t\t\t\t\t\t\t\t<view v-else >请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t\t\t<block  v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"hotel.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"hotel.editorFormdata[idx]\">{{hotel.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"hotel.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"hotel.editorFormdata[idx]\">{{hotel.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+'_'+idx\" :value=\"hotel.editorFormdata[idx]\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"hotel.editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" style=\"z-index: 2;\" @tap=\"removeimg\" :data-bid=\"hotel.bid\" :data-idx=\"idx\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"hotel.editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"hotel.editorFormdata[idx]\" mode=\"aspectFit\"/></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-bid=\"hotel.bid\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\r\n\t\t\t\t\t<!-- 本单可享 -->\r\n\t\t\t\t\t<view class=\"order-options-view flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"options-title\">本单可享</view>\r\n\t\t\t\t\t\t<view class=\"preferential-view flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t<view class=\"pre-title\">{{t('优惠券')}}</view>\r\n\t\t\t\t\t\t\t<view class=\"pre-text flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"couponList.length>0 && leftmoney>0 && !moneydec\" class=\"f2\" @tap=\"showCouponList\">\r\n\t\t\t\t\t\t<text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\" :style=\"{background:t('color1')}\">\r\n\t\t\t\t\t\t{{couponrid!=0?couponnametext:couponList.length+'张可用'}}\r\n\t\t\t\t\t \r\n\t\t\t\t\t</text><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t  </text></view>\r\n\t\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--余额抵扣-->\r\n\t\t\t\t\t<view class=\"price order-options-view flex flex-col\" v-if=\"hotel.money_dikou==1 && userinfo.money>0 \" >\r\n\t\t\t\t\t  <checkbox-group @change=\"moneydk\"  :data-rate=\"hotel.dikou_bl\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t    <view class=\"f1\">\r\n\t\t\t\t\t      <view>\r\n\t\t\t\t\t          使用{{t('余额')}}抵扣（余额：<text style=\"color:#e94745\">{{userinfo.money}}</text>{{moneyunit}}）\r\n\t\t\t\t\t      </view>\r\n\t\t\t\t\t      <view style=\"font-size:24rpx;color:#999\" >\r\n\t\t\t\t\t        1、选择此项提交订单时将直接扣除{{t('余额')}}\r\n\t\t\t\t\t      </view>\r\n\t\t\t\t\t       <!--<view style=\"font-size:24rpx;color:#999\" >\r\n\t\t\t\t\t        2、最多可抵扣房费的{{showmoneyrate}}%\r\n\t\t\t\t\t\t\t\t\t2、最多可抵扣房费{{dkmoney}}元\r\n\t\t\t\t\t      </view> -->\r\n\t\t\t\t\t    </view>\r\n\t\t\t\t\t    <view class=\"f2\" style=\"font-weight:normal\">\r\n\t\t\t\t\t      <checkbox value=\"1\" :checked=\"moneydec?true:false\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t    </view>\r\n\t\t\t\t\t  </checkbox-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--余额抵扣-->\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!--积分抵扣开始-->\r\n\t\t\t\t\t<view class=\"price  order-options-view flex flex-col\" \tv-if=\"hotel.score_dikou == 1 && userinfo.score>0 && hotel.scoredkmaxpercent>0\">\r\n\t\t\t\t\t\t<checkbox-group @change=\"scoredk\"  class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text\r\n\t\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\r\n\t\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"hotel.scoredkmaxpercent>0\">\r\n\t\t\t\t\t\t\t\t\t最多可抵扣房费金额的{{hotel.scoredkmaxpercent}}%</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" style=\"font-weight:normal\">\r\n\t\t\t\t\t\t\t\t<checkbox value=\"1\" :disabled=\"isdisabled\" :checked=\"usescore?true:false\"  style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--积分抵扣结束-->\r\n\t\t\t\r\n\t\t\t\t\t<!-- 特殊要求 -->\r\n\t\t\t\t\t<view class=\"order-options-view flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"options-title\">特殊要求</view>\r\n\t\t\t\t\t\t<view class=\"requirement-view\">\r\n\t\t\t\t\t\t\t<textarea name=\"message\" placeholder=\"填写您的入驻环境,服务等要求\"></textarea>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view style=\"height: 300rpx;\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t<!-- 底部按钮 -->\r\n\t\t\t\t<view class=\"but-view flex flex-col\"  >\r\n\t\t\t\t\t<view class=\"read-agreement flex flex-y-center\" v-if=\"hotel.xystatus==1\">\r\n\t\t\t\t\t\t<view :class=\"'select-view '+((isagree && isagree1)?'select-view-active':'')\"   :style=\"(isagree && isagree1) ?'border-color:'+t('color1')+';background-color:'+t('color1')+'':''\"   @tap=\"changeChecked\" >\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/checkd.png'\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t\t<view class=\"flex flex-y-center\">\r\n\t\t\t\t\t\t\t我已阅读并同意<view :style=\"ysfont_size > 0?'font-size:'+ysfont_size+'rpx;color:red':'color:red'\"  @tap=\"showxieyiFun2\"  >《{{hotel.ysname}}》</view>以及<view :style=\"ydfont_size > 0?'font-size:'+ydfont_size+'rpx;color:red':'color:red'\"   @tap=\"showxieyiFun\">《{{hotel.xyname}}》</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"yuding-view flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t<view class=\"price-view flex flex-col\">\r\n\t\t\t\t\t\t\t<view class=\"text\">共计:</view>\r\n\t\t\t\t\t\t\t<block v-if=\"moneydec\">\r\n\t\t\t\t\t\t\t\t<view class=\"price-text\" :style=\"{color:t('color1')}\" >￥{{totalprice}}+\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;\">{{moneydec?usemoney:0}}{{moneyunit}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"price-text\" :style=\"{color:t('color1')}\">￥{{totalprice}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex flex-row flex-y-center\">\r\n\t\t\t\t\t\t\t<view class=\"cost-details flex flex-y-center\"  @click=\"mignxiChange\"  :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t费用明细\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowdown.png'\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<button class='but-class1' :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\" form-type=\"submit\">预定</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\r\n\t\t\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t\t\t<parse :content=\"hotel.xycontent\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"xieyibut-view flex-y-center\">\r\n\t\t\t\t\t\t\t<view class=\"but-class\" style=\"background:#A9A9A9\"  @tap=\"closeXieyi\">不同意并退出</view>\r\n\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-if=\"hotel.isqianzi==1\"   @tap=\"goto\" data-url=\"signature\">去签字</view>\r\n\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-else  @tap=\"hidexieyi\">阅读并同意</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"showxieyi2\" class=\"xieyibox\">\r\n\t\t\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t\t\t<parse :content=\"hotel.yscontent\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"xieyibut-view flex-y-center\">\r\n\t\t\t\t\t\t\t<view class=\"but-class\" style=\"background:#A9A9A9\"  @tap=\"closeXieyi\">不同意并退出</view>\r\n\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi2\">阅读并同意</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 优惠券begin -->\r\n\t\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\r\n\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t<couponlist :couponlist=\"couponList\" :choosecoupon=\"true\" :selectedrid=\"couponrid\" :bid=\"hotel.bid\" @chooseCoupon=\"chooseCoupon\"></couponlist>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 优惠券end -->\r\n\t\t\t\t\r\n\t\t\t\t<!-- 费用明细弹窗 -->\r\n\t\t\t\t<uni-popup id=\"popup\" ref=\"popup\" type=\"bottom\">\r\n\t\t\t\t\t<view class=\"hotelpopup__content\">\r\n\t\t\t\t\t\t<view class=\"popup-close\" @click=\"popupClose\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose2.png`\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<scroll-view scroll-y style=\"height: auto;max-height: 50vh;\">\r\n\t\t\t\t\t\t\t<!-- 费用明细 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\" style=\"padding-bottom: 40rpx;\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">在线支付</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" style=\"margin-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text-title\">房费</view>\r\n\t\t\t\t\t\t\t\t\t\t<!--<view class=\"price-num-title\">￥{{roomprice}}</view>-->\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<!--<view class=\"price-view flex flex-bt flex-y-center\" v-for=\"(item,index) in roomprices\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{item.datetime}} </view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{item.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{t('余额')}}抵扣</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">{{moneydec?usemoney:0}}{{moneyunit}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">现金支付</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{leftmoney}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" style=\"margin-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text-title\">其他</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num-title\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">押金(可退)</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{yajin}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"service_money>0\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{text['服务费']}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{service_money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-col\" v-if=\"coupon_money>0 || usescore>0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" style=\"margin-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text-title\">优惠</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num-title\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"coupon_money>0\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">优惠券抵扣</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">-￥{{coupon_money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"usescore>0\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{t('积分')}}抵扣</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">-{{scoredk_money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t<view class=\"popup-but-view flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t<view class=\"price-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"text\">在线付:</view>\r\n\t\t\t\t\t\t\t\t<block v-if=\"moneydec\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-text\" :style=\"{color:t('color1')}\">￥{{totalprice}}\r\n\t\t\t\t\t\t\t\t\t\t<text  style=\"font-size: 24rpx;\">+{{moneydec?usemoney:0}}{{moneyunit}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-text\" :style=\"{color:t('color1')}\">￥{{totalprice}}</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex flex-row flex-y-center\">\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-y-center\" @click=\"popupClose\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t费用明细\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowdown.png'\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<button class='but-class' :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  form-type=\"submit\">极速支付</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\t\t\r\n\t\t\t<!-- 订房说明 -->\r\n\t\t\t\t<uni-popup id=\"popupBook\" ref=\"popupBook\" type=\"bottom\">\r\n\t\t\t\t\t<view class=\"hotelpopup__content\" style=\"background: #fff;\">\r\n\t\t\t\t\t\t<view class=\"popup-close\" @click=\"popupClose\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose2.png`\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t<view class=\"popup__title-text\">订房说明</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"booking_notice\">{{room.booking_notice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 详情弹窗 -->\r\n\t\t\t\t<uni-popup id=\"popupDetail\" ref=\"popupDetail\" type=\"bottom\">\r\n\t\t\t\t\t<view class=\"hotelpopup__content\">\r\n\t\t\t\t\t\t<view class=\"popup-close\" @click=\"popupdetailClose\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose.png`\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<scroll-view scroll-y style=\"height: auto;max-height: 70vh;\">\r\n\t\t\t\t\t\t\t<view class=\"popup-banner-view\" style=\"height: 450rpx;\">\r\n\t\t\t\t\t\t\t\t<swiper class=\"dp-banner-swiper\" :autoplay=\"true\" :indicator-dots=\"false\" :current=\"0\" :circular=\"true\" :interval=\"3000\" @change='swiperChange'>\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in room.pics\" :key=\"index\"> \r\n\t\t\t\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t\t\t\t<view @click=\"viewPicture(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item\" class=\"dp-banner-swiper-img\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t\t\t<view class=\"popup-numstatistics flex flex-xy-center\" v-if='room.pics.length'>\r\n\t\t\t\t\t\t\t\t\t{{bannerindex}} / {{room.pics.length}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"hotel-details-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-title\">{{room.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"introduce-view flex \">\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\"  v-if=\"room.bedxing!='0'\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedxing}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/pingfang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.square}}m²</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedwidth}}米</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t <view class=\"options-intro flex flex-y-center\" v-if=\"room.ischuanghu!=0\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/youchuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.ischuanghu}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n \r\n\t\t\t\t\t\t\t\t\t <view class=\"options-intro flex flex-y-center\" v-if=\"room.breakfast!=0\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/zaocan.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.breakfast}}早餐</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"other-view flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"other-title\">特色</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"other-text\" style=\"white-space: pre-line;\">{{room.tese}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 酒店权益 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\" v-if=\"qystatus == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">{{ qyname }}</view>\r\n\t\t\t\t\t\t\t\t\t <!--<view class=\"equity-title-tisp\">填写订单时兑换</view>-->\r\n\t\t\t\t\t\t\t\t</view>\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<parse :content=\"hotel.hotelquanyi\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 政策服务 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\"   v-if=\"fwstatus == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">{{ fwname }}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<parse :content=\"hotel.hotelfuwu\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\t\t\t\t</form>\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default{\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tisload:false,\r\n\t\t\t\tnavigationMenu:{},\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tplatform: app.globalData.platform,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tstartDate:'',\r\n\t\t\t\tendDate:'',\r\n\t\t\t\tdayCount:1,\r\n\t\t\t\tstartweek:'',\r\n\t\t\t\tendweek:'',\r\n\t\t\t\troom:[],\r\n\t\t\t\tbannerindex:1,\r\n\t\t\t\thotel:[],\r\n\t\t\t\tisagree:0,\r\n\t\t\t\tisagree1:0,\r\n\t\t\t\tshowxieyi:false,\r\n\t\t\t\tshowxieyi2:false,\r\n\t\t\t\tchangebookIndex:1,\r\n\t\t\t\tset:[],\r\n\t\t\t\ttext:[],\r\n\t\t\t\tnum:1,\r\n\t\t\t \r\n\t\t\t\tyhmoney:0,\r\n\t\t\t\ttotalprice:0,\r\n\t\t\t\tcouponmoney:0,\r\n\t\t\t\tservice_money:0,\r\n\t\t\t\tyajin:0,\r\n\t\t\t\troomprice:0,\r\n\t\t\t\ttotalroomprice:0,\r\n\t\t\t\troomprices:[],\r\n\t\t\t\tuserinfo:[],\r\n\t\t\t\tmoneydec:false,\r\n\t\t\t\tmoneyrate:0,\r\n\t\t\t\tdec_money:0,\r\n\t\t\t\tsignatureurl:'',\r\n\t\t\t\tdikoutext:[],\r\n\t\t\t\tyjcount:0,\r\n\t\t\t\tshowmoneyrate:0,\r\n\t\t\t\tdkmoney:0,\r\n\t\t\t\tusemoney:0,\r\n\t\t\t\tleftmoney:0,\r\n\t\t\t\teditorFormdata:[],\r\n\t\t\t\tname:'',\r\n\t\t\t\ttel:'',\r\n\t\t\t\tscoredkmaxmoney:0,\r\n\t\t\t\tisdisabled:false,\r\n\t\t\t\tusescore:0,\r\n\t\t\t\tscoredk_money:0,\r\n\t\t\t\tcouponList:[],\r\n\t\t\t  couponvisible: false,\r\n\t\t\t\tcouponrid: 0,\r\n\t\t\t\tcouponnametext:'',\r\n\t\t\t\tcoupon_money: 0,\r\n\t\t\t\ttmplids: [],\r\n\t\t\t\tqystatus:0,\r\n\t\t\t\tfwstatus:0,\r\n\t\t\t\tqyname:'',\r\n\t\t\t\tfwname:'',\r\n\t\t\t\tysfont_size:0,\r\n\t\t\t\tydfont_size:0,\r\n\t\t\t\tmoneyunit:'元',\r\n        startTime:'',\r\n        endTime:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\t\t\tthis.statusBarHeight = sysinfo.statusBarHeight;\t\t\t\r\n\t\t\tthis.wxNavigationBarMenu();\r\n      \r\n      this.startTime = app.getCache('startTime') || '';\r\n      this.endTime   = app.getCache('endTime') || '';\r\n      \r\n\t\t\tvar startDate = app.getCache('startDate');\r\n\t\t\tconsole.log(startDate);\r\n\t\t\tvar endDate = app.getCache('endDate');\r\n\t\t\tvar day = new Date(startDate).getDay();\r\n\t\t\tvar day2 = new Date(endDate).getDay();\r\n\t\t  var weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\tthis.startweek = weekdays[day];\r\n\t\t\tthis.endweek = weekdays[day2];\r\n\t\t\tthis.startDate = startDate\r\n\t\t\tthis.endDate = endDate\r\n\t\t\t\r\n\t\t\tthis.dayCount = this.opt.daycount\r\n\t\t\tthis.roomid = this.opt.roomid\r\n\t\t\tthis.getdata()\r\n\t\t\t//this.$refs.popup.open();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tapp.post('ApiHotel/buy', {id:that.roomid,dayCount:that.dayCount,startdate:that.startTime,enddate:that.endTime}, function (res) {\r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\tthat.set=res.set\r\n\t\t\t\t\t\t\tthat.text = res.text\r\n\t\t\t\t\t\t\tthat.room=res.room\t\r\n\t\t\t\t\t\t\tthat.hotel = res.hotel\r\n\t\t\t\t\t\t\tthat.ysfont_size = res.hotel.ysfontsize\r\n\t\t\t\t\t\t\tthat.ydfont_size = res.hotel.xyfontsize\r\n\t\t\t\t\t\t\tthat.qystatus = res.hotel.qystatus\r\n\t\t\t\t\t\t\tthat.fwstatus = res.hotel.fwstatus\r\n\t\t\t\t\t\t\tthat.qyname = res.hotel.qyname\r\n\t\t\t\t\t\t\tthat.fwname = res.hotel.fwname\r\n\t\t\t\t\t\t\tthat.roomprices = res.roomprices\r\n\t\t\t\t\t\t\tthat.totalroomprice  = res.totalroomprice\r\n\t\t\t\t\t\t\tthat.userinfo = res.userinfo\r\n\t\t\t\t\t\t\tthat.dikoutext = res.dikoutext\r\n\t\t\t\t\t\t\tthat.yjcount = res.yjcount;\r\n\t\t\t\t\t\t\tthat.moneyunit = res.moneyunit\r\n\t\t\t\t\t\t\tif(res.hotel.money_dikou==1){\r\n\t\t\t\t\t\t\t\tthat.moneydec = true;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.tmplids = res.tmplids\r\n\t\t\t\t\t\t\tvar couponList = res.couponList;\r\n\t\t\t\t\t\t\tthat.couponList = couponList;\r\n\t\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\t\t}else if (res.status == 0) {\r\n\t\t\t\t\t\t\tif (res.msg) {\r\n\t\t\t\t\t\t\t\tapp.alert(res.msg, function() {\r\n\t\t\t\t\t\t\t\t\tif (res.url) {\r\n\t\t\t\t\t\t\t\t\t\tapp.goto(res.url);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else if (res.url) {\r\n\t\t\t\t\t\t\t\tapp.goto(res.url);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tapp.alert('您没有权限预定该房型');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t })\r\n\t\t\t},\r\n\t\t\tmoneydk: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar moneydec = that.moneydec\r\n\t\t\t\tif(moneydec){\r\n\t\t\t\t\tmoneydec = false\r\n\t\t\t\t}else{\r\n\t\t\t\t\tmoneydec = true\r\n\t\t\t\t}\r\n\t\t\t\tthat.moneydec = moneydec\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t},\r\n\t\t\tshowCouponList: function () {\r\n\t\t\t  this.couponvisible = true;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function () {\r\n\t\t\t  this.couponvisible = false;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tchooseCoupon: function (e) { //选择优惠券\r\n\t\t\t\tvar couponrid = e.rid;\r\n\t\t\t  var couponkey = e.key;\r\n\t\t\t  if (couponrid == this.couponrid) {\r\n\t\t\t    this.couponkey = 0;\r\n\t\t\t    this.couponrid = 0;\r\n\t\t\t    this.coupontype = 1;\r\n\t\t\t    this.coupon_money = 0;\r\n\t\t\t    this.couponvisible = false;\r\n\t\t\t  } else {\r\n\t\t\t    var couponList = this.couponList;\r\n\t\t\t    var coupon_money = couponList[couponkey]['money'];\r\n\t\t\t    var coupontype = couponList[couponkey]['type'];\r\n\t\t\t    if (coupontype == 4) {\r\n\t\t\t      coupon_money = this.freightprice;\r\n\t\t\t    }\r\n\t\t\t\tthis.couponnametext = couponList[couponkey].couponname;\r\n\t\t\t \r\n\t\t\t    this.couponkey = couponkey;\r\n\t\t\t    this.couponrid = couponrid;\r\n\t\t\t    this.coupontype = coupontype;\r\n\t\t\t    this.coupon_money = coupon_money;\r\n\t\t\t    this.couponvisible = false;\r\n\t\t\t  }\r\n\t\t\t  this.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//积分抵扣\r\n\t\t\tscoredk: function(e) {\r\n\t\t\t\tvar that=this;\r\n\t\t\t\tif(this.leftmoney<=0){\r\n\t\t\t\t\tapp.error('房费已为0，不可使用'+that.t('积分')+'抵扣');return;\r\n\t\t\t\t}\r\n\t\t\t\tvar usescore = e.detail.value[0];\r\n\t\t\t\tif (!usescore) usescore = 0;\r\n\t\t\t\tthis.usescore = usescore;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t//计算价格\r\n\t\t\tcalculatePrice: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar userinfo = that.userinfo;\r\n\t\t\t\tvar money = userinfo && userinfo.money?parseFloat(userinfo.money):0;\r\n\t\t\t\tvar hotel = that.hotel;\r\n\t\t\t\tvar dikoutext = that.dikoutext\r\n\t\t\t\tvar rate  = '';\r\n\t\t\t\tvar num = that.num;\r\n\t\t\t\tvar daycount = that.dayCount;\r\n\t\t\t\tvar roomprices = that.roomprices;\r\n\t\t\t\tvar coupon_money = parseFloat(that.coupon_money); //-优惠券抵扣 \r\n\t\t\t\t\r\n\t\t\t\tvar usednum = 0;\r\n\t\t\t\tvar dkmoney = 0;\r\n\t\t\t\tvar usemoney = 0;\r\n\t\t\t\tfor(var i=0;i<num;i++){\r\n\t\t\t\t\tif(dikoutext.length==0) continue;\r\n\t\t\t\t\tvar thisdikou = dikoutext[0];\r\n\t\t\t\t\tfor(var k=0;k<dikoutext.length;k++){\r\n\t\t\t\t\t\tif(k <= i) thisdikou = dikoutext[k];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tfor(var j=0;j<daycount;j++){\r\n\t\t\t\t\t\tvar thisdkmoney = thisdikou.dikou_bl * that.roomprices[j].sell_price * 0.01;\r\n\t\t\t\t\t\tvar olddkmoney = dkmoney;\r\n\t\t\t\t\t\tdkmoney += thisdkmoney;\r\n\t\t\t\t\t\tvar oldusemoney = usemoney;\r\n\t\t\t\t\t\tif(hotel.money_dikou_type == 0){ //余额抵扣的是金额\r\n\t\t\t\t\t\t\tusemoney += thisdkmoney;\r\n\t\t\t\t\t\t\tif(usemoney > money){\r\n\t\t\t\t\t\t\t\tusemoney = money;\r\n\t\t\t\t\t\t\t\tdkmoney = money;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}else{ //余额抵扣的是天数 1余额代表1天\r\n\t\t\t\t\t\t\tusemoney += thisdikou.dikou_bl * that.roomprices[j].daymoney * 0.01;\r\n\t\t\t\t\t\t\tif(usemoney > money){\r\n\t\t\t\t\t\t\t\tusemoney = oldusemoney;\r\n\t\t\t\t\t\t\t\tdkmoney = olddkmoney;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(hotel.money_dikou_type == 1){\r\n\t\t\t\t\tthis.usemoney = Math.round(usemoney);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.usemoney = usemoney.toFixed(2);\r\n\t\t\t\t}\r\n\t\t\t\tvar leftmoney = 0;\r\n\t\t\t\tif(that.moneydec==1){\r\n\t\t\t\t\t\tleftmoney = (that.totalroomprice*num-dkmoney).toFixed(2);\r\n\t\t\t\t}else{\r\n\t\t\t\t\t\tleftmoney = (that.totalroomprice*num).toFixed(2);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\r\n\t\t\t\t//是否可积分抵扣\r\n\t\t\t\tif(leftmoney<=0){\r\n\t\t\t\t\tthat.usescore=0;\r\n\t\t\t\t\tthat.isdisabled=true;\r\n\t\t\t\t\tthat.coupon_money=0\r\n\t\t\t\t\tcoupon_money = 0;\r\n\t\t\t\t\tthat.couponkey = 0;\r\n\t\t\t\t\tthat.couponrid = 0;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.isdisabled=false;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.usescore) {\r\n\t\t\t\t\tvar scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\r\n\t\t\t\t\tvar scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣\r\n\t\t\t\t\tif (scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 &&\r\n\t\t\t\t\t\tscoredk_money > leftmoney * scoredkmaxpercent * 0.01) {\r\n\t\t\t\t\t\tscoredk_money = (leftmoney * scoredkmaxpercent * 0.01).toFixed(2);;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.scoredk_money = scoredk_money\r\n\t\t\t\t\tleftmoney =  (leftmoney-scoredk_money).toFixed(2);\r\n\t\t\t\t\tif (leftmoney < 0) leftmoney = 0;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar scoredk_money = 0;\r\n\t\t\t\t}\r\n\t\t\t\tthat.leftmoney = leftmoney-coupon_money;\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t/*\r\n\t\t\t\tfor(var i=0;i<dikoutext.length;i++){\r\n\t\t\t\t\tvar dikou = dikoutext[i];\r\n\t\t\t\t\tif(num>=dikou.dikou_num){\r\n\t\t\t\t\t\trate = dikou.dikou_bl;\r\n\t\t\t\t\t\tvar thisdkmoney = dikou.dikou_bl * that.totalroomprice * 0.01 * (dikou.dikou_num - usednum);\r\n\t\t\t\t\t\tdkmoney += thisdkmoney;\r\n\t\t\t\t\t\tusednum = dikou.dikou_num;\r\n\t\t\t\t\t\tif(hotel.money_dikou_type == 0){ //余额抵扣的是金额\r\n\t\t\t\t\t\t\tusemoney += thisdkmoney;\r\n\t\t\t\t\t\t}else{ //余额抵扣的是天数 1余额代表1天\r\n\t\t\t\t\t\t\tusemoney += Math.ceil(dikou.dikou_bl * 0.01);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(usemoney > money){\r\n\t\t\t\t\t\t\tusemoney = money;break;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t*/\r\n\t\t\t\tthat.dkmoney = dkmoney.toFixed(2);\r\n\t\t\t\tthat.showmoneyrate = rate\r\n\t\t\t\tthat.moneyrate = rate\r\n\t\t\t\t\r\n\t\t\t\tvar dkmoney = 0;\r\n\t\t\t\tvar dec_money = 0;\r\n\t\t\t\tif(that.moneydec==1){\r\n\t\t\t\t\tdkmoney = that.dkmoney;\r\n\t\t\t\t\tdec_money = dkmoney;\r\n\r\n\t\t\t\t\tif(userinfo && money>0){\r\n\t\t\t\t\t\tif(dec_money>=money){\r\n\t\t\t\t\t\t\tif(hotel.money_dikou_type == 0){\r\n\t\t\t\t\t\t\t\tdec_money = money; \r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.dec_money = dec_money;\r\n\r\n\t\t\t\tvar dayCount = that.dayCount;\r\n\t\t\t\tvar room = that.room\r\n\t\t\t\tvar service_money = 0;\r\n\t\t\t\tvar num = that.num; //间/人数\r\n\t\t\t\tvar hotel = that.hotel\r\n\t\t\t\tif(room.isservice_money==1){\r\n\t\t\t\t\tservice_money = room.service_money*num*dayCount;\r\n\t\t\t\t}\r\n\t\t\t\tthat.service_money = service_money.toFixed(2);\t\r\n\t\t\t\tvar yjcount = that.yjcount;\r\n\t\t\t\tvar yajin=0;\r\n\t\t\t\tif(yjcount==0){\r\n\t\t\t\t\tif(room.isyajin==1){\r\n\t\t\t\t\t\t\tyajin = room.yajin_money*num\r\n\t\t\t\t\t}else if(room.isyajin==2){\r\n\t\t\t\t\t\t\tyajin = room.yajin_money\r\n\t\t\t\t\t}else if(room.isyajin==-1){\r\n\t\t\t\t\t\t\tyajin=0;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif(that.hotel.isyajin==1){\r\n\t\t\t\t\t\t\t\tyajin = that.hotel.yajin_money*num\r\n\t\t\t\t\t\t}else if(that.hotel.isyajin==2){\r\n\t\t\t\t\t\t\t\tyajin = that.hotel.yajin_money\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tyajin = 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\t\t\r\n\t\t\t\t}\r\n\t\t\t\tvar roomprice =  parseFloat(that.totalroomprice)*num - dec_money - scoredk_money-coupon_money;\r\n\t\t\t  that.roomprice = roomprice.toFixed(2);\r\n\t\t\t\t\r\n\t\t\t\tthat.yajin =parseFloat(yajin).toFixed(2);\r\n\t\t\t\tthat.totalprice = (parseFloat(service_money) + parseFloat(yajin) + roomprice).toFixed(2);\r\n\t\t\t},\r\n\t\t\tcreateorder:function(e){\r\n\t\t\t\t\tvar that=this\r\n\t\r\n\t\t\t\t\tvar isagree = that.isagree\r\n\t\t\t\t\tif(!isagree && that.hotel.isqianzi==1){\r\n\t\t\t\t\t\treturn app.error(\"请先阅读并签订\"+that.hotel.xyname);\r\n\t\t\t\t\t}\telse if(!isagree &&  that.hotel.xystatus==1){\r\n\t\t\t\t\t\treturn app.error(\"请先阅读并同意\"+that.hotel.xyname);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar isagree1 = that.isagree1\r\n\t\t\t\t\tif(!isagree1 && that.hotel.xystatus==1){\r\n\t\t\t\t\t\treturn app.error(\"请先阅读并同意\"+that.hotel.ysname);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar signatureurl = that.signatureurl;\r\n\t\t\t\t\tif(!signatureurl && that.hotel.isqianzi==1){\r\n\t\t\t\t\t\t\treturn app.error(\"请先签订\"+that.hotel.xyname);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar num = that.num\r\n\t\t\t\t\tvar formdata = e.detail.value\r\n\t\t\t\t\tvar formname = {};\r\n\t\t\t\t\tfor (var i = 0; i < num;i++){\r\n\t\t\t\t\t\tvar thisfield = 'name' + i;\r\n\t\t\t\t\t\tif ( formdata[thisfield] === undefined || formdata[thisfield].length==0){\r\n\t\t\t\t\t\t\t\tapp.error('请填写姓名');return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tformname[i] = formdata[thisfield];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tformdata.names = formname\r\n\t\t\t\t\tif(!formdata.tel){\r\n\t\t\t\t\t\treturn app.error(\"请填写联系电话\");\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\tif(formdata.tel.trim()!= '' && !app.isPhone(formdata.tel)){\r\n\t\t\t\t\t\treturn app.error(\"请填写正确的手机号\");\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar hotel = that.hotel;\r\n\t\t\t\t\tconsole.log(hotel);\r\n\t\t\t\t\tvar formdata_fields = hotel.formdata;\r\n\t\t\t\t\t \r\n\t\t\t\t\tconsole.log(formdata);\r\n\t\t\t \r\n\t\t\t\t\tfor (var j = 0; j < formdata_fields.length;j++){\r\n\t\t\t\t\t\tvar thisfield = 'form'+ '_' + j;\r\n\t\t\t\t\t\t console.log(thisfield);\r\n\t\t\t\t\t\tif (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){\r\n\t\t\t\t\t\t\t\tapp.alert(formdata_fields[j].val1+' 必填');return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (formdata_fields[j].key == 'selector') {\r\n\t\t\t\t\t\t\t\tformdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]]\r\n\t\t\t\t\t\t}\t\t\t\t\t\t \r\n\t\t\t\t\t}\r\n\t\t\t\t \r\n\t\t\t\t\t \r\n\r\n\t\t\t\t\tvar couponkey = this.couponkey;\r\n\t\t\t\t\tvar couponrid = this.couponrid;\r\n          \r\n\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\t\tapp.post('ApiHotel/createOrder',{formdata:formdata,roomid:that.roomid,dayCount:that.dayCount,num:that.num,startDate:that.startDate,endDate:that.endDate,signatureurl:signatureurl,moneydec:that.moneydec,usescore:that.usescore,couponrid: couponrid,startTime:that.startTime,endTime:that.endTime}, function (res) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\t  that.subscribeMessage(function () {\r\n\t\t\t\t\t\t\t\t\tapp.goto('/pagesExt/pay/pay?id=' + res.payorderid,'redirectTo');\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tif (res.url) {\r\n\t\t\t\t\t\t\t\tapp.goto(res.url);\r\n\t\t\t\t\t\t\t} \r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t},\r\n\t\t\tinputname: function(e) {\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar name = e.detail.value;\r\n\t\t\t\tconsole.log(name)\r\n\t\t\t},\r\n\t\t\tchangebook(item){\r\n\t\t\t\t\tvar that=this\t\t\t\t\t\r\n\t\t\t\t\tthis.changebookIndex = item;\r\n\t\t\t\t\tthat.num = item\r\n\t\t\t\t\tthis.coupon_money = 0;\r\n\t\t\t\t\tvar startdate = app.getCache('startTime');\r\n\r\n\t\t\t \r\n\t\t\t\t\tthis.couponrid = 0\r\n\t\t\t\t\tapp.post('ApiHotel/getYajin',{num:item,roomid:that.roomid,startdate:startdate}, function (res) {\r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\tthat.yjcount = res.yjcount\r\n\t\t\t\t\t\t\tthat.couponList = res.couponlist\r\n\t\t\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\r\n\t\t\t},\r\n\t\t\t/***自定义表单 */\r\n\t\t\t\t\t//选择时间\r\n\t    \tchooseTime: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar prodata = that.prodata.split(',');\r\n\t\t\t\tthat.proid = prodata[0]\r\n\t\t\t\tthat.timeDialogShow = true;\r\n\t\t\t\tthat.timeIndex = -1;\r\n\t\t\t\tvar curTopIndex = that.datelist[0];\r\n\t\t\t\tthat.nowdate = that.datelist[that.curTopIndex].year+that.datelist[that.curTopIndex].date;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiYuyue/isgetTime', { date:that.nowdate,proid:that.proid}, function (res) {\r\n\t\t\t\t  that.loading = false;\r\n\t\t\t\t  that.timelist = res.data;\r\n\t\t\t\t})\r\n\t\t\t},\t\r\n \t\t\teditorChooseImage: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar editorFormdata = that.hotel.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\t\t// console.log(editorFormdata)\r\n\t\t\t\t\tthat.editorFormdata = editorFormdata\r\n\t\t\t\t\tthat.hotel.editorFormdata = editorFormdata\r\n\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tremoveimg:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar pics = that.editorFormdata\r\n\t\t\t\tpics.splice(idx,1);\r\n\t\t\t\tthat.editorFormdata = pics;\r\n\t\t\t\tthat.allbuydata[bid].editorFormdata = that.editorFormdata;\r\n\t\t\t},\r\n\t\t\teditorBindPickerChange:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\t\r\n\t\t\t\tvar editorFormdata = that.hotel.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t \r\n\t\t\t\tthat.hotel.editorFormdata = editorFormdata;\r\n\t\t\t \r\n\t\t\t\tlet aaa=that.hotel.formdata\r\n\t\t\t\tthat.hotel.formdata = [];\r\n\t\t\t\tthat.hotel.formdata = aaa;\r\n\t\t\t \r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t},\r\n\t\t\tremoveimg:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar pics = that.editorFormdata\r\n\t\t\t\tpics.splice(idx,1);\r\n\t\t\t\tthat.editorFormdata = pics;\r\n\t\t\t\tthat.allbuydata[bid].editorFormdata = that.editorFormdata;\r\n\t\t\t},\r\n\t\t \r\n\t\t \t/***自定义表单 */\r\n\t\t\tshowxieyiFun: function () {\r\n\t\t\t\tvar that=this\r\n\t\t\t\tif(!this.isagree1){\r\n\t\t\t\t\tapp.error('请先阅读并同意'+that.hotel.ysname);return;\r\n\t\t\t\t}\r\n\t\t\t  this.showxieyi = true;\r\n\t\t\t},\r\n\t\t\thidexieyi: function () {\r\n\t\t\t\tvar that=this\r\n\t\t\t  this.showxieyi = false;\r\n\t\t\t\tthat.isagree = true;\r\n\t\t\t},\r\n\t\t\thidexieyi2: function () {\r\n\t\t\t  this.showxieyi2 = false;\r\n\t\t\t\tthis.isagree1 = true;\r\n\t\t\t},\r\n\t\t\tshowxieyiFun2: function () {\r\n\t\t\t  this.showxieyi2 = true;\r\n\t\t\t},\r\n\t\t\t// 不同意协议\r\n\t\t\tcloseXieyi(){\r\n\t\t\t\tthis.showxieyi = false;\r\n\t\t\t\tthis.showxieyi2 = false;\r\n\t\t\t\tthis.isagree = false;\r\n\t\t\t},\r\n\t\t\tchangeChecked:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tif(!this.isagree){\r\n\t\t\t\t\t\tthat.isagree1 = 1;\r\n\t\t\t\t\t\tthat.isagree = 1;\r\n\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.isagree = 0;\r\n\t\t\t\t\t\tthat.isagree1 = 0;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(\tthat.isagree );\r\n\t\t\t},\r\n\t\t\tshowDetail:function(e){\r\n\r\n\t\t\t\t\t// 房型详情-------------------------------------------------------------------------------------------\r\n\t\t\t\t\tthis.$refs.popupDetail.open();\r\n\t\t\t},\r\n\t\t\tbookingChange(){\r\n\t\t\t\tthis.$refs.popupBook.open();\r\n\t\t\t},\r\n\t\t\tmignxiChange(){\r\n\t\t\t\tthis.$refs.popup.open();\r\n\t\t\t},\r\n\t\t\tpopupClose(){\r\n\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t},\t\r\n\t\t\tpopupdetailClose(){\r\n\t\t\t\tthis.$refs.popupDetail.close();\r\n\t\t\t},\r\n\t\t\tswiperChange(event){\r\n\t\t\t\tthis.bannerindex = event.detail.current+1;\r\n\t\t\t},\r\n\t\t\ttColor(text){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(text=='color1'){\r\n\t\t\t\t\tif(app.globalData.initdata.color1 == undefined){\r\n\t\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\t\tthat.tColor('color1')\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\treturn app.globalData.initdata.color1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(text=='color2'){\r\n\t\t\t\t\treturn app.globalData.initdata.color2;\r\n\t\t\t\t}else if(text=='color1rgb'){\r\n\t\t\t\t\tconsole.log(app.globalData.initdata.color1rgb,'/*-/*-/-*')\r\n\t\t\t\t\tif(app.globalData.initdata.color1rgb == undefined){\r\n\t\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\t\tthat.tColor('color1rgb')\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tvar color1rgb = app.globalData.initdata.color1rgb;\r\n\t\t\t\t\t\treturn color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(text=='color2rgb'){\r\n\t\t\t\t\tvar color2rgb = app.globalData.initdata.color2rgb;\r\n\t\t\t\t\treturn color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn app.globalData.initdata.textset[text] || text;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\twxNavigationBarMenu:function(){\r\n\t\t\t\tif(this.platform=='wx'){\r\n\t\t\t\t\t//胶囊菜单信息\r\n\t\t\t\t\tthis.navigationMenu = wx.getMenuButtonBoundingClientRect()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tgoback: function() {\r\n\t\t  var that = this;\r\n\t\t\tgetApp().goback();\r\n\t\t},\r\n\t}\r\n\r\n\r\n</script>\r\n\r\n<style>\r\n\t/*  */\r\n\t.uni-popup__wrapper-box{background: #f7f8fa;border-radius: 40rpx 40rpx 0rpx 0rpx;overflow: hidden;}\r\n\t.hotelpopup__content .popup-but-view{padding: 30rpx 20rpx;background: #fff;}\r\n\t.popup-but-view .price-view{} \r\n\t.popup-but-view .price-view .text{color: #222222;font-size: 24rpx;font-weight: bold;}\r\n\t.popup-but-view .price-view .price-text{color:#06D470;font-size: 36rpx;font-weight: bold;margin-top: 15rpx;}\r\n\t.popup-but-view .cost-details{color: #06D470;font-size: 24rpx;font-weight: bold;}\r\n\t.popup-but-view .cost-details image{width:24rpx;height: 24rpx;margin: 0rpx 20rpx 0rpx 10rpx;}\r\n\t.popup-but-view .but-class{background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);font-size: 32rpx;color: #fff;font-weight: bold;padding:0 30rpx;\twidth: 216rpx;border-radius: 60rpx;text-align: center;letter-spacing: 3rpx;}\r\n\t\r\n\t.hotelpopup__content{width: 100%;height:auto;position: relative;}\r\n\t.hotelpopup__content .popup-close{position: fixed;right: 20rpx;top: 20rpx;width: 56rpx;height: 56rpx;z-index: 11;}\r\n\t.hotelpopup__content .popup-close image{width: 100%;height: 100%;}\r\n\t.hotelpopup__content .hotel-equity-view{width: 100%;padding:30rpx 40rpx 40rpx;background: #fff;margin-top: 20rpx;}\r\n\t.hotel-equity-view .equity-title-view{align-items: center;justify-content: center;border-bottom: 1px #efefef solid;}\r\n\t.hotel-equity-view .equity-title-view .equity-title{color: #1E1A33;font-size: 30rpx;font-weight: bold;}\r\n\t\r\n\t.hotel-equity-view .equity-options{margin-top: 40rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view{align-items: center;justify-content: flex-start;}\r\n\t.hotel-equity-view .equity-options .options-title-view image{width: 28rpx;height: 28rpx;margin-right: 20rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view  .title-text{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t.hotel-equity-view .equity-options .options-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;padding: 15rpx 0rpx;line-height: 40rpx;margin-left: 50rpx;margin-right: 50rpx;}\r\n\t/*  */\r\n\t.hotel-equity-view .promotion-options{width: 100%;justify-content: space-between;padding: 12rpx 0rpx;}\r\n\t.hotel-equity-view .promotion-options image{width: 20rpx;height: 20rpx;}\r\n\t.hotel-equity-view .promotion-options .left-view{justify-content: flex-start;}\r\n\t.hotel-equity-view .promotion-options .left-view .logo-view{width: 80px;height: 20px;text-align: center;line-height: 18px;border-radius: 8rpx;border:1px solid;font-size: 20rpx;}\r\n\t.hotel-equity-view .promotion-options .left-view .logo-view-text{color: rgba(30, 26, 51, 0.8);font-size: 20rpx;padding-left: 30rpx;}\r\n\t/*  */\r\n\t.hotel-equity-view  .cost-details{width: 100%;padding-bottom: 30rpx;border-bottom: 1px #efefef solid;margin-top: 40rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view{padding-bottom: 10rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-num{color: #1E1A33;font-size: 24rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-text-title{color: rgba(30, 26, 51, 0.8);font-size: 30rpx;font-weight: bold;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-num-title{color: #1E1A33;font-size: 30rpx;font-weight: bold;}\r\n\t/*  */\r\n\t.but-view{width: 100%;background: #fff;position: fixed;bottom: 0rpx;padding: 20rpx;z-index: 2;box-shadow: 0rpx 0rpx 10rpx 5rpx #ebebeb;}\r\n\t.but-view .select-view{width: 32rpx;height: 32rpx;border-radius: 50%;border: 1px solid #D9D9DA;margin-right: 20rpx;}\r\n\t.but-view  .select-view image{width: 20rpx;height: 20rpx;}\r\n\t.read-agreement .t1{ font-weight: bold; font-size: 34rpx;}\r\n\t\r\n\t.but-view  .select-view-active{width: 32rpx;height: 32rpx;border-radius: 50%;border: 1px solid #06D470;margin-right: 20rpx;background: #06D470;\r\n\tdisplay: flex;align-items: center;justify-content: center;}\r\n\t.but-view  .select-view-active image{width: 20rpx;height: 20rpx;}\r\n\t.but-view .read-agreement{width: 100%;border-bottom: 1px #e6e6e6 solid;justify-content: flex-start;padding-bottom: 30rpx;padding-top:10rpx;color: #59595D;font-size: 24rpx;}\r\n\t.but-view .yuding-view{padding-bottom: env(safe-area-inset-bottom);padding-left: 20rpx;padding-right: 20rpx;padding-top: 30rpx;\t}\r\n\t.but-view .yuding-view .price-view{}\r\n\t.but-view .yuding-view .price-view .text{color: #222222;font-size: 24rpx;font-weight: bold;}\r\n\t.but-view .yuding-view .price-view .price-text{color:#06D470;font-size: 36rpx;font-weight: bold;margin-top: 15rpx;}\r\n\t.but-view .yuding-view .cost-details{color: #06D470;font-size: 24rpx;font-weight: bold;}\r\n\t.but-view .yuding-view .cost-details image{width:24rpx;height: 24rpx;margin: 0rpx 20rpx 0rpx 10rpx;}\r\n\t.but-view .yuding-view .but-class{background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);font-size: 32rpx;color: #fff;font-weight: bold;padding: 30rpx;\r\n\twidth: 216rpx;border-radius: 60rpx;text-align: center;letter-spacing: 3rpx;}\r\n\t.but-view  .yuding-view .but-class1{background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);font-size: 32rpx;color: #fff;font-weight: bold;\r\n\twidth: 216rpx;border-radius: 60rpx;text-align: center;letter-spacing: 3rpx;}\r\n\t\r\n\t/*  */\r\n\t.position-view{width: 100%;position: absolute;padding-bottom: 100rpx;}\r\n\t.position-view .order-time-details{width: 96%;margin: 0 auto;background: #fff;border-radius: 8px;padding:20rpx 30rpx;}\r\n\t.order-time-details .time-view{width: 100%;}\r\n\t.order-time-details .time-view .time-options{}\r\n\t.order-time-details .time-view .time-options .month-tetx{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t.order-time-details .time-view .time-options .day-tetx{color: rgba(30, 26, 51, 0.4);font-size: 22rpx;margin-left: 20rpx;}\r\n\t.order-time-details .time-view .content-text{width: 46px;height: 40rpx;line-height: 39rpx;text-align: center;border-radius: 20px;color: #000;font-size: 20rpx;border:1px #000 solid;position: relative;}\r\n\t.order-time-details .time-view .content-text .content-decorate{width: 13rpx;height: 2rpx;background: red;position: absolute;top: 50%;}\r\n\t.order-time-details .time-view .content-text .left-c-d{left: -13rpx;background: #000;}\r\n\t.order-time-details .time-view .content-text .right-c-d{right: -13rpx;background: #000;}\r\n\t.order-time-details .name-info{width: 100%;padding: 30rpx 0rpx;}\r\n\t.order-time-details .name-info .name-text {color: #1E1A33;font-size: 30rpx;font-weight: bold;}\r\n\t.order-time-details .name-info .name-tisp{color: #A5A3AD;font-size: 24rpx;margin-top: 15rpx;}\r\n\t.order-time-details .hotel-details-view{color: #06D470;font-size: 24rpx;}\r\n\t.order-time-details .hotel-details-view image{width: 22rpx;height: 22rpx;margin-left: 10rpx;}\r\n\t.order-time-details .time-warning{background: rgba(27, 204, 152, 0.06);width: 100%;padding: 20rpx 0rpx;border-radius: 8px;color: #06D470;font-size: 24rpx;justify-content: flex-start;}\r\n\t.order-time-details .time-warning image{width: 32rpx;height: 32rpx;margin-right: 20rpx;margin-left: 20rpx;}\r\n\t/*  */\r\n\t.position-view .order-options-view{width: 96%;margin: 20rpx auto 0rpx;background: #fff;border-radius: 8px;padding:20rpx 30rpx;}\r\n\t.order-options-view .options-title{color: #1E1A33;font-size: 32rpx;font-weight: bold;padding-bottom: 20rpx;}\r\n\t.order-options-view .preferential-view{padding: 20rpx 0rpx;}\r\n\t.order-options-view .preferential-view .pre-title{color: #888889;font-size: 28rpx;}\r\n\t.order-options-view .preferential-view .pre-text{color: #222229;font-size: 28rpx;}\r\n\t.order-options-view .preferential-view .pre-text image{width: 24rpx;height: 24rpx;margin-left: 20rpx;}\r\n\t/*  */\r\n\t.requirement-view{background: #F4F5F9;width: 100%;border-radius: 8px;height: 300rpx;margin-top: 20rpx;padding: 30rpx;}\r\n\t/*  */\r\n\t.titlebg-view{width: 1500rpx;height: 1500rpx;background: #08DA70;border-radius:50%;position: absolute;left: 50%;transform: translate(-50%,-70%);}\r\n\t/*  */\r\n\t.navigation {width: 100%;padding-bottom:10px;overflow: hidden;position: fixed;top: 0;z-index: 2;background: #08DA70;}\r\n\t.navcontent {display: flex;align-items: center;padding-left: 10px;}\r\n\t.header-location-top{position: relative;display: flex;justify-content: center;align-items: center;flex:1;}\r\n\t.header-back-but{position: absolute;left:0;display: flex;align-items: center;width: 40rpx;height: 45rpx;overflow: hidden;}\r\n\t.header-back-but image{width: 40rpx;height: 45rpx;} \r\n\t.header-page-title{font-size: 36rpx;}\r\n\r\n\r\n\t.hotelpopup__content .popup-banner-view{width: 100%;height: 500rpx;position: relative;}\r\n\t.hotelpopup__content .popup-banner-view .popup-numstatistics{position: absolute;right: 20rpx;bottom: 20rpx;background: rgba(0, 0, 0, 0.3);\r\n\tborder-radius: 28px;width: 64px;height: 28px;text-align: center;line-height: 28px;color: #fff;font-size: 20rpx;}\r\n\t.hotelpopup__content .hotel-details-view{width: 100%;padding: 30rpx 40rpx;background: #fff;}\r\n\t.hotelpopup__content .hotel-details-view\t.hotel-title{color: #1E1A33;font-size: 40rpx;}\r\n\t.hotelpopup__content .hotel-details-view\t.introduce-view{width: 100%;align-items: center;flex-wrap: wrap;justify-content: flex-start;padding: 20rpx 10rpx;}\r\n\t.hotelpopup__content .hotel-details-view\t.introduce-view .options-intro{padding: 15rpx 0rpx;margin-right: 20rpx;width:auto;}\r\n\t.hotel-details-view\t.introduce-view .options-intro image{width: 32rpx;height: 32rpx;}\r\n\t.hotel-details-view\t.introduce-view .options-intro .options-title{color: #1E1A33;font-size: 24rpx;margin-left: 15rpx;}\r\n\t.hotel-details-view .other-view{width: 100%;justify-content: flex-start;padding: 12rpx 0rpx;}\r\n\t.hotel-details-view .other-view .other-title{color: #A5A3AD;font-size: 24rpx;margin-right: 40rpx;}\r\n\t.hotel-details-view .other-view .other-text{color: #1E1A33;font-size: 24rpx;}\t\r\n\t\r\n\t /*  */\r\n\t.dp-banner{width: 100%;height: 250px;}\r\n\t.dp-banner-swiper{width:100%;height:100%;}\r\n\t.dp-banner-swiper-img{width:100%;height:auto}\r\n\t.banner-poster{width: 82%;margin: 30rpx auto 0rpx;display: flex;flex-direction:column;align-items: flex-end;}\r\n\t.banner-poster .poster-title{color: #FFFFFF;font-size: 56rpx;font-weight: 900;padding: 30rpx 0rpx;}\r\n\t.banner-poster .poster-text{color: #FFFFFF;font-size: 26rpx;opacity: 0.6;padding: 10rpx 0rpx;}\r\n\t.banner-poster .poster-but{width: 108px;height: 36px;color: #FFFFFF;text-align: center;line-height: 36px;font-size: 28rpx;font-weight: bold;margin: 40rpx 0rpx;border-radius: 36px;}\r\n\t/*  */\r\n\t\r\n\t/*  */\r\n\t.hotelpopup__content .hotel-equity-view{width: 100%;padding:30rpx 40rpx 40rpx;background: #fff;}\r\n\t.hotel-equity-view .equity-title-view{align-items: center;justify-content: flex-start; padding: 20rpx 0;}\r\n\t.hotel-equity-view .equity-title-view .equity-title{color: #1E1A33;font-size: 32rpx;font-weight: bold;}\r\n\t.hotel-equity-view .equity-title-view .equity-title-tisp{color: #A5A3AD;font-size: 24rpx;margin-left: 28rpx;}\r\n\t.hotel-equity-view .equity-options{margin-top: 40rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view{align-items: center;justify-content: flex-start;}\r\n\t.hotel-equity-view .equity-options .options-title-view image{width: 28rpx;height: 28rpx;margin-right: 20rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view  .title-text{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t.hotel-equity-view .equity-options .options-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;padding: 15rpx 0rpx;line-height: 40rpx;margin-left: 50rpx;margin-right: 50rpx;}\r\n\t/*  */\r\n\t\r\n\t.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\r\n\t.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\r\n\t.xieyibox-content .xieyibut-view{height: 60rpx;position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;justify-content: space-around;}\r\n\t.xieyibox-content .xieyibut-view .but-class{text-align:center; width: auto;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;padding:0rpx 25rpx;}\r\n\t\r\n\t.position-view .order-options-view{width: 96%;margin: 20rpx auto 0rpx;background: #fff;border-radius: 8px;padding:20rpx 30rpx;}\r\n\t\t.order-options-view .options-title{color: #1E1A33;font-size: 32rpx;font-weight: bold;padding-bottom: 20rpx;}\r\n\t\t/* +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ */\r\n\t\t.order-options-view .options-title .right-view{color: #1BCC98;font-size: 24rpx;}\r\n\t\t.order-options-view .options-title .right-view image{width: 24rpx;height: 24rpx;margin-left: 10rpx;}\r\n\t\t.order-options-view .booking-options{width: 100%;padding: 20rpx 0rpx;}\r\n\t\t.order-options-view .booking-options .book-title{color: #888889;font-size: 28rpx;text-align: left;width: 140rpx;}\r\n\t\t.order-options-view .booking-options .room-number-view{width: 510rpx;}\r\n\t\t.order-options-view .booking-options .room-number-view .room-options{background: #F8F9FD;border-radius: 4px;padding: 18rpx 21rpx;display: inline-block;\r\n\t\tmargin-right: 20rpx;color: #222222;font-size: 28rpx;}\r\n\t\t.order-options-view .booking-options .room-number-view .room-options-active{border: 1px solid rgba(27, 204, 152, 0.5);background: #E8FFF7;box-sizing: border-box;color: #06D470;}\r\n\t\t.order-options-view .booking-options .room-form{width: 510rpx;padding: 18rpx 0rpx;}\r\n\t\t.order-options-view .booking-options .room-form input{width: 100%; font-size: 24rpx;}\r\n\t\t/* ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ */\r\n\t\t.order-options-view .preferential-view{padding: 20rpx 0rpx;}\r\n\t\t.order-options-view .preferential-view .pre-title{color: #888889;font-size: 28rpx;}\r\n\t\t.order-options-view .preferential-view .pre-text{color: #222229;font-size: 28rpx;}\r\n\t\t.order-options-view .preferential-view .pre-text image{width: 24rpx;height: 24rpx;margin-left: 20rpx;}\r\n\t\t/*  */\r\n\t\t.requirement-view{background: #F4F5F9;width: 100%;border-radius: 8px;height: 300rpx;margin-top: 20rpx;padding: 30rpx;}\r\n\t\t/*  */\r\n\t\t.titlebg-view{width: 1500rpx;height: 1500rpx;background: #08DA70;border-radius:50%;position: absolute;left: 50%;transform: translate(-50%,-70%);}\r\n\t\t/*  */\r\n\t\t.navigation {width: 100%;padding-bottom:10px;overflow: hidden;position: fixed;top: 0;z-index: 2;background: #08DA70;}\r\n\t\t.navcontent {display: flex;align-items: center;padding-left: 10px;}\r\n\t\t.header-location-top{position: relative;display: flex;justify-content: center;align-items: center;flex:1;}\r\n\t\t.header-back-but{position: absolute;left:0;display: flex;align-items: center;width: 40rpx;height: 45rpx;overflow: hidden;}\r\n\t\t.header-back-but image{width: 40rpx;height: 45rpx;} \r\n\t\t.header-page-title{font-size: 36rpx;}\r\n\t\t\r\n\t\t/*余额抵扣*/\r\n\t\t.price .f1 {color: #333}\r\n\t\t.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n\t\t.price .f3 {width: 24rpx;height: 24rpx;}\r\n\t\t.price .couponname{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;display:inline-block;margin:2rpx 0 2rpx 10rpx}\r\n\t\t/*订房说明*/\r\n\t\t.booking_notice{ padding: 30rpx;}\r\n\r\n\r\n\t\t.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\r\n\t\t.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\r\n\t\t.form-item .radio{transform:scale(.7);}\r\n\t\t.form-item .checkbox{transform:scale(.7);}\r\n\t\t.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\r\n\t\t.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx;margin-left: 36rpx;}\r\n\t\t.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n\t\t.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n\t\t.form-item .radio2{display:flex;align-items:center;}\r\n\t\t.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n\t\t.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n\t\t.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n\t\t.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n\t\t.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n\t\t.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\t\t.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n\t\t.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n\t\t.form-imgbox-close .image{width:100%;height:100%}\r\n\t\t.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n\t\t.form-imgbox-img>.image{width:100%;height:100%}\r\n\t\t.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n\t\t.form-uploadbtn{position:relative;height:180rpx;width:180rpx;margin-right: 16rpx;margin-bottom:10rpx;}\r\n\t</style>\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839390392\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}