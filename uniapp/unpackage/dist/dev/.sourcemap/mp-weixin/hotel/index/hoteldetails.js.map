{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/hoteldetails.vue?5067", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/hoteldetails.vue?5d55", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/hoteldetails.vue?25a0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/hoteldetails.vue?1d2a", "uni-app:///hotel/index/hoteldetails.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/hoteldetails.vue?326e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/hoteldetails.vue?2d24"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "calendar", "data", "isload", "startDate", "endDate", "hotelid", "hotel", "navigationMenu", "statusBarHeight", "platform", "pre_url", "text", "roomlist", "bannerindex", "bannerList", "dayCount", "startWeek", "endWeek", "starttime", "pagecontent", "endtime", "roomList", "room", "yajin", "service_money", "totalprice", "photos", "pindex", "photolist", "grouplist", "gindex", "gids", "nomore", "nodata", "totalpagenum", "commentlist", "haoping", "maxen<PERSON>ate", "dayroomprice", "minday", "minstock", "calendarvisible", "maxdays", "pagenum", "qystatus", "fwstatus", "qyname", "fwname", "roomstyle", "btnstyle", "nature", "moneyunit", "onLoad", "app", "that", "methods", "getdata", "daycount", "uni", "title", "getdetail", "longitude", "latitude", "console", "handleClickMask", "showmore", "selectDate", "getDate", "popupClose", "hotelDetail", "id", "groupChange", "newgids", "ischecked", "openLocation", "name", "scale", "chanagePhoto", "calculatePrice", "tobuy", "popupdetailClose", "swiper<PERSON><PERSON>e", "tColor", "clearInterval", "wxNavigationBarMenu", "viewPicture", "urls", "goback", "getApp"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxLA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACqd91B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;MACA;MACA;IACA;MACA;MACAC;QACAC;QACAA;QACAD;QACAA;MACA;IACA;IAEA;IACA;IACA;EACA;;EACAE;IACAC;MACA;MACAH;QACA;UACAC;UACAA;UACAA;UACAA;UACA;UACA;UACA;UACA;YACAD;YACAnC;UACA;UACA;YACAmC;YACAjC;UACA;UACA;YACAiC;YACAI;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAEAH;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAI;YACAC;UACA;UAEAL;UACAA;QACA;MACA;IACA;IACAM;MACA;MACA;QACA;QACA;MACA;MACA;MACAN;MACAA;MACAA;MACA;MACA;MACAD;QAAAhD;QAAAsC;QAAAZ;QAAAb;QAAAE;QAAAqC;QAAAI;QAAAC;MAAA;QACAR;QACAI;QACA;UACA;UACA;YACAJ;YACAA;YACAA;YACAA;YACAA;YAEAA;YACA;cACAA;YACA;YACAA;YACAA;YACAS;YACAT;YACA;cACAA;YACA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;YACA;UACA;QAEA;MACA;IACA;IACAU;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACAd;QACAC;MACA;MACA;QACA;QACAD;QACA;QACAA;QAEAC;QACAA;QACAA;MACA;MACA;QACA;QACAD;QACA;QACAA;QACAC;QACAA;QACAA;MACA;MACA;MACA;MACA;QACA;QACA;UACAf;QACA;MACA;MACAe;MACAA;IACA;IACAc;MACA;MACA;IACA;IACAC;MACA;MACA;MACAhB;QAAAiB;QAAAnE;QAAAC;QAAAW;MAAA;QACAgD;QACA;UACAT;UAEA;UACAA;UAGAA;UACAA;QACA;MACA;MACA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAC;QACA;MACA;MACA;QACAD;MACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACAhB;QACAI;QACAD;QACAc;QACAC;MACA;IACA;IACAC;MACA;MACA;MAEAvB;MACAA;IACA;IACA;IACAwB;MACA;MACA;MACA;MACAf;MACA;MACA;MACA;QACAvC;MACA;MACA8B;MACA;MACA;QACA/B;MACA;QACAA;MACA;QACA;UACAA;QACA;UACAA;QACA;MACA;MACA+B;MACA;MACA;QACA7B;MACA;QACAA;MACA;MACA6B;MAAA;IACA;IACAyB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA1B;IACA;IACA2B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;UACA;YACA5B;UACA;UACA6B;QACA;UACA;QACA;MACA;QACA;MACA;QACA;UACA;YACA7B;UACA;UACA6B;QACA;UACA;UACA;QACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA3B;QACA4B;MACA;IACA;IACAC;MACA;MACAC;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACp2BA;AAAA;AAAA;AAAA;AAAurC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "hotel/index/hoteldetails.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './hotel/index/hoteldetails.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./hoteldetails.vue?vue&type=template&id=11c006f4&\"\nvar renderjs\nimport script from \"./hoteldetails.vue?vue&type=script&lang=js&\"\nexport * from \"./hoteldetails.vue?vue&type=script&lang=js&\"\nimport style0 from \"./hoteldetails.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"hotel/index/hoteldetails.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hoteldetails.vue?vue&type=template&id=11c006f4&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    calendar: function () {\n      return import(\n        /* webpackChunkName: \"components/calendar/calendar\" */ \"@/components/calendar/calendar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.tColor(\"color1\") : null\n  var g0 = _vm.isload ? _vm.hotel.tag.length : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.grouplist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.inArray(item.id, _vm.gids)\n        var m2 = m1 ? _vm.t(\"color1rgb\") : null\n        var m3 = m1 ? _vm.tColor(\"color1\") : null\n        return {\n          $orig: $orig,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.roomlist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var l1 =\n          _vm.roomstyle == 1\n            ? _vm.__map(item.tag, function (items, indexs) {\n                var $orig = _vm.__get_orig(items)\n                var m4 = _vm.t(\"color1rgb\")\n                var m5 = _vm.tColor(\"color1\")\n                return {\n                  $orig: $orig,\n                  m4: m4,\n                  m5: m5,\n                }\n              })\n            : null\n        var m6 = item.daymoney ? _vm.t(\"color1\") : null\n        var m7 = !item.daymoney ? _vm.t(\"color1\") : null\n        var m8 =\n          _vm.btnstyle == 1 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m9 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        var m10 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        var m11 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        var m12 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          l1: l1,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n          m10: m10,\n          m11: m11,\n          m12: m12,\n        }\n      })\n    : null\n  var m13 =\n    _vm.isload && _vm.totalpagenum > _vm.pagenum ? _vm.t(\"color1\") : null\n  var g1 = _vm.isload && _vm.hotel.comment == 1 ? _vm.commentlist.length : null\n  var m14 =\n    _vm.isload && _vm.hotel.comment == 1 && g1 > 0 ? _vm.t(\"color1\") : null\n  var l3 =\n    _vm.isload && _vm.hotel.comment == 1 && g1 > 0\n      ? _vm.__map(_vm.commentlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m15 = _vm.t(\"color1rgb\")\n          var m16 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m15: m15,\n            m16: m16,\n          }\n        })\n      : null\n  var g2 = _vm.isload ? _vm.bannerList.length : null\n  var g3 = _vm.isload && g2 ? _vm.bannerList.length : null\n  var m17 = _vm.isload && _vm.room.isdaymoney == 1 ? _vm.t(\"余额\") : null\n  var m18 = _vm.isload && _vm.room.isdaymoney == 1 ? _vm.t(\"余额\") : null\n  var m19 = _vm.isload ? _vm.t(\"color1\") : null\n  var m20 =\n    _vm.isload && _vm.minstock > 0 && !_vm.room.isbooking\n      ? _vm.tColor(\"color1rgb\")\n      : null\n  var m21 =\n    _vm.isload && _vm.minstock > 0 && !_vm.room.isbooking\n      ? _vm.tColor(\"color1rgb\")\n      : null\n  var m22 = _vm.isload && _vm.calendarvisible ? _vm.t(\"color1\") : null\n  var m23 = _vm.isload && _vm.calendarvisible ? _vm.tColor(\"color1rgb\") : null\n  var m24 = _vm.isload && _vm.calendarvisible ? _vm.tColor(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l0: l0,\n        l2: l2,\n        m13: m13,\n        g1: g1,\n        m14: m14,\n        l3: l3,\n        g2: g2,\n        g3: g3,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hoteldetails.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hoteldetails.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t\r\n\t\t\t<!-- #ifndef H5 || APP-PLUS -->\r\n\t\t\t\t<view class=\"navigation\">\r\n\t\t\t\t\t<view class='navcontent' :style=\"{marginTop:navigationMenu.top+'px',width:(navigationMenu.right)+'px'}\">\r\n\t\t\t\t\t\t<view class=\"header-location-top\" :style=\"{height:navigationMenu.height+'px'}\">\r\n\t\t\t\t\t\t\t<view class=\"header-back-but\" @tap=\"goback\">\r\n\t\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/fanhui.png`\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"header-page-title\" style=\"color:#fff;\">{{text['酒店']}}详情</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t<!--  #endif -->\r\n\t\t\t\t\r\n\t\t\t\t<!--  -->\r\n\t\t\t\t<view class='dp-banner'>\r\n\t\t\t\t\t<swiper class=\"dp-banner-swiper\" :autoplay=\"true\" :indicator-dots=\"false\" :current=\"0\" :circular=\"true\" :interval=\"3000\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in photolist\" :key=\"index\"> \r\n\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t<view @click=\"viewPicture(item)\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item\" class=\"dp-banner-swiper-img\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</swiper>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--  -->\r\n\t\t\t\t<view class=\"position-view\">\r\n\t\t\t\t\t<view class=\"banner-tab-view\">\r\n\t\t\t\t\t\t<scroll-view scroll-x style=\"width: auto;white-space: nowrap;margin-top: 7.5rpx;\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in photos\">\r\n\t\t\t\t\t\t\t\t<view :class=\"'tab-options-banner '+(index==pindex?'tab-options-banner-active':'')\" @tap=\"chanagePhoto\" :data-index='index'>{{item.name}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t<!--<view class='tab-options-banner'>公共区域</view>\r\n\t\t\t\t\t\t<view class='tab-options-banner tab-options-banner-active'>封面</view>\r\n\t\t\t\t\t\t<view class='tab-options-banner '>大楼外部</view>-->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content-view\" :style=\"{background: 'linear-gradient(180deg, '+ tColor('color1') +' 0%, #FFFFFF 45px, #FFFFFF 63%, #FFFFFF 100%)'}\">\r\n\t\t\t\t\t\t<view class=\"title-view\">{{hotel.name}}</view>\r\n\t\t\t\t\t\t<view class=\"hotel-nature\">\r\n\t\t\t\t\t\t\t<view class=\"star-view\" v-if=\"hotel.hotellevel>0\">\r\n\t\t\t\t\t\t\t\t<image class=\"start\"  :src=\"pre_url+'/static/img/star2native.png'\" v-for=\"(item,index) in hotel.hotellevel+2\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"hotspot-nature-text\" v-if=\"nature != null\">{{ nature }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"hotspot-view\" v-if=\"hotel.tag.length\">\r\n\t\t\t\t\t\t\t<view class=\"hotspot-view-left\">\r\n\t\t\t\t\t\t\t\t<view class='hotspot-options' v-for=\"(item,index) in hotel.tag\">\r\n\t\t\t\t\t\t\t\t\t{{item}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!--<view class=\"hotspot-more\">\r\n\t\t\t\t\t\t\t\t设施/详情\r\n\t\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/gengduo.png`\"></image>\r\n\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"address-view\">\r\n\t\t\t\t\t\t\t<view style=\"flex: 1;\">\r\n\t\t\t\t\t\t\t\t<view class=\"address-text\">{{hotel.address}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"address-traffic\"><image :src=\"`${pre_url}/static/img/hotel/address.png`\"></image>距您{{hotel.juli}} </view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"fangshi-view\">\r\n\t\t\t\t\t\t\t\t<view class=\"fagnshi-options\" @tap=\"openLocation\" :data-latitude=\"hotel.latitude\" :data-longitude=\"hotel.longitude\" :data-company=\"hotel.name\" :data-address=\"hotel.address\" >\r\n\t\t\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/dingwei.png`\"></image>\r\n\t\t\t\t\t\t\t\t\t<view>导航</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!--<view class=\"fagnshi-options\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/dache.png`\"></image>\r\n\t\t\t\t\t\t\t\t\t<view>打车</view>\r\n\t\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!--  -->\r\n\t\t\t\t\t\t<view class=\"time-view flex flex-y-center flex-bt\" @tap=\"selectDate\">\r\n\t\t\t\t\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t<view class=\"month-tetx\">{{startDate}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"day-tetx\">{{startWeek}}入住</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content-text\">\r\n\t\t\t\t\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t\t\t\t\t共{{dayCount}}晚\r\n\t\t\t\t\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t<view class=\"month-tetx\">{{endDate}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"day-tetx\">{{endWeek}}离店</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--  -->\r\n\t\t\t\t\t<view class=\"screen-view\">\r\n\t\t\t\t\t\t<view class=\"screen-view-left\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in grouplist\">\r\n\t\t\t\t\t\t\t\t<view :class=\"'screen-options '\" :style=\"inArray(item.id,gids) ?'background:rgba('+t('color1rgb')+',0.05);color:'+tColor('color1'):''\"  :data-id=\"item.id\"  @tap=\"groupChange\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!--<view class=\"right-screen\">\r\n\t\t\t\t\t\t\t筛选<image :src=\"`${pre_url}/static/img/hotel/screenicon.png`\"></image>\r\n\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--  -->\r\n\t\t\t\t\t<view class=\"hotels-list\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in roomlist\" >\r\n\t\t\t\t\t\t\t<view class=\"hotels-options\" @tap.stop=\"hotelDetail\" :data-id='item.id'>\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-img\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" v-if=\"roomstyle ==1\"></image>\r\n\t\t\t\t\t\t\t\t\t<image class=\"fangxing\" :src=\"item.pic\" v-else></image>\r\n\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-characteristic\" v-if=\"roomstyle ==1\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(items,indexs) in item.tag\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"characteristic-options\" :style=\"'background:rgba('+t('color1rgb')+',0.05);color:'+tColor('color1')\">{{items}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-characteristic \" v-else>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"under_title\" v-if=\"item.bedxing!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.bedxing}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"under_title\">\r\n\t\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.square}}m²</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"under_title\" v-if=\"item.ischuanghu!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.ischuanghu}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"under_title\" v-if=\"item.breakfast!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.breakfast}}早餐</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-but-view\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"make-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price\" :style=\"{color:t('color1')}\" v-if=\"item.daymoney\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price-num\">{{item.daymoney}}{{moneyunit}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view>/晚起</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price\" :style=\"{color:t('color1')}\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view>￥</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price-num\">{{item.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view>起</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-text\" v-if=\"roomstyle == 1\">{{item.sales}}人已预定 | 剩{{item.stock}}{{text['间']}}可订</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-text\" v-else>{{item.sales}}人已预定</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<block  v-if=\"btnstyle == 1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-make\" v-if=\"item.stock>0 && !item.isbooking\"  :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\">预约</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-make\" v-else-if=\"item.isbooking\" style=\"background:#999999; color:#fff\">不可订</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-make\" v-else style=\"background:#999999; color:#fff\">已满</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"btnstyle == 2\">\r\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.stock>0 && !item.isbooking\" class=\"hotel-make-new\" :style=\"{borderColor:t('color1')}\">\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"make-new-view\" :style=\"'background:'+t('color1')+''\">订</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"make-new-view2\" :style=\"{color:t('color1'),borderColor:t('color1')}\">剩{{item.stock}}{{text['间']}}</view>\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view  v-else-if=\"item.isbooking\"  class=\"hotel-make-new\" style=\"background:#999999;border: 1px #999999 solid; color:#fff\">\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"make-new-view\" style=\"background:#999999; color:#fff\">不可订</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"make-new-view2\" style=\"background:#999999;border-bottom: 1px #999999 solid; color:#fff\">剩{{item.stock}}{{text['间']}}</view>\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view  v-else class=\"hotel-make-new\" style=\"background:#999999;border: 1px #999999 solid; color:#fff\">\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"make-new-view\" style=\"background:#999999; color:#fff\">已满</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"make-new-view2\" style=\"background:#999999;border-bottom: 1px #999999 solid; color:#fff\">剩{{item.stock}}{{text['间']}}</view>\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<nomore text=\"没有更多了\" v-if=\"nomore\"></nomore>\r\n\t\t\t\t\t<nodata text=\"没有查找到相关房型\" v-if=\"nodata\"></nodata>\r\n\t\t\t\t\t<!--  -->\r\n\t\t\t\t\t<view class=\"hotel-more\"  :style=\"{color:t('color1')}\"  v-if=\"totalpagenum>pagenum\" @tap=\"showmore\">\r\n\t\t\t\t\t\t查看剩余房型\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/mroe-hotel.png`\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 酒店设施 -->\r\n\t\t\t\t\t<view class=\"work-order-view flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t\t\t<view class=\"work-order-title-left\">配套设施</view>\r\n\t\t\t\t\t\t\t<!--<view class=\"work-order-title-right\">\r\n\t\t\t\t\t\t\t\t更多详情<image :src=\"`${pre_url}/static/img/hotel/rightjiantou.png`\"></image>\r\n\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"facilities-view flex \">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in hotel.ptsheshi\">\r\n\t\t\t\t\t\t\t\t<view class=\"options-facilities flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.icon\"></image>\r\n\t\t\t\t\t\t\t\t\t<view class=\"facilities-text\">{{item.text}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 订房必读 -->\r\n\t\t\t\t\t<view class=\"work-order-view flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t\t\t<view class=\"work-order-title-left\">订房须知</view>\r\n\t\t\t\t\t\t\t<!--<view class=\"work-order-title-right\">\r\n\t\t\t\t\t\t\t\t全部政策<image :src=\"`${pre_url}/static/img/hotel/rightjiantou.png`\"></image>\r\n\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"required-reading-view\">\r\n\t\t\t\t\t\t\t\t\t\t<parse :content=\"hotel.content\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 评价 -->\r\n\t\t\t\t\t<view class=\"work-order-view flex flex-col\" v-if=\"hotel.comment == 1\">\r\n\t\t\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t\t\t<view class=\"work-order-title-left\">住客评价</view>\r\n\t\t\t\t\t\t\t<view class=\"work-order-title-right\" @tap=\"goto\" :data-url=\"'/hotel/order/commentlist?hotelid='+hotel.id\"><image :src=\"`${pre_url}/static/img/hotel/rightjiantou.png`\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"score-view\" v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t\t\t<view class=\"score-content flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t<view class=\"total-score-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"total-score-num flex flex-y-center\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<view class='total-num'>{{hotel.comment_score}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"tatal-describe\">{{haoping}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"evaluate-list-view flex flex-col\" >\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in commentlist\" >\r\n\t\t\t\t\t\t\t\t\t<view class=\"text-options flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"user-info-view flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"info-view flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image class=\"avater-view\" :src=\"item.headimg\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"name-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"name-text\">{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"time-view\">{{item.createtime}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"scoring-view flex flex-xy-center\" :style=\"'background:rgba('+t('color1rgb')+',0.08);color:'+t('color1')\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view>{{item.score}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"font-size: 20rpx;margin-left:5rpx;\">分</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"evaluate-imgList flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"(pic,index) in item.content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"img-options\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"evaluate-value\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{item.content}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else class=\"\">\r\n\t\t\t\t\t\t\t\t<nodata text=\"没有查找到记录\"></nodata>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<!-- 详情弹窗 -->\r\n\t\t\t\t<uni-popup id=\"popup\" ref=\"popup\" type=\"bottom\" >\r\n\t\t\t\t\t<view class=\"popup__content\" style=\"bottom: 0;padding-top:0;padding-bottom:0; max-height: 86vh; \">\r\n\t\t\t\t\t\t<view class=\"popup-close\" @click=\"popupdetailClose\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose.png`\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<scroll-view scroll-y style=\"height: auto;max-height: 90vh;\">\r\n\t\t\t\t\t\t\t<view class=\"popup-banner-view\" style=\"height: 450rpx;\">\r\n\t\t\t\t\t\t\t\t<swiper class=\"dp-banner-swiper\" :autoplay=\"true\" :indicator-dots=\"false\" :current=\"0\" :circular=\"true\" :interval=\"3000\" @change='swiperChange'>\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in room.pics\" :key=\"index\"> \r\n\t\t\t\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t\t\t\t<view @click=\"viewPicture(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item\" class=\"dp-banner-swiper-img\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t\t\t<view class=\"popup-numstatistics flex flex-xy-center\" v-if='bannerList.length'>\r\n\t\t\t\t\t\t\t\t\t{{bannerindex}} / {{bannerList.length}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"hotel-details-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-title\">{{room.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"introduce-view flex \">\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.bedxing!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedxing}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/pingfang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.square}}m²</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedwidth}}米</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.ischuanghu!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/youchuang.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.ischuanghu}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.breakfast!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/zaocan.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{room.breakfast}}早餐</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"other-view flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"other-title\">特色</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"other-text\" style=\"white-space: pre-line;\">{{room.tese}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"other-view flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"other-title\">房型详情</view>\r\n\t\t\t\t\t\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 酒店权益 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\" v-if=\"qystatus == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">{{ qyname }}</view>\r\n\t\t\t\t\t\t\t\t\t<!--<view class=\"equity-title-tisp\">填写订单时兑换</view>-->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<parse :content=\"hotel.hotelquanyi\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 政策服务 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\"  v-if=\"fwstatus == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">{{ fwname }}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<parse :content=\"hotel.hotelfuwu\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 费用明细 -->\r\n\t\t\t\t\t\t\t<view class=\"hotel-equity-view flex flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"equity-title\">费用明细</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cost-details flex flex-col\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">押金（可退）</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{yajin}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">{{text['服务费']}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{service_money}}/天</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"room.isdaymoney==1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">房费</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">{{room.daymoney}}{{moneyunit}}/晚</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\">房费</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num\">￥{{room.price}}/晚</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"price-view flex flex-y-center\" style=\"justify-content: flex-end;margin-top: 30rpx;margin-bottom: 0rpx;align-items: center;padding-bottom: 0rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-text\" style=\"font-size: 28rpx;margin-right: 15rpx;\">每日金额</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num flex flex-y-center\"  v-if=\"room.isdaymoney==1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view style=\"font-size: 24rpx;font-weight: none;margin-top: 5rpx;\">￥</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view style=\"font-size: 44rpx;\">{{totalprice}}+{{room.daymoney}}{{moneyunit}}</view>\t\t\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-num flex flex-y-center\"  v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t<view style=\"font-size: 24rpx;font-weight: none;margin-top: 5rpx;\">￥</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view style=\"font-size: 44rpx;\">{{totalprice}}</view>\t\t\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"room.isdaymoney==1\" class=\"tips\">未有旅居{{t('余额')}},支付{{room.price}}/晚或去获取旅居{{t('余额')}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view style=\"height:260rpx\"></view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t<!-- 预定 -->\r\n\t\t\t\t\t\t<view class=\"popup-but-view flex flex-col\" style=\"bottom: 0;\">\r\n\t\t\t\t\t\t\t<view class=\"price-statistics flex flex-y-center\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<view class=\"title-text\">每日金额：</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price-text flex\" v-if=\"room.isdaymoney==1\">\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 22rpx;margin-top: 8rpx;\">￥</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-weight: bold;font-size: 36rpx;\">{{totalprice}}+{{room.daymoney}}{{moneyunit}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price-text flex\" v-else>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 22rpx;margin-top: 8rpx;\">￥</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-weight: bold;font-size: 36rpx;\">{{totalprice}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!--<view class=\"title-text\">共减：</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price-text flex\">\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 22rpx;margin-top: 8rpx;\">￥</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-weight: bold;font-size: 36rpx;\">123.00</view>\r\n\t\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail_but-class\" v-if=\"minstock>0 && !room.isbooking\" @tap=\"tobuy\" :style=\"'background: linear-gradient(90deg,rgba('+tColor('color1rgb')+',1) 0%,rgba('+tColor('color1rgb')+',1) 100%)'\">预定</view>\r\n\t\t\t\t\t\t\t<view class=\"but-class\" v-else-if=\"room.isbooking && minstock>0\" :style=\"'background: #999999;color:#fff'\">不可订</view>\r\n\t\t\t\t\t\t\t<view class=\"but-class\" v-else :style=\"'background: #999999;color:#fff'\">已订满</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 选择日期弹窗 -->\r\n\t\t\t\t\r\n\t\t\t\t<view v-if=\"calendarvisible\" class=\"popup__container\">\r\n\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">选择日期</text>\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose2.png`\" class=\"popup__close\" style=\"width:56rpx;height:56rpx;top:20rpx;right:20rpx\" @tap.stop=\"handleClickMask\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t<view class=\"reserve-time-view\" >\r\n\t\t\t\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t\t\t\t<view class='time-title'>入住</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"date-time\">{{startDate}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='time-title'>{{startWeek}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='statistics-view'>\r\n\t\t\t\t\t\t\t\t\t<view class=\"statistics-date\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t\t\t\t\t\t\t共{{dayCount}}晚\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"color-line\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t\t\t\t<view class='time-title'>离店</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"date-time\">{{endDate}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='time-title'>{{endWeek}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"calendar-view\">\r\n\t\t\t\t\t\t\t\t<calendar :is-show=\"true\" :isFixed='false' showstock='1':text=\"text\"  :dayroomprice=\"dayroomprice\" :start-date=\"starttime\" :end-date=\"endtime\" mode=\"2\"  @callback=\"getDate\"  :maxdays='maxdays'  :themeColor=\"t('color1')\" :between-end=\"maxenddate\">\r\n\r\n\t\t\t\t\t\t\t\t</calendar>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"choose-but-class\" :style=\"'background: linear-gradient(90deg,rgba('+tColor('color1rgb')+',1) 0%,rgba('+tColor('color1rgb')+',1) 100%)'\" @tap=\"popupClose\">\r\n\t\t\t\t\t\t\t\t确认{{dayCount}}晚 可订{{minday}}{{text['间']}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport calendar from '../mobile-calendar-simple/Calendar.vue'\r\n\tvar app = getApp();\r\n\texport default{\r\n\t\tcomponents:{\r\n\t\t    calendar\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tisload: false,\r\n\t\t\t\tstartDate:'',\r\n\t\t\t\tendDate:'',\r\n\t\t\t\thotelid:0,\r\n\t\t\t\thotel:[],\r\n\t\t\t\tnavigationMenu:{},\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tplatform: app.globalData.platform,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\ttext:[],\r\n\t\t\t\troomlist:[],\r\n\t\t\t\tbannerindex:1,\r\n\t\t\t\tbannerList:[],\r\n\t\t\t\tdayCount:1,\r\n\t\t\t\tstartWeek:'',\r\n\t\t\t\tendWeek:'',\r\n\t\t\t\tstarttime:'',\r\n\t\t\t\tpagecontent: \"\",\r\n\t\t\t\tendtime:'',\r\n\t\t\t\troomList:[],\r\n\t\t\t\troom:[],\r\n\t\t\t\tyajin:0,\r\n\t\t\t\tservice_money:0,\r\n\t\t\t\ttotalprice:0,\r\n\t\t\t\tphotos:[],\r\n\t\t\t\tpindex:0,\r\n\t\t\t\tphotolist:[],\r\n\t\t\t\tgrouplist:[],\r\n\t\t\t\tgindex:-1,\r\n\t\t\t\tgids:[],\r\n\t\t\t\tnomore:false,\t\r\n\t\t\t\tnodata: false,\r\n\t\t\t\ttotalpagenum:0,\r\n\t\t\t\tcommentlist:[],\r\n\t\t\t\thaoping:'',\r\n\t\t\t\tmaxenddate:'',\r\n\t\t\t\tdayroomprice:[],\r\n\t\t\t\tminday:0,\r\n\t\t\t\tminstock:0,\r\n\t\t\t\tcalendarvisible:false,\r\n\t\t\t\tmaxdays:0,\r\n\t\t\t\tpagenum:0,\r\n\t\t\t\tqystatus:0,\r\n\t\t\t\tfwstatus:0,\r\n\t\t\t\tqyname:'',\r\n\t\t\t\tfwname:'',\r\n\t\t\t\troomstyle:1,\r\n\t\t\t\tbtnstyle:1,\r\n\t\t\t\tnature:'',\r\n\t\t\t\tmoneyunit:'元'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.hotelid = this.opt.id?this.opt.id:0\r\n\t\t\tvar sysinfo = uni.getSystemInfoSync();\t\r\n\t\t\tthis.statusBarHeight = sysinfo.statusBarHeight;\r\n\t\t\tthis.wxNavigationBarMenu();\r\n\t\t\t\r\n\t\t\tvar cachelongitude = app.getCache('user_current_longitude');\r\n\t\t\tvar cachelatitude = app.getCache('user_current_latitude');\r\n\t\t\tif(cachelongitude && cachelatitude){\r\n\t\t\t\tthis.latitude = cachelatitude\r\n\t\t\t\tthis.longitude = cachelongitude\r\n\t\t\t}else{\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\tapp.setCache('user_current_latitude',res.latitude)\r\n\t\t\t\t\tapp.setCache('user_current_longitude',res.longitude)\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.getdata();\r\n\t\t\t// 酒店详情弹窗！！！！！！！！！！！-----------------------------------------------------------------------------------------------\r\n\t\t\t// this.$refs.popup.open();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tapp.post('ApiHotel/getsysset', {}, function (res) { \r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\tthat.set=res.set\r\n\t\t\t\t\t\t\tthat.roomstyle = res.set.roomstyle;\r\n\t\t\t\t\t\t\tthat.btnstyle = res.set.btnstyle;\r\n\t\t\t\t\t\t\tthat.catelist = res.catelist\r\n\t\t\t\t\t\t\tvar starttime = app.getCache('startTime');\r\n\t\t\t\t\t\t\tvar endtime = app.getCache('endTime');\r\n\t\t\t\t\t\t\tvar daycount = app.getCache('dayCount');\r\n\t\t\t\t\t\t\tif(!starttime){\r\n\t\t\t\t\t\t\t\t\tapp.setCache('startTime',res.startday);\r\n\t\t\t\t\t\t\t\t\tstarttime = app.getCache('startTime');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif(!endtime){\r\n\t\t\t\t\t\t\t\t\tapp.setCache('endTime',res.endday);\r\n\t\t\t\t\t\t\t\t\tendtime = app.getCache('endTime');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif(!daycount){\r\n\t\t\t\t\t\t\t\t app.setCache('dayCount',1);\r\n\t\t\t\t\t\t\t\t daycount = that.dayCount;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\t\t\t\t\tvar day = new Date(starttime).getDay();\r\n\t\t\t\t\t\t\tvar day2 = new Date(endtime).getDay();\r\n\t\t\t\t\t\t\tvar startWeek = weekdays[day];\r\n\t\t\t\t\t\t\tvar endWeek = weekdays[day2];\r\n\t\t\t\t\t\t\tvar startDate = starttime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\t\tvar endDate = endtime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthat.starttime = starttime;\r\n\t\t\t\t\t\t\tthat.endtime = endtime;\r\n\t\t\t\t\t\t\tthat.startDate = startDate\r\n\t\t\t\t\t\t\tthat.endDate = endDate\r\n\t\t\t\t\t\t\tthat.startWeek = startWeek\r\n\t\t\t\t\t\t\tthat.endWeek = endWeek\r\n\t\t\t\t\t\t\tthat.dayCount = daycount\t\r\n\t\t\t\t\t\t\tthat.text = res.text\r\n\t\t\t\t\t\t\tthat.moneyunit = res.moneyunit\r\n\t\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\t\ttitle:res.text['酒店']+'详情'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t\t\tthat.getdetail()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t })\r\n\t\t\t},\r\n\t\t\tgetdetail:function(loadmore){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tif(!loadmore){\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t\tvar pagenum = that.pagenum;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tvar latitude = that.latitude;\r\n\t\t\t\tvar longitude = that.longitude;\r\n\t\t\t\tapp.post('ApiHotel/gethotelDetail', {hotelid:this.hotelid,pagenum: pagenum,gids:that.gids,starttime:that.starttime,endtime:that.endtime,daycount:that.dayCount,longitude: longitude,latitude: latitude,}, function (res) {\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\tvar data = res.datalist;\r\n\t\t\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\t\t\tthat.qystatus = res.detail.qystatus\r\n\t\t\t\t\t\t\t\tthat.fwstatus = res.detail.fwstatus\r\n\t\t\t\t\t\t\t\tthat.qyname = res.detail.qyname\r\n\t\t\t\t\t\t\t\tthat.fwname = res.detail.fwname\r\n\t\t\t\t\t\t\t\tthat.nature = res.detail.nature\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tthat.roomlist = res.datalist;\r\n\t\t\t\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t\t\t\t    that.nodata = true;\r\n\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\tthat.totalpagenum = res.totalpagenum\r\n\t\t\t\t\t\t\t\tthat.hotel=res.detail\r\n\t\t\t\t\t\t\t\tconsole.log(that.hotel)\r\n\t\t\t\t\t\t\t\tthat.photos = res.photos\r\n\t\t\t\t\t\t\t\tif(res.photos.length>0){\r\n\t\t\t\t\t\t\t\t\tthat.photolist = res.photos[0].pics\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.grouplist = res.grouplist\t\r\n\t\t\t\t\t\t\t\tthat.commentlist = res.commentlist\r\n\t\t\t\t\t\t\t\tthat.haoping = res.haoping\r\n\t\t\t\t\t\t\t\tthat.dayroomprice = res.roomdayprice\r\n\t\t\t\t\t\t\t\tthat.maxenddate = res.maxenddate\r\n\t\t\t\t\t\t\t\tthat.maxdays = res.maxdays\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t\t\t\t    that.nomore = true;\r\n\t\t\t\t\t\t\t  } else {\r\n\t\t\t\t\t\t\t    var roomlist = that.roomlist;\r\n\t\t\t\t\t\t\t    var newdata = roomlist.concat(data);\r\n\t\t\t\t\t\t\t    that.roomlist = newdata;\r\n\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandleClickMask:function(){\r\n\t\t\t\tthis.calendarvisible = false;\r\n\t\t\t},\r\n\t\t\tshowmore:function(e){\r\n\t\t\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\t\tthis.getdetail(true);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tselectDate:function(){\r\n\t\t\t\t// 选择日期弹窗-------------------------------------------------------------------------------------------\r\n\t\t\t\t//this.$refs.popupTime.open();\r\n\t\t\t\tthis.calendarvisible = true;\r\n\t\t\t},\r\n\t\t\tgetDate(date){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tif(date.dayCount){\r\n\t\t\t\t\t\tapp.setCache('dayCount',date.dayCount)\r\n\t\t\t\t\t\tthat.dayCount = date.dayCount;\r\n\t\t\t\t}\r\n\t\t\t\tif(date.startStr){\r\n\t\t\t\t\t\tvar starttime =  date.startStr.dateStr\r\n\t\t\t\t\t\tapp.setCache('startTime',starttime);\r\n\t\t\t\t\t\tvar startDate = starttime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\tapp.setCache('startDate',startDate);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.starttime = starttime\r\n\t\t\t\t\t\tthat.startDate = startDate\r\n\t\t\t\t\t\tthat.startWeek = date.startStr.week\r\n\t\t\t\t}\r\n\t\t\t\tif(date.endStr){\r\n\t\t\t\t\tvar endtime =  date.endStr.dateStr\r\n\t\t\t\t\tapp.setCache('endTime',endtime);\r\n\t\t\t\t\tvar endDate = endtime.substr(5).replace('-', '月');\r\n\t\t\t\t\tapp.setCache('endDate',endDate);\r\n\t\t\t\t\tthat.endtime = endtime\r\n\t\t\t\t\tthat.endDate = endDate\r\n\t\t\t\t\tthat.endWeek = date.endStr.week\r\n\t\t\t\t}\t\t\r\n\t\t\t\tvar minday = this.dayroomprice[starttime]['stock'];\r\n\t\t\t\tvar timestamp = new Date(starttime).getTime();\r\n\t\t\t\tfor(var i=0;i<date.dayCount;i++){\r\n\t\t\t\t\tvar daystr = this.dateFormat((timestamp/1000)+86400*i,'Y-m-d')\r\n\t\t\t\t\tif(minday>this.dayroomprice[daystr]['stock']){\r\n\t\t\t\t\t\tminday = this.dayroomprice[daystr]['stock'];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.minday = minday;\r\n\t\t\t\tthat.getdata();\r\n\t\t\t },\r\n\t\t\tpopupClose(){\r\n\t\t\t\t//this.$refs.popupTime.close();\r\n\t\t\t\tthis.calendarvisible = false;\r\n\t\t\t},\r\n\t\t\thotelDetail:function(e){\r\n\t\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\t\tvar that = this\r\n\t\t\t\tapp.post('ApiHotel/getroomDetail', {id:id,startDate:that.starttime,endDate:that.endtime,dayCount:that.dayCount}, function (res) {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\tthat.room=res.room\r\n\t\t\t\t\t\t \r\n\t\t\t\t\t\t\tvar pagecontent = JSON.parse(res.room.detail);\r\n\t\t\t\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\tthat.minstock = res.minstock\r\n\t\t\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// 房型详情-------------------------------------------------------------------------------------------\r\n\t\t\t\tthis.$refs.popup.open();\r\n\t\t\t},\r\n\t\t\tgroupChange:function(e){\r\n\t\t\t\tvar grouplist = this.grouplist;\r\n\t\t\t\tvar gid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar gids = this.gids;\r\n\t\t\t\tvar newgids = [];\r\n\t\t\t\tvar ischecked = false;\r\n\t\t\t\tfor(var i in gids){\r\n\t\t\t\t\tif(gids[i] != gid){\r\n\t\t\t\t\t\tnewgids.push(gids[i]);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tischecked = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(ischecked==false){\r\n\t\t\t\t\tnewgids.push(gid);\r\n\t\t\t\t}\r\n\t\t\t\tthis.gids = newgids;\r\n\t\t\t\tthis.getdetail()\r\n\t\t\t},\r\n\t\t\topenLocation:function(e){\r\n\t\t\t\t//console.log(e)\r\n\t\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\r\n\t\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\r\n\t\t\t\tvar address = e.currentTarget.dataset.address\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t latitude:latitude,\r\n\t\t\t\t longitude:longitude,\r\n\t\t\t\t name:address,\r\n\t\t\t\t scale: 13\r\n\t\t\t })\t\t\r\n\t\t\t},\r\n\t\t\tchanagePhoto:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar pindex = e.currentTarget.dataset.index\r\n\r\n\t\t\t\tthat.pindex = pindex;\r\n\t\t\t\tthat.photolist = that.photos[pindex].pics\r\n\t\t\t},\r\n\t\t\t//计算价格\r\n\t\t\tcalculatePrice: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar dayCount = that.dayCount;\r\n\t\t\t\tvar room = that.room\r\n\t\t\t\tconsole.log(room);\r\n\t\t\t\tvar totalprice = 0;\r\n\t\t\t\tvar service_money = 0;\r\n\t\t\t\tif(room.isservice_money==1){\r\n\t\t\t\t\tservice_money = room.service_money;\r\n\t\t\t\t}\r\n\t\t\t\tthat.service_money = service_money;\t\r\n\t\t\t\tvar yajin=0;\r\n\t\t\t\tif(room.isyajin==1 || room.isyajin==2){\r\n\t\t\t\t\t\tyajin = room.yajin_money\r\n\t\t\t\t}else if(room.isyajin==-1){\r\n\t\t\t\t\t\tyajin=0;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(that.hotel.isyajin==1 || that.hotel.isyajin==2){\r\n\t\t\t\t\t\t\tyajin = that.hotel.yajin_money\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tyajin = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t}\t\t\t\t\r\n\t\t\t\tthat.yajin =parseFloat(yajin) ;\r\n\t\t\t\t//是否使用余额定价\r\n\t\t\t\tif(room.isdaymoney==1){\r\n\t\t\t\t\t\ttotalprice = parseFloat(service_money);\r\n\t\t\t\t}else{\r\n\t\t\t\t\t\ttotalprice = parseFloat(service_money) + parseFloat(room.price);\r\n\t\t\t\t}\r\n\t\t\t\tthat.totalprice =parseFloat(totalprice).toFixed(2);;\r\n\t\t\t},\r\n\t\t\ttobuy: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar roomid = that.room.id;\r\n\t\t\t\tvar daycount = that.dayCount;\r\n\t\t\t\tvar starttime = app.getCache('startTime');\r\n\t\t\t\tvar endtime = app.getCache('endTime');\r\n\t\t\t\tif(!starttime || !endtime){\r\n\t\t\t\t\t\treturn app.error(\"请选择入离时间\");\r\n\t\t\t\t}\r\n\t\t\t\t//var timestamp = Date.parse(str2);\r\n\t\t\t\tapp.goto('buy?roomid=' + roomid+'&daycount='+daycount);\r\n\t\t\t},\r\n\t\t\tpopupdetailClose(){\r\n\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t},\r\n\t\t\tswiperChange(event){\r\n\t\t\t\tthis.bannerindex = event.detail.current;\r\n\t\t\t},\r\n\t\t\ttColor(text){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(text=='color1'){\r\n\t\t\t\t\tif(app.globalData.initdata.color1 == undefined){\r\n\t\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\t\tthat.tColor('color1')\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\treturn app.globalData.initdata.color1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(text=='color2'){\r\n\t\t\t\t\treturn app.globalData.initdata.color2;\r\n\t\t\t\t}else if(text=='color1rgb'){\r\n\t\t\t\t\tif(app.globalData.initdata.color1rgb == undefined){\r\n\t\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\t\tthat.tColor('color1rgb')\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tvar color1rgb = app.globalData.initdata.color1rgb;\r\n\t\t\t\t\t\treturn color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(text=='color2rgb'){\r\n\t\t\t\t\tvar color2rgb = app.globalData.initdata.color2rgb;\r\n\t\t\t\t\treturn color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn app.globalData.initdata.textset[text] || text;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\twxNavigationBarMenu:function(){\r\n\t\t\t\tif(this.platform=='wx'){\r\n\t\t\t\t\t//胶囊菜单信息\r\n\t\t\t\t\tthis.navigationMenu = wx.getMenuButtonBoundingClientRect()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tviewPicture(url){\r\n\t\t\t\tlet arr = [url]\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\turls: arr\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoback: function() {\r\n\t\t\t  var that = this;\r\n\t\t\t\tgetApp().goback();\r\n\t\t\t},\r\n\t\t},\r\n\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*  */\r\n\t.choose-but-class{width: 94%;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);color: #FFFFFF;font-size: 32rpx;font-weight: bold;padding: 24rpx;\r\n\tborder-radius: 60rpx;position: fixed;bottom:10rpx;left: 50%;transform: translateX(-50%);margin-bottom: env(safe-area-inset-bottom);text-align: center;}\r\n\t.calendar-view{width: 100%;position: relative;max-height: 60vh;padding-top: 30rpx;height: auto;overflow: hidden;padding-bottom: env(safe-area-inset-bottom);}\r\n\t/*  */\r\n\t.popup__content{ background: #fff;overflow:hidden; height: auto; }\r\n\t.popup__content .reserve-time-view{width: 88%;height:130rpx;margin:30rpx auto 0;border-bottom: 1px  #f0f0f0 solid;display: flex;align-items: center;\r\n\tjustify-content: space-between;}\r\n\t.popup__content .reserve-time-view .time-view{display: flex;flex-direction: column;align-items: flex-start;}\r\n\t.popup__content .reserve-time-view .time-view .time-title{color: #7B8085;line-height: 24rpx;}\r\n\t.popup__content .reserve-time-view .time-view .date-time{color: #111111;font-size: 32rpx;font-weight: bold;padding-right: 20rpx;}\r\n\t.popup__content .reserve-time-view .statistics-view{display: flex;flex-direction: column;align-items: center;justify-content: center;}\r\n\t.popup__content .reserve-time-view .statistics-view .statistics-date{width: 88rpx;height: 32rpx;border-radius: 20px;font-size: 20rpx;\r\n\tcolor: #000;border: 1rpx solid #000;box-sizing: border-box;display: flex;align-items: center;justify-content: center;position: relative;}\r\n\t.statistics-view .statistics-date .content-decorate{width: 13rpx;height: 2rpx;background: red;position: absolute;top: 50%;background: #000;}\r\n\t.statistics-view .statistics-date .left-c-d{left: -13rpx;}\r\n\t.statistics-view .statistics-date .right-c-d{right: -13rpx;}\r\n\t.popup__content .reserve-time-view .statistics-view .color-line{border-top: 1rpx solid #f0f0f0;width: 130rpx;margin-top: 25rpx;}\r\n\t.uni-popup__wrapper-box{background: #f7f8fa;border-radius: 40rpx 40rpx 0rpx 0rpx;overflow: hidden;}\r\n\t/*  */\r\n\t.popup__content .popup-but-view{width: 100%;position: sticky;bottom: 0rpx;padding: 20rpx 40rpx;background: #fff;box-shadow: 0rpx 0rpx 10rpx 5rpx #ebebeb;}\r\n\t.popup__content .popup-but-view .but-class{width: 100%;padding: 22rpx;text-align: center;color: #FFFFFF;font-size: 32rpx;font-weight: bold;border-radius: 60rpx;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%); }\r\n\t.popup__content .popup-but-view .price-statistics{padding-bottom: 15rpx;}\r\n\t.popup__content .popup-but-view .price-statistics .title-text{font-size: 24rpx;}\r\n\t.popup__content .popup-but-view .price-statistics .price-text{padding: 0rpx 10rpx;align-items: center;}\r\n\t/*  */\r\n\t.popup__content{width: 100%;height:auto;position: relative;}\r\n\t/*  */\r\n\t\r\n\t\r\n\t.popup__content .popup-close{position: fixed;right: 20rpx;top: 20rpx;width: 56rpx;height: 56rpx;z-index: 11;}\r\n\t\r\n\t\r\n\t.popup__content .popup-close image{width: 100%;height: 100%;}\r\n\t/*  */\r\n\t.popup__content .popup-banner-view{width: 100%;height: 500rpx;position: relative;}\r\n\t.popup__content .popup-banner-view .popup-numstatistics{position: absolute;right: 20rpx;bottom: 20rpx;background: rgba(0, 0, 0, 0.3);\r\n\tborder-radius: 28px;width: 64px;height: 28px;text-align: center;line-height: 28px;color: #fff;font-size: 20rpx;}\r\n\t.popup__content .hotel-details-view{width: 100%;padding: 30rpx 40rpx;background: #fff;}\r\n\t.popup__content .hotel-details-view\t.hotel-title{color: #1E1A33;font-size: 35rpx;}\r\n\t.popup__content .hotel-details-view\t.introduce-view{width: 100%;align-items: center;flex-wrap: wrap;justify-content: flex-start;padding: 20rpx 10rpx;}\r\n\t.popup__content .hotel-details-view\t.introduce-view .options-intro{padding: 15rpx 0rpx;margin-right: 20rpx;width: auto;}\r\n\t.hotel-details-view\t.introduce-view .options-intro image{width: 32rpx;height: 32rpx;}\r\n\t.hotel-details-view\t.introduce-view .options-intro .options-title{color: #1E1A33;font-size: 24rpx;margin-left: 15rpx;}\r\n\t.hotel-details-view .other-view{width: 100%;justify-content: flex-start;padding: 12rpx 0rpx;}\r\n\t.hotel-details-view .other-view .other-title{color: #A5A3AD;font-size: 24rpx;margin-right: 40rpx;}\r\n\t.hotel-details-view .other-view .other-text{color: #1E1A33;font-size: 24rpx;}\r\n\t/*  */\r\n\t.popup__content .hotel-equity-view{width: 100%;padding:30rpx 40rpx 40rpx;background: #fff;margin-top: 20rpx;}\r\n\t.hotel-equity-view .equity-title-view{align-items: center;justify-content: flex-start;}\r\n\t.hotel-equity-view .equity-title-view .equity-title{color: #1E1A33;font-size: 32rpx;font-weight: bold;}\r\n\t.hotel-equity-view .equity-title-view .equity-title-tisp{color: #A5A3AD;font-size: 24rpx;margin-left: 28rpx;}\r\n\t.hotel-equity-view .equity-options{margin-top: 40rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view{align-items: center;justify-content: flex-start;}\r\n\t.hotel-equity-view .equity-options .options-title-view image{width: 28rpx;height: 28rpx;margin-right: 20rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view  .title-text{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t.hotel-equity-view .equity-options .options-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;padding: 15rpx 0rpx;line-height: 40rpx;margin-left: 50rpx;margin-right: 50rpx;}\r\n\t/*  */\r\n\t.hotel-equity-view .promotion-options{width: 100%;justify-content: space-between;padding: 12rpx 0rpx;}\r\n\t.hotel-equity-view .promotion-options image{width: 20rpx;height: 20rpx;}\r\n\t.hotel-equity-view .promotion-options .left-view{justify-content: flex-start;}\r\n\t.hotel-equity-view .promotion-options .left-view .logo-view{width: 80px;height: 20px;text-align: center;line-height: 18px;border-radius: 8rpx;border:1px solid;font-size: 20rpx;}\r\n\t.hotel-equity-view .promotion-options .left-view .logo-view-text{color: rgba(30, 26, 51, 0.8);font-size: 20rpx;padding-left: 30rpx;}\r\n\t/*  */\r\n\t.hotel-equity-view  .cost-details{background: #F4F5F9;width: 100%;border-radius:6px;padding: 40rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view{padding-bottom: 30rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-num{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t/*  */\r\n\t.position-view{width: 100%;height: auto;position: relative;top:-125rpx;}\r\n\t.position-view .banner-tab-view{height: 60rpx;border-radius: 20px;background: rgba(0, 0, 0, 0.3);padding: 0rpx 8rpx;max-width:96%;width:auto;margin-left:2%;display: inline-block;}\r\n\t.position-view .banner-tab-view .tab-options-banner{font-size: 20rpx;color: #FFFFFF;height: 46rpx;line-height: 46rpx;text-align: center;border-radius: 30rpx;padding: 0rpx 23rpx;margin-right: 2rpx;display: inline-block;}\r\n\t.position-view .banner-tab-view\t.tab-options-banner-active{background: linear-gradient(90deg, #FFFFFF 0%, rgba(255, 255, 255, 0.8) 100%);color: #000000;}\r\n\t.content-view{border-radius: 40rpx 40rpx 0rpx 0rpx;background: #fff;padding: 0rpx 40rpx;width: 100%;height: auto;margin-top: 30rpx;}\r\n\t.content-view .title-view{color: #1E1A33;font-size: 40rpx;padding: 40rpx 0 0rpx 0 ;font-weight: bold;}\r\n\t.content-view .hotspot-view{width: 100%;display: flex;align-items: center;justify-content: space-between;}\r\n\t.content-view .hotspot-view .hotspot-view-left{display: flex;align-items: center;justify-content: flex-start;width: 100%; flex-wrap: wrap;}\r\n\t.content-view .hotspot-view .hotspot-view-left .hotspot-options{display: inline-block;background: #F6F7F8;padding: 5px 10px;border-radius: 6px;color: #4A4950; margin-top: 20rpx;\r\n\tfont-size: 20rpx;margin-right: 13rpx;}\r\n\t.content-view .hotspot-view .hotspot-view-left .hotspot-options-active{background: #FFF4D5;color: #EF8E32;}\r\n\t.content-view .hotspot-view .hotspot-more{color: #4A4950;font-size: 18rpx;display: flex;align-items: center;}\r\n\t.content-view .hotspot-view .hotspot-more image{width: 7px;height: 7px;margin-left: 10rpx;}\r\n\t.content-view .address-view{display: flex;align-items: center;justify-content: space-between;margin: 30rpx 0rpx;}\r\n\t.content-view .address-view .address-text{color: #727177;font-size: 31rpx;width: 95%;font-weight: bold;}\r\n\t.content-view .address-traffic{color: rgba(30, 26, 51, 0.4);font-size: 26rpx;margin-top: 20rpx;display: flex;align-items: center;}\r\n\t.content-view .address-traffic image{width: 24rpx;height: 24rpx;margin-right: 10rpx;}\r\n\t.content-view .address-view .fangshi-view{display: flex;align-items: center;justify-content: space-between;}\r\n\t.content-view .address-view .fangshi-view .fagnshi-options{display: flex;flex-direction: column;align-items: center;justify-content: center;color: #06D470;font-size: 18rpx;}\r\n\t.content-view .address-view .fangshi-view .fagnshi-options image{width: 65rpx;height: 65rpx;margin-bottom: 10rpx;}\r\n\t.content-view .time-view{width: 100%;padding: 30rpx 0rpx;}\r\n\t.content-view .time-view .time-options{}\r\n\t.content-view .time-view .time-options .month-tetx{color: #1E1A33;font-size: 32rpx;font-weight: 500;}\r\n\t.content-view .time-view .time-options .day-tetx{color: rgba(30, 26, 51, 0.4);font-size: 26rpx;margin-left: 20rpx;}\r\n\t.content-view .time-view .content-text{box-sizing: border-box;border: 1px solid #000;text-align: center;border-radius: 20px; padding:0 10rpx;\r\n\tcolor: #000;font-size: 22rpx;position: relative}\r\n\t.content-view .time-view .content-text .content-decorate{width: 13rpx;height: 2rpx;background: red;position: absolute;top: 50%;background: #000;}\r\n\t.content-view .time-view .content-text .left-c-d{left: -13rpx;}\r\n\t.content-view .time-view .content-text .right-c-d{right: -13rpx;}\r\n\t/*  */\r\n\t.screen-view{width: 100%;display: flex;align-items: center;justify-content: space-between;padding:20rpx; overflow-x: auto;}\r\n\t.screen-view .screen-view-left{flex:1;display: flex;align-items: center;justify-content: flex-start;margin-right: 30rpx;}\r\n\t.screen-view .screen-view-left .screen-options{display: flex;align-items: center;justify-content: space-between;background: #F4F4F4;border-radius: 6px;color: #212121;  font-size: 24rpx;padding: 12rpx 18rpx;margin-right: 20rpx;background: #fff; white-space: nowrap;}\r\n\t.screen-view .screen-view-left .screen-options image{width: 16rpx;height: 16rpx;margin-left: 16rpx;}\r\n\t.screen-view .right-screen{display: flex;align-items: center;color: #212121;font-size: 24rpx;background: #fff;padding: 12rpx 18rpx;border-radius: 6px;}\r\n\t.screen-view .right-screen image{width: 24rpx;height: 24rpx;margin-left: 10rpx;}\r\n\t/*  */\r\n\t.hotels-list{width: 96%;margin: 0rpx auto 0rpx;display: flex;align-items: center;justify-content: space-between;flex-direction:column;}\r\n\t.hotels-list .hotels-options{width: 100%;padding: 20rpx;display: flex;align-items: center;justify-content: space-between;border-radius: 8px;background: #FFFFFF;\r\n\tmargin-bottom: 20rpx;position: relative;}\r\n\t.hotels-list .hotels-options .hotel-img{width: 98px;height: 130px;border-radius: 15rpx;overflow: hidden;\r\n\tdisplay: flex;align-items: center;justify-content: center;\r\n\t}\r\n\t.hotels-list .hotels-options .hotel-img .fangxing{width: 98px;height: 98px;border-radius: 15rpx;overflow: hidden; \r\n \r\n\tdisplay: flex;align-items: center;justify-content: center;\r\n    }\r\n\t\r\n\r\n\t.hotels-list .hotels-options .hotel-img image{width: 100%;height: 100%;}\r\n\t.hotels-list .hotels-options .hotel-info{flex: 1;padding-left: 20rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-title{width: 100%;color: #343536;font-size: 30rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-address{width: 100%;color: #7B8085;font-size: 24rpx;margin-top: 7rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-characteristic{width: 80%;display: flex;align-items: center;justify-content: flex-start;margin-top: 7rpx;flex-wrap: wrap; }\r\n\t.hotels-list .hotels-options .hotel-info .hotel-characteristic .characteristic-options{font-size: 20rpx;padding: 7rpx 13rpx;flex-wrap: wrap;margin-right: 20rpx; margin-top: 10rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-but-view{width: 100%;display: flex;align-items: center;justify-content: space-between;margin-top: 25rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-but-view .make-info{display: flex;flex-direction: column;justify-content: flex-start;}\r\n\t.hotels-options .hotel-info .hotel-but-view .make-info .hotel-price{display: flex;align-items: center;justify-content: flex-start;font-size: 24rpx;}\r\n\t.hotel-info .hotel-but-view .make-info .hotel-price .hotel-price-num{font-size: 40rpx;font-weight: bold;padding: 0rpx 3rpx;}\r\n\t.hotels-options .hotel-info .hotel-but-view .make-info .hotel-text{color: #7B8085;font-size: 24rpx;margin-top: 15rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-but-view .hotel-make{background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);width: 72px;height: 32px;line-height: 32px;\r\n\ttext-align: center;border-radius: 36px;color: #FFFFFF;font-size: 28rpx;font-weight: bold;}\r\n\t.hotel-make-new{width: 90rpx;height: 95rpx;display: flex;align-items: center;justify-content: space-between;flex-direction: column;position: absolute;\r\n\tright: 20rpx;top: 110rpx;border-radius: 10rpx;overflow: hidden;}\r\n\t.hotel-make-new .make-new-view{width: 100%;padding:7rpx 0rpx;text-align: center;background: red;color: #fff;font-size: 30rpx;font-weight: bold;}\r\n\t.hotel-make-new .make-new-view2{width: 100%;text-align: center;font-size: 20rpx;border-bottom: 1px solid;color: red;padding-bottom: 5rpx;}\r\n\t/*  */\r\n\t.hotel-more{width: 96%;margin: 0rpx auto;background: #FFFFFF;border-radius: 8px;color: #06D470;text-align: center;padding: 30rpx 0rpx;font-size: 24rpx;letter-spacing: 3rpx;\r\n\tdisplay: flex;align-items: center;justify-content: center;}\r\n\t.hotel-more image{width: 20rpx;height: 20rpx;margin-left: 10rpx;}\r\n\t/*  */\r\n\t.work-order-view{width: 96%;margin: 0rpx auto;background: #FFFFFF;border-radius: 8px;padding: 30rpx 20rpx;margin-top: 20rpx;}\r\n\t.work-order-view .work-order-title{display: flex;align-items: center;justify-content: space-between;}\r\n\t.work-order-view .work-order-title .work-order-title-left{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t.work-order-view .work-order-title .work-order-title-right{color: rgba(30, 26, 51, 0.4);font-size: 24rpx;display: flex;align-items: center;justify-content: flex-end;}\r\n\t.work-order-view .work-order-title .work-order-title-right image{width: 20rpx;height: 20rpx;margin-left: 10rpx;}\r\n\t.work-order-view .facilities-view{width: 100%;align-items: center;flex-wrap: wrap;margin-top: 20rpx;}\r\n\t.work-order-view .facilities-view .options-facilities{align-items: center;justify-content: center;width: 25%;padding: 20rpx;}\r\n\t.work-order-view .facilities-view .options-facilities image{width: 64rpx;height: 64rpx;}\r\n\t.work-order-view .facilities-view .options-facilities .facilities-text{color: #4A4950;font-size: 20rpx;margin-top: 15rpx;}\r\n\t/*  */\r\n\t.required-reading-view{width: 100%;display: flex;flex-direction: column;padding-top: 20rpx;}\r\n\t.required-reading-view .read-address-view{display: flex;align-items: center;justify-content: flex-start;padding: 8rpx 0rpx;}\r\n\t.required-reading-view .read-address-view\t.address-title-text{color: #888889;font-size: 28rpx;}\r\n\t.required-reading-view .read-address-view\t.address-text-view{color: #222229;font-size: 28rpx;}\r\n\t.required-reading-view .read-tisp-view{display: flex;flex-direction: column;padding: 20rpx 0rpx;}\r\n\t.required-reading-view .read-tisp-view .read-tisp-title{color: #1E1A33;font-size: 26rpx;font-weight: bold;margin-bottom:20rpx;}\r\n\t.required-reading-view .read-tisp-view .read-tisp-title image{width: 28rpx;height: 28rpx;margin-right: 15rpx;}\r\n\t.required-reading-view .read-tisp-view .read-tisp-options{color: #4A4950;font-size: 24rpx;margin-bottom: 10rpx;padding-left: 40rpx;}\r\n\t/*  */\r\n\t.score-view{width: 100%;display: flex;flex-direction: column;padding-top: 20rpx;}\r\n\t.score-view .score-content{background: #FAFAFC;padding: 15rpx;border-radius: 8px;margin-top: 20rpx;}\r\n\t.score-view .score-content .total-score-view{align-items: flex-start;}\r\n\t.score-view .score-content .total-score-view .total-score-num{align-items: flex-end;}\r\n\t.score-view .score-content .total-score-view .total-score-num .total-num{font-size: 36px;font-weight: bold;}\r\n\t.score-view .score-content .total-score-view .total-score-num  .tatal-describe{font-size: 28rpx;margin-left: 10rpx;margin-bottom: 10rpx;}\r\n\t.score-view .score-content .total-score-view  .score-evaluate{color: #4A4950;font-size: 24rpx;margin-top: 15rpx;}\r\n\t.score-view .score-content .score-statistics{}\r\n\t.score-view .score-content .score-statistics .statistics-options{padding: 0rpx 12rpx;}\r\n\t.score-view .score-content .score-statistics .statistics-options .icon-view{width: 60rpx;height: 60rpx;border-radius: 50%;box-sizing: border-box;border: 2px solid #06D470;\r\n\tcolor: #06D470;font-size: 24rpx;text-align: center;line-height: 55rpx;}\r\n\t.score-view .score-content .score-statistics .statistics-options .evaluate-icon{color: #4A4950;font-size: 24rpx;margin-top: 20rpx;}\r\n\t.score-view\t.evaluate-tab{margin-top: 20rpx;width: 100%;}\r\n\t.score-view\t.evaluate-tab .evaluate-options{display: inline-block;background: #F4F4F4;padding: 10px;text-align: center;color: #4A4950;font-size: 24rpx;margin-right: 15rpx;\r\n\tborder-radius: 18rpx;}\r\n\t.score-view\t.evaluate-list-view{margin-top: 30rpx;padding-bottom: 50rpx;}\r\n\t.score-view\t.evaluate-list-view\t.text-options{padding: 20rpx 0rpx 10rpx 20rpx;}\r\n\t.evaluate-list-view\t.text-options .user-info-view{width: 100%;}\r\n\t.text-options .user-info-view .info-view .avater-view{width: 80rpx;height: 80rpx;border-radius: 50%;}\r\n\t.user-info-view .info-view .name-view{margin-left: 15rpx;}\r\n\t.user-info-view .info-view .name-view .name-text{color: #000000;font-size: 30rpx;font-weight: bold;}\r\n\t.user-info-view .info-view .name-view .time-view{color: rgba(0, 0, 0, 0.4);font-size: 28rpx;margin-top: 10rpx;}\r\n\t.text-options .user-info-view .scoring-view{border-radius: 4px;width: 96rpx;height: 80rpx;border-radius: 8rpx;text-align: center;line-height: 80rpx;font-size: 32rpx;\r\n\tpadding: 0rpx 10rpx;}\r\n\t.text-options\t.evaluate-imgList{width: 100%;padding: 20rpx 0rpx 0rpx;flex-wrap: wrap;justify-content: flex-start;}\r\n\t.text-options\t.evaluate-imgList .img-options{width: 145rpx;height: 145rpx;border-radius: 20rpx;overflow: hidden;margin-right: 15rpx;margin-bottom: 15rpx;}\r\n\t.text-options\t.evaluate-imgList .img-options image{width: 100%;height: 100%;}\r\n\t.text-options\t .evaluate-value{color: #4A4950;font-size: 26rpx;line-height: 40rpx;padding: 20rpx 0rpx;}\r\n\t\r\n\t/*  */\r\n\t.dp-banner{width: 100%;height: 250px;}\r\n\t.dp-banner-swiper{width:100%;height:100%;}\r\n\t.dp-banner-swiper-img{width:100%;height:auto}\r\n\t.banner-poster{width: 82%;margin: 30rpx auto 0rpx;display: flex;flex-direction:column;align-items: flex-end;}\r\n\t.banner-poster .poster-title{color: #FFFFFF;font-size: 56rpx;font-weight: 900;padding: 30rpx 0rpx;}\r\n\t.banner-poster .poster-text{color: #FFFFFF;font-size: 26rpx;opacity: 0.6;padding: 10rpx 0rpx;}\r\n\t.banner-poster .poster-but{width: 108px;height: 36px;color: #FFFFFF;text-align: center;line-height: 36px;font-size: 28rpx;font-weight: bold;margin: 40rpx 0rpx;border-radius: 36px;}\r\n\t/*  */\r\n\t.navigation {width: 100%;padding-bottom:10px;overflow: hidden;position: fixed;top: 0;z-index: 2;}\r\n\t.navcontent {display: flex;align-items: center;padding-left: 10px;}\r\n\t.header-location-top{position: relative;display: flex;justify-content: center;align-items: center;flex:1;}\r\n\t.header-back-but{position: absolute;left:0;display: flex;align-items: center;width: 40rpx;height: 45rpx;overflow: hidden;}\r\n\t.header-back-but image{width: 40rpx;height: 45rpx;} \r\n\t.header-page-title{font-size: 36rpx;}\r\n\t\r\n\t/*查看详情里的按钮*/\r\n\t.detail_but-class{width: 100%;padding: 22rpx;text-align: center;color: #FFFFFF;font-size: 32rpx;font-weight: bold;border-radius: 60rpx;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);   }\r\n\t\r\n\t.tips{ margin-top: 20rpx; color: #999;}\r\n\t.hotel-nature{display: flex;align-items: center;justify-content: flex-start;margin-top: 10rpx;}\r\n\t.hotel-nature .star-view{display: flex;align-items: center;justify-content: flex-start;}\r\n\t.hotel-nature .star-view image{width: 13px; height: 13px;margin-right: 10rpx;}\r\n\t.hotel-nature .hotspot-nature-text{font-size: 24rpx;color: #4A4950;margin-left: 10rpx;}\r\n    .hotel-characteristic .under_title{color: #7B8085;font-size: 24rpx;margin-top: 7rpx; margin-left: 7rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hoteldetails.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hoteldetails.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839390291\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}