{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/index.vue?5594", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/index.vue?6f17", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/index.vue?3df8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/index.vue?ffa1", "uni-app:///hotel/index/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/index.vue?7d3a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/index/index.vue?9104"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "calendar", "data", "opt", "isload", "bannerindex", "set", "cateList", "tabindex", "pre_url", "datalist", "startDate", "endDate", "startWeek", "endWeek", "dayCount", "starttime", "endtime", "keyword", "cat<PERSON>d", "text", "calendarvisible", "catelist", "loading", "maxdays", "moneyunit", "onLoad", "console", "methods", "getdata", "app", "that", "starttimestamp", "endtimestamp", "daycount", "uni", "title", "getdatalist", "pagenum", "field", "order", "cid", "handleClickMask", "tabChange", "selectDate", "getDate", "popupClose", "search", "searchChange", "tColor", "clearInterval"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACiMv1B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;IACAA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACA;UACAC;UACAA;UACA;YACAA;UACA;UACA;UACA;UACA;UAEA;UACA;UAEA;YACAD;YACAd;UACA;UACA;YACAc;YACAb;UACA;UACA;UACAe;UACAC;UACAC;UACAP;UACAG;UAGA;UACA;UACA;UACA;UACA;UACA;UACA;UACAA;UACAA;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UAEAI;YACAC;UACA;UACAL;UACAA;QACA;MACA;IACA;IACAM;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAN;MACAA;MACAA;MACAD;QAAAQ;QAAAC;QAAAC;QAAAC;MAAA;QACAV;QACAI;QACA;QACA;UACA;YACAJ;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;MACA;MACAZ;IAEA;IACAa;MACA;MACA;MACA;IACA;IACAC;MACAlB;MACA;MACA;QACAG;QACAC;MACA;MACA;QACA;QACAD;QACA;QACAA;QAEAC;QACAA;QACAA;MACA;MACA;QACA;QACAD;QACA;QACAA;QAEAC;QACAA;QACAA;MACA;IACA;IACAe;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAjB;IACA;IACAkB;MAEA;IACA;IACAC;MACA;MACA;QACA;UACA;YACAlB;UACA;UACAmB;QACA;UACA;QACA;MACA;QACA;MACA;QACA;UACA;YACAnB;UACA;UACAmB;QACA;UACA;UACA;QACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1ZA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "hotel/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './hotel/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=577ff0dc&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"hotel/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=577ff0dc&\"", "var components\ntry {\n  components = {\n    calendar: function () {\n      return import(\n        /* webpackChunkName: \"components/calendar/calendar\" */ \"@/components/calendar/calendar.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.catelist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = _vm.catelist.length\n        var m0 =\n          _vm.tabindex == index && _vm.tabindex == 0 ? _vm.t(\"color1\") : null\n        var m1 =\n          _vm.tabindex == index && _vm.tabindex == 0\n            ? _vm.tColor(\"color1\")\n            : null\n        var g1 =\n          _vm.tabindex == _vm.catelist.length - 1 && _vm.tabindex == index\n        var m2 = g1 ? _vm.t(\"color1\") : null\n        var m3 = g1 ? _vm.tColor(\"color1\") : null\n        var g2 =\n          _vm.tabindex != _vm.catelist.length - 1 &&\n          _vm.tabindex != 0 &&\n          _vm.tabindex == index\n        var m4 = g2 ? _vm.t(\"color1\") : null\n        var m5 = g2 ? _vm.tColor(\"color1\") : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          m0: m0,\n          m1: m1,\n          g1: g1,\n          m2: m2,\n          m3: m3,\n          g2: g2,\n          m4: m4,\n          m5: m5,\n        }\n      })\n    : null\n  var m6 = _vm.isload ? _vm.tColor(\"color1\") : null\n  var g3 = _vm.isload ? _vm.catelist.length : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var l1 = _vm.__map(item.tag, function (items, indexs) {\n          var $orig = _vm.__get_orig(items)\n          var m7 = indexs < 6 ? _vm.t(\"color1rgb\") : null\n          var m8 = indexs < 6 ? _vm.tColor(\"color1\") : null\n          return {\n            $orig: $orig,\n            m7: m7,\n            m8: m8,\n          }\n        })\n        var m9 = item.min_daymoney ? _vm.t(\"color1\") : null\n        var m10 = !item.min_daymoney ? _vm.t(\"color1\") : null\n        var m11 = _vm.t(\"color1rgb\")\n        return {\n          $orig: $orig,\n          l1: l1,\n          m9: m9,\n          m10: m10,\n          m11: m11,\n        }\n      })\n    : null\n  var m12 = _vm.isload && _vm.calendarvisible ? _vm.t(\"color1\") : null\n  var m13 = _vm.isload && _vm.calendarvisible ? _vm.tColor(\"color1rgb\") : null\n  var m14 = _vm.isload && _vm.calendarvisible ? _vm.tColor(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m6: m6,\n        g3: g3,\n        l2: l2,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class='dp-banner'>\r\n\t\t\t\t<swiper class=\"dp-banner-swiper\" :autoplay=\"true\" :indicator-dots=\"false\" :current=\"0\" :circular=\"true\" :interval=\"3000\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in set.pics\"> \r\n\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t<view @click=\"goto\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" class=\"dp-banner-swiper-img\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</swiper> \r\n\t\t\t</view>\r\n\t\t\t<!--  -->\r\n\t\t\t<view class=\"position-view\">\r\n\t\t\t\t<view class=\"banner-poster\">\r\n\t\t\t\t\t<!--<view class=\"poster-title\">林地探索 心灵营地</view>\r\n\t\t\t\t\t<view class=\"poster-text\">呼吸清新空气，品味天然甘露，享受烟火人间</view>\r\n\t\t\t\t\t<view class=\"poster-text\">去有风的地方，在山林里自由奔跑</view>\r\n\t\t\t\t\t<view class=\"poster-but\" :style=\"{backgroundColor:tColor('color1')}\">立即预定</view>-->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"reserve-view\">\r\n\t\t\t\t\t<view class=\"tab-view\">\r\n\t\t\t\t\t\t<scroll-view scroll-x class=\"scroll-class\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in catelist\">\r\n\t\t\t\t\t\t\t\t<view :class=\"[index == 0 ? 'options-tab-active':'' , index == catelist.length-1 ? 'options-tab-active2':'', 'options-tab']\" @click=\"tabChange(index)\">\r\n\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t\t<view class=\"active-class-show\" v-if=\"tabindex == index && tabindex == 0\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-view\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"color-view\" :style=\"{backgroundColor:tColor('color1')}\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"active-class-show-last\" v-if=\"tabindex == catelist.length-1 && tabindex == index \" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-view\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"color-view\" :style=\"{backgroundColor:tColor('color1')}\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"active-class-show-s\" v-if=\"(tabindex != catelist.length-1) && (tabindex != 0) && tabindex == index \" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-view\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"color-view\" :style=\"{backgroundColor:tColor('color1')}\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"height: 120rpx;background: #ebeef5;border-radius: 20rpx 20rpx 0rpx 0rpx;overflow: hidden;\"></view>\r\n\t\t\t\t\t<view class=\"reserve-time-view\" @tap=\"selectDate\">\r\n\t\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t\t<view class='time-title'>入住时间</view>\r\n\t\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t\t<view class=\"date-time\">{{startDate}}</view>\r\n\t\t\t\t\t\t\t\t<view class='time-title'>{{startWeek}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='statistics-view'>\r\n\t\t\t\t\t\t\t<view class=\"statistics-date\">\r\n\t\t\t\t\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t\t\t\t\t{{dayCount}}晚\r\n\t\t\t\t\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"color-line\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t\t<view class='time-title'>离店时间</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"date-time\">{{endDate}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class='time-title'>{{endWeek}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--  -->\r\n\t\t\t\t\t<view class=\"search-view\">\r\n\t\t\t\t\t\t<input placeholder=\"输入位置/关键字\" placeholder-style=\"color: rgba(123, 128, 133, 0.6);font-size: 28rpx;\"  class=\"input-class\" :value=\"keyword\" @input=\"searchChange\"/>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"search-but\"  @tap=\"search\" :style=\"{backgroundColor:tColor('color1')}\">\r\n\t\t\t\t\t\t{{text['酒店']}}查询\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- menu -->\r\n\t\t\t\t<view class=\"menu-view\">\r\n\t\t\t\t\t<scroll-view scroll-x class=\"scroll-view-class\" v-if=\"catelist.length > 3\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in catelist\">\r\n\t\t\t\t\t\t\t<view style=\"display: inline-block;width: 21.5%;margin-right: 30rpx;\">\r\n\t\t\t\t\t\t\t\t<view class=\"menu-options-scroll\" @tap=\"goto\" :data-url=\"'hotellist?cateid='+item.id\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t<view class=\"menu-text-view\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t<block v-for=\"(item,index) in catelist\" v-else>\r\n\t\t\t\t\t\t<view class=\"menu-options\" @tap=\"goto\" :data-url=\"'hotellist?cateid='+item.id\">\r\n\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t<view class=\"menu-text-view\">{{item.name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--  -->\r\n\t\t\t\t<view class=\"hotels-list\">\r\n\t\t\t\t\t<view class=\"hottitle\">热门{{text['酒店']}}</view>\r\n\t\t\t\t\t<block v-for=\"(item,index) in datalist\">\r\n\t\t\t\t\t\t<view class=\"hotels-options\" @tap=\"goto\" :data-url=\"'hoteldetails?id='+item.id\">\r\n\t\t\t\t\t\t\t<view class=\"hotel-img\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.pic\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"hotel-info\">\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-address\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-characteristic\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(items,indexs) in item.tag\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"characteristic-options\" :style=\"'background:rgba('+t('color1rgb')+',0.05);color:'+tColor('color1')\" v-if=\"indexs < 6\">{{items}} </view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-but-view\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"make-info\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price\" :style=\"{color:t('color1')}\" v-if=\"item.min_daymoney\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price-num\">{{item.min_daymoney}}{{moneyunit}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view>/晚起</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price\" :style=\"{color:t('color1')}\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t<view>￥</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price-num\">{{item.min_price}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view>起</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-text\">{{item.sales}}人已预定 </view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-make\"  :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\">预约</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"height: 160rpx;\"></view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\r\n\t\t\t<!-- 选择日期弹窗 -->\r\n\t\t\t<view v-if=\"calendarvisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">选择日期</text>\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose2.png`\" class=\"popup__close\" style=\"width:56rpx;height:56rpx;top:20rpx;right:20rpx\" @tap.stop=\"handleClickMask\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"reserve-time-view\" >\r\n\t\t\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t\t\t<view class='time-title'>入住</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"date-time\">{{startDate}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class='time-title'>{{startWeek}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='statistics-view'>\r\n\t\t\t\t\t\t\t\t<view class=\"statistics-date\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t\t\t\t\t\t共{{dayCount}}晚\r\n\t\t\t\t\t\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"color-line\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t\t\t<view class='time-title'>离店</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"date-time\">{{endDate}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='time-title'>{{endWeek}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"calendar-view\">\r\n\t\t\t\t\t\t\t<calendar :is-show=\"true\" :isFixed='false' showstock='0' :start-date=\"starttime\" :end-date=\"endtime\" mode=\"2\"  @callback=\"getDate\"  :themeColor=\"t('color1')\"  :maxdays='maxdays' >\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t</calendar>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"choose-but-class\" :style=\"'background: linear-gradient(90deg,rgba('+tColor('color1rgb')+',1) 0%,rgba('+tColor('color1rgb')+',1) 100%)'\" @tap=\"popupClose\">\r\n\t\t\t\t\t\t\t确认{{dayCount}}晚 \r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\r\n\t\t</block>\r\n\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"  @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport calendar from '../mobile-calendar-simple/Calendar.vue'\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tcomponents:{\r\n\t\t    calendar\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\topt:{},\r\n\t\t\t\tisload: false,\r\n\t\t\t\tbannerindex:0,\r\n\t\t\t\tset:[],\r\n\t\t\t\tcateList:[],\r\n\t\t\t\ttabindex:0,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tdatalist:[],\r\n\t\t\t\tstartDate:'',\r\n\t\t\t\tendDate:'',\r\n\t\t\t\tstartWeek:'',\r\n\t\t\t\tendWeek:'',\r\n\t\t\t\tdayCount:1,\r\n\t\t\t\tstarttime:'',\r\n\t\t\t\tendtime:'',\r\n\t\t\t\tkeyword:'',\r\n\t\t\t\tcateid:'',\r\n\t\t\t\ttext:[],\r\n\t\t\t\tcalendarvisible:false,\r\n\t\t\t\tcatelist:[],\r\n\t\t\t\tloading:false,\r\n\t\t\t\tmaxdays:10,\r\n\t\t\t\tmoneyunit:'元'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tconsole.log('--------------------');\r\n\t\t\tconsole.log(this.opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tapp.post('ApiHotel/getsysset', {}, function (res) { \r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\tthat.set=res.set\r\n\t\t\t\t\t\t\tthat.catelist = res.catelist\r\n\t\t\t\t\t\t\tif(res.catelist.length>0){\r\n\t\t\t\t\t\t\t\t\tthat.cateid = res.catelist[0].id\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar starttime = app.getCache('startTime');\r\n\t\t\t\t\t\t\tvar starttimestamp = new Date(starttime).getTime();\r\n\t\t\t\t\t\t\tvar timestampnow = Date.now();\r\n\r\n\t\t\t\t\t\t\tvar endtime = app.getCache('endTime');\r\n\t\t\t\t\t\t\tvar endtimestamp = new Date(endtime).getTime();\r\n\t\t\t\t\r\n\t\t\t\t\t\t\tif(!starttime || timestampnow>starttimestamp){\r\n\t\t\t\t\t\t\t\t\tapp.setCache('startTime',res.startday);\r\n\t\t\t\t\t\t\t\t\tstarttime = app.getCache('startTime');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif(!endtime  || timestampnow>endtimestamp){\r\n\t\t\t\t\t\t\t\t\tapp.setCache('endTime',res.endday);\r\n\t\t\t\t\t\t\t\t\tendtime = app.getCache('endTime');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar daycount = 1;\r\n\t\t\t\t\t\t\tstarttimestamp = new Date(starttime).getTime();\r\n\t\t\t\t\t\t\tendtimestamp = new Date(endtime).getTime();\r\n\t\t\t\t\t\t\tdaycount = (endtimestamp/1000-starttimestamp/1000)/86400;\r\n\t\t\t\t\t\t\tconsole.log(daycount);\r\n\t\t\t\t\t\t\tapp.setCache('dayCount',daycount);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\tvar weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\t\t\t\t\tvar day = new Date(starttime).getDay();\r\n\t\t\t\t\t\t\tvar day2 = new Date(endtime).getDay();\r\n\t\t\t\t\t\t\tvar startWeek = weekdays[day];\r\n\t\t\t\t\t\t\tvar endWeek = weekdays[day2];\r\n\t\t\t\t\t\t\tvar startDate = starttime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\t\tvar endDate = endtime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\t\tapp.setCache('startDate',startDate);\r\n\t\t\t\t\t\t\tapp.setCache('endDate',endDate);\r\n\t\t\t\t\t\t\tthat.starttime = starttime;\r\n\t\t\t\t\t\t\tthat.endtime = endtime;\r\n\t\t\t\t\t\t\tthat.startDate = startDate\r\n\t\t\t\t\t\t\tthat.endDate = endDate\r\n\t\t\t\t\t\t\tthat.startWeek = startWeek\r\n\t\t\t\t\t\t\tthat.endWeek = endWeek\r\n\t\t\t\t\t\t\tthat.maxdays = res.maxdays\r\n\t\t\t\t\t\t\tthat.text = res.text\r\n\t\t\t\t\t\t\tthat.dayCount = daycount\r\n\t\t\t\t\t\t\tthat.moneyunit = res.moneyunit\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\t\ttitle:res.text['酒店']+'首页'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t\t\tthat.getdatalist()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t })\r\n\t\t\t},\r\n\t\t\tgetdatalist: function (loadmore) {\r\n\t\t\t\tif(!loadmore){\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar pagenum = that.pagenum\r\n\t\t\t\tvar cid = that.curCid;\r\n\t\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\t\tvar cpid = that.opt.cpid ? that.opt.cpid : '';\r\n\t\t\t\tvar order = that.order;\r\n\t\t\t\tvar field = that.field; \r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tapp.post('ApiHotel/gethotels', {pagenum: pagenum,field: field,order: order,cid: cid}, function (res) { \r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\tif(pagenum == 1){\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleClickMask:function(){\r\n\t\t\t\tthis.calendarvisible = false;\r\n\t\t\t},\r\n\t\t\ttabChange(index){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tthis.tabindex = index;\r\n\t\t\t\tthat.cateid = that.catelist[index].id\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tselectDate:function(){\r\n\t\t\t\t// 选择日期弹窗-------------------------------------------------------------------------------------------\r\n\t\t\t\t//this.$refs.popupTime.open();\r\n\t\t\t\tthis.calendarvisible = true;\r\n\t\t\t},\r\n\t\t\tgetDate(date){\r\n\t\t\t\tconsole.log(date);\r\n\t\t\t\tvar that=this\r\n\t\t\t\tif(date.dayCount){\r\n\t\t\t\t\t\tapp.setCache('dayCount',date.dayCount)\r\n\t\t\t\t\t\tthat.dayCount = date.dayCount;\r\n\t\t\t\t}\r\n\t\t\t\tif(date.startStr){\r\n\t\t\t\t\t\tvar starttime =  date.startStr.dateStr\r\n\t\t\t\t\t\tapp.setCache('startTime',starttime);\r\n\t\t\t\t\t\tvar startDate = starttime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\tapp.setCache('startDate',startDate);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.starttime = starttime\r\n\t\t\t\t\t\tthat.startDate = startDate\r\n\t\t\t\t\t\tthat.startWeek = date.startStr.week\r\n\t\t\t\t}\r\n\t\t\t\tif(date.endStr){\r\n\t\t\t\t\tvar endtime =  date.endStr.dateStr\r\n\t\t\t\t\tapp.setCache('endTime',endtime);\r\n\t\t\t\t\tvar endDate = endtime.substr(5).replace('-', '月');\r\n\t\t\t\t\tapp.setCache('endDate',endDate);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\tthat.endtime = endtime\r\n\t\t\t\t\tthat.endDate = endDate\r\n\t\t\t\t\tthat.endWeek = date.endStr.week\r\n\t\t\t\t}\t\t\t\r\n\t\t\t },\r\n\t\t\tpopupClose(){\r\n\t\t\t\t//this.$refs.popupTime.close();\r\n\t\t\t\tthis.calendarvisible = false;\r\n\t\t\t},\r\n\t\t\tsearch: function () {\r\n\t\t\t\tvar that=this;\r\n\t\t\t\tvar cateid = that.cateid\r\n\t\t\t\tvar keyword  = that.keyword\r\n\t\t\t\tapp.goto('hotellist?keyword=' + keyword+'&cateid='+cateid);\r\n\t\t\t},\r\n\t\t\tsearchChange: function (e) {\r\n\t\r\n\t\t\t  this.keyword = e.detail.value;\r\n\t\t\t},\r\n\t\t\ttColor(text){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(text=='color1'){\r\n\t\t\t\t\tif(app.globalData.initdata.color1 == undefined){\r\n\t\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\t\tthat.tColor('color1')\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\treturn app.globalData.initdata.color1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(text=='color2'){\r\n\t\t\t\t\treturn app.globalData.initdata.color2;\r\n\t\t\t\t}else if(text=='color1rgb'){\r\n\t\t\t\t\tif(app.globalData.initdata.color1rgb == undefined){\r\n\t\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\t\tthat.tColor('color1rgb')\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tvar color1rgb = app.globalData.initdata.color1rgb;\r\n\t\t\t\t\t\treturn color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(text=='color2rgb'){\r\n\t\t\t\t\tvar color2rgb = app.globalData.initdata.color2rgb;\r\n\t\t\t\t\treturn color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn app.globalData.initdata.textset[text] || text;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.popup__modal{bottom:90px}\r\n\t.dp-tabbar{ bottom: 0; position: absolute;}\r\n.dp-banner{width: 100%;height: 450px;}\r\n.dp-banner-swiper{width:100%;height:100%;}\r\n.dp-banner-swiper-img{width:100%;height:auto}\r\n.position-view{width: 100%;position: absolute;top:30rpx;}\r\n.banner-poster{width: 82%;margin: 30rpx auto 0rpx;display: flex;flex-direction:column;align-items: flex-end; height:260rpx}\r\n.banner-poster .poster-title{color: #FFFFFF;font-size: 56rpx;font-weight: 900;padding: 30rpx 0rpx;}\r\n.banner-poster .poster-text{color: #FFFFFF;font-size: 26rpx;opacity: 0.6;padding: 10rpx 0rpx;}\r\n.banner-poster .poster-but{width: 108px;height: 36px;color: #FFFFFF;text-align: center;line-height: 36px;font-size: 28rpx;font-weight: bold;margin: 40rpx 0rpx;border-radius: 36px;}\r\n\r\n/*  */\r\n.reserve-view{width: 96%;background-color: #fff;margin: 120rpx auto 0rpx;border-radius: 20rpx;position: relative;padding-bottom: 1rpx;}\r\n.reserve-view .tab-view{color: #111111;font-size: 32rpx;height: 140rpx;width:100%;font-weight: bold;border-radius: 20rpx;\r\nbox-sizing: content-box;position: absolute;left: 0;top: -20rpx;overflow: hidden;}\r\n.reserve-view .tab-view .scroll-class{white-space: nowrap;width: 100%;height: 140rpx;position: relative;}\r\n.reserve-view .tab-view .options-tab{width: 33%;height: 120rpx;text-align: center;line-height: 120rpx;display: inline-block;position: relative;background: #ebeef5;margin-top: 20rpx;}\r\n.reserve-view .tab-view .options-tab-active{border-radius: 20rpx 0rpx 0rpx 0rpx;}\r\n.reserve-view .tab-view .options-tab-active2{border-radius: 0rpx 20rpx 0rpx 0rpx;}\r\n.reserve-view .tab-view .active-class-show{width: 100%;position: absolute;top:-20rpx;left: 0rpx;height: 140rpx;line-height: 155rpx;\r\nbackground: #fff;border-radius: 30rpx 30rpx 0rpx 0rpx;z-index: 9;}\r\n.reserve-view .tab-view .active-class-show .text-view{position: relative;width: 100%;}\r\n.reserve-view .tab-view .color-view{width: 60rpx;height: 10rpx;opacity: .4;border-radius: 10rpx;position: absolute;bottom: 20rpx;left:50%;margin-left:-30rpx;}\r\n.reserve-view .tab-view .active-class-show::after{content: ' ';display: block; width: 0;height: 0;border-top: 0rpx solid transparent; border-right: 60rpx solid transparent;\r\n        border-left: 0rpx solid transparent;border-bottom: 130rpx solid #fff;position: absolute;right: -54rpx;bottom: 0;}\r\n.reserve-view .tab-view .active-class-show-last{width: 100%;position: absolute;top:-20rpx;right: 0rpx;height: 140rpx;background: #fff;z-index: 9;line-height: 155rpx;\r\nborder-radius: 30rpx 30rpx 0rpx 0rpx;}\r\n.reserve-view .tab-view .active-class-show-last::before{content: ' ';display: block; width: 0;height: 0;border-top: 0rpx solid transparent; \r\n        border-right: 0rpx solid transparent;border-left: 60rpx solid transparent;border-bottom: 130rpx solid #fff;position: absolute;left: -54rpx;bottom: 0;}\r\n.reserve-view .tab-view .active-class-show-s{width: 100%;position: absolute;top:-20rpx;right: 0rpx;height: 140rpx;background: #fff;line-height: 155rpx;\r\nz-index: 9;border-radius: 30rpx 30rpx 0rpx 0rpx;}\r\n.reserve-view .tab-view .active-class-show-s::after{content: ' ';display: block; width: 0;height: 0;border-top: 0rpx solid transparent; border-right: 60rpx solid transparent;\r\n        border-left: 0rpx solid transparent;border-bottom: 130rpx solid #fff;position: absolute;right: -54rpx;bottom: 0;}\r\n.reserve-view .tab-view .active-class-show-s::before{content: ' ';display: block; width: 0;height: 0;border-top: 0rpx solid transparent; \r\n        border-right: 0rpx solid transparent;border-left: 60rpx solid transparent;border-bottom: 130rpx solid #fff;position: absolute;left: -54rpx;bottom: 0;}\r\n.reserve-view .reserve-time-view{width: 88%;height:130rpx;margin:30rpx auto 0;border-bottom: 1px  #f0f0f0 solid;display: flex;align-items: center;\r\njustify-content: space-between;}\r\n.reserve-view .reserve-time-view .time-view{display: flex;flex-direction: column;align-items: flex-start;}\r\n.reserve-view .reserve-time-view .time-view .time-title{color: #7B8085;line-height: 24rpx;}\r\n.reserve-view .reserve-time-view .time-view .date-time{color: #111111;font-size: 32rpx;font-weight: bold;padding-right: 20rpx;}\r\n.reserve-view .reserve-time-view .statistics-view{display: flex;flex-direction: column;align-items: center;justify-content: center;}\r\n.reserve-view .reserve-time-view .statistics-view .statistics-date{height: 32rpx;border-radius: 20px;font-size: 26rpx; padding:5rpx 20rpx;\r\ncolor: #000;border: 1rpx solid #000;box-sizing: border-box;display: flex;align-items: center;justify-content: center;position: relative}\r\n.reserve-view .reserve-time-view .statistics-view .color-line{border-top: 1rpx solid #f0f0f0;width: 130rpx;margin-top: 25rpx;}\r\n/*  */\r\n.reserve-view .search-view{width: 88%;height:130rpx;margin:0 auto;border-bottom: 1px  #f0f0f0 solid;display: flex;align-items: center;justify-content: space-between;}\r\n.reserve-view .search-view image{width: 28rpx;height: 28rpx;margin-right: 20rpx;}\r\n.reserve-view .search-view .input-class{flex: 1;height: 80rpx;line-height: 80rpx;font-size: 28rpx;}\r\n.reserve-view .search-but{width: 88%;height:96rpx;margin:60rpx auto;border-radius: 36px;display: flex;align-items: center;justify-content: center;color: #FFFFFF;font-size:30rpx; z-index: 10; position: relative;\r\nletter-spacing: 3rpx;}\r\n/*  */\r\n.menu-view{width: 96%;margin: 40rpx auto 0rpx;display: flex;align-items: center;justify-content: space-around;}\r\n.menu-view .scroll-view-class{width: 100%;white-space: nowrap;}\r\n.menu-view .menu-options{display: flex;flex-direction: column;align-items: center;justify-content: center;width: 21.5%;border-radius: 8px;padding: 20rpx;background: #FFFFFF;}\r\n.menu-view .menu-options image{width: 96rpx;height: 96rpx;}\r\n.menu-view .menu-options-scroll{display: flex;flex-direction: column;align-items: center;justify-content: center;width: 100%;border-radius: 8px;padding: 20rpx;background: #FFFFFF; margin-right: 30rpx;}\r\n.menu-view .menu-options-scroll image{width: 96rpx;height: 96rpx;}\r\n.menu-view .menu-options .menu-text-view{color: #343536;font-size: 24rpx;margin-top: 20rpx;}\r\n/*  */\r\n.hotels-list{width: 96%;margin: 40rpx auto 0rpx;display: flex;flex-direction:column;}\r\n.hotels-list .hottitle{ font-size: 32rpx; font-weight: bold; padding:20rpx }\r\n\r\n.hotels-list .hotels-options{width: 100%;padding: 20rpx;display: flex;align-items: center;justify-content: space-between;border-radius: 8px;background: #FFFFFF;margin-bottom: 20rpx;}\r\n.hotels-list .hotels-options .hotel-img{width: 98px;height: 130px;border-radius: 15rpx;overflow: hidden;}\r\n.hotels-list .hotels-options .hotel-img image{width: 100%;height: 100%;}\r\n.hotels-list .hotels-options .hotel-info{flex: 1;padding-left: 20rpx;}\r\n.hotels-list .hotels-options .hotel-info .hotel-title{width: 100%;color: #343536;font-size: 30rpx;}\r\n.hotels-list .hotels-options .hotel-info .hotel-address{width: 100%;color: #7B8085;font-size: 24rpx;margin-top: 7rpx;}\r\n.hotels-list .hotels-options .hotel-info .hotel-characteristic{width: 100%;display: flex; flex-wrap: wrap; align-items: center;justify-content: flex-start;margin-top: 7rpx;}\r\n.hotels-list .hotels-options .hotel-info .hotel-characteristic .characteristic-options{font-size: 20rpx;padding: 7rpx 13rpx;flex-wrap: wrap;margin-right: 20rpx; margin-top: 6rpx;}\r\n.hotels-list .hotels-options .hotel-info .hotel-but-view{width: 100%;display: flex;align-items: center;justify-content: space-between;margin-top: 25rpx;}\r\n.hotels-list .hotels-options .hotel-info .hotel-but-view .make-info{display: flex;flex-direction: column;justify-content: flex-start;}\r\n.hotels-options .hotel-info .hotel-but-view .make-info .hotel-price{display: flex;align-items: center;justify-content: flex-start;font-size: 24rpx;}\r\n.hotel-info .hotel-but-view .make-info .hotel-price .hotel-price-num{font-size: 40rpx;font-weight: bold;padding: 0rpx 3rpx;}\r\n.hotels-options .hotel-info .hotel-but-view .make-info .hotel-text{color: #7B8085;font-size: 24rpx;margin-top: 15rpx;}\r\n.hotels-list .hotels-options .hotel-info .hotel-but-view .hotel-make{background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);width: 72px;height: 32px;line-height: 32px;\r\ntext-align: center;border-radius: 36px;color: #FFFFFF;font-size: 28rpx;font-weight: bold;}\r\n\r\n\r\n/*时间弹窗*/\r\n\t.calendar-view{width: 100%;position: relative;max-height: 60vh;padding-top: 30rpx;height: auto;overflow: hidden;padding-bottom: env(safe-area-inset-bottom);}\r\n\t/*  */\r\n\t.popup__content{ background: #fff;overflow:hidden}\r\n\t.popup__content .reserve-time-view{width: 88%;height:130rpx;margin:30rpx auto 0;border-bottom: 1px  #f0f0f0 solid;display: flex;align-items: center;\r\n\tjustify-content: space-between;}\r\n\t.popup__content .reserve-time-view .time-view{display: flex;flex-direction: column;align-items: flex-start;}\r\n\t.popup__content .reserve-time-view .time-view .time-title{color: #7B8085;line-height: 24rpx;}\r\n\t.popup__content .reserve-time-view .time-view .date-time{color: #111111;font-size: 32rpx;font-weight: bold;padding-right: 20rpx;}\r\n\t.popup__content .reserve-time-view .statistics-view{display: flex;flex-direction: column;align-items: center;justify-content: center;}\r\n\t.popup__content .reserve-time-view .statistics-view .statistics-date{width: 88rpx;height: 32rpx;border-radius: 20px;font-size: 20rpx;\r\n\tcolor: #000;border: 1rpx solid #000;box-sizing: border-box;display: flex;align-items: center;justify-content: center;position: relative}\r\n\t.statistics-view .statistics-date .content-decorate{width: 13rpx;height: 2rpx;background: red;position: absolute;top: 50%;}\r\n\t.statistics-view .statistics-date .left-c-d{left: -13rpx;background: #000;}\r\n\t.statistics-view .statistics-date .right-c-d{right: -13rpx;background: #000}\r\n\t.popup__content .reserve-time-view .statistics-view .color-line{border-top: 1rpx solid #f0f0f0;width: 130rpx;margin-top: 25rpx;}\r\n\t.uni-popup__wrapper-box{background: #f7f8fa;border-radius: 40rpx 40rpx 0rpx 0rpx;overflow: hidden;}\r\n\t\r\n\t.popup__content .popup-close{position: fixed;right: 20rpx;top: 20rpx;width: 56rpx;height: 56rpx;z-index: 11;}\r\n\t.popup__content .popup-close image{width: 100%;height: 100%;}\r\n\t.choose-but-class{width: 94%;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);color: #FFFFFF;font-size: 32rpx;font-weight: bold;padding: 24rpx;\tborder-radius: 60rpx;position: fixed;bottom: 10rpx;left: 50%;transform: translateX(-50%);margin-bottom: env(safe-area-inset-bottom);text-align: center;}\r\n\t/*  */\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839391261\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}