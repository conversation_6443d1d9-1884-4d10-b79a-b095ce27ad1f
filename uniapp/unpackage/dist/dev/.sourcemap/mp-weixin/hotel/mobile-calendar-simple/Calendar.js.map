{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?0f29", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?e842", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?f9ae", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?8b35", "uni-app:///hotel/mobile-calendar-simple/Calendar.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?2b02", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?c15d"], "names": ["props", "isShow", "type", "default", "isFixed", "transition", "title", "mode", "startDate", "dayroomprice", "showstock", "maxdays", "text", "endDate", "betweenStart", "betweenEnd", "initMonth", "themeColor", "selectedColor", "tipData", "ysNum", "chooseType", "data", "startDates", "endDates", "betweenStarts", "betweenEnds", "calendar", "weekList", "thisday", "thismonth", "watch", "mounted", "computed", "getBetweenColor", "hex", "methods", "init", "date", "month", "day", "startStr", "createDayList", "_week", "list", "getDayNum", "day<PERSON>um", "createClendar", "yearTemp", "monthTemp", "dayList", "year", "_monthData", "console", "scrollTop", "setTimeout", "wrap", "addClassName", "className", "_date", "addClassBg", "themeOpacityBg", "themeBg", "resetTime", "setTip", "tip", "setOrderTip", "value", "isCurrent", "dateFormat", "recent", "dateStr", "week", "chooseDate", "choose"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwC11B;AAAA,gBACA;EACAA;IACAC;MAAA;MACAC;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACAF;MACAC;QACA;MACA;IACA;IACAE;MAAA;MACAH;MACAC;QACA;MACA;IACA;IACAG;MAAA;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MAAA;MACAL;MACAC;QACA;MACA;IACA;IACAK;MAAA;MACAN;IACA;IACAO;MACAP;IACA;IACAQ;MACAR;MACAC;QACA;MACA;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;IACAS;MACAV;IACA;IACAW;MAAA;MACAX;IACA;IACAY;MAAA;MACAZ;MACAC;QACA;MACA;IACA;IACAY;MAAA;MACAb;MACAC;QACA;MACA;IACA;IACAa;MAAA;MACAd;MACAC;QACA;MACA;IACA;IACAc;MAAA;MACAf;MACAC;IACA;IACAe;MAAA;MACAhB;MACAC;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACAiB;MACAlB;MACAC;QACA;MACA;IACA;IACAkB;MACAnB;MACAC;QACA;MACA;IACA;EACA;EACAmB;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA9B;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;IACA;EACA;EACAiB;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACA;QACA;UACAA;QACA;UACAA;QACA;MACA;MAAA;QACA;UACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;UACAC;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACAC;QACAC;MACA;MACA;MACA;QACA;QACA;QACA;UACAC;UACAX;UACAY;QACA;QACA;QACA;UACAA;QACA;UACAA;QACA;QACA;UACAZ;QACA;QACA;UACAA;QACA;QACAa;QACAA;QACAA;QACAC;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACAC;QACA;QACA;QACA;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;UACAA;QACA;MACA;QACA;UACAA;QACA;QAEA;QACA;UACA;YACAA;UACA;QACA;MAEA;QACA;UACAA;QACA;MACA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;QACA;UACAD;QACA;MACA;QACA;UACAA;QACA;MACA;MAEAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAF;MACA;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAzB;MACAA;MACAA;MACAA;MACA;IACA;IACA;IACA0B;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAA;QACA;UACAA;QACA;QACA;MACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QAAA5B;QAAA6B;MAAA;QAAA7B;QAAA6B;MAAA;MACA;QACA;UACAF;QACA;MACA;MACA;IACA;IACA;IACAG;MACA;QACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;QACAF;MACA;IACA;IACAG;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACAhC;MACA;MACA;QACA;MACA;QACAiC;QAEA;UACA;QACA;QACAA;QAEA;MACA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;QACA;MACA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAA6hD,CAAgB,85CAAG,EAAC,C;;;;;;;;;;;ACAjjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "hotel/mobile-calendar-simple/Calendar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./Calendar.vue?vue&type=template&id=052beb44&scoped=true&\"\nvar renderjs\nimport script from \"./Calendar.vue?vue&type=script&lang=js&\"\nexport * from \"./Calendar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Calendar.vue?vue&type=style&index=0&id=052beb44&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"052beb44\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"hotel/mobile-calendar-simple/Calendar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=template&id=052beb44&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isShow\n    ? _vm.__map(_vm.weekList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          (index == 0 || index == _vm.weekList.length - 1) && _vm.themeColor\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var l2 = _vm.isShow\n    ? _vm.__map(_vm.calendar, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var l1 = _vm.__map(item.dayList, function (day, idx) {\n          var $orig = _vm.__get_orig(day)\n          var m0 = _vm.addClassBg(day, item.month, item.year)\n          var m1 = _vm.themeOpacityBg(day, item.month, item.year)\n          var m2 =\n            _vm.mode != 4 ? _vm.addClassName(day, item.month, item.year) : null\n          var m3 =\n            _vm.mode != 4 ? _vm.themeBg(day, item.month, item.year) : null\n          var m4 =\n            _vm.mode != 4 ? _vm.setTip(day, item.month, item.year, 2) : null\n          var m5 =\n            _vm.mode != 4 && m4\n              ? _vm.setTip(day, item.month, item.year, 2)\n              : null\n          var m6 = !(_vm.mode != 4)\n            ? _vm.addClassName(day, item.month, item.year)\n            : null\n          var m7 = !(_vm.mode != 4)\n            ? _vm.setOrderTip(day, item.month, item.year)\n            : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n          }\n        })\n        return {\n          $orig: $orig,\n          l1: l1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=script&lang=js&\"", "<template>\n    <transition :name=\"transition\">\n        <div class=\"calendar-tz\" v-if=\"isShow\" :class=\"isFixed&&'fixed'\">\n            <slot name=\"header\"></slot>\n            <div class=\"week-number\">\n                <span v-for=\"(item,index) in weekList\" :style=\"{color:(index==0||index==weekList.length-1)&&themeColor}\" :key=\"index\">{{item}}</span>\n            </div>\n            <p class=\"tips\" v-if=\"title\">{{title}}</p>\n            <div class=\"content\" id=\"scrollWrap\">\n                <div class=\"con\" v-for=\"(item,index) in calendar\" :key=\"index\"  :id=\"item.year+''+item.month\">\r\n\t\t\t\t\t\t\t\t\t\n                    <h3 v-text=\"item.year + '年' + item.month + '月'\"></h3>\n                    <span class=\"month-bg\"  :style=\"{color:getBetweenColor}\">{{item.month}}</span>\n                    <ul class=\"each-month\">\n                        <li class=\"each-day\" v-for=\"(day,idx) in item.dayList\" :key=\"idx\" :class=\"[addClassBg(day, item.month, item.year)]\" :style=\"{background:themeOpacityBg(day, item.month, item.year)}\" @click=\"chooseDate(day, item.month, item.year)\">\n                            <div :class=\"[addClassName(day, item.month, item.year)]\" :style=\"{background:themeBg(day, item.month, item.year)}\" v-if=\"mode !=4\">\n                                <!-- <p class=\"day-tip\" :style=\"{color:themeColor}\"><i v-text=\"setTip(day, item.month, item.year,0)\"></i></p> -->\n                                <p class=\"day\">{{day?day:''}}</p>\n                                <p class=\"recent\" v-if=\"setTip(day, item.month, item.year,2)\"><i v-text=\"setTip(day, item.month, item.year,2)\"></i></p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class=\"recent\" v-else-if=\"showstock==1 && day>0 \" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"dayroomprice[item.year+'-'+(item.month<=9?'0':'')+item.month+'-'+((day>0 && day<=9)?'0':'')+(day?day:'')] && dayroomprice[item.year+'-'+(item.month<=9?'0':'')+item.month+'-'+((day>0 && day<=9)?'0':'')+(day?day:'')]['status']==1 \">{{dayroomprice[item.year+'-'+(item.month<=9?'0':'')+item.month+'-'+(day<=9?'0':'')+(day?day:'')]['stock']}}{{text['间']}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n                            </div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div :class=\"[addClassName(day, item.month, item.year)]\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- <p class=\"day-tip\" :style=\"{color:themeColor}\"><i v-text=\"setTip(day, item.month, item.year,0)\"></i></p> -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class=\"day\">{{day?day:''}}</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class=\"recent\" >1<i v-text=\"setOrderTip(day, item.month, item.year)\"></i></p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n                        </li>\n                    </ul>\n                </div>\n            </div>\r\n\t\t\t\t\t\t<div style='height:80px'></div>\n            <slot name=\"footer\"></slot>\n        </div>\n    </transition>\n</template>\n\n<script>\r\nvar app = getApp();\nexport default {\n  props: {\n    isShow: {//是否显示\n        type: [Boolean],\n        default() {\n            return false;\n        }\n    },\n    isFixed: {//是否定位全屏\n        type: [Boolean],\n        default() {\n            return true;\n        }\n    },\n    transition: {//动画类型slide\n        type: [String],\n        default() {\n            return \"\";\n        }\n    },\n    title: {//头部的一段文本\n        type: [String, Object],\n        default() {\n            return \"\";\n        }\n    },\n    mode: {//模式：1普通日历，2酒店，3飞机往返\n        type: [String, Number],\n        default() {\n            return 1;\n        }\n    },\n    startDate: {//开始日期\n        type: [String, Object, Date]\n    },\r\n\t\tdayroomprice:{\r\n\t\t\ttype:[Object,Array],\r\n\t\t},\r\n\t\tshowstock: {\r\n\t\t    type: [String],\r\n\t\t    default() {\r\n\t\t        return \"0\";\r\n\t\t    }\r\n\t\t},\t\r\n\t\tmaxdays: {\r\n\t\t    type: [String],\r\n\t\t    default() {\r\n\t\t        return \"0\";\r\n\t\t    }\r\n\t\t},\r\n\t\ttext:{\r\n\t\t\ttype:[Object,Array],\r\n\t\t},\n    endDate: {//结束日期\n        type: [String, Object, Date]\n    },\n    betweenStart: {//日历可选范围开始\n        type: [String, Object, Date],\n        default() {\n            return \"\";\n        }\n    },\n    betweenEnd: { //日历可选结束日期\n        type: [String, Object, Date],\n        default() {\n            return \"\";\n        }\n    },\n    initMonth: {//初始化的月数\n        type: [String, Number],\n        default() {\n            return 6;\n        }\n    },\n    themeColor: {//主题色\n        type: [String],\n        default: \"#1C75FF\"\n    },\r\n\tselectedColor: {//选中色\r\n\t    type: [String],\r\n\t    default: \"#f44336\"\r\n\t},\r\n\ttipData:{\r\n\t\ttype:[String,Object,Array],\r\n\t\tdefault() {\r\n\t\t    return [];\r\n\t\t}\r\n\t},\r\n\tysNum:{\r\n\t\ttype:[String],\r\n\t\tdefault() {\r\n\t\t    return '0';\r\n\t\t}\r\n\t},\r\n\tchooseType:{\r\n\t\ttype:[String],\r\n\t\tdefault() {\r\n\t\t    return '';\r\n\t\t}\r\n\t}\n  },\n  data() {\n    return {\n        startDates: \"\",\n        endDates: \"\",\n        betweenStarts: \"\",\n        betweenEnds: \"\",\n        calendar: [],\n        weekList: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\r\n\t\t\t\tthisday:'',\r\n\t\t\t\tthismonth:''\n    };\n  },\n  watch: {\n    isShow() {\n        this.init();\n    },\n    betweenStart() {\n        this.init();\n    },\n    betweenEnd() {\n        this.init();\n    }\n  },\n  mounted() {\n    this.init();\n  },\n  computed: {\n    //设置主题色入住离开之间的背景色\n    getBetweenColor() {\n        if (!this.themeColor) return;\n        var hex = this.themeColor;\n        if (hex.length == 4) {\n            hex = `#${hex[1]}${hex[1]}${hex[2]}${hex[2]}${hex[3]}${hex[3]}`;\n        }\n        var str = \"rgba(\" + parseInt(\"0x\" + hex.slice(1, 3)) + \",\" + parseInt(\"0x\" + hex.slice(3, 5)) + \",\" + parseInt(\"0x\" + hex.slice(5, 7)) + \",0.1)\";\n        return str;\n    }\n  },\n  methods: {\n    init() {\r\n\t\t//1默认 2工作日 3周末 4隔天\r\n\t\tvar addDate = new Date();\r\n        var date = new Date(addDate);\r\n\t\tdate.setDate(addDate.getDate() + parseInt(this.ysNum));\r\n\t\tif(this.chooseType=='2'){\r\n\t\t\tif(date.getDay()=='0'){\r\n\t\t\t\tdate.setDate(date.getDate() + 1);\r\n\t\t\t}else if(date.getDay()=='6'){\r\n\t\t\t\tdate.setDate(date.getDate() + 2);\r\n\t\t\t}\r\n\t\t}if(this.chooseType=='3'){\r\n\t\t\tif(date.getDay()!='0'||date.getDay()!='6'){\r\n\t\t\t\tdate.setDate(date.getDate() + (6 - date.getDay()));\r\n\t\t\t}\r\n\t\t}\n        this.year = date.getFullYear();\n        this.month = date.getMonth() + 1;\n        this.day = date.getDate();\n        this.today = new Date(this.year + \"/\" + this.month + \"/\" + this.day) * 1;\r\n\t\t\t\tthis.thisday  =this.day\r\n\t\t\t\tthis.thismonth = this.month\n        if (!this.startDate) {\n            const year = date.getFullYear(),\n                month = date.getMonth() + 1,\n                day = date.getDate();\n            this.startDates = this.resetTime(year + \"/\" + month + \"/\" + day);\n            this.startYear = year;\n            this.startMonth = month;\n        } else {\n            this.startDates = this.resetTime(this.startDate);\n            var dd = this.startDate.replace(/-/g, \"/\").split(\"/\");\n            this.startYear = dd[0];\n            this.startMonth = dd[1];\n        }\n        if (this.endDate) {\n            this.endDates = this.resetTime(this.endDate);\r\n            var dd = this.endDate.replace(/-/g, \"/\").split(\"/\");\r\n            this.endYear = dd[0];\r\n            this.endMonth = dd[1];\n        }\n        this.betweenStarts = this.resetTime(this.betweenStart);\n        this.betweenEnds = this.resetTime(this.betweenEnd);\r\n\t\t\t\t//console.log(this.betweenEnds);\n        this.createClendar();\r\n\t\t//默认返回 当前时间\r\n\t\tconst choose = {\r\n\t\t\tstartStr: this.dateFormat(this.startDates)\r\n\t\t};\r\n\t\t this.$emit(\"callback\", choose);\r\n    },\n    //创建每个月日历数据，传入月份1号前面用null填充\n    createDayList(month, year) {\n        const count = this.getDayNum(month, year),\n        _week = new Date(year + \"/\" + month + \"/1\").getDay();\n        let list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28];\n        for (let i = 29; i <= count; i++) {\n            list.push(i);\n        }\n        for (let i = 0; i < _week; i++) {\n            list.unshift(null);\n        }\n        return list;\n    },\n    //计算传入月份有多少天\n    getDayNum(month, year) {\n        let dayNum = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n        if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) {\n            dayNum[1] = 29;\n        }\n        return dayNum[month - 1];\n    },\n    //根据当天和结束日期创建日历数据\n    createClendar() {\n        var yearTemp = this.year;\n        var monthTemp = this.month;\n        if (!!this.betweenStarts) {\n            //如果有范围起始日期，可选范围从betweenStart开始\n            yearTemp = new Date(this.betweenStarts).getFullYear();\n            monthTemp = new Date(this.betweenStarts).getMonth() + 1;\n        }\n        this.calendar = [];\n        for (let i = 0; i < this.initMonth; i++) {\n            let year = yearTemp;\n            let month = monthTemp + i;\n            let _monthData = {\n                dayList: [],\n                month: \"\",\n                year: \"\"\n            };\n            var m = Math.ceil(month / 12);\n            if (m > 0) {\n                year += m - 1;\n            } else {\n                year += m - 1;\n            }\n            if (month > 12) {\n                month = month % 12 == 0 ? 12 : month % 12;\n            }\n            if (month <= 0) {\n                month = 12 + month % 12;\n            }\n            _monthData.year = year;\n            _monthData.month = month;\n            _monthData.dayList = this.createDayList(month, year);\r\n\t\t\t\t\t\tconsole.log(_monthData);\n            this.calendar.push(_monthData);\n        }\n        //h5默认页面加载到当前日期start-date的位置\n        if (document) {\n            this.scrollTop(this.startYear, this.startMonth);\n        }\n    },\n    scrollTop(year, month) {\n        var id = year + \"\" + parseInt(month)\n        setTimeout(() => {\n            var obj = document.getElementById(id)\n            if(!obj) return\n            var wrap = document.getElementById(\"scrollWrap\");\n            wrap.scrollTop = obj.offsetTop - 40;\n        }, 0);\n    },\n    //添加日历样式\n    addClassName(day, month, year) {\n        if (!day) return;\n        const _date = new Date(year + \"/\" + month + \"/\" + day);\n        let className = [];\n        // if (_date.getDay() == 0 || _date.getDay() == 6) { //周末或周六样式\n        //     className.push('weekend')\n        // }\n        if (_date * 1 == this.today) {\n            className.push(\"today\");\n        }\n        if (this.mode == 1) {\n            if (_date * 1 == this.startDates) {\n                className.push(\"trip-time\");\n            }\n        }else if(this.mode == 4){\r\n\t\t\tif (_date * 1 == this.startDates) {\r\n\t\t\t    className.push(\"trip-time\");\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar orderDate =this.tipData;\r\n\t\t\tfor(var i=0; i< orderDate.length;i++){\r\n\t\t\t\tif (_date  * 1 == orderDate[i]['date']) {\r\n\t\t\t\t   className.push(\"trip-time-order\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t} else {\n            if (_date * 1 == this.startDates || _date * 1 == this.endDates) {\n                className.push(\"trip-time\");\n            }\n        }\n        if (this.betweenStarts) {\n            _date * 1 < this.betweenStarts && className.push(\"disabled\");\n        } else {\n            _date * 1 < this.today && className.push(\"disabled\"); //当天和结束日期之外不可选\n        }\r\n\t\t\r\n\t\tif(this.chooseType=='2'){\r\n\t\t\tif(_date.getDay()=='0' || _date.getDay()=='6'){\r\n\t\t\t\tclassName.push(\"disabled\");\r\n\t\t\t}\r\n\t\t}else if(this.chooseType=='3'){\r\n\t\t\tif(_date.getDay()=='1' || _date.getDay()=='2' || _date.getDay()=='3' || _date.getDay()=='4' || _date.getDay()=='5'){\r\n\t\t\t\tclassName.push(\"disabled\");\r\n\t\t\t}\r\n\t\t}\r\n\t\t\n        _date * 1 > this.betweenEnds && className.push(\"disabled\");\n        return className.join(\" \");\n    },\n    //入住离开的区间背景色\n    addClassBg(day, month, year) {\n        if (!day) return;\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        let className = [];\n        if (_date >= this.startDates && _date <= this.endDates && this.mode > 1) {\n            className.push(\"between\");\n        }\n        return className.join(\" \");\n    },\n    //theme入住离开的区间背景色\n    themeOpacityBg(day, month, year) {\n        if (!this.themeColor) return;\n        if (!day) return;\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        if (_date >= this.startDates && _date <= this.endDates && this.mode > 1) {\n            return this.getBetweenColor;\n        }\n    },\n    //theme获取普通日期选中样式背景\n    themeBg(day, month, year) {\n        if (!this.themeColor) return;\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        //正常模式\n        if (this.mode == 1 ) {\n            if (_date == this.startDates) {\n                return this.themeColor;\n            }\n        }else if(this.mode ==4){\r\n\t\t\tif(_date == '1661529600000'){\r\n\t\t\t\treturn this.selectedColor;\r\n\t\t\t}\r\n\t\t} else {\n            //酒店和往返模式\n            if (_date == this.startDates || _date == this.endDates) {\n                return this.themeColor;\n            }\n        }\n    },\n    //清除时间 时 分 秒 毫秒\n    resetTime(dateStr) {\n        var date = new Date(dateStr.replace(/-/g, \"/\"));\n        date.setHours(0);\n        date.setMinutes(0);\n        date.setSeconds(0);\n        date.setMilliseconds(0);\n        return date * 1;\n    },\n    //flag==1（返回今天，明天，后天)，flag==2（返回入住，离开，去返)\n    setTip(day, month, year,flag) {\n        if (!day) return\n        var tip = \"\"\n        var _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        if(flag==1){\n            if (_date == this.today) {\n                tip = \"今天\";\n            } else if (_date - this.today == 24 * 3600 * 1000) {\n                tip = \"明天\";\n            } else if (_date - this.today == 2 * 24 * 3600 * 1000) {\n                tip = \"后天\";\n            }\n            return tip\n        }else{\n            if (this.mode == 2) {\n                if (_date == this.endDates) {\n                    tip = \"离开\";\n                } else if (_date == this.startDates) {\n                    tip = \"入住\";\n                }\n            } else if (this.mode == 3) {\n                if (_date == this.startDates && !this.endDates) {\n                    tip = \"去/返\";\n                } else {\n                    if (_date == this.endDates) {\n                        tip = \"返程\";\n                    } else if (_date == this.startDates) {\n                        tip = \"去程\";\n                    }\n                }\n            }\n            return tip;\n        }\n    },\r\n\tsetOrderTip(day, month, year,flag) {\r\n\t    if (!day) return\r\n\t    var tip = \"\"\r\n\t    var _date = this.resetTime(year + \"/\" + month + \"/\" + day);\r\n\t\t\r\n\t\tvar orderDate =[{date:\"1661529600000\",value:'待收货'},{date:\"1661702400000\",value:'待派送'}];\r\n\t\tfor(var i=0; i< orderDate.length;i++){\r\n\t\t\tif (_date == orderDate[i]['date']) {\r\n\t\t\t   tip = orderDate[i]['value'];\r\n\t\t\t}\r\n\t\t}\r\n\t    return tip;\r\n\t},\n    //是否是选中当天，或者入住离开当天\n    isCurrent(day, month, year) {\n      if (!day) {\n        return false;\n      }\n      const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n      //正常模式\n      if (this.mode == 1 || this.mode == 4 ) {\n        if (_date == this.startDates) {\n          return true;\n        }\n      } else {\n        //酒店和往返模式\n        if (_date == this.startDates || _date == this.endDates) {\n          return true;\n        }\n      }\n    },\n    dateFormat(times) {\n        let date = new Date(times);\n        let recent = \"\";\n        if (times == this.today) {\n            recent = \"今天\";\n        } else if (times - this.today === 24 * 3600 * 1000) {\n            recent = \"明天\";\n        } else if (times - this.today === 2 * 24 * 3600 * 1000) {\n            recent = \"后天\";\n        }\n        var year = date.getFullYear()\n        var month = parseInt(date.getMonth() + 1) > 9 ? parseInt(date.getMonth() + 1) : '0' + parseInt(date.getMonth() + 1)\n        var day =  date.getDate() > 9 ? date.getDate() : '0' + date.getDate()\n        return {\n            dateStr: year + \"-\" + month + \"-\" + day,\n            week: \"周\" + this.weekList[date.getDay()],\n            recent\n        };\n    },\n    chooseDate(day, month, year) {\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        const week = this.weekList[new Date(_date).getDay()];\r\n\t\tif(this.chooseType=='2'){\r\n\t\t\tif(week=='六' || week=='日'){\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}else if(this.chooseType=='3'){\r\n\t\t\tif(week=='一' || week=='二' || week=='三' || week=='四' || week=='五'){\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\n        //判断日期区域是否可点击\n        if (!day) return;\n        if (this.betweenStarts) {\n            if (_date * 1 < this.betweenStarts) return;\n        } else {\n            if (_date < this.today) return;\n        }\n        if (_date > this.betweenEnds) return;\n        //判断酒店或者往返模式的选择逻辑\n        if (this.startDates && this.endDates && _date > this.endDates) {\n            this.startDates = _date;\n            this.endDates = \"\";\n        } else if (this.endDates && _date > this.endDates) {\n            this.endDates = _date;\n        } else if (_date >= this.startDates && _date <= this.endDates) {\n            this.startDates = _date;\n            this.endDates = \"\";\n        } else if (_date < this.startDates) {\n            this.startDates = _date;\n            this.endDates = \"\";\n        } else if (_date > this.startDates) {\n            if (this.mode == 1 || this.mode == 4) {\n                this.startDates = _date;\n            } else {\n                this.endDates = _date;\n            }\n        }\n        const choose = {\n            startStr: this.dateFormat(this.startDates)\n        };\n        if (this.mode == 1 || this.mode == 4) {\n            this.$emit(\"callback\", choose);\n        } else if (this.mode == 2 && this.startDates && this.endDates) {\n            choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;\n         \r\n\t\t\t\t\t\tif( choose.dayCount>this.maxdays){\r\n\t\t\t\t\t\t\treturn app.error(\"最多可预定\"+this.maxdays+'晚');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tchoose.endStr = this.dateFormat(this.endDates);\r\n\t\t\t\t\t\t\n            this.$emit(\"callback\", choose);\n        } else if (this.mode == 3) {\n            if (this.startDates && this.endDates) {\n                choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;\n                choose.endStr = this.dateFormat(this.endDates);\n            } else if (this.startDates && !this.endDates) {\n                choose.dayCount = 0;\n                choose.endStr = this.dateFormat(this.startDates);\n            }\n            this.$emit(\"callback\", choose);\n        }else if(this.startDates && this.endDates){\r\n\t\t\tif (this.startDates && this.endDates) {\r\n\t\t\t    choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;\r\n\t\t\t    choose.endStr = this.dateFormat(this.endDates);\r\n\t\t\t} else if (this.startDates && !this.endDates) {\r\n\t\t\t    choose.dayCount = 0;\r\n\t\t\t    choose.endStr = this.dateFormat(this.startDates);\r\n\t\t\t}\r\n\t\t\tthis.$emit(\"callback\", choose);\r\n\t\t}\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\r\n\n@color: #1C75FF;\n.calendar-tz {\n    width: 100%;\n    height: 50vh;\n    background: #fff;\n    display: -webkit-box;\n    display: flex;\n    -webkit-flex-direction: column;\n    flex-direction: column;\n    &.fixed{\n        position: fixed;\n        width:100%;\n        height:100%;\n        left:0;\n        top:0;\n        z-index: 900;\n    }\n    .week-number {\n        background: #fff;\n        padding: 0 1%;\n        box-shadow: 0 2px 15px rgba(100, 100, 100, 0.1);\n        span {\n            display: inline-block;\n            text-align: center;\n            padding: 12px 0;\n            font-size: 14px;\n            width: 14.28%;\n            &:first-child,\n            &:last-child {\n                color: @color;\n            }\n        }\n    }\n    .tips {\n        padding: 6px 10px;\n        background: #fff7dc;\n        font-size: 12px;\n        color: #9e8052;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n    }\n    .content{\n        -webkit-box-flex: 1;\n        flex:1;\n        overflow-y: scroll;\n        -webkit-overflow-scrolling: touch;\r\n\t\tpadding-bottom: 100rpx;\n         .con {\n            color: #333;\n            padding-top: 10px;\n            position: relative;\n            h3 {\n                width: 100%;\n                font-weight: normal;\n                text-align: center;\n                font-size: 16px;\n                padding: 10px 0;\n            }\n            .month-bg{\n                position: absolute;\n                text-align: center;\n                opacity: 0.4;\n                left:0;\n                right:0;\n                bottom:0;\n                top:20%;\n                font-size:220px;\n                font-weight: bold;\n                color:#f8f8f8;\n            }\n            .each-month {\n                display: block;\n                width: 98%;\n                font-size: 0;\n                margin: 0 auto;\n                padding-left: 0;\n                padding-bottom: 10px;\n                border-bottom: 1px solid #eee;\n                .each-day {\n                    position: relative;\n                    display: inline-block;\n                    text-align: center;\n                    vertical-align: middle;\n                    width: 14.28%;\n                    font-size: 16px;\n                    height: 50px;\n                    margin:2px auto;\n                    div {\n                        display: inline-block;\n                        font-size: 14px;\n                        width:98%;\n                        height:100%;\n                        justify-content: space-around;\n                        display: -webkit-box;\n                        display: flex;\n                        -webkit-flex-direction: column;\n                        flex-direction: column;\n                        border-radius: 4px;\n                    }\n                    &.between {\n                        background: rgba(75, 217, 173, 0.1);\n                    }\n                    .day{\n                        font-size: 16px;\n                    }\n                    .day-tip,.recent{\n                        font-size:10px;\n                        height:14px;\n                        i{\n                            font-size:10px;\n                        }\n                    }\n                    .recent {\n                        color: #999;\n                    }\n                    .disabled {\n                        color: #ccc !important;\n                        .day-tip{\n                            color: #ccc !important;\n                        }\n                    }\n                    .today {\n                        background: rgba(100,100,100,0.1);\n                    }\n                    .trip-time {\n                        background: @color;\n                        color: #fff !important;\n                        .recent,.day-tip{\n                            color: #fff!important;\n                        }\n                    }\r\n\t\t\t\t\t.trip-time-order {\r\n\t\t\t\t\t    background: #f44336;\r\n\t\t\t\t\t    color: #fff !important;\r\n\t\t\t\t\t    .recent,.day-tip{\r\n\t\t\t\t\t        color: #fff!important;\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t}\n                    .weekend {\n                        color: @color;\n                    }\n                }\n            }\n        }\n    }\n}\n/***右侧进入动画***/\n.slide-enter-active,\n.slide-leave-active {\n  -webkit-transition: all 0.2s ease;\n  transition: all 0.2s ease;\n}\n.slide-enter,\n.slide-leave-to {\n  -webkit-transform: translateX(100%);\n  transform: translateX(100%);\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=style&index=0&id=052beb44&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=style&index=0&id=052beb44&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839377640\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}