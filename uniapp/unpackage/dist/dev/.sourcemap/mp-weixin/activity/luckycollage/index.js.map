{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luckycollage/index.vue?63aa", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luckycollage/index.vue?9ce0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luckycollage/index.vue?bffc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luckycollage/index.vue?d817", "uni-app:///activity/luckycollage/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luckycollage/index.vue?b454", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luckycollage/index.vue?fbf4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "bid", "st", "datalist", "pagenum", "navlist", "activetime", "activeindex", "selected", "top_bar_scroll", "kaituan_duration", "nowtime", "nomore", "nodata", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "changetab", "uni", "scrollTop", "duration", "getDataList", "kaituan_date", "kaituan_time"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgDv1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAC;QACA;UACAD;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;QACAA;MACA;IACA;IACAE;MACA;MACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAN;MACAA;MACAC;QAAAnB;QAAAyB;QAAAC;QAAAvB;MAAA;QACAkB;QACA;QACA;UACAH;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/luckycollage/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/luckycollage/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=38dad760&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/luckycollage/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=38dad760&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.t(\"color1\")\n        var m1 = item.istatus == 1 ? _vm.t(\"color1\") : null\n        var m2 = item.istatus == 1 ? _vm.t(\"color1rgb\") : null\n        var m3 =\n          !(item.istatus == 1) && !(item.istatus == -1) ? _vm.t(\"color2\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"navbg\"></view>\n\t\t<view class=\"nav\">\n\t\t\t<scroll-view scroll-x=\"true\" :scroll-left=\"top_bar_scroll\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t<block v-for=\"(item, index) in navlist\" :key=\"index\">\n\t\t\t\t\t<view :class=\"'item ' + (selected==index?'active':'')\" @tap=\"changetab\" :data-index=\"index\">\n\t\t\t\t\t\t\t<view class=\"t3\">{{item.seckill_date_m}}</view>\n\t\t\t\t\t\t\t<view class=\"t1\">{{item.showtime}}</view>\n\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.active==-1\">已结束</view>\n\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.active==0\">已开抢</view>\n\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.active==1\">拼团中</view>\n\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.active==2\">即将开始</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t<view class=\"content\">\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\n\t\t\t\t<image class=\"f1\" mode=\"widthFix\" :src=\"item.pic\" @tap=\"goto\" :data-url=\"'product2?id=' + item.id\"></image>\n\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\n\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t<view class=\"text1\">\n\t\t\t\t\t\t\t<text class=\"x1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\n\t\t\t\t\t\t\t<text class=\"x2\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button @tap=\"goto\" :data-url=\"'product2?id=' + item.id\" class=\"x3\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-if=\"item.istatus==1\">立即抢购</button>\n\t\t\t\t\t\t<button @tap=\"goto\" :data-url=\"'product2?id=' + item.id\" class=\"x3 xx1\" v-else-if=\"item.istatus==-1\">去看看</button>\n\t\t\t\t\t\t<button @tap=\"goto\" :data-url=\"'product2?id=' + item.id\" class=\"x3\" v-else :style=\"{background:t('color2')}\">抢先看看</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"item\" style=\"display:block\" v-if=\"nodata\"><nodata></nodata></view>\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tbid:'',\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      navlist: \"\",\n      activetime: \"\",\n      activeindex: \"\",\n      selected: \"\",\n      top_bar_scroll: \"\",\n\t\t\tkaituan_duration: \"\",\n      nowtime: \"\",\n      nomore: false,\n\t\t\tnodata:false,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st;\n\t\tthis.bid = this.opt.bid || '';\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getDataList(true);\n    }\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiLuckyCollage/index', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t} else {\n\t\t\t\t\tthat.navlist = res.navlist;\n\t\t\t\t\tthat.activetime = res.activetime;\n\t\t\t\t\tthat.activeindex = res.selected;\n\t\t\t\t\tthat.selected = res.selected;\n\t\t\t\t\tthat.top_bar_scroll = (res.selected - 2) * uni.getSystemInfoSync().windowWidth / 750 * 150;\n\t\t\t\t\tthat.kaituan_duration = res.kaituan_duration;\n\t\t\t\t\tthat.nowtime = res.nowtime;\n\t\t\t\t\tthat.getDataList();\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    changetab: function (e) {\n      var index = e.currentTarget.dataset.index;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.selected = index;\n      this.getDataList();\n    },\n    getDataList: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var selected = that.selected;\n      var navlist = that.navlist;\n      var pagenum = that.pagenum;\n\t\t\tthat.nomore = false;\n\t\t\tthat.nodata = false;\n      app.post('ApiLuckyCollage/getprolist2', {bid:that.bid,kaituan_date: navlist[selected].kaituan_date,kaituan_time: navlist[selected].kaituan_time,pagenum: pagenum}, function (res) {\n        uni.stopPullDownRefresh();\n        var data = res.data;\n        if (pagenum == 1) {\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    }\n  }\n};\n</script>\n<style>\n.container{width:100%;overflow:hidden}\n.navbg{width: 100%;position:relative}\n.navbg:after {background: linear-gradient(90deg,rgba(253, 74, 70, 1) 0%,rgba(253, 74, 70, 0.8) 100%);content: '';width: 160%;height:300rpx;position: absolute;left: -30%;top:0;border-radius: 0 0 50% 50%;z-index:1}\n.nav {width: 100%;position:relative;z-index:2}\n.nav>scroll-view {overflow: visible !important;padding-top:20rpx;padding-bottom:20rpx}\n.nav .f1 {flex-grow: 0;flex-shrink: 0;display:flex;align-items:center;color:#fff;position:relative;z-index:2}\n.nav .f1 .item{flex-grow: 0;flex-shrink: 0;width:150rpx;text-align:center;padding:16rpx 0;opacity: 0.6;}\n.nav .f1 .item .t1 {font-size:34rpx;font-weight:bold}\n.nav .f1 .item .t2 {font-size:24rpx}\r\n.nav .f1 .item .t3 {font-size:30rpx;}\r\n\n.nav .f1 .item.active {position: relative;color:#fff;opacity:1}\n\n.content{width:94%;margin-left:3%;position:relative;z-index:3}\n.data-empty{background:#fff;border-radius:16rpx}\n.content .item{width:100%;display:flex;padding: 20rpx;background:#fff;border-radius:16rpx;margin-bottom:20rpx}\n.item .f1{width:200rpx;height:200rpx;margin-right:20rpx;}\n.item .f2{position: relative; padding-right: 20rpx;flex:1;display:flex;flex-direction:column}\n.item .f2 .t1{font-size:28rpx;font-weight:bold;color: #222;margin-top: 2px;height:80rpx;overflow:hidden}\n.item .f2 .t2{width:100%;margin-top:12rpx;display:flex;align-items:center}\n.item .f2 .t2 .x2{padding-left:16rpx;font-size:24rpx;font-weight:bold}\n.item .f2 .t3{width:100%;margin-top:20rpx;display:flex;align-items:flex-end}\n.item .f2 .t3 .x1{font-size:32rpx;font-weight:bold}\n.item .f2 .t3 .x2{color:#999999;font-size:24rpx;text-decoration:line-through;padding-left:8rpx}\n.item .f2 .t3 .x3{position:absolute;bottom:0;right:0;border: 0;color: #fff;font-size:28rpx;padding:0 28rpx;height:54rpx;line-height:50rpx;border-radius:54rpx;margin:0}\n.item .f2 .t3 .x3.xx1{background:#888}\n.item .f2 .t3 .text1{margin-top: 40rpx;}\n.progress{width:240rpx;font-size:24rpx}\n.nomore-footer-tips{background:#fff!important}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839462545\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}