{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/product.vue?cc55", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/product.vue?f529", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/product.vue?7748", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/product.vue?4bf9", "uni-app:///activity/seckill/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/product.vue?0175", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/product.vue?2681"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "initdata", "pre_url", "seckill_status", "nowtime", "seckill_duration", "seckill_starttime", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "buydialogShow", "btntype", "isfavorite", "current", "isplay", "showcuxiaodialog", "business", "product", "cartnum", "commentlist", "commentcount", "cuxiaolist", "couponlist", "pagecontent", "gui<PERSON>", "gui<PERSON><PERSON>", "sysset", "seckillset", "title", "bboglist", "sharepic", "sharetypevisible", "showposter", "posterpic", "scrolltopshow", "kfurl", "showprice_dollar", "onLoad", "onPullDownRefresh", "onShareAppMessage", "pic", "onShareTimeline", "imageUrl", "query", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "uni", "getdjs", "swiper<PERSON><PERSON>e", "payvideo", "parsevideo", "buydialogChange", "addfavorite", "proid", "type", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata", "sharelink", "showsubqrcode", "closesubqrcode"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnLA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiRz1B;AACA;AAAA,eAEA;EAEAC;IACA;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAX;MAAAY;IAAA;EACA;EACAC;IACA;MAAAb;MAAAY;IAAA;IACA;IACA;MACAZ;MACAc;MACAC;IACA;EACA;EAEAC;IACAC;EACA;EAEAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UACA;QACA;QACA;QACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA,8CACAA,gDACAA;QACAA;QACAH;QACAM;UACAH;UACAA;QACA;QACAI;UACAxB;QACA;QACAoB;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAApB;UAAAY;QAAA;MACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;QAAA;QACAL;QACAA;QACAA;QACAA;MACA;QACA;UAAA;UACAA;UACA;QACA;UAAA;UACAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;MACA;IACA;IAEAM;MACA;MACAN;IACA;IAEAO;MACA;MACAH;IACA;IAEAI;MACA;MACAJ;IACA;IAEAK;MACA;QACA;MACA;MACA;IACA;IAEA;;IAEAC;MACA;MACA;MACAT;QAAAU;QAAAC;MAAA;QACA;UACAZ;QACA;QACAC;MACA;IACA;IAEAY;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;MACAf;MACAA;MACAC;MACAA;QAAAU;MAAA;QACAV;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IAEAgB;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACApB;MACA;MACA;QACAA;MACA;IACA;IACAqB;MACApB;MACA;IACA;IACAqB;MACA;MACAlB;QACAmB;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACAtB;UACA;QACA;MACA;IACA;IACAwB;MACA;IACA;IACAC;MACA;IACA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3jBA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/seckill/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/seckill/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=3839b51d&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/seckill/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=3839b51d&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    scrolltop: function () {\n      return import(\n        /* webpackChunkName: \"components/scrolltop/scrolltop\" */ \"@/components/scrolltop/scrolltop.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.bboglist.length : null\n  var g1 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload &&\n    _vm.seckillset.showcommission == 1 &&\n    _vm.product.commission > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.seckillset.showcommission == 1 &&\n    _vm.product.commission > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    _vm.seckillset.showcommission == 1 &&\n    _vm.product.commission > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m6 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m7 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var g2 = _vm.isload\n    ? _vm.cuxiaolist.length > 0 ||\n      _vm.couponlist.length > 0 ||\n      _vm.product.discount_tips != \"\"\n    : null\n  var g3 = _vm.isload && g2 ? _vm.cuxiaolist.length : null\n  var l0 =\n    _vm.isload && g2 && g3 > 0\n      ? _vm.__map(_vm.cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m8 = _vm.t(\"color1rgb\")\n          var m9 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m8: m8,\n            m9: m9,\n          }\n        })\n      : null\n  var g4 = _vm.isload && g2 ? _vm.couponlist.length : null\n  var l1 =\n    _vm.isload && g2 && g4 > 0\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m10 = _vm.t(\"color1rgb\")\n          var m11 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m10: m10,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 =\n    _vm.isload && _vm.seckillset.comment == 1 && _vm.commentcount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g5 =\n    _vm.isload && _vm.seckillset.comment == 1 && _vm.commentcount > 0\n      ? _vm.commentlist.length\n      : null\n  var m13 = _vm.isload && _vm.seckillset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m14 = _vm.isload && _vm.seckillset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m15 =\n    _vm.isload && _vm.product.status == 1 && _vm.seckill_status == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m16 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m17 =\n    _vm.isload && _vm.sharetypevisible && !(m16 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m18 =\n    _vm.isload && _vm.sharetypevisible && !(m16 == \"app\") && !(m17 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        g4: g4,\n        l1: l1,\n        m12: m12,\n        g5: g5,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<block v-if=\"sysset.showgzts\">\r\n\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\r\n\t\t\t<view class=\"follow_topbar\">\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"sysset.logo\"/></view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\r\n\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\r\n\t\t\t\t<view class=\"qrcodebox\">\r\n\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\"/>\r\n\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t</block>\r\n\t\t<view style=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\" v-if=\"bboglist.length>0\">\r\n\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\" :vertical=\"true\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\" :data-url=\"'product?id=' + item.proid\" class=\"flex-y-center\">\r\n\t\t\t\t\t<image :src=\"item.headimg\" style=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\"/>\r\n\t\t\t\t\t<div style=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">{{item.nickname}} {{item.showtime}}购买了该商品</div>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<view class=\"swiper-container\" v-if=\"isplay==0\">\r\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\">\r\n\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"imageCount\">{{current+1}}/{{(product.pics).length}}</view>\r\n\t\t\t<view v-if=\"product.video\" class=\"provideo\" @tap=\"payvideo\"><image :src=\"pre_url+'/static/img/video.png'\"/><view class=\"txt\">{{product.video_duration}}</view></view>\r\n\t\t</view>\r\n\t\t<view class=\"videobox\" v-if=\"isplay==1\">\r\n\t\t\t<video autoplay=\"true\" class=\"video\" id=\"video\" :src=\"product.video\"></video>\r\n\t\t\t<view class=\"parsevideo\" @tap=\"parsevideo\">退出播放</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"seckill_title\">\r\n\t\t\t<image :src=\"pre_url+'/static/img/tjms.png'\" class=\"f0\"/>\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t<block v-if=\"showprice_dollar && product.usd_sellprice\">\r\n\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{product.usd_sellprice}}\r\n\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{product.sell_price}}\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{product.sell_price}}\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<text class=\"x2\">￥{{product.market_price}}</text>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t2\" v-if=\"seckill_status==0\">秒杀未开始</view>\r\n\t\t\t\t<view class=\"t2\" v-if=\"seckill_status==1\">火爆抢购中</view>\r\n\t\t\t\t<view class=\"t2\" v-if=\"seckill_status==2\">秒杀已结束</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f3\" v-if=\"seckill_status==0 || seckill_status==1\">\r\n\t\t\t\t<view class=\"t1\">距秒杀{{seckill_status==0?'开始':'结束'}}还剩</view>\r\n\t\t\t\t<view class=\"t2\" id=\"djstime\"><text class=\"djsspan\">{{djshour}}</text> : <text class=\"djsspan\">{{djsmin}}</text> : <text class=\"djsspan\">{{djssec}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"header\"> \r\n\t\t\t<view class=\"price_share\">\r\n\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t<block v-if=\"showprice_dollar && product.usd_sellprice\">\r\n\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{product.usd_sellprice}}\r\n\t\t\t\t\t\t\t<text style=\"font-size: 32rpx;\"><text style=\"font-size:24rpx\">￥</text>{{product.sell_price}}</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">￥</text>{{product.sell_price}}\r\n\t\t\t\t\t\t</block>\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t<view class=\"sales_stock\">\r\n\t\t\t\t<view class=\"f1\">销量：{{product.sales}} </view>\r\n\t\t\t\t<view class=\"f2\">库存：{{product.stock}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"seckillset.showcommission==1 && product.commission > 0\">分享好友购买预计可得{{t('佣金')}}：<text style=\"font-weight:bold;padding:0 2px\">{{product.commission}}</text>元</view>\r\n\t\t</view>\r\n\t\t<view class=\"choose\" @tap=\"buydialogChange\" data-btntype=\"2\">\r\n\t\t\t<view class=\"f1 flex1\">请选择商品规格及数量</view>\r\n\t\t\t<image class=\"f2\" :src=\"pre_url+'/static/img/arrowright.png'\" />\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"cuxiaodiv\" v-if=\"product.givescore > 0\">\r\n\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t<view class=\"f0\">送{{t('积分')}}</view>\r\n\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('积分')}}{{product.givescore}}个</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"cuxiaodiv\" v-if=\"cuxiaolist.length>0 || couponlist.length>0 || product.discount_tips!=''\">\r\n\t\t\t<view class=\"cuxiaopoint\" v-if=\"cuxiaolist.length>0\">\r\n\t\t\t\t<view class=\"f0\">促销</view>\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\">{{item.tip}}</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/arrowright.png'\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cuxiaopoint\" v-if=\"product.discount_tips!=''\">\r\n\t\t\t\t<view class=\"f0\">折扣</view>\r\n\t\t\t\t<view class=\"f1\" style=\"padding-left:10rpx\">{{product.discount_tips}}</view>\r\n\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/pagesExt/my/levelinfo\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cuxiaopoint\" v-if=\"couponlist.length>0\">\r\n\t\t\t\t<view class=\"f0\">优惠</view>\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/arrow-point.png'\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"showcuxiaodialog\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidecuxiaodetail\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidecuxiaodetail\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"service-item\">\r\n\t\t\t\t\t\t\t<view class=\"suffix\">\r\n\t\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text> <text style=\"color:#333;margin-left:20rpx\">{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<couponlist :couponlist=\"couponlist\" @getcoupon=\"getcoupon\"></couponlist>\r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"commentbox\" v-if=\"seckillset.comment==1 && commentcount > 0\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\r\n\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评度 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"comment\">\r\n\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\r\n\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\r\n\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"pre_url+'/static/img/star' + (commentlist[0].score>item2?'2native':'') + '.png'\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\r\n\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"shop\" v-if=\"seckillset.showjd==1\">\r\n\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"p4\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\" data-opentype=\"reLaunch\">进入店铺</button>\r\n\t\t</view>\r\n\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t<view class=\"detail\">\r\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\r\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"item flex1\" @tap=\"shareClick\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/share2.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">分享</view> \r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\">\r\n\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\" v-if=\"seckill_status==1\">立即抢购</view>\r\n\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" style=\"background:#ccc\" v-else>秒杀未开始</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<scrolltop :isshow=\"scrolltopshow\"></scrolltop>\r\n\t\t\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"product.id\" :btntype=\"btntype\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\" controller=\"ApiSeckill\"></buydialog>\r\n\t\t<scrolltop :isshow=\"scrolltopshow\"></scrolltop>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n  \r\n\tdata() {\r\n\t\treturn {\r\n \r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tinitdata:{},\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tseckill_status:1,\r\n\t\t\tnowtime:0,\r\n\t\t\tseckill_duration:0,\r\n\t\t\tseckill_starttime:0,\r\n\t\t\tdjshour:'00',\r\n\t\t\tdjsmin:'00',\r\n\t\t\tdjssec:'00',\r\n\t\t\tbuydialogShow: false,\r\n\t\t\tbtntype:1,\r\n\t\t\tisfavorite: false,\r\n\t\t\tcurrent: 0,\r\n\t\t\tisplay: 0,\r\n\t\t\tshowcuxiaodialog: false,\r\n\t\t\tbusiness: \"\",\r\n\t\t\tproduct: [],\r\n\t\t\tcartnum: \"\",\r\n\t\t\tcommentlist: \"\",\r\n\t\t\tcommentcount: \"\",\r\n\t\t\tcuxiaolist: \"\",\r\n\t\t\tcouponlist: \"\",\r\n\t\t\tpagecontent: \"\",\r\n\t\t\tguigelist: \"\",\r\n\t\t\tguigedata: \"\",\r\n\t\t\tsysset:{},\r\n\t\t\tseckillset: \"\",\r\n\t\t\ttitle: \"\",\r\n\t\t\tbboglist: \"\",\r\n\t\t\tsharepic: \"\",\r\n\t\t\tsharetypevisible: false,\r\n\t\t\tshowposter: false,\r\n\t\t\tposterpic: \"\",\r\n\t\t\tscrolltopshow: false,\r\n\t\t\tkfurl:'',\r\n\t\t\tshowprice_dollar:false\r\n\t\t};\r\n\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiSeckill/product', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar sysset = res.sysset;\r\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.product = product;\r\n\t\t\t\tthat.cartnum = res.cartnum;\r\n\t\t\t\tthat.commentlist = res.commentlist;\r\n\t\t\t\tthat.commentcount = res.commentcount;\r\n\t\t\t\tthat.cuxiaolist = res.cuxiaolist;\r\n\t\t\t\tthat.couponlist = res.couponlist;\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.sysset = sysset;\r\n\t\t\t\tthat.seckillset = res.seckillset;\r\n\t\t\t\tthat.title = product.name;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.oglist = res.oglist;\r\n\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\tthat.seckill_duration = res.seckill_duration,\r\n\t\t\t\tthat.seckill_starttime = res.product.starttime,\r\n\t\t\t\tthat.showprice_dollar = res.showprice_dollar\r\n\t\t\t\tthat.getdjs();\r\n\t\t\t\tclearInterval(interval);\r\n\t\t\t\tinterval = setInterval(function(){\r\n\t\t\t\t\tthat.nowtime = that.nowtime+1;\r\n\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t},1000)\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: product.name\r\n\t\t\t\t});\r\n\t\t\t\tthat.kfurl = '/pages/kefu/index?bid='+res.product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded({title:res.product.name,pic:res.product.pic});\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetdjs:function(){\r\n\t\t\tvar that = this\r\n\t\t\tvar nowtime = that.nowtime*1;\r\n\t\t\tvar seckill_duration = that.seckill_duration*1;\r\n\t\t\tvar seckill_starttime = that.seckill_starttime*1;\r\n\t\t\tvar seckill_endtime = that.seckill_starttime + seckill_duration * 3600;\r\n\t\t\tif(seckill_endtime < nowtime){ //已结束\r\n\t\t\t\tthat.seckill_status = 2\r\n\t\t\t\tthat.djshour = '00';\r\n\t\t\t\tthat.djsmin = '00';\r\n\t\t\t\tthat.djssec = '00';\r\n\t\t\t}else{\r\n\t\t\t\tif(seckill_starttime > nowtime){ //未开始\r\n\t\t\t\t\tthat.seckill_status = 0\r\n\t\t\t\t\tvar totalsec = seckill_starttime - nowtime;\r\n\t\t\t\t}else{ //进行中\r\n\t\t\t\t\tthat.seckill_status = 1\r\n\t\t\t\t\tvar totalsec = seckill_endtime - nowtime;\r\n\t\t\t\t}\r\n\t\t\t\tvar houer = Math.floor(totalsec/3600);\r\n\t\t\t\tvar min = Math.floor((totalsec - houer *3600)/60);\r\n\t\t\t\tvar sec = totalsec - houer*3600 - min*60\r\n\t\t\t\tvar djs = (houer<10?'0':'')+houer+'时'+(min<10?'0':'')+min+'分'+(sec<10?'0':'')+sec+'秒';\r\n\t\t\t\tvar djshour = (houer<10?'0':'')+houer\r\n\t\t\t\tvar djsmin = (min<10?'0':'')+min\r\n\t\t\t\tvar djssec = (sec<10?'0':'')+sec\r\n\t\t\t\tthat.djshour = djshour;\r\n\t\t\t\tthat.djsmin = djsmin;\r\n\t\t\t\tthat.djssec = djssec;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tswiperChange: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.current = e.detail.current\r\n\t\t},\r\n\r\n\t\tpayvideo: function () {\r\n\t\t\tthis.isplay = 1;\r\n\t\t\tuni.createVideoContext('video').play();\r\n\t\t},\r\n\r\n\t\tparsevideo: function () {\r\n\t\t\tthis.isplay = 0;\r\n\t\t\tuni.createVideoContext('video').stop();\r\n\t\t},\r\n\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\r\n\t\t//收藏操作\r\n\r\n\t\taddfavorite: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tapp.post('ApiSeckill/addfavorite', {proid: proid,type: 'seckill'}, function (data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t}\r\n\t\t\t\tapp.success(data.msg);\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\tshareClick: function () {\r\n\t\t\tthis.sharetypevisible = true;\r\n\t\t},\r\n\r\n\t\thandleClickMask: function () {\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshowPoster: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.showposter = true;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tapp.showLoading('生成海报中');\r\n\t\t\tapp.post('ApiSeckill/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\tposterDialogClose: function () {\r\n\t\t\tthis.showposter = false;\r\n\t\t},\r\n\r\n\t\tshowcuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = true;\r\n\t\t},\r\n\r\n\t\thidecuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = false\r\n\t\t},\r\n\t\tgetcoupon:function(){\r\n\t\t\tthis.showcuxiaodialog = false;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar scrollY = e.scrollTop;     \r\n\t\t\tif (scrollY > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollY < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t},\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/seckill/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/activity/seckill/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t},\r\n\t\tshowsubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t},\r\n\t\tclosesubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t},\r\n\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\n.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}\r\n.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}\r\n.follow_topbar .headimg image {height:64rpx; width:64rpx;}\r\n.follow_topbar .info {height:56rpx; padding:16rpx 0;}\r\n.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}\r\n.follow_topbar .info {height:80rpx; float:left;}\r\n.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}\r\n.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.qrcodebox .img{width:400rpx;height:400rpx}\r\n.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\r\n.provideo image{width:50rpx;height:50rpx;}\r\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\r\n\r\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\r\n.videobox .video{width:100%;height:650rpx;}\r\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}\r\n\r\n.seckill_title{ width:100%;height:110rpx;background: linear-gradient(90deg, #FF3143 0%, #FD6647 100%);display:flex;align-items:center;}\r\n.seckill_title .f0{width:88rpx;height:88rpx;margin-left:20rpx}\r\n.seckill_title .f1{flex:1;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.seckill_title .f1 .t1{font-size:40rpx;color:#fff;line-height:50rpx}\r\n.seckill_title .f1 .t1 .x2{padding-left:8rpx;font-size:26rpx;color:#fff;text-decoration:line-through}\r\n.seckill_title .f1 .t2{color:#fff;font-size:22rpx}\r\n.seckill_title .f3{width:250rpx;height:110rpx;background:#FFDBDF;color:#333;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.seckill_title .f3 .t2{color:#FF3143}\r\n.seckill_title .djsspan{font-size:22rpx;border-radius:8rpx;background:#FF3143;color:#fff;text-align:center;padding:4rpx 8rpx;margin:0 4rpx}\r\n\r\n.header {width: 100%;padding: 20rpx 3%;background: #fff;}\r\n.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}\r\n.header .price_share .price{display:flex;align-items:flex-end}\r\n.header .price_share .price .f1{font-size:50rpx;color:#51B539;font-weight:bold}\r\n.header .price_share .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}\r\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\r\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\r\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\r\n.header .sales_stock{display:flex;justify-content:space-between;height:60rpx;line-height:60rpx;margin-top:30rpx;font-size:24rpx;color:#777777}\r\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\r\n\r\n.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; }\r\n.choose .f2{ width: 32rpx; height: 32rpx;}\r\n\r\n.cuxiaodiv{background:#fff;margin-top:20rpx;padding: 0 3%;}\r\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.cuxiaopoint .f0{color:#777777;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\r\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\r\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\r\n.cuxiaopoint .f1 .t1{padding:0 4px}\r\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\r\n.cuxiaodiv .cuxiaopoint{border-bottom:1px solid #E6E6E6;}\r\n.cuxiaodiv .cuxiaopoint:last-child{border-bottom:0}\r\n\r\n.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}\r\n.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}\r\n.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}\r\n.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}\r\n.popup__title-text{font-size:32rpx}\r\n.popup__close{position:absolute;top:34rpx;right:34rpx}\r\n.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}\r\n.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.service-item .prefix{padding-top: 2px;}\r\n.service-item .suffix{padding-left: 10rpx;}\r\n.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.commentbox{width:100%;background:#fff;padding:0 3%;margin-top:20rpx}\r\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\r\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\r\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\r\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\r\n\r\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\r\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.comment .item .f2 .t2{display:flex;width:100%}\r\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\r\n\r\n.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}\r\n.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}\r\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:80rpx;position:relative}\r\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\r\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\r\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839366372\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}