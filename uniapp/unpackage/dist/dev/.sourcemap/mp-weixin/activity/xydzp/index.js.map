{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/index.vue?49ab", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/index.vue?66a5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/index.vue?ef89", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/index.vue?ede1", "uni-app:///activity/xydzp/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/index.vue?3892", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/index.vue?2048"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "speed", "speedDot", "dotColor", "dotColor_1", "dotColor_2", "jxshow", "showmaskrule", "info", "member", "remaindaytimes", "remaintimes", "zj<PERSON>", "jxarr", "latitude", "longitude", "angel<PERSON>o", "jxmc", "jx", "backcolor", "showqrpopup", "showdoneqrcode", "isinfo", "maskshow", "formdata", "picker_tmer", "picker_date", "picker_region", "items", "showdzp", "onLoad", "app", "uni", "url", "method", "header", "success", "that", "onPullDownRefresh", "onShareAppMessage", "title", "desc", "link", "pic", "callback", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "id", "res", "console", "getcode", "choujiang_id", "code", "showqrmodal", "hideqrmodal", "sharecallback", "hid", "setTimeout", "downloadFile", "complete", "changemaskrule", "changemask", "changemaskshow", "time_editorBindPicker<PERSON><PERSON>e", "selector_editorBindPickerChange", "date_editorB<PERSON><PERSON><PERSON><PERSON><PERSON>e", "region_editorBindPickerChange", "rollStart", "bool", "op", "showCancel", "content", "angel", "clearInterval", "timeStamp", "nonceStr", "package", "signType", "paySign", "fail", "count", "draw<PERSON><PERSON>vas", "ctx", "startAngel", "dotStart", "dot_inter", "times", "formsub", "subdata", "formcontent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgKv1B;AACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IAEAC;MACA;MACAC;QACAC;QACAtC;QACAuC;QACAC;UAAA;QAAA;QACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAAP;MAAA;IAAA;EACA;EACAQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MAAAL;MAAAC;MAAAC;MAAAC;MAAAC;QAAAP;MAAA;IAAA;IACA;IACA;MACAG;MACAM;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAZ;MACAN;QAAAmB;MAAA;QACAb;QACA;UACAN;UACA;QACA;QACAoB;QACAd;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;QACA;QAEAL;UACAQ;QACA;QACA;UACAH;QACA;QACAA;QACA;UACAN;YACA;YACA;YACAM;YACAA;YACAe;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAf;UAAAG;UAAAC;UAAAC;UAAAC;UAAAC;YAAAP;UAAA;QAAA;MACA;IACA;IACAgB;MACA;MACAtB;QACAuB;QACAC;MACA;QACA;UACAxB;QACA;QACAM;MACA;IACA;IACAmB;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA3B;QAAA4B;MAAA;QACA;UACAC;YACAvB;UACA;QACA;QAAA;MAEA;IACA;IACAwB;MACA;MACA;QACA7B;UACAC;UACAG;YACA;cACAvB;cACAwB;YACA;UACA;UACAyB;YACA;cACAzB;YACA;cACAA;YACA;UACA;QACA;MACA;QACA;UACAA;QACA;UACAA;QACA;MACA;IACA;IACA0B;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA5B;MACAA;IACA;IACA6B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACAvC;UAAA;QACA;QACA;UACAA;UAAA;QACA;QACAM;QACAA;QAAA;MACA;MACA;MACAkC;MACAxC;QAAAmB;QAAAsB;QAAAzD;QAAAD;MAAA;QACA;UACA;UACAkB;YACAQ;YACAiC;YACAC;YACAtC;UACA;UACAmC;UACA;QACA;UACA;UACA;QACA;QACAI;QAKA;;QAEA;QACA;QACA;UACAtC;UACAuC;UACAvC;UAEA;YACAe;YACAmB;YACAK;YAUAvC;YACAA;YACAA;YACAA;YAEA;cACAL;gBACA6C;gBACA;gBACAC;gBACA;gBACAC;gBACA;gBACAC;gBACA;gBACAC;gBACA;gBACA7C;kBACAgB;gBACA;gBACA8B;kBACA9B;gBACA;gBACAU;kBACAV;gBACA;cACA;YACA;UACA;UAEA+B;UAOA;YACAA;UACA;QAEA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;;MAEAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACA;MACA;QACAA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAC;MACA;MAEA;MAEAD;MAEA;QACAA;;QAEAA;QACAA;QACAA;QACAA;QACAA;MACA;;MAEA;QACAA;MACA;MAEA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;MAEAA;IACA;IACAE;MACA;MACA;MACAX;MACAvC;MACAmD;QACA;UACA;QACA;UACA;QACA;QAEAC;QACApD;QACAA;MACA;IACA;IACAqD;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;UACA3D;UACA;QACA;QAEA;UACA;YACA4D;UACA;YACAA;UACA;QACA;QAEA;UACAA;QACA;QAEA;QACAnE;MACA;MAGAO;QACA4B;QACAiC;MACA;QACA;UACA7D;QACA;UACAM;UACAN;UACAM;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtnBA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/xydzp/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/xydzp/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=767dcaca&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/xydzp/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=767dcaca&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.info.use_type == 1 && _vm.info.usescore > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m1 =\n    _vm.isload && _vm.info.use_type == 1 && _vm.info.usescore > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m2 =\n    _vm.isload && _vm.info.use_type == 2 && _vm.info.usemoney > 0\n      ? _vm.t(\"余额\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"pageback\" :style=\"{ background:backcolor==''?'#f58d40':backcolor}\"></view>\n\t\t<view class=\"wrap\" :style=\"'background-image:url(' + info.bgpic + ');background-size:100% 100%;'\">\n\t\t\t<view class=\"header clearfix\">\n\t\t\t\t<view class=\"rule\" @tap=\"changemaskrule\">活动规则</view>\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'myprize?hid=' + info.id\" class=\"my\">我的奖品</view>\n\t\t\t</view>\n\t\t\t<view class=\"title\" :style=\"'background-image:url(' + info.banner + ');background-size:100% 100%;'\"></view>\n\t\t\t<view class=\"canvas\" :hidden=\"showmaskrule || jxshow || showqrpopup\">\n\t\t\t\t<canvas canvas-id=\"roulette\" style=\" width: 650rpx; height: 650rpx;\" v-if=\"showdzp\">\n\t\t\t\t\t<!-- #ifndef MP-TOUTIAO -->\n\t\t\t\t\t<cover-image :src=\"pre_url + '/static/img/xydzp_start.png'\" class=\"start\" @tap=\"rollStart\"></cover-image>\n\t\t\t\t\t<!-- #endif -->\n\t\t\t\t</canvas>\n\t\t\t\t<!-- #ifdef MP-TOUTIAO -->\n\t\t\t\t<image :src=\"pre_url + '/static/img/xydzp_start.png'\" class=\"start\" @tap=\"rollStart\"></image>\n\t\t\t\t<!-- #endif -->\n\t\t\t</view>\n\t\t\t<view class=\"border\" v-if=\"info.use_type != 2\">您今日还有 <text id=\"change\">{{remaindaytimes}}</text> 次抽奖机会</view>\n\t\t\t<view class=\"border2\" v-if=\"info.use_type == 1 && info.usescore>0\"><text v-if=\"!info.is_tr\">每次</text><text v-else>本次</text>抽奖将消耗 <text>{{info.usescore}}</text> {{t('积分')}}，您共有 <text id=\"myscore\">{{member.score}}</text> {{t('积分')}}</view>\n\t\t\t<view class=\"border2\" v-if=\"info.use_type == 2 && info.usemoney>0\">每次抽奖将消耗 <text>{{t('余额')}}</text>{{info.usemoney}}元 ，您共有 <text id=\"mymoney\">{{member.money}}</text> 元</view>\n            <!--滚动信息-->\n\t\t\t<view class=\"scroll\">\n\t\t\t\t<view class=\"p\" :style=\"'background-image: url('+pre_url+'/static/img/dzp/list.png);background-size:100% 100%;'\"></view>\n\t\t\t\t<view class=\"sideBox\">\n\t\t\t\t\t<swiper class=\"bd\" autoplay=\"true\" :indicator-dots=\"false\" current=\"0\" :vertical=\"true\" circular=\"true\">\n\t\t\t\t\t\t<swiper-item v-for=\"(item, index) in zjlist\" :key=\"index\" class=\"sitem\" v-if=\"index%2==0\">\n\t\t\t\t\t\t\t<view>恭喜{{item.nickname}} 获得<text class=\"info\">{{item.jxmc}}</text></view>\n\t\t\t\t\t\t\t<view v-if=\"zjlist[index+1]\">恭喜{{zjlist[index+1].nickname}} 获得<text class=\"info\">{{zjlist[index+1].jxmc}}</text></view>\n\t\t\t\t\t\t</swiper-item>\n\t\t\t\t\t</swiper>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view id=\"mask-rule\" v-if=\"showmaskrule\">\n\t\t\t\t<view class=\"box-rule\">\n\t\t\t\t\t<view class=\"h2\">活动规则说明</view>\n\t\t\t\t\t<view id=\"close-rule\" :style=\"'background-image:url('+pre_url+'/static/img/dzp/close.png);background-size:100%'\" @tap=\"changemaskrule\"></view>\n\t\t\t\t\t<view class=\"con\">\n\t\t\t\t\t\t<view class=\"text\">\n\t\t\t\t\t\t\t<text decode=\"true\" space=\"true\">{{info.guize}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!--中奖提示-->\n\t\t\t<view id=\"mask\" v-if=\"jxshow && jx>0\">\n\t\t\t\t<view class=\"blin\"></view>\n\t\t\t\t<view class=\"caidai\" :style=\"'background-image: url('+pre_url+'/static/img/dzp/dianzhui.png);'\"></view>\n\t\t\t\t<view class=\"winning reback\" :style=\"'background:url(' + pre_url + '/static/img/dzp/bg2.png) no-repeat;background-size:100% 100%;'\">\n\t\t\t\t\t<view class=\"p\">\n\t\t\t\t\t\t<view>恭喜您抽中了</view>\n\t\t\t\t\t\t<view class=\"b\" id=\"text1\">{{jxmc}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view @tap=\"changemask\" class=\"btn\">确定</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!--未中奖提示-->\n\t\t\t<view id=\"mask2\" v-if=\"jxshow && jx==0\">\n\t\t\t\t<view class=\"blin\"></view>\n\t\t\t\t<view class=\"caidai\" :style=\"'background-image: url('+pre_url+'/static/img/dzp/dianzhui.png);'\"></view>\n\t\t\t\t<view class=\"winning reback\" :style=\"'background:url(' + pre_url + '/static/img/dzp/bg3.png) no-repeat;background-size:100% 100%;'\">\n\t\t\t\t\t<view class=\"p\">\n\t\t\t\t\t\t<view class=\"b text2\" id=\"text2\">{{jxmc}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view @tap=\"changemask\" class=\"btn\">确定</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-if=\"showqrpopup\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideqrmodal\"></view>\n\t\t\t\t<view class=\"popup__modal\" style=\"\">\n\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t<view><image :data-url=\"info.qrcode\" :src=\"info.qrcode\" @tap=\"previewImage\" ></image></view>\n\t\t\t\t\t\t<view class=\"txt\" v-if=\"info.qrcode_tip\">{{info.qrcode_tip}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\r\n\t\t\t\r\n\t\t\t<view id=\"mask-rule1\" v-if=\"maskshow && !formdata\">\r\n\t\t\t\t<view class=\"box-rule\" style=\"height:640rpx\">\r\n\t\t\t\t\t<view class=\"h2\">请填写兑奖信息</view>\r\n\t\t\t\t\t<view id=\"close-rule1\" :style=\"'background: no-repeat center / contain;background-image: url('+pre_url+'/static/img/dzp/close.png);'\" @tap=\"changemaskshow\"></view>\r\n\t\t\t\t\t<view class=\"con\">\r\n\t\t\t\t\t\t<form class @submit=\"formsub\">\r\n\t\t\t\t\t\t<view class=\"pay-form\" style=\"margin-top:0.18rem\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, idx) in info.formcontent\" :key=\"idx\" class=\"item flex-y-center\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.val1}}：</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2 flex flex1\">\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form' + idx\" class=\"input\" :placeholder=\"item.val2\"></input>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t\t\t<textarea :name=\"'form' + idx\" class=\"textarea\" :placeholder=\"item.val2\"></textarea>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form' + idx\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1, index) in item.val2\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<radio :value=\"item1\"></radio>{{item1}}\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form' + idx\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1, index) in item.val2\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<checkbox :value=\"item1\" class=\"xyy-zu\"></checkbox>{{item1}}\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='switch'\">\r\n\t\t\t\t\t\t\t\t\t\t<switch class=\"xyy-zu\" value=\"1\" :name=\"'form' + idx\"></switch>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t\t\t<picker mode=\"selector\" :name=\"'form' + idx\" class=\"xyy-pic\" :range=\"item.val2\" @change=\"selector_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"item.val2[selectIndex]\"> {{item.val2[selectIndex]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t\t\t<picker mode=\"time\" :name=\"'form' + idx\" class=\"xyy-pic\" @change=\"time_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"picker_tmer\">{{picker_tmer}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-else>选择时间</view>\r\n\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t\t\t<picker mode=\"date\" :name=\"'form' + idx\" class=\"xyy-pic\" @change=\"date_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"picker_date\"> {{picker_date}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-else>选择日期</view>\r\n\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='region'\">\r\n\t\t\t\t\t\t\t\t\t\t<uni-data-picker style=\"color: #333;\" class=\"flex1\" :localdata=\"items\" popup-title=\"请选择省市区\"  @change=\"region_editorBindPickerChange\"\t:data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" :value=\"picker_region\" placeholder=\"请选择省市区\" placeholder-style=\"color:#fff;font-size:28rpx\" style=\"border: none;font-size:28rpx;padding: 0rpx;color:#fff;\"/>\r\n\t\t\t\t\t\t\t\t\t\t</uni-data-picker>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view >\r\n\t\t\t\t\t\t\t\t<button class=\"subbtn\" form-type=\"submit\">确 定</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</form>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar dot_inter, bool;\nvar app = getApp();\nvar windowWidth = uni.getSystemInfoSync().windowWidth;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      speed: 10,\n\n      /**转盘速度 */\n      speedDot: 1000,\n\n      /**点切换速度 */\n      dotColor: ['#ffffff', '#FCDF00'],\n      dotColor_1: ['#ffffff', '#FCDF00'],\n      dotColor_2: ['#FCDF00', '#ffffff'],\n      jxshow: false,\n      showmaskrule: false,\n\t\t\tinfo:{},\n\t\t\tmember:{},\n\t\t\tremaindaytimes:0,\n\t\t\tremaintimes:0,\n\t\t\tzjlist:[],\n      jxarr: \"\",\n      latitude: \"\",\n      longitude: \"\",\n      angelTo: \"\",\n      jxmc: \"\",\n      jx: \"\",\n\t\t\tbackcolor: \"\",\n\t\t\tshowqrpopup:false,\n\t\t\tshowdoneqrcode:false,\r\n\t\t\tisinfo:false,\r\n\t\t\tmaskshow: false,\r\n\t\t\tformdata: \"\",\r\n\t\t\tpicker_tmer:'',\r\n\t\t\tpicker_date:'',\r\n\t\t\tpicker_region:'',\r\n\t\t\titems: [],\r\n\t\t\tshowdzp:true\n    };\n  },\n\n  onLoad: function (opt) {\r\n\t\tvar that=this\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt.code){\r\n\t\t\tthis.getcode();\r\n\t\t}else{\r\n\t\t\tthis.getdata();\r\n\t\t}\t\t\n\t\t\r\n\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\r\n\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\r\n\t\t\tuni.request({\r\n\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\r\n\t\t\t\tdata: {},\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onShareAppMessage: function () {\n\t\tvar that = this;\n\t\tvar title = that.info.name;\n\t\tif (that.info.sharetitle) title = that.info.sharetitle;\n\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : '';\n\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\n\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\n\t\treturn this._sharewx({title:title,desc:sharedesc,link:sharelink,pic:sharepic,callback:function(){that.sharecallback();}});\n  },\n\tonShareTimeline:function(){\n\t\tvar that = this;\n\t\tvar title = that.info.name;\n\t\tif (that.info.sharetitle) title = that.info.sharetitle;\n\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : '';\n\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\n\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\n\t\tvar sharewxdata = this._sharewx({title:title,desc:sharedesc,link:sharelink,pic:sharepic,callback:function(){that.sharecallback();}});\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tvar id = that.opt.id;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiChoujiang/index', {id: id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif(res.status == 0){\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\r\n\t\t\t\tres.info.formcontent = JSON.parse(res.info.formcontent);\n\t\t\t\tthat.info = res.info;\n\t\t\t\tthat.jxarr = res.jxarr;\n\t\t\t\tthat.member = res.member;\n\t\t\t\tthat.remaindaytimes = res.remaindaytimes;\n\t\t\t\tthat.remaintimes = res.remaintimes;\n\t\t\t\tthat.zjlist = res.zjlist;\n\t\t\t\tthat.backcolor = res.info.bgcolor;\r\n\t\t\t\tthat.isinfo = res.isinfo\r\n\t\t\t\tif(\tthat.isinfo && res.record && res.record.formdata){\r\n\t\t\t\t\t\tthat.formdata = res.record.formdata\r\n\t\t\t\t}\r\n\t\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: res.info.name\n\t\t\t\t});\n\t\t\t\tif(that.info.qrcode){\n\t\t\t\t\tthat.showqrpopup = true\n\t\t\t\t}\n\t\t\t\tthat.downloadFile(res.jxarr, 0);\n\t\t\t\tif (that.info.fanwei == 1) {\n\t\t\t\t\tapp.getLocation(function (res) {\n\t\t\t\t\t\tvar latitude = res.latitude;\n\t\t\t\t\t\tvar longitude = res.longitude;\n\t\t\t\t\t\tthat.latitude = latitude;\n\t\t\t\t\t\tthat.longitude = longitude;\n\t\t\t\t\t\tconsole.log(longitude);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tvar title = that.info.name;\n\t\t\t\tif (that.info.sharetitle) title = that.info.sharetitle;\n\t\t\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : '';\n\t\t\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\n\t\t\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\n\t\t\t\tthat.loaded({title:title,desc:sharedesc,link:sharelink,pic:sharepic,callback:function(){that.sharecallback();}});\n\t\t\t});\n\t\t},\r\n\t\tgetcode: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.get('ApiChoujiang/qrcode_addtimes', {\r\n\t\t\t\tchoujiang_id: that.opt.id,\r\n\t\t\t\tcode: that.opt.code,\r\n\t\t\t}, function(res) {\r\n\t\t\t\t\tif (res.status != 1 ) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\tthat.getdata();\r\n\t\t\t});\r\n\t\t},\n\t\tshowqrmodal:function(){\n\t\t\tif(this.info.qrcode){\n\t\t\t\tthis.showqrpopup = true;\n\t\t\t}\n\t\t\tthis.showdoneqrcode = true;\n\t\t},\n\t\thideqrmodal:function(){\n\t\t\tthis.showqrpopup = false;\n\t\t},\n\t\tsharecallback:function(){\n\t\t\tvar that = this;\n\t\t\tapp.post(\"ApiChoujiang/share\", {hid: that.info.id}, function (res) {\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else if (res.status == 0) {//dialog(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    downloadFile: function (jxarr, i) {\n      var that = this;\n      if (jxarr[i].pic) {\n        uni.downloadFile({\n          url: jxarr[i].pic,\n          success: function (res) {\n            if (res.tempFilePath) {\n              jxarr[i].pic = res.tempFilePath;\n              that.jxarr = jxarr;\n            }\n          },\n          complete: function () {\n            if (jxarr.length > i + 1) {\n              that.downloadFile(jxarr, i + 1);\n            } else {\n              that.dotStart();\n            }\n          }\n        });\n      } else {\n        if (jxarr.length > i + 1) {\n          that.downloadFile(jxarr, i + 1);\n        } else {\n          that.dotStart();\n        }\n      }\n    },\n    changemaskrule: function () {\n      this.showmaskrule = !this.showmaskrule\n    },\n    changemask: function () {\n      this.jxshow = !this.jxshow;\n      this.getdata();\n    },\r\n\t\tchangemaskshow: function () {\r\n\t\t  var that = this;\r\n\t\t  that.maskshow = !that.maskshow;\r\n\t\t\tthat.showdzp=true\r\n\t\t},\r\n\t\ttime_editorBindPickerChange:function(e){\r\n\t\t\tthis.picker_tmer = e.detail.value\r\n\t\t},\r\n\t\tselector_editorBindPickerChange:function(e){\r\n\t\t\tthis.selectIndex = e.detail.value;\r\n\t\t},\r\n\t\tdate_editorBindPickerChange:function(e){\r\n\t\t\tthis.picker_date = e.detail.value;\r\n\t\t},\r\n\t\tregion_editorBindPickerChange:function(e){\r\n\t\t\tconst value = e.detail.value\r\n\t\t\tthis.picker_region = value[0].text + ',' + value[1].text + ',' + value[2].text;\r\n\t\t},\n    rollStart: function () {\n      var that = this;\r\n\t\t\tif(that.isinfo){\r\n\t\t\t\tif(that.info.isbegin){\r\n\t\t\t\t\t\tapp.error('活动未开始');return;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info.isend){\r\n\t\t\t\t\t\tapp.error('活动已结束');return;\r\n\t\t\t\t}\r\n\t\t\t\tthat.showdzp=false\r\n\t\t\t\tthat.maskshow = true;return;\r\n\t\t\t}\n      if (bool) return; // 如果在执行就退出\n      bool = true; // 标志为 在执行\n      app.post('ApiChoujiang/index', {id: that.info.id,op: 'getjx',longitude: that.longitude,latitude: that.latitude}, function (res) {\n        if (res.status != 1) {\n          // app.alert(res.msg);\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示信息',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tcontent: res.msg,\r\n\t\t\t\t\t\tsuccess: function (res) {}\r\n\t\t\t\t\t});\n          bool = false;\n          return;\n        } else {\n          //奖品数量等于10,指针落在对应奖品区域的中心角度[252, 216, 180, 144, 108, 72, 36, 360, 324, 288]\n          var angel = 360 - 360 / res.jxcount * res.jxindex;\n        }\n        angel += 360 * 6;\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tvar baseStep = 30; // 起始滚动速度\r\n\t\t\t\t// #endif\n        // #ifndef H5\r\n        var baseStep = 140; // 起始滚动速度\r\n        // #endif\n        var baseSpeed = 0.3;\n        var count = 1;\n        var timer = setInterval(function () {\n          that.angelTo = count\n          clearInterval(dot_inter);\n          that.drawCanvas();\n\n          if (count == angel) {\n            console.log('完毕');\n\t\t\t\t\t\tbool = false;\n            clearInterval(timer);\n\t\t\t\t\t\t// #ifdef MP-TOUTIAO\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthat.jxshow = true;\n\t\t\t\t\t\t\tthat.jxmc = res.jxmc;\n\t\t\t\t\t\t\tthat.jx = res.jx;\n\t\t\t\t\t\t\tthat.dotStart();\n\t\t\t\t\t\t},3000)\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t// #ifndef MP-TOUTIAO\n\t\t\t\t\t\t\tthat.jxshow = true;\n\t\t\t\t\t\t\tthat.jxmc = res.jxmc;\n\t\t\t\t\t\t\tthat.jx = res.jx;\n\t\t\t\t\t\t\tthat.dotStart();\n\t\t\t\t\t\t// #endif\n            if (res.jxtp == 2 && res.spdata) {\n              uni.sendBizRedPacket({\n                timeStamp: res.spdata.timeStamp,\n                // 支付签名时间戳，\n                nonceStr: res.spdata.nonceStr,\n                // 支付签名随机串，不长于 32 位\n                package: res.spdata.package,\n                //扩展字段，由商户传入\n                signType: res.spdata.signType,\n                // 签名方式，\n                paySign: res.spdata.paySign,\n                // 支付签名\n                success: function (res) {\n                  console.log(res);\n                },\n                fail: function (res) {\n                  console.log(res);\n                },\n                complete: function (res) {\n                  console.log(res);\n                }\n              });\n            }\n          }\n\n          count = count + baseStep * ((angel - count) / angel > baseSpeed ? baseSpeed : (angel - count) / angel);\n          // #ifdef H5\r\n          if (angel - count < 0.5) {\r\n            count = angel;\r\n          }\r\n          // #endif\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tif (angel - count < 1) {\r\n\t\t\t\t\t  count = angel;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\n        }, that.speed);\n      });\n    },\n    drawCanvas: function () {\n      var that = this;\n      var ctx = uni.createCanvasContext('roulette', this);\n      var angelTo = this.angelTo || 0;\n      var width = windowWidth / 750 * 650;\n      var height = width;\n      var x = width / 2;\n      var y = width / 2;\n      var num = that.jxarr.length;\n      ctx.translate(x, y);\n      ctx.clearRect(-width, -height, width, height);\n      ctx.rotate(angelTo * Math.PI / 180); // 画外圆\n\n      ctx.beginPath();\n\t\t\tctx.setLineWidth(width / 2)\n\t\t\tctx.setStrokeStyle('#FFF7C5')\n      ctx.arc(0, 0, width / 4, 0, 2 * Math.PI);\n      ctx.stroke();\n      ctx.beginPath();\n\t\t\tctx.setLineWidth(1)\n\t\t\tctx.setStrokeStyle('#D9644F')\n      ctx.arc(0, 0, width / 2 - 1, 0, 2 * Math.PI);\n      ctx.stroke();\n      ctx.beginPath();\n\t\t\tctx.setLineWidth(2)\n\t\t\tctx.setStrokeStyle('#FDF28C')\n      ctx.arc(0, 0, width / 2 - 3, 0, 2 * Math.PI);\n      ctx.stroke();\n      ctx.beginPath();\n\t\t\tctx.setLineWidth(15)\n\t\t\tctx.setStrokeStyle('#F8645E')\n      ctx.arc(0, 0, width / 2 - 14, 0, 2 * Math.PI);\n      ctx.stroke(); // 装饰点\n\n      var dotColor = that.dotColor;\n      var startAngel = 0;\n      for (var i = 0; i < 26; i++) {\n        ctx.beginPath();\n        var radius = width / 2 - 14;\n        var xr = radius * Math.cos(startAngel);\n        var yr = radius * Math.sin(startAngel);\n\t\t\t\tctx.setFillStyle(dotColor[i % dotColor.length])\n        ctx.arc(xr, yr, 4, 0, 2 * Math.PI);\n        ctx.fill();\n        startAngel += 360 / 26 * (Math.PI / 180);\n      }\n\n      var jxarr = that.jxarr;\r\n\t\t\t\n      ctx.rotate(-(360 / num) * Math.PI / 180);\n\n      for (var i = 0; i < num; i++) {\n        ctx.rotate(360 / num * Math.PI / 180); //ctx.setFontSize(14)\n\n        ctx.font = 'normal bold 14px Arial';\n\t\t\t\tctx.setFillStyle('#e75228');\n\t\t\t\tctx.setTextAlign(\"center\");\n        ctx.fillText(jxarr[i].mc?jxarr[i].mc:'', 0, -(width / 2 - 50));\n        ctx.drawImage(jxarr[i].pic, -20, -(width / 2 - 70), 40, 40); //ctx.restore();\n      }\n\n      if (num % 2 == 0) {\n        ctx.rotate(180 / num * Math.PI / 180);\n      }\n\n      for (var i = 0; i < num; i++) {\n        //ctx.save();\n        ctx.rotate(360 / num * Math.PI / 180);\n        ctx.beginPath();\n\t\t\t\tctx.setLineWidth(2)\n        ctx.moveTo(0, 0);\n        ctx.lineTo(0, width / 2 - 20);\n        ctx.setStrokeStyle('#f6625c');\n        ctx.stroke();\n      }\n\n      ctx.draw();\n    },\n    dotStart: function () {\n      var that = this;\n      var times = 0;\n\t\t\tclearInterval(dot_inter);\n      that.drawCanvas();\n      dot_inter = setInterval(function () {\n        if (times % 2) {\n          var dotColor = that.dotColor_1;\n        } else {\n          var dotColor = that.dotColor_2;\n        }\n\n        times++;\n        that.dotColor = dotColor;\n        that.drawCanvas();\n      }, that.speedDot);\n    },\r\n\t\tformsub: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var subdata = e.detail.value;\r\n\t\t  var formcontent = that.info.formcontent;\r\n\t\t  var record = that.record;\r\n\t\t  var formdata = {};\r\n\t\t\r\n\t\t  for (var i = 0; i < formcontent.length; i++) {\r\n\t\t    //console.log(subdata['form' + i]);\r\n\t\t    if (formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === undefined || subdata['form' + i].length == 0)) {\r\n\t\t      app.alert(formcontent[i].val1 + ' 必填');\r\n\t\t      return;\r\n\t\t    }\r\n\t\t\r\n\t\t    if (formcontent[i].key == 'switch') {\r\n\t\t      if (subdata['form' + i] == false) {\r\n\t\t        subdata['form' + i] = '否';\r\n\t\t      } else {\r\n\t\t        subdata['form' + i] = '是';\r\n\t\t      }\r\n\t\t    }\r\n\t\t\r\n\t\t    if (formcontent[i].key == 'selector') {\r\n\t\t      subdata['form' + i] = formcontent[i].val2[subdata['form' + i]];\r\n\t\t    }\r\n\t\t\r\n\t\t    var nowformdata = {};\r\n\t\t    formdata[formcontent[i].val1] = subdata['form' + i];\r\n\t\t  }\r\n\t\t\r\n\r\n\t\t  app.post(\"ApiChoujiang/savememberinfo\", {\r\n\t\t\t\thid:that.info.id,\r\n\t\t    formcontent: formdata\r\n\t\t  }, function (res) {\r\n\t\t    if (res.status == 0) {\r\n\t\t      app.alert(res.msg);\r\n\t\t    } else {\r\n\t\t      that.changemaskshow();\r\n\t\t      app.success(res.msg);\r\n\t\t      that.getdata();\r\n\t\t    }\r\n\t\t  });\r\n\t\t}\n  }\n};\n</script>\n<style>\n.canvas{position: relative;}\n.canvas{width: 650rpx;height: 650rpx;margin:0 auto;margin-bottom:20rpx}\n.canvas>canvas{z-index: 0;}\n.start{height: 200rpx;width: 160rpx;position: absolute;top:50%;left: 50%;margin-left: -80rpx;margin-top: -110rpx;z-index: 2;}\n\n.wrap {width:100%;height:100%;}\n.header{width:100%;padding:22rpx 37rpx 0 37rpx;display:flex;justify-content:space-between}\n.rule,.my{width:140rpx;height:60rpx;border: 1px solid #f58d40;font-size:30rpx;line-height:60rpx;text-align: center;color: #f58d40;border-radius:5rpx;}\n.title {width:640rpx;height:316rpx;margin: auto;margin-top:-60rpx;}\n\n/*次数*/\n.border {width: 380rpx;height:64rpx;margin: 0 auto 25rpx;background:#fb3a13;font-size:24rpx;line-height:64rpx;text-align: center;color: #fff;border-radius:45rpx}\n.border2 {width:600rpx;height:50rpx;margin: 0 auto;background:#dbaa83;font-size:24rpx;line-height:50rpx;text-align: center;color: #fff;border-radius:10rpx}\n.scroll {width:550rpx;height:185rpx;margin:75rpx auto 0 auto;}\n.scroll .p {width: 372rpx;height:24rpx;margin: auto;}\n.sideBox{  width: 100%;height:100rpx;margin-top:20rpx;padding: 10rpx 0 10rpx 0;background-color: rgba(255, 255, 255, 0.2);border-radius:10rpx;overflow:hidden;}\n.sideBox .bd {width: 100%;height:80rpx;overflow:hidden;}\n.sideBox .sitem{overflow:hidden;text-align: center;font-size:20rpx;line-height:40rpx;color: #fff;}\n\n/*规则弹窗*/\n#mask-rule,#mask {position: fixed;left: 0;top: 0;z-index: 999;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\n#mask-rule .box-rule {position: relative;margin: 30% auto;padding-top: 40rpx;width: 90%;height: 675rpx;border-radius: 20rpx;background-color: #f58d40;}\n#mask-rule .box-rule .star {position: absolute;left: 50%;top: -100rpx;margin-left: -130rpx;width: 259rpx;height:87rpx;}\n#mask-rule .box-rule .h2 {width: 100%;text-align: center;line-height: 34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}\n#mask-rule #close-rule {position: absolute;right: 34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}\n/*内容盒子*/\n#mask-rule .con {overflow: auto;position: relative;margin: 40rpx auto;padding-right: 15rpx;width: 580rpx;height: 82%;line-height: 48rpx;font-size: 26rpx;color: #fff;}\n#mask-rule .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}\n/*中奖提示*/\n#mask,#mask2{position: fixed;left: 0;top: 0;z-index: 999;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\n#mask .blin,#mask2 .blin {width: 100%;height: 100%;-o-animation: circle 10s linear infinite;-ms-animation: circle 10s linear infinite;-moz-animation: circle 10s linear infinite;-webkit-animation: circle 10s linear infinite;animation: circle 10s linear infinite;}\n@keyframes circle {0% {-o-transform: rotate(0deg);-ms-transform: rotate(0deg);-moz-transform: rotate(0deg);-webkit-transform: rotate(0deg);transform: rotate(0deg);}100% {-o-transform: rotate(360deg);-ms-transform: rotate(360deg);-moz-transform: rotate(360deg);-webkit-transform: rotate(360deg);transform: rotate(360deg);}}\n#mask .caidai,#mask2 .caidai{position: absolute;left: 0;top: 0;z-index: 1;width: 100%;height: 100%;-o-transform: scale(1.2);-ms-transform: scale(1.2);-moz-transform: scale(1.2);-webkit-transform: scale(1.2);transform: scale(1.2);}\n#mask .winning,#mask2 .winning {position: absolute;left: 50%;top: 50%;z-index: 1;width: 675rpx;height: 600rpx;margin: -300rpx 0 0 -338rpx;-o-transform: scale(0.1);-ms-transform: scale(0.1);-moz-transform: scale(0.1);-webkit-transform: scale(0.1);transform: scale(0.1);}\n#mask .reback,#mask2 .reback{-o-animation: reback .5s linear forwards;-ms-animation: reback .5s linear forwards;-moz-animation: reback .5s linear forwards;-webkit-animation: reback .5s linear forwards;animation: reback .5s linear forwards;}\n@keyframes reback {100% {-o-transform: scale(1);-ms-transform: scale(1);-moz-transform: scale(1);-webkit-transform: scale(1);transform: scale(1);}}\n.winning .p{ position: absolute;left: 50%;top: 30%;width:80%;margin-left:-40%;color:#FFF;font-size:52rpx;text-align:center;}\n.winning .b{ font-size:44rpx;}\n.winning .btn {position: absolute;left: 50%;bottom: 15%;z-index: 2;width: 364rpx;height: 71rpx;line-height: 71rpx;margin-left: -182rpx;background-color:#ffee8d;border-radius:45rpx;-webkit-border-radius:45rpx;color:#f62a39;text-align:center;font-size:45rpx;\n}\n.winning .text2{padding-top: 70rpx;}\n@keyframes shake {50% {-o-transform: rotate(-5deg);-ms-transform: rotate(-5deg);-moz-transform: rotate(-5deg);-webkit-transform: rotate(-5deg);transform: rotate(-5deg);}100% {-o-transform: rotate(5deg);-ms-transform: rotate(5deg);-moz-transform: rotate(5deg);-webkit-transform: rotate(5deg);transform: rotate(5deg);}}\n@keyframes fadein {100% {opacity: 1;-o-transform: rotate(360deg);-ms-transform: rotate(360deg);-moz-transform: rotate(360deg);-webkit-transform: rotate(360deg);transform: rotate(360deg);}}\n.pageback{position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: -1;}\n\n\t.popup__container{width: 80%; margin: 10% auto;}\n\t.popup__modal{position: fixed;top:400rpx;width: 660rpx;height: 720rpx;margin: 0 auto;border-radius: 20rpx;left: 46rpx;}\n\t.popup__content{text-align: center;padding: 20rpx 0 ;}\n\t.popup__content image{height: 600rpx;width: 600rpx;}\r\n\t\r\n\t\r\n\t\r\n\t#mask-rule1{position: fixed;top: 0;z-index: 10;width: 100%;max-width:640px;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\r\n\t#mask-rule1 .box-rule {background-color: #f58d40;position: relative;margin: 30% auto;padding-top:40rpx;width: 90%;height:700rpx;border-radius:20rpx;}\r\n\t#mask-rule1 .box-rule .h2{width: 100%;text-align: center;line-height:34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}\r\n\t#mask-rule1 #close-rule1{position: absolute;right:34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}\r\n\t#mask-rule1 .con {overflow: auto;position: relative;margin: 40rpx auto;padding-right: 15rpx;width:580rpx;height: 82%;line-height: 48rpx;font-size: 26rpx;color: #fff;}\r\n\t#mask-rule1 .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}\r\n\t\r\n\t#mask-rule2{position: fixed;top: 0;z-index: 10;width: 100%;max-width:640px;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\r\n\t#mask-rule2 .box-rule {background-color: #f58d40;position: relative;margin: 30% auto;padding-top:40rpx;width: 90%;height:700rpx;border-radius:20rpx;}\r\n\t#mask-rule2 .box-rule .h2{width: 100%;text-align: center;line-height:34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}\r\n\t#mask-rule2 #close-rule2{position: absolute;right:34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}\r\n\t#mask-rule2 .con {overflow: auto;position: relative;margin: 20rpx auto;padding-right: 15rpx;width:580rpx;height:90%;line-height: 48rpx;font-size: 26rpx;color: #fff;}\r\n\t#mask-rule2 .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}\r\n\t.pay-form .item{width:100%;padding:0 0 10px 0;color:#fff;}\r\n\t.pay-form .item:last-child{border-bottom:0}\r\n\t.pay-form .item .f1{width:80px;text-align:right;padding-right:10px}\r\n\t.pay-form .item .f2 input[type=text]{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n\t.pay-form .item .f2 textarea{width:100%;height:60px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n\t.pay-form .item .f2 select{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n\t.pay-form .item .f2 label{height:35px;line-height:35px;} \r\n\t.subbtn{width:100%;background:#fb3a13;font-size: 30rpx;padding:0 22rpx;border-radius: 8rpx;color:#FFF;margin-top: 30rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464563\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}