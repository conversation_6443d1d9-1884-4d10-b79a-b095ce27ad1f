{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/myprize.vue?5c35", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/myprize.vue?edb9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/myprize.vue?1058", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/myprize.vue?613f", "uni-app:///activity/xydzp/myprize.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/myprize.vue?b191", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/xydzp/myprize.vue?09e8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "info", "datalist", "pagenum", "maskshow", "record", "formdata", "selectIndex", "picker_tmer", "picker_date", "picker_region", "items", "onLoad", "app", "url", "uni", "method", "header", "success", "that", "onPullDownRefresh", "methods", "time_editorBindPicker<PERSON><PERSON>e", "selector_editorBindPickerChange", "date_editorB<PERSON><PERSON><PERSON><PERSON><PERSON>e", "region_editorBindPickerChange", "getdata", "hid", "res", "<PERSON><PERSON>ang", "changemaskshow", "formsub", "subdata", "console", "formcontent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsHz1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACAC;MACA;MACAC;QACAD;QACApB;QACAsB;QACAC;UAAA;QAAA;QACAC;UACAC;QACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAb;QAAAc;MAAA;QACAC;QACAT;QACAA;QACAA;MACA;IACA;IACAU;MACA;MACAV;MACAA;MACAA;MACAA;MACA;MACA;MACA;MACA;MACAA;MACAA;MACAA;IACA;IACAW;MACA;MACAX;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;UACAlB;UACA;QACA;QAEA;UACA;YACAmB;UACA;YACAA;UACA;QACA;QAEA;UACAA;QACA;QAEA;QACA1B;MACA;MAEA2B;MACApB;QACAqB;MACA;QACA;UACArB;QACA;UACAM;UACAN;UACAM;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5PA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/xydzp/myprize.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/xydzp/myprize.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myprize.vue?vue&type=template&id=41b8b1de&\"\nvar renderjs\nimport script from \"./myprize.vue?vue&type=script&lang=js&\"\nexport * from \"./myprize.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myprize.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/xydzp/myprize.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myprize.vue?vue&type=template&id=41b8b1de&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myprize.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myprize.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" style=\"background-color:#fce243;min-height:100vh\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"banner\"><image :src=\"info.banner\" mode=\"widthFix\"></image></view>\n\t\t<view class=\"activity\">\n\t\t\t<view class=\"activity-amin\">\n\t\t\t\t<view class=\"h2\">我的奖品</view>\n\t\t\t\t<view class=\"tb0\">\n\t\t\t\t\t<view class=\"tr\">\n\t\t\t\t\t\t<view class=\"td\">中奖时间 </view>\n\t\t\t\t\t\t<view class=\"td\">中奖奖品</view>\n\t\t\t\t\t\t<view class=\"td\">领奖状态</view>\n\t\t\t\t\t\t<view class=\"td\">操作</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"tr\">\n\t\t\t\t\t\t<view class=\"td td2\">{{item.createtime}}</view>\n\t\t\t\t\t\t<view class=\"td td2\">{{item.jxmc}}</view>\n\t\t\t\t\t\t<view class=\"td td2\"><block v-if=\"item.status==0\">未领奖</block><block v-else>已领奖</block></view>\n\t\t\t\t\t\t<view class=\"td td2\">\n\t\t\t\t\t\t\t<text v-if=\"item.status==0\" @tap=\"duijiang\" :data-k=\"index\" :data-id=\"item.id\" style=\"background-color:#fb5a43;padding:4rpx 8rpx\">兑奖</text>\n\t\t\t\t\t\t\t<text v-if=\"item.jxtp==3 && item.status==1\" @tap=\"goto\" data-url=\"/pagesExt/coupon/mycoupon\" style=\"background-color:#fb6a43;padding:4rpx 8rpx\">查看</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"!datalist\" style=\"width:100%;padding:40rpx 0;text-align:center;color:#f19132\">暂无中奖记录~</view>\n\t\t\t\t<view @tap=\"goback\" class=\"goback\">返回</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view id=\"mask-rule2\" v-if=\"maskshow && formdata\">\n\t\t\t<view class=\"box-rule\" style=\"height:900rpx\">\n\t\t\t\t<view class=\"h2\">兑奖信息</view>\n\t\t\t\t<view id=\"close-rule2\" :style=\"'background: no-repeat center / contain;background-image: url('+pre_url+'/static/img/dzp/close.png);'\" @tap=\"changemaskshow\"></view>\n\t\t\t\t<view class=\"con\">\n\t\t\t\t\t<view class=\"text\" style=\"text-align:center\">\n\t\t\t\t\t\t<view id=\"linkinfo\" style=\"text-align: left;margin-left:10%;\">\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in formdata\" :key=\"index\">{{index}}:{{item}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image :src=\"record.hexiaoqr\" style=\"width:80%\" id=\"hexiaoqr\" mode=\"widthFix\"></image>\n\t\t\t\t\t\t<view>请出示兑奖码给核销员进行兑奖</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view id=\"mask-rule1\" v-if=\"maskshow && !formdata\">\n\t\t\t<view class=\"box-rule\" style=\"height:640rpx\">\n\t\t\t\t<view class=\"h2\">请填写兑奖信息</view>\n\t\t\t\t<view id=\"close-rule1\" :style=\"'background: no-repeat center / contain;background-image: url('+pre_url+'/static/img/dzp/close.png);'\" @tap=\"changemaskshow\"></view>\n\t\t\t\t<view class=\"con\">\n\t\t\t\t\t<form class @submit=\"formsub\">\n\t\t\t\t\t<view class=\"pay-form\" style=\"margin-top:0.18rem\">\n\t\t\t\t\t\t<view v-for=\"(item, idx) in info.formcontent\" :key=\"idx\" class=\"item flex-y-center\">\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.val1}}：</view>\n\t\t\t\t\t\t\t<view class=\"f2 flex flex1\">\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\n\t\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form' + idx\" class=\"input\" :placeholder=\"item.val2\"></input>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\n\t\t\t\t\t\t\t\t\t<textarea :name=\"'form' + idx\" class=\"textarea\" :placeholder=\"item.val2\"></textarea>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\n\t\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form' + idx\">\n\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1, index) in item.val2\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t\t\t<radio :value=\"item1\"></radio>{{item1}}\n\t\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\n\t\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form' + idx\">\n\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1, index) in item.val2\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t\t<checkbox :value=\"item1\" class=\"xyy-zu\"></checkbox>{{item1}}\n\t\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='switch'\">\n\t\t\t\t\t\t\t\t\t<switch class=\"xyy-zu\" value=\"1\" :name=\"'form' + idx\"></switch>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\n\t\t\t\t\t\t\t\t\t<picker mode=\"selector\" :name=\"'form' + idx\" class=\"xyy-pic\" :range=\"item.val2\" @change=\"selector_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"item.val2[selectIndex]\"> {{item.val2[selectIndex]}}</view>\n\t\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\n\t\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\n\t\t\t\t\t\t\t\t\t<picker mode=\"time\" :name=\"'form' + idx\" class=\"xyy-pic\" @change=\"time_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"picker_tmer\">{{picker_tmer}}</view>\n\t\t\t\t\t\t\t\t\t\t<view v-else>选择时间</view>\n\t\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\n\t\t\t\t\t\t\t\t\t<picker mode=\"date\" :name=\"'form' + idx\" class=\"xyy-pic\" @change=\"date_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"picker_date\"> {{picker_date}}</view>\n\t\t\t\t\t\t\t\t\t\t<view v-else>选择日期</view>\n\t\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"item.key=='region'\">\r\n\t\t\t\t\t\t\t\t\t<uni-data-picker style=\"color: #333;\" class=\"flex1\" :localdata=\"items\" popup-title=\"请选择省市区\"  @change=\"region_editorBindPickerChange\"\t:data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" :value=\"picker_region\" placeholder=\"请选择省市区\" placeholder-style=\"color:#fff;font-size:28rpx\" style=\"border: none;font-size:28rpx;padding: 0rpx;color:#fff;\"/>\r\n\t\t\t\t\t\t\t\t\t</uni-data-picker>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"padding:0 40px 0 80px\">\n\t\t\t\t\t\t\t<button class=\"subbtn\" form-type=\"submit\">确 定</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t</form>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\n      st: 0,\n\t\t\tinfo:{},\n      datalist: [],\n      pagenum: 1,\n      maskshow: false,\n      record: \"\",\n      formdata: \"\",\r\n\t\t\tselectIndex:null,\r\n\t\t\tpicker_tmer:'',\r\n\t\t\tpicker_date:'',\r\n\t\t\tpicker_region:'',\r\n\t\t\titems: [],\n    };\n  },\n\n  onLoad: function (opt) {\r\n\t\tlet that = this;\r\n\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\r\n\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\r\n\t\t\tif(customs.data.includes('plug_zhiming')) {\r\n\t\t\t\turl = app.globalData.pre_url+'/static/area_gaoxin.json';\r\n\t\t\t}\r\n\t\t\tuni.request({\r\n\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\r\n\t\t\t\tdata: {},\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\r\n\t\ttime_editorBindPickerChange:function(e){\r\n\t\t\tthis.picker_tmer = e.detail.value\r\n\t\t},\r\n\t\tselector_editorBindPickerChange:function(e){\r\n\t\t\tthis.selectIndex = e.detail.value;\r\n\t\t},\r\n\t\tdate_editorBindPickerChange:function(e){\r\n\t\t\tthis.picker_date = e.detail.value;\r\n\t\t},\r\n\t\tregion_editorBindPickerChange:function(e){\r\n\t\t\tconst value = e.detail.value\r\n\t\t\tthis.picker_region = value[0].text + ',' + value[1].text + ',' + value[2].text;\r\n\t\t},\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tapp.get('ApiChoujiang/myprize', {hid: that.opt.hid}, function (res) {\n\t\t\t\tres.info.formcontent = JSON.parse(res.info.formcontent);\n\t\t\t\tthat.info = res.info;\n\t\t\t\tthat.datalist = res.datalist;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    duijiang: function (e) {\n      var that = this;\r\n\t\t\tthat.picker_tmer = '';\r\n\t\t\tthat.picker_date = '';\r\n\t\t\tthat.picker_region = '';\r\n\t\t\tthat.selectIndex = null;\n      var k = e.currentTarget.dataset.k;\n      var id = e.currentTarget.dataset.id;\n      var record = that.datalist[k];\n      var formdata = JSON.parse(record.formdata);\n      that.record = record;\n      that.formdata = formdata;\n      that.maskshow = true;\n    },\n    changemaskshow: function () {\n      var that = this;\n      that.maskshow = !that.maskshow;\n    },\n    formsub: function (e) {\n      var that = this;\n      var subdata = e.detail.value;\n      var formcontent = that.info.formcontent;\n      var record = that.record;\n      var formdata = {};\n\n      for (var i = 0; i < formcontent.length; i++) {\n        //console.log(subdata['form' + i]);\n        if (formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === undefined || subdata['form' + i].length == 0)) {\n          app.alert(formcontent[i].val1 + ' 必填');\n          return;\n        }\n\n        if (formcontent[i].key == 'switch') {\n          if (subdata['form' + i] == false) {\n            subdata['form' + i] = '否';\n          } else {\n            subdata['form' + i] = '是';\n          }\n        }\n\n        if (formcontent[i].key == 'selector') {\n          subdata['form' + i] = formcontent[i].val2[subdata['form' + i]];\n        }\n\n        var nowformdata = {};\n        formdata[formcontent[i].val1] = subdata['form' + i];\n      }\n\n      console.log(formdata);\n      app.post(\"ApiChoujiang/subinfo/rid/\" + record.id, {\n        formcontent: formdata\n      }, function (res) {\n        if (res.status == 0) {\n          app.alert(res.msg);\n        } else {\n          that.changemaskshow();\n          app.success(res.msg);\n          that.getdata();\n        }\n      });\n    }\n  }\n};\r\n</script>\r\n<style>\r\n.banner{ width:100%;padding:0 5%}\r\n.banner image{ display:block;width:100%;}\r\n.activity{padding:0 0 45rpx 0;margin-top:20rpx}\r\n.activity-amin{width:94%; margin:0 auto;}\r\n.activity-amin .h2{ margin:0 auto 30rpx auto;width:330rpx;height: 60rpx;background-color: #fc8209;text-align: center;line-height:60rpx;font-size: 30rpx;color: #ffffff;border-radius: 26rpx;letter-spacing:14rpx}\r\n.wt1{display:block; border:none; background-color:#FFF; padding:22rpx 22rpx; border-radius:8rpx; font-size: 30rpx; margin-bottom:60rpx;width:100%}\r\n.wt4{width:100%;background-color:#fb3a13; color:#FFF;font-size:30rpx;margin-top: 60rpx;}\r\n.tb0{ width:100%; margin-bottom:6%;font-size:24rpx}\r\n.tb0 .tr{width:100%;display:flex;border-bottom:1px solid #fff}\r\n.tb0 .tr .td{width:20%;background-color:#f19132;line-height:80rpx;text-align:center; color:#fff886;}\r\n.tb0 .tr .td:nth-child(1){width:32%; }\r\n.tb0 .tr .td:nth-child(2){width:32%; }\r\n.tb0 .tr .td:nth-child(3){width:18%; }\r\n.tb0 .tr .td:nth-child(4){width:18%; }\r\n.tb0 .tr .td2{padding:20rpx 0; text-align:center; color:#FFF; background-color:#f19c48}\r\n.goback{display:block;color:#fff;background-color:#fb3a13;margin:20rpx auto 40rpx auto;width:90%;padding:20rpx 0;text-align:center;font-size:36rpx;border-radius:15rpx;}\r\n\r\n#mask-rule1{position: fixed;top: 0;z-index: 10;width: 100%;max-width:640px;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\r\n#mask-rule1 .box-rule {background-color: #f58d40;position: relative;margin: 30% auto;padding-top:40rpx;width: 90%;height:700rpx;border-radius:20rpx;}\r\n#mask-rule1 .box-rule .h2{width: 100%;text-align: center;line-height:34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}\r\n#mask-rule1 #close-rule1{position: absolute;right:34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}\r\n#mask-rule1 .con {overflow: auto;position: relative;margin: 40rpx auto;padding-right: 15rpx;width:580rpx;height: 82%;line-height: 48rpx;font-size: 26rpx;color: #fff;}\r\n#mask-rule1 .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}\r\n\r\n#mask-rule2{position: fixed;top: 0;z-index: 10;width: 100%;max-width:640px;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\r\n#mask-rule2 .box-rule {background-color: #f58d40;position: relative;margin: 30% auto;padding-top:40rpx;width: 90%;height:700rpx;border-radius:20rpx;}\r\n#mask-rule2 .box-rule .h2{width: 100%;text-align: center;line-height:34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}\r\n#mask-rule2 #close-rule2{position: absolute;right:34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}\r\n#mask-rule2 .con {overflow: auto;position: relative;margin: 20rpx auto;padding-right: 15rpx;width:580rpx;height:90%;line-height: 48rpx;font-size: 26rpx;color: #fff;}\r\n#mask-rule2 .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}\r\n\r\n.pay-form .item{width:100%;padding:0 0 10px 0;color:#fff;}\r\n.pay-form .item:last-child{border-bottom:0}\r\n.pay-form .item .f1{width:80px;text-align:right;padding-right:10px}\r\n.pay-form .item .f2 input[type=text]{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n.pay-form .item .f2 textarea{width:100%;height:60px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n.pay-form .item .f2 select{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n.pay-form .item .f2 label{height:35px;line-height:35px;}\r\n.subbtn{width:100%;background:#fb3a13;font-size: 30rpx;padding:0 22rpx;border-radius: 8rpx;color:#FFF;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myprize.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myprize.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464573\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}