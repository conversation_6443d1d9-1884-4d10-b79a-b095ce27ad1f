{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/mldetail.vue?b76f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/mldetail.vue?9609", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/mldetail.vue?9638", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/mldetail.vue?f10c", "uni-app:///activity/kecheng/mldetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/mldetail.vue?79c4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/mldetail.vue?8bf4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "isload", "isplay", "detail", "datalist", "pagecontent", "playshow", "stipshow", "lock", "status", "currentTime", "duration", "videoContext", "<PERSON><PERSON><PERSON>", "pagenum", "studlog", "innerAudioContext", "startTime", "seek", "time", "playJd", "nowtime", "isauto", "showspeed", "speedlist", "rate", "speedtext", "nodata", "mlid", "alert_time", "alert_status", "pre_url", "onLoad", "onShow", "clearInterval", "onUnload", "onHide", "onReachBottom", "methods", "getdata", "that", "app", "id", "kcid", "uni", "title", "pic", "interval", "setspeed", "todetail", "totiku", "getdatalist", "field", "order", "scrolltolower", "payvideo", "parsevideo", "pause", "addstudy", "logid", "timeupdate", "video", "icon", "ended", "kccid", "setTimeout", "bindButtonRate", "play", "<PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON>", "sliderChanging"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+E11B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;IACA;EACA;EACAC;IACAD;IACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACA;MACAA;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAI;YACAC;UACA;UACA;YACAJ;YAAA;UACA;UACAD;UACA;UACAA;UACAA;YAAAK;YAAAC;UAAA;UACAN;UACA;YACAO;cACAP;YACA;UACA;UACAA;QACA;UACAC;QACA;MAEA;IACA;IACAO;MACA;MACA;QACAR;MACA;QACAA;MACA;IAEA;IACAS;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;UACA;YACAR;YACA;UACA;UACA;YACA;YACA;YACA;cACAA;cACA;YACA;UACA;QACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;MAEA;MACA;MACA;MAEA;QACAA;MACA;QACAA;UACAA;QACA;MACA;IACA;IACAS;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;UACA;YACAT;YACA;UACA;UACA;YACA;YACA;YACA;cACAA;cACA;YACA;UACA;QACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;MACA;MACAA;IACA;IACAU;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAX;MACAA;MACAA;MACAC;QAAA3B;QAAAsC;QAAAC;QAAAX;MAAA;QACAF;QACAI;QACA;QACA;UACAJ;UACA;YACAA;UACA;QAEA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MAEA;IACA;IACAc;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAX;IACA;IACAY;MACA;MACAZ;IACA;IACAa;MACA;MACA;MACA;MACAjB;IACA;IACAkB;MACA;MACA;MACA;MACA;MACAjB;QAAAkB;QAAAjD;QAAAU;MAAA;QACAoB;QACA;UACA;UACA;YACApC;YACA;cACA;gBACAA;cACA;YACA;YACAoC;UACA;QACA;QACA;QACA;QACA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA;IACA;;IACAoB;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACApB;QACA;QACAqB;QACAA;QACApB;UACAA;QACA;UACAD;QACA;QACA;MACA;;MAGA;MACA;MACA;MACA;QACA;UACA;UACA5B;UACAlB;YACAmD;YACAiB;YACAnD;UACA;QACA;MACA;MAEA6B;MACA;MACA;MACAA;MACA;MACA;QACAA;QACA;MACA;MACAA;IAEA;IACAuB;MACA;MACA;QACAtB;UAAAuB;QAAA;UACA;YACAvB;YACAwB;cACA;gBACAxB;gBACA;gBAAAG;cAEA;YACA;UACA;QACA;MACA;MACA;QACAH;QACA;QAAAG;MAEA;IACA;IACAsB;MAAA;MACA;MACA;MACA1B;MACAA;MAEAA;MACAI;IACA;IACA;IACAuB;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACAF;UACAzB;UACA;UACA;UACA;QACA;MACA;MACAA;MAEA;QACAA;MACA;MAEA;MACA;QACAA;MACA;MACA;QACA;QACAA;MACA;MACA;QACAA;QACAN;QACAM;QACAA;MACA;MACA;QACA;QACA;QACA;QACAA;QACA;QACA;UACAA;UACA;QACA;QACAA;QACA;QACA;MACA;IAGA;IACA;IACA4B;MACA;MACA;MACA5B;IACA;IACA;IACA6B;MACA;MACA;QACA5B;QAAA;MACA;QACAD;QACA;MACA;IACA;IACA;IACA8B;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AClhBA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kecheng/mldetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kecheng/mldetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mldetail.vue?vue&type=template&id=038c8ae2&\"\nvar renderjs\nimport script from \"./mldetail.vue?vue&type=script&lang=js&\"\nexport * from \"./mldetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mldetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kecheng/mldetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mldetail.vue?vue&type=template&id=038c8ae2&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mldetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mldetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"wrap\">\r\n\t<block v-if=\"isload\">\r\n\t\t<block v-if=\"detail.kctype==1\">\r\n\t\t\t<view class=\"title\">{{detail.name}}</view>\r\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t<view style=\"margin-bottom: 40rpx;\"></view>\r\n\t\t</block>\r\n\t\t<view class=\"audo-video\" v-if=\"detail.kctype==2\">\r\n\t\t\t<view class=\"audoimg\"><image :src=\"detail.pic\"/></view>\r\n\t\t\t<view class=\"play\">\r\n\t\t\t\t<view class=\"play-left\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/video_icon.png'\" v-show=\"playshow\" @tap=\"play\"></image>   \r\n\t\t\t    <image :src=\"pre_url+'/static/img/play.png'\" v-show=\"!playshow\" @tap=\"pauseaudio\"></image> \r\n\t\t\t    <text>{{nowtime}}</text>\r\n\t\t\t  </view>\r\n\t\t\t  <view class=\"play-right\">\r\n\t\t\t\t\t<slider @change=\"sliderChange\"  @changing=\"sliderChanging\" class=\"slider\" block-size=\"16\"  :min=\"0\" :max=\"time\"  :value=\"currentTime\" activeColor=\"#595959\"  />\r\n\t\t\t  </view>\r\n\t\t\t\t<view class=\"play-end\"><text>{{duration}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"videobox\" v-if=\"detail.kctype==3\"> \r\n\t\t\t<video  class=\"video\" id=\"video\" :autoplay=\"true\" :src=\"detail.video_url\"  :initial-time=\"detail.startTime\" @pause=\"pause\" @timeupdate=\"timeupdate\" @ended=\"ended\"></video>\r\n\t\t\r\n\t\t\t\t<view class=\"speed\" v-if=\"showspeed\">\r\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"showspeed==1\" @tap=\"setspeed\">{{speedtext}}</view>\r\n\t\t\t\t\t\t<view v-if=\"showspeed==2\" class=\"f2\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in speedlist\">\r\n\t\t\t\t\t\t\t\t<view  :class=\"'f22 '+(rate==item?'on':'')\" @tap=\"bindButtonRate\" :data-rate='item'><text class=\"t2\">{{item}}</text><text>X</text></view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\" height: 30rpx; width: 100%; background-color: #f5f5f5;\"></view>\r\n\t\t<view class=\"content_box\">\r\n\t\t\t<view class=\"title flex\">\r\n\t\t\t\t<view class=\"t1\">课程目录</view>\r\n          <view class=\"t2\" v-if=\"!detail.learnhg && detail.isdt==1 && detail.count>=detail.kccount && iskaoshi!=1\" @tap.stop=\"goto\" :data-url=\"'tiku?id=' + detail.kcid\" data-opentype=\"redirect\">去答题</view>\r\n          <view class=\"t2\" v-if=\"iskaoshi==1\" @tap.stop=\"goto\" :data-url=\"'recordlog?kcid=' + detail.kcid\">答题记录</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"mulubox flex\" v-for=\"(item, index) in datalist\" :key=\"index\" >\r\n\t\t\t\t<view class=\"left_box\">\r\n\t\t\t\t\t<image v-if=\"item.kctype==1\" :src=\"pre_url+'/static/img/tw_icon.png'\" /> \r\n\t\t\t\t\t<image v-if=\"item.kctype==2\" :src=\"pre_url+'/static/img/mp3_icon.png'\" />\r\n\t\t\t\t\t<image v-if=\"item.kctype==3\" :src=\"pre_url+'/static/img/video_icon.png'\" /> \r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right_box flex\">\r\n\t\t\t\t\t<view :class=\"'title_box '+ (item.id==detail.id?'on':'')\"   @tap=\"todetail\" :data-index='index' :data-mianfei='item.ismianfei' :data-url=\"'mldetail?id='+item.id+'&kcid='+detail.kcid\" :data-opentype=\"item.kctype==1 ? 'redirect' : 'redirect'\" :data-study_status='item.study_status' :data-record_status='item.record_status'>\r\n\t\t\t\t\t\t<view class=\"t1\"> {{item.name}}</view>\r\n\t\t\t\t\t\t<view> \r\n\t\t\t\t\t\t\t<text  v-if=\"item.kctype==1\"  class=\"t2\">图文课程 </text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.kctype==2\"  class=\"t2\">音频课程 </text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.kctype==3\"  class=\"t2\">视频课程 </text>\r\n\t\t\t\t\t\t\t<text  class=\"t2\" v-if=\"item.video_duration>0\"> 时长: {{item.duration}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n          <view>\r\n            <view class=\"jindu\" v-if=\"item.jindu\">{{item.jindu}}{{item.kctype==1 && item.jindu!='100'?'':'%'}}</view>\r\n            <view class=\"skbtn\" v-if=\"item.ismianfei && !item.jindu\">试看</view>\r\n            <block v-if=\"detail.learnhg==1 && item.study_status == 1\">\r\n              <!-- 需要上一章答题合格，且有题库可答 -->\r\n              <view class=\"skbtn\" style=\"width: 120rpx;height:50rpx ;line-height: 50rpx;padding: 0rpx 20rpx;\" v-if=\"item.record_status==0 && item.tiku_status==1\" @tap.stop=\"totiku\" :data-index='index' :data-study_status='item.study_status' :data-record_status='item.record_status' :data-url=\"'tiku?id=' + detail.kcid + '&mlid='+item.id\" data-opentype=\"redirect\">去答题</view>\r\n              <view class=\"skbtn\" style=\"width: 140rpx;background-color: #fff;border: 2rpx solid #FC6D65;color: #FC6D65;height:50rpx ;line-height: 50rpx;padding: 0rpx 20rpx;\" v-if=\"item.record_status==1\" @tap.stop=\"goto\" :data-url=\"'recordlog?kcid=' + detail.kcid + '&mlid='+item.id\">答题记录</view>\r\n            </block>\r\n          </view>\r\n\t\t\t\t</view>\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<nomore text=\"没有更多课程了\" v-if=\"nomore\"></nomore>\r\n\t<nodata text=\"没有查找到相关课程\" v-if=\"nodata\"></nodata>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tisplay: 0,\r\n\t\t\tdetail: [],\r\n\t\t\tdatalist: [],\r\n\t\t\tpagecontent: \"\",\r\n\t\t\tplayshow:true, //播放的图片\r\n\t\t\tstipshow:false, //暂停的图片\r\n\t\t\tlock: false, // 锁\r\n\t\t\tstatus: 1, // 1暂停 2播放\r\n\t\t\tcurrentTime: 0,  //当前进度\r\n\t\t\tduration: '', // 总进度\r\n\t\t\tvideoContext: '',\r\n\t\t\tiskaoshi:'',\r\n\t\t\tpagenum:1,\r\n\t\t\tstudlog:[],\r\n\t\t\tinnerAudioContext: '',\r\n\t\t\tstartTime:'',\r\n\t\t\tseek: false ,//是否处于拖动状态\r\n\t\t\ttime:'',\r\n\t\t\tplayJd:0,\r\n\t\t\tnowtime:'',\r\n\t\t\tisauto:false,\r\n\t\t\tshowspeed:0,\r\n\t\t\tspeedlist:[],\r\n\t\t\trate:1,\r\n\t\t\tspeedtext:'倍速',\r\n\t\t\tnodata: false,\r\n      mlid:0,\r\n\t\t\talert_time:0,\r\n\t\t\talert_status:0,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t};\r\n\t},\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t\tthis.getdatalist(); \r\n\t\tthis.innerAudioContext = uni.createInnerAudioContext();\r\n\t},\r\n\tonShow:function(){\r\n\t\tvar that=this\r\n\t\tclearInterval(interval);\r\n\t\tthis.innerAudioContext.stop();\r\n\t},\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t\tvar that=this\r\n\t\tthis.innerAudioContext.stop();\r\n\t},\r\n\tonHide(){\r\n\t\tthis.playshow = false\r\n\t},\r\n\tonReachBottom: function () {\r\n\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\tthis.getdatalist(true);\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.id = id;\r\n\t\t\tvar kcid = this.opt.kcid || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiKecheng/mldetail', {id: id,kcid:kcid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        if(res.status == 1){\r\n          var detail = res.detail;\r\n          that.detail = detail;\r\n          that.iskaoshi = res.iskaoshi;\r\n          that.isauto = res.isauto;\r\n          that.currentTime = detail.startTime\r\n          that.showspeed = res.showspeed\r\n          that.speedlist = res.speedlist\r\n\t\t\t\t\tthat.alert_time = (detail.mianfei_time>0 && detail.ismianfei==1)?detail.mianfei_time:0\r\n          uni.setNavigationBarTitle({\r\n          \ttitle: detail.name\r\n          });\r\n          if(detail.jumpurl){\r\n          \tapp.goto(detail.jumpurl);return;\r\n          }\r\n          that.studylog = res.studylog;\r\n          var pagecontent = JSON.parse(detail.detail);\r\n          that.pagecontent = pagecontent;\r\n          that.loaded({title:detail.name,pic:detail.pic});\r\n          that.addstudy();\r\n          if(detail.kctype>1){\r\n          \tinterval = setInterval(function () {\r\n          \t\tthat.addstudy();\r\n          \t}, 10000);\r\n          }\r\n          that.play();\r\n        }else{\r\n          app.alert(res.msg)\r\n        }\r\n\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetspeed:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tif(that.showspeed==2){\r\n\t\t\t\t\tthat.showspeed = 1\r\n\t\t\t}else\tif(that.showspeed==1){\r\n\t\t\t\t\tthat.showspeed = 2\r\n\t\t\t}\r\n\t\r\n\t\t},\r\n\t\ttodetail:function(e){\r\n\t\t\tvar that = this;\r\n\t\t  var detail = that.detail;\r\n\t\t  var index = e.currentTarget.dataset.index;\r\n      //按顺序学习\r\n\t\t  if(detail.orderlearn){\r\n\t\t    var datalist = that.datalist;\r\n\t\t    if(index>=1){\r\n\t\t      var study_status = datalist[index-1].study_status;\r\n\t\t      if(study_status!=1){\r\n\t\t        app.alert('请按顺序从上往下学习');\r\n\t\t        return;\r\n\t\t      }\r\n          if(detail.learnhg){\r\n            var tiku_status      = datalist[index-1].tiku_status;\r\n            var record_status = datalist[index-1].record_status;\r\n            if(tiku_status == 1 && record_status!=1){\r\n              app.alert('上一章答题合格才能学习下一章');\r\n              return;\r\n            }\r\n          }\r\n\t\t    }\r\n\t\t  }else{\r\n        //答题合格后学习下一章\r\n        if(detail.learnhg && that.detail.learnkey != index){\r\n          var datalist = that.datalist;\r\n          var tiku_status   = datalist[that.detail.learnkey].tiku_status;\r\n          var record_status = datalist[that.detail.learnkey].record_status;\r\n          if(tiku_status == 1 && record_status!=1){\r\n            app.alert('上一章答题合格才能学习下一章');\r\n            return;\r\n          }\r\n        }\r\n      }\r\n      \r\n\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\tvar ismf = e.currentTarget.dataset.mianfei;\r\n\t\t\tvar opentype = e.currentTarget.dataset.opentype;\r\n\t\t\t\r\n\t\t\tif(ismf==1 || that.detail.ispay==1 || that.detail.price==0){\r\n\t\t\t\tapp.goto(url,opentype);\r\n\t\t\t}else{\r\n\t\t\t\tapp.alert('请先购买课程',function(){\r\n\t\t\t\t\tapp.goto('product?id='+that.opt.kcid);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n    totiku:function(e){\r\n    \tvar that = this;\r\n      var detail = that.detail;\r\n      var index  = e.currentTarget.dataset.index;\r\n      //按顺序学习\r\n      if(detail.orderlearn){\r\n        var datalist = that.datalist;\r\n        if(index>=1){\r\n          var prestudy_status = datalist[index-1].study_status;\r\n          if(prestudy_status!=1){\r\n            app.alert('请按顺序从上往下学习');\r\n            return;\r\n          }\r\n          if(detail.learnhg){\r\n            var tiku_status      = datalist[index-1].tiku_status;\r\n            var prerecord_status = datalist[index-1].record_status;\r\n            if(tiku_status == 1 && prerecord_status!=1){\r\n              app.alert('上一章答题合格才能学习下一章');\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      }else{\r\n        //答题合格后学习下一章\r\n        if(detail.learnhg && that.detail.learnkey != index){\r\n          var datalist = that.datalist;\r\n          var tiku_status   = datalist[that.detail.learnkey].tiku_status;\r\n          var record_status = datalist[that.detail.learnkey].record_status;\r\n          if(tiku_status == 1 && record_status!=1){\r\n            app.alert('上一章答题合格才能学习下一章');\r\n            return;\r\n          }\r\n        }\r\n      }\r\n    \tvar url = e.currentTarget.dataset.url;\r\n    \tapp.goto(url);\r\n    },\r\n\t\tgetdatalist: function(loadmore){\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar kcid = that.opt.kcid ? that.opt.kcid : '';\r\n\t\t\tvar order = that.order;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tapp.post('ApiKecheng/getmululist', {pagenum: pagenum,field: field,order: order,id:kcid}, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t  that.datalist = data;\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nodata = true;\r\n\t\t\t\t  }\r\n\t\t\t\t\t\r\n\t\t\t\t}else{\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nomore = true;\r\n\t\t\t\t  } else {\r\n\t\t\t\t    var datalist = that.datalist;\r\n\t\t\t\t    var newdata = datalist.concat(data);\r\n\t\t\t\t    that.datalist = newdata;\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\tscrolltolower: function () {\t     \r\n\t\t\tif (!this.nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;    \r\n\t\t\t\tthis.getdatalist(true);\r\n\t\t\t}\r\n\t\t},\r\n\t\tpayvideo: function () {\r\n\t\t\tthis.isplay = 1;\r\n\t\t\tuni.createVideoContext('video').play();\r\n\t\t},\r\n\t\tparsevideo: function () {\r\n\t\t\tthis.isplay = 0;\r\n\t\t\tuni.createVideoContext('video').stop();\r\n\t\t},\r\n\t\tpause:function(){\r\n\t\t\t//将暂停播放时间请求\r\n\t\t\tvar that = this\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tthat.addstudy();\r\n\t\t},\r\n\t\taddstudy:function(){\r\n\t\t\tvar that = this\r\n      var studylog = that.studylog; \r\n\t\t\tvar id       = that.detail.id;\r\n      var datalist = that.datalist;\r\n\t\t\tapp.post('ApiKecheng/addstudy', {logid:studylog.id,currentTime:that.currentTime,playJd:that.playJd}, function (res) {\r\n\t\t\t\tthat.detail.startTime = that.currentTime\r\n        if(res.status == 1 ){\r\n          //查询当前学习章节\r\n          if(that.detail.learnkey>=0){\r\n            datalist[that.detail.learnkey].jindu = res.jindu\r\n            if(res.jindu == '已学完' || res.jindu >= 100){\r\n              if(datalist[that.detail.learnkey].study_status == 0){\r\n                datalist[that.detail.learnkey].study_status = 1;\r\n              }\r\n            }\r\n            that.datalist = datalist;\r\n          }\r\n        }\r\n\t\t\t\t//if(res.playJd>='100' &&  that.isauto && that.detail.isdt==1 && that.detail.count>=that.detail.kccount && that.iskaoshi!=1){\r\n\t\t\t\t\t//app.goto('tiku?id=' + that.detail.kcid);\r\n\t\t\t\t//}\r\n\t\t\t\t/*if(that.playJd>=100){\r\n\t\t\t\t\t  app.confirm('本节已学完，是否学习下一节', function (res) {\r\n\t\t\t\t\t\t\t\tapp.post('ApiKecheng/nextsection', {id: id,kcid:that.detail.kcid}, function (res) {\r\n\t\t\t\t\t\t\t\t\t\t\tapp.goto('/activity/kecheng/mldetail?id='+res.id+'&kcid='+that.detail.kcid);\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfunction (res) {\r\n\t\t\t\t\t\t\t\t\tthat.stipshow=false;\r\n\t\t\t\t\t\t\t\t\tthat.playshow=true;\r\n\t\t\t\t\t\t\t\t\tthat.currentTime = 0;\r\n\t\t\t\t\t\t\t\t\tthat.nowtime = '00:00';\r\n\t\t\t\t\t\t\t\t\tthat.playJd = 0;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t}*/\r\n\t\t\t})\r\n\t\t},\r\n\t\ttimeupdate:function(e){\r\n\t\t\t//跳转到指定播放位置 initial-time 时间为秒\r\n\t\t\tlet that = this;\r\n      if(that.detail.learnkey<0){\r\n        return;\r\n      }\r\n      var studylog = that.studylog; \r\n\t\t\t//播放的总时长\r\n\t\t\tvar duration = e.detail.duration;\r\n\t\t\t//实时播放进度 秒数\r\n\t\t\tvar currentTime = e.detail.currentTime;\r\n\t\t\t\r\n\t\t\tif(that.alert_time>0 && currentTime>that.alert_time && that.alert_status == 0){\r\n\t\t\t\tthat.alert_status = 1\r\n\t\t\t\tvar video = uni.createVideoContext('video');\r\n\t\t\t\tvideo.pause();\r\n\t\t\t\tvideo.seek(that.alert_time)\r\n\t\t\t\tapp.confirm('试看时长已结束，前去开通继续观看', ()=>{\r\n\t\t\t\t\tapp.goto('/activity/kecheng/product?id='+that.detail.kcid);\r\n\t\t\t\t}, ()=>{\r\n\t\t\t\t\tthat.alert_status = 0;\r\n\t\t\t\t});\r\n\t\t\t\treturn ;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t//当前视频进度\r\n\t\t\t// console.log(\"视频播放到第\" + currentTime + \"秒\")//查看正在播放时间，以秒为单位\r\n\t\t\tvar jump_time = that.currentTime   //上次结束时间\r\n\t\t\tif (that.detail.isjinzhi == 1) {\r\n        if (currentTime > jump_time && currentTime - jump_time > 2 && that.datalist[that.detail.learnkey].jindu!='100' ) {\r\n            let videoContext = wx.createVideoContext('video');\r\n            videoContext.seek(that.currentTime);\r\n            wx.showToast({\r\n              title: '未完整看完该视频，不能快进',\r\n              icon: 'none',\r\n              duration: 2000\r\n            });\r\n        }\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthat.currentTime  = currentTime; //实时播放进度\r\n\t\t\tvar min = Math.floor(currentTime/60)\r\n\t\t\tvar second = currentTime%60\r\n\t\t\tthat.nowtime = (min>=10?min:'0'+min)+':'+(second>=10?second:'0'+second)\r\n\t\t\t  //计算进度\r\n\t\t\tif(that.playJd < 100){\r\n\t\t\t\tthat.playJd = (that.currentTime/(duration-1)).toFixed(2)*100;\r\n\t\t\t\tif(that.playJd>100) that.playJd=100\r\n\t\t\t}\r\n\t\t\tthat.datalist[that.detail.learnkey].jindu = that.playJd.toFixed(1)\r\n\t\t\r\n\t\t},\r\n\t\tended(){\r\n\t\t\tvar that=this;\r\n\t\t\tif(that.detail.is_give_score){\r\n\t\t\t\tapp.get('ApiKecheng/givescore', {kccid:that.detail.id}, function (res) {\r\n\t\t\t\t\tif(res.status){\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t if(that.iskaoshi!=1 && that.playJd==100 &&  that.isauto && that.detail.isdt==1  ){\r\n\t\t\t\t\t\t\t\tapp.goto('tiku?id=' + that.detail.kcid);\r\n\t\t\t\t\t\t\t\treturn;uni.createVideoContext('video')\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif(that.iskaoshi!=1 && that.playJd==100 &&  that.isauto && that.detail.isdt==1  ){\r\n\t\t\t\tapp.goto('tiku?id=' + that.detail.kcid);\r\n\t\t\t\treturn;uni.createVideoContext('video')\r\n\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tbindButtonRate:function(e){  //设置倍速\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar rate = e.currentTarget.dataset.rate\r\n\t\t\t\tthat.rate = rate;\r\n\t\t\t\tthat.showspeed=1;\r\n\r\n\t\t\t\tthat.speedtext = (rate=='1.0'?'正常':rate+'X');\r\n\t\t\t\tuni.createVideoContext('video').playbackRate(Number(rate));\r\n\t\t},\r\n\t\t// 播放\r\n\t\tplay() {\r\n\t\t\tvar that=this\r\n\t\t\tthis.playshow=true;\r\n\t\t\tthis.innerAudioContext.autoplay = true;\r\n\t\t\tthis.innerAudioContext.src = that.detail.voice_url;\r\n\t\t\tthis.innerAudioContext.play();\r\n\t\t\tthis.innerAudioContext.onCanplay(()=> {\r\n\t\t\t\tthis.innerAudioContext.duration;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthat.time = this.innerAudioContext.duration.toFixed(0);\r\n\t\t\t\t\tvar min = Math.floor(that.time/60);\r\n\t\t\t\t\tvar second = that.time%60\r\n\t\t\t\t\tthis.duration = (min>10?min:'0'+min)+':'+(second>10?second:'0'+second);\t\r\n\t\t\t\t}, 1000)\r\n\t\t\t})  \r\n\t\t\tthat.startTime =  that.detail.startTime\r\n\r\n\t\t\tif(that.detail.startTime >=that.detail.video_duration){\r\n\t\t\t\tthat.startTime =  0\r\n\t\t\t}\r\n\r\n\t\t\tthis.innerAudioContext.seek(that.startTime)\r\n\t\t\tthis.innerAudioContext.onPlay(() => {\r\n\t\t\t\tthat.playshow=false;   \r\n\t\t\t});\r\n\t\t\tthis.innerAudioContext.onPause(() => {\r\n\t\t\t\t//that.addstudy();\r\n\t\t\t\tthat.playshow=true;\r\n\t\t\t});\r\n\t\t\tthis.innerAudioContext.onEnded(() => {\r\n\t\t\t\tthat.playJd = 100;\r\n\t\t\t\tclearInterval(interval);\r\n\t\t\t\tthat.addstudy();\r\n\t\t\t\tthat.playshow=true;\r\n\t\t\t});\r\n\t\t\tthis.innerAudioContext.onTimeUpdate(() => {\r\n\t\t\t\tvar nowtime = this.innerAudioContext.currentTime.toFixed(0)\r\n\t\t\t\tvar min = Math.floor(nowtime/60)\r\n\t\t\t\tvar second = nowtime%60\r\n\t\t\t\tthat.nowtime = (min>=10?min:'0'+min)+':'+(second>=10?second:'0'+second)\r\n\t\t\t\t  //计算进度\r\n\t\t\t\tif(that.playJd < 100 && that.innerAudioContext.duration > 0){\r\n\t\t\t\t\tthat.playJd = ((nowtime/that.innerAudioContext.duration).toFixed(2))*100;\r\n\t\t\t\t\tif(that.playJd>100) that.playJd=100\r\n\t\t\t\t}\r\n\t\t\t\tthat.currentTime = this.innerAudioContext.currentTime;\r\n\t\t\t//\tconsole.log(that.currentTime);\r\n\t\t\t\t//console.log('播放进度',that.innerAudioContext.currentTime,)\t\t\t\r\n\t\t\t});\r\n\t\t \r\n\t\r\n\t\t}, \r\n\t\t// 暂停\r\n\t\tpauseaudio() {\r\n\t\t\tvar that=this\r\n\t\t\tthis.innerAudioContext.pause();\r\n\t\t\tthat.addstudy();\r\n\t\t},\r\n\t\t// 拖动进度条\r\n\t\tsliderChange(data) {\r\n\t\t\tvar that=this;\r\n\t\t\tif(that.detail.isjinzhi == 1 && data.detail.value>that.detail.startTime && that.datalist[that.detail.learnkey].jindu!='100'){\r\n\t\t\t\tapp.error('未完整听完该音频，不能快进');return;\r\n\t\t\t}else{\r\n\t\t\t\tthat.currentTime = data.detail.value;\r\n\t\t\t\tthis.innerAudioContext.seek(data.detail.value)\r\n\t\t\t}\t\r\n\t\t},\r\n\t\t//拖动中\r\n\t\tsliderChanging(data) {\t\r\n\t\t\tthis.currentTime = data.detail.value\t\r\n\t\t}\r\n\t\t\r\n\t}\r\n};\r\n</script>\r\n<style>\r\n.wrap{ background: #fff;}\r\n.wrap .title{ padding: 30rpx; font-size: 42rpx; color: #111111; font-weight: bold; justify-content: space-between;}\r\n\t\r\n.hide{ display: none;}\r\n.provideo{border-radius:27rpx;width:750rpx;position:absolute;z-index:1000;align-items:center;justify-content:space-between}\r\n.provideo image{ width: 100%;}\r\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\r\n.videobox{width:100%;text-align:center;background:#000;position: relative; }\r\n.videobox .video{width:100%; }\r\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}\r\n.videobox .speed{ position: absolute; right: 0; bottom:96rpx; display: flex;}\r\n.videobox .speed .f2{ display: flex; flex-direction: column; color:#7B7F80;background: rgba(0,0,0,0.6);padding-bottom: 20rpx;}\r\n.videobox .speed .f22{padding: 20rpx 20rpx 0rpx 20rpx; font-size: 20rpx;} \r\n.videobox .speed .f22 .t2{ color:#fff; font-size: 30rpx; margin-right: 4rpx;}\r\n.videobox .speed .f22.on{ color: #00BFFF;}\r\n.videobox .speed .f22.on .t2{color: #00BFFF;font-size: 34rpx; }\r\n.videobox .speed .t1{ background: rgba(0,0,0,0.5); padding:10rpx; color: #C4C3C3; font-size: 20rpx;}\r\n\r\n\r\n.content_box{ background: #fff;}\r\n.content_box .title{ line-height: 60rpx; margin-left: 30rpx; padding:20rpx 0rpx;border-bottom: 1px solid #F7F7F7;}\r\n.content_box .title .t1{ font-size: 32rpx; font-weight: bold;  }\r\n.content_box .title .t2{ font-size: 24rpx; background:#fff;border:1px solid #cdcdcd;border-radius:3px; margin-right: 20rpx; padding: 0rpx 20rpx; border-radius: 10rpx;}\r\n.mulubox{ padding-top: 35rpx; padding-left: 30rpx;}\r\n.left_box{ display: flex;}\r\n.left_box image{ width: 44rpx; height:44rpx; margin-right: 40rpx; margin-top: 26rpx; }\r\n.right_box{ border-bottom: 1px solid #F6F6F6; padding-bottom: 30rpx; width: 100%; justify-content: space-between;}\r\n.title_box{ width: 80%;}\r\n.title_box .t1{ color: #1E252F; font-size: 28rpx; font-weight: bold;}\r\n.title_box .t2{ color: #B8B8B8;font-size: 24rpx;line-height: 60rpx; margin-right: 15rpx;}\r\n.right_box .on text{ color:#FF5347}\r\n.right_box .on .t1{  color:#FF5347}\r\n.skbtn{  background-color: #FFEEEC; padding: 6rpx 20rpx; margin-right: 10px; height: 44rpx; width: 90rpx; color: #FC6D65; font-size: 24rpx; border-radius: 22rpx; margin-top: 20rpx;}\r\n.right_box .jindu{ color:#FF5347; margin-right: 20rpx; font-size: 24rpx;}\r\n.baner{ width:100%; overflow: hidden; box-sizing: border-box; position: relative;}\r\n.audioBg{display: block; width:100%; height:370rpx;}\r\n.transmit{ position: absolute; left: 0;  right: 0; top: 0; bottom:0; margin: auto; display: block; width:80rpx; height:80rpx;}\r\n\r\n.content {\tpadding: 20upx;}\r\n.list {font-size: 28upx;line-height: 88upx;padding-left: 30upx;background: #fff;border-radius: 10upx;margin-top: 20upx;color: #333;}\r\n.active {\tbackground: #169af3;color: #fff;}\r\n\r\n/*音频播放器样式*/\r\n.audoimg{ width: 100%; }\r\n.audoimg image{ width: 100%; height: 600rpx; }\r\n/deep/.uni-slider-handle-wrapper{\r\n    background: black !important;\r\n}\r\n/deep/.uni-slider-thumb{\r\n    background: black !important;\r\n}\r\n.play{ background-color:rgba(255,255,255,0.5);width: 100%; height: 124rpx;position: absolute; bottom:0%;  }\r\n.play-left text{ margin-top: 1px; color: black;  font-size: 13px; line-height: 120rpx;  position: absolute; left: 13%;    }\r\n.play-end text{ margin-top: 1px; color: black;  font-size: 13px; line-height: 120rpx; right: 8%;  position: absolute;      }\r\n.slider{  width: 366rpx; position: relative; margin-top: 42rpx;  color: black; float: left;}\r\n.musions{  width: 26px; height: 26px; margin: 17px 4px 0 5px; float: left; }\r\n.play image{   width: 26px; height: 26px; margin: 34rpx 4px 0 5px;float: left;  }\r\n.play-left{width: 170rpx;height: 116upx;    float: left;  border-radius: 38px;  }\r\n.play-right{ width: 66%;  float: left; height: 58px; position: relative; }\r\n.audo-video {  width: 100%;   position: relative; top: -18px; }\r\n.slider-box {  display: flex; align-items: center;justify-content: center;font-size: 26upx; color: #999; }\r\nbutton {  display: inline-block; width: 100upx; background-color: #fff;  font-size: 24upx;    color: #000;   padding: 0; }\r\n.hidden {position: fixed;  z-index: -1;   width: 1upx;height: 1upx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mldetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mldetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464169\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}