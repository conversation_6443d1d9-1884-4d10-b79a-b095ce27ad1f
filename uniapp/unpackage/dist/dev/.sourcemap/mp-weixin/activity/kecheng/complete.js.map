{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/complete.vue?ab37", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/complete.vue?8ae7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/complete.vue?9c20", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/complete.vue?7760", "uni-app:///activity/kecheng/complete.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/complete.vue?5311", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/complete.vue?4684"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pre_url", "tkdata", "onLoad", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "rid"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8C11B;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACA;UACA;QACA;QACAA;QACAA;MACA;IACA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kecheng/complete.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kecheng/complete.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./complete.vue?vue&type=template&id=11c21ceb&\"\nvar renderjs\nimport script from \"./complete.vue?vue&type=script&lang=js&\"\nexport * from \"./complete.vue?vue&type=script&lang=js&\"\nimport style0 from \"./complete.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kecheng/complete.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=template&id=11c21ceb&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.tkdata.ishg == 1 ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && !(_vm.tkdata.ishg == 1) ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"wrap\" :style=\"{background:t('color1')}\">\r\n\t\t\t<view class=\"top flex\" >\r\n\t\t\t\t<view class=\"f1\"><text class=\"t1\">{{tkdata.ishg==1?'合格':'不合格'}}</text>\r\n\t\t\t\t<text class=\"t2\">{{tkdata.ishg==1?'恭喜您，已通过考试':'请认真学习后再来试一下吧~'}} </text></view>\r\n\t\t\t\t<view class=\"score\"><text class=\"t1\">{{tkdata.score}}</text><text class=\"t2\">分</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"c_1 flex\">\r\n\t\t\t\t\t<view class=\"f2\"><text class=\"t1\">{{tkdata.rightnum}}</text><text class=\"t2\">答对题目</text></view>\r\n\t\t\t\t\t<view class=\"f2\"><text class=\"t1\">{{tkdata.errornum}}</text><text class=\"t2\">答错题目</text></view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"c_2\">\r\n\t\t\t\t\t<view class=\"list1\"><text class=\"t3\">考试时间</text><text class=\"t4\">{{tkdata.time}}</text></view>\r\n\t\t\t\t\t<view class=\"list1\"> <text class=\"t3\">交卷时间</text><text class=\"t4\">{{tkdata.endtime}}</text></view>\r\n\t\t\t\t\t<view class=\"list1\"><text class=\"t3\">答题用时</text><text class=\"t4\">{{tkdata.longtime}}</text></view>\r\n\t\t\t\t</view>\r\n        <block v-if=\"tkdata.ishg==1\">\r\n          <view  class=\"aginbtn\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'recordlog?kcid='+tkdata.kcid+'&mlid='+tkdata.mlid\">答题记录</view>\r\n          <view  class=\"bottom flex\" >\r\n            <view class=\"btn2\" v-if=\"tkdata.mlid>0\" :style=\"'width:100%'\" @tap=\"goto\" :data-url=\"'mldetail?kcid='+tkdata.kcid+'&id='+tkdata.mlid\" :data-opentype=\"redirect\"> 继续学习</view>\r\n            <view class=\"btn3\" v-if=\"tkdata.errornum>0\" :style=\"'margin-left:0;width:100%'\" @tap=\"goto\" :data-url=\"'error?rid='+tkdata.id\">错题回顾</view>\r\n          </view>\r\n        </block>\r\n        \r\n        <block v-else>\r\n          <view   class=\"aginbtn\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'tiku?id='+tkdata.kcid+'&mlid='+tkdata.mlid\">再答一次</view>\r\n          <view  class=\"bottom flex\" >\r\n            <view class=\"btn2\" :style=\"'width:100%'\" @tap=\"goto\" :data-url=\"'product?id='+tkdata.kcid\"> 继续学习</view>\r\n            <view class=\"btn3\" v-if=\"tkdata.errornum>0\" :style=\"'width:100%'\" @tap=\"goto\" :data-url=\"'error?rid='+tkdata.id\">错题回顾</view>\r\n          </view>\r\n        </block>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"height: 130rpx;\"></view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\ttkdata:[]\r\n\t\t};\r\n\t},\r\n\t  onLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t  },\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar rid = this.opt.rid || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiKecheng/complete', {rid:rid}, function (res){\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t//app.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.tkdata = res.data\r\n\t\t\t\tthat.loaded();\t\t\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\n.wrap{ background: #fff; padding: 30rpx; height: auto;}\r\n.top{ margin-top: 60rpx; padding: 50rpx; justify-content: space-between;}\r\n.top .f1 .t1{ display: block; color: #fff; font-size: 44rpx;}\r\n.top .f1 .t2{ font-size: 26rpx;  color:rgba(255,255,255,0.8); margin-top: 30rpx;display: block;}\r\n.content{ background: #fff; padding: 80rpx; border-radius:20rpx ;}\r\n.content .c_1{ justify-content: space-between; padding: 0 80rpx;}\r\n.content .c_1 .f2 text{ display:block }\r\n.content .c_1 .f2 .t1{  font-size: 64rpx; font-weight: bold; color: #222; text-align: center;}\r\n.content .c_1 .f2 .t2{ color: #93949E; font-size: 24rpx;  margin-top: 20rpx;}\r\n.content .list1{ line-height: 60rpx;}\r\n.content .list1 .t3{ font-size: 26rpx; color: #222;}\r\n.content .list1 .t4{ display: inline-block; margin-left: 40rpx;color: #93949E;}\r\n.content .c_2{ margin-top: 50rpx;} \r\n.aginbtn{ margin-top: 80rpx; text-align: center;color: #fff; border-radius:40rpx ; height: 88rpx; line-height: 88rpx; font-size: 32rpx;}\r\n.top .score{ width: 160rpx; height: 160rpx;}\r\n.bottom .btn2{ background-color: rgba(255,0,0,0.3); width: 240rpx; color:#FF5347 ; margin-top: 50rpx; height: 88rpx; text-align: center; \r\nfont-weight: bold; font-size: 32rpx;\r\nline-height: 88rpx;border-radius: 40rpx;}\r\n.bottom .btn3{ background-color:#EEEEEE; width: 240rpx; color:#222 ; margin-top: 50rpx; height: 88rpx; text-align: center; \r\nfont-weight: bold; font-size: 32rpx;\r\nline-height: 88rpx;border-radius: 40rpx; margin-left: 30rpx;}\r\n.score .t1{ color:#fff; font-size: 64rpx;font-weight: bold; text-align: center; display: inline-block; margin-left: 30rpx; margin-top: 20rpx;}\r\n.score .t2{ color:rgba(255,255,255,0.8);}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464205\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}