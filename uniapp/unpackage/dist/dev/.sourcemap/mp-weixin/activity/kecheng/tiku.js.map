{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/tiku.vue?30fd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/tiku.vue?4661", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/tiku.vue?9a69", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/tiku.vue?e45c", "uni-app:///activity/kecheng/tiku.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/tiku.vue?355e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/tiku.vue?c525"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "title", "datalist", "currentindex", "up", "djs", "tkdata", "set", "hasnum", "rid", "nums", "dtid", "lefttime", "right_option", "isActive", "mlid", "onLoad", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "kcid", "tmid", "op", "interval", "prevquestion", "answer", "nextquestion", "console", "getdjs", "bindTextAreaBlur", "selectOption", "bindanswer", "finish", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0Dt1B;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;QAAAC;QAAAC;QAAAhB;QAAAM;MAAA;QACAM;QACA;UACAC;UACA;QACA;QACA;UACAA;UACAA;UAAA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAK;YACAL;YACAA;UACA;QACA;UACAC;UAAA;QACA;QACAD;MACA;IACA;IACA;IACAM;MACA;MACAN;MACAC;QAAAX;QAAAI;MAAA;QACAM;QACA;UACAC;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACA;UACA;YACA;YACAO;cACAP;YACA;UACA;UACAA;QACA;UACAA;QACA;MACA;IACA;IACA;IACAQ;MACA;MACA;MACA;QACAhB;QACA;UACAS;UAAA;QACA;MACA;MACA;QACAT;QACA;UACAS;UAAA;QACA;MACA;MACAD;MACAC;QAAAX;QAAAE;QAAAE;MAAA;QACAM;QACA;UACAC;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACA;UACA;YACA;YACAO;cACAP;YACA;YACAS;UACA;UACAT;QACA;UACAA;QACA;QACA;UACAA;UACAA;QACA;MAEA;IACA;IACAU;MACA;MACA;MACA;QACAV;QACAA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAW;MACA;MACAX;IACA;IACAY;MACA;MACA;MACA;QACAZ;MACA;QACA;UACAA;QACA;UACAA;QACA;;QACAA;MACA;IACA;IACAa;MACA;MACA;MACAb;MACAA;IACA;IACAc;MACA;MACA;MACA;QACAtB;QACA;UACAS;UAAA;QACA;MACA;MACA;QACAT;QACA;UACAS;UAAA;QACA;MACA;MACAA;QACAX;QACAE;QACAE;MACA;QACAO;QACA;UACAJ;UACAI;UAAA;QACA;MACA;IACA;IACAc;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;AC7QA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kecheng/tiku.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kecheng/tiku.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tiku.vue?vue&type=template&id=621af851&\"\nvar renderjs\nimport script from \"./tiku.vue?vue&type=script&lang=js&\"\nexport * from \"./tiku.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tiku.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kecheng/tiku.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tiku.vue?vue&type=template&id=621af851&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload && _vm.tkdata.type == 1 && _vm.tkdata.rightcount > 1\n      ? _vm.__map(_vm.tkdata.option, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = _vm.isActive.indexOf(index)\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  var m0 = _vm.isload && _vm.hasnum > 1 ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.nums == _vm.hasnum ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && !(_vm.nums == _vm.hasnum) ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tiku.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tiku.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\r\n\t\t<view class=\"wrap\">\r\n\t\t\t<view class=\"top flex\">\r\n\t\t\t\t<view class=\"f1\" v-if=\"tkdata.type==1 && tkdata.rightcount==1\">单选题</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"tkdata.type==1 && tkdata.rightcount==2\">多选题</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"tkdata.type==2\">填空题</view>\r\n\t\t\t\t<view class=\"f2\">倒计时：{{djs}}</view>\r\n\t\t\t\t<view class=\"f3\">{{hasnum}}/{{nums}}</view>\r\n\t\t\t</view>\r\n\t\t\t\t<view class=\"question\" >\r\n\t\t\t\t\t<view class=\"title\" >\r\n\t\t\t\t\t\t{{hasnum}}.{{tkdata.title}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"tkdata.type==1 && tkdata.rightcount==1\">\r\n\t\t\t\t\t\t<view class=\"option_group\" >\r\n\t\t\t\t\t\t\t<view :class=\"'option flex ' +(index==currentindex?'on':'') \"  v-for=\"(item, index) in tkdata.option\" :key=\"index\" @tap=\"selectOption\" :data-index='index'>\r\n\t\t\t\t\t\t\t{{tkdata.sorts[index]}}\r\n\t\t\t\t\t\t\t<view class=\"after\" ></view> \r\n\t\t\t\t\t\t\t<view class=\"t1\">{{item}}</view></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"tkdata.type==1 && tkdata.rightcount>1\">\r\n\t\t\t\t\t\t<view class=\"option_group\" >\r\n\t\t\t\t\t\t\t<view :class=\"'option flex '+(isActive.indexOf(index)!=-1?'on':'')\"  v-for=\"(item, index) in tkdata.option\" :key=\"index\" @tap=\"selectOption\" :data-index='index'>\r\n\t\t\t\t\t\t\t{{tkdata.sorts[index]}}\r\n\t\t\t\t\t\t\t<view class=\"after\" ></view> \r\n\t\t\t\t\t\t\t<view class=\"t1\">{{item}}</view></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"tkdata.type==2\">\r\n\t\t\t\t\t\t<view class=\"option_group\">\r\n\t\t\t\t\t\t\t<view class=\"uni-textarea\">\r\n\t\t\t\t\t\t\t\t<textarea placeholder-style=\"color:#222\" placeholder=\"答:\" @blur=\"bindTextAreaBlur\" :value=\"right_option\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\r\n\t\t</view>\r\n\t\t<view class=\"bottom flex\">\r\n\t\t\t<block v-if=\"hasnum==1\"><button class=\"upbut flex-x-center flex-y-center hui\" >上一题</button></block>\r\n\t\t\t<block v-if=\"hasnum>1\"><button  @tap=\"prevquestion\" data-dttype=\"up\" class=\"upbut flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >上一题</button></block>\r\n\t\t\t<button v-if=\"nums==hasnum\" @tap=\"finish\" data-dttype=\"down\" class=\"downbtn flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >交卷</button>\r\n\t\t\t<button v-else @tap=\"nextquestion\" data-dttype=\"down\" class=\"downbtn flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >下一题</button>\r\n\t\t</view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\ttitle: \"\",\r\n\t\t\tdatalist: [],\r\n\t\t\tcurrentindex:'-1',\r\n\t\t\tup:'',\r\n\t\t\tdjs: '',\r\n\t\t\ttkdata:[],\r\n\t\t\tset:{},\r\n\t\t\thasnum:0,\r\n\t\t\trid:0,\r\n\t\t\tnums:0,\r\n\t\t\tdtid:0,\r\n\t\t\tlefttime:0,\r\n\t\t\tright_option:'',\r\n\t\t\tisActive: [],\r\n      mlid:0,\r\n\t\t};\r\n\t},\r\n\t  onLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n      this.mlid = this.opt.mlid || 0;\r\n\t\t\tthis.getdata();\r\n\t  },\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiKecheng/getTiku', {kcid: id,tmid:that.tmid,op:that.op,rid:that.rid,mlid:that.mlid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (res.status == 2) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\tapp.goto('complete?rid=' + that.rid);return;\r\n\t\t\t\t}\r\n\t\t\t\tthat.set = res.data.set;\r\n\t\t\t\tthat.tkdata = res.data.tkdata;\r\n\t\t\t\tthat.hasnum = res.data.hasnum;\r\n\t\t\t\tthat.rid = res.data.rid;\r\n\t\t\t\tthat.nums = res.data.nums;\r\n\t\t\t\tthat.dtid = res.data.dtid;\r\n\t\t\t\tthat.lefttime = res.data.lefttime;\r\n\t\t\t\tif (res.data.lefttime > 0) {\r\n\t\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\r\n\t\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.goto('complete?rid=' + res.rid);return;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\t//上一题\r\n\t\tprevquestion:function(){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiKecheng/prevquestion', {\tdtid:that.dtid,mlid:that.mlid}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.tkdata = res.data.tkdata;\r\n\t\t\t\t\tthat.hasnum = res.data.hasnum;\r\n\t\t\t\t\tthat.dtid = res.data.dtid;\r\n\t\t\t\t\tthat.isActive = [];\r\n\t\t\t\t\tif(that.tkdata.type==1 && res.data.answer!=null){\r\n\t\t\t\t\t\tif(that.tkdata.rightcount>1){\r\n\t\t\t\t\t\t\tvar answer = res.data.answer;\r\n\t\t\t\t\t\t\tanswer.map((item) => {\r\n\t\t\t\t\t\t\t  that.isActive.push(item);\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.currentindex = res.data.answer;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.right_option = res.data.answer;\r\n\t\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//下一题\r\n\t\tnextquestion:function(){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar right_option='';\r\n\t\t\t\tif(that.tkdata.type==1){\r\n\t\t\t\t\tright_option = that.currentindex;\r\n\t\t\t\t\tif(right_option==-1){\r\n\t\t\t\t\t\tapp.error('请选择答案');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(that.tkdata.type==2){\r\n\t\t\t\t\tright_option = that.right_option;\r\n\t\t\t\t\tif(right_option=='' || right_option==undefined){\r\n\t\t\t\t\t\tapp.error('请填写答案');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiKecheng/nextquestion', {dtid:that.dtid,\tright_option:right_option,mlid:that.mlid}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.tkdata = res.data.tkdata;\r\n\t\t\t\t\tthat.hasnum = res.data.hasnum;\r\n\t\t\t\t\tthat.dtid = res.data.dtid;\r\n\t\t\t\t\tthat.isActive = [];\r\n\t\t\t\t\tif(that.tkdata.type==1 && res.data.answer!=null){\r\n\t\t\t\t\t\tif(that.tkdata.rightcount>1){\r\n\t\t\t\t\t\t\tvar answer = res.data.answer;\r\n\t\t\t\t\t\t\tanswer.map((item) => {\r\n\t\t\t\t\t\t\t  that.isActive.push(item);\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tconsole.log(that.isActive)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.currentindex = res.data.answer;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.right_option = res.data.answer;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!res.data.answer){\r\n\t\t\t\t\t\tthat.currentindex='-1';\r\n\t\t\t\t\t\tthat.isActive=[];\r\n\t\t\t\t\t}\t\t\r\n\t\t\t\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetdjs: function () {\r\n\t\t  var that = this;\r\n\t\t  var totalsec = that.lefttime;\r\n\t\t  if (totalsec <= 0) {\r\n\t\t    that.djs = '00时00分00秒';\r\n\t\t\t\tthat.finish();\r\n\t\t\t//that.toanswer();return;\r\n\t\t\t//app.goto('complete?rid=' + that.rid);return;\r\n\t\t  } else {\r\n\t\t    var houer = Math.floor(totalsec / 3600);\r\n\t\t    var min = Math.floor((totalsec - houer * 3600) / 60);\r\n\t\t    var sec = totalsec - houer * 3600 - min * 60;\r\n\t\t    var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n\t\t    that.djs = djs;\r\n\t\t  }\r\n\t\t},\r\n\t\tbindTextAreaBlur: function (e) {\r\n\t\t\tvar that=this\r\n\t\t\tthat.right_option = e.detail.value\r\n\t\t},\r\n\t\tselectOption: function (e) {\r\n\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t  var that = this;\r\n\t\t  if(that.tkdata.rightcount==1){\r\n\t\t\tthat.currentindex = index;\r\n\t\t  }else{\r\n\t\t\t\tif (that.isActive.indexOf(index) == -1) {\r\n\t\t\t\t\tthat.isActive.push(index); //选中添加到数组里\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.isActive.splice(that.isActive.indexOf(index), 1); //取消\r\n\t\t\t\t}\r\n\t\t\t\tthat.currentindex = that.isActive;\r\n\t\t  }\r\n\t\t}, \r\n\t\tbindanswer:function(e){\r\n\t\t\tvar that=this;\r\n\t\t\tvar op = e.currentTarget.dataset.dttype\r\n\t\t\tthat.op = op;\r\n\t\t\tthat.toanswer();\r\n\t\t},\r\n\t\tfinish:function(){\r\n\t\t\tvar that=this\r\n\t\t\tvar right_option='';\r\n\t\t\tif(that.tkdata.type==1 && that.lefttime>0){\r\n\t\t\t\tright_option = that.currentindex;\r\n\t\t\t\tif(right_option==-1){\r\n\t\t\t\t\tapp.error('请选择答案');return;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(that.tkdata.type==2  && that.lefttime>0){\r\n\t\t\t\tright_option = that.right_option;\r\n\t\t\t\tif(right_option=='' || right_option==undefined){\r\n\t\t\t\t\tapp.error('请填写答案');return;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tapp.post('ApiKecheng/tofinish', {\r\n\t\t\t\tdtid:that.dtid,\r\n\t\t\t\tright_option:right_option,\r\n        mlid:that.mlid\r\n\t\t\t}, function(res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\tclearInterval(interval);\r\n\t\t\t\t\tapp.goto('complete?rid=' + that.rid);return;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\n.wrap{ background: #fff; margin: 30rpx; border-radius: 10rpx; padding: 30rpx;}\r\n\r\n.top{height: 120rpx; line-height: 100rpx; justify-content: space-between;}\r\n.top .f1{ color:#93949E;font-size: 28rpx;}\r\n.top .f2{ color:#FF5347 ; font-size: 28rpx;}\r\n.top .f3{ color:#93949E ; font-size: 28rpx;}\r\n.question .title{ font-size: 30rpx; color: #333; font-weight: bold;}\r\n\r\n.option_group .option{ position: relative; padding:30rpx; background:#F8F4F4 ; margin-top: 30rpx; border-radius: 48rpx;border-color: #bbb; }\r\n.option_group .option .t1{ margin-left: 20rpx;border-left: 1px solid;padding-left: 20rpx;}\r\n.option_group .option.on{ background: #FDF1F1; color:#FF5347 ; border: 1px solid #FFAEA8 ; border-color: #FF8D8D;}\r\n/* .option_group .option.on .after{ border:1px solid #FF8D8D; } */\r\n/* .option_group .option .after{ border:1px solid #BBB; height:29rpx;  position: absolute;left: 12%; margin-top: 35rpx;} */\r\n.bottom .upbut{width:240rpx;height: 88rpx; line-height: 88rpx;color: #fff;  border-radius: 44rpx;border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; }\t\r\n.bottom .upbut.hui{ background:#E3E3E3;}\r\n.bottom .downbtn{margin-left:50rpx;width:360rpx;height: 88rpx; border-radius: 44rpx; line-height: 72rpx;color: #fff;  border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; }\t\r\n.bottom{ margin-top: 30rpx; padding: 30rpx;}\r\n.uni-textarea{ margin-top: 30rpx; background: #FAFAFA; border: 1px solid #EBE5E5;border-radius: 8rpx; padding: 30rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tiku.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tiku.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464218\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}