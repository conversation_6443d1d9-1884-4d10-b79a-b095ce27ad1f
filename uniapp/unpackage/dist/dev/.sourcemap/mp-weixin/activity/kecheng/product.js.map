{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/product.vue?8334", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/product.vue?ee2b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/product.vue?4631", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/product.vue?289c", "uni-app:///activity/kecheng/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/product.vue?488a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/product.vue?cee5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "app", "onLoad", "uni", "success", "that", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "console", "imageUrl", "query", "onUnload", "clearInterval", "onReachBottom", "methods", "getdata", "id", "showmore", "swiper<PERSON><PERSON>e", "buydialogChange", "currgg", "switchTopTab", "getdatalist", "pagenum", "field", "order", "todetail", "addfavorite", "proid", "type", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "showfuwudetail", "hidefuwudetail", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "sharemp", "shareapp", "itemList", "scene", "sharedata", "sharelink", "showsubqrcode", "closesubqrcode", "tobuy", "kcid", "loadImg", "element"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnKA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwLz1B;AACA;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,0DACA,uDACA,mDACA,wDACA,kDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,2DACA,2DACA,oDACA,uDACA,uDACA,qDACA,mDACA,oDACAC,uEACA,4DACA,mDACA,sDACA;EAEA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAA;IACAA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACAE;IACAA;IACA;MACAH;MACAI;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACAb;MACAJ;QAAAkB;MAAA;QACAd;QACA;UACAJ;UACA;QACA;QACAI;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAF;UACAK;QACA;QACAH;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAAG;UAAAC;QAAA;MACA;IACA;IACAW;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAhB;IACA;IACAiB;MACA;QACA;MACA;MACA;IACA;IACAC;MACAZ;MACA;MACA;MACAN;MACAA;MACAA;IACA;IACAmB;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACApB;MACAA;MACAA;MACAJ;QAAAyB;QAAAC;QAAAC;QAAAT;MAAA;QACAd;QACAF;QACA;QACA;UACAE;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAwB;MACA;MACA;MACA;MACA;QACA5B;MACA;QACAA;MACA;IACA;IACA;IACA6B;MACA;MACA;MACA7B;QAAA8B;QAAAC;MAAA;QACA;UACA3B;QACA;QACAJ;MACA;IACA;IACAgC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA9B;MACAA;MACAJ;MACAA;QAAA8B;MAAA;QACA9B;QACA;UACAA;QACA;UACAI;QACA;MACA;IACA;IACA+B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACArC;MACA;MACA;QACAA;MACA;IACA;IACAsC;MACA1C;MACA;IACA;IACA2C;MACA;MACAvC;MACAF;QACA0C;QACAzC;UACA;YACA;YACA;cACA0C;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACA5C;UACA;QACA;MACA;IACA;IACA8C;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACAlD;QACA;MACA;;MAEA;MACAA;QACAmD;MACA;QACAnD;QACA;UACAA;UACA;QACA;QACAA;MACA;IACA;IACAoD;MACA1C;MACA;IACA;EAAA,2DACA;IAAA;IACA;IACAN;;IAEA;IACA;MACA;IACA;EACA,sGAEAiD;IAAA;IACA;IACAzC;IACA;IACAF;IACAE;MACA;MACA;QACA;MACA;IACA;EACA;AAIA;AAAA,2B;;;;;;;;;;;;;AChhBA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kecheng/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kecheng/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=4c2051a6&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kecheng/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=4c2051a6&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    dpKechengItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kecheng-item/dp-kecheng-item\" */ \"@/components/dp-kecheng-item/dp-kecheng-item.vue\"\n      )\n    },\n    scrolltop: function () {\n      return import(\n        /* webpackChunkName: \"components/scrolltop/scrolltop\" */ \"@/components/scrolltop/scrolltop.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.product.pics.length : null\n  var m0 = _vm.isload && _vm.product.price > 0 ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && !(_vm.product.price > 0) ? _vm.t(\"color1\") : null\n  var m2 =\n    _vm.isload &&\n    _vm.kechengset.showcommission == 1 &&\n    _vm.product.commission > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.kechengset.showcommission == 1 &&\n    _vm.product.commission > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.kechengset.showcommission == 1 &&\n    _vm.product.commission > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m5 =\n    _vm.isload && (!_vm.product.chaptertype || _vm.product.chaptertype == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload && (!_vm.product.chaptertype || _vm.product.chaptertype == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var g1 = _vm.isload ? _vm.tjdatalist.length : null\n  var m7 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    (_vm.product.price == 0 || _vm.product.ispay == 1) &&\n    (!_vm.product.chaptertype || _vm.product.chaptertype == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    (_vm.product.price == 0 || _vm.product.ispay == 1) &&\n    !(!_vm.product.chaptertype || _vm.product.chaptertype == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !(_vm.product.price == 0 || _vm.product.ispay == 1) &&\n    (!_vm.product.chaptertype || _vm.product.chaptertype == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !(_vm.product.price == 0 || _vm.product.ispay == 1) &&\n    !(!_vm.product.chaptertype || _vm.product.chaptertype == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m11 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m12 =\n    _vm.isload && _vm.sharetypevisible && !(m11 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m13 =\n    _vm.isload && _vm.sharetypevisible && !(m11 == \"app\") && !(m12 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        g1: g1,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"swiper-container\"  >\r\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"500000\" @change=\"swiperChange\" :current=\"current\" :style=\"{ height: swiperHeight + 'px' }\">\r\n\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"  @load=\"loadImg\"/></view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"imageCount\">{{current+1}}/{{(product.pics).length}}</view>\r\n\t\t</view>\r\n\t\t<view class=\"header\"> \r\n\t\t\t<view class=\"price_share\">\r\n\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n        <view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pricebox flex\">\r\n\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"product.price>0\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t￥<text style=\"font-size:36rpx\">{{product.price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t<text style=\"font-size:36rpx\">免费</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\"  v-if=\"product.market_price>0\">￥{{product.market_price}}</view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"!product.chaptertype || product.chaptertype==1\" class=\"sales_stock\">\r\n\t\t\t\t\t<view class=\"f1\">{{product.count}}节课<block v-if=\"sysset && sysset.show_join_num == 1\"><text style=\"margin: 0 6rpx;\">|</text>已有{{product.join_num}}人学习</block> </view>\r\n\t\t\t\t</view>\t\r\n\t\t\t</view>\r\n\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"kechengset.showcommission==1 && product.commission > 0\">分享好友购买预计可得{{t('佣金')}}：<text style=\"font-weight:bold;padding:0 2px\">{{product.commission}}</text>{{product.commission_desc}}</view>\r\n\t\t\t<view class=\"upsavemoney\" :style=\"{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}\" v-if=\"kechengset.show_lvupsavemoney==1 &&product.upgrade_text && product.price > 0\">\r\n\t\t\t\t<!-- <view class=\"flex1\">升级到 {{product.nextlevelname}} 预计可节省<text style=\"font-weight:bold;padding:0 2px;color:#ca4312\">{{product.upsavemoney}}</text>元</view> -->\r\n\t\t\t\t<view class=\"flex1\">{{product.upgrade_text}} </view>\r\n\t\t\t\t<!-- <view style=\"margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312\" @tap=\"goto\" data-url=\"/pagesExt/my/levelup\">立即升级<image :src=\"pre_url+'/static/img/arrowright2.png'\" style=\"width:30rpx;height:30rpx\"/></view> -->\r\n\t\t\t\t<view style=\"margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312\" @tap=\"goto\" data-url=\"/pagesExt/my/levelup\"><image :src=\"pre_url+'/static/img/arrowright2.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t</view> \r\n\t\t</view>\t\t\r\n\t\t\r\n\t\t<view v-if=\"!product.chaptertype || product.chaptertype==1\" class=\"detail\">\r\n\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t<view class=\"order-tab2\">\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == 1 ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"1\" >课程介绍<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == 2 ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"2\" >课程目录<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"curTopIndex==1\"><dp :pagecontent=\"pagecontent\"></dp></block>\r\n\t\t\t<block v-if=\"curTopIndex==2\">\r\n\t\t\t\t<view class=\"mulubox flex\" v-for=\"(item, index) in datalist\" :key=\"index\" @tap=\"todetail\" :data-mianfei='item.ismianfei' :data-url=\"'mldetail?id='+item.id+'&kcid='+item.kcid\">\r\n\t\t\t\t\t<view class=\"left_box\">\r\n\t\t\t\t\t\t<image v-if=\"item.kctype==1\" :src=\"pre_url+'/static/img/tw_icon.png'\" /> \r\n\t\t\t\t\t\t<image v-if=\"item.kctype==2\" :src=\"pre_url+'/static/img/mp3_icon.png'\" />\r\n\t\t\t\t\t\t<image v-if=\"item.kctype==3\" :src=\"pre_url+'/static/img/video_icon.png'\" /> \r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right_box flex\">\r\n\t\t\t\t\t\t<view class=\"title_box\">\r\n\t\t\t\t\t\t\t<view class=\"t1\"> {{item.name}}</view>\r\n\t\t\t\t\t\t\t<view> <text  v-if=\"item.kctype==1\"  class=\"t2\">图文课程 </text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.kctype==2\"  class=\"t2\">音频课程 </text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.kctype==3\"  class=\"t2\">视频课程 </text>\r\n\t\t\t\t\t\t\t\t<text  v-if=\"item.kctype!=1\" class=\"t2\"> 时长: {{item.duration?item.duration:'未知'}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"skbtn\" v-if=\"item.ismianfei && product.price>0\">试看</view>\r\n\t\t\t\t\t\t<view class=\"skbtn\" v-if=\"product.price==0\">免费</view>\r\n\t\t\t\t\t</view>\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"more\" v-if=\"kechengset.details_rec==1 && !nomore\" @tap=\"showmore\"> 点击查看更多 </view>\r\n\t\t\t\t<nomore text=\"没有更多课程了\" v-if=\"nomore\"></nomore>\r\n\t\t\t\t<nodata text=\"没有查找到相关课程\" v-if=\"nodata\"></nodata>\r\n\t\t\t</block>\r\n\t\t</view>\r\n    <view v-else-if=\"product.freecontent\" class=\"detail\">\r\n      <view style=\"padding: 20rpx;font-size: 28rpx;\">\r\n       <parse :content=\"product.freecontent\"></parse>\r\n      </view>\r\n    </view>\r\n\t\t\r\n\t\t<view>\r\n\t\t<view class=\"xihuan\" v-if=\"tjdatalist.length > 0\">\r\n\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t<view class=\"xihuan-text\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/xihuan.png'\" class=\"img\"/>\r\n\t\t\t\t\t<text class=\"txt\">为您推荐</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"prolist\">\r\n\t\t\t\t<dp-kecheng-item :data=\"tjdatalist\" @addcart=\"addcart\" :menuindex=\"menuindex\"></dp-kecheng-item>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\r\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"f1\" style=\"width:100%;flex: 1;\">\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'/pages/index/index'\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shou.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">首页</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</button>\r\n\r\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\" style=\"width: 100%;flex: 1;\">\r\n        <block v-if=\"product.price==0 || product.ispay==1\">\r\n          <view  class=\"tobuy\">\r\n            <block v-if=\"!product.chaptertype || product.chaptertype==1\" >\r\n              <view class=\"tobuy flex-x-center flex-y-center\" @tap=\"goto\" :data-url=\"'mldetail?kcid='+product.id\" :style=\"{background:t('color1')}\" >立即学习</view>\r\n            </block>\r\n            <block v-else>\r\n              <view class=\"tobuy flex-x-center flex-y-center\" @tap=\"goto\" :data-url=\"'/pagesB/kecheng/lecturermldetail?kcid='+product.id\" :style=\"{background:t('color1')}\" >立即查看</view>\r\n            </block>\r\n          </view>\r\n        </block>\r\n        <block v-else>\r\n          <view v-if=\"!product.chaptertype || product.chaptertype==1\" class=\"tobuy flex-x-center flex-y-center\" @tap=\"tobuy\" :style=\"{background:t('color1')}\" >立即购买</view>\r\n          <view v-else class=\"tobuy flex-x-center flex-y-center\" @tap=\"goto\" :data-url=\"'/pagesB/kecheng/lecturermldetail?kcid='+product.id\" :style=\"{background:t('color1')}\" >立即查看</view>\r\n        </block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<scrolltop :isshow=\"scrolltopshow\"></scrolltop>\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- \t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tisload:false,\r\n\t\t\tisfavorite: false,\r\n\t\t\tcurrent: 0,\r\n\t\t\tproduct: [],\r\n\t\t\tpagecontent: \"\",\r\n\t\t\ttitle: \"\",\r\n\t\t\tsharepic: \"\",\r\n\t\t\tsharetypevisible: false,\r\n\t\t\tshowposter: false,\r\n\t\t\tposterpic: \"\",\r\n\t\t\tscrolltopshow: false,\r\n\t\t\tkfurl:'',\r\n\t\t\ttimeDialogShow: false,\r\n\t\t\tcurTopIndex: 1,\r\n\t\t\tdatalist: [],\r\n\t\t\ttjdatalist:[],\r\n\t\t\tkechengset: {},\r\n\t\t\tbusiness:{},\r\n\t\t\tsysset:{},\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tnodata: false,\r\n\t\t\tswiperHeight: '',\r\n\t\t\tnomore:false,\r\n      osname:'',\r\n\t\t};\r\n\t},\r\n  onLoad: function (opt) {\r\n    var that = this;\r\n    uni.getSystemInfo({\r\n    \tsuccess: function(res) {\r\n    \t\tthat.osname = res.osName;\r\n    \t}\r\n    })\r\n\t\tthat.opt = app.getopts(opt);\r\n\t\tthat.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\tconsole.log(sharewxdata)\r\n\t\tconsole.log(query)\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\tonReachBottom: function () {\r\n\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\tthis.getdatalist(true);\r\n\t\t}\r\n\t},\r\n\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiKecheng/detail', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.textset = app.globalData.textset;\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\t\t\t\tthat.product = product;\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.title = product.name;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\tthat.tjdatalist = res.tjdatalist;\r\n\t\t\t\tthat.kechengset = res.kechengset;\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: product.name\r\n\t\t\t\t});\r\n\t\t\t\tthat.kfurl = '/pages/kefu/index?bid='+product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded({title:product.name,pic:product.pic});\r\n\t\t\t});\r\n\t\t},\r\n\t\tshowmore:function(e){\r\n\t\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getdatalist(true);\r\n\t\t\t}\r\n\t\t},\r\n\t\tswiperChange: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.current = e.detail.current\r\n\t\t},\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype;\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\tcurrgg: function (e) {\r\n\t\t\tconsole.log(e);\r\n\t\t\tvar that = this\r\n\t\t\tthis.ggname = e.ggname;\r\n\t\t\tthat.ggid = e.ggid\r\n\t\t\tthat.proid = e.proid\r\n\t\t\tthat.num = e.num\r\n\t\t},\r\n\t\tswitchTopTab: function (e) {\r\n\t\t  var that = this;\r\n\t\t  this.curTopIndex = e.currentTarget.dataset.index;\r\n\t\t  this.getdatalist();\r\n\t\t},\r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tvar order = that.order;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tapp.post('ApiKecheng/getmululist', {pagenum: pagenum,field: field,order: order,id:id}, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t  that.datalist = data;\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nodata = true;\r\n\t\t\t\t  }\r\n\t\t\t\t}else{\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nomore = true;\r\n\t\t\t\t  } else {\r\n\t\t\t\t    var datalist = that.datalist;\r\n\t\t\t\t    var newdata = datalist.concat(data);\r\n\t\t\t\t    that.datalist = newdata;\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\ttodetail:function(e){\r\n\t\t\tvar that = this;\r\n\t\t    var url = e.currentTarget.dataset.url;\r\n\t\t\tvar ismf = e.currentTarget.dataset.mianfei;\r\n\t\t\tif(ismf==1 || that.product.ispay==1 || that.product.price==0){\r\n\t\t\t\tapp.goto(url);\r\n\t\t\t}else{\r\n\t\t\t\tapp.error('请先购买课程');\r\n\t\t\t}\r\n\t\t},\r\n\t\t//收藏操作\r\n\t\taddfavorite: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tapp.post('ApiKecheng/addfavorite', {proid: proid,type: 'kecheng'}, function (data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t}\r\n\t\t\t\tapp.success(data.msg);\r\n\t\t\t});\r\n\t\t},\r\n\t\tshareClick: function () {\r\n\t\t\tthis.sharetypevisible = true;\r\n\t\t},\r\n\t\thandleClickMask: function () {\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshowPoster: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.showposter = true;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tapp.showLoading('生成海报中');\r\n\t\t\tapp.post('ApiKecheng/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tposterDialogClose: function () {\r\n\t\t\tthis.showposter = false;\r\n\t\t},\r\n\t\tshowfuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = true;\r\n\t\t},\r\n\t\thidefuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = false\r\n\t\t},\r\n\t\tshowcuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = true;\r\n\t\t},\r\n\t\thidecuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = false\r\n\t\t},\r\n\t\tgetcoupon:function(){\r\n\t\t\tthis.showcuxiaodialog = false;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar scrollY = e.scrollTop;     \r\n\t\t\tif (scrollY > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollY < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t},\t\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/kecheng/detail?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/activity/kecheng/detail'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t},\r\n\t\tshowsubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t},\r\n\t\tclosesubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t},\r\n\t\ttobuy: function (e) {\r\n\t\t\tvar that=this;\r\n      var kechengset = that.kechengset;\r\n      if(that.osname == 'ios' && !kechengset.ios_canbuy){\r\n        app.alert(kechengset.ios_tip);\r\n        return;\r\n      }\r\n\r\n\t\t\t//购买\r\n\t\t\tapp.post('ApiKecheng/createOrder', {\r\n\t\t\t\tkcid:that.product.id,\r\n\t\t\t}, function(res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tapp.goto('/pagesExt/pay/pay?id=' + res.payorderid);\r\n\t\t\t});\r\n\t\t},\t \r\n\t\tloadImg() {\r\n\t\t\tconsole.log(222);\r\n\t\t\tthis.getCurrentSwiperHeight('.img');\r\n\t\t},\r\n\t\tswiperChange: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.current = e.detail.current\r\n\t\t\t\r\n\t\t\t//动态设置swiper的高度，使用nextTick延时设置\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t  this.getCurrentSwiperHeight('.img');\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 动态获取内容高度\r\n\t  getCurrentSwiperHeight(element) {\r\n\t      let query = uni.createSelectorQuery().in(this);\r\n\t      query.selectAll(element).boundingClientRect();\r\n\t\t\t\tvar imgList = this.product.pics;\r\n\t\t\t\tconsole.log(imgList)\r\n\t      query.exec((res) => {\r\n\t        // 切换到其他页面swiper的change事件仍会触发，这时获取的高度会是0，会导致回到使用swiper组件的页面不显示了\r\n\t        if (imgList.length && res[0][this.current].height) {\r\n\t          this.swiperHeight = res[0][this.current].height;\r\n\t        }\r\n\t      });\t\r\n\t\t},\r\n\t\t\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\n.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}\r\n.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}\r\n.follow_topbar .headimg image {height:64rpx; width:64rpx;}\r\n.follow_topbar .info {height:56rpx; padding:16rpx 0;}\r\n.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}\r\n.follow_topbar .info {height:80rpx; float:left;}\r\n.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}\r\n.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.qrcodebox .img{width:400rpx;height:400rpx}\r\n.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.goback{ position: absolute; top:0 ;width:64rpx ; height: 64rpx;z-index: 10000; margin: 30rpx;}\r\n.goback img{ width:64rpx ; height: 64rpx;}\r\n\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 420rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\r\n.provideo image{width:50rpx;height:50rpx;}\r\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\r\n\r\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\r\n.videobox .video{width:100%;height:650rpx;}\r\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx;}\r\n\r\n.header {padding: 20rpx 3%;background: #fff; width: 100%; border-radius:10rpx; margin: auto; margin-bottom: 20rpx; position: relative;}\r\n.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}\r\n.header .price_share .price{display:flex;align-items:flex-end}\r\n.header .price_share .price .f1{font-size:50rpx;color:#51B539;font-weight:bold}\r\n.header .price_share .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}\r\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center;flex-shrink: 0;}\r\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\r\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\r\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\r\n.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}\r\n.header .sales_stock{height:60rpx;line-height:60rpx;font-size:24rpx;color:#BBB; }\r\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\r\n.header .upsavemoney{display:flex;align-items:center;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:70rpx;padding:0 20rpx}\r\n\r\n.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}\r\n.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}\r\n.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}\r\n.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}\r\n.popup__title-text{font-size:32rpx}\r\n.popup__close{position:absolute;top:34rpx;right:34rpx}\r\n.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}\r\n.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.service-item .prefix{padding-top: 2px;}\r\n.service-item .suffix{padding-left: 10rpx;}\r\n.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx;}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx; width: 100%; margin: auto; border-radius: 10rpx; background: #fff;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}\r\n.bottombar .f1{flex:1;display:flex;align-items:center;justify-content: space-between;}\r\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:33.3%;position:relative}\r\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\r\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\r\n.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}\r\n\r\n.pricebox{ width: 100%;border:1px solid #fff; justify-content: space-between;}\r\n.pricebox .price{display:flex;align-items:flex-end}\r\n.pricebox .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:4rpx}\r\n\r\n.order-tab2{display:flex;width:auto;min-width:100%; padding-top: 10rpx; border-bottom: 1px solid #F7F7F7;}\r\n.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:80rpx; line-height:80rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;position:absolute;left:47%;margin-left:-20rpx;bottom:0rpx;height:6rpx;border-radius:1.5px;width:60rpx}\r\n.order-tab2 .on .after{display:block}\r\n\r\n.mulubox{ padding-top: 35rpx; padding-left: 30rpx;}\r\n.left_box{ display: flex;}\r\n.left_box image{ width: 44rpx; height:44rpx; margin-right: 40rpx; margin-top: 26rpx; }\r\n.right_box{ border-bottom: 1px solid #F6F6F6; padding-bottom: 30rpx; width: 100%; justify-content: space-between;}\r\n.title_box .t1{ color: #1E252F; font-size: 28rpx; font-weight: bold;}\r\n.title_box .t2{ color: #B8B8B8;font-size: 24rpx;line-height: 60rpx; margin-right: 15rpx;}\r\n.skbtn{  background-color: #FFEEEC; text-align: center; margin-right: 10px; height: 44rpx; width: 95rpx; color: #FC6D65; font-size: 24rpx; line-height: 40rpx; border-radius: 22rpx; margin-top: 20rpx;}\r\n.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:12rpx 160rpx}\r\n.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}\r\n.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\r\n.xihuan-text .txt{color:#111;font-size:30rpx}\r\n.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}\r\n.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}\r\n\r\n.more{text-align: center; height:80rpx;line-height: 80rpx;color: #999;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464201\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}