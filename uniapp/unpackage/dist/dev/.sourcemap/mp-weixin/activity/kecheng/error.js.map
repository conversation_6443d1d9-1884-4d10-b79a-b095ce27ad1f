{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/error.vue?9c59", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/error.vue?b983", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/error.vue?a7f9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/error.vue?71b0", "uni-app:///activity/kecheng/error.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/error.vue?7554", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kecheng/error.vue?cd3e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "title", "datalist", "logid", "isActive", "currentindex", "op", "onLoad", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "rid", "answer", "toanswer", "bindTextAreaBlur"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqEv1B;AACA;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,mDACA,yDACA,mDACA;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;QAAAR;QAAAH;MAAA;QACAS;QACA;UACAC;UACA;QACA;QACAD;QACA;QACA;UACA;UACAG;YACAH;UACA;UACAA;QACA;UACAA;QACA;QACAA;QACAA;MACA;IACA;IACAI;MACA;MACAJ;MACAA;MACAA;IACA;IACAK;MACA;MACAL;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpIA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kecheng/error.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kecheng/error.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./error.vue?vue&type=template&id=43d83946&\"\nvar renderjs\nimport script from \"./error.vue?vue&type=script&lang=js&\"\nexport * from \"./error.vue?vue&type=script&lang=js&\"\nimport style0 from \"./error.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kecheng/error.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=template&id=43d83946&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload && _vm.tkdata.type == 1 && _vm.tkdata.rightcount > 1\n      ? _vm.__map(_vm.tkdata.option, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = _vm.isActive.indexOf(index)\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  var m0 = _vm.isload && _vm.tkdata.isup == 1 ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.tkdata.isdown == 1 ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\r\n\t\t<view class=\"wrap\">\r\n\t\t\t\t<view class=\"top flex\">\r\n\t\t\t\t<view class=\"f1\" v-if=\"tkdata.type==1 && tkdata.rightcount==1\">单选题</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"tkdata.type==1 && tkdata.rightcount==2\">多选题</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"tkdata.type==2\">填空题</view>\r\n\t\t\t\t<view class=\"f3\">{{tkdata.sort}}/{{tkdata.nums}}</view>\r\n\t\t\t</view>\r\n\t\t\t\t<view class=\"question\" >\r\n\t\t\t\t\t<view class=\"title\" >\r\n\t\t\t\t\t\t{{tkdata.sort}}.{{tkdata.title}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"tkdata.type==1 && tkdata.rightcount==1\">\r\n\t\t\t\t\t\t<view class=\"option_group\" >\r\n\t\t\t\t\t\t\t<view :class=\"'option flex ' +(index==currentindex?'on':'') \"  v-for=\"(item, index) in tkdata.option\" :key=\"index\">\r\n\t\t\t\t\t\t\t{{tkdata.sorts[index]}}\r\n\t\t\t\t\t\t\t<view class=\"after\" ></view> \r\n\t\t\t\t\t\t\t<view class=\"t1\">{{item}}</view></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"tkdata.type==1 && tkdata.rightcount>1\">\r\n\t\t\t\t\t\t<view class=\"option_group\" >\r\n\t\t\t\t\t\t\t<view :class=\"'option flex '+(isActive.indexOf(index)!=-1?'on':'')\"  v-for=\"(item, index) in tkdata.option\" :key=\"index\" @tap=\"selectOption(index)\" :data-index='index'>\r\n\t\t\t\t\t\t\t{{tkdata.sorts[index]}}\r\n\t\t\t\t\t\t\t<view class=\"after\" ></view> \r\n\t\t\t\t\t\t\t<view class=\"t1\">{{item}}</view></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"tkdata.type==1 && !tkdata.rightcount\">\r\n\t\t\t\t\t\t<view class=\"option_group\" >\r\n\t\t\t\t\t\t\t<view :class=\"'option flex '\"  v-for=\"(item, index) in tkdata.option\" :key=\"index\" @tap=\"selectOption(index)\" :data-index='index'>\r\n\t\t\t\t\t\t\t{{tkdata.sorts[index]}}\r\n\t\t\t\t\t\t\t<view class=\"after\" ></view> \r\n\t\t\t\t\t\t\t<view class=\"t1\">{{item}}</view></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"tkdata.type==2\">\r\n\t\t\t\t\t\t<view class=\"option_group\">\r\n\t\t\t\t\t\t\t<view class=\"uni-textarea\">\r\n\t\t\t\t\t\t\t\t<textarea placeholder-style=\"color:#222\" placeholder=\"答:\" @blur=\"bindTextAreaBlur\" :value=\"tkdata.answer\"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\r\n\t\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"right_content\">\r\n\t\t\t<text class=\"t1\">正确答案</text>\r\n\t\t\t<text class=\"t2\">{{tkdata.type==2?tkdata.right_option:''}}{{tkdata.type==1?tkdata.right_options:''}}</text>\r\n\t\t\t<view  style=\"color: #93949E;font-size: 26rpx\">题目解析：{{tkdata.jiexi}}</view>\r\n\t\t</view>\r\n\t\t<view class=\"bottom flex\">\r\n\t\t\t<block v-if=\"tkdata.isup!=1\"><button class=\"upbut flex-x-center flex-y-center hui\" >上一题</button></block>\r\n\t\t\t<block v-if=\"tkdata.isup==1\"><button  @tap=\"toanswer\" data-dttype=\"up\" class=\"upbut flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >上一题</button></block>\r\n\t\t\t<button  v-if=\"tkdata.isdown==1\" @tap=\"toanswer\" data-dttype=\"down\" class=\"downbtn flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >下一题</button>\r\n\t\t\t<button  v-if=\"tkdata.isdown!=1\"  class=\"downbtn flex-x-center flex-y-center hui\"  >下一题</button>\r\n\t\t</view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\ttitle: \"\",\r\n\t\t  datalist: [],\r\n\t\t\tlogid:'',\r\n\t\t\tisActive:[],\r\n\t\t\tcurrentindex:'',\r\n\t\t\top:'',\r\n\t\t\tisActive:[],\r\n\t\t\tright_option:'',\r\n\t\t\ttkdata:[]\r\n\t\t};\r\n\t},\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t},\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that =this;\r\n\t\t\tvar id = this.opt.rid || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiKecheng/error', { rid:id,op:that.op,logid:that.logid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.tkdata = res.data;\t\r\n\t\t\t\tvar answer = [];\r\n\t\t\t\tif(that.tkdata.type==1 && res.data.answer.length>0){\r\n\t\t\t\t\tvar answer = res.data.answer;\r\n\t\t\t\t\tanswer.map((item) => {\r\n\t\t\t\t\t  that.isActive.push(item);\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.currentindex = answer;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.currentindex = res.data.answer;\r\n\t\t\t\t}\r\n\t\t\t\tthat.logid = res.data.logid\r\n\t\t\t\tthat.loaded();\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\ttoanswer:function(e){\r\n\t\t\tvar that =this;\r\n\t\t\tthat.op = e.currentTarget.dataset.dttype\r\n\t\t\tthat.isActive=[];\r\n\t\t\tthat.getdata();\r\n\t\t},\r\n\t\tbindTextAreaBlur: function (e) {\r\n\t\t\tvar that=this\r\n\t\t\tthat.right_option = e.detail.value\r\n\t\t},\r\n\t}\r\n};\r\n</script>\r\n<style>\r\n.wrap{ background: #fff; margin: 30rpx; border-radius: 10rpx; padding: 30rpx;}\r\n\r\n.top{height: 120rpx; line-height: 100rpx; justify-content: space-between;}\r\n.top .f1{ color:#93949E;font-size: 28rpx;}\r\n.top .f2{ color:#FF5347 ; font-size: 28rpx;}\r\n.top .f3{ color:#93949E ; font-size: 28rpx;}\r\n.question .title{ font-size: 30rpx; color: #333; font-weight: bold;}\r\n\r\n.right_content{ background: #fff; margin: 30rpx; border-radius: 10rpx; padding: 30rpx;}\r\n.right_content .t1{ color: #333; font-weight: 30rpx; font-weight: bold; display: block; margin-bottom: 20rpx;}\r\n.right_content .t2{ color:#93949E;font-size: 26rpx;}\r\n\r\n.option_group .option{ position: relative; padding-left: 37rpx; height: 96rpx; line-height: 96rpx; background:#F8F4F4 ; margin-top: 30rpx; border-radius: 48rpx; }\r\n.option_group .option .t1{ margin-left: 40rpx;}\r\n.option_group .option.on{ background: #FDF1F1; color:#FF5347 ; border: 1px solid #FFAEA8;}\r\n.option_group .option.on .after{ border:1px solid #FF8D8D; }\r\n.option_group .option.green{ background:#36CF7B ; color:#fff}\r\n.option_group .option.green .after{  border:1px solid #fff;} \r\n.option_group .option .after{ border:1px solid #BBB; height:29rpx;  position: absolute;left: 12%; margin-top: 35rpx;}\r\n.bottom .upbut{width:240rpx;height: 88rpx; line-height: 88rpx;color: #fff;  border-radius: 44rpx;border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; }\t\r\n.bottom .upbut.hui{ background: #E3E3E3;}\r\n.bottom .downbtn{margin-left:50rpx;width:360rpx;height: 88rpx; border-radius: 44rpx; line-height: 72rpx;color: #fff;  border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; }\t\r\n.bottom .downbtn.hui{ background: #E3E3E3;}\r\n.bottom{ margin-top: 30rpx; padding: 30rpx;}\r\n.uni-textarea{ margin-top: 30rpx; background: #FAFAFA; border: 1px solid #EBE5E5;border-radius: 8rpx; padding: 30rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464176\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}