{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/withdraw.vue?efa1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/withdraw.vue?1f1e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/withdraw.vue?ce4f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/withdraw.vue?00b7", "uni-app:///activity/commission/withdraw.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/withdraw.vue?06b0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/withdraw.vue?ed7b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "userinfo", "money", "sysset", "paytype", "tmplids", "bid", "selectbank", "bank", "pre_url", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "moneyinput", "changeradio", "formSubmit", "use_money", "redirect_url", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgH11B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAT;MAAA;QACAQ;QACAE;UACAC;QACA;QACA;QACAH;QACAA;QACAA;QACA;QACA;UACAV;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAU;QACAA;QACAA;QACAA;MACA;IACA;IAEAI;MACA;MACA;MACA;QACAH;MACA;QACAA;MACA;MACA;IACA;IACAI;MACA;MACA;MACAL;IACA;IACAM;MAEA;MACA;MACA;MACA;MAEA;MACA;MAEA;QACAL;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MAEA;QACAA;QACA;MACA;MACA;MACA;QACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;YACAM;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;UACAN;UACA;QACA;MACA;MAEA;QACAA;UACAA;QACA;QACA;MACA;MACA;QACA;UACAA;YACAA;UACA;QACA;MACA;QACA;UACAA;YACAA;UACA;UACA;QACA;MACA;MACA;QACAA;UACAA;QACA;QACA;MACA;MACA;MACA;QACAA;UACAA;QACA;QACA;MACA;MACA;MACA;QACAA;UACAA;QACA;QACA;MACA;MACAA;MACAA;QAAAb;QAAAE;QAAAE;MAAA;QACAS;QACA;UACAA;UACA;QACA;UACA;YACA;YACA;YACA;cACAO;YACA;YACA;UACA;YACAP;YACA;cACAD;gBACAS;kBACAR;gBACA;cACA;YACA;cACAD;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/VA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/withdraw.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/withdraw.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./withdraw.vue?vue&type=template&id=56e3583c&\"\nvar renderjs\nimport script from \"./withdraw.vue?vue&type=script&lang=js&\"\nexport * from \"./withdraw.vue?vue&type=script&lang=js&\"\nimport style0 from \"./withdraw.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/withdraw.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdraw.vue?vue&type=template&id=56e3583c&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"佣金\") : null\n  var m2 =\n    _vm.isload &&\n    _vm.sysset.comwithdraw_need_score &&\n    _vm.sysset.comwithdraw_need_score == 1\n      ? _vm.t(\"积分\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.sysset.comwithdraw_need_score &&\n    _vm.sysset.comwithdraw_need_score == 1\n      ? _vm.t(\"积分\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.sysset.comwithdraw_need_score &&\n    _vm.sysset.comwithdraw_need_score == 1\n      ? _vm.t(\"佣金\")\n      : null\n  var m5 =\n    _vm.isload && _vm.sysset.withdraw_weixin == 1 && _vm.paytype == \"微信钱包\"\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload && _vm.sysset.withdraw_aliaccount == 1 && _vm.paytype == \"支付宝\"\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload && _vm.sysset.withdraw_bankcard == 1 && _vm.paytype == \"银行卡\"\n      ? _vm.t(\"color1\")\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.sysset.withdraw_adapay == 1 &&\n    _vm.paytype == \"汇付天下银行卡\"\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload &&\n    _vm.sysset.withdraw_aliaccount_xiaoetong == 1 &&\n    _vm.paytype == \"小额通支付宝\"\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload &&\n    _vm.sysset.withdraw_bankcard_xiaoetong == 1 &&\n    _vm.paytype == \"小额通银行卡\"\n      ? _vm.t(\"color1\")\n      : null\n  var m11 =\n    _vm.isload &&\n    _vm.sysset.withdraw_aliaccount_linghuoxin == 1 &&\n    _vm.paytype == \"灵活薪支付宝\"\n      ? _vm.t(\"color1\")\n      : null\n  var m12 =\n    _vm.isload &&\n    _vm.sysset.withdraw_bankcard_linghuoxin == 1 &&\n    _vm.paytype == \"灵活薪银行卡\"\n      ? _vm.t(\"color1\")\n      : null\n  var m13 =\n    _vm.isload && _vm.sysset.withdraw_paycode == 1 && _vm.paytype == \"收款码\"\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    _vm.sysset.custom_status == 1 &&\n    _vm.sysset.custom_name &&\n    _vm.paytype == _vm.sysset.custom_name\n      ? _vm.t(\"color1\")\n      : null\n  var m15 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdraw.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdraw.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"formSubmit\">\r\n\t\t\t\t\t<view class=\"mymoney\" :style=\"{background:t('color1')}\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">我的可提现{{t('佣金')}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\"><text style=\"font-size:26rpx\">￥</text>{{userinfo.commission}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"sysset.commissionrecord_withdrawlog_show == 1\" @tap=\"goto\" data-url=\"commissionlog?st=1\"><text>提现记录</text><text class=\"iconfont iconjiantou\" style=\"font-size:20rpx\"></text></view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"userinfo.show_cash_count\">累计提现：{{userinfo.cash_yongji_total}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"userinfo.show_cash_count\">累计返现：{{userinfo.cashback_total}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content2\">\r\n\t\t\t\t\t\t\t<view class=\"item2\"><view class=\"f1\">提现金额(元)</view></view>\r\n\t\t\t\t\t\t\t<view class=\"item3\"><view class=\"f1\">￥</view><view class=\"f2\"><input class=\"input\" type=\"digit\" name=\"money\" value=\"\" placeholder=\"请输入提现金额\" placeholder-style=\"color:#999;font-size:40rpx\" @input=\"moneyinput\"></input></view></view>\r\n\t\t\t\t\t\t\t<view class=\"tips-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"item4 tips\" v-if=\"sysset.comwithdrawfee>0 || sysset.comwithdrawmin>0\">\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"sysset.comwithdrawmin>0\" style=\"margin-right:10rpx\">最低提现金额{{sysset.comwithdrawmin}}元 </text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"sysset.comwithdrawfee>0\">提现手续费{{sysset.comwithdrawfee}}% </text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item4 tips\" v-if=\"sysset.comwithdrawmul && sysset.comwithdrawmul>0\">\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"margin-right:10rpx\">提现金额需为{{sysset.comwithdrawmul}}的整数倍</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"tips\" v-if=\"sysset.comwithdraw_integer_type>0\">\r\n\t\t\t\t\t\t\t\t\t\t最低提现金额按\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"sysset.comwithdraw_integer_type==1\">个</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"sysset.comwithdraw_integer_type==2\">十</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"sysset.comwithdraw_integer_type==3\">百</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"sysset.comwithdraw_integer_type==4\">千</text>\r\n\t\t\t\t\t\t\t\t\t\t位整数提现\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"tips\" v-if=\"sysset.comwithdraw_duipeng_score_bili>0\">\r\n\t\t\t\t\t\t\t\t\t\t提现积分{{userinfo.commission_withdraw_score}}按照{{sysset.comwithdraw_duipeng_score_bili}}:1同比减少 \r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"sysset.comwithdrawbl && sysset.comwithdrawbl!=100\" class=\"tips\">提现金额的{{100-sysset.comwithdrawbl}}%将直接转到余额用于复购 </view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"sysset.comwithdraw_need_score && sysset.comwithdraw_need_score==1\" class=\"tips\">\r\n\t\t\t\t\t\t\t\t\t提现需消耗{{t('积分')}},{{sysset.commission_score_exchange_num}}{{t('积分')}}=1{{t('佣金')}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"withdrawtype\">\r\n\t\t\t\t\t\t<view class=\"f1\">选择提现方式：</view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<view class=\"item\" v-if=\"sysset.withdraw_weixin==1\" @tap.stop=\"changeradio\" data-paytype=\"微信钱包\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-weixin.png'\"/>微信钱包</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='微信钱包' ? 'background:'+t('color1')+';border:0' :''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<label class=\"item\" v-if=\"sysset.withdraw_aliaccount==1\" @tap.stop=\"changeradio\" data-paytype=\"支付宝\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-alipay.png'\"/>支付宝</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='支付宝' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"item\" v-if=\"sysset.withdraw_bankcard==1\" @tap.stop=\"changeradio\" data-paytype=\"银行卡\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>银行卡</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='银行卡' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"item\" v-if=\"sysset.withdraw_adapay==1\" @tap.stop=\"changeradio\" data-paytype=\"汇付天下银行卡\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>汇付天下银行卡</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='汇付天下银行卡' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"item\" v-if=\"sysset.withdraw_aliaccount_xiaoetong==1\" @tap.stop=\"changeradio\" data-paytype=\"小额通支付宝\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>小额通支付宝</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='小额通支付宝' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"item\" v-if=\"sysset.withdraw_bankcard_xiaoetong==1\" @tap.stop=\"changeradio\" data-paytype=\"小额通银行卡\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>小额通银行卡</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='小额通银行卡' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</label>\r\n              <label class=\"item\" v-if=\"sysset.withdraw_aliaccount_linghuoxin==1\" @tap.stop=\"changeradio\" data-paytype=\"灵活薪支付宝\">\r\n              \t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>灵活薪支付宝</view>\r\n              \t\t<view class=\"radio\" :style=\"paytype=='灵活薪支付宝' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n              </label>\r\n              <label class=\"item\" v-if=\"sysset.withdraw_bankcard_linghuoxin==1\" @tap.stop=\"changeradio\" data-paytype=\"灵活薪银行卡\">\r\n              \t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>灵活薪银行卡</view>\r\n              \t\t<view class=\"radio\" :style=\"paytype=='灵活薪银行卡' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n              </label>\r\n              <label class=\"item\" v-if=\"sysset.withdraw_paycode==1\" @tap.stop=\"changeradio\" data-paytype=\"收款码\">\r\n              \t\t<view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>收款码</view>\r\n              \t\t<view class=\"radio\" :style=\"paytype=='收款码' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n              </label>\r\n              <label class=\"item\" v-if=\"sysset.custom_status==1 && sysset.custom_name\" @tap.stop=\"changeradio\" :data-paytype=\"sysset.custom_name\">\r\n                    <view class=\"t1\"><image class=\"img\" :src=\"pre_url+'/static/img/withdraw-cash.png'\"/>{{sysset.custom_name}}</view>\r\n                    <view class=\"radio\" :style=\"paytype == sysset.custom_name ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n              </label>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"banklist\" v-if=\"selectbank && paytype=='银行卡' && bank\">\r\n\t\t\t\t\t\t\t  <view class=\"f1\">默认银行卡：{{bank.bankname}} {{bank.bankcardnum}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"t2\" @tap=\"goto\" data-url=\"/pagesA/banklist/bank?fromPage=commission\">修改</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t<button class=\"btn\" :style=\"{background:t('color1')}\" @tap=\"formSubmit\">立即提现</button>\r\n\t\t\t\t\t<view v-if=\"paytype=='支付宝'\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesExt/my/setaliaccount\">设置支付宝账户<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n\t\t\t\t\t<view v-if=\"paytype=='银行卡' && !sysset.withdraw_huifu\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesExt/my/setbankinfo\">设置银行卡账户<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n\t\t\t\t\t<view v-if=\"paytype=='银行卡' && sysset.withdraw_huifu == 1\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesExt/my/sethuifuinfo\">设置银行卡账户<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n\t\t\t\t\t<view v-if=\"paytype=='小额通支付宝'\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesExt/my/setaliaccount\">设置支付宝账户<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n\t\t\t\t\t<view v-if=\"paytype=='小额通银行卡'\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesExt/my/setbankinfo\">设置银行卡账户<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n          <view v-if=\"paytype=='灵活薪支付宝'\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesExt/my/setaliaccount\">设置支付宝账户<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n          <view v-if=\"paytype=='灵活薪银行卡'\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesExt/my/setbankinfo\">设置银行卡账户<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n          <view v-if=\"paytype=='收款码'\" class=\"textbtn\" @tap=\"goto\" data-url=\"/pagesB/my/setpaycode\">设置收款码<image :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n          <view v-if=\"paytype == sysset.custom_name\" style=\"width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center\" @tap=\"goto\" data-url=\"/pagesB/my/setcustomaccount\">设置{{sysset.custom_name}}账户<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t</form>\r\n\t\t\t<view class=\"withdraw_desc\" v-if=\"sysset.withdraw_desc\">\r\n\t\t\t\t\t<view class=\"title\">说明</view>\r\n\t\t\t\t\t<textarea :value=\"sysset.withdraw_desc\"></textarea>\r\n\t\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n      userinfo: [],\r\n      money: 0,\r\n      sysset: false,\r\n      paytype: '微信钱包',\r\n\t\t\ttmplids:[],\r\n\t\t\tbid:0,\r\n\t\t\tselectbank:false,\r\n\t\t\tbank:[],\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt && this.opt.bid){\r\n\t\t\tthis.bid = this.opt.bid || 0;\r\n\t\t}\r\n\t\tvar that = this;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAgent/commissionWithdraw', {bid:that.bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: that.t('佣金') + '提现'\r\n\t\t\t\t});\r\n\t\t\t\tvar sysset = res.sysset;\r\n\t\t\t\tthat.sysset = sysset;\r\n\t\t\t\tthat.tmplids = res.tmplids;\r\n\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\tvar paytype = '';\r\n\t\t\t\tif (sysset.withdraw_weixin == 1) {\r\n\t\t\t\t\tpaytype = '微信钱包';\r\n\t\t\t\t}\r\n\t\t\t\tif ((!sysset.withdraw_weixin || sysset.withdraw_weixin == 0) && sysset.withdraw_aliaccount == 1) {\r\n\t\t\t\t\tpaytype = '支付宝';\r\n\t\t\t\t}\r\n\t\t\t\tif ((!sysset.withdraw_weixin || sysset.withdraw_weixin == 0) && (!sysset.withdraw_aliaccount || sysset.withdraw_aliaccount == 0) && sysset.withdraw_bankcard==1) {\r\n\t\t\t\t\tpaytype = '银行卡';\r\n\t\t\t\t}\r\n\t\t\t\tif(sysset.withdraw_aliaccount_xiaoetong == 1 && paytype == ''){\r\n\t\t\t\t\tpaytype = '小额通支付宝';\r\n\t\t\t\t}\r\n\t\t\t\tif(sysset.withdraw_bankcard_xiaoetong == 1 && paytype == ''){\r\n\t\t\t\t\tpaytype = '小额通银行卡';\r\n\t\t\t\t}\r\n        if(sysset.withdraw_aliaccount_linghuoxin == 1 && paytype == ''){\r\n        \tpaytype = '灵活薪支付宝';\r\n        }\r\n        if(sysset.withdraw_bankcard_linghuoxin == 1 && paytype == ''){\r\n        \tpaytype = '灵活薪银行卡';\r\n        }\r\n        if(sysset.withdraw_paycode == 1 && paytype == ''){\r\n        \tpaytype = '收款码';\r\n        }\r\n\t\t\t\tif(sysset.withdraw_paycode == 1 && paytype == ''){\r\n\t\t\t\t\tpaytype = sysset.custom_name;\r\n\t\t\t\t}\r\n\t\t\t\tthat.paytype = paytype;\r\n\t\t\t\tthat.selectbank = res.selectbank\r\n\t\t\t\tthat.bank = res.bank\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n    moneyinput: function (e) {\r\n      var usermoney = parseFloat(this.userinfo.commission);\r\n      var money = parseFloat(e.detail.value);\r\n      if (money < 0) {\r\n        app.error('必须大于0');\r\n      } else if (money > usermoney) {\r\n        app.error('可提现' + this.t('佣金') + '不足');\r\n      }\r\n\t\t\tthis.money = money;\r\n    },\r\n    changeradio: function (e) {\r\n      var that = this;\r\n      var paytype = e.currentTarget.dataset.paytype;\r\n      that.paytype = paytype;\r\n    },\r\n    formSubmit: function () {\r\n\t\t\t\r\n      var that = this;\r\n      var usermoney = parseFloat(this.userinfo.commission);\r\n      var withdrawmin = parseFloat(this.sysset.withdrawmin); //var formdata = e.detail.value;\r\n\t\t\tvar comwithdrawmul = parseFloat(this.sysset.comwithdrawmul);\r\n\r\n      var money = parseFloat(that.money);\r\n      var paytype = this.paytype;\r\n\r\n\t  if (paytype == '') {\r\n\t    app.error('请选择提现方式');\r\n\t    return;\r\n\t  }\r\n      if (isNaN(money) || money <= 0) {\r\n        app.error('提现金额必须大于0');\r\n        return;\r\n      }\r\n      if (withdrawmin > 0 && money < withdrawmin) {\r\n        app.error('提现金额必须大于¥' + withdrawmin);\r\n        return;\r\n      }\r\n\t\t\tif(!isNaN(comwithdrawmul) && comwithdrawmul>0){\r\n\t\t\t\tif(!Number.isInteger((money*100)/(comwithdrawmul*100))){\r\n\t\t\t\t\tapp.error('提现金额需为' + comwithdrawmul+ '的整数倍');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n      if (money > usermoney) {\r\n        app.error(that.t('佣金') + '不足');\r\n        return;\r\n      }\r\n\t  //提现控制整倍数\r\n\t  if (this.sysset.comwithdraw_integer_type) {\r\n\t\t  var comwithdraw_integer_type = this.sysset.comwithdraw_integer_type; \r\n\t\t  if(comwithdraw_integer_type == 1 && (money % 1) != 0){\t\t\t  \r\n\t\t\t  app.error('请输入整数');\r\n\t\t\t  return;\r\n\t\t  }else if(comwithdraw_integer_type == 2 && (money % 10) != 0){\r\n\t\t\t  app.error('请输入10整倍整数');\r\n\t\t\t  return;\r\n\t\t  }else if(comwithdraw_integer_type == 3 && (money % 100) != 0){\r\n\t\t\t  app.error('请输入100整倍整数');\r\n\t\t\t  return;\r\n\t\t  }else if(comwithdraw_integer_type == 4 && (money % 1000) != 0){\r\n\t\t\t  app.error('请输入1000整倍整数');\r\n\t\t\t  return;\r\n\t\t  }\r\n      }\r\n\t  //计算提现积分\r\n\t  if (this.sysset.comwithdraw_duipeng_score_bili) {\r\n\t  \t\t  var comwithdraw_duipeng_score_bili = this.sysset.comwithdraw_duipeng_score_bili; \r\n\t  \t\t  var commission_withdraw_score = this.userinfo.commission_withdraw_score; \r\n\t\t\t  var comwithdraw_integer_type = this.sysset.comwithdraw_integer_type; \r\n\t\t\t  var use_commission_withdraw_score = commission_withdraw_score / comwithdraw_duipeng_score_bili\r\n\t  \t\t  if(use_commission_withdraw_score < money){\r\n\t\t\t\t  var use_money = money;\r\n\t\t\t\t  if(comwithdraw_integer_type == 1){\r\n\t\t\t\t  \t\tuse_money =  Math.floor(use_commission_withdraw_score);\r\n\t\t\t\t  }else if(comwithdraw_integer_type == 2){\r\n\t\t\t\t  \t\tuse_money =  Math.floor(use_commission_withdraw_score/10) * 10;\r\n\t\t\t\t  }else if(comwithdraw_integer_type == 3){\r\n\t\t\t\t  \t\tuse_money =  Math.floor(use_commission_withdraw_score/100) * 100;\r\n\t\t\t\t  }else if(comwithdraw_integer_type == 4){\r\n\t\t\t\t  \t\tuse_money =  Math.floor(use_commission_withdraw_score/1000) * 1000;\r\n\t\t\t\t  }\r\n\t  \t\t\t  app.error('提现积分不足，最高可提现'+use_money);\r\n\t  \t\t\t  return;\r\n\t  \t\t  }\r\n\t  }\r\n\r\n      if (paytype == '支付宝' && !this.userinfo.aliaccount) {\r\n        app.alert('请先设置支付宝账号', function () {\r\n          app.goto('/pagesExt/my/setaliaccount');\r\n        });\r\n        return;\r\n      }\r\n\t\t\tif(this.selectbank){\r\n\t\t\t\t\tif (paytype == '银行卡' && !this.bank){\r\n\t\t\t\t\t\tapp.alert('请先设置完整银行卡信息', function () {\r\n\t\t\t\t\t\t  app.goto('/pagesA/banklist/bankadd');\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\tif (paytype == '银行卡' && (!this.userinfo.bankname || !this.userinfo.bankcarduser || !this.userinfo.bankcardnum)) {\r\n\t\t\t\t  app.alert('请先设置完整银行卡信息', function () {\r\n\t\t\t\t    app.goto('/pagesExt/my/setbankinfo');\r\n\t\t\t\t  });\r\n\t\t\t\t  return;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(paytype == '汇付天下银行卡' && (this.userinfo.to_set_adapay && this.userinfo.to_set_adapay==1)) {\r\n\t\t\t\tapp.alert('请先设置汇付天银行卡信息', function () {\r\n\t\t\t\t\tapp.goto('/pagesExt/my/setadapayinfo');\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t//自定义提现方式\r\n\t\t\tif(paytype == this.sysset.custom_name && (!this.userinfo.customaccountname || !this.userinfo.customaccount || !this.userinfo.customtel)){\r\n\t\t\t  app.alert('请先设置'+ this.sysset.custom_name +'账户信息', function () {\r\n\t\t\t    app.goto('/pagesB/my/setcustomaccount');\r\n\t\t\t  });\r\n\t\t\t  return;\r\n\t\t\t}\r\n\t\t\tvar wx_max_money = parseFloat(that.sysset.wx_max_money);\r\n\t\t\tif(paytype=='微信钱包' && !this.userinfo.realname && money>=wx_max_money){\r\n\t\t\t\t  app.alert('请先设置姓名', function () {\r\n\t\t\t\t\tapp.goto('/pagesExt/my/setrealname');\r\n\t\t\t\t  });\r\n\t\t\t\t  return;\r\n\t\t\t}\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post('ApiAgent/commissionwithdraw', {money: money,paytype: paytype,bid:that.bid}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.error(data.msg);\r\n          return;\r\n        } else {\r\n\t\t  if(data.need_confirm==1 && data.id){\r\n\t\t  \t//需要用户主动确认收款\r\n\t\t\tvar redirect_url = '';\r\n\t\t\tif(that.sysset.commissionrecord_withdrawlog_show == 1){\r\n\t\t\t\tredirect_url = '/activity/commission/commissionlog?st=1';\r\n\t\t\t}\r\n\t\t  \treturn that.shoukuan(data.id,'member_commission_withdrawlog',redirect_url);\r\n\t\t  }else{\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  if(that.sysset.commissionrecord_withdrawlog_show == 1){\r\n\t\t\t\t  that.subscribeMessage(function () {\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t  app.goto('commissionlog?st=1');\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t  });\r\n\t\t\t  }else{\r\n\t\t\t\t that.getdata();\r\n\t\t\t\t //app.goto('/activity/commission/withdraw?bid='+that.bid)\r\n\t\t\t  }\r\n\t\t  }\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{display:flex;flex-direction:column;padding-bottom: 40rpx;}\r\n.mymoney{width:94%;margin:20rpx 3%;border-radius: 10rpx 56rpx 10rpx 10rpx;position:relative;display:flex;flex-direction:column;padding:70rpx 0}\r\n.mymoney .f1{margin:0 0 0 60rpx;color:rgba(255,255,255,0.8);font-size:24rpx;}\r\n.mymoney .f2{margin:20rpx 0 0 60rpx;color:#fff;font-size:64rpx;font-weight:bold}\r\n.mymoney .f3{height:56rpx;padding:0 10rpx 0 20rpx;border-radius: 28rpx 0px 0px 28rpx;background:rgba(255,255,255,0.2);font-size:20rpx;font-weight:bold;color:#fff;display:flex;align-items:center;position:absolute;top:94rpx;right:0}\r\n\r\n.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}\r\n.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}\r\n.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}\r\n.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}\r\n\r\n.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}\r\n.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}\r\n\r\n.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}\r\n.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}\r\n.content2 .item3 .f2{display:flex;align-items:center;font-size:60rpx;color:#333333;font-weight:bold}\r\n.content2 .item3 .f2 .input{font-size:60rpx;height:100rpx;line-height:100rpx;}\r\n.content2 .item4{display:flex;}\r\n\r\n.withdrawtype{width:94%;margin:20rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}\r\n.withdrawtype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}\r\n\r\n\r\n.withdrawtype .f2{padding:0 30rpx}\r\n.withdrawtype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}\r\n.withdrawtype .f2 .item:last-child{border-bottom:0}\r\n.withdrawtype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#333}\r\n.withdrawtype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}\r\n\r\n.withdrawtype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\r\n.withdrawtype .f2 .item .radio .radio-img{width:100%;height:100%}\r\n\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n.textbtn {width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center}\r\n.textbtn image {width:30rpx;height:30rpx}\r\n\r\n.withdraw_desc{padding: 30rpx;}\r\n.withdraw_desc .title{font-size: 30rpx;color: #5E5E5E;font-weight: bold;padding: 10rpx 0;}\r\n.withdraw_desc textarea{width: 100%; line-height: 46rpx;font-size: 24rpx;color: #222222;}\r\n.tips-box{width:94%;margin:0 3%;padding: 20rpx 0;border-top:1px solid #F0F0F0;}\r\n.tips{color:#8C8C8C;font-size:28rpx;line-height: 50rpx;}\r\n\r\n.banklist{ padding:0 20rpx 20rpx;margin-left: 10rpx;display: flex; width: 100%; }\r\n.banklist .t2{ line-height: 90rpx;width: 80rpx;text-align: right;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdraw.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdraw.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370799\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}