{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/poster.vue?8cb8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/poster.vue?014a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/poster.vue?025a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/poster.vue?6c48", "uni-app:///activity/commission/poster.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/poster.vue?7fb9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/poster.vue?668c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "poster", "guize", "posterid", "posterlist", "postercount", "shopset", "is_show_title", "shareTitle", "sharePic", "shareLink", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "path", "imageUrl", "methods", "shareChange", "query", "scene", "currentpath", "getdata", "app", "that", "pic", "link", "changeposter", "index", "sharemp", "uni", "success", "duration", "icon", "fail", "shareapp", "itemList", "sharedata", "sharelink"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8Bx1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;QACAA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;;IACAC;MACA;MACA;MACAC;MACAA;QAAApB;MAAA;QACAoB;QACA;UACAA;QACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;UAAAV;UAAAW;UAAAC;QAAA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACAA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACAJ;MACAA;IACA;IACAK;MACA;MACA;MACA;MACAC;QACAlC;QACAmC;UACAD;YACAhB;YACAkB;YACAC;UACA;QACA;QACAC;UACAJ;YACAhB;YACAkB;YACAC;UACA;QACA;MACA;IACA;IACAE;MACAL;QACAM;QACAL;UACA;YACA;YACA;cACAX;YACA;YACA;YACAiB;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACAP;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxNA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/poster.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/poster.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./poster.vue?vue&type=template&id=40618f7f&\"\nvar renderjs\nimport script from \"./poster.vue?vue&type=script&lang=js&\"\nexport * from \"./poster.vue?vue&type=script&lang=js&\"\nimport style0 from \"./poster.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/poster.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=template&id=40618f7f&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.is_show_title ? _vm.t(\"佣金\") : null\n  var m1 = _vm.isload ? _vm.getplatform() : null\n  var m2 = _vm.isload && m1 == \"app\" ? _vm.t(\"color2\") : null\n  var m3 = _vm.isload && !(m1 == \"app\") ? _vm.getplatform() : null\n  var m4 = _vm.isload && !(m1 == \"app\") && m3 == \"mp\" ? _vm.t(\"color2\") : null\n  var m5 =\n    _vm.isload && !(m1 == \"app\") && !(m3 == \"mp\") ? _vm.getplatform() : null\n  var m6 =\n    _vm.isload && !(m1 == \"app\") && !(m3 == \"mp\") && m5 == \"h5\"\n      ? _vm.t(\"color2\")\n      : null\n  var m7 =\n    _vm.isload && !(m1 == \"app\") && !(m3 == \"mp\") && !(m5 == \"h5\")\n      ? _vm.t(\"color2\")\n      : null\n  var m8 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<block v-if=\"postercount > 1\">\n\t\t\t<view class=\"arrowleft\" @tap=\"changeposter\" data-type=\"-1\"><text class=\"iconfont iconjiantou\" ></text></view>\n\t\t\t<view class=\"arrowright\" @tap=\"changeposter\" data-type=\"1\"><text class=\"iconfont iconjiantou\"></text></view>\n\t\t</block>\n\t\t<image :src=\"poster\" class=\"sjew-img\" @tap=\"previewImage\" :data-url=\"poster\" mode=\"widthFix\"></image>\n\t\t<view class=\"sjew-box\">\n\t\t\t<view class=\"sjew-tle flex-y-center\" v-if=\"is_show_title\">如何推荐好友拿{{t('佣金')}}</view>\n\t\t\t<view class=\"sjew-sp1\"><text>{{guize}}</text></view>\n\t\t</view>\n\t\t<view class=\"sjew-he\"></view>\n\t\t<view class=\"sjew-bottom\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t<view class=\"sjew-bottom-content\">\n\t\t\t\t<view class=\"sjew-bot-a1\" :style=\"{background:t('color2')}\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">分享链接</view>\n\t\t\t\t<view class=\"sjew-bot-a1\" :style=\"{background:t('color2')}\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">分享链接</view>\n\t\t\t\t<view class=\"sjew-bot-a1\" :style=\"{background:t('color2')}\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">分享链接</view>\n\t\t\t\t<button class=\"sjew-bot-a1\" :style=\"{background:t('color2')}\" open-type=\"share\" v-else>分享链接</button>\n\t\t\t\t<view class=\"sjew-bot-a2\" :style=\"{background:t('color1')}\" @tap=\"previewImage\" :data-url=\"poster\">分享图片</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tposter:'',\n\t\t\tguize:'',\n\t\t\tposterid:'',\n\t\t\tposterlist:[],\n\t\t\tpostercount:1,\n\t\tshopset: [],\n\t\tis_show_title:1,\r\n\t\tshareTitle:'',\r\n\t\tsharePic:'',\r\n\t\tshareLink:''\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\r\n\t\tthis.shareChange();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onShareAppMessage: function (res){\n    var that = this;\n    return {\n      title: that.shareTitle,\n      path: that.shareLink,\n      imageUrl: that.sharePic\n    };\n  },\n  methods: {\r\n\t\tshareChange(){\r\n\t\t\tvar pages = getCurrentPages(); //获取加载的页面\r\n\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n\t\t\tvar currenturl = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \r\n\t\t\tvar query = ''\r\n\t\t\tvar opt = this.opt;\r\n\t\t\tif(this.opt && this.opt.id){\r\n\t\t\t\tquery+='?id='+this.opt.id\r\n\t\t\t}else if(this.opt && this.opt.cid){\r\n\t\t\t\tquery+='?cid='+this.opt.cid\r\n\t\t\t}else if(this.opt && this.opt.gid){\r\n\t\t\t\tquery+='?gid='+this.opt.gid\r\n\t\t\t}else if(this.opt && this.opt.bid){\r\n\t\t\t\tquery+='?bid='+this.opt.bid\r\n\t\t\t}\r\n\t\t\tvar scene = [];\r\n\t\t\tfor(var i in opt){\r\n\t\t\t\tif(i != 'pid' && i != 'scene'){\r\n\t\t\t\t\tscene.push(i+'_'+opt[i]);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(app.globalData.mid){\r\n\t\t\t\tscene.push('pid_'+app.globalData.mid);\r\n\t\t\t}\r\n\t\t\tvar scenes = scene.join('-');\r\n\t\t\tvar currentpath = '/pages/index/index';\r\n\t\t\tif(scenes){\r\n\t\t\t\tcurrentpath = currentpath + \"?scene=\"+scenes + '&t='+parseInt((new Date().getTime())/1000);\r\n\t\t\t}\r\n\t\t\tthis.shareLink = currentpath;\r\n\t\t\tvar currentfullurl = currenturl+query\r\n\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\tif(sharelist){\r\n\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\tif((sharelist[i]['is_rootpath']==1 && sharelist[i]['indexurl'] == currenturl) || (!sharelist[i]['is_rootpath'] && sharelist[i]['indexurl'] == currentfullurl)){\r\n\t\t\t\t\t\tthis.shareTitle = sharelist[i].title;\r\n\t\t\t\t\t\tthis.sharePic = sharelist[i].pic;\r\n\t\t\t\t\t\tthis.shareLink = sharelist[i].url ? sharelist[i].url : currentpath; //分享链接，不填写代表首页\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tvar posterid = that.posterid;\n\t\t\tapp.showLoading('海报生成中');\n\t\t\tapp.get('ApiAgent/poster', {posterid:posterid}, function (res) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t}\n\t\t\t\tthat.poster = res.poster;\n\t\t\t\tthat.guize = res.guize;\n\t\t\t\tthat.posterid = res.posterid;\n\t\t\t\tthat.posterlist = res.posterlist;\n\t\t\t\tthat.postercount = res.postercount;\n\t\t\t\tthat.is_show_title = res.is_show_title;\n\t\t\t\tthat.loaded({title:app.globalData.initdata.name,pic:app.globalData.initdata.logo,link:app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ app.globalData.initdata.indexurl + '?scene=pid_' + app.globalData.mid});\n\t\t\t});\n\t\t},\n    changeposter: function (e) {\n\t\t\tvar type = parseInt(e.currentTarget.dataset.type)\n      var that = this;\n\t\t\tvar posterlist = that.posterlist;\n\t\t\tvar posterid = that.posterid;\n\t\t\tvar index = 0;\n\t\t\tfor(var i in posterlist){\n\t\t\t\tif(posterlist[i].id == posterid){\n\t\t\t\t\tindex = i;\n\t\t\t\t}\n\t\t\t}\n\t\t\tindex = parseInt(index) + type;\n\t\t\tif(index == posterlist.length){\n\t\t\t\tindex = 0;\n\t\t\t}\n\t\t\tif(index < 0){\n\t\t\t\tindex = posterlist.length - 1;\n\t\t\t}\n\t\t\tthat.posterid = posterlist[index].id;\n\t\t\tthat.getdata();\n    },\n\t\tsharemp:function(){\n\t\t\tif(app.globalData.platform == 'mp') return app.error('点击右上角发送给好友或分享到朋友圈');\n\t\t\tlet that = this;\n\t\t\tlet shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+that.shareLink;\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: shareLink,\n\t\t\t\tsuccess: function() {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '复制成功,快去分享吧！',\n\t\t\t\t\t\tduration: 3000,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: function(err) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\t\tduration: 2000,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tshareapp:function(){\n\t\t\tuni.showActionSheet({\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\n        success: function (res){\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\n\t\t\t\t\t\tif (res.tapIndex == 1) {\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar sharedata = {};\n\t\t\t\t\t\tsharedata.provider = 'weixin';\n\t\t\t\t\t\tsharedata.type = 0;\n\t\t\t\t\t\tsharedata.scene = scene;\n\t\t\t\t\t\tsharedata.title = app.globalData.initdata.name;\n\t\t\t\t\t\tsharedata.summary = app.globalData.initdata.desc;\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+that.shareLink+ '?scene=pid_' + app.globalData.mid;\n\t\t\t\t\t\tsharedata.imageUrl = app.globalData.initdata.logo;\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\n\t\t\t\t\t\tif(sharelist){\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == app.globalData.initdata.indexurl){\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.share(sharedata);\n\t\t\t\t\t}\n        }\n      });\n\t\t}\n  }\n};\n</script>\n<style>\n.sjew-img{ width:100%; display: block;}\n.sjew-box{ width:100%; background:#FFF; margin-bottom:120rpx;padding-bottom:10px;margin-top:20rpx}\n.sjew-tle{ width:100%;padding:0 2%; border-bottom:1px #E6E6E6 solid;  height:100rpx;line-height:100rpx; color:#111;}\n.sjew-ic1{width:46rpx;height:46rpx;display: block;margin-right:10rpx;}\n.sjew-sp1{ width:100%; padding:0 20rpx; color:#666; margin-top:20rpx; }\n.sjew-sp2{ width:78%; color:#999; margin-top:20rpx; }\n.sjew-sp3{ width:96%; margin:20rpx 2%;background:#fe924a; padding:16rpx 2%; color:#fff;  box-sizing: border-box;}\n\n.sjew-bottom{ width:94%; position:fixed; bottom:0px;height:110rpx;line-height:110rpx;border-top:1px #ececec solid;background:#fff;display:flex;padding:15rpx 3%;box-sizing:content-box}\n.sjew-bottom-content{height:80rpx;line-height:80rpx;width:100%;border-radius:50rpx;display:flex;border-radius:45rpx;overflow:hidden}\n.sjew-bot-a1{flex:1;height:80rpx;line-height:80rpx;text-align:center;color:#fff;}\n.sjew-bot-a2{flex:1;height:80rpx;line-height:80rpx;text-align:center;color:#fff;}\n.sjew-he{width: 100%;height:20rpx;}\n\n.arrowleft{position:fixed;top:calc(var(--window-top) + 140rpx);display:flex;justify-content:center;z-index:8;transform: rotateY(180deg);background:rgba(0,0,0,0.3);color:#fff;border-radius:50%;left:40rpx;width:60rpx;height:60rpx;line-height:60rpx;text-align:center;}\n.arrowright{position:fixed;top:calc(var(--window-top) + 140rpx);display:flex;justify-content:center;z-index:8;background:rgba(0,0,0,0.3);color:#fff;border-radius:50%;right:40rpx;width:60rpx;height:60rpx;line-height:60rpx;text-align:center}\n\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370868\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}