{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/index.vue?86e4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/index.vue?3796", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/index.vue?90fc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/index.vue?b744", "uni-app:///activity/commission/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/index.vue?32f1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/index.vue?b5c1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "hiddenmodalput", "userinfo", "count", "count1", "count2", "count3", "count4", "comwithdraw", "canwithdraw", "money", "count0", "countdqr", "commission2money", "showfenhong", "showMendianOrder", "hastouzifenhong", "has<PERSON><PERSON>", "hasareafenhong", "has<PERSON><PERSON>fenhong", "showYeji", "fxjiesuantime", "teamyeji_show", "teamnum_show", "gongxianfenhong_show", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commission_money_exchange_num", "hasfen<PERSON>_huiben", "hasbusinessteamfenhong", "commission_butie", "<PERSON><PERSON><PERSON><PERSON>", "xycontent", "fhtype_arr", "is_end", "commission_to_money_rate", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "frontColor", "backgroundColor", "sequentialRequests", "i", "console", "getdata2", "fhtype", "resolve", "cancel", "tomoney", "tomonenyconfirm", "done", "exchangeSubmit", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "getunit", "rtext"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnTA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyiBv1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAE;UACAC;QACA;QACAD;UACAE;UACAC;QACA;QACAL;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACAA;QACA;UACAA;UACAA;QACA;UACAA;QACA;MAEA;IACA;IACA;IACAM;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAN;gBACAP;gBACAc;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;gBAAA,OACAR;cAAA;gBAFAO;gBAAA;gBAAA;cAAA;gBAIAP;gBACAQ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MAAA;QAAA;MAAA;MAAA;IAAA;IACAC;MACA;MACAD;MACA;QACAP;UAAAS;QAAA;UACA;UACA;UACA;YACA;YACAV;UACA;UACA;YACA;YACAA;UACA;UACA;YACA;YACAA;UACA;UACA;YACA;YACAA;UACA;UACA;YACA;YACAA;UACA;UACA;YACA;YACAA;UACA;UACA;YACAA;UACA;UACAW;QACA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAN;MACA;MACA;MACA;QACAP;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACA;QACA;UACAA;YACAc;YACAf;UACA;QACA;MACA;QACAe;QACAf;MACA;IACA;IACAgB;MACA;MACAf;MACAA;QAAA/B;MAAA;QACA+B;QACA;UACAA;QACA;UACAD;UACAC;UACAgB;YACAjB;UACA;QACA;MACA;IACA;IACAkB;MACA;MACAjB;IACA;IACAkB;MAAA;MACA;MACA;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpwBA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=181aa910&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=181aa910&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m2 =\n    _vm.isload && _vm.set && _vm.set.parent_show == 1 ? _vm.t(\"推荐人\") : null\n  var m3 = _vm.isload ? _vm.t(\"佣金\") : null\n  var m4 = _vm.isload ? _vm.t(\"佣金\") : null\n  var m5 = _vm.isload ? _vm.getunit(\"佣金单位\") : null\n  var m6 = _vm.isload && _vm.comwithdraw == 1 ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && _vm.comwithdraw == 1 ? _vm.t(\"color1rgb\") : null\n  var m8 =\n    _vm.isload && !(_vm.comwithdraw == 1) && _vm.commission2money == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload && !(_vm.comwithdraw == 1) && _vm.commission2money == \"1\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m10 =\n    _vm.isload && !(_vm.comwithdraw == 1) && _vm.commission2money == \"1\"\n      ? _vm.t(\"余额\")\n      : null\n  var m11 = _vm.isload ? _vm.t(\"佣金\") : null\n  var m12 = _vm.isload ? _vm.getunit(\"佣金单位\") : null\n  var m13 = _vm.isload ? _vm.getunit(\"佣金单位\") : null\n  var m14 = _vm.isload && _vm.hasfenhong ? _vm.t(\"股东分红\") : null\n  var m15 = _vm.isload && _vm.hasfenhong ? _vm.getunit(\"佣金单位\") : null\n  var m16 = _vm.isload && _vm.hasfenhong ? _vm.getunit(\"佣金单位\") : null\n  var m17 =\n    _vm.isload && _vm.hasfenhong && _vm.userinfo.fenhong_max_show\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m18 =\n    _vm.isload && _vm.hasfenhong && _vm.userinfo.fenhong_max_show\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m19 =\n    _vm.isload && _vm.hasfenhong && _vm.gongxianfenhong_show == 1\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m20 = _vm.isload && _vm.hasfenhong_huiben ? _vm.t(\"回本股东分红\") : null\n  var m21 = _vm.isload && _vm.hasfenhong_huiben ? _vm.getunit(\"佣金单位\") : null\n  var m22 = _vm.isload && _vm.hasfenhong_huiben ? _vm.getunit(\"佣金单位\") : null\n  var m23 =\n    _vm.isload && _vm.hasteamfenhong && _vm.set.teamfenhong_show\n      ? _vm.t(\"团队分红\")\n      : null\n  var m24 =\n    _vm.isload && _vm.hasteamfenhong && _vm.set.teamfenhong_show\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m25 =\n    _vm.isload && _vm.hasteamfenhong && _vm.set.teamfenhong_show\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m26 =\n    _vm.isload &&\n    _vm.hasbusinessteamfenhong &&\n    _vm.set.business_teamfenhong_show\n      ? _vm.t(\"商家团队分红\")\n      : null\n  var m27 =\n    _vm.isload &&\n    _vm.hasbusinessteamfenhong &&\n    _vm.set.business_teamfenhong_show\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m28 =\n    _vm.isload &&\n    _vm.hasbusinessteamfenhong &&\n    _vm.set.business_teamfenhong_show\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m29 =\n    _vm.isload && _vm.hasteamshouyi && _vm.set.teamshouyi_show\n      ? _vm.t(\"团队收益\")\n      : null\n  var m30 =\n    _vm.isload && _vm.hasteamshouyi && _vm.set.teamshouyi_show\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m31 = _vm.isload && _vm.hasareafenhong ? _vm.t(\"区域代理分红\") : null\n  var m32 = _vm.isload && _vm.hasareafenhong ? _vm.getunit(\"佣金单位\") : null\n  var m33 = _vm.isload && _vm.hasareafenhong ? _vm.getunit(\"佣金单位\") : null\n  var m34 =\n    _vm.isload && _vm.set.show_myyeji == 1 ? _vm.getunit(\"佣金单位\") : null\n  var m35 =\n    _vm.isload && _vm.set.show_myyeji == 1 ? _vm.getunit(\"佣金单位\") : null\n  var m36 =\n    _vm.isload &&\n    _vm.set.show_myyeji == 1 &&\n    _vm.userinfo.showyejicommission &&\n    _vm.userinfo.yeji_commission > 0\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m37 =\n    _vm.isload && _vm.userinfo.showxiaoshouyeji ? _vm.getunit(\"佣金单位\") : null\n  var m38 =\n    _vm.isload && _vm.userinfo.showxiaoshouyeji ? _vm.getunit(\"佣金单位\") : null\n  var m39 =\n    _vm.isload && _vm.userinfo.showxiaoshouyeji ? _vm.getunit(\"佣金单位\") : null\n  var m40 =\n    _vm.isload &&\n    _vm.userinfo.showxiaoshouyeji &&\n    _vm.userinfo.yeji_commission > 0\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m41 =\n    _vm.isload &&\n    _vm.hasteamfenhong &&\n    (_vm.teamnum_show == 1 || _vm.teamyeji_show == 1)\n      ? _vm.t(\"我的团队\")\n      : null\n  var g0 =\n    _vm.isload &&\n    _vm.hasteamfenhong &&\n    (_vm.teamnum_show == 1 || _vm.teamyeji_show == 1)\n      ? _vm.teamyeji_show == 1 && _vm.userinfo.hasOwnProperty(\"teamyeji_prosum\")\n      : null\n  var m42 =\n    _vm.isload &&\n    _vm.hasteamfenhong &&\n    (_vm.teamnum_show == 1 || _vm.teamyeji_show == 1) &&\n    _vm.teamyeji_show == 1\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m43 =\n    _vm.isload &&\n    _vm.hasteamfenhong &&\n    (_vm.teamnum_show == 1 || _vm.teamyeji_show == 1) &&\n    _vm.set.show_teamyeji_search == 1\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m44 = _vm.isload && _vm.hastouzifenhong ? _vm.t(\"投资分红\") : null\n  var m45 = _vm.isload && _vm.hastouzifenhong ? _vm.getunit(\"佣金单位\") : null\n  var m46 = _vm.isload && _vm.hastouzifenhong ? _vm.getunit(\"佣金单位\") : null\n  var m47 = _vm.isload && _vm.hastouzifenhong ? _vm.getunit(\"佣金单位\") : null\n  var m48 = _vm.isload && _vm.commission_butie ? _vm.t(\"分销补贴\") : null\n  var m49 = _vm.isload && _vm.commission_butie ? _vm.getunit(\"佣金单位\") : null\n  var m50 = _vm.isload && _vm.commission_butie ? _vm.getunit(\"佣金单位\") : null\n  var m51 =\n    _vm.isload && _vm.userinfo.show_team_yeji_fenhong\n      ? _vm.t(\"团队业绩阶梯奖\")\n      : null\n  var m52 =\n    _vm.isload && _vm.userinfo.show_team_yeji_fenhong\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m53 =\n    _vm.isload && _vm.userinfo.show_team_yeji_fenhong\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m54 =\n    _vm.isload && _vm.userinfo.show_team_yeji_fenhong\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m55 =\n    _vm.isload && _vm.userinfo.show_team_yeji_fenhong\n      ? _vm.getunit(\"佣金单位\")\n      : null\n  var m56 =\n    _vm.isload && _vm.comwithdraw == 1 && _vm.commission2money == \"1\"\n      ? _vm.t(\"佣金\")\n      : null\n  var m57 =\n    _vm.isload && _vm.comwithdraw == 1 && _vm.commission2money == \"1\"\n      ? _vm.t(\"余额\")\n      : null\n  var m58 = _vm.isload && _vm.set.fxorder_show == 1 ? _vm.t(\"分销订单\") : null\n  var m59 = _vm.isload && _vm.set.commissionlog_show ? _vm.t(\"佣金\") : null\n  var m60 = _vm.isload && _vm.set.commissionrecord_show ? _vm.t(\"佣金\") : null\n  var m61 = _vm.isload && _vm.showMendianOrder ? _vm.t(\"佣金\") : null\n  var m62 = _vm.isload ? _vm.t(\"佣金\") : null\n  var m63 = _vm.isload ? _vm.t(\"余额\") : null\n  var m64 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1\") : null\n  var m65 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        g0: g0,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        m47: m47,\n        m48: m48,\n        m49: m49,\n        m50: m50,\n        m51: m51,\n        m52: m52,\n        m53: m53,\n        m54: m54,\n        m55: m55,\n        m56: m56,\n        m57: m57,\n        m58: m58,\n        m59: m59,\n        m60: m60,\n        m61: m61,\n        m62: m62,\n        m63: m63,\n        m64: m64,\n        m65: m65,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"banner\" :style=\"{background:'linear-gradient(180deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0) 100%)'}\">\r\n\t\t</view>\r\n\t\t<view class=\"user\">\r\n\t\t\t<image :src=\"userinfo.headimg\" background-size=\"cover\"/>\r\n\t\t\t<view class=\"info\" v-if=\"set && set.parent_show == 1\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"nickname\">{{userinfo.nickname}}</view>\r\n\t\t\t\t\t<view>{{t('推荐人')}}：{{userinfo.pid > 0 ? userinfo.pnickname : '无'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info\" v-else>\r\n\t\t\t\t <text class=\"nickname\">{{userinfo.nickname}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"contentdata\">\r\n\t\t\t<view class=\"data\">\r\n\t\t\t\t<view class=\"data_title flex-y-center\"><image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m1.png'\"/>我的{{t('佣金')}}</view>\r\n\t\t\t\t<view class=\"data_text\">\r\n\t\t\t\t\t{{comwithdraw==1?'可提现':'剩余'}}{{t('佣金')}}({{getunit('佣金单位')}})\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_price flex-y-center flex-bt\">\r\n\t\t\t\t\t<text @tap=\"goto\" data-url=\"../order/shoporder?st=0\">{{userinfo.commission}}</text>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"withdraw\" v-if=\"comwithdraw==1\" class=\"data_btn flex-xy-center\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">去提现<image :src=\"pre_url+'/static/imgsrc/commission_dw.png'\"/></view>\r\n\t\t\t\t\t<view @tap=\"tomoney\" v-else-if=\"commission2money=='1'\" class=\"data_btn flex-xy-center\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转到{{t('余额')}}账户<image :src=\"pre_url+'/static/imgsrc/commission_dw.png'\"/></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex flex-wp\">\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"../order/shoporder?st=0\" class=\"data_module_view\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">已提现{{t('佣金')}}({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{count3}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"../order/shoporder?st=0\" class=\"data_module_view\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{is_end==1?userinfo.commission_yj:'计算中'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"userinfo.show_totalcommission && userinfo.totalcommission > 0\" @tap=\"goto\" data-url=\"../order/shoporder?st=0\" class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计总佣金</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.totalcommission}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"userinfo.show_teamfenhongyeji > 0\" class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">团队业绩</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.team_fenhong_yeji}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view  class=\"data_module flex\" v-if=\"(userinfo.baodan_freeze && userinfo.baodan_freeze > 0) || (userinfo.zt_commission && userinfo.zt_commission > 0)\">\r\n\t\t\t\t\t<view v-if=\"userinfo.baodan_freeze && userinfo.baodan_freeze > 0\" @tap=\"goto\" data-url=\"/pagesA/commission/baodanfreezelog\" class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">报单冻结</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.baodan_freeze}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"userinfo.zt_commission && userinfo.zt_commission > 0\" @tap=\"goto\" data-url=\"/activity/commission/commissionlog?st=1\" class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">直推奖</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.zt_commission}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"hasfenhong\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('股东分红')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"fenhong\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.fenhong}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{is_end==1?userinfo.fenhong_yj:'计算中'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\" v-if=\"userinfo.fenhong_max_show\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">已发分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.gudong_total}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.gudong_remain}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\" v-if=\"gongxianfenhong_show==1\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">预计{{userinfo.gongxianfenhong_txt || '股东贡献量分红'}}({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.gongxianfenhong}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"hasfenhong_huiben\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('回本股东分红')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"/pagesA/commission/fenhong_huiben\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.fenhong_huiben}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" @tap=\"goto\" data-url=\"/pagesA/commission/fenhong_huiben\">查看</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"data\" v-if=\"hasteamfenhong && set.teamfenhong_show\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('团队分红')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"teamfenhong\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.teamfenhong}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{is_end==1?userinfo.teamfenhong_yj:'计算中'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"hasbusinessteamfenhong && set.business_teamfenhong_show\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('商家团队分红')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"/pagesA/commission/business_teamfenhong\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.business_teamfenhong}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{is_end==1?userinfo.business_teamfenhong_yj:'计算中'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"hasteamshouyi && set.teamshouyi_show\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('团队收益')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"/pagesA/commission/teamshouyi\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计收益({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.teamshouyi}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"data\" v-if=\"hasareafenhong\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('区域代理分红')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"areafenhong\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.areafenhong}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{is_end==1?userinfo.areafenhong_yj:'计算中'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"data\" v-if=\"set.show_myyeji == 1\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>我的业绩</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\" @tap=\"goto\" data-url=\"/pagesExt/commission/myyeji\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.buymoney}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">本月({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.buymoney_thismonth}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\" v-if=\"userinfo.showyejicommission && userinfo.yeji_commission > 0\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">业绩奖({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.yeji_commission}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"userinfo.showxiaoshouyeji \">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>销售业绩</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\" >\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">总业绩({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" style=\"font-size: 38rpx;\">{{userinfo.totalyeji}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">销售额({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" style=\"font-size: 38rpx;\">{{userinfo.coupon_yeji}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">收益({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" style=\"font-size: 38rpx;\">{{userinfo.shouyi}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\" v-if=\"userinfo.yeji_commission > 0\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">业绩奖({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" style=\"font-size: 38rpx;\">{{userinfo.yeji_commission}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"data\" v-if=\"hasteamfenhong && (teamnum_show==1 || teamyeji_show==1)\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m3.png'\"></image>\r\n\t\t\t\t\t\t<text>{{t('我的团队')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"myteam\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex flex-wp\">\r\n\t\t\t\t\t<view class=\"flex1\" v-if=\"teamnum_show==1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">团队人数</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.teamnum}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\" v-if=\"teamyeji_show==1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">团队单量</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.teamOrderCount}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\" v-if=\"teamyeji_show==1 && userinfo.hasOwnProperty('teamyeji_prosum')\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">团队件数</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.teamyeji_prosum}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\" v-if=\"teamyeji_show==1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">团队业绩({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" v-if=\"set.show_teamyeji_search == 1\" @tap=\"goto\" data-url=\"/pagesExt/commission/teamyeji\">{{userinfo.teamyeji}}</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" v-else>{{userinfo.teamyeji}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\" v-if=\"set.show_teamyeji_search==1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">本月({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\" @tap=\"goto\" data-url=\"/pagesExt/commission/teamyeji\">{{userinfo.teamyeji_thismonth}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"hastouzifenhong\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('投资分红')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"touzifenhong\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">投资金额({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.touzimoney}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.touzifenhong}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.touzifenhong_yj}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"commission_butie\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('分销补贴')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"/pagesA/commission/commissionbutie\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待发放({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.butie_total}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">已发放({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.butie_send}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"data\" v-if=\"userinfo.show_team_yeji_fenhong\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>{{t('团队业绩阶梯奖')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"/pagesB/teamsaleyeji/fhorderlist\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.total_team_yeji_fenhong}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.team_yeji_fenhong_yj}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data\" v-if=\"userinfo.show_team_yeji_fenhong\">\r\n\t\t\t\t<view class=\"data_title flex-y-center flex-bt\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"data_icon\" :src=\"pre_url+'/static/imgsrc/commission_m2.png'\"/>\r\n\t\t\t\t\t\t<text>运费补贴</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url=\"/pagesC/commission/teamfenhongfreight\" class=\"data_detail flex-y-center\">\r\n\t\t\t\t\t\t查看详情<image :src=\"pre_url+'/static/imgsrc/commission_db.png'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data_module flex\">\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">累计分红({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.teamfenhong_freight}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"data_lable\">待结算({{getunit('佣金单位')}})</view>\r\n\t\t\t\t\t\t<view class=\"data_value\">{{userinfo.teamfenhong_freight_yj}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"poster\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i4.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t分享海报\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t邀请好友享收益\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"tomoney\" v-if=\"comwithdraw==1 && commission2money=='1'\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i1.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t{{t('佣金')}}转{{t('余额')}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t直接到账\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"downorder\" v-if=\"set.fxorder_show == 1\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t{{t('分销订单')}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"commissionlog\" v-if=\"set.commissionlog_show\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t{{t('佣金')}}明细\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看明细\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"commissionrecord\" v-if=\"set.commissionrecord_show\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t{{t('佣金')}}记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"fhorder\" v-if=\"showfenhong && set.fhorder_show\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t分红订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"fhlog\" v-if=\"showfenhong && set.fhlog_show\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t分红记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pagesExt/commission/tjbusinessList\" v-if=\"set.tjbusiness_show\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t推荐商家\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"orderMendian\" v-if=\"showMendianOrder\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t服务订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"commissionlogMendian\" v-if=\"showMendianOrder\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i4.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t服务{{t('佣金')}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"orderYeji\" v-if=\"showYeji\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i3.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t业绩统计\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看业绩\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pagesExt/agent/cardEdit\" v-if=\"set && set.agent_card == 1\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i10.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t代理卡片\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t查看代理信息\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pagesA/agent/priceRate\" v-if=\"set && set.product_price_rate == 1\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i10.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t价格倍率\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t修改商品价格倍率\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pagesA/agent/memberPriceRate\" v-if=\"set && set.member_level_price_rate == 1\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/commission_i10.png'\"></image>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t价格倍率\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t修改会员等级倍率\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:20rpx\"></view>\r\n\t\t\r\n\t\t<uni-popup id=\"dialogInput\" ref=\"dialogInput\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" :title=\"t('佣金') + '转' + t('余额')+commission_to_money_rate\" value=\"\" placeholder=\"请输入转入金额\" @confirm=\"tomonenyconfirm\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\r\n\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\r\n\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t<parse :content=\"xycontent\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi\">已阅读并同意</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n      hiddenmodalput: true,\r\n      userinfo: [],\r\n      count: 0,\r\n      count1: 0,\r\n      count2: 0,\r\n      count3: 0,\r\n      count4: 0,\r\n      comwithdraw: 0,\r\n      canwithdraw: true,\r\n      money: 0,\r\n      count0: \"\",\r\n      countdqr: \"\",\r\n      commission2money: \"\",\r\n\t\t\tshowfenhong:false,\r\n\t\t\tshowMendianOrder:false,\r\n\t\t\thastouzifenhong:false,\r\n\t\t\thasfenhong:false,\r\n\t\t\thasareafenhong:false,\r\n\t\t\thasteamfenhong:false,\r\n\t\t\tshowYeji:false,\r\n\t\t\tfxjiesuantime:0,\r\n\t\t\tteamyeji_show:0,\r\n\t\t\tteamnum_show:0,\r\n\t\t\tgongxianfenhong_show:0,\r\n\t\t\tset:{},\r\n\t\t\thasteamshouyi:false,\r\n\t\t\tcommission_money_exchange_num:0,\r\n\t\t\thasfenhong_huiben:false,\r\n\t\t\thasbusinessteamfenhong:false,\r\n\t\t\tcommission_butie:false,\r\n\t\t\tshowxieyi:false,\r\n\t\t\txycontent:'',\r\n\t\t\tfhtype_arr:[],\r\n\t\t\tis_end:0,\r\n      commission_to_money_rate:'',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tvar that = this;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAgent/commissionSurvey', {}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '我的' + that.t('佣金')\r\n\t\t\t\t});\r\n\t\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\t\tfrontColor: '#ffffff', \r\n\t\t\t\t\tbackgroundColor: that.t('color1') \r\n\t\t\t\t});\r\n\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\tthat.set = res.set;\r\n\t\t\t\tthat.count = res.count;\r\n\t\t\t\tthat.count1 = res.count1;\r\n\t\t\t\tthat.count2 = res.count2;\r\n\t\t\t\tthat.count3 = res.count3;\r\n\t\t\t\tthat.count0 = res.count0;\r\n\t\t\t\tthat.countdqr = res.countdqr;\r\n\t\t\t\tthat.comwithdraw = res.comwithdraw;\r\n\t\t\t\tthat.commission2money = res.commission2money;\r\n\t\t\t\tthat.showfenhong = res.showfenhong;\r\n\t\t\t\tthat.showMendianOrder = res.showMendianOrder;\r\n\t\t\t\tthat.hastouzifenhong = res.hastouzifenhong;\r\n\t\t\t\tthat.hasfenhong = res.hasfenhong;\r\n\t\t\t\tthat.hasfenhong_huiben = res.hasfenhong_huiben;\r\n\t\t\t\tthat.hasareafenhong = res.hasareafenhong;\r\n\t\t\t\tthat.hasteamfenhong = res.hasteamfenhong;\r\n\t\t\t\tthat.showYeji = res.hasYeji;\r\n\t\t\t\tthat.fxjiesuantime = res.fxjiesuantime;\r\n\t\t\t\tthat.teamyeji_show = res.teamyeji_show;\r\n\t\t\t\tthat.teamnum_show = res.teamnum_show;\r\n\t\t\t\tthat.gongxianfenhong_show = res.gongxianfenhong_show;\r\n\t\t\t\tthat.hasteamshouyi = res.hasteamshouyi;\r\n\t\t\t\tthat.commission_money_exchange_num = res.commission_money_exchange_num;\r\n\t\t\t\tthat.hasbusinessteamfenhong = res.hasbusinessteamfenhong;\r\n\t\t\t\tthat.commission_butie = res.commission_butie;\r\n\t\t\t\tthat.fhtype_arr = res.fhtype_arr;\r\n\t\t\t\tthat.commission_to_money_rate = res.commission_to_money_rate;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\t//异步请求接口获取分红预收益数据\r\n\t\t\t\tthat.sequentialRequests();\r\n\t\t\t\tif(res.uplv_agree == 1){\r\n\t\t\t\t\tthat.showxieyi = true;\r\n\t\t\t\t\tthat.xycontent = res.agree_content;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.showxieyi = false;\r\n\t\t\t\t}\r\n\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 异步函数\r\n\t\tsequentialRequests:async function() {\r\n\t\t\tvar that = this;\r\n\t\t\tvar fhtype_arr = that.fhtype_arr;\r\n\t\t\tfor (let i = 0; i < fhtype_arr.length; i++) {\r\n\t\t\t  console.log(fhtype_arr[i]);\r\n\t\t\t  await that.getdata2(fhtype_arr[i]);\r\n\t\t\t}\r\n\t\t\tthat.is_end = 1;\r\n\t\t\tconsole.log('请求完成'); // 处理响应\r\n\t\t},\r\n\t\tgetdata2: function (fhtype) {\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log('请求'+fhtype);\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t   app.get('ApiAgent/get_fenhong', {fhtype:fhtype}, function (res) {\r\n\t\t\t\t\tvar data = res.data\r\n\t\t\t\t\tvar commission_yj = parseFloat(that.userinfo.commission_yj);\r\n\t\t\t\t\tif(fhtype=='gudong' && data){\r\n\t\t\t\t\t\t//股东分红\r\n\t\t\t\t\t\tthat.userinfo.fenhong_yj = data.fenhong_yj;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(fhtype=='huiben' && data){\r\n\t\t\t\t\t\t//股东回本分红\r\n\t\t\t\t\t\tthat.userinfo.fenhong_yj_huiben = data.fenhong_yj;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(fhtype=='team' && data){\r\n\t\t\t\t\t\t//团队分红\r\n\t\t\t\t\t\tthat.userinfo.teamfenhong_yj = data.fenhong_yj;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(fhtype=='area' && data){\r\n\t\t\t\t\t\t//区域分红\r\n\t\t\t\t\t\tthat.userinfo.areafenhong_yj = data.fenhong_yj;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(fhtype=='touzi' && data){\r\n\t\t\t\t\t\t//投资分红\r\n\t\t\t\t\t\tthat.userinfo.touzifenhong_yj = data.fenhong_yj;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(fhtype=='business_teamfenhong' && data){\r\n\t\t\t\t\t\t//投资分红\r\n\t\t\t\t\t\tthat.userinfo.business_teamfenhong_yj = data.fenhong_yj;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(data){\r\n\t\t\t\t\t\tthat.userinfo.commission_yj = (parseFloat(commission_yj )+ parseFloat(data.fenhong_yj)).toFixed(2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tresolve(data);\r\n\t\t\t\t});\r\n\t\t\t   \r\n\t\t\t});\r\n\t\t},\r\n    cancel: function () {\r\n      this.hiddenmodalput = true;\r\n    },\r\n    tomoney: function () {\r\n      this.$refs.dialogInput.open()\r\n    },\r\n    tomonenyconfirm: function (done, val) {\r\n\t\t\tconsole.log(val)\r\n      var that = this;\r\n      var money = val;\r\n      if (money == '' || parseFloat(money) <= 0) {\r\n        app.alert('请输入转入金额');\r\n        return;\r\n      }\r\n      if (parseFloat(money) > this.userinfo.commission) {\r\n        app.alert('可转入' + that.t('佣金') + '不足');\r\n        return;\r\n      }\r\n\t\t\tif(that.commission_money_exchange_num>0){\r\n\t\t\t\tvar need_score = that.commission_money_exchange_num*money\r\n\t\t\t\tif(need_score>0){\r\n\t\t\t\t\tapp.confirm('本次操作需要消耗' +need_score+ that.t('积分'),function(){\r\n\t\t\t\t\t\tdone();\r\n\t\t\t\t\t\tthat.exchangeSubmit(money)\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\tdone();\r\n\t\t\t\tthat.exchangeSubmit(money)\r\n\t\t\t}\r\n    },\r\n\t\texchangeSubmit:function(money){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiAgent/commission2money', {money: money}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t  if (data.status == 0) {\r\n\t\t\t    app.error(data.msg);\r\n\t\t\t  } else {\r\n\t\t\t    that.hiddenmodalput = true;\r\n\t\t\t    app.success(data.msg);\r\n\t\t\t    setTimeout(function () {\r\n\t\t\t      that.getdata();\r\n\t\t\t    }, 1000);\r\n\t\t\t  }\r\n\t\t\t});\r\n\t\t},\r\n\t\thidexieyi: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.goto('signature');\r\n\t\t},\r\n\t\tgetunit:function(text=''){\r\n\t\t\tvar rtext = this.t(text);\r\n\t\t\tif(rtext =='佣金单位' || rtext =='余额单位'){\r\n\t\t\t\trtext = '元';\r\n\t\t\t}\r\n\t\t\treturn rtext;\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.banner{position: absolute;width: 100%;height: 900rpx;}\r\n.user{ display:flex;width:100%;padding:40rpx 45rpx 0 45rpx;color:#fff;position:relative}\r\n.user image{ width:80rpx;height:80rpx;border-radius:50%;margin-right:20rpx}\r\n.user .info{display:flex;align-items: center;}\r\n.user .info .nickname{font-size:32rpx;font-weight:bold;}\r\n.user .set{ width:70rpx;height:100rpx;line-height:100rpx;font-size:40rpx;text-align:center}\r\n.user .set image{width:50rpx;height:50rpx;border-radius:0}\r\n\r\n.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;position:relative;margin-bottom:20rpx}\r\n\r\n.data{background:#fff;padding:30rpx;margin-top:30rpx;border-radius:16rpx}\r\n.data_title{font-size: 28rpx;color: #333;font-weight: bold;}\r\n.data_detail{font-size: 24rpx;font-family: Source Han Sans CN;font-weight: 400;color: #999999;font-weight: normal;}\r\n.data_detail image{height: 24rpx;width: 24rpx;margin-left: 10rpx;}\r\n.data_icon{height: 35rpx;width: 35rpx;margin-right: 15rpx;}\r\n.data_text{font-size: 26;color: #999;margin-top: 60rpx;}\r\n.data_price{font-size: 64rpx;color: #333;font-weight: bold;margin-top: 10rpx;}\r\n.data_btn{height: 56rpx;padding: 0 30rpx;font-size: 24rpx;color: #fff;font-weight: normal;border-radius: 100rpx;}\r\n.data_btn image{height: 24rpx;width: 24rpx;margin-left: 6rpx;}\r\n.data_module{margin-top: 60rpx;width: 100%;}\r\n.data_module .data_module_view{min-width: 50%;margin-bottom: 20rpx;}\r\n.data_lable{font-size: 26;color: #999;}\r\n.data_value{font-size: 44rpx;font-weight: bold;color: #333;margin-top: 10rpx;}\r\n\r\n.list{ background: #fff;margin-top:30rpx;padding:30rpx;border-radius:16rpx;display: grid;grid-template-columns: repeat(2, 1fr);grid-column-gap: 10rpx;grid-row-gap: 50rpx;}\r\n.list .item{ display:flex;align-items:center;}\r\n.list image{ height: 72rpx;width: 72rpx;margin-right: 20rpx; }\r\n.list .title{font-size: 28rpx;font-family: Source Han Sans CN;font-weight: 500;color: #121212;}\r\n.list .text{font-size: 24rpx;font-family: Source Han Sans CN;font-weight: 400;color: #999999;margin-top: 10rpx;}\r\n\r\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\r\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370938\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}