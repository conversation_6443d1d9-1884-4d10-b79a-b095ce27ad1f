{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/downorder.vue?b2fb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/downorder.vue?9b49", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/downorder.vue?a883", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/downorder.vue?484b", "uni-app:///activity/commission/downorder.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/downorder.vue?474f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/downorder.vue?0d51"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "count", "commissionyj", "pagenum", "datalist", "express_content", "nodata", "nomore", "pre_url", "tabitem", "tabst", "module", "is_cashdesk_commission", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "uni", "title", "changetab", "scrollTop", "duration", "logistics", "console", "todetail", "changemoduletab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkF31B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IAEA;EACA;;EAEAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAnB;QAAAG;QAAAQ;MAAA;QACAO;QACA;QACAA;QACA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAE;YACAC;UACA;UACAH;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;MACA;MACAF;QACAG;QACAC;MACA;MACA;IACA;IACAC;MACAC;MACA;MACA;MACA;MACA;MACAA;MACA;QACAP;MACA;QACA;QACAO;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAR;MACA;IACA;IACAS;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;MACAR;QACAG;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChNA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/downorder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/downorder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./downorder.vue?vue&type=template&id=5ae8982a&\"\nvar renderjs\nimport script from \"./downorder.vue?vue&type=script&lang=js&\"\nexport * from \"./downorder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./downorder.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/downorder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./downorder.vue?vue&type=template&id=5ae8982a&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"分销订单\") : null\n  var g0 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.t(\"佣金\")\n          var m2 = item.order_info ? _vm.t(\"color1\") : null\n          var m3 = item.order_info ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./downorder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./downorder.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"module_tab\" v-if=\"is_cashdesk_commission\">\r\n\t\t\t<dd-tab :itemdata=\"['商城订单','收银台订单']\" :itemst=\"['shop','cashdesk']\" :st=\"module\" :isfixed=\"false\" @changetab=\"changemoduletab\"></dd-tab>\r\n\t\t</view>\n\t\t<view class=\"topfix \" :class=\"is_cashdesk_commission?'top90':''\">\n\t\t\t<view class=\"toplabel\">\n\t\t\t\t<text class=\"t1\">{{t('分销订单')}}（{{count}}）</text>\n\t\t\t\t<text class=\"t2\">预计：+{{commissionyj}}元</text>\n\t\t\t</view>\n\t\t\t<dd-tab :itemdata=\"tabitem\" :itemst=\"tabst\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\"></dd-tab>\n\t\t</view>\n\t\t<view class=\"mt190\" :class=\"is_cashdesk_commission?'mt280':''\"></view>\n\t\t<block v-if=\"datalist && datalist.length>0\">\n\t\t<view class=\"content\">\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\" @tap='todetail' :data-show=\"item.order_info?1:0\" :data-url=\"'/pagesExt/order/detail?id='+item.order_info.id+'&fromfenxiao=1'\">\n\t\t\t\t<view class=\"f1 flex\" style=\"justify-content: flex-start;\">\n\t\t\t\t\t<image class=\"img\" :src=\"item.headimg\"></image>\n\t\t\t\t\t<view class=\"t1\">\n\t\t\t\t\t\t<view v-if=\"item.nickname\">\n\t\t\t\t\t\t\t{{item.nickname}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view >\n\t\t\t\t\t\t\t{{item.ordernum}}({{item.dengji}})\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- fengdanjiangli 逢单奖励 -->\n\t\t\t\t\t<view v-if=\"item.dannum && item.dannum > 0\" style=\"color:#a55;margin-left: 30rpx;\">\n\t\t\t\t\t\t第{{item.dannum}}单\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t<view class=\"t1 flex\">\n\t\t\t\t\t\t<view class=\"x1 flex\">\n\t\t\t\t\t\t\t<image :src=\"item.pic\" class=\"img\"></image>\n\t\t\t\t\t\t\t<view style=\"margin-left: 20rpx;\">\n\t\t\t\t\t\t\t\t<view class=\"x1t\" style=\"line-height: 50rpx;\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"x1t\"> 下单时间：{{item.createtime}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view>共{{item.num}}件</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"t2 flex\">\n\t\t\t\t\t\t<view >合计金额：￥{{item.totalprice}}</view>\n\t\t\t\t\t\t<view >预估{{t('佣金')}}：<text style=\"color: #ff6600;\">{{item.commission}}</text></view>\n\t\t\t\t\t\t<view >\n\t\t\t\t\t\t\t<text class=\"dior-sp6 yfk\" v-if=\"item.status==1 || item.status==2\"><text v-if=\"module =='shop'\">已付款</text> <text v-else>已完成</text></text>\n\t\t\t\t\t\t\t<text class=\"dior-sp6 dfk\" v-if=\"item.status==0\">待付款</text>\n\t\t\t\t\t\t\t<text class=\"dior-sp6 ywc\" v-if=\"item.status==3\">已完成</text>\n\t\t\t\t\t\t\t<text class=\"dior-sp6 ygb\" v-if=\"item.status==4\">已关闭</text>\n\t\t\t\t\t\t\t<text class=\"dior-sp6 ygb\" v-if=\"item.refund_money > 0\">退款/售后</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"t3 flex\"  v-if=\"item.order_info\">\n\t\t\t\t\t\t<view class=\"btn2\" :style=\"'border:solid 2rpx '+t('color1')+';color:'+t('color1')\" @tap.stop=\"logistics(item.order_info)\" >查看物流</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSelectExpress\" ref=\"dialogSelectExpress\" type=\"dialog\">\n\t\t\t<view style=\"background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx\" v-if=\"express_content\">\n\t\t\t\t<view class=\"sendexpress-item\" v-for=\"(item, index) in express_content\" :key=\"index\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" style=\"display: flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 0;\">\n\t\t\t\t\t<view class=\"flex1\">{{item.express_com}} - {{item.express_no}}</view>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t</block>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n      st: 0,\n\t\t\tcount:0,\n      commissionyj: 0,\n      pagenum: 1,\n      datalist: [],\n\t\t\texpress_content:'',\n      nodata: false,\n      nomore: false,\n\t  pre_url:app.globalData.pre_url,\r\n\t  tabitem:['所有订单','待付款','已付款','已完成','退款/售后'],\r\n\t  tabst:['0','1','2','3','5'],\r\n\t  module:'shop',//模块  shop:商城 cashdesk:收银台\r\n\t  is_cashdesk_commission:0//是否展示 收银台佣金\r\n\t  \n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.pagenum = 1;\n\t\tthis.datalist = [];\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata();\n    }\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n\t\t\tapp.get('ApiAgent/agorder',{st:st,pagenum: pagenum,module:that.module},function(res){\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar data = res.datalist;\r\n\t\t\t\tthat.is_cashdesk_commission = res.is_cashdesk_commission;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.commissionyj = res.commissionyj;\n\t\t\t\t\tthat.count = res.count;\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: that.t('分销订单')\n\t\t\t\t\t});\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n\t\t\t});\n\t\t},\n    changetab: function (st) {\n      this.pagenum = 1;\n      this.st = st;\n      this.datalist = [];\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    logistics:function(e){\n\t     console.log(e)\n      var express_com = e.express_com\n      var express_no = e.express_no\n      var express_content = e.express_content\n      var express_type = e.express_type\n      console.log(express_content)\n      if(!express_content){\n        app.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);\n      }else{\n        this.express_content = JSON.parse(express_content);\n        console.log(express_content);\n        this.$refs.dialogSelectExpress.open();\n      }\n    },\n\ttodetail:function(e){\n\t\tvar url = e.currentTarget.dataset.url;\n\t\tvar show = e.currentTarget.dataset.show;\n\t\tif(show){\n\t\t\tapp.goto(url);\n\t\t}\n\t},\r\n\tchangemoduletab: function (module) {\r\n\t  this.pagenum = 1;\r\n\t  this.module = module;\r\n\t  if(module =='shop'){\r\n\t\t  this.tabitem = ['所有订单','待付款','已付款','已完成','退款/售后'];\r\n\t\t  this.tabst = ['0','1','2','3','5'];\r\n\t  }else if(module =='cashdesk'){\r\n\t\t  this.tabitem = ['所有订单','已完成','退款/售后'];\r\n\t\t  this.tabst = ['0','2','5'];\r\n\t  }\r\n\t  this.datalist = [];\r\n\t  uni.pageScrollTo({\r\n\t    scrollTop: 0,\r\n\t    duration: 0\r\n\t  });\r\n\t  this.getdata();\r\n\t},\n  }\n};\n</script>\n<style>\n.topfix{width: 100%;position:relative;position:fixed;background: #f9f9f9;top:var(--window-top);z-index:11;}\n.toplabel{width: 100%;background: #f9f9f9;padding: 20rpx 20rpx;border-bottom: 1px #e3e3e3 solid;display:flex;}\n.toplabel .t1{color: #666;font-size:30rpx;flex:1}\n.toplabel .t2{color: #666;font-size:30rpx;text-align:right}\n\n.content{ width:100%;}\n.content .item{width:94%;margin-left:3%;border-radius:10rpx;background: #fff;margin-bottom:16rpx;}\n.content .item .f1{width:100%;padding: 10rpx 20rpx;color: #666;border-bottom: 1px #f5f5f5 solid;align-items: center;justify-content: space-between}\n.content .item .f1 .img{width: 100rpx;height: 100rpx;border-radius:50%;}\n.content .item .f1 .t1{line-height: 50rpx;margin-left: 30rpx;}\n\n.content .item .f2{padding:20rpx;align-items:center}/* display:flex; */\n.content .item .f2 .t1{display:flex;flex:auto; justify-content: space-between;align-items: center;border-bottom: 1px #f5f5f5 solid;padding-bottom: 20rpx;}\n.content .item .f2 .t1 .img{width: 140rpx;height: 140rpx;}\n.content .item .f2 .t1 .x1{align-items: center;}\n.content .item .f2 .t1 .x1 .x1t{line-height: 60rpx;}\n.content .item .f2 .t1 .x2{ color:#999}\n.content .item .f2 .t1 .x3{display:flex;align-items:center}\n/* .content .item .f2 .t1 .x3 image{width:40rpx;height:40rpx;border-radius:50%;margin-right:4px} */\n\n.content .item .f2 .t2{padding: 20rpx 0;justify-content: space-between}\n.content .item .f2 .t3{justify-content: flex-end;}\n\n.dfk{color: #ff9900;}\n.yfk{color: red;}\n.ywc{color: #ff6600;}\n.ygb{color: #aaaaaa;}\n.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;border-radius:20px;text-align:center;}\n.mt190{margin-top: 190rpx;}\r\n.mt280{margin-top: 280rpx;}\r\n.top90{top:90rpx}\r\n.module_tab{position: fixed;width: 100%;top: 0;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./downorder.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./downorder.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370826\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}