{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/signature.vue?9635", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/signature.vue?5656", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/signature.vue?1140", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/signature.vue?3c4d", "uni-app:///activity/commission/signature.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/signature.vue?473f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/signature.vue?3a8d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "id", "Strokes", "dom", "width", "height", "<PERSON><PERSON><PERSON>", "opt", "onLoad", "uni", "success", "onUnload", "methods", "confirm", "app", "touchmoveEnd", "e", "sumbit", "console", "canvasId", "url", "filePath", "name", "formData", "that", "fail", "clear", "touchstart", "imageData", "style", "color", "lineWidth", "points", "x", "y", "type", "touchmove", "touchend", "drawLine"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmB31B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IAMAC;MACAC;QACA;QACA;MACA;IACA;IACA;EACA;EACAC,+BAMA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACAA;QAAAR;MAAA;QACAQ;QACA;UACAA;UACA;QACA;QACAA;MACA;IACA;IACAC;MACAC;MACAA;IACA;IACAC;MACA;MACAC;MACA;QACAJ;QAAA;MACA;MACAL;QACAU;QACAT;UACAQ;UACAT;YACAW;YACAC;YACAC;YACAC;cACA;YAAA,CACA;YACAb;cACA;cACA;cACA;cACA;cACAc;cACAA;YACA;UACA;QACA;QACAC;UACAP;QACA;MACA;IACA;IACAQ;MAAA;MACA;MACA;MACA;IACA;IACAC;MACA;QACAC;QACAC;UACAC;UACAC;QACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IACAC;MACA;QACAH;QACAC;QACAC;MACA;MACA;IACA;IACAE;MACA;QAAA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA,oGACAN,gBACA;QACA,oGACAA,gBACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/signature.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/signature.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./signature.vue?vue&type=template&id=24e6f2f6&\"\nvar renderjs\nimport script from \"./signature.vue?vue&type=script&lang=js&\"\nexport * from \"./signature.vue?vue&type=script&lang=js&\"\nimport style0 from \"./signature.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/signature.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signature.vue?vue&type=template&id=24e6f2f6&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signature.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signature.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<view>\r\n\t\t<view style=\"position:fixed;top:100rpx;left:-10rpx;width:160rpx;border:1px solid #f5f5f5;border-radius:40rpx;height:80rpx;line-height:80rpx;background:#fff;color:#333;text-align:center;transform: rotate(90deg);\" @tap=\"goback\">返回</view>\r\n\t\t<view style=\"position:fixed;bottom:260rpx;left:-10rpx;width:160rpx;border:1px solid #f5f5f5;border-radius:40rpx;height:80rpx;line-height:80rpx;background:#fff;color:#333;text-align:center;transform: rotate(90deg);\"  @click=\"clear\">重新签名</view>\r\n\t\t<view style=\"position:fixed;bottom:70rpx;left:-10rpx;width:160rpx;border:1px solid #f5f5f5;border-radius:40rpx;height:80rpx;line-height:80rpx;background:#fff;color:#fff;text-align:center;transform: rotate(90deg);\" :style=\"{background:t('color1')}\"  @click=\"sumbit\">确认</view>\r\n\t</view>\r\n\t<view class=\"htz-signature-body\">\r\n\t\t<!-- <view class=\"title\">请在下方区域签写姓名</view> -->\r\n\t\t<canvas canvas-id=\"canvas\" id=\"canvas\" @touchstart=\"touchstart\" @touchmove=\"touchmove\"\t@touchend=\"touchend\"></canvas>\r\n\t\t<!-- <view class=\"clear\"  @click=\"clear\"><image src=\"/static/img/refsh.png\"><text>清除内容</text></view>\r\n\t\t<view class=\"htz-signature-fixed-bottom\">\r\n\t\t\t<view class=\"htz-signature-fixed-bottom-item sumbit\" @click=\"sumbit\" :style=\"{background:t('color1')}\">确定签字</view>\r\n\t\t</view> -->\r\n\t</view>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\tStrokes: [],\r\n\t\t\tdom: null,\r\n\t\t\twidth: 0,\r\n\t\t\theight: 0,\r\n\t\t\tsignatureurl:'',\r\n\t\t\topt:{},\r\n\t\t}\r\n\t},\r\n\tonLoad: function(opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\t// #ifdef H5\r\n\t\tdocument.body.addEventListener('touchmove', this.touchmoveEnd, {\r\n\t\t\tpassive: false\r\n\t\t});\r\n\t\t// #endif\r\n\t\tuni.getSystemInfo({\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tthis.width = res.windowWidth;\r\n\t\t\t\tthis.height = res.windowHeight;\r\n\t\t\t}\r\n\t\t});\r\n\t\tthis.dom = uni.createCanvasContext('canvas', this);\r\n\t},\r\n\tonUnload: function() {\r\n\t\t// #ifdef H5\r\n\t\tdocument.body.removeEventListener('touchmove', this.touchmoveEnd, {\r\n\t\t\tpassive: false\r\n\t\t})\r\n\t\t// #endif\r\n\t},\r\n\tmethods: {\r\n\t\tconfirm:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar orderid = this.opt.id;\r\n\t\t\tvar signatureurl = this.signatureurl;\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiAgent/signature',{signatureurl:signatureurl}, function(res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tapp.goback(true);\r\n\t\t\t});\r\n\t\t},\r\n\t\ttouchmoveEnd(e) {\r\n\t\t\te.preventDefault();\r\n\t\t\te.stopPropagation();\r\n\t\t},\r\n\t\tsumbit(){\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log(that.Strokes);\r\n\t\t\tif((that.Strokes).length == 0){\r\n\t\t\t\tapp.error('请先签字');return;\r\n\t\t\t}\r\n\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\tcanvasId: 'canvas',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconsole.log('success', res);\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id+'/xuanzhuan/1',\r\n\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\t// 'user': 'test'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\t\t//console.log(uploadFileRes.data);\r\n\t\t\t\t\t\t\t// 判断是否json字符串，将其转为json格式\r\n\t\t\t\t\t\t\t//let data = _this.$u.test.jsonString(uploadFileRes.data) ? JSON.parse(uploadFileRes.data) : uploadFileRes.data;\r\n\t\t\t\t\t\t\tvar data =  JSON.parse(uploadFileRes.data)\r\n\t\t\t\t\t\t\tthat.signatureurl = data.url;\r\n\t\t\t\t\t\t\tthat.confirm();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.log('fail', err)\r\n\t\t\t\t}\r\n\t\t\t}, this);\r\n\t\t},\r\n\t\tclear() { //清空\r\n\t\t\tthis.Strokes = [];\r\n\t\t\tthis.dom.clearRect(0, 0, this.width, this.height)\r\n\t\t\tthis.dom.draw();\r\n\t\t},\r\n\t\ttouchstart(e) {\r\n\t\t\tthis.Strokes.push({\r\n\t\t\t\timageData: null,\r\n\t\t\t\tstyle: {\r\n\t\t\t\t\tcolor: '#000000',\r\n\t\t\t\t\tlineWidth: 6,\r\n\t\t\t\t},\r\n\t\t\t\tpoints: [{\r\n\t\t\t\t\tx: e.touches[0].x,\r\n\t\t\t\t\ty: e.touches[0].y,\r\n\t\t\t\t\ttype: e.type,\r\n\t\t\t\t}]\r\n\t\t\t})\r\n\t\t\tthis.drawLine(this.Strokes[this.Strokes.length - 1], e.type);\r\n\t\t},\r\n\t\ttouchmove(e) {\r\n\t\t\tthis.Strokes[this.Strokes.length - 1].points.push({\r\n\t\t\t\tx: e.touches[0].x,\r\n\t\t\t\ty: e.touches[0].y,\r\n\t\t\t\ttype: e.type,\r\n\t\t\t})\r\n\t\t\tthis.drawLine(this.Strokes[this.Strokes.length - 1], e.type);\r\n\t\t},\r\n\t\ttouchend(e) {\r\n\t\t\tif (this.Strokes[this.Strokes.length - 1].points.length < 2) { //当此路径只有一个点的时候\r\n\t\t\t\tthis.Strokes.pop();\r\n\t\t\t}\r\n\t\t},\r\n\t\tdrawLine(StrokesItem, type) {\r\n\t\t\tif (StrokesItem.points.length > 1) {\r\n\t\t\t\tthis.dom.beginPath();\r\n\t\t\t\tthis.dom.setLineCap('round')\r\n\t\t\t\tthis.dom.setStrokeStyle(StrokesItem.style.color);\r\n\t\t\t\tthis.dom.setLineWidth(StrokesItem.style.lineWidth);\r\n\t\t\t\tthis.dom.moveTo(StrokesItem.points[StrokesItem.points.length - 2].x, StrokesItem.points[StrokesItem\r\n\t\t\t\t\t.points.length -\r\n\t\t\t\t\t2].y);\r\n\t\t\t\tthis.dom.lineTo(StrokesItem.points[StrokesItem.points.length - 1].x, StrokesItem.points[StrokesItem\r\n\t\t\t\t\t.points.length -\r\n\t\t\t\t\t1].y);\r\n\t\t\t\tthis.dom.stroke();\r\n\t\t\t\tthis.dom.draw(true);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n\t.title{ height: 100rpx;line-height: 100rpx; text-align: center;}\r\n\t.clear{ text-align: center;  height: 100rpx; line-height: 100rpx;color: #999; display: flex;align-items: center; justify-content: center;}\r\n\t.clear image{ width: 40rpx; height: 40rpx;  margin-right: 10rpx;}\r\n\t\r\n\t.htz-signature-body {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tbottom: 120rpx;\r\n\t\tleft: 15%;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.htz-signature-body canvas {\r\n\t\twidth: 85%;\r\n\t\theight: 100vh;\r\n\t\tbackground: #fff;\r\n\t\tmargin:0 20rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.htz-signature-fixed-bottom {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\r\n\t\ttext-align: center;\r\n\t\tcolor: #000;\r\n\t\tz-index: 11;\r\n\t\tdisplay: -webkit-box;\r\n\t\tdisplay: -webkit-flex;\r\n\t\tdisplay: flex;\r\n\t\tbackground-color: #fff;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 30rpx 0;\r\n\t}\r\n\t.htz-signature-fixed-bottom .htz-signature-fixed-bottom-item {\r\n\t\tbackground: #1890ff;\r\n\t\tcolor: #fff;\r\n\t\theight: 80rpx; width: 80%;\r\n\t\tline-height: 80rpx;\r\n\t\tborder-radius: 50rpx; ;\r\n\t}\r\n\t.htz-signature-fixed-bottom-item view image {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tpadding-top: 10rpx;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signature.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signature.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370181\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}