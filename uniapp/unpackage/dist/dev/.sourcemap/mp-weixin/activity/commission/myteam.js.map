{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/myteam.vue?3c3a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/myteam.vue?8e4d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/myteam.vue?185e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/myteam.vue?7209", "uni-app:///activity/commission/myteam.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/myteam.vue?976a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/myteam.vue?be5e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "userlevel", "userinfo", "textset", "levelList", "keyword", "to<PERSON>d", "tomoney", "toscore", "nodata", "nomore", "dialogShow", "tempMid", "tempLevelid", "tempLevelsort", "mid", "range", "tabdata", "tabitems", "startDate", "endDate", "pre_url", "team_auth", "checkLevelid", "checkLevelname", "levelDialogShow", "allLevel", "month_item", "monthindex", "month_text", "month_value", "zt_member_limit", "custom", "is_end", "levelup_uesnum", "custom_field", "change_mid", "select_member", "change_downs", "old_down", "sdate", "edate", "selectedDateRange", "first_mid", "showlevel", "onLoad", "onPullDownRefresh", "onReachBottom", "onShow", "uni", "that", "scrollTop", "duration", "methods", "getdata", "app", "date_start", "date_end", "month_search", "version", "console", "title", "sequentialRequests", "getdata2", "resolve", "sequentialRequests2", "i", "member", "teamorder", "<PERSON><PERSON><PERSON>", "changetab", "givemoneyshow", "givescoreshow", "givemoney", "id", "money", "givescore", "score", "searchChange", "searchConfirm", "showDialog", "changeLevel", "levelId", "toteam", "url", "toCheckDate", "clearDate", "callphone", "phoneNumber", "fail", "chooseLevel", "hideTimeDialog", "levelRadioChange", "choose<PERSON>ont<PERSON>", "toDown", "changedown", "now_down", "tochange", "new_down", "setTimeout", "closechangedown", "olddownchange", "setDate", "toSelectDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7LA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmOx1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;MACA;IACA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;QACAA;MACA;QACAA;QACAA;MACA;MACAD;QACAE;QACAC;MACA;MACAF;IACA;EACA;EACAG;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAJ;MACAA;MACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACAK;QAAAzD;QAAAE;QAAAK;QAAAU;QAAAyC;QAAAC;QAAAlC;QAAAmC;QAAAC;QAAAhB;MAAA;QACAO;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAU;UACA;YACAV;YACAA;UACA;YACAA;YACAA;UACA;YACAA;YACAA;UACA;UACA;YACAA;YACAA;UACA;UACAA;UACAA;UACAA;;UAGA;UACAA;;UAEA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAD;YACAY;UACA;UAEAX;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACA;QACAA;MACA;IACA;IACA;IACAY;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAZ;gBAAA;gBAAA,OACAA;cAAA;gBACAA;gBACAU;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MAAA;QAAA;MAAA;MAAA;IAAA;IACAG;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACAR;UAAAxC;UAAAyC;UAAAC;UAAAlC;UAAAmC;UAAAlB;UAAAC;UAAAE;QAAA;UACA;UACAO;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAc;QACA;MACA;IACA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAf;gBACAnD;gBACAmE;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAC;gBACA;kBACAA;gBACA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAjB;cAAA;gBAAAzD;gBACA;kBACA0E;kBACAA;kBACAA;gBACA;gBAEA;kBACAA;kBACAA;gBACA;gBAEApE;cAAA;gBAnBAmE;gBAAA;gBAAA;cAAA;gBAsBAhB;gBACAU;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MAAA;QAAA;MAAA;MAAA;IAAA;IACAS;MACA;MACAT;MACA;QACAL;UAAAxC;UAAAyB;UAAAC;UAAAE;QAAA;UACA;UACAqB;QACA;MAEA;IACA;IACAM;MACA;MACArB;QACAE;QACAC;MACA;MACA;IACA;IACAmB;MACA;MACA;MACArB;MACAA;IACA;IACAsB;MACA;MACA;MACAtB;MACAA;IACA;IACAuB;MACA;MACA;MACAlB;MACAA;QAAAmB;QAAAC;MAAA;QACApB;QACA;UACAA;QACA;UACAA;UACAL;UACAA;QACA;MACA;IACA;IACA0B;MACA;MACA;MACArB;MACAA;QAAAmB;QAAAG;MAAA;QACAtB;QACA;UACAA;QACA;UACAA;UACAL;UACAA;QACA;MACA;IACA;IACA4B;MACA;IACA;IACAC;MACA;MACA;MACA7B;MACAA;IACA;IACA8B;MACA;MACA9B;MACAA;MACAA;MACA;IACA;IACA+B;MACA;MACA;MACA;MACA;MACA1B;QACAA;QACAA;UAAAxC;UAAAmE;QAAA;UACA3B;UACA;YACAA;UACA;YACAA;YACAL;YACAA;UACA;QACA;MACA;IACA;IACAiC;MACA;QACAlC;UACAmC;QACA;MACA;MACA;IAEA;IACAC;MACA9B;IACA;IACA+B;MACA;MACApC;MACAA;MACAD;QACAE;QACAC;MACA;MACA;IACA;IACAmC;MACA;MACAtC;QACAuC;QACAC,uBACA;MACA;IACA;IACAC;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA1C;MACAA;MACAA;MACAA;IACA;IACA2C;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACAvC;IACA;IACAwC;MACA;MACA7C;MACAK;MACAA;QAAAyC;MAAA;QACAzC;QACA;UACAA;UACA;QACA;QACAL;QACAA;QACAA;MACA;IACA;IACA+C;MACA;MACA;MACA;MACA1C;QACAA;QACAA;UAAA2C;UAAA3D;QAAA;UACA;YACAgB;YACA;UACA;UACAA;UACAA;UACAL;UACAA;UACAiD;YACAjD;UACA;QACA;MACA;IAEA;IACAkD;MACA;IACA;IACAC;MACAzC;MACA;MACA;IACA;IACA0C;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IACAC;MACA;MACAhD;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtrBA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/myteam.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/myteam.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myteam.vue?vue&type=template&id=757b6e3b&\"\nvar renderjs\nimport script from \"./myteam.vue?vue&type=script&lang=js&\"\nexport * from \"./myteam.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myteam.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/myteam.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myteam.vue?vue&type=template&id=757b6e3b&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    _vm.userlevel &&\n    _vm.userlevel.team_show_down_order &&\n    _vm.selectedDateRange == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload &&\n    _vm.userlevel &&\n    _vm.userlevel.team_show_down_order &&\n    _vm.selectedDateRange == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.userlevel &&\n    _vm.userlevel.team_show_down_order &&\n    _vm.selectedDateRange == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.userlevel &&\n    _vm.userlevel.team_show_down_order &&\n    _vm.selectedDateRange == 4\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload && _vm.custom && _vm.custom.yeji_with_pronum && _vm.userlevel\n      ? _vm.t(\"团队\")\n      : null\n  var m5 =\n    _vm.isload && !(_vm.custom && _vm.custom.yeji_with_pronum) && _vm.userlevel\n      ? _vm.t(\"团队\")\n      : null\n  var m6 =\n    _vm.isload &&\n    !(_vm.custom && _vm.custom.yeji_with_pronum) &&\n    _vm.userinfo &&\n    _vm.userinfo.next_ordermoney_show\n      ? _vm.t(\"团队业绩\")\n      : null\n  var g0 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var g1 =\n    _vm.isload && g0\n      ? _vm.levelup_uesnum && _vm.levelup_uesnum.length > 0\n      : null\n  var m7 = _vm.isload && g0 ? _vm.t(\"佣金\") : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m8 =\n            _vm.userlevel && _vm.userlevel.team_yeji == 1 ? _vm.t(\"团队\") : null\n          var m9 =\n            _vm.userlevel && _vm.userlevel.team_down_total == 1\n              ? _vm.t(\"一级\")\n              : null\n          var m10 =\n            _vm.userlevel && _vm.userlevel.team_score == 1\n              ? _vm.t(\"积分\")\n              : null\n          var m11 =\n            _vm.userlevel && _vm.userlevel.team_view_zhitui_member_num == 1\n              ? _vm.t(\"一级\")\n              : null\n          var m12 =\n            _vm.userlevel && _vm.userlevel.team_givemoney == 1\n              ? _vm.t(\"余额\")\n              : null\n          var m13 =\n            _vm.userlevel && _vm.userlevel.team_givescore == 1\n              ? _vm.t(\"积分\")\n              : null\n          return {\n            $orig: $orig,\n            m8: m8,\n            m9: m9,\n            m10: m10,\n            m11: m11,\n            m12: m12,\n            m13: m13,\n          }\n        })\n      : null\n  var l1 =\n    _vm.isload && _vm.dialogShow\n      ? _vm.__map(_vm.levelList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m14 =\n            item.id != _vm.tempLevelid && item.sort > _vm.tempLevelsort\n              ? _vm.t(\"color1\")\n              : null\n          return {\n            $orig: $orig,\n            m14: m14,\n          }\n        })\n      : null\n  var m15 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        g0: g0,\n        g1: g1,\n        m7: m7,\n        l0: l0,\n        l1: l1,\n        m15: m15,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myteam.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myteam.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<block v-if=\"showlevel\">\n\t\t\t<dd-tab :isshow=\"showlevel\" :itemdata=\"tabdata\" :itemst=\"tabitems\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"userlevel && userlevel.can_agent>=1\"></dd-tab>\n\t\t</block>\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入昵称/姓名/手机号搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"content\">\n      <view class=\"date-search\" v-if=\"userlevel && userlevel.team_show_down_order\">\n        <view class=\"flex-bt\">\n          <view class=\"date-btn\" :style=\"selectedDateRange == 1 ? 'color:#fff;background-color:'+t('color1') : ''\" @click=\"setDate(1)\">今日</view>\n          <view class=\"date-btn\" :style=\"selectedDateRange == 2 ? 'color:#fff;background-color:'+t('color1') : ''\" @click=\"setDate(2)\">近七日</view>\n          <view class=\"date-btn\" :style=\"selectedDateRange == 3 ? 'color:#fff;background-color:'+t('color1') : ''\" @click=\"setDate(3)\">近30日</view>\n          <view class=\"date-btn\" :style=\"selectedDateRange == 4 ? 'color:#fff;background-color:'+t('color1') : ''\" @click=\"toSelectDate\">自定义</view>\n        </view>\n        <view class=\"show-search-date\" v-if=\"sdate && edate\">{{sdate}} / {{edate}}</view>\n      </view>\n\t\t\t<view class=\"topsearch flex-y-center sx\" v-if=\"userlevel && userlevel.team_month_data==1\">\n\t\t\t\t<text class=\"t1\">日期筛选：</text>\n\t\t\t\t<view class=\"body_data\" style=\"min-width: 200rpx;\" @click=\"toCheckDate\"> {{startDate?startDate:'点击选择日期'}}{{endDate?' 至 '+endDate:''}}\n\t\t\t\t\t<!-- <img class=\"body_detail\" :src=\"pre_url+'/static/img/week/week_detail.png'\" /> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"t_date\">\n\t\t\t\t\t<view v-if=\"startDate\" class=\"x1\" @tap=\"clearDate\">清除</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"topsearch flex-y-center sx\" v-if=\"(team_auth==1 && userlevel && userlevel.team_month_data==1) || !showlevel\">\n\t\t\t\t<text class=\"t1\">身份筛选：</text>\n\t\t\t\t<view class=\"t2\" @tap=\"chooseLevel\">\r\n\t\t\t\t\t<view class=\"uni-input\" v-if=\"checkLevelname\">{{checkLevelname}}</view>\r\n\t\t\t\t\t<view style=\"font-size:28rpx;color: #686868\" v-else>请选择级别</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"topsearch flex-y-center sx\" v-if=\"userinfo.month_yeji_show\">\n\t\t\t\t<text class=\"t1\">月份筛选：</text>\n\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t<picker @change=\"chooseMonth\" :range=\"month_item\">\n\t\t\t\t\t\t<view class=\"uni-input\" v-if=\"month_item[monthindex]\">{{month_item[monthindex]}}</view>\n\t\t\t\t\t\t<view style=\"font-size:28rpx;color: #686868\" v-else>请选择月份</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t<!-- <view class=\"topsearch flex-y-center\" v-if=\"userlevel && userlevel.team_month_data==1\">\n\t\t\t<text class=\"t1\">日期筛选：</text>\n\t\t\t<view class=\"example-body\" style=\"width: 500rpx;\">\n\t\t\t\t<uni-datetime-picker\n\t\t\t\t\tv-model=\"range\"\n\t\t\t\t\ttype=\"daterange\"\n\t\t\t\t\tstart=\"\"\n\t\t\t\t\tend=\"\"\n\t\t\t\t\trangeSeparator=\"至\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\t<view class=\"yejilabel\">\n\t\t\t\t<block v-if=\"custom && custom.yeji_with_pronum\">\n\t\t\t\t\t<!-- 定制：业绩显示个人和团队商品数量，其他不显示 -->\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel\">个人业绩：{{is_end==1?userinfo.yeji_pronum:'计算中'}}，{{t('团队')}}业绩：{{is_end==1?userinfo.team_yeji_pronum:'计算中'}}</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<!-- 默认团队业绩 -->\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel\">{{t('团队')}}业绩：{{is_end==1?userinfo.team_yeji_total:'计算中'}} 元</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel && userinfo.miniyeji_show\">小市场业绩：{{is_end==1?userinfo.team_miniyeji_total:'计算中'}}  元</view>\r\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel && userinfo.maxyeji_show\">大市场业绩：{{is_end==1?userinfo.team_maxyeji_total:'计算中'}}  元</view>\n\t\t\t\t\t<view class=\"t1\" style=\"width:100%\" v-if=\"userinfo && userinfo.next_ordermoney_show\">距升级{{t('团队业绩')}}：{{is_end==1?userinfo.next_up_ordermoney:'计算中'}} 元</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel && userinfo.month_yeji_show\">{{month_text}}：{{is_end==1?userinfo.now_month_yeji:'计算中'}} 元</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel && userlevel.team_show_down_order\">总成单量：{{userinfo.team_order_count}} 单</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel && userlevel.team_show_down_order\">总退款单数：{{userinfo.team_refund_order}} 单</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"userlevel && userlevel.team_show_down_order\">成交总额：{{userinfo.team_order_money}} 元</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"content\" v-if=\"datalist && datalist.length>0\">\n\t\t\t<view class=\"label\" v-if=\"zt_member_limit != 0 && st == 1\">\n\t\t\t\t<text class=\"t1\">直推名额：{{zt_member_limit}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"levelup-num\" v-if=\"levelup_uesnum && levelup_uesnum.length > 0\">\n\t\t\t\t<text class=\"t1\">可升级数量</text>\n\t\t\t\t<view class=\"flex list\">\n\t\t\t\t\t<view class=\"num\" v-for=\"(item,index) in levelup_uesnum\">{{item.name}}：{{item.num}} 个</view>\n\t\t\t\t</view>\t\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 给下级升级 人数 -->\n\t\t\t<view class=\"label\">\n\t\t\t\t<text class=\"t1\">成员信息</text>\n\t\t\t\t<text class=\"t2\">来自TA的{{t('佣金')}}</text>\n\t\t\t</view>\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f1\" @click=\"toteam(item.id)\">\n\t\t\t\t\t\t<image :src=\"item.headimg\" mode=\"widthFix\"></image>\n\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t<text class=\"x1\">{{item.nickname}}(ID:{{item.id}})</text>\n\t\t\t\t\t\t\t<block v-if=\"custom && custom.team_show_visittime\">\n\t\t\t\t\t\t\t\t<text class=\"x2\">加入时间：{{item.createtime}}</text>\n\t\t\t\t\t\t\t\t<text class=\"x2\">最后访问：{{item.last_visittime}}</text>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<text v-else class=\"x2\">{{item.createtime}}</text>\n\t\t\t\t\t\t\t<text class=\"x2\">等级：{{item.levelname}}</text>\n\t\t\t\t\t\t\t<text class=\"x2\" v-if=\"item.tel\">手机号：{{item.tel}}</text>\n\t\t\t\t\t\t\t<text class=\"x2\" v-if=\"item.pid\">推荐人ID：{{item.pid}}</text>\n\t\t\t\t\t\t\t\t<block v-if=\"custom && custom.yx_gift_pack\" >\n\t\t\t\t\t\t\t\t<text class=\"x3\">礼包订单：{{item.packcount}}</text>\n\t\t\t\t\t\t\t\t<text class=\"x3\" >订单金额：￥{{item.totalpackmoney}}</text>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<text class=\"x2\" v-if=\"item.xianxia_sales\">出券量：{{item.xianxia_sales}}组</text>\n\t\t\t\t\t\t\t<block v-if=\"item.custom_field\" v-for=\"(v,k) in item.custom_field\" :key=\"k\">\n\t\t\t\t\t\t\t\t<view v-if=\"v.key == 'upload'\">\n\t\t\t\t\t\t\t\t\t<text class=\"x2\" >{{v.field}}：</text>\n\t\t\t\t\t\t\t\t\t<image :src=\"v.value\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"x2\" v-else>{{v.field}}：{{v.value}}</text>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<text class=\"t4\" v-if=\"userlevel && userlevel.team_yeji==1\">{{t('团队')}}业绩：{{item.teamyeji}}</text>\n\t\t\t\t\t\t<text class=\"t4\" v-if=\"userlevel && userlevel.team_self_yeji==1\">个人业绩：{{item.selfyeji}}</text>\n\t\t\t\t\t\t<text class=\"t4\" v-if=\"userlevel && userlevel.team_down_total==1\">{{t('一级')}}人数：{{item.team_down_total}} 人</text>\n\t\t\t\t\t\t<text class=\"t4\" v-if=\"userlevel && userlevel.team_score==1\">{{t('积分')}}：{{item.score}}</text>\n            <text class=\"t4\" v-if=\"userlevel && userlevel.team_view_zhitui_member_num==1\">{{t('一级')}}：{{item.team_view_zhitui_member_num}}人</text>\n            <text class=\"t4\" v-if=\"userlevel && userlevel.team_show_down_order==1\" @tap.stop=\"toDown\" :data-mid=\"item.id\" style=\"text-decoration: underline;\">成交订单：{{item.team_order_count}}(单)</text>\n            <text class=\"t4\" v-if=\"userlevel && userlevel.team_show_down_order==1\">成交金额：{{item.team_order_money}}(元)</text>\n\t\t\t\t\t\t<text class=\"t1\">+{{item.commission}}</text>\n\t\t\t\t\t\t<!-- <text class='t2'>{{item.downcount}}个成员</text> -->\n\t\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t\t<view v-if=\"userlevel && userlevel.team_givemoney==1\" class=\"x1\" @tap=\"givemoneyshow\" :data-id=\"item.id\">转{{t('余额')}}</view>\n\t\t\t\t\t\t\t<view v-if=\"userlevel && userlevel.team_givescore==1\" class=\"x1\" @tap=\"givescoreshow\" :data-id=\"item.id\" >转{{t('积分')}}</view>\n\t\t\t\t\t\t\t<view v-if=\"userlevel && userlevel.team_levelup==1\" class=\"x1\"   @tap=\"showDialog\" :data-id=\"item.id\" :data-levelid=\"item.levelid\" :data-levelsort=\"item.levelsort\">升级</view>\n              <view v-if=\"userlevel && userlevel.team_shortvideo==1\" class=\"x1\" @tap=\"goto\" :data-url=\"'/pagesA/shortvideo/looklog?mid=' + item.id\">短视频记录</view>\n              <view v-if=\"userlevel && userlevel.team_callphone==1 && item.tel\" class=\"x1\" @tap.stop=\"callphone\" :data-phone=\"item.tel\">拨打电话</view>\n              <view v-if=\"userlevel && userlevel.team_view_down_to_down==1 && item.team_view_zhitui_member_num > 0\" class=\"x1\" @tap.stop=\"toDown\" :data-mid=\"item.id\">人员</view>\n              <view v-if=\"custom && custom.team_member_history\" class=\"x1\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/my/history?mid='+item.id\">查看足迹</view>\n              <view v-if=\"userlevel && userlevel.team_update_member_info==1\" class=\"x1\" @tap.stop=\"goto\" :data-url=\"'/pagesC/my/saveinfo?mid='+item.id\">修改资料</view>\n\t\t\t\t\t\t<view v-if=\"item.can_change_pid==1\" class=\"x1\" @tap=\"changedown(item.id)\" >链动换位</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<uni-popup id=\"dialogmoneyInput\" ref=\"dialogmoneyInput\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"转账金额\" value=\"\" placeholder=\"请输入转账金额\" @confirm=\"givemoney\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\t<uni-popup id=\"dialogscoreInput\" ref=\"dialogscoreInput\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"转账数量\" value=\"\" placeholder=\"请输入转账数量\" @confirm=\"givescore\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\t\n\t\t<view v-if=\"dialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"showDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">升级</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"showDialog\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"sheet-item\" v-for=\"(item, index) in levelList\" :key=\"index\">\n\t\t\t\t\t\t<text class=\"item-text flex-item\">{{item.name}}</text>\n\t\t\t\t\t\t<view class=\"flex1\"></view><view @tap=\"changeLevel\" :data-id=\"item.id\" :data-name=\"item.name\" v-if=\"item.id != tempLevelid && item.sort > tempLevelsort\" :style=\"{'color':t('color1')}\">选择</view><view v-else style=\"color: #ccc;\">选择</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view v-if=\"levelDialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideTimeDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择级别</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideTimeDialog\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"pstime-item\" @tap=\"levelRadioChange\" data-id=\"0\" data-name=\"全部\">\n\t\t\t\t\t\t<view class=\"flex1\">全部</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in allLevel\" :key=\"index\" @tap=\"levelRadioChange\" :data-id=\"item.id\" :data-name=\"item.name\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!--换位弹窗-->\n\t\t<uni-popup id=\"dialogChangeDown\" ref=\"dialogChangeDown\" type=\"center\" background-color='#fff' >\n\t\t\t<view class=\"popup_centent_view\">\n\t\t\t\t<view class='popup_title_view-c'>链动换位</view>\n\t\t\t\t<view class=\"popup_input-view\">\n\t\t\t\t\t<view class=\"options-view flex \">\n\t\t\t\t\t\t<view class=\"title-text\" style=\"width: 30%;\">选择会员：</view>\n\t\t\t\t\t\t<view class=\"flex flex-xy-center num-options\">\n\t\t\t\t\t\t\t{{select_member.nickname}}(ID:{{select_member.id}})\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"options-view flex \">\n\t\t\t\t\t\t<view class=\"title-text\" style=\"width: 30%;\">换位会员:</view>\n\t\t\t\t\t\t<view class=\"flex flex-xy-center num-options\">\n\t\t\t\t\t\t\t<radio-group class=\"flex\" name=\"new_down\" style=\"flex-wrap:wrap\" @change=\"olddownchange\" >\n\t\t\t\t\t\t\t\t<label class=\"flex-y-center\" v-for=\"(item,idx) in change_downs\" >\n\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item.mid\" style=\"transform: scale(0.8);\"/>{{item.nickname}}(ID：{{item.mid}})\n\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup_but_view\">\n\t\t\t\t\t<view class='poput_options_but' @tap=\"closechangedown()\">取消</view>\n\t\t\t\t\t<view class='poput_options_but success' :style=\"{'backgroundColor':t('color1')}\" @tap=\"tochange()\">确认换位</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\topt:{},\n\t\tloading:false,\n\t\tisload: false,\n\t\tmenuindex:-1,\n\n\t\tst: 1,\n\t\tdatalist: [],\n\t\tpagenum: 1,\n\t\tuserlevel:{},\n\t\tuserinfo:{},\n\t\ttextset:{},\n\t\tlevelList:{},\n\t\tkeyword:'',\n\t\ttomid:'',\n\t\ttomoney:0,\n\t\ttoscore:0,\n\t\tnodata: false,\n\t\tnomore: false,\n\t\tdialogShow: false,\n\t\ttempMid: '',\n\t\ttempLevelid: '',\n\t\ttempLevelsort: '',\n\t\tmid:0,\n\t\trange: [],\n\t\ttabdata:[],\n\t\ttabitems:[],\n\t\tstartDate: '',\n\t\tendDate: '',\n\t\tpre_url: app.globalData.pre_url,\n\t\tteam_auth:0,\n\t\tcheckLevelid: 0,\n\t\tcheckLevelname: '',\n\t\tlevelDialogShow: false,\n\t\tallLevel:{},\n\t\tmonth_item:[],\n\t\tmonthindex:-1,\n\t\tmonth_text:'当月团队总业绩',\n\t\tmonth_value:'',\n\t\tzt_member_limit:0, //直推名额数\n\t\tcustom:{},\n\t\tis_end:0,\n\t\tlevelup_uesnum:[],\n\t\tcustom_field:[],\n\t\tchange_mid:0,//要换位的下级会员\n\t\tselect_member:{},//选择的下级会员\n\t\tchange_downs:[],//当前会员已经脱离的下级\n\t\told_down:0,\n    sdate:'',//订单筛选时间 开始时间\n    edate:'',//订单筛选时间 结束时间\n    selectedDateRange:'',//选中时间筛选\r\n    first_mid:0,//查看成交订单mid\r\n\t\tshowlevel:true\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.mid = this.opt.mid;\r\n    if(this.opt.first_mid){\r\n      this.first_mid = this.opt.first_mid;\r\n    }\r\n\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1\n      this.getdata(true);\n    }\n  },\n  onShow(){\n  \tvar that  = this;\n  \tuni.$on('selectedDate',function(data,otherParam){\n      if(otherParam && otherParam == 1){\n        that.sdate =  data.startStr.dateStr;\n        that.edate = data.endStr.dateStr;\n      }else{\n        that.startDate = data.startStr.dateStr;\n        that.endDate = data.endStr.dateStr;\n      }\n\t\t\tuni.pageScrollTo({\n\t\t\t\tscrollTop: 0,\n\t\t\t\tduration: 0\n\t\t\t});\n\t\t\tthat.getdata();\n\t\t})\n  },\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t\tthis.is_end = 0;\n\t\t\t}\n      var that = this;\n      var st = that.st;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n\t\t\tvar mid = that.mid;\n\t\t\tvar date_start = that.startDate;\n\t\t\tvar date_end = that.endDate;\n\t\t\tvar checkLevelid = that.checkLevelid;\n\t\t\tvar month_search = that.month_value;\t\r\n\t\t\tvar first_mid = that.first_mid;\t\n      app.post('ApiAgent/team', {st: st,pagenum: pagenum,keyword:keyword,mid:mid,date_start:date_start,date_end:date_end,checkLevelid:checkLevelid,month_search:month_search,version:'2.6.3',first_mid:first_mid}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.userinfo = res.userinfo;\n\t\t\t\t\tthat.userlevel = res.userlevel;\n\t\t\t\t\tthat.textset = app.globalData.textset;\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tthat.levelList = res.levelList;\n\t\t\t\t\tthat.month_item = res.month_item;\n\t\t\t\t\tthat.levelup_uesnum = res.levelup_uesnum;\n\t\t\t\t\tconsole.log(that.levelup_uesnum,'that.levelup_uesnum');\n\t\t\t\t\tif(res.userlevel && res.userlevel.can_agent==2){\n\t\t\t\t\t\tthat.tabdata = [that.t('一级')+'('+res.userinfo.myteamCount1+')',that.t('二级')+'('+res.userinfo.myteamCount2+')'];\n\t\t\t\t\t\tthat.tabitems = ['1','2'];\n\t\t\t\t\t}else if(res.userlevel && res.userlevel.can_agent==3){\n\t\t\t\t\t\tthat.tabdata = [that.t('一级')+'('+res.userinfo.myteamCount1+')',that.t('二级')+'('+res.userinfo.myteamCount2+')',that.t('三级')+'('+res.userinfo.myteamCount3+')'];\n\t\t\t\t\t\tthat.tabitems = ['1','2','3'];\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthat.tabdata = [that.t('一级')+'('+res.userinfo.myteamCount1+')'];\n\t\t\t\t\t\tthat.tabitems = ['1'];\n\t\t\t\t\t}\n\t\t\t\t\tif(res.team_auth){\n\t\t\t\t\t\tthat.tabdata = that.tabdata.concat([that.t('团队')+'('+res.userinfo.myteamCount4+')'])\n\t\t\t\t\t\tthat.tabitems = that.tabitems.concat(['4']);\n\t\t\t\t\t}\n\t\t\t\t\tthat.team_auth = res.team_auth;\n\t\t\t\t\tthat.allLevel = res.all_level;\r\n\t\t\t\t\tthat.showlevel = res.showlevel;\r\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\t\t//自定义集合\n\t\t\t\t\tthat.custom = res.custom;\n\t\t\t\t\t\n\t\t\t\t\t//会员等级直推人数\n\t\t\t\t\tif(res.userlevel && res.userlevel.zt_member_limit){\n\t\t\t\t\t\tthat.zt_member_limit = res.userlevel.zt_member_limit\n\t\t\t\t\t}\n\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t}\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: that.t('我的团队')\n\t\t\t\t\t});\n\n\t\t\t\t\tthat.loaded();\n\t\t\t\t\t//异步获取当前会员的业绩数据\n\t\t\t\t\tthat.sequentialRequests();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n        // 异步获取团队会员的业绩\n        that.sequentialRequests2();\n      });\n    },\n\t// 异步获取当前会员的业绩数据\n\tsequentialRequests:async function() {\n\t\tvar that = this;\n\t\tawait that.getdata2();\n\t\tthat.is_end = 1;\n\t\tconsole.log('请求完成1'); // 处理响应\n\t},\n\tgetdata2: function () {\n\t\tvar that = this;\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tvar mid = that.mid;\n\t\t\tvar date_start = that.startDate;\n\t\t\tvar date_end = that.endDate;\n\t\t\tvar checkLevelid = that.checkLevelid;\n\t\t\tvar month_search = that.month_value;\t\n\t\t   app.get('ApiAgent/get_team_yeji', {mid:mid,date_start:date_start,date_end:date_end,checkLevelid:checkLevelid,month_search:month_search,sdate:that.sdate,edate:that.edate,first_mid:that.first_mid}, function (res) {\n\t\t\t\tvar data = res.data\n\t\t\t\tthat.userinfo.team_yeji_total = data.team_yeji_total || 0;\n\t\t\t\tthat.userinfo.team_miniyeji_total = data.team_miniyeji_total || 0;\n\t\t\t\tthat.userinfo.next_up_ordermoney = data.next_up_ordermoney || 0;\n\t\t\t\tthat.userinfo.now_month_yeji = data.now_month_yeji || 0;\n\t\t\t\tthat.userinfo.team_yeji_pronum = data.team_yeji_pronum || 0;\n\t\t\t\tthat.userinfo.yeji_pronum = data.yeji_pronum || 0;\n\t\t\t\tif(data){\n\t\t\t\t\tthat.userinfo = { ...that.userinfo, ...data };\n\t\t\t\t}\n\t\t\t\tresolve(data);\n\t\t\t});\n\t\t});\n\t},\n\t// 异步获取团队会员的业绩\n\tsequentialRequests2:async function() {\n    var that = this;\n\t\tvar datalist = that.datalist;\n\t\tfor (let i = 0; i < datalist.length; i++) {\n\t\t\tvar member = datalist[i];\n      var teamorder = 0;\n      if(member.team_order_count=='计算中' || member.team_order_money=='计算中'){\n        teamorder = 1;\n      }\n\t\t\tif(member.teamyeji=='计算中' || teamorder){\n\t\t\t  var data = await that.getyeji(member.id);\n        if(member.teamyeji == '计算中'){\n          member.teamyeji = data.teamyeji || 0;\n          member.selfyeji = data.selfyeji || 0;\n          member.team_down_total = data.team_down_total || 0;\n        }\n\t\t\t  \n        if(teamorder){\n          member.team_order_count = data.team_order_count || 0;\n          member.team_order_money = data.team_order_money || 0;\n        }\n        \n        datalist[i] = member;\n      }\n\t\t}\n\t\tthat.datalist = datalist;\n\t\tconsole.log('请求完成2'); // 处理响应\n\t},\n\tgetyeji: function (mid) {\n\t\tvar that = this;\n\t\tconsole.log('请求'+mid);\n\t\treturn new Promise((resolve, reject) => {\n\t\t   app.get('ApiAgent/get_member_yeji', {mid:mid,sdate:that.sdate,edate:that.edate,first_mid:that.first_mid}, function (res) {\n\t\t\t\tvar data = res.data\n\t\t\t\tresolve(data);\n\t\t\t});\n\t\t   \n\t\t});\n\t},\n    changetab: function (st) {\n\t\t\tthis.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n\t\tgivemoneyshow:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tthat.tomid = id;\n\t\t\tthat.$refs.dialogmoneyInput.open();\n\t\t},\n\t\tgivescoreshow:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tthat.tomid = id;\n\t\t\tthat.$refs.dialogscoreInput.open();\n\t\t},\n\t\tgivemoney:function(done, money){\n\t\t\tvar that = this;\n\t\t\tvar id = that.tomid;\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiAgent/givemoney', {id:id,money:money}, function (res) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (res.status == 0) {\n          app.error(res.msg);\n        } else {\n          app.success(res.msg);\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tthat.$refs.dialogmoneyInput.close();\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tgivescore:function(done, score){\n\t\t\tvar that = this;\n\t\t\tvar id = that.tomid;\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiAgent/givescore', {id:id,score:score}, function (res) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (res.status == 0) {\n          app.error(res.msg);\n        } else {\n          app.success(res.msg);\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tthat.$refs.dialogscoreInput.close();\n\t\t\t\t}\n\t\t\t})\n\t\t},\n    searchChange: function (e) {\n      this.keyword = e.detail.value;\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword;\n      that.getdata();\n    },\n\t\tshowDialog:function(e){\n\t\t\tlet that = this;\n\t\t\tthat.tempMid = e.currentTarget.dataset.id;\n\t\t\tthat.tempLevelid = e.currentTarget.dataset.levelid;\n\t\t\tthat.tempLevelsort = e.currentTarget.dataset.levelsort;\n\t\t\tthis.dialogShow = !this.dialogShow\n\t\t},\n\t\tchangeLevel: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar mid = that.tempMid;\n\t\t\tvar levelId = e.currentTarget.dataset.id;\n\t\t\tvar levelName = e.currentTarget.dataset.name;\n\t\t\tapp.confirm('确定要升级为'+levelName+'吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t  app.post('ApiAgent/levelUp', {mid: mid,levelId:levelId}, function (res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t  app.error(res.msg);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\t\tthat.dialogShow = false;\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}\n\t\t\t  });\n\t\t\t});\n\t\t},\n\t\ttoteam:function(mid){\n\t\t\tif(this.team_auth){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl:'/activity/commission/myteam?mid='+mid\n\t\t\t\t})\n\t\t\t}\n\t\t\treturn;\n\n\t\t},\n\t\ttoCheckDate(){\n\t\t\tapp.goto('../../pagesExt/checkdate/checkDate?ys=2&type=1&t_mode=5');\n\t\t},\n\t\t clearDate(){\n\t\t  \tvar that  = this;\n\t\t\tthat.startDate = '';\n\t\t\tthat.endDate = '';\n\t\t  \tuni.pageScrollTo({\n\t\t  \t  scrollTop: 0,\n\t\t  \t  duration: 0\n\t\t  \t});\n\t\t  \tthis.getdata();\n\t\t  },\n      callphone:function(e) {\n      \tvar phone = e.currentTarget.dataset.phone;\n      \tuni.makePhoneCall({\n      \t\tphoneNumber: phone,\n      \t\tfail: function () {\n      \t\t}\n      \t});\n      },\n\t  chooseLevel: function (e) {\n\t\t  \n\t\t  this.levelDialogShow = true;\n\t\t},\n\t\thideTimeDialog: function () {\n\t\t  this.levelDialogShow = false;\n\t\t},\n\t\tlevelRadioChange: function (e) {\n\t\t  var that = this;\n\t\t  that.checkLevelname = e.currentTarget.dataset.name;\n\t\t  that.checkLevelid = e.currentTarget.dataset.id;\n\t\t  that.levelDialogShow = false;\n\t\t  that.getdata();\n\t\t},\n\t\tchooseMonth:function(e){\n\t\t\tthis.monthindex  = e.detail.value;\n\t\t\tthis.month_value = this.month_item[this.monthindex]\n\t\t\tthis.month_text =this.month_value +'团队总业绩';\n\t\t\tthis.getdata();\n\t\t},\n    //查看下级人员、查看下级成交订单\n    toDown: function (e) {\r\n      if(this.first_mid == 0){\r\n        this.first_mid = this.userinfo.id;\r\n      }\n      app.goto('/activity/commission/myteam?mid='+e.currentTarget.dataset.mid+'&first_mid='+this.first_mid);\n    },\n\tchangedown:function(mid){\n\t\tvar that = this;\n\t\tthat.change_mid = mid;\n\t\tapp.showLoading();\n\t\tapp.get('ApiAgent/change_down_user', {now_down: mid}, function (data) {\n\t\t\tapp.showLoading(false);\n\t\t\tif(data.status!=1){\n\t\t\t\tapp.alert(data.msg);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.select_member = data.member;\n\t\t\tthat.change_downs = data.change_downs;\n\t\t\tthat.$refs.dialogChangeDown.open();\n\t\t});\n\t},\n\ttochange:function(mid){\n\t\tvar that = this;\n\t\tvar select_member = that.select_member;\n\t\tvar old_down = that.old_down;\n\t\tapp.confirm('确认现会员ID:'+select_member.id+'与'+old_down+'换位?', function () {\n\t\t\tapp.showLoading();\n\t\t  app.post('ApiAgent/change_down_user', {new_down: select_member.id,old_down:old_down}, function (data) {\n\t\t\t  if(data.status!=1){\n\t\t\t\t  app.error(data.msg);\n\t\t\t\t  return;\n\t\t\t  }\n\t\t\tapp.showLoading(false);\n\t\t\tapp.success(data.msg);\n\t\t\tthat.old_down = 0;\n\t\t\tthat.$refs.dialogChangeDown.close();\n\t\t\tsetTimeout(function () {\n\t\t\t  that.getdata();\n\t\t\t}, 1000);\n\t\t  });\n\t\t});\n\t\t\n\t},\n\tclosechangedown:function(){\n\t\tthis.$refs.dialogChangeDown.close();\n\t},\n\tolddownchange:function(e){\n\t\tconsole.log(e);\n\t\tvar old_down = e.detail.value;\n\t\tthis.old_down = old_down;\n    },\n    setDate(date) {\n      const currentDate = new Date();\n      this.selectedDateRange = date;\n\n      const formatDate = (date) => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n\n      if (date === 1) {\n        const today = formatDate(currentDate);\n        this.sdate = today;\n        this.edate = today;\n      } else if (date === 2) {\n        const sevenDaysAgo = new Date(currentDate.setDate(currentDate.getDate() - 6));\n        const today = formatDate(new Date());\n        this.sdate = formatDate(sevenDaysAgo);\n        this.edate = today;\n      } else if (date === 3) {\n        const thirtyDaysAgo = new Date(currentDate.setDate(currentDate.getDate() - 29));\n        const today = formatDate(new Date());\n        this.sdate = formatDate(thirtyDaysAgo);\n        this.edate = today;\n      }\n      \n      this.getdata();\n    },\n    toSelectDate(){\n      this.selectedDateRange = 4;\n    \tapp.goto('../../pagesExt/checkdate/checkDate?ys=2&type=1&t_mode=5&otherParam=1');\n    },\n\t}\n};\n</script>\n<style>\n\n.topsearch{width:94%;margin:16rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n\n.content{width:94%;margin:0 3%;border-radius:16rpx;background: #fff;margin-top: 20rpx;}\n.content .label{display:flex;width: 100%;padding: 16rpx;color: #333;}\n.content .label .t1{flex:1}\n.content .label .t2{ width:300rpx;text-align:right}\n\n.content .item{width: 100%;padding:32rpx 20rpx;border-top: 1px #eaeaea solid;min-height: 112rpx;display:flex;align-items:center;}\n.content .item image{width: 90rpx;height: 90rpx;border-radius:4px}\n.content .item .f1{display:flex;flex:1;align-items:center;}\n.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}\n.content .item .f1 .t2 .x1{color: #333;font-size:26rpx;}\n.content .item .f1 .t2 .x2{color: #999;font-size:24rpx;}\n.content .item .f1 .t2 .x3{font-size:24rpx;}\n\n.content .item .f2{display:flex;flex-direction:column;width:200rpx;border-left:1px solid #eee;text-align: right;}\n.content .item .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}\n.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}\n.content .item .f2 .t3{ display:flex;justify-content:flex-end;margin-top:10rpx; flex-wrap: wrap;}\n.content .item .f2 .t3 .x1{padding:8rpx;border:1px solid #ccc;border-radius:6rpx;font-size:22rpx;color:#666;margin-top: 10rpx;margin-left: 6rpx;}\n.content .item .f2 .t4{ display:flex;margin-top:10rpx;margin-left: 10rpx;color: #666; flex-wrap: wrap;font-size:18rpx;text-align: left}\n.sheet-item {display: flex;align-items: center;padding:20rpx 30rpx;}\n.sheet-item .item-img {width: 44rpx;height: 44rpx;}\n.sheet-item .item-text {display: block;color: #333;height: 100%;padding: 20rpx;font-size: 32rpx;position: relative; width: 90%;}\n.sheet-item .item-text:after {position: absolute;content: '';height: 1rpx;width: 100%;bottom: 0;left: 0;border-bottom: 1rpx solid #eee;}\n.man-btn {\n\tline-height: 100rpx;\n\ttext-align: center;\n\tbackground: #FFFFFF;\n\tfont-size: 30rpx;\n\tcolor: #FF4015;\n}\n\t\n\t.body_data {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: normal;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: #686868;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfloat: right;\n\t\t/* border: 1rpx solid #cac5c5;\n\t\tpadding: 2px;\n\t\tmargin-left: 5px; */\n\t}\n\t.body_detail {\n\t\theight: 35rpx;\n\t\twidth: 35rpx;\n\t\tmargin-left: 10rpx;\n\t}\n\t.t_date .x1{height:40rpx;line-height:40rpx;padding:0 8rpx;border:1px solid #ccc;border-radius:6rpx;font-size:22rpx;color:#666;margin-left: 10rpx;}\n\t.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n\t.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n\t.pstime-item .radio .radio-img{width:100%;height:100%}\n\t.content .yejilabel{/* display:flex; */width: 100%;padding: 16rpx;color: #333;justify-content: space-between;flex-wrap: wrap;line-height:60rpx;border-top:1rpx solid #f5f5f5}\n\t/* .content .yejilabel .t1{flex-shrink: 0;width: 50%;} */\n\t.content .sx{padding:20rpx 16rpx; margin:0}\n\t.levelup-num{padding:10rpx 20rpx}\n\t.levelup-num .list{flex-wrap: wrap;}\n\t.levelup-num .t1{font-weight: 700;}\n\t.num{padding: 10rpx;width: 50%;}\n\t\n\t.popup_centent_view{width: 90vw;background: #fff;border-radius: 15rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;}\n\t\t.popup_centent_view .popup_title_view{width: 100%;padding: 30rpx 0rpx;color: #3D3D3D;font-size: 36rpx;text-align: center;}\n\t\t.popup_centent_view .popup_input-view{width: 90%;margin: 0 auto;padding: 40rpx 20rpx 0rpx;}\n\t\t.popup_centent_view .popup_input-view .options-view{padding: 30rpx 0rpx;}\n\t\t.popup_input-view .options-view .title-text{font-size: 32rpx;color: #000;}\n\t\t.popup_input-view .options-view .num-options{margin-left: 40rpx;}\n\t\t.popup_input-view .options-view .num-options .num-but{width: 40rpx;height:40rpx;border:2px #46DE99 solid;border-radius:10rpx;background:#fff;\n\t\t\tdisplay: flex;align-items: center;justify-content: center;color: #46DE99;}\n\t\t.popup_input-view .options-view .num-options .num-input-view{width: 140rpx;height: 70rpx;background: #efefef;margin: 0rpx 30rpx;border-radius: 10rpx;}\n\t\t.options-view .num-options .num-input-view input{width: 100%;height: 100%;font-size: 32rpx;font-weight: bold;color: #000;}\n\t\t.popup_centent_view .popup_but_view{width: 90%;margin: 0 auto;display: flex;align-items: center;justify-content: space-around;padding: 30rpx;}\n\t\t.popup_centent_view .popup_but_view .poput_options_but{width: 190rpx;height: 72rpx;line-height: 72rpx;text-align: center;border-radius: 10rpx;\n\t\tfont-size: 32rpx;color: #000;border: 1px  #DFDFDF solid;}\n\t\t.popup_centent_view .popup_but_view .success{background: #46DE99;color: #FFFFFF;border: unset;}\n    .date-search{padding: 3%;}\n    .date-btn{color: #999;width: 20.5%;text-align: center;padding: 10rpx;border-radius: 5rpx;}\n    .show-search-date {margin-top: 20px;color: #333;text-align: center;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myteam.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myteam.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370956\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}