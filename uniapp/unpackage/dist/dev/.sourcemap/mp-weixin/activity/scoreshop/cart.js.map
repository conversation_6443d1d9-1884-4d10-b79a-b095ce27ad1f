{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/cart.vue?64d8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/cart.vue?4394", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/cart.vue?9c02", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/cart.vue?079a", "uni-app:///activity/scoreshop/cart.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/cart.vue?3708", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/cart.vue?1925"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "initdata", "pre_url", "scoreshopindexurl", "indexurl", "cartlist", "totalscore", "totalmoney", "selectedcount", "onLoad", "onShow", "onPullDownRefresh", "methods", "getdata", "that", "app", "bid", "isnew", "calculate", "changeradio", "changeradio2", "isallchecked", "cartdelete", "id", "setTimeout", "cartdeleteb", "toOrder", "prodata", "gwcplus", "num", "gwcminus", "gwcinput", "addcart"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Dt1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;MACA;MACAA;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACAA;QACA;QACA;UACAT;QACA;QACAS;QACAA;MACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;YACAX;YACAD;YACAE;UACA;QACA;MACA;MACAM;MACAA;MACAA;IACA;IACAK;MACA;MACA;MACA;MACA;MACA;QACAd;MACA;QACAA;MACA;MACA;QACAA;MACA;MACAS;MACAA;IACA;IACAM;MACA;MACA;MACA;MACA;MACA;MACA;QACAf;MACA;QACAA;MACA;MACA;MACA;QACA;UACAgB;QACA;MACA;MACA;QACAhB;MACA;QACAA;MACA;MACAS;MACAA;IACA;IACAQ;MACA;MACA;MACAP;QACAA;UAAAQ;QAAA;UACAR;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;MACAV;QACAA;UAAAC;QAAA;UACAD;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;YACA;YACAC;UACA;QACA;MACA;MACA;QACAZ;QACA;MACA;MACAA;IACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;QACAb;QACA;MACA;MACA;MACAV;MACA;MACA;MACA;MACAU;QAAAQ;QAAAM;MAAA;QACA;UACA;QAAA,CACA;UACAd;UACAV;QACA;MACA;IACA;IACA;IACAyB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAzB;MACA;MACA;MAEA;MACAU;QAAAQ;QAAAM;MAAA;QACA;UACA;QAAA,CACA;UACAd;UACAV;QACA;MACA;IACA;IACA;IACA0B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAhB;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACAV;MACA;MACA;MAEA;MACAU;QAAAQ;QAAAM;MAAA;QACA;UACA;QAAA,CACA;UACAd;QACA;MACA;IACA;IACAiB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzSA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/scoreshop/cart.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/scoreshop/cart.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cart.vue?vue&type=template&id=736ff0a9&\"\nvar renderjs\nimport script from \"./cart.vue?vue&type=script&lang=js&\"\nexport * from \"./cart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cart.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/scoreshop/cart.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=template&id=736ff0a9&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.cartlist.length : null\n  var l1 =\n    _vm.isload && g0 > 0\n      ? _vm.__map(_vm.cartlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = item.checked ? _vm.t(\"color1\") : null\n          var g1 = item.prolist.length\n          var m2 = _vm.t(\"color1\")\n          var m3 = _vm.t(\"积分\")\n          var l0 = _vm.__map(item.prolist, function (item2, index2) {\n            var $orig = _vm.__get_orig(item2)\n            var m1 = item2.checked ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m1: m1,\n            }\n          })\n          return {\n            $orig: $orig,\n            m0: m0,\n            g1: g1,\n            m2: m2,\n            m3: m3,\n            l0: l0,\n          }\n        })\n      : null\n  var m4 = _vm.isload && g0 > 0 ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload && g0 > 0 ? _vm.t(\"积分\") : null\n  var m6 = _vm.isload && g0 > 0 ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && g0 > 0 ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<block v-if=\"cartlist.length>0\">\n\t\t\t<view class=\"cartmain\">\n\t\t\t\t<block v-for=\"(item, index) in cartlist\" :key=\"item.bid\">\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<view class=\"btitle\">\n\t\t\t\t\t\t\t<view @tap.stop=\"changeradio\" :data-index=\"index\" class=\"radio\" :style=\"item.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t\t<view class=\"btitle-name\" @tap=\"goto\" :data-url=\"item.bid==0?indexurl:'/pagesExt/business/index?id=' + item.business.id\">{{item.business.name}}</view>\n\t\t\t\t\t\t\t<view class=\"flex1\"> </view>\n\t\t\t\t\t\t\t<view class=\"btitle-del\" @tap=\"cartdeleteb\" :data-bid=\"item.bid\"><image class=\"img\" :src=\"pre_url+'/static/img/del.png'\"/><text style=\"margin-left:10rpx\">删除</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"content\" v-for=\"(item2,index2) in item.prolist\" :key=\"index2\">\n\t\t\t\t\t\t\t<view @tap.stop=\"changeradio2\" :data-index=\"index\" :data-index2=\"index2\" class=\"radio\" :style=\"item2.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t\t<view class=\"proinfo\" :style=\"(item.prolist).length == index2+1 ? 'border:0' : ''\">\n\t\t\t\t\t\t\t\t<image :src=\"item2.ggpic?item2.ggpic:item2.product.pic\" class=\"img\" @tap=\"goto\" :data-url=\"'/activity/scoreshop/product?id=' + item2.product.id\"/>\n\t\t\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"title\"><text>{{item2.product.name}}</text></view>\n\t\t\t\t\t\t\t\t\t<view class=\"desc\"><text v-if=\"item2.product.ggname\">规格：￥{{item2.product.ggname}}</text><text v-else>价值：￥{{item2.product.sell_price}}</text></view>\n\t\t\t\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\"><text v-if=\"item2.product.money_price>0\">￥{{item2.product.money_price}}+</text>{{item2.product.score_price}}{{t('积分')}}</view>\n\n\t\t\t\t\t\t\t\t\t<view class=\"addnum\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"minus\" @tap.stop=\"gwcminus\" :data-index=\"index\" :data-index2=\"index2\" :data-cartid=\"item2.id\" :data-num=\"item2.num\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" /></view>\n\t\t\t\t\t\t\t\t\t\t<input class=\"input\" @tap.stop=\"\" type=\"number\" :value=\"item2.num\" @blur=\"gwcinput\" :data-max=\"item2.stock\" :data-index=\"index\" :data-index2=\"index2\" :data-cartid=\"item2.id\" :data-num=\"item2.num\"></input>\n\t\t\t\t\t\t\t\t\t\t<view class=\"plus\" @tap.stop=\"gwcplus\" :data-index=\"index\" :data-index2=\"index2\" :data-max=\"item2.stock\" :data-num=\"item2.num\" :data-cartid=\"item2.id\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"prodel\" @tap.stop=\"cartdelete\" :data-cartid=\"item2.id\"><image class=\"prodel-img\" :src=\"pre_url+'/static/img/del.png'\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view style=\"height:auto;position:relative\">\n\t\t\t\t<view style=\"width:100%;height:110rpx\"></view>\n\t\t\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t<view class=\"text1\">合计：</view>\n\t\t\t\t\t<view class=\"text2\" :style=\"{color:t('color1')}\"><text v-if=\"totalmoney>0\">￥{{totalmoney}}+</text><text>{{totalscore}}{{t('积分')}}</text></view>\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t<view class=\"op\" @tap=\"toOrder\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">去结算</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</block>\n\t\t<block v-else>\n\t\t\t<view class=\"data-empty\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/cartnull.png'\" class=\"data-empty-img\" style=\"width:120rpx;height:120rpx\"/>\n\t\t\t\t<view class=\"data-empty-text\" style=\"margin-top:20rpx;font-size:24rpx\">购物车空空如也~</view>\n\t\t\t\t<button style=\"width:400rpx;border:1px solid #ff6801;border-radius:6rpx;background:#ff6801;margin-top:20px;color:#fff\" @tap=\"goto\" :data-url=\"scoreshopindexurl\">去选购</button>\n\t\t\t</view>\n\t\t</block>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tinitdata:{},\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tscoreshopindexurl:'/activity/scoreshop/index',\n\t\t\tindexurl:app.globalData.indexurl,\n\n\t\t\tcartlist:[],\n\t\t\ttotalscore:0,\n      totalmoney: '0.00',\n      selectedcount: 0,\n    };\n  },\n  \n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n  },\n\tonShow:function(){\n\t\tthis.getdata();\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n    getdata: function (){\n      var that = this;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n\t\t\tif(bid){\n\t\t\t\tthat.scoreshopindexurl = '/activity/scoreshop/index?bid='+bid;\n\t\t\t}\n\t\t\tthat.loading = true;\n      app.get('ApiScoreshop/cart', {bid:bid,isnew:1}, function (res) {\n\t\t\t\tthat.loading = false;\n        that.cartlist = res.cartlist;\n        var cartlist = res.cartlist;\n        for (var i in cartlist) {\n\t\t\t\t\tcartlist[i].checked = true;\n        }\n        that.calculate();\n\t\t\t\tthat.loaded();\n      });\n    },\n    calculate: function () {\n      var that = this;\n      var cartlist = that.cartlist;\n      var totalmoney = 0.00;\n      var totalscore = 0;\n      var selectedcount = 0;\n      for (var i in cartlist) {\n\t\t\t\tfor(var j in cartlist[i].prolist){\n\t\t\t\t\tif(cartlist[i].prolist[j].checked){\n\t\t\t\t\t\tvar thispro = cartlist[i].prolist[j];\n\t\t\t\t\t\ttotalmoney += thispro.product.money_price * thispro.num;\n\t\t\t\t\t\ttotalscore += thispro.product.score_price * thispro.num;\n\t\t\t\t\t\tselectedcount += thispro.num;\n\t\t\t\t\t}\n\t\t\t\t}\n      }\n      that.totalmoney = totalmoney.toFixed(2);\n      that.totalscore = totalscore;\n      that.selectedcount = selectedcount;\n    },\n    changeradio: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar cartlist = that.cartlist;\n\t\t\tvar checked = cartlist[index].checked;\n\t\t\tif(checked){\n\t\t\t\tcartlist[index].checked = false;\n\t\t\t}else{\n\t\t\t\tcartlist[index].checked = true;\n\t\t\t}\n\t\t\tfor(var i in cartlist[index].prolist){\n\t\t\t\tcartlist[index].prolist[i].checked = cartlist[index].checked;\n\t\t\t}\n\t\t\tthat.cartlist = cartlist;\n\t\t\tthat.calculate();\n    },\n    changeradio2: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar index2 = e.currentTarget.dataset.index2;\n\t\t\tvar cartlist = that.cartlist;\n\t\t\tvar checked = cartlist[index].prolist[index2].checked;\n\t\t\tif(checked){\n\t\t\t\tcartlist[index].prolist[index2].checked = false;\n\t\t\t}else{\n\t\t\t\tcartlist[index].prolist[index2].checked = true;\n\t\t\t}\n\t\t\tvar isallchecked = true;\n\t\t\tfor(var i in cartlist[index].prolist){\n\t\t\t\tif(cartlist[index].prolist[i].checked == false){\n\t\t\t\t\tisallchecked = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(isallchecked){\n\t\t\t\tcartlist[index].checked = true;\n\t\t\t}else{\n\t\t\t\tcartlist[index].checked = false;\n\t\t\t}\n\t\t\tthat.cartlist = cartlist;\n\t\t\tthat.calculate();\n    },\n    cartdelete: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.cartid;\n      app.confirm('确定要从购物车移除吗?', function () {\n        app.post('ApiScoreshop/cartdelete', {id: id}, function (data) {\n          app.success('操作成功');\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n\t\tcartdeleteb:function(e){\n\t\t\tvar that = this;\n\t\t\tvar bid   = e.currentTarget.dataset.bid;\n\t\t\tapp.confirm('确定要从购物车移除吗?', function () {\n\t\t\t\tapp.post('ApiScoreshop/cartdelete', {bid: bid}, function (data) {\n\t\t\t\t\tapp.success('操作成功');\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n    toOrder: function () {\n      var that = this;\n\t\t\tvar cartlist = that.cartlist;\n\t\t\tvar ids = [];\n\t\t\tvar prodata = [];\n\t\t\tfor(var i in cartlist){\n\t\t\t\tfor(var j in cartlist[i].prolist){\n\t\t\t\t\tif(cartlist[i].prolist[j].checked){\n\t\t\t\t\t\tvar thispro = cartlist[i].prolist[j];\n\t\t\t\t\t\tvar tmpprostr = thispro.product.id + ',' + thispro.num + ',' + thispro.ggid;\n\t\t\t\t\t\tprodata.push(tmpprostr);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (prodata == undefined || prodata.length == 0) {\n\t\t\t\tapp.error('请先选择产品');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tapp.goto('buy?&prodata=' + prodata.join('-'));\n    },\n    //加\n    gwcplus: function (e) {\n\t\t\tvar index  = parseInt(e.currentTarget.dataset.index);\n\t\t\tvar index2 = parseInt(e.currentTarget.dataset.index2);\n\t\t\tvar cartid = e.currentTarget.dataset.cartid;\n\t\t\tvar num = parseInt(e.currentTarget.dataset.num);\n\t\t\tvar maxnum = parseInt(e.currentTarget.dataset.max);\n\t\t\tif (num >= maxnum) {\n\t\t\t\t\tapp.error('库存不足');\n\t\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar cartlist = this.cartlist;\n\t\t\tcartlist[index].prolist[index2].num++;\n\t\t\tthis.cartlist = cartlist\n\t\t\tthis.calculate();\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiScoreshop/cartChangenum', {id: cartid,num: num + 1}, function (data){\n\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t//that.getdata();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\tcartlist[index].prolist[index2].num--;\n\t\t\t\t}\n\t\t\t});\n    },\n    //减\n    gwcminus: function (e) {\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\n\t\t\tvar index2 = parseInt(e.currentTarget.dataset.index2);\n\t\t\tvar cartid = e.currentTarget.dataset.cartid;\n\t\t\tvar num = parseInt(e.currentTarget.dataset.num);\n\t\t\tif (num == 1) return;\n\t\t\tvar maxnum = parseInt(e.currentTarget.dataset.max);\n\t\t\tvar cartlist = this.cartlist;\n\t\t\tcartlist[index].prolist[index2].num--;\n\t\t\tthis.cartlist = cartlist\n\t\t\tthis.calculate();\n\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiScoreshop/cartChangenum', {id: cartid,num: num - 1}, function (data) {\n\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t//that.getdata();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\tcartlist[index].prolist[index2].num++;\n\t\t\t\t}\n\t\t\t});\n    },\n    //输入\n    gwcinput: function (e) {\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\n\t\t\tvar index2 = parseInt(e.currentTarget.dataset.index2);\n\t\t\tvar maxnum = parseInt(e.currentTarget.dataset.max);\n\t\t\tvar cartid = e.currentTarget.dataset.cartid;\n\t\t\tvar num = e.currentTarget.dataset.num;\n\t\t\tvar newnum = parseInt(e.detail.value);\n\t\t\tif (num == newnum) return;\n\t\t\tif (newnum < 1) {\n\t\t\t\tapp.error('最小数量为1');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (newnum > maxnum) {\n\t\t\t\tapp.error('库存不足');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar cartlist = this.cartlist;\n\t\t\tcartlist[index].prolist[index2].num = newnum;\n\t\t\tthis.cartlist = cartlist\n\t\t\tthis.calculate();\n\t\t\t\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiScoreshop/cartChangenum', {id: cartid,num: newnum}, function (data) {\n\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t//that.getdata();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t}\n\t\t\t});\n    },\n\t\taddcart:function(){\n\t\t\tthis.getdata();\n\t\t}\n  }\n};\n</script>\n<style>\n.container{height:100%}\n.cartmain .item {width: 94%;margin:20rpx 3%;background: #fff;border-radius:20rpx;padding:30rpx 3%;}\n.cartmain .item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.cartmain .item .radio .radio-img{width:100%;height:100%}\n\n.cartmain .item .btitle{width:100%;display:flex;align-items:center;margin-bottom:30rpx}\n.cartmain .item .btitle-name{color:#222222;font-weight:bold;font-size:28rpx;}\n.cartmain .item .btitle-del{display:flex;align-items:center;color:#999999;font-size:24rpx;}\n.cartmain .item .btitle-del .img{width:24rpx;height:24rpx}\n\n.cartmain .item .content {width:100%;position: relative;display:flex;align-items:center;}\n.cartmain .item .content .proinfo{flex:1;display:flex;padding:20rpx 0;border-bottom:1px solid #f2f2f2}\n.cartmain .item .content .proinfo .img {width: 150rpx;height: 150rpx;}\n.cartmain .item .content .detail {flex:1;margin-left:20rpx;height: 150rpx;position: relative;}\n.cartmain .item .content .detail .title {color: #222222;font-weight:bold;font-size:28rpx;line-height:34rpx;height:68rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.cartmain .item .content .detail .desc {height: 30rpx;line-height: 30rpx;color: #999;overflow: hidden;font-size: 20rpx;}\n.cartmain .item .content .prodel{width:24rpx;height:24rpx;position:absolute;top:60rpx;right:0}\n.cartmain .item .content .prodel-img{width:100%;height:100%}\n.cartmain .item .content .price{height:60rpx;line-height:60rpx;font-size:28rpx;font-weight:bold;display:flex;align-items:center}\n.cartmain .item .content .addnum {position: absolute;right: 0;bottom:0rpx;font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\n.cartmain .item .content .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.cartmain .item .content .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.cartmain .item .content .addnum .img{width:24rpx;height:24rpx}\n.cartmain .item .content .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\n.cartmain .item .content .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx;margin: 0 15rpx;}\n\n.cartmain .item .bottom{width: 94%;margin: 0 3%;border-top: 1px #e5e5e5 solid;padding: 10rpx 0px;overflow: hidden;color: #ccc;display:flex;align-items:center;justify-content:flex-end}\n.cartmain .item .bottom .f1{display:flex;align-items:center;color:#333}\n.cartmain .item .bottom .f1 image{width:40rpx;height:40rpx;border-radius:4px;margin-right:4px}\n.cartmain .item .bottom .op {border: 1px #ff4246 solid;border-radius: 10rpx;color: #ff4246;padding: 0 10rpx;height: 46rpx;line-height: 46rpx;margin-left: 10rpx;}\n\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\n.footer .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\n.footer .radio .radio-img{width:100%;height:100%}\n.footer .text0{color:#666666;font-size:24rpx;}\n.footer .text1 {height: 110rpx;line-height: 110rpx;color:#444;font-weight:bold;font-size:24rpx;}\n.footer .text2 {color: #F64D00;font-size: 36rpx;font-weight:bold}\n.footer .text3 {color: #F64D00;font-size: 28rpx;font-weight:bold}\n.footer .op{width: 216rpx;height: 80rpx;line-height:80rpx;border-radius: 6rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center;margin-left:30rpx}\n\n.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:12rpx 160rpx}\n.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}\n.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\n.xihuan-text .txt{color:#111;font-size:30rpx}\n.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}\n\n.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}\n\n.data-empty {width: 100%; text-align: center; padding-top:100rpx;padding-bottom:100rpx}\n.data-empty-img{ width: 300rpx; height: 300rpx; display: inline-block; }\n.data-empty-text{ display: block; text-align: center; color: #999999; font-size:32rpx; width: 100%; margin-top: 30rpx; } \n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839366244\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}