{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/prolist.vue?266f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/prolist.vue?44a3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/prolist.vue?215a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/prolist.vue?e69b", "uni-app:///activity/scoreshop/prolist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/prolist.vue?b86f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/prolist.vue?6c94"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "textset", "nomore", "nodata", "keyword", "pagenum", "datalist", "history_list", "history_show", "order", "field", "cid", "clist", "glist", "productlisttype", "showfilter", "cpid", "bid", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "gid", "getprolist", "searchChange", "searchbtn", "searchConfirm", "searchproduct", "sortClick", "groupClick", "cateClick", "filterClick", "addHistory", "historylist", "newhistorylist", "historyClick", "deleteSearchHistory"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Fz1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;MACA;MACAA;MACAC;QAAAR;QAAAN;QAAAe;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAH;MACAA;MACAA;MACAA;MACAC;QAAAR;QAAAZ;QAAAD;QAAAM;QAAAD;QAAAiB;QAAAf;QAAAK;MAAA;QACAQ;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAL;MACA;QACA;UACAA;UACAC;QACA;UACAD;UACAC;QACA;MACA;IACA;IACAK;MACA;MACA;MACAN;MACAA;IACA;IACAO;MACA;MACAP;MACAA;MACAA;MACAA;IACA;IACAQ;MACA;MACA;MACAR;MACAA;MACAA;IACA;IACAS;MACA;MACA;MACA;QACAT;MACA;QACAA;MACA;MACAA;IACA;IACAU;MACA;MACA;MACA;QACAV;MACA;QACAA;MACA;MACAA;IACA;IACAW;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACAb;MACAD;IACA;IACAe;MACA;MACA;MACA;MACAf;MACAA;IACA;IACAgB;MACA;MACAhB;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzRA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/scoreshop/prolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/scoreshop/prolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prolist.vue?vue&type=template&id=efecd55c&\"\nvar renderjs\nimport script from \"./prolist.vue?vue&type=script&lang=js&\"\nexport * from \"./prolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/scoreshop/prolist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=template&id=efecd55c&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? !_vm.history_list || _vm.history_list.length == 0 : null\n  var m0 = _vm.isload ? _vm.t(\"积分\") : null\n  var g1 =\n    _vm.isload && _vm.showfilter ? _vm.glist && _vm.glist.length > 0 : null\n  var g2 =\n    _vm.isload && _vm.showfilter ? _vm.clist && _vm.clist.length > 0 : null\n  var g3 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l0 =\n    _vm.isload && g3\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.t(\"color1\")\n          var m2 = item.score_price > 0 ? _vm.t(\"积分\") : null\n          var m3 = _vm.t(\"color1\")\n          var m4 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"search-container\" :style=\"history_show?'height:100%;':''\">\n\t\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"商品搜索...\" @confirm=\"searchConfirm\" @input=\"searchChange\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"search-history\" v-show=\"history_show\">\n\t\t\t\t<view>\n\t\t\t\t\t<text class=\"search-history-title\">最近搜索</text>\n\t\t\t\t\t<view class=\"delete-search-history\" @tap=\"deleteSearchHistory\">\n\t\t\t\t\t\t<text class=\"fa fa-trash-o\" style=\"font-size:36rpx\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-history-list\">\n\t\t\t\t\t<view v-for=\"(item, index) in history_list\" :key=\"index\" class=\"search-history-item\" :data-value=\"item\" @tap=\"historyClick\">{{item}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"!history_list || history_list.length==0\"><text class=\"fa fa-exclamation-circle\"></text> 暂无记录\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"search-navbar\" v-show=\"!history_show\">\n\t\t\t\t<view @tap.stop=\"sortClick\" :class=\"'search-navbar-item ' + (!field?'active':'')\" data-field=\"\" data-order=\"\">综合</view>\n\t\t\t\t<view @tap.stop=\"sortClick\" :class=\"'search-navbar-item ' + (field=='sales'?'active':'')\" data-field=\"sales\" data-order=\"desc\">兑换数</view>\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" data-field=\"score_price\" :data-order=\"order=='asc'?'desc':'asc'\">\n\t\t\t\t\t<text :class=\"field=='score_price'?'active':''\">所需{{t('积分')}}</text>\n\t\t\t\t\t<text :class=\"'fa fa-caret-up ' + (field=='score_price'&&order=='asc'?'active':'')\"></text>\n\t\t\t\t\t<text :class=\"'fa fa-caret-down ' + (field=='score_price'&&order=='desc'?'active':'')\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-navbar-item flex-x-center flex-y-center\" @tap.stop=\"filterClick\">筛选<text class=\"iconfont iconshaixuan\" style=\"font-size: 28rpx;margin-left: 5rpx;\"></text></view>\n\t\t\t</view>\n\t\t\t<view class=\"search-filter\" v-if=\"showfilter\">\n\t\t\t\t<view class=\"search-filter-content\" :if=\"glist && glist.length >0\">\n\t\t\t\t\t<block v-for=\"(item, index) in glist\" :key=\"index\">\n\t\t\t\t\t\t<view @tap.stop=\"groupClick\" :data-gid=\"item.id\" style=\"display: flex;align-items: center;justify-content: center;padding: 15rpx 30rpx;\">\n\t\t\t\t\t\t\t<icon type=\"success_no_circle\" size=\"18\" v-if=\"gid == item.id\"></icon>{{item.name}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-filter-content\" :if=\"clist && clist.length >0\" style=\"border-top:1px solid #f5f5f5\">\n\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\n\t\t\t\t\t\t<view @tap.stop=\"cateClick\" :data-cid=\"item.id\" style=\"display: flex;align-items: center;justify-content: center;padding: 15rpx 30rpx;\">\n\t\t\t\t\t\t\t<icon type=\"success_no_circle\" size=\"18\" v-if=\"cid == item.id\"></icon>{{item.name}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-filter-content\" style=\"border-top:1px solid #eee;text-align:right\"><div class=\"close\"><text @tap.stop=\"filterClick\">关闭</text></div></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"product-container\">\n\t\t\t<block v-if=\"datalist && datalist.length>0\">\n\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'product?id=' + item.id\" class=\"product-item\">\n\t\t\t\t\t<view class=\"itemcontent\">\n\t\t\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t\t\t</view> \n\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t<view class=\"p1\">{{item.name}}</view>\n\t\t\t\t\t\t\t<view class=\"p2\"><block v-if=\"item.sell_price>0\">价值{{item.sell_price}}元</block></view>\n\t\t\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t\t\t<view class=\"t1 flex\">\n\t\t\t\t\t\t\t\t\t<view class=\"x1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:13px\">\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.score_price>0\">{{item.score_price}}{{t('积分')}}</block>\n\t\t\t\t\t\t\t\t\t\t\t\t <block v-if=\"item.money_price>0\"><block v-if=\"item.score_price>0\">+</block>{{item.money_price}}元</block>\n\t\t\t\t\t\t\t\t\t\t\t </text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"itembottom\">\n\t\t\t\t\t\t<view class=\"f1\">已兑换<text :style=\"{color:t('color1')}\"> {{item.sales}} </text>件</view>\n\t\t\t\t\t\t<button class=\"f2\" :style=\"{background:t('color1')}\">立即兑换</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</block>\n\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\n\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\n\t\t\t<loading v-if=\"loading\"></loading>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\ttextset:{},\n\t\t\tnomore:false,\n\t\t\tnodata:false,\n      keyword: '',\n      pagenum: 1,\n      datalist: [],\n      history_list: [],\n      history_show: false,\n      order: '',\n\t\t\tfield:'',\n      cid: \"\",\n      clist: [],\n      glist: [],\n      productlisttype: 'item2',\n      showfilter: \"\",\n\t\t\tcpid:0,\n\t\t\tbid:0,\n      pre_url:app.globalData.pre_url,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.cid = this.opt.cid;\n\t\tthis.keyword = this.opt.keyword;\n\t\tthis.bid = this.opt.bid || 0;\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getprolist();\n    }\n  },\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.pagenum = 1;\n\t\t\tthat.datalist = [];\n\t\t\tvar cid = that.opt.cid;\n\t\t\tvar gid = that.opt.gid;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiScoreshop/prolist', {bid:that.bid,cid: cid,gid: gid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t  that.clist = res.clist;\n\t\t\t  that.textset = app.globalData.textset;\n\t\t\t\tthat.loaded();\n\t\t\t\tthat.getprolist();\n\t\t\t});\n\t\t},\n    getprolist: function () {\n      var that = this;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n      var order = that.order;\n      var field = that.field;\n      var gid = that.gid;\n      var cid = that.cid;\n      var cpid = that.cpid;\n      that.history_show = false;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n      app.post('ApiScoreshop/getprolist',{bid:that.bid,pagenum: pagenum,keyword: keyword,field: field,order: order,gid: gid,cid: cid,cpid:cpid}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.data;\n        if (pagenum == 1) {\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    searchChange: function (e) {\n      this.keyword = e.detail.value;\n      if (e.detail.value == '') {\n        this.history_show = true;\n        this.datalist = [];\n      }\n    },\n    searchbtn: function () {\n      var that = this;\n      if (that.history_show) {\n        var keyword = that.keyword;\n        that.searchproduct();\n      } else {\n        if (that.productlisttype == 'itemlist') {\n          that.productlisttype = 'item2';\n          app.setCache('productlisttype', 'item2');\n        } else {\n          that.productlisttype = 'itemlist';\n          app.setCache('productlisttype', 'itemlist');\n        }\n      }\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.searchproduct();\n    },\n    searchproduct: function () {\n      var that = this;\n      that.pagenum = 1;\n      that.datalist = [];\n      that.addHistory();\n      that.getprolist();\n    },\n    sortClick: function (e) {\n      var that = this;\n      var t = e.currentTarget.dataset;\n      that.field = t.field;\n      that.order = t.order;\n      that.searchproduct();\n    },\n    groupClick: function (e) {\n      var that = this;\n      var gid = e.currentTarget.dataset.gid;\n      if (gid == this.gid) {\n        that.gid = '';\n      } else {\n        that.gid = gid\n      }\n      that.searchproduct();\n    },\n    cateClick: function (e) {\n      var that = this;\n      var cid = e.currentTarget.dataset.cid;\n      if (cid == this.cid) {\n        that.cid = '';\n      } else {\n        that.cid = cid;\n      }\n      that.searchproduct();\n    },\n    filterClick: function () {\n      this.showfilter = !this.showfilter\n    },\n    addHistory: function () {\n      var that = this;\n      var keyword = that.keyword;\n      if (app.isNull(keyword)) return;\n      var historylist = app.getCache('search_history_list');\n      if (app.isNull(historylist)) historylist = [];\n      historylist.unshift(keyword);\n      var newhistorylist = [];\n      for (var i in historylist) {\n        if (historylist[i] != keyword || i == 0) {\n          newhistorylist.push(historylist[i]);\n        }\n      }\n      if (newhistorylist.length > 5) newhistorylist.splice(5, 1);\n      app.setCache('search_history_list', newhistorylist);\n      that.history_list = newhistorylist\n    },\n    historyClick: function (e){\n      var that = this;\n      var keyword = e.currentTarget.dataset.value;\n      if (keyword.length == 0) return;\n      that.keyword = keyword;\n      that.searchproduct();\n    },\n    deleteSearchHistory: function () {\n      var that = this;\n      that.history_list = null;\n      app.removeCache(\"search_history_list\");\n    }\n  }\n};\n</script>\n<style>\n.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}\n.topsearch{width:100%;padding:10rpx 20rpx;}\n.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f2f2f2;flex:1}\n.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.search-btn{color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}\n.search-navbar {display: flex;text-align: center;align-items:center;padding:4rpx 0}\n.search-navbar-item {flex: 1;height: 60rpx;line-height: 60rpx;position: relative;}\n.search-navbar .active{color:#ff4544!important;}\n.search-navbar-item .fa-caret-up {position: absolute;top: 12rpx;padding: 0 6rpx;font-size: 24rpx;color:#ddd;}\n.search-navbar-item .fa-caret-down {position: absolute;top: 24rpx;padding: 0 6rpx;font-size: 24rpx;color:#ddd;}\n.search-history {padding: 24rpx 34rpx;}\n.search-history .search-history-title {color: #666;}\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\n.search-history-list {padding: 24rpx 0 0 0;}\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\n\n.search-filter {display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:6rpx;border-top:1px solid #ddd;border-bottom:1px solid #f5f5f0;}\n.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 0;}\n.search-filter-content button{margin:4rpx 10rpx;font-size: 24rpx;display:flex;}\n.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}\n.search-filter button .icon{margin-top:6rpx;height:54rpx;}\n.search-navbar .active{color:#ff4544!important;}\n\n.product-container {width: 100%;margin-top: 176rpx;font-size:26rpx;padding:0 14rpx}\n\n.product-item{display:flex;flex-direction:column;background: #fff; padding:0 20rpx;margin:0;margin-bottom:20rpx;border-radius:20rpx}\n.product-item .itemcontent{display:flex;height:220rpx;border-bottom:1px solid #E6E6E6;padding:20rpx 0}\n.product-item .product-pic {width: 180rpx;height: 180rpx; background: #ffffff;overflow:hidden}\n.product-item .product-pic image{width: 100%;height:180rpx;}\n.product-item .product-info {padding:4rpx 10rpx;flex:1}\n.product-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.product-item .product-info .p2{height:50rpx;display:flex;align-items:center;color:#666;font-size:24rpx}\n.product-item .product-info .p3{font-size:32rpx;height:40rpx;line-height:40rpx;display:flex;align-items:center}\n.product-item .product-info .p3 .t1{flex:auto}\n.product-item .product-info .p3 .t1 .x1{font-size:28rpx;font-weight:bold}\n.product-item .product-info .p3 .t1 .x2{margin-left:10rpx;font-size:26rpx;color: #888;}\n.product-item .product-info .p3 .t2{padding:0 16rpx;font-size:22rpx;height:44rpx;line-height:44rpx;overflow: hidden;color:#fff;background:#4fee4f;border:0;border-radius:20rpx;}\n.product-item .product-info .p3 button:after{border:0}\n\n.product-item .itembottom{width:100%;padding:0 20rpx;display:flex;height:100rpx;align-items:center}\n.product-item .itembottom .f1{flex:1;color:#666;font-size:24rpx}\n.product-item .itembottom .f2{color:#fff;width:160rpx;height:56rpx;display:flex;align-items:center;justify-content:center;border-radius:8rpx}\n\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839369953\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}