{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/index.vue?3c68", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/index.vue?d1a5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/index.vue?b0a0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/index.vue?ee35", "uni-app:///activity/scoreshop/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/index.vue?5f6a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/index.vue?cf18"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "score", "pics", "clist", "datalist", "pagenum", "keyword", "nodata", "nomore", "sclist", "bid", "bgurl", "adpidnum", "errMsg", "onShow", "uni", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "onadload", "onadclose", "app", "adpid", "icon", "title", "console", "onaderror", "getdata", "that", "getdatalist", "search"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgFv1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAMAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IAAA,CACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAC;UAAAC;QAAA;UACA;YACAR;cACAS;cACAC;YACA;UACA;YACAV;cACAS;cACAC;YACA;UACA;QACA;MACA;QACA;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAC;MACAP;QAAAZ;MAAA;QACAK;QACAc;QACAA;QACAd;UACAU;QACA;QACAI;QACAA;QACAA;QACAA;QACA;;QASAA;MACA;MACAA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAD;MACAA;MACAA;MACAP;QAAAZ;QAAAL;QAAAC;MAAA;QACAS;QACAc;QACAA;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClOA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/scoreshop/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/scoreshop/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=7f4ba4ce&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/scoreshop/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=7f4ba4ce&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"积分\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1\") : null\n  var m4 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m5 = _vm.isload ? _vm.t(\"积分\") : null\n  var g0 = _vm.isload ? _vm.clist.length : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m6 = _vm.t(\"color1\")\n        var m7 = item.score_price > 0 ? _vm.t(\"积分\") : null\n        var m8 = _vm.t(\"color1\")\n        var m9 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"topimg\" :style=\"{background:t('color1')}\">\n\t\t\t<image class=\"img\" :src=\"bgurl\"/>\n\t\t</view>\n\t\t<view class=\"topinfo\">\n\t\t\t<view class=\"flex\">\n\t\t\t\t<view class=\"myscore\"><text class=\"t1\" :style=\"{color:t('color1')}\">{{score}}</text><text class=\"t2\">我的{{t('积分')}}</text></view>\n\t\t\t\t<view class=\"scorelog\" @tap=\"goto\" data-url=\"/pagesExt/my/scorelog\" :style=\"{color:t('color1'),background:'rgba('+t('color1rgb')+',0.2)'}\">{{t('积分')}}明细<text class=\"iconfont iconjiantou\"></text></view>\n\t\t\t\t<view class=\"orderlog\" @tap=\"goto\" data-url=\"orderlist\">兑换记录<text class=\"iconfont iconjiantou\"></text></view>\n\t\t\t</view>\n\t\t\t<view class=\"search-container\">\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"/>\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"商品搜索...\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" type=\"text\" confirm-type=\"search\" @confirm=\"search\"></input>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<block v-if=\"clist.length > 0\">\n\t\t<view class=\"navbox\">\n\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\n\t\t\t<view style=\"cursor:pointer\" @tap=\"goto\" :data-url=\"item.url?item.url:'prolist?bid='+bid+'&cid='+item.id\" class=\"nav_li\">\n\t\t\t\t<image class=\"img\" :src=\"item.pic\"/>\n\t\t\t\t<view class=\"txt\">{{item.name}}</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\t<view class=\"nav_li\" @tap=\"goto\" :data-url=\"'prolist?bid='+bid\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/all.png'\" class=\"img\"/>\n\t\t\t\t<view class=\"txt\">全部</view>\n\t\t\t</view>\n\t\t</view>\n\t\t</block>\n\n\t\t<view id=\"datalist\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t<view @tap=\"goto\" :data-url=\"'product?id=' + item.id\" class=\"product-item\">\n\t\t\t\t<view class=\"itemcontent\">\n\t\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t\t<image :src=\"item.pic\" class=\"img\"/>\n\t\t\t\t\t</view> \n\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t<view class=\"p1\">{{item.name}}</view>\n\t\t\t\t\t\t<view class=\"p2\"><block v-if=\"item.sell_price>0\">价值{{item.sell_price}}元</block></view>\n\t\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t\t<view class=\"t1 flex\">\n\t\t\t\t\t\t\t\t<view class=\"x1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:13px\">\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.score_price>0\">{{item.score_price}}{{t('积分')}}</block>\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.money_price>0\"><block v-if=\"item.score_price>0\">+</block>{{item.money_price}}元</block>\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"itembottom\">\n\t\t\t\t\t<view class=\"f1\">已兑换<text :style=\"{color:t('color1')}\"> {{item.sales}} </text>件</view>\n\t\t\t\t\t<button class=\"f2\" :style=\"{background:t('color1')}\">立即兑换</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<!-- app全屏广告 -->\n\t<!-- #ifdef APP-PLUS -->\n\t<ad-fullscreen-video ref=\"adFullscreenVideo\" :adpid=\"adpidnum\" :preload=\"false\" :loadnext=\"false\" \n\t\t:disabled=\"true\" v-slot:default=\"{loading, error}\" @load=\"onadload\" @close=\"onadclose\" @error=\"onaderror\">\n\t</ad-fullscreen-video>\n\t<view class=\"ad-error\" v-if=\"errMsg\">{{errMsg}}</view>\n\t<!-- #endif -->\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tscore:0,\n\t\t\tpics:[],\n\t\t\tclist:[],\n      datalist: [],\n      pagenum: 1,\n      keyword: '',\n      nodata: false,\n      nomore: false,\n      sclist: \"\",\n\t\t\tbid:0,\n\t\t\tbgurl:'',\n\t\t\tadpidnum:'',\n\t\t\terrMsg:'',\n    };\n  },\n\t// #ifdef APP-PLUS\n\tonReady() {\n\t\tthis.$refs.adFullscreenVideo.load();\n\t},\n\t// #endif\n\tonShow:function(){\n    if(app.globalData.platform=='wx' && app.globalData.hide_home_button==1){\n      uni.hideHomeButton();\n    }\n\t},\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.bid = this.opt.bid || 0;\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdatalist(true);\n    }\n  },\n  methods: {\n\t\tonadload(e) {\n\t\t\t// 广告数据加载成功\n\t\t},\n\t\tonadclose(e) {\n\t\t\tlet that = this;\n\t\t\tconst detail = e.detail\n\t\t\t// 用户点击了【关闭广告】按钮\n\t\t\tif (detail && detail.isEnded) {\n\t\t\t\t// 正常播放结束\n\t\t\t\tapp.post('ApiScoreshop/givescore',{adpid:that.adpidnum},function (res) {\n\t\t\t\t\tif(res.status == 1){\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon:'none',\n\t\t\t\t\t\t\ttitle:res.msg\n\t\t\t\t\t\t})\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon:'none',\n\t\t\t\t\t\t\ttitle:res.msg\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\t// 播放中途退出\n\t\t\t\tconsole.log(\"onClose \" + detail.isEnded);\n\t\t\t}\n\t\t\tthis.$refs.adFullscreenVideo.load();\n\t\t},\n\t\tonaderror(e) {\n\t\t\t// 广告加载失败\n\t\t\tthis.errMsg = JSON.stringify(e.detail);\n\t\t},\n    getdata: function () {\n      var that = this;\n\t\t\tthat.loading = true;\n      app.post('ApiScoreshop/index', {bid:that.bid}, function (res) {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.isload = true;\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: that.t('积分') + '商城'\n\t\t\t\t});\n        that.clist = res.clist;\n\t\t\t\tthat.pics = res.pics;\n\t\t\t\tthat.score = res.score;\n\t\t\t\tthat.bgurl = res.bgurl;\n\t\t\t\t// 是否开启app全屏广告\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif(res.adset_show && res.adset_show == 1){\n\t\t\t\t\tthat.adpidnum = res.adpid;\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthat.$refs.adFullscreenVideo.show();\n\t\t\t\t\t},1000)\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\tthat.loaded();\n      });\n\t\t\tthat.getdatalist();\n    },\n    getdatalist: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      app.post('ApiScoreshop/getprolist', {bid:that.bid,pagenum: pagenum,keyword: keyword}, function (res) {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.isload = true;\n        var data = res.data;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    search: function (e) {\n      var keyword = e.detail.value;\n      app.goto('prolist?bid='+this.bid+'&keyword='+keyword);\n    }\n  }\n};\n</script>\n<style>\n.topimg{width:100%;height:300rpx;}\n.topimg .img{width:100%;height:100%}\n\n.topinfo{width:94%;margin:0 3%;background:#fff;border-radius:16rpx;overflow:hidden;padding:60rpx 20rpx 20rpx 20rpx;margin-top:-100rpx;position:relative;display:flex;flex-direction:column}\n.topinfo .myscore{margin-left:40rpx;display:flex;flex-direction:column}\n.topinfo .myscore .t1{font-size:48rpx;font-weight:bold}\n.topinfo .myscore .t2{color:#999999;font-size:24rpx;margin-top:10rpx}\n.topinfo .scorelog{margin-left:120rpx;margin-top:30rpx;height:56rpx;line-height:56rpx;width:172rpx;text-align:center;border-radius:28rpx;font-size:24rpx;font-weight:bold;display:flex;justify-content:center;align-items:center}\n.topinfo .orderlog{margin-left:40rpx;margin-top:30rpx;background:rgba(255, 160, 10, 0.2);height:56rpx;line-height:56rpx;width:172rpx;text-align:center;border-radius:28rpx;color:#FFA00A;font-size:24rpx;font-weight:bold;display:flex;justify-content:center;align-items:center}\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;margin-top:20rpx}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\n\n\n.nav_li{width: 25%;text-align: center;box-sizing: border-box;padding:30rpx 0 10rpx;float: left;color:#333}\n.nav_li .img{width:80rpx;height: 80rpx;margin-bottom:10rpx;}\n\n\n\n.weui-loadmore_line .weui-loadmore__tips{background-color:#f6f6f6}\n.swiper-container {width: 100%;} \n.swiper-container image {display: block;width: 100%;}\n.category{width: 100%;padding-top: 20rpx;padding-bottom: 20rpx;flex-direction:row;white-space: nowrap; display:flex;background:#fff;overflow-x:scroll;margin-bottom:20rpx}\n.category .item{width:150rpx;display: inline-block; text-align: center;}\n.category .item image{width:80rpx;height:80rpx;margin: 0 auto;border-radius: 50%;}\n.category .item .t1{display: block;color: #666;}\n.product-item{display:flex;flex-direction:column;background: #fff; padding:0 20rpx;margin:0 20rpx;margin-top:20rpx;border-radius:20rpx}\n.product-item .itemcontent{display:flex;height:220rpx;border-bottom:1px solid #E6E6E6;padding:20rpx 0}\n.product-item .product-pic {width: 180rpx;height: 180rpx; background: #ffffff;overflow:hidden}\n.product-item .product-pic .img{width: 100%;height:180rpx;}\n.product-item .product-info {padding:4rpx 10rpx;flex:1}\n.product-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.product-item .product-info .p2{height:50rpx;display:flex;align-items:center;color:#666;font-size:24rpx}\n.product-item .product-info .p3{font-size:32rpx;height:40rpx;line-height:40rpx;display:flex;align-items:center}\n.product-item .product-info .p3 .t1{flex:auto;font-size:28rpx;font-weight:bold}\n.product-item .product-info .p3 .t1 .x2{margin-left:10rpx;font-size:26rpx;color: #888;}\n.product-item .product-info .p3 .t2{padding:0 16rpx;font-size:22rpx;height:44rpx;line-height:44rpx;overflow: hidden;color:#fff;background:#4fee4f;border:0;border-radius:20rpx;}\n.product-item .product-info .p3 button:after{border:0}\n\n.product-item .itembottom{width:100%;padding:0 20rpx;display:flex;height:100rpx;align-items:center}\n.product-item .itembottom .f1{flex:1;color:#666;font-size:24rpx}\n.product-item .itembottom .f2{color:#fff;width:160rpx;height:56rpx;display:flex;align-items:center;justify-content:center;border-radius:8rpx}\n\n.navbox{width:94%;margin:0 3%;margin-top:20rpx;background: #fff;height: auto;overflow: hidden;border-radius: 20rpx;}\n.nav_li{width: 25%;text-align: center;box-sizing: border-box;padding:30rpx 0 10rpx;float: left;color:#333}\n.nav_li image{width:80rpx;height: 80rpx;margin-bottom:10rpx;}\n\n.plr20{width: 100%;box-sizing: border-box;padding: 0 10rpx;margin-bottom: 10rpx;}\n.tj_title{background: #fff;height: 70rpx;width: 100%;box-sizing: border-box;padding: 0 20rpx;line-height: 70rpx;display: flex;align-items: center;border-radius: 10rpx;}\n.icon1{width: 40rpx;margin-right: 14rpx}\n\n.weui-search-bar__box{ position: relative}\n.weui-icon-search{ position: absolute;width:32rpx; top:12rpx;left:40%}\n.weui-search-bar__input{ background: #fff;padding:0 10px; margin:10px; border-radius:5px; text-align: center;}\n\n.topsearch{width:100%;max-width:750px;padding:16rpx 20rpx;background:#f6f6f6}\n.topsearch .f1{height:70rpx;border-radius:8rpx;border:1px solid #eeeeee;background-color:#fff;flex:1}\n.topsearch .f1 image{width:34rpx;height:34rpx;margin-left:20rpx}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:24rpx;color:#333;border:0;background:#fff}\n.search-btn{color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}\n\n.covermy{position:absolute;z-index:99999;cursor:pointer;display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;z-index:9999;top:260rpx;right:0;color:#fff;background-color:rgba(17,17,17,0.3);width:140rpx;height:60rpx;font-size:26rpx;border-radius:30rpx 0px 0px 30rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839369894\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}