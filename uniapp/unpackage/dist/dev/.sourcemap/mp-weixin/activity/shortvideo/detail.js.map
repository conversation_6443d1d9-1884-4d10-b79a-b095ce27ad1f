{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?bd64", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?472a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?c20f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?8167", "uni-app:///activity/shortvideo/detail.nvue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?a99c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?afb8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?e55d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/shortvideo/detail.nvue?bc15"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nomore", "nodata", "platform", "pre_url", "gobacktopHeight", "only_show", "playst", "circular", "videoList", "id", "src", "img", "_videoDataIndex", "videoDataList", "prodialogshow", "sharetypevisible", "showposter", "posterpic", "showcomment", "commentlist", "pagenum", "message", "trimMessage", "faceshow", "hfid", "messageholder", "showproduct", "prolist", "isplay", "isplayst", "isalipay", "pageHeight", "bottomshare_status", "islooklog", "currentTime", "duration", "playJd", "nowtime", "jindu", "shareinfor", "binfo", "pageshow", "currentValue", "inactiveColor", "countdownGetType", "countTextShow", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "countTimeShow", "awardMsgShow", "awardMsgTxt", "awardMsgTxtShow", "hasMoreStatus", "hasMoreStatusH5", "count<PERSON><PERSON>in<PERSON><PERSON><PERSON>", "popupShow", "give_reward_list", "default_award_list", "tispTextReward", "videoGesture", "shortvideoStyle", "commentScrollTop", "onLoad", "that", "onReady", "onShareAppMessage", "title", "pic", "onShareTimeline", "imageUrl", "query", "onUnload", "clearInterval", "methods", "gobackhome", "uni", "url", "changeBid", "openPopupShow", "give_coupon_close", "convertSecondsToHMS", "reset", "app", "video_id", "init", "getdata", "cid", "interval", "desc", "setTimeout", "onSwiperChange", "isNext", "oldid", "<PERSON><PERSON><PERSON>", "getNextIndex", "getNextDataIndex", "onplay", "playClick", "playpause", "updateVideo", "console", "prodialogClose", "dozan", "proboxClickClose", "proboxClick", "playend", "getprolist", "got<PERSON>l", "getApp", "getmoreprolist", "commentClick", "getcommentlist", "commentScrolltoupper", "commentScroll", "getmorecomment", "replyClick", "replyshadowClick", "sendcomment", "content", "pzan", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "toggleFaceBox", "onInputFocus", "messageChange", "transformMsgHtml", "contentdata", "selectface", "goback", "getshareNum", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata", "sharelink", "globalData", "updateValueW", "timestamp", "timeupdate", "<PERSON><PERSON><PERSON>", "getshare", "pid", "openLocation", "latitude", "longitude", "name", "scale", "onprogress"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;AACA;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,EAAE;AAC/C;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8bz1B;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IAIA;MACA;IACA;EACA;EACAC;IACA;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACAF;MACAC;IACA;IACA;IACA;MACAD;MACAG;MACAC;IACA;EACA;EACAC;IACAC;IACA;MACA;IACA;EACA;;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACAC;QAAAC;MAAA,GACA;QACApB;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAqB;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACAtB;MACAA;MACAmB;QACAzE;QACA6E;MACA;QACAvB;QACAA;QACA;UACAA;UACAA;UACAA;QACA;QACA;UACAA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;QACA;UACAA;UACAwB;YACA;YACAxB;UACA;QACA;QACAA;QAEAA;UACAG;UACAsB;UACArB;QACA;QAEAsB;UACA1B;QACA;MACA;IACA;IACA2B;MAAA;MACA;MACA;QACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MAGA;MACA;QACAnB;QACAe;UACA;UACAxB;QACA;MACA;MACA;MACA;MACA;;MAEA;QACA;MACA;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;MAEA;MAEA;QACAY;QACAA;MACA;MAEA;MACAc;QACA;MACA;MAEA;MACA;MACAP;QACAzE;QACAmF;QACAC;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAtB;MACA;IACA;IACAuB;MACA;QACAvB;MACA;QACAA;MACA;MACA;IACA;IACAwB;MAAA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;MACA;MACAV;QACAd;QACA;MACA;MACAc;QACA;MACA;MACAW,qGACAT;IACA;IACAU;MACA;IACA;IACAC;MACA;MACApB;QACAzE;MACA;QACAsD;QACAA;MACA;IACA;IACA;IACAwC;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA7B;UACAC;QACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA6B;MACAL;MACA;MACArC;MACAmB;QACAzE;MACA;QACA;QACA;UACAsD;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;YACAA;YACA0B;cACA1B;YACA;UACA;UACA;YACAA;YACAA;YACA0B;cACA1B;cACAA;cACAA;cACAA;YACA;UACA;YACAA;YACAA;UACA;QAEA;QACAqC;MACA;IACA;IACAM;MACA;MACA;QACA;QACA;MACA;MACA3C;MACAA;MACAmB;QACAzE;QACAW;MACA;QACA;QACA;UACA2C;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACA4C;MACAC;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;MACAhD;MACAA;MACAmB;QACAzE;QACAW;MACA;QACA;QACA;UACA2C;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAiD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAnC;MACAA;QACAzE;QACAe;QACA8F;MACA;QACApC;QACA;UACAA;QACA;UACAA;UACAnB;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IAEA;IACAwD;MACA;MACA;MACA;MACA;MACArC;QACAzE;MACA;QACA;UACA;UACA;QACA;UACA;QACA;QACAU;QACAA;QACA4C;MACA;IACA;IACAyD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA3D;MACAA;MACAmB;MACAA;QACAzE;MACA;QACAyE;QACA;UACAA;QACA;UACAnB;UACAA;QACA;MACA;IACA;IACA4D;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACAT,yFACAU;MACA;MACA;QACAV;MACA;MACA;IACA;IACAW;MACA;MACA;IACA;IACAC;MACA;MACA;QACA1D;QACAT;MACA;;MACA6C;IACA;IACAuB;MACA;MACAjD;QAAAzE;MAAA;QACA;UACAsD;QACA;MACA;IACA;IACAqE;MACAlD;MACA;IACA;IACAmD;MACA;MACA;MACA1D;QACA2D;QACAC;UACA;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA,wEACA,yEACAvD;YACAuD;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC,kDACAC;oBACA;oBACA;sBACAD,2DACA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACA9D;UACA;QACA;MACA;IACA;IACA;IACAiE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAKA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA/E;QACAA;MACA;MACA;QACAA;QACAA;MACA;MACA;MACA;MACA;MACAA;MACA;MACA;MACAA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACAA;MACA;QACA;QACAA;MACA;IACA;IACAgF;MACA;MACA;QACA;MACA;MACA;MACA;MACA7D;QAAAzE;QAAA6B;MAAA;IACA;IACA0G;MACA;MACA9D;QACAzE;QACAwI;MACA;QACA;UACAlF;UACAA;QACA;MACA;IACA;IACAmF;MACA;MACA;MACA;MACA;MACAvE;QACAwE;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;UACA;QACA;QACAxF;QACA;UACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClsCA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/shortvideo/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/shortvideo/detail.nvue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.nvue?vue&type=template&id=7df7a4e2&\"\nvar renderjs\nimport script from \"./detail.nvue?vue&type=script&lang=js&\"\nexport * from \"./detail.nvue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.nvue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./detail.nvue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/shortvideo/detail.nvue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.nvue?vue&type=template&id=7df7a4e2&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxface: function () {\n      return import(\n        /* webpackChunkName: \"components/wxface/wxface\" */ \"@/components/wxface/wxface.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload && _vm.showcomment\n      ? _vm.__map(_vm.commentlist, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = item.replylist.length\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  var g1 =\n    _vm.isload &&\n    _vm.showproduct &&\n    _vm.prolist &&\n    _vm.prolist[0] &&\n    _vm.prolist[0][\"type\"] == \"gift_bag\"\n      ? _vm.prolist.length\n      : null\n  var m0 = _vm.isload && _vm.countMmainStatus ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.countMmainStatus ? _vm.t(\"color1\") : null\n  var l1 =\n    _vm.isload && _vm.shortvideoStyle\n      ? _vm.__map(_vm.commentlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g2 = item.nickname.replace(/^.{2}/, \"***\")\n          return {\n            $orig: $orig,\n            g2: g2,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        m0: m0,\n        m1: m1,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.nvue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.nvue?vue&type=script&lang=js&\"", "<style>\n  view,\n  label,\n  swiper-item,\n  scroll-view {\n    display:flex;\n    flex-direction:column;\n    flex-shrink: 0;\n    flex-grow: 0;\n    flex-basis: auto;\n    align-items: stretch;\n    align-content: flex-start;\n  }\n  view,\n  image,\n  input,\n  scroll-view,\n  swiper,\n  swiper-item,\n  text,\n  textarea,\n  video {\n    position: relative;\n    border: 0px solid #000000;\n    box-sizing: border-box;\n  }\n  swiper-item {\n    position: absolute;\n  }\n  button {\n    margin: 0;\n  }\n</style>\n<template>\r\n\t<view class=\"page\" v-if=\"isload\" :style=\"{height:pageHeight+'px'}\">\r\n\t\t<swiper class=\"swiper\" :style=\"{height:pageHeight+'px'}\" :circular=\"circular\" :vertical=\"true\" @change=\"onSwiperChange\">\r\n\t\t\t<swiper-item v-for=\"item in videoList\" :key=\"item.id\">\r\n\t\t\t\t<video class=\"video\" :style=\"{height:pageHeight+'px'}\" :id=\"item.id\" :ref=\"item.id\" :src=\"item.src\" :controls=\"false\" :loop=\"true\"\r\n\t\t\t\t\t:show-center-play-btn=\"false\" @tap=\"playpause\" @play=\"onplay\" @ended=\"playend\" @timeupdate=\"timeupdate\" @error=\"changeBid\" :enable-progress-gesture='videoGesture'></video>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<view v-if=\"!isalipay\" class=\"goback\" @tap=\"goback\" :style=\"{top:gobacktopHeight+'px'}\">\r\n\t\t\t<image class=\"goback-img\" :src=\"pre_url+'/static/img/goback.png'\" />\r\n\t\t</view>\r\n\t\t<view v-if=\"!isplayst\" class=\"playbox\" @tap.stop=\"playClick\">\r\n\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_playnum.png'\" class=\"playbox-img\" />\r\n\t\t</view>\r\n\t\t<block v-if=\"!shortvideoStyle\">\r\n\t\t\t<view class=\"video-right-pos flex\">\r\n\t\t\t\t<view class=\"logo\" @tap=\"gotourl\"\r\n\t\t\t\t\t:data-url=\"videoDataList[_videoDataIndex].bid==0?'/pages/index/index':'/pages/business/index?id='+videoDataList[_videoDataIndex].bid\">\r\n\t\t\t\t\t<image :src=\"videoDataList[_videoDataIndex].logo\" class=\"logo-img\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-right-pos-options\" >\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_view.png'\" class=\"viewnum-img\" /><text\tclass=\"viewnum-txt\">{{videoDataList[_videoDataIndex].view_num}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-right-pos-options\" @tap=\"dozan\">\r\n\t\t\t\t\t<image :src=\"videoDataList[_videoDataIndex].iszan ? pre_url+'/static/img/shortvideo_like2.png':pre_url+'/static/img/shortvideo_like.png'\"\r\n\t\t\t\t\t\tclass=\"likenum-img\" /><text class=\"likenum-txt\">{{videoDataList[_videoDataIndex].zan_num}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-right-pos-options\" @tap=\"commentClick\" v-if=\"videoDataList[_videoDataIndex].comment==1\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_comment.png'\" class=\"commentnum-img\" /><text\r\n\t\t\t\t\t\tclass=\"commentnum-txt\">{{videoDataList[_videoDataIndex].commentnum}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-right-pos-options\" @tap=\"shareClick\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_share.png'\" class=\"sharenum-img\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<block v-if=\"shortvideoStyle\">\r\n\t\t\t<view class=\"video-right-pos flex\">\r\n\t\t\t\t<view class=\"logo\" @tap=\"gotourl\"\r\n\t\t\t\t\t:data-url=\"videoDataList[_videoDataIndex].bid==0?'/pages/index/index':'/pages/business/index?id='+videoDataList[_videoDataIndex].bid\">\r\n\t\t\t\t\t<image :src=\"videoDataList[_videoDataIndex].logo\" class=\"logo-img\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-right-pos-options\" @tap=\"dozan\">\r\n\t\t\t\t\t<image :src=\"videoDataList[_videoDataIndex].iszan ? `${pre_url}/static/img/shortvideo/dianzanactive.png`:`${pre_url}/static/img/shortvideo/dianzan.png`\"\tclass=\"likenum-img\" />\r\n\t\t\t\t\t<text class=\"likenum-txt\">{{videoDataList[_videoDataIndex].zan_num}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-right-pos-options\" @tap=\"commentClick\" v-if=\"videoDataList[_videoDataIndex].comment==1\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo/pinglun.png'\" class=\"commentnum-img\" />\r\n\t\t\t\t\t<text class=\"commentnum-txt\">{{videoDataList[_videoDataIndex].commentnum}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-right-pos-options\" @tap=\"shareClick\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_share.png'\" class=\"sharenum-img\" />\r\n\t\t\t\t\t<text class=\"sharenum-txt\">{{videoDataList[_videoDataIndex].share_num}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<view class=\"linkurl\" @tap=\"gotourl\" :data-url=\"videoDataList[_videoDataIndex].linkurl\"\r\n\t\t\tv-if=\"videoDataList[_videoDataIndex].linkurl && !shortvideoStyle\">\r\n\t\t\t<text class=\"linkurl-txt\">{{videoDataList[_videoDataIndex].linkname}}</text>\r\n\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_arrowright.png'\" class=\"linkurl-img\" />\r\n\t\t</view>\r\n\t\t<!-- <view class=\"bottomshadow\" :style=\"menuindex > -1 ? 'bottom:110rpx':''\"></view> -->\r\n\t\t<view class=\"cart\" :style=\"menuindex > -1 || bottomshare_status == 2 ? 'bottom:150rpx':''\" v-if=\"videoDataList[_videoDataIndex].pronum > 0 && !shortvideoStyle\"\r\n\t\t\t@tap=\"proboxClick\">\r\n\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_cart.png'\" class=\"cart-img\" /><text\r\n\t\t\t\tclass=\"cart-txt\">{{videoDataList[_videoDataIndex].pronum}}</text>\r\n\t\t</view>\r\n\t\t<!-- 商品信息框 -->\r\n\t\t<view class=\"prodialog\" :style=\"menuindex > -1 || bottomshare_status == 2 ? 'bottom:230rpx':''\"\r\n\t\t\tv-if=\"!videoDataList[_videoDataIndex].linkurl && prodialogshow && videoDataList[_videoDataIndex].proid > 0\">\r\n      <view v-if=\"videoDataList[_videoDataIndex].protype == 'shop'\" @tap=\"gotourl\" :data-url=\"'/pages/shop/product?id='+videoDataList[_videoDataIndex].proid\">\r\n        <image :src=\"pre_url+'/static/img/shortvideo_probg.png'\" class=\"prodialog-bgimg\" />\r\n        <view class=\"prodialog-content\">\r\n          <image :src=\"videoDataList[_videoDataIndex].propic\" class=\"prodialog-content-img\" />\r\n          <view class=\"prodialog-content-info\">\r\n            <text class=\"prodialog-content-name\">{{videoDataList[_videoDataIndex].proname}}</text>\r\n            <text class=\"prodialog-content-sales\">{{videoDataList[_videoDataIndex].prosales}}人购买</text>\r\n            <text class=\"prodialog-content-price\">￥{{videoDataList[_videoDataIndex].prosell_price}}</text>\r\n          </view>\r\n        </view>\r\n        <image :src=\"pre_url+'/static/img/close.png'\" class=\"prodialog-close\" @tap.stop=\"prodialogClose\" />\r\n      </view>\r\n      <view v-if=\"videoDataList[_videoDataIndex].protype == 'gift_bag'\" @tap=\"gotourl\" :data-url=\"'/pagesA/giftbag/list?gbid=' + videoDataList[_videoDataIndex].proid+'&bid='+videoDataList[_videoDataIndex].probid\" >\r\n        <image :src=\"pre_url+'/static/img/shortvideo_probg.png'\" class=\"prodialog-bgimg\" />\r\n        <view class=\"prodialog-content\">\r\n          <image :src=\"videoDataList[_videoDataIndex].propic\" class=\"prodialog-content-img\" />\r\n          <view class=\"prodialog-content-info\">\r\n            <text class=\"prodialog-content-name\">{{videoDataList[_videoDataIndex].proname}}</text>\r\n            <text class=\"prodialog-content-sales\"></text>\r\n            <text class=\"prodialog-content-price\">￥{{videoDataList[_videoDataIndex].prosell_price}}</text>\r\n          </view>\r\n        </view>\r\n        <image :src=\"pre_url+'/static/img/close.png'\" class=\"prodialog-close\" @tap.stop=\"prodialogClose\" />\r\n      </view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"title\" :style=\"menuindex > -1 || bottomshare_status == 2 ? 'bottom:300rpx':''\" v-if='!shortvideoStyle'><text\r\n\t\t\t\tclass=\"title-txt\">{{videoDataList[_videoDataIndex].name}}</text></view>\r\n\t\t<view class=\"description\" :style=\"menuindex > -1  || bottomshare_status == 2? 'bottom:200rpx':''\"><text\r\n\t\t\t\tclass=\"description-txt\">{{videoDataList[_videoDataIndex].description}}</text></view>\r\n        \r\n    <view v-if=\"bottomshare_status == 2\" class=\"bottomshare\">\r\n      <view style=\"width: 720rpx;margin: 0 auto;display: flex;flex-direction: unset;\">\r\n        <view v-if=\"shareinfor\" class=\"nocss bottomshare_item \">\r\n          <view class=\"nocss\" style=\"justify-content: center;\">\r\n            <image :src=\"shareinfor.headimg\" class=\"bottomshare_headimg\" style=\"background-color:#ccc\"></image>\r\n          </view>\r\n          <view class=\"nocss bottomshare_title\">\r\n            {{shareinfor.nickname}}\r\n          </view>\r\n        </view>\r\n        <view class=\"nocss bottomshare_item\" @tap=\"shareClick\">\r\n          <view class=\"nocss\" style=\"justify-content: center;\">\r\n            <image :src=\"pre_url+'/static/img/shortvideo_share.png'\" class=\"bottomshare_headimg\" style=\"border-radius: 0;border: 0;\"></image>\r\n          </view>\r\n          <view class=\"nocss bottomshare_title\">\r\n           分享\r\n          </view>\r\n        </view>\r\n        <view  v-if=\"videoDataList[_videoDataIndex].pronum > 0\" @tap=\"proboxClick\" class=\"nocss bottomshare_item\">\r\n          <view class=\"nocss\" style=\"justify-content: center;\">\r\n            <image :src=\"pre_url+'/static/img/cart_64.png'\" class=\"bottomshare_headimg\" style=\"border-radius: 0;border: 0;\"></image>\r\n          </view>\r\n          <view class=\"nocss bottomshare_title\">\r\n           <text  v-if=\"videoDataList[_videoDataIndex].protype == 'shop'\">立即购买</text>\r\n           <text  v-if=\"videoDataList[_videoDataIndex].protype == 'gift_bag'\">立即领取</text>\r\n          </view>\r\n          \r\n        </view>\r\n        <view v-if=\"binfo && binfo.bid>0\" @tap=\"openLocation\" :data-latitude=\"binfo.latitude\" :data-longitude=\"binfo.longitude\" :data-company=\"binfo.name\" :data-address=\"binfo.address\" class=\"nocss bottomshare_item\">\r\n          <view class=\"nocss\" style=\"justify-content: center;\">\r\n            <image :src=\"pre_url+'/static/img/daohang.png'\" class=\"bottomshare_headimg\" style=\"border-radius: 0;border: 0;\"></image>\r\n          </view>\r\n          <view class=\"nocss bottomshare_title\">\r\n           {{binfo.name}}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\r\n\t\t<view class=\"popupshare__content\" v-if=\"sharetypevisible\" :style=\"menuindex > -1 ? 'bottom:110rpx':''\">\r\n\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popupshare__close2\" @tap.stop=\"handleClickMask\" />\r\n\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t<!-- #ifdef APP -->\r\n\t\t\t\t<view class=\"sharetypecontent-f1\" @tap=\"shareapp\">\r\n\t\t\t\t\t<image class=\"sharetypecontent-f1-img\" :src=\"pre_url+'/static/img/sharefriends.png'\" />\r\n\t\t\t\t\t<text class=\"sharetypecontent-f1-t1\">分享给好友</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"sharetypecontent-f1\" @tap=\"sharemp\" v-if=\"platform == 'mp'\">\r\n\t\t\t\t\t<image class=\"sharetypecontent-f1-img\" :src=\"pre_url+'/static/img/sharefriends.png'\" />\r\n\t\t\t\t\t<text class=\"sharetypecontent-f1-t1\">分享给好友</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<button class=\"sharetypecontent-f1\" open-type=\"share\" v-if=\"platform != 'h5'\">\r\n\t\t\t\t\t<image class=\"sharetypecontent-f1-img\" :src=\"pre_url+'/static/img/sharefriends.png'\" />\r\n\t\t\t\t\t<text class=\"sharetypecontent-f1-t1\">分享给好友</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"sharetypecontent-f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t<image class=\"sharetypecontent-f2-img\" :src=\"pre_url+'/static/img/sharepic.png'\" />\r\n\t\t\t\t\t<text class=\"sharetypecontent-f2-t1\">生成分享图片</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"posterDialog-main\">\r\n\t\t\t\t<view class=\"posterDialog-close\" @tap=\"posterDialogClose\">\r\n\t\t\t\t\t<image class=\"posterDialog-img\" :src=\"pre_url+'/static/img/close.png'\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"posterDialog-content\">\r\n\t\t\t\t\t<image class=\"posterDialog-content-img\" :src=\"posterpic\" mode=\"aspectFit\" @tap=\"previewImage\"\r\n\t\t\t\t\t\t:data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!--评论-->\r\n\t\t<view class=\"plbox\" v-if=\"showcomment\" :style=\"menuindex > -1 ? 'bottom:110rpx':'bottom:0rpx'\">\r\n\t\t\t<view :class=\"[commentScrollTop > 1 ? 'plbox_titleTop':'','plbox_title']\"><text class=\"plbox_title-t1\">评论</text><text\r\n\t\t\t\t\tclass=\"plbox_title-t2\">({{videoDataList[_videoDataIndex].commentnum}})</text></view>\r\n\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"plbox-close\" @tap.stop=\"commentClick\" />\r\n\t\t\t<scroll-view class=\"plbox_content\" scroll-y=\"true\" @scrolltolower=\"getmorecomment\" @scroll='commentScroll' @scrolltoupper='commentScrolltoupper'>\r\n\t\t\t\t<block v-for=\"(item, idx) in commentlist\" :key=\"item.id\">\r\n\t\t\t\t\t<view class=\"plbox_content-item\">\r\n\t\t\t\t\t\t<view class=\"plbox_content-item-f1 flex0\">\r\n\t\t\t\t\t\t\t<image :src=\"item.headimg\" class=\"plbox_content-item-f1-img\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"plbox_content-item-f2 flex-col\">\r\n\t\t\t\t\t\t\t<text class=\"plbox_content-item-f2-t1\">{{item.nickname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"plbox_content-item-f2-t2 plbox_content-plcontent\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"con in item.content\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"con.type=='text'\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"plbox_content-plcontent-text\">{{con.content}}</text>\r\n\t\t\t\t\t\t\t\t\t<image v-if=\"con.type=='image'\" :src=\"con.content\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"plbox_content-plcontent-image\"></image>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.replylist.length>0\">\r\n\t\t\t\t\t\t\t\t<view class=\"plbox_content-relist\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(hfitem, index) in item.replylist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"plbox_content-relist-item2\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex-row flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"hfitem.headimg\" class=\"plbox_content-relist-headimg\" />\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"plbox_content-relist-nickname\">{{hfitem.nickname}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"plbox_content-relist-f2 plbox_content-plcontent\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"con in hfitem.content\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"con.type=='text'\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"plbox_content-plcontent-text\">{{con.content}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<image v-if=\"con.type=='image'\" :src=\"con.content\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"plbox_content-plcontent-image\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view class=\"plbox_content-item-f2-t3\">\r\n\t\t\t\t\t\t\t\t<text class=\"plbox_content-item-f2-t3-x1\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"plbox_content-item-f2-t3-x2\"><text class=\"plbox_content-item-f2-phuifu\"\r\n\t\t\t\t\t\t\t\t\t\t@tap=\"replyClick\" :data-id=\"item.id\" :data-nickname=\"item.nickname\">回复</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"plbox_content-item-f2-pzan\" @tap=\"pzan\" :data-id=\"item.id\"\r\n\t\t\t\t\t\t\t\t\t:data-index=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/zan-' + (item.iszan==1?'2':'1') + '.png'\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"plbox_content-item-f2-pzan-img\" /><text\r\n\t\t\t\t\t\t\t\t\t\tclass=\"plbox_content-item-f2-pzan-txt\">{{item.zan}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view v-if=\"nomore\"><text class=\"comment-nomore\">没有更多数据了</text></view>\r\n\t\t\t\t<view v-if=\"nodata\" class=\"flex flex-x-center flex-y-center\" style=\"width: 100%;height: 100%;\"><text class=\"comment-nodata\">暂无评论!</text></view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<loading v-if=\"loading\"></loading>\r\n\t\t\t<view style=\"height:160rpx\"></view>\r\n\t\t\t<view class=\"plbox-replyshadow\" v-if=\"hfid!=0\" @tap.stop=\"replyshadowClick\"></view>\r\n\t\t\t<view class=\"pinglun\">\r\n\t\t\t\t<image @tap=\"toggleFaceBox\" class=\"pinglun-faceicon\" :src=\"pre_url+'/static/img/face-icon.png'\"></image>\r\n\t\t\t\t<input @confirm=\"sendMessage\" @focus=\"onInputFocus\" @input=\"messageChange\" class=\"pinglun-input\"\r\n\t\t\t\t\tconfirmHold=\"true\" confirmType=\"send\" cursorSpacing=\"20\" type=\"text\" :value=\"message\"\r\n\t\t\t\t\t:placeholder=\"messageholder\" />\r\n\t\t\t\t<view class=\"pinglun-buybtn\" @tap=\"sendcomment\"><text class=\"pinglun-buybtn-txt\">发表</text></view>\r\n\t\t\t</view>\r\n\t\t\t<wxface v-if=\"faceshow\" @selectface=\"selectface\"></wxface>\r\n\t\t</view>\r\n\r\n\t\t<!--商品-->\r\n\t\t<view class=\"probox\" v-if=\"showproduct\" :style=\"menuindex > -1 ? 'bottom:110rpx':'bottom:0rpx'\">\r\n      <block v-if=\"prolist && prolist[0] && prolist[0]['type'] == 'gift_bag'\">\r\n        <view class=\"probox_title\">\r\n          <text class=\"probox_title-t1\">礼包详情</text>\r\n          <text class=\"plbox_title-t2\">({{prolist.length}})</text>\r\n        </view>\r\n      </block>\r\n      <block v-else>\r\n        <view class=\"probox_title\">\r\n          <text class=\"probox_title-t1\">全部商品</text>\r\n          <text class=\"plbox_title-t2\">({{videoDataList[_videoDataIndex].pronum}})</text>\r\n        </view>\r\n      </block>\r\n\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"plbox-close\" @tap.stop=\"proboxClickClose\" />\r\n\t\t\t<scroll-view class=\"probox_content\" scroll-y=\"true\">\r\n\t\t\t\t<block v-for=\"(item, idx) in prolist\" :key=\"item.id\">\r\n\t\t\t\t\t<view v-if=\"item.type == 'shop'\" class=\"probox_content-item\" @tap=\"gotourl\" :data-url=\"'/pages/shop/product?id='+item.id\">\r\n\t\t\t\t\t\t<image :src=\"item.pic\" class=\"probox_content-item-img\" />\r\n\t\t\t\t\t\t<view class=\"probox_content-item-info\">\r\n\t\t\t\t\t\t\t<text class=\"probox_content-item-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"probox_content-item-sales\">{{item.sales}}人购买</text>\r\n\t\t\t\t\t\t\t<text class=\"probox_content-item-price\">￥{{item.sell_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"probox_content-item-btn\"><text class=\"probox_content-item-btn-txt\">立即抢购</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n          <view v-if=\"item.type == 'gift_bag'\" class=\"probox_content-item\" @tap.stop=\"gotourl\" :data-url=\"'/pagesA/giftbag/detail?id=' + item.id+'&gbid=' + item.gbid+'&bid='+item.bid\">\r\n          \t<image :src=\"item.pic\" class=\"probox_content-item-img\" />\r\n          \t<view class=\"probox_content-item-info\" style=\"overflow: hidden;display: block;\">\r\n          \t\t<text class=\"probox_content-item-name\">{{idx+1}}、 {{item.name}}</text>\r\n          \t\t<view class=\"probox_content-item-price\" style=\"flex-direction: unset;display:block;line-height: 60rpx;height: 60rpx;overflow: hidden;\">\r\n                ￥{{item.sell_price}}\r\n                <view class=\"gbdesc\">详情</view>\r\n              </view>\r\n              <text class=\"gbshortdesc\" v-if=\"item.shortdesc\" >{{item.shortdesc}}</text>\r\n          \t</view>\r\n          </view>\r\n\t\t\t\t</block>\r\n        <block v-if=\"prolist && prolist[0] && prolist[0]['type'] == 'gift_bag'\">\r\n          <view style=\"width:100%;height:110rpx;box-sizing:content-box\" class=\"notabbarbot\"></view>\r\n          <view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n          \t<view style=\"text-align: center;line-height: 100%;width: 50%;\">\r\n              <text v-if=\"prolist[0]['pirce']>0\">价格：￥{{prolist[0]['pirce']}}</text>\r\n              <text v-else>价格：免费</text>\r\n            </view>\r\n          \t<view class=\"tobuy\" :style=\"{margin:'0 auto'}\" @tap.stop=\"goto\" :data-url=\"'/pagesA/giftbag/buy?gbid=' + prolist[0]['gbid'] + '&num=1&bid='+prolist[0]['bid']\" ><text>立即领取</text></view>\r\n          </view>\r\n        </block>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t<!-- 倒计时 -->\r\n\t\t<view class=\"count-main\" v-if=\"countMmainStatus\" @click=\"openPopupShow\">\r\n\t\t\t<view class=\"postions-view\">\r\n\t\t\t\t<view class=\"class1\" :style=\"{ background: `conic-gradient(#f06e34 ${currentValue}%, ${inactiveColor} ${currentValue}% 100%)` }\"></view>\r\n\t\t\t\t<view class=\"circle\">\r\n\t\t\t\t\t<block v-if=\"countTimeShow\">\r\n\t\t\t\t\t\t<view v-if=\"countTextShow\" class=\"time-num-view\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/gift.png'\" style=\"width: 76rpx; height: 76rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text-tips\" v-else>\r\n\t\t\t\t\t\t\t<view>奖励</view>\r\n\t\t\t\t\t\t\t<view>已获得</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n<!-- \t\t\t\t<view class=\"reward-text\" :style=\"{width:  awardMsgShow ? '260%':'100%'}\">\r\n\t\t\t\t\t<view class=\"text-view\" v-if=\"awardMsgTxtShow\">{{awardMsgTxt}}</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"circle1\" :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t<view class=\"circle2\" :style=\"{ transform: `translateX(-50%) rotate(${currentValue * 3.6}deg)`}\">\r\n\t\t\t\t\t<view class=\"circle2-r\" :style=\"{background:t('color1') }\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 获得奖励 -->\r\n\t\t<block v-if=\"popupShow\">\r\n\t\t\t<view class=\"give-coupon flex-x-center flex-y-center\">\r\n\t\t\t\t<view class='coupon-block'>\r\n\t\t\t\t\t<image :src=\"pre_url+(only_show==1?'/static/img/coupon-top2.png':'/static/img/coupon-top.png')\" style=\"width:630rpx;height:330rpx;\"></image>\r\n\t\t\t\t\t<view @tap=\"give_coupon_close\" :data-url=\"give_coupon_close_url\" class=\"coupon-del flex-x-center flex-y-center\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/coupon-del.png'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex flex-x-center flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"coupon-info\">\r\n\t\t\t\t\t\t\t<view class=\"flex-x-center coupon-get\" style=\"text-align: center;\">\r\n\t\t\t\t\t\t\t\t{{tispTextReward}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view style=\"background:#f5f5f5;padding:10rpx 0; max-height: 600rpx; overflow-y: scroll;\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in give_reward_list\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t\t<view class=\"coupon-coupon\">\r\n\t\t\t\t\t\t\t\t\t<view :class=\"item.type==1?'pt_img1':'pt_img2'\"></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"pt_left\" :class=\"item.type==1?'':'bg2'\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.value}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"pt_right\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.sec_title\">{{item.sec_title}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"only_show==0\" @tap=\"goto\" data-url=\"/pages/my/usercenter\" style=\"text-align: center;\" class=\"flex-x-center coupon-btn\">前往查看</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<!-- 信息 -->\r\n\t\t<view class=\"count-main-info flex flex-y-center flex-row\" :style=\"{top:gobacktopHeight+38+'px'}\" v-if=\"shortvideoStyle\"\r\n\t\t@tap=\"gotourl\" :data-url=\"videoDataList[_videoDataIndex].bid==0?'/pages/index/index':'/pages/business/index?id='+videoDataList[_videoDataIndex].bid\">\r\n\t\t\t<view class=\"head-portrait\">\r\n\t\t\t\t<image :src=\"videoDataList[_videoDataIndex].logo\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-view flex flex-col\">\r\n\t\t\t\t<view class=\"info-view-title\">{{binfo.name}}</view>\r\n\t\t\t\t<view class=\"info-view-zan\">{{videoDataList[_videoDataIndex].zan_num}}人点赞</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 评论轮播 -->\r\n\t\t<view class=\"comment-rotation-view\" v-if=\"shortvideoStyle\">\r\n\t\t\t<swiper :autoplay=\"true\" :interval=\"5000\" :vertical=\"true\" :display-multiple-items='2' circular style=\"height: 120rpx;\">\r\n\t\t\t\t<swiper-item v-for=\"(item,index) in commentlist\" class=\"comment-swiper-item\" @tap=\"commentClick\">\r\n\t\t\t\t\t<view class=\"comment-options flex flex-y-center flex-row\">\r\n\t\t\t\t\t\t<view class=\"nick-text\">{{item.nickname.replace(/^.{2}/, '***')}}：</view>\r\n\t\t\t\t\t\t<view class=\"comment-text\">\r\n\t\t\t\t\t\t\t<block v-for=\"con in item.content\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"con.type=='text'\"\tclass=\"comment-text-view\">{{con.content}}</view>\r\n\t\t\t\t\t\t\t\t<image v-if=\"con.type=='image'\" :src=\"con.content\" class=\"plcontent-image\"></image>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<!-- 底部按钮，立即抢购-邀请好友 -->\r\n\t\t<view class=\"page-but-view flex\" v-if=\"shortvideoStyle\">\r\n\t\t\t<view class=\"but-options-top flex\">\r\n\t\t\t\t<view class=\"flex but-top\" @click=\"gobackhome\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo/shouye.png'\"></image>\r\n\t\t\t\t\t首页\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex but-top\" @tap=\"goto\" :data-url=\"'tel::'+videoDataList[_videoDataIndex].tel\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo/dianhua.png'\"></image>\r\n\t\t\t\t\t客服\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"but-options-view flex flex-y-center invitation-class\" @tap=\"shareClick\" :style=\"{width:videoDataList[_videoDataIndex].pronum > 0 ? '':'470rpx'}\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo/fenxianglianjie.png'\"></image>\r\n\t\t\t\t邀请好友\r\n\t\t\t</view>\r\n\t\t\t<view class=\"but-options-view flex flex-y-center flash-sale-class\" @tap=\"proboxClick\" v-if=\"videoDataList[_videoDataIndex].pronum > 0\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/shortvideo/gouwuche.png'\"></image>\r\n\t\t\t\t立即抢购\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n  var interval = null;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tnomore: false,\r\n\t\t\t\tnodata: false,\r\n\t\t\t\tplatform: app.globalData.platform,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tgobacktopHeight: 40,\r\n\t\t\t\tonly_show: 0,\r\n\t\t\t\tplayst: true,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tvideoList: [{\r\n\t\t\t\t\tid: \"video0\",\r\n\t\t\t\t\tsrc: \"\",\r\n\t\t\t\t\timg: \"\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: \"video1\",\r\n\t\t\t\t\tsrc: \"\",\r\n\t\t\t\t\timg: \"\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: \"video2\",\r\n\t\t\t\t\tsrc: \"\",\r\n\t\t\t\t\timg: \"\"\r\n\t\t\t\t}],\r\n\t\t\t\t_videoDataIndex: 0,\r\n\t\t\t\tvideoDataList: [],\r\n\t\t\t\tprodialogshow: true,\r\n\t\t\t\tsharetypevisible: false,\r\n\t\t\t\tshowposter: false,\r\n\t\t\t\tposterpic: \"\",\r\n\t\t\t\tshowcomment: false,\r\n\t\t\t\tcommentlist: [],\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\tmessage: \"\",\r\n\t\t\t\ttrimMessage: \"\",\r\n\t\t\t\tfaceshow: false,\r\n\t\t\t\thfid: 0,\r\n\t\t\t\tmessageholder: '发表评论',\r\n\t\t\t\tshowproduct: false,\r\n\t\t\t\tprolist: [],\r\n\t\t\t\tisplay: false,\r\n\t\t\t\tisplayst: true,\r\n\t\t\t\tisalipay:false,\r\n\t\t\t\tpageHeight:\"\",\r\n        bottomshare_status:0,//底部分享信息\r\n        islooklog:false,//是否记录\r\n        currentTime: 0,  //当前进度\r\n        duration: '', // 总进度\r\n        playJd:0,\r\n        nowtime:'',\r\n        jindu:'',//当前视频进度\r\n        shareinfor:'',\r\n        binfo:'',\r\n\t\t\t\tpageshow:true,\r\n\t\t\t\tcurrentValue: 0,\r\n\t\t\t\tinactiveColor:'#ececec',\r\n\t\t\t\tcountdownGetType:false,\r\n\t\t\t\tcountTextShow:true,\r\n\t\t\t\tdjsmin:'00',\r\n\t\t\t\tdjssec:'00',\r\n\t\t\t\tcountTimeShow:false,\r\n\t\t\t\tawardMsgShow:false,\r\n\t\t\t\tawardMsgTxt:'',\r\n\t\t\t\tawardMsgTxtShow:false,\r\n\t\t\t\thasMoreStatus:false,\r\n\t\t\t\thasMoreStatusH5:true,\r\n\t\t\t\tcountMmainStatus:false,\r\n\t\t\t\tpopupShow:false,\r\n\t\t\t\tgive_reward_list:[],\r\n\t\t\t\tdefault_award_list:[],\r\n\t\t\t\ttispTextReward:'观看完整视频获得以下奖励',\r\n\t\t\t\tvideoGesture:true,// 视频快进手势\r\n\t\t\t\tshortvideoStyle:false,\r\n\t\t\t\tcommentScrollTop:0,\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n      var that = this;\r\n\t\t\tvar opt = app.getopts(opt);\r\n      that.opt = opt;\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.init();\r\n\t\t\tthis.getdata();\r\n\t\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\t\t\tthis.pageHeight = sysinfo.windowHeight;\r\n\t\t\tif (sysinfo && sysinfo.statusBarHeight) {\r\n\t\t\t\tthis.gobacktopHeight = sysinfo.statusBarHeight;\r\n\t\t\t}\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.gobacktopHeight = 20;\r\n\t\t\t// #endif\r\n\t\t\tif(uni.getSystemInfoSync().uniPlatform=='mp-alipay'||uni.getSystemInfoSync().uniPlatform=='mp-baidu'){\r\n\t\t\t\tthis.isalipay = true;\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\tthis.getshareNum();\r\n\t\t\treturn this._sharewx({\r\n\t\t\t\ttitle: this.videoDataList[this._videoDataIndex].name,\r\n\t\t\t\tpic: this.videoDataList[this._videoDataIndex].coverimg\r\n\t\t\t});\r\n\t\t},\r\n\t\tonShareTimeline: function() {\r\n\t\t\tthis.getshareNum();\r\n\t\t\tvar sharewxdata = this._sharewx({\r\n\t\t\t\ttitle: this.videoDataList[this._videoDataIndex].name,\r\n\t\t\t\tpic: this.videoDataList[this._videoDataIndex].coverimg\r\n\t\t\t});\r\n\t\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\t\treturn {\r\n\t\t\t\ttitle: sharewxdata.title,\r\n\t\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\t\tquery: query\r\n\t\t\t}\r\n\t\t},\r\n    onUnload: function () {\r\n      clearInterval(interval);\r\n      if(this.islooklog){\r\n        this.upjindu();//更新进度\r\n      }\r\n    },\r\n\t\tmethods: {\r\n\t\t\tgobackhome(){\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl:\"/pages/index/index\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchangeBid(){\r\n\t\t\t\tthis.$set(this.videoDataList[this._videoDataIndex], 'bid', 1);\r\n\t\t\t},\r\n\t\t\topenPopupShow(){\r\n\t\t\t\tthis.give_reward_list = this.default_award_list;\r\n\t\t\t\tthis.only_show = 1;\r\n\t\t\t\tthis.tispTextReward = \"观看完整视频获得以下奖励\";\r\n\t\t\t\tthis.popupShow = true;\r\n\t\t\t},\r\n\t\t\tgive_coupon_close:function(){\r\n\t\t\t\tthis.popupShow = false;\r\n\t\t\t},\r\n\t\t\tconvertSecondsToHMS(seconds) {\r\n\t\t\t\tvar minutes = Math.floor((seconds % 3600) / 60);\r\n\t\t\t\tvar remainingSeconds = seconds % 60;\r\n\t\t\t\tthis.djsmin = (minutes < 10 ? '0':'') + minutes;\r\n\t\t\t\tthis.djssec = (remainingSeconds < 10 ? '0':'') + remainingSeconds;\r\n\t\t\t\tthis.countTimeShow = true;\r\n\t\t\t}, \r\n\t\t\treset() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tapp.post('ApiShortvideo/playEndAward', {video_id: this.videoDataList[this._videoDataIndex].id},\r\n\t\t\t\tfunction(res){\r\n\t\t\t\t\tthat.default_award_list = res.data;\r\n\t\t\t\t\tif(res.data.length > 0){\r\n\t\t\t\t\t\tthat.tispTextReward = '观看完整视频获得以下奖励';\r\n\t\t\t\t\t\tthat.countMmainStatus = true;\r\n\t\t\t\t\t\tthat.currentValue = 0;\r\n\t\t\t\t\t\tthat.countTextShow = true;\r\n\t\t\t\t\t\tthat.countTimeShow = false;\r\n\t\t\t\t\t\tthat.awardMsgShow = false;\r\n\t\t\t\t\t\tthat.awardMsgTxtShow = false;\r\n\t\t\t\t\t\tthat.hasMoreStatus = false;\r\n\t\t\t\t\t\tthat.hasMoreStatusH5 = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}, \r\n\t\t\tinit() {\r\n\t\t\t\tthis._videoIndex = 0;\r\n\t\t\t\tthis._videoContextList = [];\r\n\t\t\t\tfor (var i = 0; i < this.videoList.length; i++) {\r\n\t\t\t\t\tthis._videoContextList.push(uni.createVideoContext('video' + i, this));\r\n\t\t\t\t}\r\n\t\t\t\tthis._videoDataIndex = 0;\r\n\t\t\t},\r\n\t\t\tgetdata(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiShortvideo/detail', {\r\n\t\t\t\t\tid: that.opt.id,\r\n\t\t\t\t\tcid: that.opt.cid\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.videoDataList = res.videoList;\r\n\t\t\t\t\tif(res.shortvideo_style){\r\n\t\t\t\t\t\tthat.shortvideoStyle = true;\r\n\t\t\t\t\t\tthat.getcommentlist();\r\n\t\t\t\t\t\tthat.getprolist();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.video_gesture==1){\r\n\t\t\t\t\t\tthat.videoGesture = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.videoGesture = false;\r\n\t\t\t\t\t}\r\n          if(res.binfo){\r\n            that.binfo=res.binfo\r\n          }\r\n          if(res.bottomshare_status){\r\n            that.bottomshare_status = res.bottomshare_status;\r\n            if(that.opt && that.opt.pid ){\r\n              that.getshare();\r\n            }\r\n          }\r\n          if(res.islooklog){\r\n            that.islooklog = res.islooklog;\r\n            interval = setInterval(function () {\r\n              //更新进度\r\n            \tthat.upjindu();\r\n            }, 7000);\r\n          }\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\tthat.loaded({\r\n\t\t\t\t\t\ttitle: that.videoDataList[that._videoDataIndex].name,\r\n\t\t\t\t\t\tdesc: that.videoDataList[that._videoDataIndex].description,\r\n\t\t\t\t\t\tpic: that.videoDataList[that._videoDataIndex].coverimg\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthat.updateVideo(true);\r\n\t\t\t\t\t}, 200)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonSwiperChange(e) {\r\n\t\t\t\tlet currentIndex = e.detail.current;\r\n\t\t\t\tif (currentIndex === this._videoIndex) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet isNext = false;\r\n\t\t\t\tif (currentIndex === 0 && this._videoIndex === this.videoList.length - 1) {\r\n\t\t\t\t\tisNext = true;\r\n\t\t\t\t} else if (currentIndex === this.videoList.length - 1 && this._videoIndex === 0) {\r\n\t\t\t\t\tisNext = false;\r\n\t\t\t\t} else if (currentIndex > this._videoIndex) {\r\n\t\t\t\t\tisNext = true;\r\n\t\t\t\t}\r\n\r\n        \r\n        var that = this;\r\n        if(that.islooklog){\r\n          clearInterval(interval);\r\n          interval = setInterval(function () {\r\n            //更新进度\r\n          \tthat.upjindu();\r\n          }, 7000);\r\n        }\r\n        var oldid    = this.videoDataList[this._videoDataIndex].id;//之前的视频id\r\n        var oldjindu = this.jindu;//之前视频进度\r\n        this.jindu   = 0;//重置\r\n        \r\n\t\t\t\tif (isNext) {\r\n\t\t\t\t\tthis._videoDataIndex++;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis._videoDataIndex--;\r\n\t\t\t\t}\r\n      \r\n\t\t\t\tif (this._videoDataIndex < 0) {\r\n\t\t\t\t\tthis._videoDataIndex = this.videoDataList.length - 1;\r\n\t\t\t\t} else if (this._videoDataIndex >= this.videoDataList.length) {\r\n\t\t\t\t\tthis._videoDataIndex = 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.circular = (this._videoDataIndex != 0);\r\n\r\n\t\t\t\tif (this._videoIndex >= 0) {\r\n\t\t\t\t\tuni.createVideoContext(`video` + this._videoIndex).pause();\r\n\t\t\t\t\tuni.createVideoContext(`video` + this._videoIndex).seek(0);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis._videoIndex = currentIndex;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.updateVideo(isNext);\r\n\t\t\t\t}, 200);\r\n        \r\n\t\t\t\tthis.showcomment = false;\r\n\t\t\t\tthis.prodialogshow = true;\r\n\t\t\t\tapp.post('ApiShortvideo/updateviewnum', {\r\n\t\t\t\t\tid: this.videoDataList[this._videoDataIndex].id,\r\n          oldid:oldid,\r\n          oldjindu:oldjindu\r\n\t\t\t\t}, function() {});\r\n\t\t\t},\r\n\t\t\tgetNextIndex(isNext) {\r\n\t\t\t\tlet index = this._videoIndex + (isNext ? 1 : -1);\r\n\t\t\t\tif (index < 0) {\r\n\t\t\t\t\treturn this.videoList.length - 1;\r\n\t\t\t\t} else if (index >= this.videoList.length) {\r\n\t\t\t\t\treturn 0;\r\n\t\t\t\t}\r\n\t\t\t\treturn index;\r\n\t\t\t},\r\n\t\t\tgetNextDataIndex(isNext) {\r\n\t\t\t\tlet index = this._videoDataIndex + (isNext ? 1 : -1);\r\n\t\t\t\tif (index < 0) {\r\n\t\t\t\t\treturn this.videoDataList.length - 1;\r\n\t\t\t\t} else if (index >= this.videoDataList.length) {\r\n\t\t\t\t\treturn 0;\r\n\t\t\t\t}\r\n\t\t\t\treturn index;\r\n\t\t\t},\r\n\t\t\tonplay: function() {\r\n\t\t\t\tthis.isplay = true;\r\n\t\t\t\tthis.isplayst = true;\r\n\t\t\t},\r\n\t\t\tplayClick: function() {\r\n\t\t\t\tuni.createVideoContext(`video` + this._videoIndex).play();\r\n\t\t\t\tthis.isplayst = true;\r\n\t\t\t},\r\n\t\t\tplaypause: function() {\r\n\t\t\t\tif (this.playst) {\r\n\t\t\t\t\tuni.createVideoContext(`video` + this._videoIndex).pause();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.createVideoContext(`video` + this._videoIndex).play();\r\n\t\t\t\t}\r\n\t\t\t\tthis.playst = !this.playst;\r\n\t\t\t},\r\n\t\t\tupdateVideo(isNext) {\r\n\t\t\t\tif(this.videoDataList.length == 1){\r\n\t\t\t\t\tthis.videoList.length = 1;\r\n\t\t\t\t\tthis.$set(this.videoList[0], 'src', this.videoDataList[0].src);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$set(this.videoList[this._videoIndex], 'src', this.videoDataList[this._videoDataIndex].src);\r\n\t\t\t\t\tthis.$set(this.videoList[this.getNextIndex(isNext)], 'src', this.videoDataList[this.getNextDataIndex(isNext)].src);\r\n\t\t\t\t}\r\n\t\t\t\tthis.isplay = false;\r\n\t\t\t\tthis.isplayst = true;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.createVideoContext(`video` + this._videoIndex).play();\r\n\t\t\t\t\tthis.countdownGetType = true;\r\n\t\t\t\t}, 200);\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (this.isplay == false) this.isplayst = false;\r\n\t\t\t\t}, 400)\r\n\t\t\t\tconsole.log(\"v:\" + this._videoIndex + \" d:\" + this._videoDataIndex + \"; next v:\" + this.getNextIndex(\r\n\t\t\t\t\tisNext) + \" next d:\" + this.getNextDataIndex(isNext));\r\n\t\t\t},\r\n\t\t\tprodialogClose: function() {\r\n\t\t\t\tthis.prodialogshow = false\r\n\t\t\t},\r\n\t\t\tdozan: function() {\r\n\t\t\t\tvar that = this\r\n\t\t\t\tapp.post('ApiShortvideo/zan', {\r\n\t\t\t\t\tid: this.videoDataList[this._videoDataIndex].id\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.videoDataList[that._videoDataIndex].iszan = res.iszan;\r\n\t\t\t\t\tthat.videoDataList[that._videoDataIndex].zan_num = res.zan_num;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 全部商品列表关闭\r\n\t\t\tproboxClickClose(){\r\n\t\t\t\tthis.showproduct = !this.showproduct;\r\n\t\t\t\tif (this.showproduct) {\r\n\t\t\t\t\tthis.getprolist();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tproboxClick: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t// 单个商品打开商品详情 多个打开商品列表\r\n\t\t\t\tif(that.prolist.length == 1){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl:'/pages/shop/product?id='+ that.prolist[0].id\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.showproduct = !this.showproduct;\r\n\t\t\t\t\tif (this.showproduct) {\r\n\t\t\t\t\t\tthis.getprolist();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tplayend:function(e){\r\n        console.log('playend')\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.currentValue = 100;\r\n\t\t\t\tapp.post('ApiShortvideo/playEnd', {\r\n\t\t\t\t\tid: this.videoDataList[this._videoDataIndex].id\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\t\tif(!res.award_data.length) return;\r\n\t\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\t\tthat.countTextShow = false;\r\n\t\t\t\t\t\t\tthat.only_show = 0;\r\n\t\t\t\t\t\t\tthat.give_reward_list = res.award_data;\r\n\t\t\t\t\t\t\tthat.popupShow = true;\r\n\t\t\t\t\t\t\tthat.tispTextReward = '获得以下奖励';\r\n\t\t\t\t\t\t\tthat.coupon_top_url = that.coupon_top;\r\n\t\t\t\t\t\t\tif(res.award_msg){\r\n\t\t\t\t\t\t\t\tthat.awardMsgShow = true;\r\n\t\t\t\t\t\t\t\tthat.awardMsgTxt = res.award_msg;\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tthat.awardMsgTxtShow = true;\r\n\t\t\t\t\t\t\t\t},400)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif(res.has_more == 1){\r\n\t\t\t\t\t\t\t\tthat.hasMoreStatus = true;\r\n\t\t\t\t\t\t\t\tthat.hasMoreStatusH5 = true;\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tthat.awardMsgShow = false;\r\n\t\t\t\t\t\t\t\t\tthat.awardMsgTxt = '';\r\n\t\t\t\t\t\t\t\t\tthat.awardMsgTxtShow = false;\r\n\t\t\t\t\t\t\t\t\tthat.countTextShow = true;\r\n\t\t\t\t\t\t\t\t},2000)\r\n\t\t\t\t\t\t\t}else {\r\n\t\t\t\t\t\t\t\tthat.hasMoreStatusH5 = false;\r\n\t\t\t\t\t\t\t\tthat.hasMoreStatus = false;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetprolist: function(loadmore) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (!loadmore) {\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.prolist = [];\r\n\t\t\t\t}\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tapp.post('ApiShortvideo/getprolist', {\r\n\t\t\t\t\tid: this.videoDataList[this._videoDataIndex].id,\r\n\t\t\t\t\tpagenum: this.pagenum\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (that.pagenum == 1) {\r\n\t\t\t\t\t\tthat.prolist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar prolist = that.prolist;\r\n\t\t\t\t\t\t\tvar newdata = prolist.concat(data);\r\n\t\t\t\t\t\t\tthat.prolist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgotourl: function(e) {\r\n\t\t\t\tgetApp().goto(e.currentTarget.dataset.url, e.currentTarget.dataset.opentype)\r\n\t\t\t},\r\n\t\t\tgetmoreprolist: function() {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tif (!this.nomore && !this.nodata) {\r\n\t\t\t\t\tthis.getprolist(true);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcommentClick: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthis.showcomment = !this.showcomment;\r\n\t\t\t\tif (this.showcomment) {\r\n\t\t\t\t\tthis.getcommentlist();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetcommentlist: function(loadmore) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (!loadmore) {\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.commentlist = [];\r\n\t\t\t\t}\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tapp.post('ApiShortvideo/getcommentlist', {\r\n\t\t\t\t\tid: this.videoDataList[this._videoDataIndex].id,\r\n\t\t\t\t\tpagenum: this.pagenum\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (that.pagenum == 1) {\r\n\t\t\t\t\t\tthat.commentlist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar commentlist = that.commentlist;\r\n\t\t\t\t\t\t\tvar newdata = commentlist.concat(data);\r\n\t\t\t\t\t\t\tthat.commentlist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcommentScrolltoupper(){\r\n\t\t\t\tthis.commentScrollTop = 0;\r\n\t\t\t},\r\n\t\t\tcommentScroll(e){\r\n\t\t\t\tthis.commentScrollTop = e.detail.scrollTop;\r\n\t\t\t},\r\n\t\t\tgetmorecomment: function() {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tif (!this.nomore && !this.nodata) {\r\n\t\t\t\t\tthis.getcommentlist(true);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\treplyClick: function(e) {\r\n\t\t\t\tvar id = e.currentTarget.dataset.id\r\n\t\t\t\tthis.message = '';\r\n\t\t\t\tthis.hfid = e.currentTarget.dataset.id;\r\n\t\t\t\tthis.messageholder = '回复 ' + e.currentTarget.dataset.nickname + '：';\r\n\t\t\t},\r\n\t\t\treplyshadowClick: function() {\r\n\t\t\t\tthis.hfid = 0;\r\n\t\t\t\tthis.message = '';\r\n\t\t\t\tthis.messageholder = '发表评论';\r\n\t\t\t},\r\n\t\t\tsendcomment: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar message = this.message;\r\n\t\t\t\tvar hfid = this.hfid;\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiShortvideo/subpinglun', {\r\n\t\t\t\t\tid: this.videoDataList[this._videoDataIndex].id,\r\n\t\t\t\t\thfid: hfid,\r\n\t\t\t\t\tcontent: message\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\tthat.videoDataList[that._videoDataIndex].commentnum = res.commentnum;\r\n\t\t\t\t\t\tthat.getcommentlist();\r\n\t\t\t\t\t\tthat.message = '';\r\n\t\t\t\t\t\tthat.hfid = 0;\r\n\t\t\t\t\t\tthat.messageholder = '发表评论';\r\n\t\t\t\t\t\tthat.faceshow = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\tpzan: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar commentlist = that.commentlist;\r\n\t\t\t\tapp.post(\"ApiShortvideo/pzan\", {\r\n\t\t\t\t\tid: id\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif (res.type == 0) {\r\n\t\t\t\t\t\t//取消点赞\r\n\t\t\t\t\t\tvar iszan = 0;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar iszan = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcommentlist[index].iszan = iszan;\r\n\t\t\t\t\tcommentlist[index].zan = res.zancount;\r\n\t\t\t\t\tthat.commentlist = commentlist;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshareClick: function() {\r\n\t\t\t\tthis.sharetypevisible = !this.sharetypevisible;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.sharetypevisible = false\r\n\t\t\t},\r\n\t\t\tshowPoster: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.showposter = true;\r\n\t\t\t\tthat.sharetypevisible = false;\r\n\t\t\t\tapp.showLoading('生成海报中');\r\n\t\t\t\tapp.post('ApiShortvideo/getposter', {\r\n\t\t\t\t\tid: this.videoDataList[this._videoDataIndex].id\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t\t\tthat.videoDataList[that._videoDataIndex].share_num = that.videoDataList[that._videoDataIndex].share_num+1;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tposterDialogClose: function() {\r\n\t\t\t\tthis.showposter = false;\r\n\t\t\t},\r\n\t\t\ttoggleFaceBox: function() {\r\n\t\t\t\tthis.faceshow = !this.faceshow\r\n\t\t\t},\r\n\t\t\tonInputFocus: function(e) {\r\n\t\t\t\tthis.faceshow = false\r\n\t\t\t},\r\n\t\t\tmessageChange: function(e) {\r\n\t\t\t\tthis.message = e.detail.value;\r\n\t\t\t\tthis.trimMessage = e.detail.value.trim();\r\n\t\t\t},\r\n\t\t\ttransformMsgHtml: function(msgtype, content) {\r\n\t\t\t\tif (msgtype == 'miniprogrampage') {\r\n\t\t\t\t\tvar contentdata = JSON.parse(content);\r\n\t\t\t\t\tcontent = '<div style=\"font-size:16px;font-weight:bold;height:25px;line-height:25px\">' +\r\n\t\t\t\t\t\tcontentdata.Title + '</div><img src=\"' + contentdata.ThumbUrl + '\" width=\"200\"/>';\r\n\t\t\t\t}\r\n\t\t\t\tif (msgtype == 'image') {\r\n\t\t\t\t\tcontent = '<img src=\"' + content + '\" width=\"200\"/>';\r\n\t\t\t\t}\r\n\t\t\t\treturn content;\r\n\t\t\t},\r\n\t\t\tselectface: function(face) {\r\n\t\t\t\tthis.message = \"\" + this.message + face;\r\n\t\t\t\tthis.trimMessage = this.message.trim();\r\n\t\t\t},\r\n\t\t\tgoback: function() {\r\n        var that = this;\r\n        if(that.islooklog){\r\n          clearInterval(interval);\r\n          that.upjindu();//更新进度\r\n        }\r\n\t\t\t\tgetApp().goback();\r\n\t\t\t},\r\n\t\t\tgetshareNum(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tapp.post('ApiShortvideo/addsharenum',{id:that.videoDataList[that._videoDataIndex].id},function(e){\r\n\t\t\t\t\tif(e.status == 1){\r\n\t\t\t\t\t\tthat.videoDataList[that._videoDataIndex].share_num = that.videoDataList[that._videoDataIndex].share_num+1;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsharemp: function() {\r\n\t\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\t\tthis.sharetypevisible = false\r\n\t\t\t},\r\n\t\t\tshareapp: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar videoinfo = that.videoDataList[that._videoDataIndex]\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthis.getshareNum();\r\n\t\t\t\t\t\tif (res.tapIndex >= 0) {\r\n\t\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\t\tsharedata.title = videoinfo.name;\r\n\t\t\t\t\t\t\tsharedata.summary = videoinfo.description;\r\n\t\t\t\t\t\t\tsharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid +\r\n\t\t\t\t\t\t\t\t'.html#/activity/shortvideo/detail?scene=id_' + videoinfo.id + '-pid_' +\r\n\t\t\t\t\t\t\t\tapp.globalData.mid;\r\n\t\t\t\t\t\t\tsharedata.imageUrl = videoinfo.coverimg;\r\n\t\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\t\tif (sharelist) {\r\n\t\t\t\t\t\t\t\tfor (var i = 0; i < sharelist.length; i++) {\r\n\t\t\t\t\t\t\t\t\tif (sharelist[i]['indexurl'] == '/activity/shortvideo/detail') {\r\n\t\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\t\tif (sharelist[i].url) {\r\n\t\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\t\tif (sharelink.indexOf('/') === 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url + '/h5/' + app\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.globalData.aid + '.html#' + sharelink;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (app.globalData.mid > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tsharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'pid=' + app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 倒计时组件圆环进度\r\n\t\t\tasync updateValueW(timestamp){\r\n\t\t\t\tif (timestamp >= 99 && this.hasMoreStatusH5) {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\tthis.playend();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\treturn;\r\n\t\t\t\t};\r\n\t\t\t\tthis.currentValue = timestamp;\r\n\t\t\t},\r\n      timeupdate:function(e){\r\n      \t//跳转到指定播放位置 initial-time 时间为秒\r\n      \tlet that = this;\r\n      \t//播放的总时长\r\n      \tvar duration = e.detail.duration;\r\n        if(isNaN(duration)){\r\n          return\r\n        }\r\n\t\t\t\t// that.videoDataList[that._videoDataIndex].award_show && \r\n\t\t\t\t// 视频播放 倒计时重置\r\n\t\t\t\tif(that.countdownGetType){\r\n\t\t\t\t\tthat.reset();\r\n\t\t\t\t\tthat.countdownGetType = false;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.currentValue < 100 || that.hasMoreStatus){\r\n\t\t\t\t\tthat.updateValueW((e.detail.currentTime / e.detail.duration)*100);\r\n\t\t\t\t\tthat.convertSecondsToHMS(Math.round(e.detail.duration - e.detail.currentTime));\r\n\t\t\t\t}\r\n      \t//实时播放进度 秒数\r\n      \tvar currentTime = e.detail.currentTime;\r\n      \t//当前视频进度\r\n      \tthat.currentTime  = currentTime; //实时播放进度\r\n      \tvar min = Math.floor(currentTime/60)\r\n      \tvar second = currentTime%60\r\n      \tthat.nowtime = (min>=10?min:'0'+min)+':'+(second>=10?second:'0'+second)\r\n      \t  //计算进度\r\n      \tif(that.playJd < 100){\r\n      \t\tthat.playJd = (that.currentTime/(duration-1)).toFixed(2)*100;\r\n      \t\tif(that.playJd>100) that.playJd=100\r\n      \t}\r\n        var jindu    = that.playJd.toFixed(1);\r\n        var oldjindu = that.jindu;\r\n        that.jindu   = jindu;\r\n        if(that.islooklog && jindu == 100 && jindu >oldjindu){\r\n          //更新进度\r\n          that.upjindu();\r\n        }\r\n      },\r\n      upjindu:function(){\r\n        var that = this;\r\n        if(!that.islooklog){\r\n          return\r\n        }\r\n        var id    = that.videoDataList[that._videoDataIndex].id;//之前的视频id\r\n        var jindu = that.jindu;//之前视频进度\r\n        app.post('ApiShortvideo/addlook', {id:id,jindu:jindu}, function(res) {});\r\n      },\r\n      getshare:function(){\r\n      \tvar that = this;\r\n      \tapp.post('ApiShortvideo/getshareinfor', {\r\n          id: that.opt.id,\r\n      \t\tpid: that.opt.pid,\r\n      \t}, function(res) {\r\n            if(res.status == 1){\r\n              that.bottomshare_status = 2;\r\n              that.shareinfor = res.data.shareinfor\r\n            }\r\n      \t});\r\n      },\r\n      openLocation:function(e){\r\n      \t//console.log(e)\r\n      \tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\r\n      \tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\r\n      \tvar address = e.currentTarget.dataset.address\r\n      \tuni.openLocation({\r\n      \t latitude:latitude,\r\n      \t longitude:longitude,\r\n      \t name:address,\r\n      \t scale: 13\r\n       })\t\t\r\n      },\r\n      onprogress:function(e){\r\n        var that = this;\r\n        if(e && e.detail && e.detail.buffered){\r\n          var jindu    = e.detail.buffered;\r\n          var oldjindu = that.jindu;\r\n          if(isNaN(jindu)){\r\n            return\r\n          }\r\n          that.jindu = jindu;\r\n          if(that.islooklog && jindu == 100 && jindu >oldjindu){\r\n            //更新进度\r\n            that.upjindu();\r\n          }\r\n        }\r\n      }\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.give-coupon .coupon-coupon{display: flex; flex-direction: row;}\r\n\t.give-coupon .coupon-coupon .pt_right{padding: 20rpx;}\r\n\t.give-coupon .coupon-coupon .pt_right .f1 .t2{height: unset;line-height: unset;}\r\n\t.give-coupon .coupon-coupon .pt_right .f1 .t4{ max-width: 310rpx; overflow: hidden;  text-overflow: ellipsis; white-space: nowrap;}\r\n\t/* 页面底部功能按钮 */\r\n\t.page-but-view{width: 100%;height: 145rpx;position: fixed;bottom: 0;align-items: flex-start;justify-content: space-between;flex-direction: row;padding: 10rpx 10rpx 0rpx 10rpx;z-index: 5;}\r\n\t.page-but-view .but-options-top{width: 225rpx;height: 80rpx;flex-direction: row;align-items: center;justify-content: center;}\r\n\t.page-but-view .but-options-top .but-top{align-items: center;justify-content: center;font-size: 20rpx;color: #dcdad2;padding: 0rpx 28rpx;}\r\n\t.page-but-view .but-options-top .but-top image{width: 40rpx;height: 40rpx;margin-bottom: 5rpx;}\r\n\t\r\n\t.page-but-view .but-options-view{width: 240rpx;height: 80rpx;border-radius: 10rpx;box-sizing: border-box;font-size: 28rpx;color: #fff;\r\n\tflex-direction: row;align-items: center;justify-content: center;}\r\n\t.page-but-view .but-options-view image{width: 35rpx;height: 35rpx;margin-right: 7rpx;}\r\n\t.page-but-view .invitation-class{background-image: linear-gradient(to right,#feb123,#f9b321);}\r\n\t.page-but-view .flash-sale-class{background-image: linear-gradient(to right,#f6568e,#fd2d56);}\r\n\t/* 评论 轮播 */\r\n\t.comment-rotation-view {position: absolute;z-index: 4;bottom: 160rpx;left: 18rpx;width: 380rpx;height: 120rpx !important;}\r\n\t.comment-rotation-view .comment-swiper-item{}\r\n\t.comment-rotation-view .comment-options{width: 100%;background: rgba(0, 0, 0, 0.3);border-radius: 30rpx;align-items: center;padding: 5rpx;}\r\n\t.comment-rotation-view .comment-options .nick-text{color: #d9bd51;font-size: 24rpx;padding-left: 9rpx;}\r\n\t.comment-rotation-view .comment-options .comment-text{flex-direction: row;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}\r\n\t.comment-options .comment-text .plcontent-image{width: 20rpx;height: 20rpx;}\r\n\t.comment-options .comment-text .comment-text-view{color: #f1f1f1;font-size: 24rpx;width: 236rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;display: block;}\r\n\t\r\n\t/* 信息 */\r\n\t.count-main-info{position: fixed;left: 18rpx;top:18rpx;z-index: 9999;width: 230rpx;height: 80rpx;border-radius:40rpx;box-sizing: border-box;background: rgba(0, 0, 0, 0.5);}\r\n\t.count-main-info .head-portrait{height: 72rpx;width: 72rpx;border-radius: 50%;overflow: hidden;margin-left: 5rpx;}\r\n\t.count-main-info .head-portrait image{width: 100%;height: 100%;}\r\n\t.count-main-info .info-view{padding-left: 8rpx;width: 132rpx;}\r\n\t.count-main-info .info-view .info-view-title{font-size: 22rpx;color: #eeeeee;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 132rpx;display: block;}\r\n\t.count-main-info .info-view .info-view-zan{font-size: 18rpx;color: #c1c1c1;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 120rpx;display: block;margin-top: 5rpx;}\r\n\t/* 倒计时 */\r\n\t.count-main{\r\n\t\tposition: fixed;\r\n\t\tleft: 18rpx;\r\n\t\ttop:260rpx;\r\n\t\tz-index: 9999;\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\toptions:0.8;\r\n\t}\r\n\t.count-main .postions-view{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 50%;\r\n\t\toptions:0.8;\r\n\t}\r\n\t.count-main .class1{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-size: contain;\r\n\t\tbackground-origin: border-box;\r\n\t\tbackground-clip: content-box;\r\n\t\toptions:0.8;\r\n\t\tz-index: 2;\r\n\t}\r\n\t.count-main .circle {\r\n\t\tposition: absolute;\r\n\t\twidth: 90%;\r\n\t\theight: 90%;\r\n\t\tleft: 50%;\r\n\t\ttop: 50%;\r\n\t\tbackground: #5e5e5e;\r\n\t\ttransform: translateX(-50%) translateY(-50%);\r\n\t\tborder-radius: 50%;\r\n\t\tz-index: 99;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\t.count-main .reward-text{\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50rpx; \r\n\t\tbackground: #5e5e5e;\r\n\t\ttransition: width .6s;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.count-main .reward-text .text-view{\r\n\t\tpadding-right: 10rpx;\r\n\t\tmargin-left: 90rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\twhite-space:nowrap;\r\n\t\twidth: 130rpx;\r\n\t\tword-break: break-all;\r\n\t\twhite-space: normal;\r\n\t}\r\n\t.count-main .circle .time-num-view {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 100%;\r\n\t\tflex-direction: row;\r\n\t\t\r\n\t}\r\n\t.count-main .circle .text-tips{\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.count-main .circle1 {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\twidth: 10%;\r\n\t\theight: 10%; \r\n\t\tborder-radius: 50%;\r\n\t\toptions:0.8;\r\n\t}\r\n\t.count-main .circle2 {\r\n\t\theight: 100%;\r\n\t\twidth: 12rpx;\r\n\t\tleft: 50%;\r\n\t\tposition: absolute;\r\n\t}\r\n\t.circle2-r {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tborder-radius: 50%;\r\n\t\toptions:0.8;\r\n\t}\r\n\t/* #ifndef APP-PLUS */\r\n\tpage {\r\n\t\twidth: 750rpx;\r\n\t\theight: 100vh;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.page {\r\n\t\tposition: absolute;\r\n\t\twidth: 750rpx;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\t/* width: 750rpx; */\r\n\t}\r\n\r\n\t.swiper {\r\n\t\tposition: absolute;\r\n\t\twidth: 750rpx;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.video {\r\n\t\tposition: absolute;\r\n\t\twidth: 750rpx;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.flex-row {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row\r\n\t}\r\n\r\n\t.goback {\r\n\t\tposition: absolute;\r\n\t\tz-index: 4;\r\n\t\ttop: 40px;\r\n\t\tleft: 15px;\r\n\t\twidth: 30px;\r\n\t\theight: 30px;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.goback-img {\r\n\t\twidth: 30px;\r\n\t\theight: 30px\r\n\t}\r\n\r\n\t.playbox {\r\n\t\tposition: absolute;\r\n\t\twidth: 750rpx;\r\n\t\theight: 100%;\r\n\t\tflex: 1;\r\n\t\tbackground: rgba(0, 0, 0, 0.5);\r\n\t\tz-index: 3\r\n\t}\r\n\r\n\t.playbox-img {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tmargin-left: -30rpx;\r\n\t\tmargin-top: -30rpx\r\n\t}\r\n\t.video-right-pos{position: absolute;right: 26rpx;top: 50%;z-index: 4;transform: translateY(-50%);}\r\n\t.video-right-pos .video-right-pos-options{display: flex;flex-direction: column;align-items: center;margin-bottom: 20rpx;}\r\n\t.logo {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 50%;\r\n\t\toverflow: hidden;\r\n\t\tborder: 4rpx solid #FFFFFF;\r\n\t\tbackground: linear-gradient(180deg, #FF2775 0%, #FF2F4B 100%);\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t.logo-img {\r\n\t\twidth: 86rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\t.viewnum-img {\r\n\t\twidth: 72rpx;\r\n\t\theight: 72rpx\r\n\t}\r\n\r\n\t.viewnum-txt {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);\r\n\t}\r\n\t.likenum-img {\r\n\t\twidth: 72rpx;\r\n\t\theight: 72rpx\r\n\t}\r\n\r\n\t.likenum-txt {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);\r\n\t}\r\n\t.commentnum-img {\r\n\t\twidth: 72rpx;\r\n\t\theight: 72rpx\r\n\t}\r\n\t.commentnum-txt {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);\r\n\t}\r\n\t.sharenum-img {\r\n\t\twidth: 72rpx;\r\n\t\theight: 72rpx\r\n\t}\r\n\r\n\t.sharenum-txt {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);\r\n\t}\r\n\r\n\t.linkurl {\r\n\t\tposition: absolute;\r\n\t\tz-index: 4;\r\n\t\tbottom: 160rpx;\r\n\t\tright: 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tbackground: rgba(100, 100, 100, 0.5);\r\n\t\tpadding: 10rpx 0 10rpx 20rpx;\r\n\t\tborder-radius: 30rpx 0 0 30rpx\r\n\t}\r\n\r\n\t.linkurl-img {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx\r\n\t}\r\n\r\n\t.linkurl-txt {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.bottomshadow {\r\n\t\tposition: absolute;\r\n\t\tz-index: 4;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t\tbackground: linear-gradient(0deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\r\n\t.cart {\r\n\t\tposition: absolute;\r\n\t\tz-index: 5;\r\n\t\tbottom: 40rpx;\r\n\t\tright: 50rpx;\r\n\t\twidth: 88rpx;\r\n\t\theight: 88rpx;\r\n\t\tdisplay: relative\r\n\t}\r\n\r\n\t.cart-img {\r\n\t\twidth: 88rpx;\r\n\t\theight: 88rpx\r\n\t}\r\n\r\n\t.cart-txt {\r\n\t\tposition: absolute;\r\n\t\tz-index: 5;\r\n\t\tbottom: 6rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 88rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.title {\r\n\t\tposition: absolute;\r\n\t\tz-index: 3;\r\n\t\tbottom: 160rpx;\r\n\t\tleft: 30rpx;\r\n\t\twidth: 500rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.title-txt {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #fff;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.description {\r\n\t\tposition: absolute;\r\n\t\tz-index: 3;\r\n\t\tbottom: 60rpx;\r\n\t\tleft: 30rpx;\r\n\t\twidth: 500rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.description-txt {\r\n\t\tfont-size: 28rpx;\r\n\t\theight: 88rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #fff;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.prodialog {\r\n\t\twidth: 480rpx;\r\n\t\theight: 190rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: 120rpx;\r\n\t\tright: 10rpx;\r\n\t\tz-index: 5;\r\n\t\tdisplay: flex\r\n\t}\r\n\r\n\t.prodialog-bgimg {\r\n\t\twidth: 480rpx;\r\n\t\theight: 190rpx;\r\n\t}\r\n\r\n\t.prodialog-content {\r\n\t\twidth: 470rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex: 1;\r\n\t\tpadding: 20rpx;\r\n\t\tposition: relative;\r\n\t\tposition: absolute;\r\n\t\tbottom: 24rpx;\r\n\t\tright: 10rpx\r\n\t}\r\n\r\n\t.prodialog-content-img {\r\n\t\twidth: 128rpx;\r\n\t\theight: 128rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tflex-shrink: 0\r\n\t}\r\n\r\n\t.prodialog-content-info {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding-left: 20rpx\r\n\t}\r\n\r\n\t.prodialog-content-name {\r\n\t\tcolor: #212121;\r\n\t\tfont-size: 28rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.prodialog-content-sales {\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 24rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx\r\n\t}\r\n\r\n\t.prodialog-content-price {\r\n\t\tcolor: #FD4A46;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\theight: 50rpx;\r\n\t\tline-height: 50rpx\r\n\t}\r\n\r\n\t.probox {\r\n\t\twidth: 750rpx;\r\n\t\theight: 700rpx;\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tz-index: 6;\r\n\t\tposition: absolute\r\n\t}\r\n\r\n\t.probox-replyshadow {\r\n\t\tposition: absolute;\r\n\t\tz-index: 7;\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\twidth: 750rpx;\r\n\t\tflex: 1;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.probox-close {\r\n\t\tposition: absolute;\r\n\t\ttop: 10rpx;\r\n\t\tright: 10rpx;\r\n\t\tz-index: 7;\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.probox_title {\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row\r\n\t}\r\n\r\n\t.probox_title-t1 {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.probox_title-t2 {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.probox_content {\r\n\t\theight: 580rpx;\r\n\t\toverflow: scroll;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.probox_content-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex: 1;\r\n\t\tpadding: 20rpx;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.probox_content-item-img {\r\n\t\twidth: 188rpx;\r\n\t\theight: 188rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tflex-shrink: 0\r\n\t}\r\n\r\n\t.probox_content-item-info {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding-left: 20rpx\r\n\t}\r\n\r\n\t.probox_content-item-name {\r\n\t\tcolor: #212121;\r\n\t\tfont-size: 28rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.probox_content-item-sales {\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 24rpx;\r\n\t\theight: 50rpx;\r\n\t\tline-height: 50rpx\r\n\t}\r\n\r\n\t.probox_content-item-price {\r\n\t\tcolor: #FD4A46;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\theight: 50rpx;\r\n\t\tline-height: 50rpx\r\n\t}\r\n\r\n\t.probox_content-item-btn {\r\n\t\tposition: absolute;\r\n\t\ttop: 140rpx;\r\n\t\tright: 20rpx;\r\n\t\tbackground: linear-gradient(-90deg, #FD4A46 0%, rgba(253, 74, 70, 0.76) 100%);\r\n\t\tborder-radius: 26px;\r\n\t\twidth: 150rpx;\r\n\t\theight: 54rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n\r\n\t.probox_content-item-btn-txt {\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.popupshare__content {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 999;\r\n\t\twidth: 750rpx;\r\n\t\theight: 280rpx;\r\n\t\tpadding: 20rpx 0;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.popupshare__close2 {\r\n\t\tposition: absolute;\r\n\t\ttop: 10rpx;\r\n\t\tright: 10rpx;\r\n\t\tz-index: 1000;\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.sharetypecontent {\r\n\t\tflex: 1;\r\n\t\twidth: 710rpx;\r\n\t\theight: 250rpx;\r\n\t\tmargin: 0 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tpadding: 50rpx;\r\n\t}\r\n\r\n\t.sharetypecontent-f1 {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbackground: #fff;\r\n\t\tfont-size: 24rpx;\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.sharetypecontent button::after {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.sharetypecontent-f1-img {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\tpadding: 0;\r\n\t\tmargin: 0\r\n\t}\r\n\r\n\t.sharetypecontent-f1-t1 {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #111111;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx\r\n\t}\r\n\r\n\t.sharetypecontent-f2 {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.sharetypecontent-f2-img {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t}\r\n\r\n\t.sharetypecontent-f2-t1 {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #111111;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx\r\n\t}\r\n\r\n\t.posterDialog {\r\n\t\tposition: absolute;\r\n\t\tz-index: 99;\r\n\t\twidth: 750rpx;\r\n\t\theight: 1200rpx;\r\n\t\ttop: 100rpx;\r\n\t\tleft: 0\r\n\t}\r\n\r\n\t.posterDialog-main {\r\n\t\twidth: 670rpx;\r\n\t\theight: 1100rpx;\r\n\t\tmargin: 60rpx 40rpx 30rpx 40rpx;\r\n\t\tbackground: #fff;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 20rpx;\r\n\t\tdisplay: flex\r\n\t}\r\n\r\n\t.posterDialog-close {\r\n\t\tposition: absolute;\r\n\t\tpadding: 20rpx;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 10;\r\n\t}\r\n\r\n\t.posterDialog-img {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\r\n\t.posterDialog-content {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tpadding: 70rpx 20rpx 30rpx 20rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 30rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.posterDialog-content-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.prodialog-close {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tpadding: 10rpx;\r\n\t\tposition: absolute;\r\n\t\ttop: 6rpx;\r\n\t\tright: 14rpx;\r\n\t\tz-index: 9\r\n\t}\r\n\r\n\r\n\t.pinglun {\r\n\t\twidth: 750rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 100rpx;\r\n\t\tbackground: #fff;\r\n\t\tz-index: 8;\r\n\t\tborder-top: 1px solid #f7f7f7\r\n\t}\r\n\r\n\t.pinglun-faceicon {\r\n\t\twidth: 46rpx;\r\n\t\theight: 46rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t\tmargin-right: 18rpx\r\n\t}\r\n\r\n\t.pinglun-input {\r\n\t\tflex: 1;\r\n\t\tcolor: #000;\r\n\t\tfont-size: 32rpx;\r\n\t\tpadding: 0;\r\n\t\tline-height: 100rpx\r\n\t}\r\n\r\n\t.pinglun-buybtn {\r\n\t\tmargin-left: 0.08rpx;\r\n\t\tbackground: #FD4A46;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-radius: 6rpx\r\n\t}\r\n\r\n\t.pinglun-buybtn-txt {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t}\r\n\r\n\r\n\t.plbox {\r\n\t\twidth: 750rpx;\r\n\t\theight: 700rpx;\r\n\t\tpadding: 20rpx 0rpx;\r\n\t\tbackground: #fff;\r\n\t\tz-index: 6;\r\n\t\tposition: absolute;\r\n\t}\r\n\r\n\t.plbox-replyshadow {\r\n\t\tposition: absolute;\r\n\t\tz-index: 7;\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\twidth: 750rpx;\r\n\t\tflex: 1;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.plbox-close {\r\n\t\tposition: absolute;\r\n\t\ttop: 10rpx;\r\n\t\tright: 10rpx;\r\n\t\tz-index: 7;\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.plbox_title {\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tpadding: 0rpx 20rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.plbox_titleTop{\r\n\t\tbox-shadow: 0rpx 8rpx 7rpx -2rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\t.plbox_title-t1 {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.plbox_title-t2 {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.plbox_content {\r\n\t\theight: 500rpx;\r\n\t\toverflow: scroll;\r\n\t\tdisplay: flex;\r\n\t\tpadding: 0rpx 20rpx;\r\n\t}\r\n\r\n\t.plbox_content-plcontent {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tvertical-align: middle;\r\n\t\tcolor: #111;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.plbox_content-plcontent-text {\r\n\t\tcolor: #111;\r\n\t\tfont-size: 28rpx\r\n\t}\r\n\r\n\t.plbox_content-plcontent-image {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tvertical-align: inherit;\r\n\t}\r\n\r\n\t.plbox_content-item {\r\n\t\tflex: 1;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.plbox_content-item-f1 {\r\n\t\twidth: 80rpx;\r\n\t}\r\n\r\n\t.plbox_content-item-f1-img {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50%\r\n\t}\r\n\r\n\t.plbox_content-item-f2 {\r\n\t\twidth: calc(100% - 88rpx);\r\n\t}\r\n\r\n\t.plbox_content-item-f2-t1 {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #838383;\r\n\t}\r\n\r\n\t.plbox_content-item-f2-t2 {\r\n\t\tcolor: #000;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin: 15rpx 0;\r\n\t\tline-height: 38rpx;\r\n\t\t\r\n\t}\r\n\r\n\t.plbox_content-item-f2-t3 {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex: 1;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding-bottom: 30rpx;\r\n\t\tborder-bottom: 1px #ebebeb solid;\r\n\t}\r\n\r\n\t.plbox_content-item-f2-t3-x1 {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.plbox_content-item-f2-t3-x2 {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.plbox_content-item-f2-pzan {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row\r\n\t}\r\n\r\n\t.plbox_content-item-f2-pzan-img {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tmargin-right: 10rpx\r\n\t}\r\n\r\n\t.plbox_content-item-f2-pzan-txt {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666\r\n\t}\r\n\r\n\t.plbox_content-item-f2-phuifu {\r\n\t\tmargin-left: 6px;\r\n\t\tcolor: #507DAF;\r\n\t\tfont-size: 26rpx\r\n\t}\r\n\r\n\t.plbox_content-relist {\r\n\t\tflex: 1;\r\n\t\tbackground: #f5f5f5;\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\tborder-radius: 5rpx;\r\n\t}\r\n\r\n\t.plbox_content-relist-item2 {\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-bottom: 10rpx\r\n\t}\r\n\r\n\t.plbox_content-relist-headimg {\r\n\t\twidth: 45rpx;\r\n\t\theight: 45rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.plbox_content-relist-nickname {\r\n\t\tcolor: #838383;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.plbox_content-relist-f2 {\r\n\t\tmargin-top: 10rpx\r\n\t}\r\n\r\n\t.comment-nodata {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 26rpx\r\n\t}\r\n\r\n\t.comment-nomore {\r\n\t\tflex: 1;\r\n\t\tcolor: #999;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\t@supports(bottom: env(safe-area-inset-bottom)) {\r\n\t\t.dp-tabbar-bot {\r\n\t\t\tpadding-bottom: 0 !important;\r\n\t\t}\r\n\r\n\t\t.dp-tabbar-bar {\r\n\t\t\tpadding-bottom: 0 !important;\r\n\t\t}\r\n\t}\r\n  .gbdesc{float: right;background: linear-gradient(-90deg, #FD4A46 0%, rgba(253, 74, 70, 0.76) 100%);overflow: hidden;border-radius:10rpx;padding: 0rpx 24rpx;flex-direction: unset;font-weight: 500;font-size: 28rpx;color: #fff;position: unset;}\r\n  .gbshortdesc{margin-top: 14rpx;color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;height:36rpx}\r\n  .bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;height:110rpx;align-items:center;box-sizing:content-box}\r\n  .bottombar .cart{width: 50%;font-size:26rpx;color:#707070}\r\n  .bottombar .cart .img{ width:50rpx;height:50rpx}\r\n  .bottombar .cart .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n  .bottombar .tobuy{ width:50%; height: 90rpx;border-radius:8rpx;color: #fff;background: linear-gradient(-90deg, #FD4A46 0%, rgba(253, 74, 70, 0.76) 100%); font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:45rpx;margin-right:16rpx;padding-right:20rpx}\r\n  \r\n  .bottomshare{width: 100%;height: 150rpx;position: fixed;bottom: 0;flex-direction: unset;color: #000;background-color: #fff;}\r\n  .bottomshare_item{width: 100%;line-height: 100rpx;margin-top: 15rpx;text-align: center;display: block;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;margin: 10rpx;margin-top: 15rpx;padding: 10rpx;border-radius: 8rpx;height: 120rpx;box-shadow: 6rpx 6rpx 16rpx #ccc;}\r\n  .bottomshare_headimg{width: 50rpx;height: 50rpx;overflow: hidden;border: 2rpx solid #ccc;border-radius: 50%;}\r\n  .bottomshare_title{font-size: 24rpx;line-height: 50rpx;text-align: center;display: block;overflow: hidden;}\r\n  .nocss{flex-shrink: unset;flex-direction: unset;} \r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.nvue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.nvue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464242\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.nvue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.nvue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464271\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}