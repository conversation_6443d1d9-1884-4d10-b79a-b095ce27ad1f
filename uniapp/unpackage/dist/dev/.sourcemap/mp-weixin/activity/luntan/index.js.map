{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/index.vue?e2ca", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/index.vue?ec9d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/index.vue?71d1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/index.vue?821a", "uni-app:///activity/luntan/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/index.vue?7ac9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/index.vue?df5d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "sysset", "datalist", "clist", "taglist", "navlist", "title", "pagenum", "keyword", "nomore", "nodata", "picindex", "need_call", "is_back", "scrollTop", "top", "showFilterPanel", "filterOptions", "filter", "sort_by", "time_range", "content_type", "distance", "category_ids", "latitude", "longitude", "hasActiveFilter", "locationEnabled", "defaultCategoryIcon", "activeDropdown", "currentLatitude", "currentLongitude", "onLoad", "onPullDownRefresh", "onReachBottom", "onPageScroll", "page", "uni", "onShow", "methods", "getdata", "that", "filterParams", "params", "app", "clearTimeout", "zan", "id", "savecontent", "fuzhi", "success", "setTimeout", "fail", "savpic", "savpic2", "url", "filePath", "savevideo", "callphone", "phoneNumber", "topPost", "luntanid", "cid", "toggleFilterDropdown", "closeDropdown", "selectFilter", "reset<PERSON>rea<PERSON><PERSON>er", "resetDistanceFilter", "resetCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "resetTimeFilter", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetSortFilter", "resetFilter", "applyFilter", "isCategorySelected", "toggleCate<PERSON>y", "getUserLocation", "type", "console", "selectSortOption", "selectTimeOption", "selectContentOption", "selectDistanceOption", "playvideo", "videoContext", "stopAllVideos", "res", "loaded", "previewImage", "current", "urls"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5GA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwSv1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;;MAEA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;QACAC;QACAtB;MACA;MACAuB;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;QACA3C;MACA;QACAA;UACAyC;UACAtB;QACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAyB;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;;MAEA;MACA;QAAAlC;MAAA;;MAEA;MACA;QACA;QACA;;QAEA;QACA;UACA;UACAmC;UACAA;QACA;UACA;UACA;UACA;UACA;YACA;UACA;QACA;QAEAC;MACA;MAEAC;QACAH;QACA;UACAA;QACA;QACA;QACA;UACAJ;YACA/B;UACA;UACAmC;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;YACAA;UACA;;UAEA;UACA;YACAA;;YAEA;YACA;cACAA;YACA;UACA;UAEA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACA;QACA;UACA;YACA;YACA;cACA9C;YACA;cACAA;gBACAyC;gBACAtB;cACA;YACA;YAEA;cACAuB;gBACAvB;cACA;cACA+B;cACAJ;cACAJ;YACA;cACAI;cACAA;YACA;UACA;QACA;MAGA;IACA;IACAK;MACA;MACA;MACA;MACA;MACAF;QAAAG;MAAA;QACA;UACA;UACA7C;UACAA;QACA;UACAA;UACAA;QACA;QAEAuC;MACA;IACA;IACAO;MACA;MACA;MACA;MACAP;QACAA;UACAA;QACA;MACA;IACA;IACAQ;MACA;QACA;QACA;MACA;MACA;MACAZ;QACA1C;QACAuD;UACAN;UACAO;YACA;UACA;QACA;QACAC;UACAR;UACAO;YACA;UACA;QACA;MACA;IACA;IACAE;MACA;QACA;QACA;MACA;MACA;QACAT;QAAA;MACA;MACA;MACA;MACA;IACA;IACAU;MACA;MACA;MACA;QACAV;QACAA;QACA;MACA;MACA;MACAA;MACAP;QACAkB;QACAL;UACA;YACAb;cACAmB;cACAN;gBACAT;gBACAA;cACA;cACAW;gBACAR;gBACAA;cACA;YACA;UACA;QACA;QACAQ;UACAR;UACAA;QACA;MACA;IACA;IACAa;MACA;MACAb;MACAP;QACAkB;QACAL;UACA;YACAb;cACAmB;cACAN;gBACAN;gBACAA;cACA;cACAQ;gBACAR;gBACAA;cACA;YACA;UACA;QACA;QACAQ;UACAR;UACAA;QACA;MACA;IACA;IACAc;MACA;MACArB;QACAsB;QACAP,uBACA;MACA;IACA;IACAQ;MACAhB;QAAAiB;MAAA;QACA;UACA;YACAjB;UACA;YACAA;cAAAkB;YAAA;cACA;gBACA;kBACA;kBACAzB;oBACAkB;kBACA;gBACA;kBACAX;gBACA;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACA;IACAmB;MACA;QACA;MACA;QACA;MACA;IACA;;IAEA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACArD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;IACA;IACAgD;MACA;MACA,uBACA,8BACA,oCACA,sCACA,kCACA,mCACA;;MAEA;MACA;MACA;;MAEA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAvC;QACAwC;QAAA;QACA3B;UACAT;UACAA;UACAqC;QACA;QACA1B;UACA0B;UACA;QACA;MACA;IACA;;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACAC;QACA;QACAA;MACA;IACA;IAEAC;MAAA;MACA;MACAhD;QACAU;MACA;QACA;UACAuC;YACA;cACA;cACA;gBACAF;gBACAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAG;MAAA;MACA;MACA;;MAEA;MACApC;QACA;QACA;MACA;IACA;IAEA;IACAqC;MACA;MACA;MAEA;QACAnD;UACAoD;UACAC;UACAtC;YACA0B;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACv2BA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/luntan/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/luntan/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4ae04161&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/luntan/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4ae04161&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.clist.length > 0 || _vm.navlist.length > 0 : null\n  var g1 = _vm.isload ? _vm.taglist && _vm.taglist.length > 0 : null\n  var g2 = _vm.isload ? _vm.filter.category_ids.length : null\n  var l0 =\n    _vm.isload && _vm.activeDropdown && _vm.activeDropdown === \"category\"\n      ? _vm.__map(_vm.clist, function (cate, __i4__) {\n          var $orig = _vm.__get_orig(cate)\n          var m0 = _vm.isCategorySelected(cate.id)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g3 =\n          item.show_form_content &&\n          item.form_content &&\n          item.form_content.length > 0\n        var l1 = g3\n          ? _vm.__map(item.form_content, function (field, fieldIndex) {\n              var $orig = _vm.__get_orig(field)\n              var g4 = field.images && field.images.length > 0\n              return {\n                $orig: $orig,\n                g4: g4,\n              }\n            })\n          : null\n        return {\n          $orig: $orig,\n          g3: g3,\n          l1: l1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<view class=\"container2\">\n\t\t\t<image :src=\"sysset.pic\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"sysset.pic\" @tap=\"goto\" :data-url=\"sysset.picurl\"></image>\n\t\t\t\n\t\t\t<!-- 所有分类和导航标签合并展示 -->\n\t\t\t<view class=\"navbox\" v-if=\"clist.length>0 || navlist.length>0\">\n\t\t\t\t<!-- 普通分类 -->\n\t\t\t\t<block v-for=\"item in clist\" :key=\"item.id\">\n\t\t\t\t\t<view style=\"cursor:pointer\" @tap=\"goto\" :data-url=\"'ltlist?cid=' + item.id\" class=\"nav_li\">\n\t\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t\t\t<view>{{item.name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<!-- 导航标签 -->\n\t\t\t\t<block v-for=\"item in navlist\" :key=\"item.id\">\n\t\t\t\t\t<view style=\"cursor:pointer\" @tap=\"goto\" :data-url=\"item.nav_url\" class=\"nav_li\">\n\t\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t\t\t<view>{{item.name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 标签种类 -->\n\t\t\t<view class=\"tag-category\" v-if=\"taglist && taglist.length>0\">\n\t\t\t\t<view class=\"tag-item\" \n\t\t\t\t\tv-for=\"item in taglist\" \n\t\t\t\t\t:key=\"item.id\"\n\t\t\t\t\t@tap=\"goto\" \n\t\t\t\t\t:data-url=\"'ltlist?cid=' + item.id\">\n\t\t\t\t\t<image :src=\"item.pic\" class=\"tag-icon\" v-if=\"item.pic\"></image>\n\t\t\t\t\t<text class=\"tag-name\">{{item.name}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view style=\"display:flex;width:100%;height:16rpx;background:#F7F7F7\"></view>\n\t\t<view class=\"container2\">\n\t\t\t<view class=\"listtitle\">最新动态</view>\n\t\t\t\n\t\t\t<!-- 筛选栏移到这里 -->\n\t\t\t<view class=\"top-filter-bar\">\n\t\t\t\t<!-- 距离筛选 -->\n\t\t\t\t<view class=\"filter-item\" @tap=\"toggleFilterDropdown('distance')\">\n\t\t\t\t\t<text :class=\"{'active-filter': filter.distance !== 'all'}\">距离</text>\n\t\t\t\t\t<view class=\"arrow\" :class=\"{'arrow-up': activeDropdown === 'distance'}\"></view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 分类筛选 -->\n\t\t\t\t<view class=\"filter-item\" @tap=\"toggleFilterDropdown('category')\">\n\t\t\t\t\t<text :class=\"{'active-filter': filter.category_ids.length > 0}\">分类</text>\n\t\t\t\t\t<view class=\"arrow\" :class=\"{'arrow-up': activeDropdown === 'category'}\"></view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 内容筛选 -->\n\t\t\t\t<view class=\"filter-item\" @tap=\"toggleFilterDropdown('content')\">\n\t\t\t\t\t<text :class=\"{'active-filter': filter.content_type !== 'all'}\">内容</text>\n\t\t\t\t\t<view class=\"arrow\" :class=\"{'arrow-up': activeDropdown === 'content'}\"></view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 时间筛选 -->\n\t\t\t\t<view class=\"filter-item\" @tap=\"toggleFilterDropdown('time')\">\n\t\t\t\t\t<text :class=\"{'active-filter': filter.time_range !== 'all'}\">时间</text>\n\t\t\t\t\t<view class=\"arrow\" :class=\"{'arrow-up': activeDropdown === 'time'}\"></view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 排序方式 -->\n\t\t\t\t<view class=\"filter-item\" @tap=\"toggleFilterDropdown('sort')\">\n\t\t\t\t\t<text :class=\"{'active-filter': filter.sort_by !== ''}\">排序</text>\n\t\t\t\t\t<view class=\"arrow\" :class=\"{'arrow-up': activeDropdown === 'sort'}\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 筛选下拉面板 -->\n\t\t\t<view class=\"filter-dropdown\" v-if=\"activeDropdown\">\n\t\t\t\t<!-- 距离筛选下拉内容 -->\n\t\t\t\t<view v-if=\"activeDropdown === 'distance'\" class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"options-list\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t:class=\"{'option-active': filter.distance === option.id}\"\n\t\t\t\t\t\t\tv-for=\"option in filterOptions.distance_ranges\" \n\t\t\t\t\t\t\t:key=\"option.id\"\n\t\t\t\t\t\t\t@tap=\"selectDistanceOption(option.id)\">\n\t\t\t\t\t\t\t{{option.name}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"dropdown-actions\">\n\t\t\t\t\t\t<view class=\"action-btn reset\" @tap=\"resetDistanceFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"action-btn confirm\" @tap=\"applyFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 分类筛选下拉内容 -->\n\t\t\t\t<view v-if=\"activeDropdown === 'category'\" class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"options-grid\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"grid-item\" \n\t\t\t\t\t\t\t:class=\"{'grid-active': isCategorySelected(cate.id)}\"\n\t\t\t\t\t\t\tv-for=\"cate in clist\" \n\t\t\t\t\t\t\t:key=\"cate.id\"\n\t\t\t\t\t\t\t@tap=\"toggleCategory(cate.id)\">\n\t\t\t\t\t\t\t{{cate.name}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"dropdown-actions\">\n\t\t\t\t\t\t<view class=\"action-btn reset\" @tap=\"resetCategoryFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"action-btn confirm\" @tap=\"applyFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 内容筛选下拉内容 -->\n\t\t\t\t<view v-if=\"activeDropdown === 'content'\" class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"options-list\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t:class=\"{'option-active': filter.content_type === option.id}\"\n\t\t\t\t\t\t\tv-for=\"option in filterOptions.content_types\" \n\t\t\t\t\t\t\t:key=\"option.id\"\n\t\t\t\t\t\t\t@tap=\"selectContentOption(option.id)\">\n\t\t\t\t\t\t\t{{option.name}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"dropdown-actions\">\n\t\t\t\t\t\t<view class=\"action-btn reset\" @tap=\"resetContentFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"action-btn confirm\" @tap=\"applyFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 时间筛选下拉内容 -->\n\t\t\t\t<view v-if=\"activeDropdown === 'time'\" class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"options-list\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t:class=\"{'option-active': filter.time_range === option.id}\"\n\t\t\t\t\t\t\tv-for=\"option in filterOptions.time_ranges\" \n\t\t\t\t\t\t\t:key=\"option.id\"\n\t\t\t\t\t\t\t@tap=\"selectTimeOption(option.id)\">\n\t\t\t\t\t\t\t{{option.name}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"dropdown-actions\">\n\t\t\t\t\t\t<view class=\"action-btn reset\" @tap=\"resetTimeFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"action-btn confirm\" @tap=\"applyFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 排序筛选下拉内容 -->\n\t\t\t\t<view v-if=\"activeDropdown === 'sort'\" class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"options-list\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t:class=\"{'option-active': filter.sort_by === option.id}\"\n\t\t\t\t\t\t\tv-for=\"option in filterOptions.sort_options\" \n\t\t\t\t\t\t\t:key=\"option.id\"\n\t\t\t\t\t\t\t@tap=\"selectSortOption(option.id)\">\n\t\t\t\t\t\t\t{{option.name}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"dropdown-actions\">\n\t\t\t\t\t\t<view class=\"action-btn reset\" @tap=\"resetSortFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"action-btn confirm\" @tap=\"applyFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 遮罩层 -->\n\t\t\t<view class=\"mask\" v-if=\"activeDropdown\" @tap=\"closeDropdown\"></view>\n\t\t\t\n\t\t\t<view class=\"datalist\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'detail?id=' + item.id\">\n\t\t\t\t<view class=\"top\">\n\t\t\t\t\t<image :src=\"item.headimg\" class=\"f1\"></image>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<view class=\"t1\">\n\t\t\t\t\t\t\t{{item.nickname}}\n\t\t\t\t\t\t\t<text v-if=\"item.is_top == 1\" class=\"top-tag\">{{sysset.top_text || '置顶'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"cate\" v-if=\"item.cname\">{{item.cname}}</view>\n\t\t\t\t\t\t<view class=\"t2\">{{item.showtime}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"con\">\n\t\t\t\t\t<!-- 显示表单内容 -->\n\t\t\t\t\t<view v-if=\"item.show_form_content && item.form_content && item.form_content.length > 0\" class=\"form-content\">\n\t\t\t\t\t\t<view class=\"form-item\" v-for=\"(field, fieldIndex) in item.form_content\" :key=\"fieldIndex\">\n\t\t\t\t\t\t\t<view class=\"field-label\">{{field.label}}</view>\n\t\t\t\t\t\t\t<!-- 文本类型字段 -->\n\t\t\t\t\t\t\t<view v-if=\"!field.images && !field.video\" class=\"field-value\">\n\t\t\t\t\t\t\t\t{{field.value}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 图片类型字段 -->\n\t\t\t\t\t\t\t<view v-if=\"field.images && field.images.length > 0\" class=\"field-images\">\n\t\t\t\t\t\t\t\t<image \n\t\t\t\t\t\t\t\t\tv-for=\"(img, imgIndex) in field.images\" \n\t\t\t\t\t\t\t\t\t:key=\"imgIndex\" \n\t\t\t\t\t\t\t\t\t:src=\"img\" \n\t\t\t\t\t\t\t\t\tclass=\"form-image\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t\**********=\"previewImage\" \n\t\t\t\t\t\t\t\t\t:data-urls=\"field.images\" \n\t\t\t\t\t\t\t\t\t:data-current=\"img\">\n\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 视频类型字段 -->\n\t\t\t\t\t\t\t<video \n\t\t\t\t\t\t\t\tv-if=\"field.video\"\n\t\t\t\t\t\t\t\tclass=\"form-video\" \n\t\t\t\t\t\t\t\t:id=\"'form-video-' + item.id + '-' + fieldIndex\"\n\t\t\t\t\t\t\t\t:src=\"field.video\" \n\t\t\t\t\t\t\t\**********=\"playvideo\"\n\t\t\t\t\t\t\t\t:poster=\"pre_url + '/static/img/video-poster.png'\"\n\t\t\t\t\t\t\t\t:show-play-btn=\"false\"\n\t\t\t\t\t\t\t\t:controls=\"false\"\n\t\t\t\t\t\t\t\t:loop=\"false\"\n\t\t\t\t\t\t\t\t:enable-progress-gesture=\"false\"\n\t\t\t\t\t\t\t\t:initial-time=\"0\"\n\t\t\t\t\t\t\t\tobject-fit=\"cover\"\n\t\t\t\t\t\t\t></video>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 如果内容被隐藏，显示提示信息 -->\n\t\t\t\t\t<view v-else-if=\"item.content_hidden\" class=\"content-hidden-tip\">\n\t\t\t\t\t\t<text class=\"tip-text\">🔒 该分类内容需要点击查看详情</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 正常显示内容 -->\n\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.luntan_content_ueditor\"><rich-text :nodes=\"item.content\" style=\"word-wrap: break-word;\"></rich-text></view>\n\t\t\t\t\t\t<view class=\"f1\" v-else><text style=\"white-space:pre-wrap;\">{{item.content}}</text></view>\n\t\t\t\t\t\t<view class=\"f2\" v-if=\"item.pics\">\n\t\t\t\t\t\t\t<image v-for=\"pic in item.pics\" :key=\"pic\" :src=\"pic\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<video \n\t\t\t\t\t\t\tclass=\"video\" \n\t\t\t\t\t\t\t:id=\"'video-' + item.id\" \n\t\t\t\t\t\t\t:src=\"item.video\" \n\t\t\t\t\t\t\tv-if=\"item.video\" \n\t\t\t\t\t\t\**********=\"playvideo\"\n\t\t\t\t\t\t\t:poster=\"pre_url + '/static/img/video-poster.png'\"\n\t\t\t\t\t\t\t:show-play-btn=\"false\"\n\t\t\t\t\t\t\t:controls=\"false\"\n\t\t\t\t\t\t\t:loop=\"false\"\n\t\t\t\t\t\t\t:enable-progress-gesture=\"false\"\n\t\t\t\t\t\t\t:initial-time=\"0\"\n\t\t\t\t\t\t\tobject-fit=\"cover\"\n\t\t\t\t\t\t></video>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 显示价格 -->\n\t\t\t\t\t<view class=\"price-tag\" v-if=\"item.display_price\">\n\t\t\t\t\t\t<text class=\"price-value\" v-if=\"item.is_negotiable\">议价</text>\n\t\t\t\t\t\t<text class=\"price-value\" v-else>¥{{item.price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"phone\" v-if=\"item.isshowphone\">\n\t\t\t\t\t<view class=\"f1\"><label class=\"t1\">姓名：</label>{{item.name}}</view>\n\t\t\t\t\t<view class=\"f1\"><label class=\"t1\">手机号：</label>{{item.mobile}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/lt_read.png'\" style=\"margin-top:0\"></image>{{item.readcount}}</view>\n\t\t\t\t\t<view class=\"f1\" style=\"margin-left:60rpx;\"><image :src=\"pre_url+'/static/img/lt_pinglun.png'\"></image>{{item.plcount}}</view>\n\t\t\t\t\t<view class=\"f1\" v-if=\"item.comment_count !== undefined\" style=\"margin-left:16rpx;font-size:22rpx;color:#999;\">(评论数:{{item.comment_count}})</view>\n\t\t\t\t\t<view class=\"f1\" v-if=\"need_call && item.mobile\" @tap.stop=\"callphone\" :data-phone=\"item.mobile\" style=\"margin-left:60rpx;\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/mobile.png'\" style=\"width: 30rpx;height: 30rpx;margin-top: 0;\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\"></view>\n\t\t\t\t\t<view class=\"f4\" @tap.stop=\"savecontent\" :data-id=\"item.id\" :data-index=\"index\" v-if=\"sysset.cansave\"><image :src=\"pre_url+'/static/img/lt_save.png'\"></image>保存</view>\n\t\t\t\t\t<view class=\"f3\" @tap.stop=\"zan\" :data-id=\"item.id\" :data-index=\"index\"><image :src=\"pre_url+'/static/img/lt_like' + (item['iszan']==0?'':'2') + '.png'\"></image>{{item.zan}}</view>\n\t\t\t\t\t<button v-if=\"sysset.show_top_btn && item.is_top !== 1 && item.mid === mid\" class=\"top-btn\" @tap.stop=\"topPost(item)\">{{sysset.top_text || '置顶'}}</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\t\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"covermy-view flex-col\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t<view class=\"covermy\" @tap=\"goto\" data-url=\"pages/index/index\"><image :src=\"pre_url+'/static/img/lt_gohome.png'\"></image></view>\n\t\t\t<view class=\"covermy\" v-if=\"sysset && sysset.sendcheck!=2\" @tap=\"goto\" data-url=\"fatie\"><image :src=\"pre_url+'/static/img/lt_fatie2.png'\"></image></view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\t\n\t\t\tsysset:{},\n      datalist: [],\n\t\t\tclist:[],\n\t\t\ttaglist:[], // 标签种类\n\t\t\tnavlist:[], // 导航标签\n\t\t\ttitle:'',\n      pagenum: 1,\n      keyword: '',\n      nomore: false,\n\t\t\tnodata:false,\n\t\t\tpicindex:0,\n      need_call:false,\n\t\t\tis_back:0,\n\t\t\tscrollTop: '', // 滚动距离\n\t\t\ttop: '', // 返回时距离顶部\n\t\t\t\n\t\t\t// 筛选相关\n\t\t\tshowFilterPanel: false, // 是否显示筛选面板\n\t\t\tfilterOptions: {}, // 筛选选项\n\t\t\tfilter: {\n\t\t\t\tsort_by: '', // 排序方式\n\t\t\t\ttime_range: 'all', // 时间范围\n\t\t\t\tcontent_type: 'all', // 内容类型\n\t\t\t\tdistance: 'all', // 距离筛选\n\t\t\t\tcategory_ids: [], // 分类ID筛选\n\t\t\t\tlatitude: '', // 用户位置纬度\n\t\t\t\tlongitude: '' // 用户位置经度\n\t\t\t},\n\t\t\thasActiveFilter: false, // 是否有激活的筛选条件\n\t\t\tlocationEnabled: false, // 是否启用位置功能\n\t\t\tdefaultCategoryIcon: '/static/img/default-category.png', // 默认分类图标\n\t\t\t\n\t\t\t// 新增筛选相关\n\t\t\tactiveDropdown: '', // 当前激活的下拉菜单：'area', 'content', 'sort'\n\t\t\t\n\t\t\t// 用户当前位置\n\t\t\tcurrentLatitude: '',\n\t\t\tcurrentLongitude: '',\n    };\n  }, \n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  onPageScroll: function (e) {\n\t\tif (!this.is_back) {\n\t\t\tvar PageScroll = {\n\t\t\t\tpage: this.pagenum,\n\t\t\t\tscrollTop: e.scrollTop\n\t\t\t}\n\t\t\tuni.setStorageSync('onPageScroll', JSON.stringify(PageScroll))\n\t\t}\n\t},\n  onShow() {\n\t if (this.is_back) {\n\t\tvar that = this;\n\t\tvar data = {}\n\t\tif (uni.getStorageSync('onPageScroll')) {\n\t\t\tdata = JSON.parse(uni.getStorageSync('onPageScroll'));\n\t\t} else {\n\t\t\tdata = {\n\t\t\t\tpage: 1,\n\t\t\t\tscrollTop: 0,\n\t\t\t}\n\t\t}\n\t\tthis.top = data.scrollTop;\n\t\tthis.pagenum = 1;\n\t\tthis.getdata(true);\n\t}\n  },\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      that.loading = true;\n      \n      // 构建请求参数，包含筛选条件\n      var params = { pagenum: pagenum };\n      \n      // 如果有筛选条件，添加到请求参数\n      if (that.hasActiveFilter) {\n        // 创建筛选参数的副本\n        let filterParams = JSON.parse(JSON.stringify(that.filter));\n        \n        // 处理距离筛选的位置参数\n        if (filterParams.distance && filterParams.distance !== 'all') {\n          // 添加用户位置信息\n          filterParams.latitude = that.currentLatitude;\n          filterParams.longitude = that.currentLongitude;\n        } else {\n          // 如果不是距离筛选或选择了全部区域，删除位置相关参数\n          delete filterParams.latitude;\n          delete filterParams.longitude;\n          if (filterParams.distance === 'all') {\n            delete filterParams.distance;\n          }\n        }\n        \n        params.filter = filterParams;\n      }\n      \n      app.post('ApiLuntan/index', params, function (res) {\n\t\t    that.loading = false;\n        if(res.need_call){\n            that.need_call = true;\n        }\n        var data = res.datalist;\n        if (pagenum == 1) {\n            uni.setNavigationBarTitle({\n                title: res.title\n            });\n            that.datalist = res.datalist;\n            that.sysset = res.sysset;\n            that.title = res.title;\n            that.clist = res.clist;\n            that.taglist = res.taglist || [];\n            that.navlist = res.navlist || [];\n            \n            // 保存筛选选项\n            if (res.filter_options) {\n              that.filterOptions = res.filter_options;\n            }\n            \n            // 获取位置功能状态\n            if (res.sysset && res.sysset.location_enable !== undefined) {\n              that.locationEnabled = res.sysset.location_enable == 1;\n              \n              // 如果启用位置功能，获取当前位置\n              if (that.locationEnabled && (!that.currentLatitude || !that.currentLongitude)) {\n                that.getUserLocation();\n              }\n            }\n            \n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n\t\t//阅读后返回调用，更新列表阅读数量\n\t\tif (that.is_back) {\n\t\t\tvar timer = setTimeout(() => {\n\t\t\t\tvar data = {}\n\t\t\t\tif (uni.getStorageSync('onPageScroll')) {\n\t\t\t\t\tdata = JSON.parse(uni.getStorageSync('onPageScroll'))\n\t\t\t\t} else {\n\t\t\t\t\tdata = {\n\t\t\t\t\t\tpage: 1,\n\t\t\t\t\t\tscrollTop: 0\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (that.page >= data.page) {\n\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\tscrollTop: that.top,\n\t\t\t\t\t})\n\t\t\t\t\tclearTimeout(timer);\n\t\t\t\t\tthat.is_back = false;\n\t\t\t\t\tuni.setStorageSync('onPageScroll', '');\n\t\t\t\t} else {\n\t\t\t\t\tthat.pagenum++;\n\t\t\t\t\tthat.is_back = true;\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\t\n      });\n    },\n    zan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var index = e.currentTarget.dataset.index;\n      var datalist = that.datalist;\n      app.post(\"ApiLuntan/zan\", {id: id}, function (res) {\n        if (res.type == 0) {\n          //取消点赞\n          datalist[index].iszan = 0;\n          datalist[index].zan = datalist[index].zan - 1;\n        } else {\n          datalist[index].iszan = 1;\n          datalist[index].zan = datalist[index].zan + 1;\n        }\n\n        that.datalist = datalist;\n      });\n    },\n\t\tsavecontent:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar info = that.datalist[index];\n\t\t\tthat.fuzhi(info.content,function(){\n\t\t\t\tthat.savpic(info.pics,function(){\n\t\t\t\t\tthat.savevideo(info.video);\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\tfuzhi:function(content,callback){\n\t\t\tif(!content){\n\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar that = this;\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: content,\n\t\t\t\tsuccess: function () {\n\t\t\t\t\tapp.success('已复制到剪贴板');\n\t\t\t\t\tsetTimeout(function(){\n\t\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\t\t},500)\n\t\t\t\t},\n\t\t\t\tfail:function(){\n\t\t\t\t\tapp.error('请长按文本内容复制');\n\t\t\t\t\tsetTimeout(function(){\n\t\t\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\t\t},500)\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tsavpic:function(pics,callback){\n\t\t\tif(!pics){\n\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){\n\t\t\t\tapp.error('请长按图片保存');return;\n\t\t\t}\n\t\t\tthis.picindex = 0;\n\t\t\tthis.savpic2(pics);\n\t\t\ttypeof callback == 'function' && callback();\n\t\t},\n\t\tsavpic2:function(pics){\n\t\t\tvar that = this;\n\t\t\tvar picindex = this.picindex;\n\t\t\tif(picindex >= pics.length){\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success('已保存到相册');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar pic = pics[picindex];\n\t\t\tapp.showLoading('图片保存中');\n\t\t\tuni.downloadFile({\n\t\t\t\turl: pic,\n\t\t\t\tsuccess (res) {\n\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\tsuccess:function () {\n\t\t\t\t\t\t\t\tthat.picindex++;\n\t\t\t\t\t\t\t\tthat.savpic2(pics);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail:function(){\n\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\tapp.error('保存失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail:function(){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.error('下载失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tsavevideo:function(video){\n\t\t\tif(!video) return;\n\t\t\tapp.showLoading('视频下载中');\n\t\t\tuni.downloadFile({\n\t\t\t\turl: video,\n\t\t\t\tsuccess (res) {\n\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\tuni.saveVideoToPhotosAlbum({\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\tsuccess:function () {\n\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\tapp.success('视频保存成功');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail:function(){\n\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\tapp.error('视频保存失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail:function(){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.error('视频下载失败!');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n        callphone:function(e) {\n        \tvar phone = e.currentTarget.dataset.phone;\n        \tuni.makePhoneCall({\n        \t\tphoneNumber: phone,\n        \t\tfail: function () {\n        \t\t}\n        \t});\n        },\n    topPost(item) {\n      app.post('ApiLuntan/checkTopStatus', { luntanid: item.id }, res => {\n        if (res.status === 1) {\n          if (res.is_top) {\n            app.alert(`帖子已置顶，剩余${res.remain_days}天${res.remain_hours}小时`);\n          } else {\n            app.post('ApiLuntan/getTopPrices', { cid: item.cid }, r => {\n              if (r.status === 1) {\n                if (r.can_top) {\n                  // 可以前往置顶\n                  uni.navigateTo({\n                    url: '/activity/luntan/fatielog?action=top&id=' + item.id\n                  });\n                } else {\n                  app.alert(r.limit_msg || '无法置顶');\n                }\n              } else {\n                app.alert(r.msg || '获取置顶价格失败');\n              }\n            });\n          }\n        } else {\n          app.alert(res.msg || '检查置顶状态失败');\n        }\n      });\n    },\n    // 切换筛选下拉菜单\n    toggleFilterDropdown: function(type) {\n      if (this.activeDropdown === type) {\n        this.activeDropdown = ''; // 已激活，则关闭\n      } else {\n        this.activeDropdown = type; // 激活指定类型\n      }\n    },\n    \n    // 关闭下拉菜单\n    closeDropdown: function() {\n      this.activeDropdown = '';\n    },\n    selectFilter(type, value) {\n      // 如果点击当前选中的值，则取消选择\n      if (this.filter[type] === value) {\n        // 时间范围和内容类型默认为'all'\n        if (type === 'time_range' || type === 'content_type' || type === 'distance') {\n          this.filter[type] = 'all';\n        } else {\n          this.filter[type] = '';\n        }\n      } else {\n        this.filter[type] = value;\n      }\n    },\n    // 重置区域筛选\n    resetAreaFilter: function() {\n      this.filter.distance = 'all';\n      this.filter.category_ids = [];\n      this.filter.latitude = '';\n      this.filter.longitude = '';\n    },\n    \n    // 新增的重置方法\n    resetDistanceFilter: function() {\n      this.filter.distance = 'all';\n    },\n    \n    resetCategoryFilter: function() {\n      this.filter.category_ids = [];\n    },\n    \n    resetTimeFilter: function() {\n      this.filter.time_range = 'all';\n    },\n    \n    // 重置内容筛选\n    resetContentFilter: function() {\n      this.filter.content_type = 'all';\n    },\n    \n    // 重置排序筛选\n    resetSortFilter: function() {\n      this.filter.sort_by = '';\n    },\n    \n    // 重置所有筛选\n    resetFilter: function() {\n      this.filter = {\n        sort_by: '',\n        time_range: 'all',\n        content_type: 'all',\n        distance: 'all',\n        category_ids: [],\n        latitude: '',\n        longitude: ''\n      };\n      this.hasActiveFilter = false;\n      \n      // 重新加载数据\n      this.pagenum = 1;\n      this.getdata();\n      \n      // 关闭筛选面板\n      this.activeDropdown = '';\n    },\n    applyFilter: function() {\n      // 检查是否有激活的筛选条件\n      this.hasActiveFilter = (\n        this.filter.sort_by !== '' || \n        this.filter.time_range !== 'all' || \n        this.filter.content_type !== 'all' || \n        this.filter.distance !== 'all' || \n        this.filter.category_ids.length > 0\n      );\n      \n      // 重新加载数据\n      this.pagenum = 1;\n      this.getdata();\n      \n      // 关闭筛选面板\n      this.activeDropdown = '';\n    },\n    isCategorySelected(categoryId) {\n      return this.filter.category_ids.includes(categoryId);\n    },\n    toggleCategory: function(id) {\n      const index = this.filter.category_ids.indexOf(id);\n      if (index > -1) {\n        // 已选中，则移除\n        this.filter.category_ids.splice(index, 1);\n      } else {\n        // 未选中，则添加\n        this.filter.category_ids.push(id);\n      }\n    },\n    getUserLocation: function() {\n      var that = this;\n      uni.getLocation({\n        type: 'gcj02', // 使用国测局坐标系\n        success: function(res) {\n          that.currentLatitude = res.latitude;\n          that.currentLongitude = res.longitude;\n          console.log('获取位置成功:', res.latitude, res.longitude);\n        },\n        fail: function(err) {\n          console.log('获取位置失败:', err);\n          // 失败时也不阻止用户使用，只是不能按距离筛选\n        }\n      });\n    },\n    // 简化的筛选方法\n    selectSortOption: function(id) {\n      if (this.filter.sort_by === id) {\n        this.filter.sort_by = '';\n      } else {\n        this.filter.sort_by = id;\n      }\n    },\n    \n    selectTimeOption: function(id) {\n      if (this.filter.time_range === id) {\n        this.filter.time_range = 'all';\n      } else {\n        this.filter.time_range = id;\n      }\n    },\n    \n    selectContentOption: function(id) {\n      if (this.filter.content_type === id) {\n        this.filter.content_type = 'all';\n      } else {\n        this.filter.content_type = id;\n      }\n    },\n    \n    selectDistanceOption: function(id) {\n      if (this.filter.distance === id) {\n        this.filter.distance = 'all';\n      } else {\n        this.filter.distance = id;\n      }\n    },\n    playvideo: function(e) {\n      // 获取当前视频的ID\n      const videoId = e.currentTarget.id;\n      if (!videoId) return;\n      \n      // 停止所有播放中的视频\n      this.stopAllVideos();\n      \n      // 只播放当前点击的视频\n      const videoContext = uni.createVideoContext(videoId, this);\n      if (videoContext) {\n        videoContext.play();\n        // 显示控制条\n        videoContext.requestFullScreen();\n      }\n    },\n    \n    stopAllVideos: function() {\n      // 使用uni-app的API处理\n      uni.createSelectorQuery().selectAll('video').fields({\n        id: true\n      }, (res) => {\n        if (res && res.length > 0) {\n          res.forEach((item) => {\n            if (item.id) {\n              var videoContext = uni.createVideoContext(item.id, this);\n              if (videoContext) {\n                videoContext.pause();\n                videoContext.stop();\n              }\n            }\n          });\n        }\n      }).exec();\n    },\n    \n    loaded: function() {\n      // 页面加载完成后处理\n      this.isload = true;\n      \n      // 延迟执行以确保视频元素已经渲染\n      setTimeout(() => {\n        // 确保所有视频都处于暂停状态\n        this.stopAllVideos();\n      }, 300);\n    },\n    \n    // 预览表单中的图片\n    previewImage: function(e) {\n      const urls = e.currentTarget.dataset.urls || [];\n      const current = e.currentTarget.dataset.current || '';\n      \n      if (urls.length > 0) {\n        uni.previewImage({\n          current: current,\n          urls: urls,\n          fail: function(err) {\n            console.log('预览图片失败:', err);\n          }\n        });\n      }\n    },\n  }\n};\n</script>\n<style>\n.container2{width:100%;padding:20rpx;background:#fff;}\n.navbox{background: #fff;height: auto;overflow: hidden;}\n.nav_li{width:20%;text-align: center;box-sizing: border-box;padding:15rpx 0 5rpx;float: left;color:#222;font-size:24rpx}\n.nav_li image{width:70rpx;height: 70rpx;margin-bottom:8rpx;}\n\n.listtitle{width:100%;padding:0 24rpx;color:#222;font-weight:bold;font-size:32rpx;height:60rpx;line-height:60rpx}\n.datalist{width:100%;padding:0 24rpx;}\n.datalist .item{width:100%;display:flex;flex-direction:column;padding:24rpx 0;border-bottom:1px solid #f1f1f1}\n.datalist .item .top{width:100%;display:flex;align-items:center}\n.datalist .item .top .f1{width:80rpx;height:80rpx;border-radius:50%;margin-right:16rpx}\n.datalist .item .top .f2 .t1{color:#222;font-weight:bold;font-size:28rpx}\n.datalist .item .top .f2 .t2{color:#bbb;font-size:24rpx}\n.datalist .item .con{width:100%;padding:24rpx 0;display:flex;flex-direction:column;color:#000}\n.datalist .item .con .f2{margin-top:10rpx;display:flex;flex-wrap:wrap}\n.datalist .item .con .f2 image{width:200rpx;height:200rpx;margin-right:2%;margin-bottom:10rpx;border-radius:8rpx}\n.datalist .item .con .video{width:80%;height:300rpx;margin-top:20rpx}\n.datalist .item .bot{width:100%;display:flex;align-items:center;color:#222222;font-size:28rpx}\n.datalist .item .bot .f1{display:flex;align-items:center;font-weight:bold}\n.datalist .item .bot .f1 image{width:36rpx;height:36rpx;margin-right:16rpx;margin-top:2px}\n.datalist .item .bot .f2{flex:1;}\n.datalist .item .bot .f3{display:flex;align-items:center;font-weight:bold}\n.datalist .item .bot .f3 image{width:40rpx;height:40rpx;margin-right:16rpx}\n.datalist .item .bot .f4{display:flex;align-items:center;margin-right:30rpx}\n.datalist .item .bot .f4 image{width:40rpx;height:40rpx;margin-right:10rpx}\n.datalist .item .bot .btn2{color:#fff;background:#FE1A29;border:1px solid #FE1A29;padding:6rpx 40rpx;font-size:24rpx;border-radius:40rpx;margin-left:16rpx}\n.covermy-view{position:fixed;z-index:99999;bottom:0;right:20rpx;width:126rpx;height: 250rpx;box-sizing:content-box;justify-content: space-between;margin-bottom: 140rpx;}\n.covermy{width:126rpx;height:126rpx;box-sizing:content-box;}\n.covermy image{width:100%;height:100%;}\n\n.nomore-footer-tips{background:#fff!important}\n.phone .f1{line-height: 60rpx;display: flex;}\n.phone .f1 label{ color: #999; width: 120rpx;}\n.cate{color:#aaa;font-size:22rpx;margin-top:2rpx;}\n.top-tag {\n  display: inline-block;\n  background-color: #ff6b6b;\n  color: #fff;\n  font-size: 20rpx;\n  padding: 2rpx 10rpx;\n  border-radius: 8rpx;\n  margin-left: 10rpx;\n  font-weight: normal;\n  vertical-align: middle;\n}\n.top-btn {\n  color: #fff;\n  background-color: #ff6b6b;\n  border: none;\n  border-radius: 30rpx;\n  padding: 0 16rpx;\n  font-size: 24rpx;\n  height: 48rpx;\n  line-height: 48rpx;\n  margin-left: 20rpx;\n}\n\n/* 标签种类样式 */\n.tag-category {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 10rpx 0;\n  margin-bottom: 20rpx;\n}\n.tag-item {\n  background-color: #f8f8f8;\n  border-radius: 30rpx;\n  padding: 8rpx 20rpx;\n  margin: 10rpx;\n  display: flex;\n  align-items: center;\n}\n.tag-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 10rpx;\n}\n.tag-name {\n  font-size: 24rpx;\n  color: #666;\n}\n\n/* 导航标签新样式 */\n.nav-tags-wrapper {\n  display: none;\n}\n.nav-tag-item {\n  display: none;\n}\n.nav-tag-icon {\n  display: none;\n}\n.nav-tag-name {\n  display: none;\n}\n.nav-tag-item::after {\n  display: none;\n}\n\n/* 价格标签样式 */\n.price-tag {\n  display: inline-block;\n  background-color: #f8f8f8;\n  border: 1px solid #e74c3c;\n  border-radius: 8rpx;\n  margin-top: 16rpx;\n  padding: 6rpx 16rpx;\n  align-self: flex-start;\n}\n.price-value {\n  color: #e74c3c;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n/* 内容隐藏提示样式 */\n.content-hidden-tip {\n  background-color: #f0f0f0;\n  border: 1rpx dashed #ccc;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  text-align: center;\n  margin: 10rpx 0;\n}\n.tip-text {\n  color: #999;\n  font-size: 26rpx;\n}\n\n/* 表单内容显示样式 */\n.form-content {\n  width: 100%;\n  padding: 0;\n}\n\n.form-item {\n  margin-bottom: 24rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n.field-label {\n  color: #666;\n  font-size: 26rpx;\n  margin-bottom: 8rpx;\n  font-weight: bold;\n}\n\n.field-value {\n  color: #333;\n  font-size: 28rpx;\n  line-height: 1.6;\n  margin-bottom: 8rpx;\n}\n\n.field-images {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 8rpx;\n}\n\n.form-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n  border-radius: 8rpx;\n  object-fit: cover;\n}\n\n.form-video {\n  width: 100%;\n  height: 300rpx;\n  margin-top: 8rpx;\n  border-radius: 8rpx;\n}\n\n/* 新的筛选组件样式 */\n.top-filter-bar {\n  display: flex;\n  width: 100%;\n  height: 80rpx;\n  line-height: 80rpx;\n  border-bottom: 1rpx solid #eee;\n  background-color: #fff;\n  position: relative;\n  z-index: 10;\n  margin-bottom: 20rpx;\n}\n\n.filter-item {\n  flex: 1;\n  text-align: center;\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 26rpx;\n}\n\n.filter-item text {\n  font-size: 26rpx;\n  color: #333;\n  padding-right: 6rpx;\n}\n\n.filter-item .active-filter {\n  color: #3B7CFF;\n  font-weight: 500;\n}\n\n.arrow {\n  display: inline-block;\n  width: 0;\n  height: 0;\n  border-left: 8rpx solid transparent;\n  border-right: 8rpx solid transparent;\n  border-top: 8rpx solid #666;\n  transition: transform 0.3s;\n}\n\n.arrow-up {\n  transform: rotate(180deg);\n}\n\n.filter-dropdown {\n  position: absolute;\n  left: 0;\n  width: 100%;\n  background-color: #fff;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n  z-index: 100;\n}\n\n.dropdown-content {\n  padding: 20rpx;\n}\n\n.dropdown-section {\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 16rpx;\n}\n\n.options-list {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.option-item {\n  background-color: #f5f5f5;\n  padding: 10rpx 30rpx;\n  margin-right: 20rpx;\n  margin-bottom: 20rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  color: #333;\n}\n\n.option-active {\n  background-color: #E6F0FF;\n  color: #3B7CFF;\n  font-weight: 500;\n}\n\n.options-grid {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.grid-item {\n  width: 22%;\n  margin-right: 3%;\n  margin-bottom: 20rpx;\n  padding: 10rpx 0;\n  text-align: center;\n  background-color: #f5f5f5;\n  border-radius: 6rpx;\n  font-size: 24rpx;\n  color: #333;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.grid-item:nth-child(4n) {\n  margin-right: 0;\n}\n\n.grid-active {\n  background-color: #E6F0FF;\n  color: #3B7CFF;\n  font-weight: 500;\n}\n\n.dropdown-actions {\n  display: flex;\n  margin-top: 30rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n}\n\n.reset {\n  background-color: #f5f5f5;\n  color: #666;\n  margin-right: 10rpx;\n}\n\n.confirm {\n  background-color: #3B7CFF;\n  color: #fff;\n  margin-left: 10rpx;\n}\n\n.mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.3);\n  z-index: 99;\n}\n\n/* 适应5个筛选项的手机布局 */\n@media screen and (max-width: 768px) {\n  .top-filter-bar {\n    padding: 0 10rpx;\n  }\n  \n  .filter-item {\n    padding: 0 5rpx;\n  }\n  \n  .filter-item text {\n    font-size: 24rpx;\n    padding-right: 4rpx;\n  }\n  \n  .arrow {\n    border-left: 6rpx solid transparent;\n    border-right: 6rpx solid transparent;\n    border-top: 6rpx solid #666;\n  }\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464363\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}