{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatie.vue?0506", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatie.vue?d59c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatie.vue?eaf7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatie.vue?f1b1", "uni-app:///activity/luntan/fatie.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatie.vue?45a7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatie.vue?9bb9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "content_pic", "pagenum", "cateArr", "cindex", "pics", "video", "need_call", "clist", "clist2", "cateArr2", "cindex2", "cate2", "displaytype", "isphone", "iscatephone", "formfields", "showFormFields", "formvaldata", "defaultIcon", "currentCategory", "price", "showPrice", "userScoreBalance", "currentPostData", "latitude", "longitude", "address", "province", "city", "district", "hasLocation", "locationEnabled", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "display_type", "cateChange", "cateChange2", "formsubmit", "cid", "cid2", "content", "mobile", "name", "postData", "closePaymentPopup", "choosePaymentMethod", "submitPostWithScore", "setTimeout", "submitPostWithMoney", "submitFreePost", "uploadimg", "uploadvideo", "uni", "sourceType", "maxDuration", "success", "url", "filePath", "fail", "removeVideo", "removeimg", "getCate2", "pid", "onSecondaryCategoryTap", "getFormByCategory", "setFormField", "onPickerChange", "value", "onCategoryTap", "onShowPriceChange", "getUserLocation", "onShow", "payorderid", "order_id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwUv1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UACA;QACA;QACAD;QACAA;QACA;QACA;UACA;UACA;YACA;cAEAA;cACA;gBACAA;cACA;cACA;gBACAA;cACA;YACA;YACAlC;UACA;QACA;UACAA;QACA;QACAkC;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;UACA;UACA;YACAA;UACA;QACA;QAEAA;UACA;YACAA;YACA;;YAEA;YACA;cACA;cACAA;YACA;cACA;cACAA;YACA;UACA;QACA;QACAA;MACA;IACA;IACAG;MACA;MACAH;MACA;MACA;QACA;QACA;UACAA;QACA;MACA;QACA;MACA;MACA;QACAA;MACA;MACAA;IACA;IACAI;MACA;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACA;UACAJ;UACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA;YACAA;YACA;UACA;UACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UAAA;QACA;QACA;UACAA;UAAA;QACA;QACA;UACAA;UAAA;QACA;MACA;MACA;MACA;QACA;UACA;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;MACA;MACA;QACAK;QACAC;QACAvC;QACAwC;QACAvC;QACAwC;QACAC;MACA;;MAEA;MACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;MAEA;QACAA;MACA;;MAEA;MACA;QACAA;QACAA;MACA;;MAEA;MACA;QACA;QACAX;QACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACAA;IACA;IAEA;IACAY;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;QACAZ;QACA;MACA;;MAEA;MACAD;MAEA;QACA;QACAA;MACA;QACA;QACA;UACAC;UACA;QACA;;QAEA;QACAA;UACAD;QACA;MACA;IACA;IAEA;IACAc;MACA;MACA;;MAEA;MACAH;MAEAV;QACAA;QACA;UACAA;UACAc;YACAd;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAe;MACA;MACA;;MAEA;MACAL;MAEAV;QACAA;QAEA;UACA;UACA;UACAA;UACA;QACA;UACA;UACA;UACAA;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAgB;MACA;MACA;;MAEA;MACAN;MAEAV;QACAA;QACA;UACAA;UACAc;YACAd;UACA;QACA;UACAA;QACA;MACA;IACA;IACAiB;MACA;MACA;;MAEA;MACA;MACA;MACAjB;QACA;UACAjC;QACA;QACA;QACA;QACA;MACA;IACA;IACAmD;MACA;MACAC;QACAC;QACAC;QACAC;UACA;UACAtB;UACAmB;YACAI;YACAC;YACAf;YACAa;cACAtB;cACA;cAEA;gBACAD;cACA;gBACAC;cACA;YACA;YACAyB;cACAzB;cACAA;YACA;UACA;QACA;QACAyB;UACA;QAAA;MAEA;IACA;IACAC;MACA;MACA1B;IACA;IACA2B;MACA;MACA;MACA;MACA;MACA5D;IACA;IACA6D;MACA;MACA;;MAEA5B;QAAA6B;MAAA;QACA;UACA;UACA9B;UACA;YACA;YACAA;YACA;YACAA;UACA;YACA;YACAA;YACAA;UACA;QACA;UACAA;UACAA;UACA;UACAA;UACAC;QACA;MACA;IACA;IAEA;IACA8B;MACA;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;;MAEA;MACA/B;QAAAK;MAAA;QACA;UACAN;UACAA;QACA;UACAA;UACAA;QACA;MACA;;MAEA;MACAC;QAAAK;MAAA;QACA;UACAN;UACA;UACA;YACAA;YACAA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAiC;MACA;MACA;MACA;MACA;MACA;QACAjC;MACA;QACAA;MACA;QACAA;MACA;IACA;IACAkC;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACAC;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACArC;QACA;QACAD;QACAA;QACAA;;QAEA;QACAC;UACAb;UACAC;QACA;UACA;YACAW;YACAA;YACAA;YACAA;UACA;QACA;MACA;QACA;QACA;MAAA,CACA;IACA;EACA;EACAuC;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;MACAC;IACA;IACA;MACA;MACAvC;QAAAwC;MAAA;QACA;UACAxC;UACAc;YACAd;UACA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACl6BA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/luntan/fatie.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/luntan/fatie.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fatie.vue?vue&type=template&id=c861f5f8&\"\nvar renderjs\nimport script from \"./fatie.vue?vue&type=script&lang=js&\"\nexport * from \"./fatie.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fatie.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/luntan/fatie.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatie.vue?vue&type=template&id=c861f5f8&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.cate2 && _vm.clist2.length > 0 : null\n  var g1 = _vm.isload ? _vm.pics.length : null\n  var g2 = _vm.isload ? _vm.pics.join(\",\") : null\n  var g3 = _vm.isload\n    ? _vm.showFormFields &&\n      _vm.formfields &&\n      _vm.formfields.content &&\n      _vm.formfields.content.length\n    : null\n  var l1 =\n    _vm.isload && g3\n      ? _vm.__map(_vm.formfields.content, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var l0 =\n            !(item.key == \"input\") &&\n            !(item.key == \"textarea\") &&\n            !(item.key == \"radio\" || item.key == \"sex\") &&\n            item.key == \"checkbox\"\n              ? _vm.__map(item.val2, function (opt, optIdx) {\n                  var $orig = _vm.__get_orig(opt)\n                  var g4 =\n                    _vm.formvaldata[\"form\" + idx] &&\n                    _vm.formvaldata[\"form\" + idx].indexOf(opt) >= 0\n                  return {\n                    $orig: $orig,\n                    g4: g4,\n                  }\n                })\n              : null\n          var g5 =\n            !(item.key == \"input\") &&\n            !(item.key == \"textarea\") &&\n            !(item.key == \"radio\" || item.key == \"sex\") &&\n            !(item.key == \"checkbox\") &&\n            item.key == \"selector\" &&\n            _vm.formvaldata[\"form\" + idx]\n              ? item.val2.indexOf(_vm.formvaldata[\"form\" + idx])\n              : null\n          return {\n            $orig: $orig,\n            l0: l0,\n            g5: g5,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatie.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatie.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"formsubmit\">\n\t\t\t<view class=\"page-header\">\n\t\t\t\t<view @tap=\"goback\" class=\"back-btn\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/goback.jpg'\" class=\"back-icon\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"page-title\">我要发帖</view>\n\t\t\t\t<view class=\"header-right\"></view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"card-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text>分类选择</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-bar\">\n\t\t\t\t\t<scroll-view scroll-x=\"true\" class=\"category-scroll\">\n\t\t\t\t\t\t<view class=\"category-flex\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tv-for=\"(item, idx) in clist\"\n\t\t\t\t\t\t\t\t:key=\"item.id\"\n\t\t\t\t\t\t\t\tclass=\"category-btn\"\n\t\t\t\t\t\t\t\t:class=\"{active: cindex === idx}\"\n\t\t\t\t\t\t\t\t@tap=\"onCategoryTap(idx)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<image :src=\"item.pic || defaultIcon\" class=\"category-icon\"></image>\n\t\t\t\t\t\t\t\t<view class=\"category-name\">{{ item.name }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 二级分类选择 -->\n\t\t\t\t<view class=\"category-bar\" v-if=\"cate2 && clist2.length > 0\" style=\"margin-top: 20rpx;\">\n\t\t\t\t\t<view class=\"section-subtitle\">二级分类</view>\n\t\t\t\t\t<scroll-view scroll-x=\"true\" class=\"category-scroll\">\n\t\t\t\t\t\t<view class=\"category-flex\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tv-for=\"(item, idx) in clist2\"\n\t\t\t\t\t\t\t\t:key=\"item.id\"\n\t\t\t\t\t\t\t\tclass=\"category-btn\"\n\t\t\t\t\t\t\t\t:class=\"{active: cindex2 === idx}\"\n\t\t\t\t\t\t\t\t@tap=\"onSecondaryCategoryTap(idx)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<image :src=\"item.pic || defaultIcon\" class=\"category-icon\"></image>\n\t\t\t\t\t\t\t\t<view class=\"category-name\">{{ item.name }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"card-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text>内容描述</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"content-area\">\n\t\t\t\t\t<textarea class=\"form-textarea\" placeholder=\"请输入内容描述(可以添加图片、视频等)\" name=\"content\" maxlength=\"-1\"></textarea>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 价格设置 -->\n\t\t\t<view class=\"card-section\" v-if=\"currentCategory && currentCategory.price_enable == 1\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text>价格设置</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price-input-wrapper\">\n\t\t\t\t\t<view class=\"price-switch-container\">\n\t\t\t\t\t\t<text class=\"price-switch-label\">显示价格</text>\n\t\t\t\t\t\t<switch name=\"show_price\" :checked=\"showPrice\" @change=\"onShowPriceChange\" color=\"#3B7CFF\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"price-input-container\" v-if=\"showPrice\">\n\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\n\t\t\t\t\t\t<input type=\"digit\" name=\"price\" v-model=\"price\" class=\"price-input\" placeholder=\"设置价格\" maxlength=\"10\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"price-tip\" v-if=\"showPrice\">\n\t\t\t\t\t\t<text>提示：价格为0表示议价，将显示为\"议价\"</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"card-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text>有图有真相</text>\n\t\t\t\t\t<text class=\"subtitle\">(最多上传9张图片、1个视频)</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"media-upload-area\">\n\t\t\t\t\t<view class=\"upload-title\">上传图片</view>\n\t\t\t\t\t<view class=\"upload-content\">\n\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;\">\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"image-preview-box\">\n\t\t\t\t\t\t\t\t<view class=\"delete-icon\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\">\n\t\t\t\t\t\t\t\t\t<image src=\"/static/img/ico-del.png\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"aspectFill\" class=\"preview-image\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"upload-btn\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<9\">\n\t\t\t\t\t\t\t\t<view class=\"upload-icon\">+</view>\n\t\t\t\t\t\t\t\t<view class=\"upload-text\">上传图片</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"upload-title\">上传视频</view>\n\t\t\t\t\t<view class=\"upload-content\">\n\t\t\t\t\t\t<view class=\"video-upload-area\">\n\t\t\t\t\t\t\t<view class=\"video-preview\" v-if=\"video\">\n\t\t\t\t\t\t\t\t<video :src=\"video\" class=\"preview-video\"></video>\n\t\t\t\t\t\t\t\t<view class=\"delete-icon\" @tap=\"removeVideo\">\n\t\t\t\t\t\t\t\t\t<image src=\"/static/img/ico-del.png\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"upload-btn video-upload-btn\" @tap=\"uploadvideo\" v-if=\"!video\">\n\t\t\t\t\t\t\t\t<view class=\"upload-icon\">+</view>\n\t\t\t\t\t\t\t\t<view class=\"upload-text\">上传视频</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"video\" :value=\"video\" maxlength=\"-1\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 自定义表单区域 -->\n\t\t\t<view class=\"card-section\" v-if=\"showFormFields && formfields && formfields.content && formfields.content.length\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text>其他信息</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\" v-for=\"(item, idx) in formfields.content\" :key=\"idx\">\n\t\t\t\t\t<view class=\"item-label\">{{item.val1}}<text class=\"required-mark\" v-if=\"item.val3==1\">*</text></view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 输入框 -->\n\t\t\t\t\t<block v-if=\"item.key=='input'\">\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t:type=\"item.val4==1?'text':(item.val4==2?'number':'text')\"\n\t\t\t\t\t\t\t:placeholder=\"'请输入'+item.val1\"\n\t\t\t\t\t\t\t:value=\"formvaldata['form'+idx] || ''\"\n\t\t\t\t\t\t\t@input=\"setFormField($event, idx)\"\n\t\t\t\t\t\t\tclass=\"form-input\" />\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 文本域 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='textarea'\">\n\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\t:placeholder=\"'请输入'+item.val1\"\n\t\t\t\t\t\t\t:value=\"formvaldata['form'+idx] || ''\"\n\t\t\t\t\t\t\t@input=\"setFormField($event, idx)\"\n\t\t\t\t\t\t\tclass=\"form-textarea-field\"></textarea>\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 单选 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='radio' || item.key=='sex'\">\n\t\t\t\t\t\t<radio-group @change=\"setFormField($event, idx)\" class=\"option-group\">\n\t\t\t\t\t\t\t<label v-for=\"(opt, optIdx) in item.val2\" :key=\"optIdx\" class=\"radio-label\">\n\t\t\t\t\t\t\t\t<radio :value=\"opt\" :checked=\"formvaldata['form'+idx] == opt\" class=\"radio-input\" />\n\t\t\t\t\t\t\t\t<text class=\"option-text\">{{opt}}</text>\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 多选 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='checkbox'\">\n\t\t\t\t\t\t<checkbox-group @change=\"setFormField($event, idx)\" class=\"option-group\">\n\t\t\t\t\t\t\t<label v-for=\"(opt, optIdx) in item.val2\" :key=\"optIdx\" class=\"checkbox-label\">\n\t\t\t\t\t\t\t\t<checkbox :value=\"opt\" :checked=\"formvaldata['form'+idx] && formvaldata['form'+idx].indexOf(opt)>=0\" class=\"checkbox-input\" />\n\t\t\t\t\t\t\t\t<text class=\"option-text\">{{opt}}</text>\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 选择器 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='selector'\">\n\t\t\t\t\t\t<view class=\"selector-container\">\n\t\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t\t:range=\"item.val2\" \n\t\t\t\t\t\t\t\t:value=\"formvaldata['form'+idx] ? item.val2.indexOf(formvaldata['form'+idx]) : -1\"\n\t\t\t\t\t\t\t\t@change=\"onPickerChange($event, idx, 'selector', item.val2)\"\n\t\t\t\t\t\t\t\tclass=\"picker-wrapper\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">\n\t\t\t\t\t\t\t\t\t<text>{{formvaldata['form'+idx] || '请选择'+item.val1}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"picker-arrow\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 时间选择 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='time'\">\n\t\t\t\t\t\t<view class=\"selector-container\">\n\t\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t\tmode=\"time\"\n\t\t\t\t\t\t\t\t:value=\"formvaldata['form'+idx] || ''\" \n\t\t\t\t\t\t\t\t@change=\"onPickerChange($event, idx, 'time')\"\n\t\t\t\t\t\t\t\tclass=\"picker-wrapper\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">\n\t\t\t\t\t\t\t\t\t<text>{{formvaldata['form'+idx] || '请选择时间'}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"picker-arrow\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 日期选择 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='date'\">\n\t\t\t\t\t\t<view class=\"selector-container\">\n\t\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t\tmode=\"date\"\n\t\t\t\t\t\t\t\t:value=\"formvaldata['form'+idx] || ''\" \n\t\t\t\t\t\t\t\t@change=\"onPickerChange($event, idx, 'date')\"\n\t\t\t\t\t\t\t\tclass=\"picker-wrapper\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">\n\t\t\t\t\t\t\t\t\t<text>{{formvaldata['form'+idx] || '请选择日期'}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"picker-arrow\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 年份选择 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='year'\">\n\t\t\t\t\t\t<view class=\"selector-container\">\n\t\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t\tmode=\"date\" \n\t\t\t\t\t\t\t\tfields=\"year\"\n\t\t\t\t\t\t\t\t:value=\"formvaldata['form'+idx] || ''\" \n\t\t\t\t\t\t\t\t@change=\"onPickerChange($event, idx, 'year')\"\n\t\t\t\t\t\t\t\tclass=\"picker-wrapper\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">\n\t\t\t\t\t\t\t\t\t<text>{{formvaldata['form'+idx] || '请选择年份'}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"picker-arrow\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 地区选择 -->\n\t\t\t\t\t<block v-else-if=\"item.key=='region' || item.key=='area'\">\n\t\t\t\t\t\t<view class=\"selector-container\">\n\t\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t\tmode=\"region\"\n\t\t\t\t\t\t\t\t:value=\"formvaldata['form'+idx] || ''\" \n\t\t\t\t\t\t\t\t@change=\"onPickerChange($event, idx, 'region')\"\n\t\t\t\t\t\t\t\tclass=\"picker-wrapper\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">\n\t\t\t\t\t\t\t\t\t<text>{{formvaldata['form'+idx] || '请选择省市区'}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"picker-arrow\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"submit-area\">\n\t\t\t\t<button form-type=\"submit\" class=\"submit-btn\">发布</button>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"history-link\" @tap=\"goto\" data-url=\"fatielog\">\n\t\t\t\t<text>我的发帖记录</text>\n\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"arrow-right\"></image>\n\t\t\t</view>\n\t\t</form>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t\n\t<!-- 支付方式选择弹窗 -->\n\t<uni-popup ref=\"paymentPopup\" type=\"center\">\n\t\t<view class=\"payment-popup\">\n\t\t\t<view class=\"payment-popup-title\">选择发帖方式</view>\n\t\t\t<view class=\"payment-popup-user-score\">当前积分余额：{{userScoreBalance}}</view>\n\t\t\t<view class=\"payment-popup-content\">\n\t\t\t\t<view v-if=\"currentCategory && currentCategory.post_price > 0\" \n\t\t\t\t\tclass=\"payment-option\" \n\t\t\t\t\t:class=\"{'disabled': false}\"\n\t\t\t\t\t@tap=\"choosePaymentMethod('money')\">\n\t\t\t\t\t<view class=\"payment-option-left\">\n\t\t\t\t\t\t<view class=\"payment-option-info\">\n\t\t\t\t\t\t\t<view class=\"payment-name\">付费发帖</view>\n\t\t\t\t\t\t\t<view class=\"payment-desc\">需要支付 ¥{{currentCategory.post_price}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"payment-option-right\">\n\t\t\t\t\t\t<radio :checked=\"false\" color=\"#3B7CFF\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view v-if=\"currentCategory && currentCategory.post_score > 0\" \n\t\t\t\t\tclass=\"payment-option\" \n\t\t\t\t\t:class=\"{'disabled': userScoreBalance < currentCategory.post_score}\"\n\t\t\t\t\t@tap=\"choosePaymentMethod('score')\">\n\t\t\t\t\t<view class=\"payment-option-left\">\n\t\t\t\t\t\t<view class=\"payment-option-info\">\n\t\t\t\t\t\t\t<view class=\"payment-name\">积分发帖</view>\n\t\t\t\t\t\t\t<view class=\"payment-desc\">需要消耗 {{currentCategory.post_score}} 积分</view>\n\t\t\t\t\t\t\t<view v-if=\"userScoreBalance < currentCategory.post_score\" class=\"payment-warning\">积分不足</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"payment-option-right\">\n\t\t\t\t\t\t<radio :checked=\"false\" color=\"#3B7CFF\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"payment-popup-buttons\">\n\t\t\t\t<button class=\"cancel-btn\" @tap=\"closePaymentPopup\">取消</button>\n\t\t\t</view>\n\t\t</view>\n\t</uni-popup>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      datalist: [],\n      content_pic: [],\n      pagenum: 1,\n      cateArr: [],\n      cindex: -1,\n\t\t\tpics:[],\n      video: '',\n      need_call:false,\n      clist:[],\n      \n      clist2:[],\n      cateArr2: [],\n      cindex2: -1,\n      cate2:false,\n      displaytype:-1,\n\t\t\tisphone:false,\n\t\t\tiscatephone:false,\n      formfields: null,\n      showFormFields: false,\n      formvaldata: {},\n      defaultIcon: '/static/img/default-category.png', // 默认分类图标\n      currentCategory: null,\n      price: '',\n      showPrice: false,\n      userScoreBalance: 0,\n      currentPostData: null, // 当前表单数据，用于弹窗选择时使用\n      \n      // 添加位置相关变量\n      latitude: null,\n      longitude: null,\n      address: null,\n      province: null,\n      city: null,\n      district: null,\n      hasLocation: false, // 是否已获取位置信息\n      locationEnabled: true, // 是否启用位置功能\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n        this.displaytype = this.opt.displaytype || -1;\n\t\t// 先获取系统设置，再决定是否获取位置\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiLuntan/fatie', {display_type:that.displaytype}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.clist = res.clist;\n\t\t\t\tthat.iscatephone = res.iscatephone\n\t\t\t\tvar clist = res.clist;\n\t\t\t\tif (clist.length > 0) {\n\t\t\t\t\tvar cateArr = [];\n\t\t\t\t\tfor (var i in clist) {\n\t\t\t\t\t\tif (that.opt && that.opt.cid == clist[i].id) {\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tthat.cindex = i;\n\t\t\t\t\t\t\tif(that.iscatephone){\n\t\t\t\t\t\t\t\t\tthat.isphone = clist[that.cindex].isphone;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(res.cate2){\n\t\t\t\t\t\t\t\t\tthat.getCate2(that.opt.cid);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcateArr.push(clist[i].name);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tcateArr = false;\n\t\t\t\t}\n\t\t\t\tthat.cateArr = cateArr\n\t\t\t\tif(res.need_call){\n\t\t\t\t\t\tthat.need_call = true;\n\t\t\t\t}\n\t\t\t\tif(res.cate2){\n\t\t\t\t\t\tthat.cate2    = true;\n\t\t\t\t}\n\t\t\t\t// 获取用户积分余额\n\t\t\t\tif (res.user_score !== undefined) {\n\t\t\t\t\tthat.userScoreBalance = res.user_score;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理位置功能开关\n\t\t\t\tif (res.location_enable !== undefined) {\n\t\t\t\t\tthat.locationEnabled = res.location_enable == 1;\n          // 根据开关状态决定是否获取位置信息\n          if (that.locationEnabled) {\n            that.getUserLocation();\n          }\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthat.$nextTick(function(){\n\t\t\t\t\tif (that.clist && that.clist.length > 0) {\n\t\t\t\t\t\tthat.cindex = 0;\n\t\t\t\t\t\tvar defaultCid = that.clist[0].id;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果启用了二级分类功能，检查是否有二级分类\n\t\t\t\t\t\tif(that.cate2){\n\t\t\t\t\t\t\t// 获取二级分类（会自动选择第一个二级分类）\n\t\t\t\t\t\t\tthat.getCate2(defaultCid);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 没有二级分类功能，直接获取一级分类的表单信息\n\t\t\t\t\t\t\tthat.getFormByCategory(defaultCid);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    cateChange: function (e) {\n        var that = this;\n        that.cindex = e.detail.value;\n        var clist = that.clist;\n        if (clist &&clist.length > 0) {\n            var cid = clist[that.cindex].id;\n            if(that.iscatephone){\n                that.isphone = clist[that.cindex].isphone;\n            }\n        } else {\n            var cid = 0;\n        }\n        if(that.cate2){\n            that.getCate2(cid);\n        }\n        that.getFormByCategory(cid);\n    },\n    cateChange2: function (e) {\n      this.cindex2 = e.detail.value;\n    },\n    formsubmit: function (e) {\n      var that = this;\n      var app = getApp(); // 确保app对象正确引用\n      \n      var clist = that.clist;\n      if (clist.length > 0) {\n        if (that.cindex == -1) {\n          app.error('请选择分类');\n          return false;\n        }\n        var cid = clist[that.cindex].id;\n        // 注意：这里获取的价格信息可能不准确，应该使用currentCategory中的\n        var post_price = clist[that.cindex].post_price || 0;\n        var post_score = clist[that.cindex].post_score || 0;\n      } else {\n        var cid = 0;\n        var post_price = 0;\n        var post_score = 0;\n      }\n      var cid2 =0;\n      if(that.cate2){\n          var clist2 = that.clist2;\n          if (clist2.length > 0) {\n            if (that.cindex2 == -1) {\n              app.error('请选择二级分类');\n              return false;\n            }\n            var cid2 = clist2[that.cindex2].id;\n          } else {\n            var cid2 = 0;\n          }\n      }\n      var formdata = e.detail.value;\n      var content = formdata.content;\n      var pics = formdata.pics;\n      var video = formdata.video;\n      var mobile = formdata.mobile;\n      var name = formdata.name;\n      if (content == '' && pics == '') {\n        app.error('请输入内容');\n        return false;\n      }\n      if(that.isphone){\n        if(!name){\n          app.error('请输入姓名');return false;\n        }\n        if(!mobile){\n          app.error('请输入手机号'); return false;\n        }\n        if (!app.isPhone(mobile)) {\n          app.alert('手机号格式错误');return;\n        }\n      }\n      // 校验自定义表单必填项\n      if(that.showFormFields && that.formfields && that.formfields.content){\n        for(var i=0;i<that.formfields.content.length;i++){\n          var item = that.formfields.content[i];\n          var val = that.formvaldata['form'+i];\n          if(item.val3==1 && (!val || (Array.isArray(val) && val.length==0))){\n            app.error(item.val1+'为必填项');\n            return false;\n          }\n        }\n      }\n      // 带上formdata\n      var postData = {\n        cid: cid,\n        cid2: cid2,\n        pics: pics,\n        content: content,\n        video: video,\n        mobile: mobile,\n        name: name\n      };\n      \n      // 添加位置信息\n      if (that.hasLocation && that.locationEnabled) {\n        postData.latitude = that.latitude;\n        postData.longitude = that.longitude;\n        postData.address = that.address;\n        postData.province = that.province;\n        postData.city = that.city;\n        postData.district = that.district;\n      }\n      \n      if(that.showFormFields && that.formfields){\n        postData.formdata = that.formvaldata;\n      }\n      \n      // 添加价格字段\n      if (that.currentCategory && that.currentCategory.price_enable == 1) {\n        postData.price = that.price || 0;\n        postData.show_price = that.showPrice ? 1 : 0;\n      }\n      \n      // 检查是否同时存在积分和付费发帖选项\n      if (that.currentCategory && that.currentCategory.post_price > 0 && that.currentCategory.post_score > 0) {\n        // 记录当前表单数据，供弹窗选择后使用\n        that.currentPostData = postData;\n        // 打开支付方式选择弹窗\n        that.$refs.paymentPopup.open();\n        return;\n      }\n      \n      // 如果只有付费发帖\n      if (that.currentCategory && that.currentCategory.post_price > 0 && that.currentCategory.post_score <= 0) {\n        that.submitPostWithMoney(postData);\n        return;\n      }\n      \n      // 如果只有积分发帖\n      if (that.currentCategory && that.currentCategory.post_score > 0 && that.currentCategory.post_price <= 0) {\n        that.submitPostWithScore(postData);\n        return;\n      }\n      \n      // 免费发帖\n      that.submitFreePost(postData);\n    },\n    \n    // 关闭支付方式选择弹窗\n    closePaymentPopup: function() {\n      this.$refs.paymentPopup.close();\n    },\n    \n    // 选择支付方式\n    choosePaymentMethod: function(method) {\n      var that = this;\n      var app = getApp(); // 确保app对象正确引用\n      \n      if (!that.currentPostData) {\n        app.error('表单数据错误');\n        return;\n      }\n      \n      // 关闭弹窗\n      that.closePaymentPopup();\n      \n      if (method === 'money') {\n        // 付费发帖\n        that.submitPostWithMoney(that.currentPostData);\n      } else if (method === 'score') {\n        // 积分发帖\n        if (that.userScoreBalance < that.currentCategory.post_score) {\n          app.error('积分不足，当前积分：' + that.userScoreBalance + '，需要：' + that.currentCategory.post_score);\n          return;\n        }\n        \n        // 确认扣除积分\n        app.confirm('确定使用'+that.currentCategory.post_score+'积分发布帖子吗？', function() {\n          that.submitPostWithScore(that.currentPostData);\n        });\n      }\n    },\n    \n    // 积分发帖\n    submitPostWithScore: function(postData) {\n      var that = this;\n      var app = getApp(); // 确保app对象正确引用\n      \n      // 设置使用积分标记\n      postData.use_score = 1;\n      \n      app.post('ApiLuntan/fatie', postData, function (res) {\n        app.showLoading(false);\n        if (res.status == 1) {\n          app.success(res.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        } else {\n          app.error(res.msg);\n        }\n      });\n    },\n    \n    // 付费发帖\n    submitPostWithMoney: function(postData) {\n      var that = this;\n      var app = getApp(); // 确保app对象正确引用\n      \n      // 确保不使用积分\n      postData.use_score = 0;\n      \n      app.post('ApiLuntan/fatie', postData, function (res) {\n        app.showLoading(false);\n        \n        if (res.status == 2 && res.payorderid) {\n          // 跳转支付中控台\n          var payUrl = '/pagesExt/pay/pay?id=' + res.payorderid + '&fromPage=luntan_fatie';\n          app.goto(payUrl);\n          return;\n        } else if (res.status == 1 && res.payorderid) {\n          // 兼容老逻辑\n          var payUrl = '/pagesExt/pay/pay?id=' + res.payorderid + '&fromPage=luntan_fatie';\n          app.goto(payUrl);\n          return;\n        } else {\n          app.error(res.msg || '发帖失败');\n        }\n      });\n    },\n    \n    // 免费发帖\n    submitFreePost: function(postData) {\n      var that = this;\n      var app = getApp(); // 确保app对象正确引用\n      \n      // 确保不使用积分\n      postData.use_score = 0;\n      \n      app.post('ApiLuntan/fatie', postData, function (res) {\n        app.showLoading(false);\n        if (res.status == 1) {\n          app.success(res.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        } else {\n          app.error(res.msg);\n        }\n      });\n    },\n\t\tuploadimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar app = getApp(); // 确保app对象正确引用\n\t\t\t\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tif(!pics) pics = [];\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tfor(var i=0;i<urls.length;i++){\n\t\t\t\t\tpics.push(urls[i]);\n\t\t\t\t}\n\t\t\t\tif(field == 'pic') that.pic = pics;\n\t\t\t\tif(field == 'pics') that.pics = pics;\n\t\t\t\tif(field == 'zhengming') that.zhengming = pics;\n\t\t\t},9)\n\t\t},\n    uploadvideo: function () {\n      var that = this;\n      uni.chooseVideo({\n        sourceType: ['album', 'camera'],\n        maxDuration: 60,\n        success: function (res) {\n          var tempFilePath = res.tempFilePath;\n          app.showLoading('上传中');\n          uni.uploadFile({\n            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\n            filePath: tempFilePath,\n            name: 'file',\n            success: function (res) {\n              app.showLoading(false);\n              var data = JSON.parse(res.data);\n\n              if (data.status == 1) {\n                that.video = data.url;\n              } else {\n                app.alert(data.msg);\n              }\n            },\n            fail: function (res) {\n              app.showLoading(false);\n              app.alert(res.errMsg);\n            }\n          });\n        },\n        fail: function (res) {\n          // 不阻止用户发帖，只是没有位置信息\n        }\n      });\n    },\n    removeVideo: function() {\n      this.video = '';\n      app.success('视频已移除');\n    },\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index= e.currentTarget.dataset.index\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tpics.splice(index,1)\n\t\t},\n        getCate2: function (pid) {\n        \tvar that = this;\n        \tvar app = getApp(); // 确保app对象正确引用\n        \t\n        \tapp.post('ApiLuntan/getCate2', {pid:pid}, function (res) {\n                if(res.status == 1){\n                    var clist2 = res.data;\n                    that.clist2 = clist2;\n                    if (clist2.length > 0) {\n                    \t// 有二级分类：自动选择第一个二级分类\n                    \tthat.cindex2 = 0;\n                    \t// 获取默认选择的二级分类的表单信息\n                    \tthat.getFormByCategory(clist2[0].id);\n                    } else {\n                    \t// 没有二级分类：重置二级分类索引，并获取一级分类的表单信息\n                    \tthat.cindex2 = -1;\n                    \tthat.getFormByCategory(pid);\n                    }\n                }else{\n                    that.clist2 = [];\n                    that.cindex2 = -1;\n                    // 接口调用失败时，也尝试获取一级分类的表单信息\n                    that.getFormByCategory(pid);\n                    app.alert(res.msg)\n                }\n        \t});\n        },\n        \n        // 新增：处理二级分类点击\n        onSecondaryCategoryTap: function(idx) {\n            this.cindex2 = idx;\n            if (this.clist2 && this.clist2[idx]) {\n                const cid = this.clist2[idx].id;\n                // 获取二级分类的表单信息\n                this.getFormByCategory(cid);\n            }\n        },\n        getFormByCategory: function(cid) {\n            var that = this;\n            var app = getApp(); // 确保app对象正确引用\n            \n            if (!cid) return;\n            app.post('ApiLuntan/getFormByCategory', {cid: cid}, function(res) {\n                if (res.status == 1 && res.formid && res.form && res.form.content) {\n                    that.formfields = res.form;\n                    that.showFormFields = true;\n                } else {\n                    that.formfields = null;\n                    that.showFormFields = false;\n                }\n            });\n            \n            // 获取分类详细信息，包括价格启用状态\n            app.post('ApiLuntan/getCategoryInfo', {cid: cid}, function(res) {\n                if (res.status == 1 && res.data) {\n                    that.currentCategory = res.data;\n                    // 重置价格相关字段\n                    if (that.currentCategory.price_enable != 1) {\n                        that.price = '';\n                        that.showPrice = false;\n                    }\n                } else {\n                    that.currentCategory = null;\n                }\n            });\n        },\n    setFormField: function(e, idx) {\n      var that = this;\n      var key = 'form'+idx;\n      // 判断类型\n      var item = that.formfields.content[idx];\n      if(item.key=='checkbox'){\n        that.formvaldata[key] = e.detail.value;\n      }else if(item.key=='radio' || item.key=='sex'){\n        that.formvaldata[key] = e.detail.value;\n      }else{\n        that.formvaldata[key] = e.detail.value;\n      }\n    },\n    onPickerChange(e, idx, type, range) {\n      let value = '';\n      if(type === 'selector') {\n        value = range[e.detail.value];\n      } else if(type === 'region') {\n        value = e.detail.value.join ? e.detail.value.join('-') : e.detail.value;\n      } else {\n        value = e.detail.value;\n      }\n      this.$set(this.formvaldata, 'form'+idx, value);\n    },\n    onCategoryTap(idx) {\n      this.cindex = idx;\n      const cid = this.clist[idx].id;\n      \n      // 重置二级分类选择\n      this.cindex2 = -1;\n      this.cateArr2 = [];\n      this.clist2 = [];\n      \n      // 如果启用了二级分类功能，获取二级分类\n      if(this.cate2){\n        this.getCate2(cid);\n      }\n      \n      // 获取表单信息\n      this.getFormByCategory(cid);\n    },\n    onShowPriceChange(e) {\n      this.showPrice = e.detail.value;\n    },\n    // 新增方法：获取用户位置信息\n    getUserLocation: function() {\n      var that = this;\n      var app = getApp(); // 确保app对象正确引用\n      \n      // 检查是否启用位置功能，如果未启用则不获取位置\n      if (!that.locationEnabled) {\n        return;\n      }\n      \n      // 调用app.getLocation获取用户位置\n      app.getLocation(function(res) {\n        // 成功获取位置信息\n        that.latitude = res.latitude;\n        that.longitude = res.longitude;\n        that.hasLocation = true;\n        \n        // 通过接口获取地址详情\n        app.post('ApiAddress/getAreaByLocation', {\n          latitude: that.latitude,\n          longitude: that.longitude\n        }, function(res) {\n          if (res.status == 1) {\n            that.address = res.address || '';\n            that.province = res.province || '';\n            that.city = res.city || '';\n            that.district = res.district || '';\n          }\n        });\n      }, function(err) {\n        // 获取位置失败的回调\n        // 不阻止用户发帖，只是没有位置信息\n      });\n    },\n  },\n  onShow: function () {\n    var that = this;\n    var app = getApp(); // 确保app对象正确引用\n    \n    // 假设支付中控台支付成功后会带回 payorderid\n    var pages = getCurrentPages();\n    var currentPage = pages[pages.length - 1];\n    var payorderid = '';\n    if (currentPage.options && currentPage.options.payorderid) {\n      payorderid = currentPage.options.payorderid;\n    }\n    if (payorderid) {\n      // 调用支付回调接口，发布帖子\n      app.post('ApiLuntan/fatiePayCallback', { order_id: payorderid }, function (res) {\n        if (res.status == 1) {\n          app.success(res.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        } else {\n          app.error(res.msg);\n        }\n      });\n    }\n  },\n};\n</script>\n<style>\npage{\n\tbackground-color: #f7f8fa;\n\tfont-family: PingFang SC, Helvetica Neue, Helvetica, sans-serif;\n}\n\n.container {\n\tpadding-bottom: 120rpx;\n}\n\n.page-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 24rpx;\n\tbackground-color: #ffffff;\n\tposition: sticky;\n\ttop: 0;\n\tz-index: 100;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.back-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.back-icon {\n\twidth: 18rpx;\n\theight: 32rpx;\n}\n\n.page-title {\n\tflex: 1;\n\ttext-align: center;\n\tfont-size: 34rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.header-right {\n\twidth: 60rpx;\n}\n\n.card-section {\n\tmargin: 20rpx 24rpx;\n\tborder-radius: 16rpx;\n\tbackground-color: #ffffff;\n\tpadding: 24rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0,0,0,0.03);\n}\n\n.section-title {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding-bottom: 20rpx;\n\tmargin-bottom: 16rpx;\n\tborder-bottom: 1rpx solid #f5f5f5;\n\tcolor: #333;\n\tfont-size: 30rpx;\n\tfont-weight: 500;\n}\n\n.subtitle {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tfont-weight: normal;\n\tmargin-left: 10rpx;\n}\n\n.voice-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 8rpx 16rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 30rpx;\n}\n\n.voice-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tmargin-right: 8rpx;\n}\n\n.voice-text {\n\tfont-size: 26rpx;\n\tcolor: #ff5a5f;\n}\n\n.category-scroll {\n\twidth: 100%;\n\twhite-space: nowrap;\n}\n\n.category-flex {\n\tdisplay: inline-flex;\n\tpadding: 10rpx 0;\n}\n\n.category-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 16rpx 24rpx;\n\tmargin-right: 20rpx;\n\tborder-radius: 12rpx;\n\tbackground-color: #f8f9fa;\n\ttransition: all 0.3s;\n\tmin-width: 140rpx;\n}\n\n.category-btn.active {\n\tbackground-color: rgba(59,124,255,0.1);\n\tborder: 2rpx solid #3B7CFF;\n}\n\n.category-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tmargin-bottom: 10rpx;\n\tborder-radius: 50%;\n}\n\n.category-name {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tmax-width: 120rpx;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.content-area {\n\twidth: 100%;\n}\n\n.form-textarea {\n\twidth: 100%;\n\tmin-height: 240rpx;\n\tfont-size: 30rpx;\n\tpadding: 20rpx;\n\tborder-radius: 12rpx;\n\tbackground-color: #f8f9fa;\n\tborder: none;\n\tmargin-top: 10rpx;\n}\n\n.price-input-wrapper {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.price-switch-container {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n}\n\n.price-switch-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.price-input-container {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 0 20rpx;\n\theight: 88rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n}\n\n.price-symbol {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tmargin-right: 10rpx;\n}\n\n.price-input {\n\tflex: 1;\n\theight: 88rpx;\n\tfont-size: 32rpx;\n\tborder: none;\n\tbackground: transparent;\n}\n\n.price-tip {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-top: 10rpx;\n}\n\n.media-upload-area {\n\twidth: 100%;\n}\n\n.upload-title {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin: 20rpx 0 16rpx;\n}\n\n.upload-content {\n\twidth: 100%;\n}\n\n.image-preview-box {\n\tposition: relative;\n\twidth: 200rpx;\n\theight: 200rpx;\n\tmargin-right: 20rpx;\n\tmargin-bottom: 20rpx;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n}\n\n.preview-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.delete-icon {\n\tposition: absolute;\n\tright: 10rpx;\n\ttop: 10rpx;\n\twidth: 36rpx;\n\theight: 36rpx;\n\tbackground-color: rgba(0,0,0,0.5);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 2;\n}\n\n.delete-icon image {\n\twidth: 20rpx;\n\theight: 20rpx;\n}\n\n.upload-btn {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #f8f9fa;\n\tborder: 1rpx dashed #ddd;\n\tborder-radius: 8rpx;\n\tmargin-right: 20rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.upload-icon {\n\tfont-size: 60rpx;\n\tcolor: #ccc;\n\tline-height: 60rpx;\n\tfont-weight: 300;\n}\n\n.upload-text {\n\tfont-size: 26rpx;\n\tcolor: #999;\n\tmargin-top: 10rpx;\n}\n\n.video-upload-area {\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 100%;\n}\n\n.video-preview {\n\tposition: relative;\n\twidth: 300rpx;\n\theight: 200rpx;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n}\n\n.preview-video {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.video-upload-btn {\n\twidth: 300rpx;\n\theight: 200rpx;\n}\n\n.form-item {\n\tmargin-bottom: 30rpx;\n}\n\n.item-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tmargin-bottom: 12rpx;\n}\n\n.required-mark {\n\tcolor: #ff4d4f;\n\tmargin-left: 6rpx;\n}\n\n.form-input {\n\twidth: 100%;\n\theight: 88rpx;\n\tfont-size: 28rpx;\n\tpadding: 0 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n\tborder: none;\n}\n\n.form-textarea-field {\n\twidth: 100%;\n\tmin-height: 160rpx;\n\tfont-size: 28rpx;\n\tpadding: 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n\tborder: none;\n}\n\n.option-group {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 20rpx;\n\tmargin-top: 10rpx;\n}\n\n.radio-label, .checkbox-label {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-right: 30rpx;\n}\n\n.option-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-left: 8rpx;\n}\n\n.selector-container {\n\twidth: 100%;\n\tposition: relative;\n}\n\n.picker-wrapper {\n\twidth: 100%;\n}\n\n.picker {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\twidth: 100%;\n\theight: 88rpx;\n\tfont-size: 28rpx;\n\tpadding: 0 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n\tcolor: #333;\n}\n\n.picker-arrow {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.arrow-icon {\n\twidth: 24rpx;\n\theight: 24rpx;\n}\n\n.submit-area {\n\tmargin: 40rpx 24rpx;\n}\n\n.submit-btn {\n\twidth: 100%;\n\theight: 90rpx;\n\tline-height: 90rpx;\n\tbackground: linear-gradient(135deg, #3B7CFF 0%, #6AA1FF 100%);\n\tcolor: #fff;\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tborder-radius: 45rpx;\n\tbox-shadow: 0 8rpx 16rpx rgba(59,124,255,0.2);\n\tborder: none;\n}\n\n.history-link {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-bottom: 40rpx;\n}\n\n.arrow-right {\n\twidth: 24rpx;\n\theight: 24rpx;\n\tmargin-left: 8rpx;\n}\n\n.score-input-wrapper {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.score-switch-container {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n}\n\n.score-switch-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.score-tips {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-top: 10rpx;\n}\n\n.score-amount {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tmargin-bottom: 10rpx;\n}\n\n.score-balance {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tfont-weight: normal;\n}\n\n.score-warning {\n\tfont-size: 24rpx;\n\tcolor: #ff4d4f;\n\tfont-weight: normal;\n\tmargin-top: 10rpx;\n}\n\n/* 支付方式选择弹窗样式 */\n.payment-popup {\n\twidth: 600rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tpadding: 0;\n}\n\n.payment-popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\ttext-align: center;\n\tpadding: 30rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.payment-popup-user-score {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\ttext-align: center;\n\tpadding: 20rpx 0;\n}\n\n.payment-popup-content {\n\tpadding: 20rpx 30rpx;\n}\n\n.payment-option {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.payment-option:last-child {\n\tborder-bottom: none;\n}\n\n.payment-option.disabled {\n\topacity: 0.6;\n}\n\n.payment-option-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.payment-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tmargin-right: 20rpx;\n}\n\n.payment-option-info {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.payment-name {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n}\n\n.payment-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-top: 6rpx;\n}\n\n.payment-warning {\n\tfont-size: 24rpx;\n\tcolor: #ff4d4f;\n\tmargin-top: 6rpx;\n}\n\n.payment-option-right {\n\tmargin-left: 20rpx;\n}\n\n.select-indicator {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder-radius: 50%;\n\tborder: 1rpx solid #ddd;\n}\n\n.payment-popup-buttons {\n\tdisplay: flex;\n\tborder-top: 1rpx solid #f0f0f0;\n}\n\n.cancel-btn {\n\tflex: 1;\n\theight: 90rpx;\n\tline-height: 90rpx;\n\ttext-align: center;\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tbackground: #f5f5f5;\n\tborder: none;\n\tborder-radius: 0;\n}\n\n/* 二级分类样式 */\n.section-subtitle {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-bottom: 20rpx;\n\tfont-weight: 500;\n}\n\n.category-picker {\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n\tborder: 2rpx solid #e9ecef;\n}\n\n.picker-display {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx 24rpx;\n\tmin-height: 80rpx;\n}\n\n.picker-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tflex: 1;\n}\n\n.picker-arrow-icon {\n\twidth: 24rpx;\n\theight: 24rpx;\n\tmargin-left: 20rpx;\n\topacity: 0.6;\n}\n\n.payment-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\twidth: 600rpx;\n\tpadding: 40rpx;\n}\n\n.payment-popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\ttext-align: center;\n\tmargin-bottom: 20rpx;\n\tcolor: #333;\n}\n\n.payment-popup-user-score {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\ttext-align: center;\n\tmargin-bottom: 30rpx;\n}\n\n.payment-popup-content {\n\tmargin-bottom: 30rpx;\n}\n\n.payment-option {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tborder: 2rpx solid #e9ecef;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 20rpx;\n\tbackground-color: #fff;\n}\n\n.payment-option.disabled {\n\topacity: 0.5;\n\tbackground-color: #f8f9fa;\n}\n\n.payment-option-left {\n\tflex: 1;\n}\n\n.payment-option-info {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.payment-name {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n}\n\n.payment-desc {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.payment-warning {\n\tfont-size: 22rpx;\n\tcolor: #ff4d4f;\n\tmargin-top: 4rpx;\n}\n\n.payment-option-right {\n\tmargin-left: 20rpx;\n}\n\n.payment-popup-buttons {\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.cancel-btn {\n\tbackground-color: #f5f5f5;\n\tcolor: #666;\n\tborder: none;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx 40rpx;\n\tfont-size: 28rpx;\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatie.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatie.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464344\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}