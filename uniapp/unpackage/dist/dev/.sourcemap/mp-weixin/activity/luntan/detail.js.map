{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/detail.vue?c00f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/detail.vue?5580", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/detail.vue?13ca", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/detail.vue?75f2", "uni-app:///activity/luntan/detail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/detail.vue?2590", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/detail.vue?517a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "pre_url", "isload", "menuindex", "detail", "datalist", "pagenum", "id", "nomore", "iszan", "title", "sharepic", "mid", "need_call", "formData", "report_enable", "showReportDialog", "reportReason", "reportContact", "onLoad", "prevPage", "onShareAppMessage", "pic", "onShareTimeline", "console", "imageUrl", "query", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "getFormData", "zan", "pzan", "showpinglun", "<PERSON><PERSON>", "setTimeout", "del<PERSON><PERSON>n", "delplreply", "callphone", "uni", "phoneNumber", "fail", "handleReport", "closeReportDialog", "submitReport", "reason", "contact", "luntanid"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8Hx1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;MACAC;IACA;IACA;EACA;EACAC;IACA;MAAAX;MAAAY;IAAA;EACA;EACAC;IACA;MAAAb;MAAAY;IAAA;IACA;IACAE;IACAA;IACA;MACAd;MACAe;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAA1B;QAAAC;MAAA;QACAwB;QACA;UACAA;QACA;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;YAAArB;YAAAY;UAAA;UACAS;UACA;UACA;YACA;cAAA;YAAA;YACA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACAD;QAAAzB;MAAA;QACA;UACAwB;QACA;UACAA;QACA;MACA;IACA;IACAG;MACA;MACA;MACAF;QAAAzB;MAAA;QACA;UACA;UACA;QACA;UACA;QACA;QACAwB;QACAA;MACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACAH;QAAAzB;MAAA;QACA;UACA;UACA;QACA;UACA;QACA;QACAF;QACAA;QACA0B;MACA;IACA;IACAK;IACAC;MACA;MACA;MACAL;QACAA;UAAAzB;QAAA;UACAyB;UACAM;YACAN;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACAP;QACAA;UAAAzB;QAAA;UACAyB;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACAR;QACAA;UAAAzB;QAAA;UACAyB;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAU;MACA;MACAC;QACAC;QACAC,uBACA;MACA;IACA;IACAC;MACArB;MACA;IACA;IACAsB;MACAtB;MACA;MACA;MACA;IACA;IACAuB;MACA;MACAvB;QACAwB;QACAC;MACA;MAEA;QACAjB;QACA;MACA;MAEAA;QACAkB;QACAF;QACAC;MACA;QACAzB;QACA;UACAQ;UACAD;QACA;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpWA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/luntan/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/luntan/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=ad0a0b5c&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/luntan/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=ad0a0b5c&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload\n    ? _vm.formData && _vm.formData.fields && _vm.formData.fields.length\n    : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, idx) {\n        var $orig = _vm.__get_orig(item)\n        var g1 = item.replylist.length\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t<view class=\"datalist\">\n\t\t<view class=\"item\">\n\t\t\t<view class=\"top\">\n\t\t\t\t<image :src=\"detail.headimg\" class=\"f1\"></image>\n\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t<view class=\"covermy-view flex-col\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t\t<view class=\"covermy\" @tap=\"goto\" data-url=\"pages/index/index\"><image :src=\"pre_url+'/static/img/lt_gohome.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"covermy\" @tap=\"goto\" data-url=\"fatie\"><image :src=\"pre_url+'/static/img/lt_fatie2.png'\"></image></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"t1\">\n\t\t\t\t\t\t{{detail.nickname}}\n\t\t\t\t\t\t<text v-if=\"detail.is_top == 1\" class=\"top-tag\">置顶</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"t2\">{{detail.showtime}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"con previewImgContent\">\n\t\t\t\t<view class=\"f1\" v-if=\"detail.luntan_content_ueditor\"><rich-text :nodes=\"detail.content\" style=\"word-wrap: break-word;\"></rich-text></view>\n        <view class=\"f1\" v-else><text style=\"white-space:pre-wrap;\">{{detail.content}}</text></view>\n        <view class=\"f2\" v-if=\"detail.pics\">\n\t\t\t\t\t<image v-for=\"(pic, idx) in detail.pics\" :key=\"idx\" :src=\"pic\" @tap=\"previewImage\" :data-url=\"pic\" :data-urls=\"detail.pics\"></image>\n\t\t\t\t</view>\n\t\t\t\t<video class=\"video\" id=\"video\" :src=\"detail.video\" v-if=\"detail.video\"></video>\n\t\t\t\t\n\t\t\t\t<!-- 显示价格 -->\n\t\t\t\t<view class=\"price-tag\" v-if=\"detail.display_price\">\n\t\t\t\t\t<text class=\"price-value\" v-if=\"detail.is_negotiable\">议价</text>\n\t\t\t\t\t<text class=\"price-value\" v-else>¥{{detail.price}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"phone\" v-if=\"detail.isshowphone\">\n\t\t\t\t<view class=\"f1\" v-if=\"detail.name\"><label class=\"t1\">姓名：</label>{{detail.name}}</view>\n\t\t\t\t<view class=\"f1\" v-if=\"detail.mobile\"><label class=\"t1\">手机号：</label>{{detail.mobile}}</view>\n\t\t\t</view>\n\t\t\t\n\t\t</view>\n\t\t<!-- 表单内容卡片 -->\n\t\t<view v-if=\"formData && formData.fields && formData.fields.length\" class=\"form-card\">\n\t\t\t<view class=\"form-card-title\">{{formData.form_name}}</view>\n\t\t\t<view v-if=\"detail.cname\" class=\"form-card-cate\">分类：{{detail.cname}}</view>\n\t\t\t<view class=\"form-card-item\" v-for=\"(item, idx) in formData.fields\" :key=\"idx\">\n\t\t\t\t<view class=\"form-card-label\">{{item.label}}</view>\n\t\t\t\t<view class=\"form-card-value\">{{item.value}}</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"flex\">\n\t\t<!-- \t<view style=\"color:#507DAF;font-size:28rpx;font-weight:800;height:60rpx;line-height:60rpx;margin-right:20rpx\">点击右下角去曝光</view> -->\n\t\t\t<view v-if=\"detail.mid == mid\" style=\"color:#507DAF;font-size:28rpx;height:60rpx;line-height:60rpx;margin-right:20rpx\" @tap=\"deltie\" :data-id=\"detail.id\">删除</view>\n\t\t\t<view style=\"color:#aaa;font-size:28rpx;height:60rpx;line-height:60rpx;margin-right:20rpx\">阅读 {{detail.readcount}}</view>\n            <view class=\"f1\" v-if=\"need_call && detail.mobile\" @tap.stop=\"callphone\" :data-phone=\"detail.mobile\" style=\"margin-left:60rpx;height: 30px;line-height: 30px;overflow: hidden;\">\n                <image :src=\"pre_url+'/static/img/mobile.png'\" style=\"width: 30rpx;height: 30rpx;float: left;margin-top: 12rpx;\"></image>\n                <text style=\"margin-left: 10rpx;\">拨打电话</text>\n            </view>\n\t\t</view>\n\t</view>\n\n\t<!--评论-->\n\t<view class=\"plbox\">\n\t\t<view class=\"plbox_title\"><text class=\"t1\">评论</text><text>{{plcount}}</text></view>\n\t\t<view class=\"plbox_content\" id=\"datalist\">\n\t\t\t<block v-for=\"(item, idx) in datalist\" :key=\"idx\">\n\t\t\t<view class=\"item1 flex\">\n\t\t\t\t<view class=\"f1 flex0\"><image :src=\"item.headimg\"></image></view>\n\t\t\t\t<view class=\"f2 flex-col\">\n\t\t\t\t\t<text class=\"t1\">{{item.nickname}}</text>\n\t\t\t\t\t<text class=\"t11\">{{item.createtime}}</text>\n\t\t\t\t\t<view class=\"t2 plcontent\"><parse :content=\"item.content\" /></view>\n\t\t\t\t\t<block v-if=\"item.replylist.length>0\">\n\t\t\t\t\t<view class=\"relist\">\n\t\t\t\t\t\t<block v-for=\"(hfitem, index) in item.replylist\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"item2\">\n\t\t\t\t\t\t\t<view class=\"f1\">{{hfitem.nickname}}<text class=\"t1\">{{hfitem.createtime}}</text>\n\t\t\t\t\t\t\t\t<text v-if=\"hfitem.mid==mid\" class=\"phuifu\" style=\"font-size:20rpx;margin-left:20rpx;font-weight:normal\" @tap=\"delplreply\" :data-id=\"hfitem.id\">删除</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f2 plcontent\"><parse :content=\"hfitem.content\" /></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t </block>\n\t\t\t\t\t</view>\n          </block>\n\t\t\t\t\t<view class=\"t3 flex\">\n\t\t\t\t\t\t<view class=\"flex1\">\n\t\t\t\t\t\t\t<text class=\"phuifu\" style=\"cursor:pointer\" @tap=\"goto\" :data-url=\"'pinglun?type=1&id=' + detail.id + '&hfid=' + item.id\">回复</text>\n\t\t\t\t\t\t\t<text v-if=\"item.mid==mid\" class=\"phuifu\" style=\"cursor:pointer;margin-left:20rpx\" @tap=\"delpinglun\" :data-id=\"item.id\">删除</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center pzan\" @tap=\"pzan\" :data-id=\"item.id\" :data-index=\"idx\"><image :src=\"pre_url+'/static/img/lt_like' + (item.iszan==1?'2':'') + '.png'\"></image>{{item.zan}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n      </block>\n\t\t</view>\n\t</view>\n\t<view style=\"height:100rpx\"></view>\n\t<view class=\"pinglun\">\n\t\t<view class=\"pinput\" @tap=\"goto\" :data-url=\"'pinglun?type=0&id=' + detail.id\">发表评论</view>\n\t\t<view class=\"zan flex-y-center\" @tap=\"zan\" :data-id=\"detail.id\">\n\t\t\t<image :src=\"pre_url+'/static/img/lt_like' + (iszan?'2':'') + '.png'\"></image><text style=\"padding-left:2px\">{{detail.zan}}</text>\n\t\t</view>\n\t\t<view v-if=\"report_enable==1\" class=\"report-btn\" @tap.stop=\"handleReport\">举报</view>\n\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<uni-popup ref=\"reportPopup\" type=\"center\">\n\t\t<view class=\"report-dialog\">\n\t\t\t<view class=\"report-title\">举报帖子</view>\n\t\t\t<view class=\"report-field\">\n\t\t\t\t<input v-model=\"reportReason\" placeholder=\"请输入举报理由\" maxlength=\"100\" placeholder-style=\"color:#bbb;font-size:28rpx;\" />\n\t\t\t</view>\n\t\t\t<view class=\"report-field\">\n\t\t\t\t<input v-model=\"reportContact\" placeholder=\"联系方式（选填）\" maxlength=\"50\" placeholder-style=\"color:#bbb;font-size:28rpx;\" />\n\t\t\t</view>\n\t\t\t<view class=\"report-actions\">\n\t\t\t\t<button @tap=\"submitReport\" class=\"report-submit\">提交</button>\n\t\t\t\t<button @tap=\"closeReportDialog\" class=\"report-cancel\">取消</button>\n\t\t\t</view>\n\t\t</view>\n\t</uni-popup>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n\t\t\tpre_url:app.globalData.pre_url,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tdetail:{},\n      datalist: [],\n      pagenum: 1,\n      id: 0,\n      nomore: false,\n      iszan: \"\",\n\t\t\ttitle: \"\",\n\t\t\tsharepic: \"\",\n\t\t\tmid:'',\n            need_call:false,\n            formData: null, // 帖子的表单内容\n            report_enable: 0,\n            showReportDialog: false,\n            reportReason: '',\n            reportContact: '',\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.id = this.opt.id || 0;\n\t\tvar pages = getCurrentPages();\n\t\tvar prevPage = pages[pages.length - 2];\n\t\tif(prevPage && prevPage.route=='activity/luntan/index'){\n\t\t\tprevPage.$vm.is_back = true;\n\t\t}\n\t\tthis.getdata();\n  },\n\tonShareAppMessage:function(){\n\t\treturn this._sharewx({title:this.detail.content,pic:this.detail.pics[0]});\n\t},\n\tonShareTimeline:function(){\n\t\tvar sharewxdata = this._sharewx({title:this.detail.content,pic:this.detail.pics[0]});\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\n\t\tconsole.log(sharewxdata)\n\t\tconsole.log(query)\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      app.post('ApiLuntan/detail', {pagenum: pagenum,id: that.id}, function (res) {\n\t\tthat.loading = false;\n        if(res.need_call){\n            that.need_call = true;\n        }\n        var data = res.datalist;\n        if (pagenum == 1) {\n            that.mid = res.mid;\n            that.datalist = res.datalist;\n            that.plcount = res.plcount;\n            that.iszan = res.iszan;\n            that.detail = res.detail;\n          that.myscore = res.myscore;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded({title:res.detail.content,pic:res.detail.pics[0]});\n          that.getFormData(); // 加载表单内容\n          // 分类名称赋值\n          if (!that.detail.cname && that.detail.cid && res.sysset && res.sysset.clist) {\n            var cate = res.sysset.clist.find(c => c.id == that.detail.cid);\n            if (cate) that.detail.cname = cate.name;\n          }\n          that.report_enable = res.report_enable || 0;\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    getFormData: function() {\n      var that = this;\n      app.get('ApiLuntan/getFormData', {id: that.id}, function(res) {\n        if(res.status === 1) {\n          that.formData = res;\n        } else {\n          that.formData = null;\n        }\n      });\n    },\n    zan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.post(\"ApiLuntan/zan\", {id: id}, function (res) {\n        if (res.type == 0) {\n          //取消点赞\n          var iszan = 0;\n        } else {\n          var iszan = 1;\n        }\n        that.iszan = iszan;\n\t\t\t\tthat.detail.zan = res.zancount;\n      });\n    },\n    pzan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var index = e.currentTarget.dataset.index;\n      var datalist = that.datalist;\n      app.post(\"ApiLuntan/pzan\", {id: id}, function (res) {\n        if (res.type == 0) {\n          //取消点赞\n          var iszan = 0;\n        } else {\n          var iszan = 1;\n        }\n        datalist[index].iszan = iszan;\n        datalist[index].zan = res.zancount;\n        that.datalist = datalist;\n      });\n    },\n    showpinglun: function () {},\n    deltie: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.confirm('确定要删除吗?', function () {\n        app.post(\"ApiLuntan/deltie\", {id: id}, function (res) {\n          app.success(res.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n      });\n    },\n    delpinglun: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.confirm('确定要删除吗?', function () {\n        app.post(\"ApiLuntan/delpinglun\", {id: id}, function (res) {\n          app.success(res.msg);\n          setTimeout(function () {\n            that.onLoad();\n          }, 1000);\n        });\n      });\n    },\n    delplreply: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.confirm('确定要删除吗?', function () {\n        app.post(\"ApiLuntan/delplreply\", {id: id}, function (res) {\n          app.success(res.msg);\n          setTimeout(function () {\n            that.onLoad();\n          }, 1000);\n        });\n      });\n    },\n    callphone:function(e) {\n    \tvar phone = e.currentTarget.dataset.phone;\n    \tuni.makePhoneCall({\n    \t\tphoneNumber: phone,\n    \t\tfail: function () {\n    \t\t}\n    \t});\n    },\n    handleReport: function() {\n      console.log('举报按钮被点击');\n      this.$refs.reportPopup.open();\n    },\n    closeReportDialog: function() {\n      console.log('关闭举报弹窗');\n      this.$refs.reportPopup.close();\n      this.reportReason = '';\n      this.reportContact = '';\n    },\n    submitReport: function() {\n      var that = this;\n      console.log('提交举报:', {\n        reason: that.reportReason,\n        contact: that.reportContact\n      });\n      \n      if (!that.reportReason) {\n        app.error('请填写举报理由');\n        return;\n      }\n      \n      app.post('ApiLuntan/reportLuntan', {\n        luntanid: that.id,\n        reason: that.reportReason,\n        contact: that.reportContact\n      }, function(res) {\n        console.log('举报提交结果:', res);\n        if(res.status==1){\n          app.success(res.msg);\n          that.closeReportDialog();\n        }else{\n          app.error(res.msg);\n    \t\t}\n    \t});\n    },\n  }\n};\n</script>\n<style>\n.datalist{width:100%;padding:0 24rpx;background:#fff}\n.datalist .item{width:100%;display:flex;flex-direction:column;padding:24rpx 0;}\n.datalist .item .top{width:100%;display:flex;align-items:center}\n.datalist .item .top .f1{width:80rpx;height:80rpx;border-radius:50%;margin-right:16rpx}\n.datalist .item .top .f2 .t1{color:#222;font-weight:bold;font-size:28rpx}\n.datalist .item .top .f2 .t2{color:#bbb;font-size:24rpx}\n.datalist .item .con{width:100%;padding:24rpx 0;display:flex;flex-direction:column;color:#000}\n.datalist .item .con .f2{margin-top:10rpx;display:flex;flex-wrap:wrap}\n.datalist .item .con .f2 image{width:200rpx;height:200rpx;margin-right:2%;margin-bottom:10rpx;border-radius:8rpx}\n.datalist .item .con .video{width:80%;height:300rpx;margin-top:20rpx}\n\n.pinglun{width:100%;max-width:750px;margin:0 auto;position:fixed;display:flex;align-items:center;bottom:0;left:0;right:0;height:100rpx;background:#fff;z-index:995;border-top:1px solid #f5f5f5;padding:0 20rpx;}\n.pinglun .pinput{flex:1;color:#a5adb5;font-size:32rpx;padding:0;line-height:100rpx}\n.pinglun .zan{padding:0 12rpx;line-height:100rpx;font-size:32rpx}\n.pinglun .zan image{width:48rpx;height:48rpx;margin-right:16rpx}\n.pinglun .buybtn{margin-left:16rpx;background:#31C88E;height:72rpx;line-height:72rpx;padding:0 20rpx;color:#fff;border-radius:6rpx}\n\n.plbox{width:100%;padding:20rpx 20rpx;background:#fff;margin-top:10px}\n.plbox_title{font-size:28rpx;height:60rpx;line-height:60rpx;margin-bottom:20rpx;color:#222;font-weight:bold}\n.plbox_title .t1{margin-right:16rpx}\n.plbox_content .plcontent{vertical-align: middle;color:#111}\n.plbox_content .plcontent image{ width:44rpx;height:44rpx;vertical-align: inherit;}\n.plbox_content .item1{width:100%;margin-bottom:20rpx}\n.plbox_content .item1 .f1{width:80rpx;}\n.plbox_content .item1 .f1 image{width:60rpx;height:60rpx;border-radius:50%}\n.plbox_content .item1 .f2{flex:1}\n.plbox_content .item1 .f2 .t1{color:#222;font-weight:bold}\n.plbox_content .item1 .f2 .t11{color:#999999;font-size:20rpx}\n.plbox_content .item1 .f2 .t2{color:#000;margin:10rpx 0;line-height:60rpx;}\n.plbox_content .item1 .f2 .t3{color:#999;font-size:20rpx}\n.plbox_content .item1 .f2 .pzan image{width:32rpx;height:32rpx;margin-right:16rpx}\n.plbox_content .item1 .f2 .phuifu{color:#507DAF;font-size:24rpx}\n.plbox_content .relist{width:100%;background:#F6F5F8;padding:4rpx 20rpx;margin-bottom:20rpx}\n.plbox_content .relist .item2{font-size:24rpx;margin-bottom:10rpx}\n.plbox_content .relist .item2 .f1{font-weight:bold;color:#222;width:100%}\n.plbox_content .relist .item2 .f1 .t1{font-weight:normal;color:#999999;font-size:20rpx;padding-left:20rpx}\n.covermy-view{position:fixed;z-index:99999;bottom:0;right:20rpx;width:126rpx;height: 250rpx;box-sizing:content-box;justify-content: space-between;margin-bottom: 140rpx;}\n.covermy{width:126rpx;height:126rpx;box-sizing:content-box;}\n.covermy image{width:100%;height:100%}\n\n.phone .f1{line-height: 60rpx;display: flex;}\n.phone .f1 label{ color: #999; width: 120rpx;}\n\n.form-card {\n  background: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 24rpx 0 rgba(22,88,198,0.06);\n  margin: 32rpx 16rpx 0 16rpx;\n  padding: 28rpx 24rpx;\n}\n.form-card-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #1658c6;\n  margin-bottom: 18rpx;\n  letter-spacing: 2rpx;\n}\n.form-card-item {\n  display: block;\n  margin-bottom: 18rpx;\n}\n.form-card-label {\n  color: #333;\n  font-size: 28rpx;\n  font-weight: 500;\n  text-align: left;\n  margin-bottom: 4rpx;\n  width: auto;\n  display: block;\n}\n.form-card-value {\n  color: #666;\n  font-size: 28rpx;\n  word-break: break-all;\n  text-align: left;\n  display: block;\n}\n.form-card-cate {\n  font-size: 26rpx;\n  color: #888;\n  margin-bottom: 10rpx;\n  margin-left: 2rpx;\n}\n.report-btn{color:#e74c3c;font-size:28rpx;margin-left:20rpx;}\n.report-dialog{background:#fff;padding:40rpx 30rpx;border-radius:16rpx;width:500rpx;box-shadow:0 8rpx 32rpx 0 rgba(0,0,0,0.12);}\n.report-title{font-size:32rpx;font-weight:bold;color:#e74c3c;margin-bottom:24rpx;text-align:center;}\n.report-field{margin-bottom:20rpx;}\n.report-field input{\n  width:100%;\n  border:1px solid #eee;\n  border-radius:8rpx;\n  padding:8rpx 16rpx;\n  font-size:28rpx;\n  height:48rpx;\n  line-height:48rpx;\n  box-sizing:border-box;\n}\n.report-actions{display:flex;justify-content:space-between;}\n.report-submit{background:#e74c3c;color:#fff;border:none;border-radius:8rpx;padding:12rpx 32rpx;}\n.report-cancel{background:#eee;color:#333;border:none;border-radius:8rpx;padding:12rpx 32rpx;}\n.top-tag {\n  display: inline-block;\n  background-color: #ff6b6b;\n  color: #fff;\n  font-size: 20rpx;\n  padding: 2rpx 10rpx;\n  border-radius: 8rpx;\n  margin-left: 10rpx;\n  font-weight: normal;\n  vertical-align: middle;\n}\n\n/* 价格标签样式 */\n.price-tag {\n  display: inline-block;\n  background-color: #f8f8f8;\n  border: 1px solid #e74c3c;\n  border-radius: 8rpx;\n  margin-top: 16rpx;\n  padding: 6rpx 16rpx;\n  align-self: flex-start;\n}\n.price-value {\n  color: #e74c3c;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464325\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}