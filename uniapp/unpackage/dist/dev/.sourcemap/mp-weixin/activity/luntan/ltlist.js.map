{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/ltlist.vue?b389", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/ltlist.vue?5e1e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/ltlist.vue?e78c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/ltlist.vue?dbad", "uni-app:///activity/luntan/ltlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/ltlist.vue?9f4f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/ltlist.vue?dd56"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "sysset", "cid", "datalist", "pagenum", "keyword", "nomore", "nodata", "banner", "clist", "selectedCategoryId", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "postData", "that", "app", "uni", "title", "searchConfirm", "zan", "id", "savecontent", "fuzhi", "success", "setTimeout", "fail", "savpic", "savpic2", "url", "filePath", "savevideo", "topPost", "luntanid", "playvideo", "selectCategory", "previewImage", "current", "urls", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Hx1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;QAAAb;QAAAE;QAAAC;MAAA;;MAEA;MACA;QACAW;MACA;MAEAC;MACAA;MACAA;MACAC;QACAD;QACAA;QACA;QACA;UACAE;YACAC;UACA;UACAH;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;MACAJ;MACAA;IACA;IACAK;MACA;MACA;MACA;MACA;MACAJ;QAAAK;MAAA;QACA;UACA;UACApB;UACAA;QACA;UACAA;UACAA;QACA;QACAc;MACA;IACA;IACAO;MACA;MACA;MACA;MACAP;QACAA;UACAA;QACA;MACA;IACA;IACAQ;MACA;QACA;QACA;MACA;MACA;MACAN;QACAxB;QACA+B;UACAR;UACAS;YACA;UACA;QACA;QACAC;UACAV;UACAS;YACA;UACA;QACA;MACA;IACA;IACAE;MACA;QACA;QACA;MACA;MACA;QACAX;QAAA;MACA;MACA;MACA;MACA;IACA;IACAY;MACA;MACA;MACA;QACAZ;QACAA;QACA;MACA;MACA;MACAA;MACAC;QACAY;QACAL;UACA;YACAP;cACAa;cACAN;gBACAT;gBACAA;cACA;cACAW;gBACAV;gBACAA;cACA;YACA;UACA;QACA;QACAU;UACAV;UACAA;QACA;MACA;IACA;IACAe;MACA;MACAf;MACAC;QACAY;QACAL;UACA;YACAP;cACAa;cACAN;gBACAR;gBACAA;cACA;cACAU;gBACAV;gBACAA;cACA;YACA;UACA;QACA;QACAU;UACAV;UACAA;QACA;MACA;IACA;IACAgB;MACAhB;QAAAiB;MAAA;QACA;UACA;YACAjB;UACA;YACAA;cAAAhB;YAAA;cACA;gBACA;kBACA;kBACAiB;oBACAY;kBACA;gBACA;kBACAb;gBACA;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAkB;IACAC;MACA;MACA;;MAEA;MACA;QACApB;MACA;QACAA;MACA;;MAEA;MACAA;IACA;IAEA;IACAqB;MACA;MACA;MAEA;QACAnB;UACAoB;UACAC;UACAZ;YACAa;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzYA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/luntan/ltlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/luntan/ltlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ltlist.vue?vue&type=template&id=7572a9a7&\"\nvar renderjs\nimport script from \"./ltlist.vue?vue&type=script&lang=js&\"\nexport * from \"./ltlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ltlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/luntan/ltlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ltlist.vue?vue&type=template&id=7572a9a7&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.clist && _vm.clist.length > 0 : null\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g1 =\n          item.show_form_content &&\n          item.form_content &&\n          item.form_content.length > 0\n        var l0 = g1\n          ? _vm.__map(item.form_content, function (field, fieldIndex) {\n              var $orig = _vm.__get_orig(field)\n              var g2 = field.images && field.images.length > 0\n              return {\n                $orig: $orig,\n                g2: g2,\n              }\n            })\n          : null\n        return {\n          $orig: $orig,\n          g1: g1,\n          l0: l0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ltlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ltlist.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的帖子\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t<view class=\"container2\">\n\t\t\t<image :src=\"banner\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"banner\"></image>\n\t\t\t\n\t\t\t<!-- 二级分类显示区域 -->\n\t\t\t<view class=\"navbox\" v-if=\"clist && clist.length > 0\">\n\t\t\t\t<!-- 添加\"全部\"选项 -->\n\t\t\t\t<view class=\"nav_li\" @tap=\"selectCategory\" :data-id=\"0\" :class=\"{'active': selectedCategoryId == null || selectedCategoryId == 0}\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/lt_read.png'\"></image>\n\t\t\t\t\t<view>全部</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-for=\"item in clist\" :key=\"item.id\" class=\"nav_li\" @tap=\"selectCategory\" :data-id=\"item.id\" :class=\"{'active': selectedCategoryId == item.id}\">\n\t\t\t\t\t<image :src=\"item.pic || pre_url+'/static/img/lt_read.png'\"></image>\n\t\t\t\t\t<view>{{ item.name }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"datalist\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'detail?id=' + item.id\">\n\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t<image :src=\"item.headimg\" class=\"f1\"></image>\n\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t<view class=\"t1\">\n\t\t\t\t\t\t\t\t{{item.nickname}}\n\t\t\t\t\t\t\t\t<text v-if=\"item.is_top == 1\" class=\"top-tag\">{{sysset.top_text || '置顶'}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"t2\">{{item.showtime}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"con\">\n\t\t\t\t\t\t<!-- 显示表单内容 -->\n\t\t\t\t\t\t<view v-if=\"item.show_form_content && item.form_content && item.form_content.length > 0\" class=\"form-content\">\n\t\t\t\t\t\t\t<view class=\"form-item\" v-for=\"(field, fieldIndex) in item.form_content\" :key=\"fieldIndex\">\n\t\t\t\t\t\t\t\t<view class=\"field-label\">{{field.label}}</view>\n\t\t\t\t\t\t\t\t<!-- 文本类型字段 -->\n\t\t\t\t\t\t\t\t<view v-if=\"!field.images && !field.video\" class=\"field-value\">\n\t\t\t\t\t\t\t\t\t{{field.value}}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<!-- 图片类型字段 -->\n\t\t\t\t\t\t\t\t<view v-if=\"field.images && field.images.length > 0\" class=\"field-images\">\n\t\t\t\t\t\t\t\t\t<image \n\t\t\t\t\t\t\t\t\t\tv-for=\"(img, imgIndex) in field.images\" \n\t\t\t\t\t\t\t\t\t\t:key=\"imgIndex\" \n\t\t\t\t\t\t\t\t\t\t:src=\"img\" \n\t\t\t\t\t\t\t\t\t\tclass=\"form-image\"\n\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t\t\**********=\"previewImage\" \n\t\t\t\t\t\t\t\t\t\t:data-urls=\"field.images\" \n\t\t\t\t\t\t\t\t\t\t:data-current=\"img\">\n\t\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<!-- 视频类型字段 -->\n\t\t\t\t\t\t\t\t<video \n\t\t\t\t\t\t\t\t\tv-if=\"field.video\"\n\t\t\t\t\t\t\t\t\tclass=\"form-video\" \n\t\t\t\t\t\t\t\t\t:id=\"'form-video-' + item.id + '-' + fieldIndex\"\n\t\t\t\t\t\t\t\t\t:src=\"field.video\" \n\t\t\t\t\t\t\t\t\**********=\"playvideo\"\n\t\t\t\t\t\t\t\t\t:poster=\"pre_url + '/static/img/video-poster.png'\"\n\t\t\t\t\t\t\t\t\t:show-play-btn=\"false\"\n\t\t\t\t\t\t\t\t\t:controls=\"false\"\n\t\t\t\t\t\t\t\t\t:loop=\"false\"\n\t\t\t\t\t\t\t\t\t:enable-progress-gesture=\"false\"\n\t\t\t\t\t\t\t\t\t:initial-time=\"0\"\n\t\t\t\t\t\t\t\t\tobject-fit=\"cover\"\n\t\t\t\t\t\t\t\t></video>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 如果内容被隐藏，显示提示信息 -->\n\t\t\t\t\t\t<view v-else-if=\"item.content_hidden\" class=\"content-hidden-tip\">\n\t\t\t\t\t\t\t<text class=\"tip-text\">🔒 该分类内容需要点击查看详情</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 正常显示内容 -->\n\t\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t\t<view class=\"f1\"><text style=\"white-space:pre-wrap;\">{{item.content}}</text></view>\n\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"item.pics\">\n\t\t\t\t\t\t\t\t<image v-for=\"(pic, idx) in item.pics\" :key=\"idx\" :src=\"pic\" mode=\"widthFix\" style=\"height:auto\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<video class=\"video\" id=\"video\" :src=\"item.video\" v-if=\"item.video\" @tap.stop=\"playvideo\"></video>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 显示价格 -->\n\t\t\t\t\t\t<view class=\"price-tag\" v-if=\"item.display_price\">\n\t\t\t\t\t\t\t<text class=\"price-value\">¥{{item.price}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"phone\" v-if=\"item.isshowphone\">\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.name\"><label class=\"t1\">姓名：</label>{{item.name}}</view>\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.mobile\"><label class=\"t1\">手机号：</label>{{item.mobile}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/lt_read.png'\" style=\"margin-top:0\"></image>{{item.readcount}}</view>\n\t\t\t\t\t\t<view class=\"f1\" style=\"margin-left:60rpx;\"><image :src=\"pre_url+'/static/img/lt_pinglun.png'\"></image>{{item.plcount}}</view>\n\t\t\t\t\t\t<view class=\"f2\"></view>\n\t\t\t\t\t\t<view class=\"f4\" @tap.stop=\"savecontent\" :data-id=\"item.id\" :data-index=\"index\" v-if=\"sysset.cansave\"><image :src=\"pre_url+'/static/img/lt_save.png'\"></image>保存</view>\n\t\t\t\t\t\t<view class=\"f3\" @tap.stop=\"zan\" :data-id=\"item.id\" :data-index=\"index\"><image :src=\"pre_url+'/static/img/lt_like' + (item['iszan']==0?'':'2') + '.png'\"></image>{{item.zan}}</view>\n\t\t\t\t\t\t<button v-if=\"sysset.show_top_btn && item.is_top !== 1 && item.mid === mid\" class=\"top-btn\" @tap.stop=\"topPost(item)\">{{sysset.top_text || '置顶'}}</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"covermy\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" @tap=\"goto\" :data-url=\"'fatie?cid=' + cid\"><image :src=\"pre_url+'/static/img/lt_fatie.png'\"></image></view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\t\n\t\t\tsysset:{},\n\t\t\tcid:'',\n      datalist: [],\n      pagenum: 1,\n      keyword: '',\n      nomore: false,\n      nodata: false,\n\t  banner: '',\n\t  clist: [],\n\t  selectedCategoryId: null,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.cid = this.opt.cid || '';\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tvar postData = {cid: that.cid, pagenum: pagenum, keyword: keyword};\n\t\t\t\n\t\t\t// 如果选择了二级分类，使用二级分类ID替换cid\n\t\t\tif(that.selectedCategoryId) {\n\t\t\t\tpostData.cid = that.selectedCategoryId;\n\t\t\t}\n\t\t\t\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      that.loading = true;\n      app.post('ApiLuntan/ltlist', postData, function (res) {\n\t\t\t\tthat.loading = false;\n        that.loaddingmore = false;\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: res.title\n\t\t\t\t\t});\n\t\t\t\t\tthat.pernum = res.pernum;\n\t\t\t\t\tthat.datalist = res.datalist;\n\t\t\t\t\tthat.sysset = res.sysset;\n\t\t\t\t\tthat.banner = res.banner;\n\t\t\t\t\tthat.title = res.title;\n\t\t\t\t\tthat.clist = res.clist;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.getdata();\n    },\n    zan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var index = e.currentTarget.dataset.index;\n      var datalist = that.datalist;\n      app.post(\"ApiLuntan/zan\", {id: id}, function (res) {\n        if (res.type == 0) {\n          //取消点赞\n          datalist[index].iszan = 0;\n          datalist[index].zan = datalist[index].zan - 1;\n        } else {\n          datalist[index].iszan = 1;\n          datalist[index].zan = datalist[index].zan + 1;\n        }\n        that.datalist = datalist;\n      });\n    },\n\t\tsavecontent:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar info = that.datalist[index];\n\t\t\tthat.fuzhi(info.content,function(){\n\t\t\t\tthat.savpic(info.pics,function(){\n\t\t\t\t\tthat.savevideo(info.video);\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\tfuzhi:function(content,callback){\n\t\t\tif(!content){\n\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar that = this;\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: content,\n\t\t\t\tsuccess: function () {\n\t\t\t\t\tapp.success('已复制到剪贴板');\n\t\t\t\t\tsetTimeout(function(){\n\t\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\t\t},500)\n\t\t\t\t},\n\t\t\t\tfail:function(){\n\t\t\t\t\tapp.error('请长按文本内容复制');\n\t\t\t\t\tsetTimeout(function(){\n\t\t\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\t\t},500)\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tsavpic:function(pics,callback){\n\t\t\tif(!pics){\n\t\t\t\ttypeof callback == 'function' && callback();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){\n\t\t\t\tapp.error('请长按图片保存');return;\n\t\t\t}\n\t\t\tthis.picindex = 0;\n\t\t\tthis.savpic2(pics);\n\t\t\ttypeof callback == 'function' && callback();\n\t\t},\n\t\tsavpic2:function(pics){\n\t\t\tvar that = this;\n\t\t\tvar picindex = this.picindex;\n\t\t\tif(picindex >= pics.length){\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success('已保存到相册');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar pic = pics[picindex];\n\t\t\tapp.showLoading('图片保存中');\n\t\t\tuni.downloadFile({\n\t\t\t\turl: pic,\n\t\t\t\tsuccess (res) {\n\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\tsuccess:function () {\n\t\t\t\t\t\t\t\tthat.picindex++;\n\t\t\t\t\t\t\t\tthat.savpic2(pics);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail:function(){\n\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\tapp.error('保存失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail:function(){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.error('下载失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tsavevideo:function(video){\n\t\t\tif(!video) return;\n\t\t\tapp.showLoading('视频下载中');\n\t\t\tuni.downloadFile({\n\t\t\t\turl: video,\n\t\t\t\tsuccess (res) {\n\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\tuni.saveVideoToPhotosAlbum({\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\tsuccess:function () {\n\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\tapp.success('视频保存成功');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail:function(){\n\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\tapp.error('视频保存失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail:function(){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.error('视频下载失败!');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    topPost(item) {\n      app.post('ApiLuntan/checkTopStatus', { luntanid: item.id }, res => {\n        if (res.status === 1) {\n          if (res.is_top) {\n            app.alert(`帖子已置顶，剩余${res.remain_days}天${res.remain_hours}小时`);\n          } else {\n            app.post('ApiLuntan/getTopPrices', { cid: item.cid }, r => {\n              if (r.status === 1) {\n                if (r.can_top) {\n                  // 可以前往置顶\n                  uni.navigateTo({\n                    url: '/activity/luntan/fatielog?action=top&id=' + item.id\n                  });\n                } else {\n                  app.alert(r.limit_msg || '无法置顶');\n                }\n              } else {\n                app.alert(r.msg || '获取置顶价格失败');\n              }\n            });\n          }\n        } else {\n          app.alert(res.msg || '检查置顶状态失败');\n        }\n      });\n    },\n    playvideo: function () {},\n\t\tselectCategory: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\t\n\t\t\t// 如果选择的是0，表示选择\"全部\"，清除二级分类筛选\n\t\t\tif(id == 0) {\n\t\t\t\tthat.selectedCategoryId = null;\n\t\t\t} else {\n\t\t\t\tthat.selectedCategoryId = id;\n\t\t\t}\n\t\t\t\n\t\t\t// 重新加载数据\n\t\t\tthat.getdata();\n\t\t},\n\t\t\n\t\t// 预览表单中的图片\n\t\tpreviewImage: function(e) {\n\t\t\tconst urls = e.currentTarget.dataset.urls || [];\n\t\t\tconst current = e.currentTarget.dataset.current || '';\n\t\t\t\n\t\t\tif (urls.length > 0) {\n\t\t\t\tuni.previewImage({\n\t\t\t\t\tcurrent: current,\n\t\t\t\t\turls: urls,\n\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\tconsole.log('预览图片失败:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n  }\n};\n</script>\n<style>\n.container2{width:100%;padding:20rpx;background:#fff;}\n\n.topsearch{width:100%;padding:20rpx 20rpx;margin-bottom:10rpx;margin-bottom:10rpx;background:#fff}\n.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1}\n.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n\n\n.navbox{background: #fff;height: auto;overflow: hidden;}\n.nav_li{width:20%;text-align: center;box-sizing: border-box;padding:15rpx 0 5rpx;float: left;color:#222;font-size:24rpx;cursor:pointer;}\n.nav_li image{width:70rpx;height: 70rpx;margin-bottom:8rpx;}\n.nav_li.active{background-color: #ff6b6b;color: #fff;border-radius: 8rpx;}\n\n.listtitle{width:100%;padding:0 24rpx;color:#222;font-weight:bold;font-size:32rpx;height:60rpx;line-height:60rpx}\n.datalist{width:100%;padding:0 24rpx;}\n.datalist .item{width:100%;display:flex;flex-direction:column;padding:24rpx 0;border-bottom:1px solid #f1f1f1}\n.datalist .item .top{width:100%;display:flex;align-items:center}\n.datalist .item .top .f1{width:80rpx;height:80rpx;border-radius:50%;margin-right:16rpx}\n.datalist .item .top .f2 .t1{color:#222;font-weight:bold;font-size:28rpx}\n.datalist .item .top .f2 .t2{color:#bbb;font-size:24rpx}\n.datalist .item .con{width:100%;padding:24rpx 0;display:flex;flex-direction:column;color:#000}\n.datalist .item .con .f2{margin-top:10rpx;display:flex;flex-wrap:wrap}\n.datalist .item .con .f2 image{width:31%;margin-right:2%;margin-bottom:10rpx;border-radius:8rpx}\n.datalist .item .con .video{width:80%;height:300rpx;margin-top:20rpx}\n.datalist .item .bot{width:100%;display:flex;align-items:center;color:#222222;font-size:28rpx}\n.datalist .item .bot .f1{display:flex;align-items:center;font-weight:bold}\n.datalist .item .bot .f1 image{width:36rpx;height:36rpx;margin-right:16rpx;margin-top:2px}\n.datalist .item .bot .f2{flex:1;}\n.datalist .item .bot .f3{display:flex;align-items:center;font-weight:bold}\n.datalist .item .bot .f3 image{width:40rpx;height:40rpx;margin-right:16rpx}\n.datalist .item .bot .f4{display:flex;align-items:center;margin-right:30rpx}\n.datalist .item .bot .f4 image{width:40rpx;height:40rpx;margin-right:10rpx}\n.datalist .item .bot .btn2{color:#fff;background:#FE1A29;border:1px solid #FE1A29;padding:6rpx 40rpx;font-size:24rpx;border-radius:40rpx;margin-left:16rpx}\n\n.covermy{position:fixed;z-index:99999;bottom:0;right:0;width:130rpx;height:130rpx;box-sizing:content-box}\n.covermy image{width:100%;height:100%}\n\n.nomore-footer-tips{background:#fff!important}\n\n.phone .f1{line-height: 60rpx;display: flex;}\n.phone .f1 label{ color: #999; width: 120rpx;}\n\n.top-tag {\n  display: inline-block;\n  background-color: #ff6b6b;\n  color: #fff;\n  font-size: 20rpx;\n  padding: 2rpx 10rpx;\n  border-radius: 8rpx;\n  margin-left: 10rpx;\n  font-weight: normal;\n  vertical-align: middle;\n}\n\n.top-btn {\n  color: #fff;\n  background-color: #ff6b6b;\n  border: none;\n  border-radius: 30rpx;\n  padding: 0 16rpx;\n  font-size: 24rpx;\n  height: 48rpx;\n  line-height: 48rpx;\n  margin-left: 20rpx;\n}\n\n/* 价格标签样式 */\n.price-tag {\n  display: inline-block;\n  background-color: #f8f8f8;\n  border: 1px solid #e74c3c;\n  border-radius: 8rpx;\n  margin-top: 16rpx;\n  padding: 6rpx 16rpx;\n  align-self: flex-start;\n}\n.price-value {\n  color: #e74c3c;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n/* 内容隐藏提示样式 */\n.content-hidden-tip {\n  background-color: #f0f0f0;\n  border: 1rpx dashed #ccc;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  text-align: center;\n  margin: 10rpx 0;\n}\n.tip-text {\n  color: #999;\n  font-size: 26rpx;\n}\n\n/* 表单内容显示样式 */\n.form-content {\n  width: 100%;\n  padding: 0;\n}\n\n.form-item {\n  margin-bottom: 24rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n.field-label {\n  color: #666;\n  font-size: 26rpx;\n  margin-bottom: 8rpx;\n  font-weight: bold;\n}\n\n.field-value {\n  color: #333;\n  font-size: 28rpx;\n  line-height: 1.6;\n  margin-bottom: 8rpx;\n}\n\n.field-images {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 8rpx;\n}\n\n.form-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n  border-radius: 8rpx;\n  object-fit: cover;\n}\n\n.form-video {\n  width: 100%;\n  height: 300rpx;\n  margin-top: 8rpx;\n  border-radius: 8rpx;\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ltlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ltlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464353\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}