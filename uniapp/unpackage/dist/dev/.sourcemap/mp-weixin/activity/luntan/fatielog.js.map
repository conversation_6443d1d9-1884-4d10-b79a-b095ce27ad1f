{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatielog.vue?5e94", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatielog.vue?83e7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatielog.vue?7554", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatielog.vue?65a1", "uni-app:///activity/luntan/fatielog.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatielog.vue?49d0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/luntan/fatielog.vue?8e8d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "datalist", "pagenum", "loading", "nomore", "refreshing", "topPrices", "selectedTopPrice", "currentTopPost", "topNopriceMsg", "refreshingPost", "refreshStatusMap", "onLoad", "methods", "getList", "app", "loadMore", "refresh", "viewDetail", "uni", "url", "fail", "console", "title", "icon", "editPost", "deletePost", "content", "success", "id", "commentPost", "setTimeout", "query", "selector", "duration", "likePost", "item", "previewImg", "current", "urls", "checkRefreshStatus", "confirmText", "cancelText", "refreshPost", "refreshPayPost", "force", "formatDateTimeNow", "topPost", "luntanid", "cid", "selectTopPrice", "closeTopPopup", "confirmTopPost", "days", "formatTopEndTime", "showTopInfo", "handleTopRedirect"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgF11B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;;IAEA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;QACA;QACA;MACA;MACA;MACAC;QAAAb;MAAA;QACA;QACA;QACA;UACA;UACA;YACA;UACA;YACA;UACA;UACA,+DACA;QACA;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAC;QACAC;QACAC;UACAC;UACAH;YACAI;YACAC;UACA;QACA;MACA;IACA;IACAC;MACAN;QAAAC;MAAA;IACA;IACAM;MAAA;MACAP;QACAI;QACAI;QACAC;UACA;YACAb;cAAAc;YAAA;cACA;gBACA;gBACAd;cACA;gBACAA;cACA;YACA;UACA;QACA;MACA;IACA;IACAe;MACAX;QACAC;QACAQ;UACAG;YACA;YACAC;YACAA;cACA;gBACAb;kBACAc;kBACAC;gBACA;cACA;YACA;UACA;QACA;QACAb;UACAC;UACAH;YACAI;YACAC;UACA;QACA;MACA;IACA;IACAW;MAAA;MACApB;QAAAc;MAAA;QACA;UACA;UACAO;UACAA;UACA;QACA;MACA;IACA;IACAC;MACAlB;QAAAmB;QAAAC;MAAA;IACA;IACAC;MAAA;MACA;MACAzB;QAAAc;MAAA;QACA;QACA;UACA;UACA;UACAP;;UAEA;UACA;YACA;YACA;cACA;cACA;gBACA;gBACAH;kBACAI;kBACAI;kBACAc;kBACAC;kBACAd;oBACA;sBACA;oBACA;kBACA;gBACA;cACA;gBACA;gBACAb;cACA;YACA;cACA;cACAA;YACA;YACA;UACA;;UAEA;UACA;YACA;YACA;cACA;cACAI;gBACAI;gBACAI;gBACAc;gBACAC;gBACAd;kBACA;oBACA;kBACA;gBACA;cACA;cACA;YACA;UACA;;UAEA;UACA;YACA;YACA;YACAT;cACAI;cACAI;cACAc;cACAC;cACAd;gBACA;kBACA;gBACA;cACA;YACA;UACA;YACA;YACA;UACA;QACA;UACAb;QACA;MACA;IACA;IACA4B;MAAA;MACAxB;QACAI;MACA;MAEAR;QAAAc;MAAA;QACAV;QAEA;UACAJ;UACA;UACA;QACA;UACA;UACAI;YACAI;YACAI;YACAc;YACAC;YACAd;cACA;gBACAT;kBACAC;gBACA;cACA;YACA;UACA;QACA;UACAL;QACA;MACA;IACA;IACA6B;MAAA;MACAzB;QACAI;MACA;MAEAR;QAAAc;QAAAgB;MAAA;QACA1B;QAEA;UACA;UACAA;YACAC;UACA;QACA;UACAL;UACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACA+B;MACA;MACA;IACA;IACAC;MAAA;MACA;MACAhC;QAAAiC;MAAA;QACA;UACA;YACAjC;UACA;YACAA;cAAAkC;YAAA;cACA;gBACA;kBACA;kBACA;kBACA;gBACA;kBACAlC;gBACA;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAmC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACArC;QACA;MACA;MAEAA;QACAiC;QACAK;MACA;QACA;UACA;UAEAlC;YACAC;UACA;QACA;UACAL;QACA;MACA;IACA;IACAuC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACAxC;MACA;QACAA;MACA;IACA;IACAyC;MAAA;MACA;MACAzC;QAAAiC;MAAA;QACA;UACA;YACAjC;UACA;YACA;YACAA;cAAAc;YAAA;cACA;gBACA;gBACAd;kBAAAkC;gBAAA;kBACA;oBACA;sBACA;sBACA;sBACA;sBACA;oBACA;sBACAlC;oBACA;kBACA;oBACAA;kBACA;gBACA;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrcA;AAAA;AAAA;AAAA;AAA2sC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA/tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/luntan/fatielog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/luntan/fatielog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fatielog.vue?vue&type=template&id=630c99f0&scoped=true&\"\nvar renderjs\nimport script from \"./fatielog.vue?vue&type=script&lang=js&\"\nexport * from \"./fatielog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fatielog.vue?vue&type=style&index=0&id=630c99f0&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"630c99f0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/luntan/fatielog.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatielog.vue?vue&type=template&id=630c99f0&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.datalist.length\n  var l0 = g0\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 =\n          item.is_top == 1 ? _vm.formatTopEndTime(item.top_endtime) : null\n        var m1 =\n          item.is_top == 1 && m0 ? _vm.formatTopEndTime(item.top_endtime) : null\n        var g1 = item.pics && item.pics.length\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          g1: g1,\n        }\n      })\n    : null\n  var g2 = _vm.nomore && _vm.datalist.length\n  var g3 = _vm.topPrices.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatielog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatielog.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"fatielog-page\">\r\n    <view class=\"header\">\r\n      <text class=\"title\">我的发帖</text>\r\n    </view>\r\n    <scroll-view scroll-y class=\"post-list\" @scrolltolower=\"loadMore\" @refresherrefresh=\"refresh\" :refresher-enabled=\"true\" :refresher-triggered=\"refreshing\">\r\n      <block v-if=\"datalist.length\">\r\n        <view v-for=\"(item, index) in datalist\" :key=\"item.id\" class=\"post-card\">\r\n          <view class=\"post-header\">\r\n            <image :src=\"item.headimg || '/static/default-avatar.png'\" class=\"avatar\" />\r\n            <view class=\"info\">\r\n              <text class=\"nickname\">{{ item.nickname || '匿名用户' }}</text>\r\n              <text class=\"time\">{{ item.createtime }}</text>\r\n            </view>\r\n            <view class=\"status\" v-if=\"item.status==0\">待审核</view>\r\n            <view class=\"status status-pass\" v-else-if=\"item.status==1\">已通过</view>\r\n            <view class=\"status status-reject\" v-else-if=\"item.status==2\">已驳回</view>\r\n            <view class=\"status status-top\" v-if=\"item.is_top==1\">\r\n              {{ formatTopEndTime(item.top_endtime) ? '置顶至' + formatTopEndTime(item.top_endtime) : '已置顶' }}\r\n            </view>\r\n          </view>\r\n          <view class=\"post-content\" @tap=\"viewDetail(item.id)\">\r\n            <text v-if=\"item.is_top === 1\" class=\"top-badge\">置顶</text>\r\n            {{ item.content }}\r\n          </view>\r\n          <view class=\"post-images\" v-if=\"item.pics && item.pics.length\">\r\n            <image v-for=\"(pic, idx) in item.pics\" :key=\"idx\" :src=\"pic\" class=\"post-img\" mode=\"aspectFill\" @tap=\"previewImg(pic, item.pics)\" />\r\n          </view>\r\n          <view class=\"post-actions\">\r\n            <button class=\"action-btn\" @tap=\"viewDetail(item.id)\">详情</button>\r\n            <button class=\"action-btn\" @tap=\"commentPost(item.id)\">评论({{ item.plcount !== undefined ? item.plcount : 0 }})</button>\r\n            <button v-if=\"item.status === 1\" class=\"action-btn refresh-btn\" @tap=\"checkRefreshStatus(item.id, index)\">刷新</button>\r\n            <button v-if=\"item.status === 1 && item.is_top !== 1\" class=\"action-btn\" @tap=\"topPost(item)\">置顶</button>\r\n            <button v-if=\"item.status === 1 && item.is_top === 1\" class=\"action-btn action-btn-topped\" @tap=\"showTopInfo(item)\">已置顶</button>\r\n            <button class=\"action-btn delete\" @tap=\"deletePost(item.id, index)\">删除</button>\r\n            <view class=\"action-static\">\r\n              <text class=\"static-label\">点赞：</text>\r\n              <text class=\"static-num\">{{ item.zan !== undefined ? item.zan : 0 }}</text>\r\n              <text class=\"static-label\">浏览：</text>\r\n              <text class=\"static-num\">{{ item.readcount !== undefined ? item.readcount : 0 }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      <view v-else class=\"nodata\">暂无发帖记录</view>\r\n      <view v-if=\"loading\" class=\"loading\">加载中...</view>\r\n      <view v-if=\"nomore && datalist.length\" class=\"nomore\">没有更多了</view>\r\n    </scroll-view>\r\n\r\n    <uni-popup ref=\"topPopup\" type=\"center\">\r\n      <view class=\"top-popup\">\r\n        <view class=\"top-popup-title\">置顶帖子</view>\r\n        <view v-if=\"topPrices.length > 0\">\r\n          <view class=\"top-popup-subtitle\">请选择置顶时间</view>\r\n          <view class=\"top-price-list\">\r\n            <view \r\n              v-for=\"(price, index) in topPrices\" \r\n              :key=\"index\" \r\n              class=\"top-price-item\" \r\n              :class=\"{ 'top-price-item-active': selectedTopPrice.days === price.days }\"\r\n              @tap=\"selectTopPrice(price)\"\r\n            >\r\n              <view class=\"top-price-days\">{{ price.days }}天</view>\r\n              <view class=\"top-price-price\">{{ price.price }}元</view>\r\n            </view>\r\n          </view>\r\n          <view class=\"top-popup-actions\">\r\n            <button class=\"top-popup-btn top-popup-cancel\" @tap=\"closeTopPopup\">取消</button>\r\n            <button class=\"top-popup-btn top-popup-confirm\" @tap=\"confirmTopPost\">确定</button>\r\n          </view>\r\n        </view>\r\n        <view v-else class=\"top-no-prices\">\r\n          <text>{{ topNopriceMsg || '暂无置顶价格配置' }}</text>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      datalist: [],\r\n      pagenum: 1,\r\n      loading: false,\r\n      nomore: false,\r\n      refreshing: false,\r\n      topPrices: [],\r\n      selectedTopPrice: {},\r\n      currentTopPost: null,\r\n      topNopriceMsg: '',\r\n      refreshingPost: false,\r\n      refreshStatusMap: {},\r\n    };\r\n  },\r\n  onLoad(options) {\r\n    this.getList();\r\n    \r\n    // 检查是否是置顶跳转\r\n    if (options && options.action === 'top' && options.id) {\r\n      this.handleTopRedirect(options.id);\r\n    }\r\n  },\r\n  methods: {\r\n    getList(refresh) {\r\n      if (this.loading || this.nomore && !refresh) return;\r\n      if (refresh) {\r\n        this.pagenum = 1;\r\n        this.nomore = false;\r\n      }\r\n      this.loading = true;\r\n      app.post('ApiLuntan/fatielog', { pagenum: this.pagenum }, res => {\r\n        this.loading = false;\r\n        this.refreshing = false;\r\n        if (res.status == 1) {\r\n          let list = res.data || [];\r\n          if (refresh) {\r\n            this.datalist = list;\r\n          } else {\r\n            this.datalist = this.datalist.concat(list);\r\n          }\r\n          if (!list.length || list.length < 20) this.nomore = true;\r\n          else this.pagenum++;\r\n        }\r\n      });\r\n    },\r\n    loadMore() {\r\n      this.getList();\r\n    },\r\n    refresh() {\r\n      this.refreshing = true;\r\n      this.getList(true);\r\n    },\r\n    viewDetail(id) {\r\n      uni.navigateTo({ \r\n        url: '/activity/luntan/detail?id=' + id,\r\n        fail: (err) => {\r\n          console.error('跳转失败:', err);\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    editPost(id) {\r\n      uni.navigateTo({ url: '/activity/luntan/edit?id=' + id });\r\n    },\r\n    deletePost(id, idx) {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要删除该帖子吗？',\r\n        success: res => {\r\n          if (res.confirm) {\r\n            app.post('ApiLuntan/deltie', { id }, r => {\r\n              if (r.status == 1) {\r\n                this.datalist.splice(idx, 1);\r\n                app.success('删除成功');\r\n              } else {\r\n                app.error(r.msg);\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    commentPost(id) {\r\n      uni.navigateTo({ \r\n        url: '/activity/luntan/detail?id=' + id,\r\n        success: () => {\r\n          setTimeout(() => {\r\n            const query = uni.createSelectorQuery();\r\n            query.select('#datalist').boundingClientRect();\r\n            query.exec((res) => {\r\n              if (res[0]) {\r\n                uni.pageScrollTo({\r\n                  selector: '#datalist',\r\n                  duration: 300\r\n                });\r\n              }\r\n            });\r\n          }, 500);\r\n        },\r\n        fail: (err) => {\r\n          console.error('跳转失败:', err);\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    likePost(id, idx) {\r\n      app.post('ApiLuntan/zan', { id }, r => {\r\n        if (r.status == 1) {\r\n          let item = this.datalist[idx];\r\n          item.iszan = !item.iszan;\r\n          item.zan = item.iszan ? (item.zan + 1) : (item.zan - 1);\r\n          this.$set(this.datalist, idx, item);\r\n        }\r\n      });\r\n    },\r\n    previewImg(pic, pics) {\r\n      uni.previewImage({ current: pic, urls: pics });\r\n    },\r\n    checkRefreshStatus(postId, index) {\r\n      this.refreshingPost = true;\r\n      app.post('ApiLuntan/getRefreshInfo', { id: postId }, res => {\r\n        this.refreshingPost = false;\r\n        if (res.status == 1) {\r\n          const data = res.data;\r\n          this.$set(this.refreshStatusMap, postId, data);\r\n          console.log('获取刷新状态:', data);\r\n          \r\n          // 刷新逻辑判断\r\n          if (!data.can_refresh) {\r\n            // 如果不能刷新\r\n            if (data.remain_interval > 0) {\r\n              // 在刷新间隔期内\r\n              if (data.can_pay_refresh) {\r\n                // 可以付费绕过间隔限制\r\n                uni.showModal({\r\n                  title: '刷新提示',\r\n                  content: `刷新间隔为${data.refresh_interval}天，还需等待${data.remain_interval}天。是否付费${data.refresh_price}元立即刷新？`,\r\n                  confirmText: '付费刷新',\r\n                  cancelText: '取消',\r\n                  success: modal => {\r\n                    if (modal.confirm) {\r\n                      this.refreshPayPost(postId, index);\r\n                    }\r\n                  }\r\n                });\r\n              } else {\r\n                // 不能付费绕过\r\n                app.alert(`刷新间隔为${data.refresh_interval}天，还需等待${data.remain_interval}天`);\r\n              }\r\n            } else {\r\n              // 其他原因导致不能刷新\r\n              app.alert('该分类未开启刷新功能或已达到刷新限制');\r\n            }\r\n            return;\r\n          }\r\n          \r\n          // 可以刷新，但需要判断是免费还是付费\r\n          if (data.free_refresh_limit > 0 && data.today_free_count >= data.free_refresh_limit) {\r\n            // 免费次数已用完\r\n            if (data.can_pay_refresh) {\r\n              // 可以付费刷新\r\n              uni.showModal({\r\n                title: '刷新提示',\r\n                content: `今日免费刷新次数(${data.free_refresh_limit}次)已用完，是否付费${data.refresh_price}元刷新？`,\r\n                confirmText: '付费刷新',\r\n                cancelText: '取消',\r\n                success: modal => {\r\n                  if (modal.confirm) {\r\n                    this.refreshPayPost(postId, index);\r\n                  }\r\n                }\r\n              });\r\n              return;\r\n            }\r\n          }\r\n          \r\n          // 可以免费刷新\r\n          if (data.free_refresh_limit > 0) {\r\n            // 显示剩余次数\r\n            const remainCount = data.free_refresh_limit - data.today_free_count;\r\n            uni.showModal({\r\n              title: '免费刷新',\r\n              content: `今日还可免费刷新${remainCount}次，是否刷新？`,\r\n              confirmText: '刷新',\r\n              cancelText: '取消',\r\n              success: modal => {\r\n                if (modal.confirm) {\r\n                  this.refreshPost(postId, index);\r\n                }\r\n              }\r\n            });\r\n          } else {\r\n            // 无次数限制的免费刷新\r\n            this.refreshPost(postId, index);\r\n          }\r\n        } else {\r\n          app.error(res.msg || '获取刷新信息失败');\r\n        }\r\n      });\r\n    },\r\n    refreshPost(postId, index) {\r\n      uni.showLoading({\r\n        title: '刷新中...'\r\n      });\r\n      \r\n      app.post('ApiLuntan/refreshPost', { id: postId }, res => {\r\n        uni.hideLoading();\r\n        \r\n        if (res.status == 1) {\r\n          app.success(res.msg || '刷新成功');\r\n          // 刷新列表数据，将该帖子移到首位\r\n          this.getList(true); // 直接刷新整个列表更可靠\r\n        } else if (res.status == 2 && res.payorderid) {\r\n          // 需要付费\r\n          uni.showModal({\r\n            title: '付费刷新',\r\n            content: res.msg || '需要支付才能刷新',\r\n            confirmText: '去支付',\r\n            cancelText: '取消',\r\n            success: modal => {\r\n              if (modal.confirm) {\r\n                uni.navigateTo({\r\n                  url: '/pagesExt/pay/pay?id=' + res.payorderid + '&fromPage=luntan_refresh'\r\n                });\r\n              }\r\n            }\r\n          });\r\n        } else {\r\n          app.error(res.msg || '刷新失败');\r\n        }\r\n      });\r\n    },\r\n    refreshPayPost(postId, index) {\r\n      uni.showLoading({\r\n        title: '处理中...'\r\n      });\r\n      \r\n      app.post('ApiLuntan/refreshPost', { id: postId, force: 1 }, res => {\r\n        uni.hideLoading();\r\n        \r\n        if (res.status == 2 && res.payorderid) {\r\n          // 跳转支付页面\r\n          uni.navigateTo({\r\n            url: '/pagesExt/pay/pay?id=' + res.payorderid + '&fromPage=luntan_refresh'\r\n          });\r\n        } else if (res.status == 1) {\r\n          app.success(res.msg || '刷新成功');\r\n          // 刷新列表\r\n          this.getList(true);\r\n        } else {\r\n          app.error(res.msg || '刷新失败');\r\n        }\r\n      });\r\n    },\r\n    formatDateTimeNow() {\r\n      const now = new Date();\r\n      return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;\r\n    },\r\n    topPost(item) {\r\n      this.currentTopPost = item;\r\n      app.post('ApiLuntan/checkTopStatus', { luntanid: item.id }, res => {\r\n        if (res.status === 1) {\r\n          if (res.is_top) {\r\n            app.alert(`帖子已置顶，剩余${res.remain_days}天${res.remain_hours}小时`);\r\n          } else {\r\n            app.post('ApiLuntan/getTopPrices', { cid: item.cid }, r => {\r\n              if (r.status === 1) {\r\n                if (r.can_top) {\r\n                  this.topPrices = r.prices;\r\n                  this.selectedTopPrice = this.topPrices.length > 0 ? this.topPrices[0] : {};\r\n                  this.$refs.topPopup.open();\r\n                } else {\r\n                  app.alert(r.limit_msg || '无法置顶');\r\n                }\r\n              } else {\r\n                app.alert(r.msg || '获取置顶价格失败');\r\n              }\r\n            });\r\n          }\r\n        } else {\r\n          app.alert(res.msg || '检查置顶状态失败');\r\n        }\r\n      });\r\n    },\r\n    selectTopPrice(price) {\r\n      this.selectedTopPrice = price;\r\n    },\r\n    closeTopPopup() {\r\n      this.$refs.topPopup.close();\r\n    },\r\n    confirmTopPost() {\r\n      if (!this.selectedTopPrice.days) {\r\n        app.alert('请选择置顶天数');\r\n        return;\r\n      }\r\n      \r\n      app.post('ApiLuntan/createTopOrder', {\r\n        luntanid: this.currentTopPost.id,\r\n        days: this.selectedTopPrice.days\r\n      }, res => {\r\n        if (res.status === 1) {\r\n          this.closeTopPopup();\r\n          \r\n          uni.navigateTo({\r\n            url: '/pagesExt/pay/pay?id=' + res.payorderid\r\n          });\r\n        } else {\r\n          app.alert(res.msg || '创建置顶订单失败');\r\n        }\r\n      });\r\n    },\r\n    formatTopEndTime(timestamp) {\r\n      if (!timestamp) return '';\r\n      const date = new Date(timestamp * 1000);\r\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\r\n    },\r\n    showTopInfo(item) {\r\n      const now = Math.floor(Date.now() / 1000);\r\n      const endTime = item.top_endtime;\r\n      if (endTime > now) {\r\n        const remainSeconds = endTime - now;\r\n        const remainDays = Math.floor(remainSeconds / 86400);\r\n        const remainHours = Math.floor((remainSeconds % 86400) / 3600);\r\n        app.alert(`帖子已置顶，剩余${remainDays}天${remainHours}小时`);\r\n      } else {\r\n        app.alert('置顶即将到期，请刷新页面查看最新状态');\r\n      }\r\n    },\r\n    handleTopRedirect(postId) {\r\n      // 获取帖子信息\r\n      app.post('ApiLuntan/checkTopStatus', { luntanid: postId }, res => {\r\n        if (res.status === 1) {\r\n          if (res.is_top) {\r\n            app.alert(`帖子已置顶，剩余${res.remain_days}天${res.remain_hours}小时`);\r\n          } else {\r\n            // 获取该帖子的分类ID\r\n            app.post('ApiLuntan/getPostInfo', { id: postId }, postInfo => {\r\n              if (postInfo.status === 1) {\r\n                // 获取置顶价格\r\n                app.post('ApiLuntan/getTopPrices', { cid: postInfo.data.cid }, r => {\r\n                  if (r.status === 1) {\r\n                    if (r.can_top) {\r\n                      this.topPrices = r.prices;\r\n                      this.selectedTopPrice = this.topPrices.length > 0 ? this.topPrices[0] : {};\r\n                      this.currentTopPost = postInfo.data;\r\n                      this.$refs.topPopup.open();\r\n                    } else {\r\n                      app.alert(r.limit_msg || '无法置顶');\r\n                    }\r\n                  } else {\r\n                    app.alert(r.msg || '获取置顶价格失败');\r\n                  }\r\n                });\r\n              } else {\r\n                app.alert(postInfo.msg || '获取帖子信息失败');\r\n              }\r\n            });\r\n          }\r\n        } else {\r\n          app.alert(res.msg || '检查置顶状态失败');\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.fatielog-page { background: #f7f8fa; min-height: 100vh; }\r\n.header { padding: 32rpx 24rpx 0 24rpx; background: #fff; }\r\n.title { font-size: 36rpx; font-weight: bold; color: #222; }\r\n.post-list { padding: 24rpx; }\r\n.post-card { background: #fff; border-radius: 16rpx; margin-bottom: 24rpx; box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04); padding: 24rpx; }\r\n.post-header { display: flex; align-items: center; margin-bottom: 12rpx; }\r\n.avatar { width: 64rpx; height: 64rpx; border-radius: 50%; margin-right: 16rpx; }\r\n.info { flex: 1; }\r\n.nickname { font-size: 28rpx; color: #333; font-weight: 500; }\r\n.time { font-size: 24rpx; color: #999; margin-left: 16rpx; }\r\n.status { font-size: 24rpx; color: #fff; background: #ff9800; border-radius: 8rpx; padding: 2rpx 12rpx; margin-left: 8rpx; }\r\n.status-pass { background: #4caf50; }\r\n.status-reject { background: #f44336; }\r\n.status-top { \r\n  background: #ff6b6b;\r\n  font-size: 22rpx;\r\n}\r\n.post-content { font-size: 30rpx; color: #222; margin-bottom: 12rpx; position: relative; }\r\n.top-badge {\r\n  display: inline-block;\r\n  background-color: #ff6b6b;\r\n  color: #fff;\r\n  font-size: 20rpx;\r\n  padding: 2rpx 10rpx;\r\n  border-radius: 8rpx;\r\n  margin-right: 8rpx;\r\n  font-weight: normal;\r\n  vertical-align: middle;\r\n}\r\n.post-images { display: flex; flex-wrap: wrap; gap: 8rpx; margin-bottom: 12rpx; }\r\n.post-img { width: 180rpx; height: 180rpx; border-radius: 8rpx; object-fit: cover; }\r\n.post-actions {\r\n  display: flex;\r\n  gap: 8rpx;\r\n  flex-wrap: wrap;\r\n}\r\n.action-btn {\r\n  min-width: 80rpx;\r\n  max-width: 160rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 26rpx;\r\n  color: #007aff;\r\n  background: #f0f4fa;\r\n  border: none;\r\n  border-radius: 8rpx;\r\n  padding: 12rpx 0;\r\n  margin: 0;\r\n}\r\n.action-btn.delete { color: #f44336; }\r\n.action-btn.liked { color: #e91e63; }\r\n.action-btn.refresh-btn { color: #31C88E; }\r\n.action-btn-topped { \r\n  background-color: #ff9800 !important; \r\n  color: #fff !important;\r\n}\r\n.nodata, .loading, .nomore { text-align: center; color: #999; font-size: 28rpx; margin: 32rpx 0; }\r\n.action-static {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  margin-left: 16rpx;\r\n}\r\n.static-label {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n.static-num {\r\n  font-size: 24rpx;\r\n  color: #333;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n/* 置顶价格弹窗样式 */\r\n.top-popup {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 40rpx 30rpx;\r\n  width: 600rpx;\r\n}\r\n\r\n.top-popup-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  text-align: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.top-popup-subtitle {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.top-price-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.top-price-item {\r\n  width: 48%;\r\n  background: #f8f8f8;\r\n  border-radius: 12rpx;\r\n  padding: 20rpx 0;\r\n  margin-bottom: 20rpx;\r\n  text-align: center;\r\n  border: 2rpx solid transparent;\r\n}\r\n\r\n.top-price-item-active {\r\n  border-color: #1989fa;\r\n  background: #ecf5ff;\r\n}\r\n\r\n.top-price-days {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.top-price-price {\r\n  font-size: 26rpx;\r\n  color: #fa3534;\r\n}\r\n\r\n.top-popup-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.top-popup-btn {\r\n  width: 45%;\r\n  font-size: 28rpx;\r\n  padding: 15rpx 0;\r\n  border-radius: 10rpx;\r\n}\r\n\r\n.top-popup-cancel {\r\n  background: #f2f2f2;\r\n  color: #666;\r\n}\r\n\r\n.top-popup-confirm {\r\n  background: #1989fa;\r\n  color: #fff;\r\n}\r\n\r\n.top-no-prices {\r\n  text-align: center;\r\n  color: #999;\r\n  font-size: 28rpx;\r\n  padding: 20rpx 0;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatielog.vue?vue&type=style&index=0&id=630c99f0&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fatielog.vue?vue&type=style&index=0&id=630c99f0&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839464370\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}