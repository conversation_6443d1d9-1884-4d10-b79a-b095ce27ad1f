{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/product.vue?cb0c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/product.vue?c595", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/product.vue?f5b8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/product.vue?bf75", "uni-app:///activity/kanjia/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/product.vue?ea75", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/product.vue?eab4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "current", "indexurl", "platform", "tabnum", "num", "isfavorite", "btntype", "ggselected", "ks", "gwcnum", "nodata", "userinfo", "djsday", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "product", "business", "shopset", "pagecontent", "joinlist", "nowtime", "imJoin", "title", "sharepic", "sharetypevisible", "showposter", "posterpic", "kfurl", "onLoad", "onPullDownRefresh", "onShareAppMessage", "pic", "callback", "that", "onShareTimeline", "imageUrl", "query", "onUnload", "clearInterval", "methods", "getdata", "app", "id", "interval", "uni", "sharecallback", "proid", "swiper<PERSON><PERSON>e", "getdjs", "joinin", "addfavorite", "type", "tabClick", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata", "sharelink"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoJz1B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAGAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAR;MAAAS;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACA;MAAAZ;MAAAS;MAAAC;QAAAC;MAAA;IAAA;IACA;IACA;MACAX;MACAa;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACA;MACAP;MACAQ;QAAAC;MAAA;QACAT;QACA;UACAQ;UACA;QACA;QACA;QACA;QACA;QAEAR;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAK;QACAK;UACAV;UACAA;QACA;QACAW;UACAtB;QACA;QACAW;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAAX;UAAAS;UAAAC;YAAAC;UAAA;QAAA;MACA;IACA;IACAY;MACA;MACAJ;QAAAK;MAAA;QACA;UACA;UACA;UACA;QAAA,CACA;UACA;QAAA;MAEA;IACA;IACAC;MACA;MACAd;IACA;IACAe;MACA;MACA;QACA;MACA;QACA;MACA;MACA;QACAf;QACAA;QACAA;QACAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAgB;MACA;MACA;MACA;MACAR;IACA;IACA;IACAS;MACA;MACA;MACAT;MACAA;QAAAK;QAAAK;MAAA;QACAV;QACA;UACAR;QACA;QACAQ;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAtB;MACAA;MACAQ;MACAA;QAAAK;MAAA;QACAL;QACA;UACAA;QACA;UACAR;QACA;MACA;IACA;IACAuB;MACA;IACA;IACAC;MACAhB;MACA;IACA;IACAiB;MACA;MACAd;QACAe;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACAlB;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzYA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kanjia/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kanjia/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=07ab44ec&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kanjia/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=07ab44ec&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.product.pics.length : null\n  var m0 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m2 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    _vm.imJoin == 0 &&\n    _vm.product.starttime > _vm.nowtime\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    _vm.imJoin == 0 &&\n    !(_vm.product.starttime > _vm.nowtime) &&\n    !(_vm.product.endtime < _vm.nowtime)\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload && _vm.product.status == 1 && !(_vm.imJoin == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m5 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m6 =\n    _vm.isload && _vm.sharetypevisible && !(m5 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m7 =\n    _vm.isload && _vm.sharetypevisible && !(m5 == \"app\") && !(m6 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"swiper-container\">\r\n\t\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<view class=\"imageCount\">{{current+1}}/{{product.pics.length}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"kanjia_title\">\r\n\t\t\t\t \r\n\t\t\t\t<view class=\"f1\" :style=\"[{background:(product.endtime > nowtime ? 'linear-gradient(90deg,#FF3143,#FE6748);':'#ccc')}]\">\r\n\t\t\t\t\t<view class=\"t1\">砍价最低 </view>\r\n\t\t\t\t\t<view class=\"t2\"><text style=\"font-size:40rpx;font-weight:bold\">{{product.min_price}}</text> 元可拿</view>\r\n\t\t\t\t\t<view class=\"t3\" v-if=\"product.endtime > nowtime\">{{product.sales}}人已砍价成功</view>\r\n\t\t\t\t\t<view class=\"t3\" style=\"background: #ccc;color: #fff;\" v-else>活动已结束</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f3\" v-if=\"product.endtime > nowtime\">\r\n\t\t\t\t\t<view class=\"t1\">{{ product.starttime > nowtime ? '距活动开始还有' : '距活动结束还剩'}}</view>\r\n\t\t\t\t\t<view class=\"t2\" id=\"djstime\"><text class=\"djsspan\">{{djsday}}</text> 天 <text class=\"djsspan\">{{djshour}}</text> : <text class=\"djsspan\">{{djsmin}}</text> : <text class=\"djsspan\">{{djssec}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"header\"> \r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view class=\"lef\">\r\n\t\t\t\t\t\t<text>{{product.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/share.png'\"></image>\r\n\t\t\t\t\t\t<text>分享</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sales_stock\">\r\n\t\t\t\t\t<view class=\"f1\">原价：￥{{product.sell_price}} </view>\r\n\t\t\t\t\t<view class=\"f2\">已砍走{{product.sales}}件 剩余{{product.stock}}件</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"joinlist\" v-if=\"product.saleing>0\">\r\n\t\t\t\t<view v-for=\"(join, index) in joinlist\" :key=\"index\" class=\"t1\">\r\n\t\t\t\t\t<image :src=\"join.headimg\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t1\" v-if=\"product.saleing>7\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/moreuser.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t2\">{{product.saleing}}人正在参加</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\r\n\t\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t\t</view> \r\n\t\t\t\t<button class=\"p4\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\" data-opentype=\"reLaunch\">进入店铺</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t\t<view class=\"detail\">\r\n\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view style=\"width:100%;height:70px;\"></view>\r\n\r\n\t\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<view class=\"item flex1\" @tap=\"shareClick\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/share2.png'\"/>\r\n\t\t\t\t\t\t<view class=\"t1\">分享</view> \r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\r\n\t\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t<block v-if=\"imJoin==0\">\r\n\t\t\t\t\t\t<view class=\"tobuy\" :style=\"{background:t('color1')}\" v-if=\"product.starttime > nowtime\" style=\"background:#aaa\">活动未开始</view>\r\n\t\t\t\t\t\t<view class=\"tobuy\" v-else-if=\"product.endtime < nowtime\" style=\"background:#ccc\">活动已结束</view>\r\n\t\t\t\t\t\t<view class=\"tobuy\" :style=\"{background:t('color1')}\" @tap=\"joinin\" v-else>立即参与砍价</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view class=\"tobuy\" :style=\"{background:t('color1')}\" @tap=\"joinin\">查看我的砍价</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      current: 0,\r\n\t\t\tindexurl:app.globalData.indexurl,\r\n\t\t\tplatform:app.globalData.platform,\r\n\t\t\ttabnum: 1,\r\n      num: 1,\r\n      isfavorite: false,\r\n      btntype: 1,\r\n      ggselected: [],\r\n      ks: '',\r\n      gwcnum: 1,\r\n      nodata: 0,\r\n      userinfo: [],\r\n      djsday: '00',\r\n      djshour: '00',\r\n      djsmin: '00',\r\n      djssec: '00',\r\n      product: \"\",\r\n      business: {},\r\n      shopset: \"\",\r\n      pagecontent: \"\",\r\n      joinlist: \"\",\r\n      nowtime: \"\",\r\n      imJoin: \"\",\r\n      title: \"\",\r\n      sharepic: \"\",\r\n      sharetypevisible: false,\r\n      showposter: false,\r\n      posterpic: \"\",\r\n\t\t\tkfurl:'',\r\n    };\r\n  },\r\n\r\n \r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic,callback:function(){that.sharecallback();}});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic,callback:function(){that.sharecallback();}});\r\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  onUnload: function () {\r\n    clearInterval(interval);\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiKanjia/product', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar business = res.business;\r\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\r\n\t\t\t\tthat.product = product;\r\n\t\t\t\tthat.business = business;\r\n\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.joinlist = res.joinlist;\r\n\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\tthat.imJoin = res.imJoin;\r\n\t\t\t\tthat.title = product.name;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\tclearInterval(interval);\r\n\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\tthat.nowtime = that.nowtime + 1;\r\n\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t}, 1000);\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: product.name\r\n\t\t\t\t});\r\n\t\t\t\tthat.kfurl = '/pages/kefu/index?bid='+res.product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded({title:res.product.name,pic:res.product.pic,callback:function(){that.sharecallback();}});\r\n\t\t\t});\r\n\t\t},\r\n\t\tsharecallback:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post(\"ApiKanjia/share\", {proid: that.product.id}, function (res) {\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t//setTimeout(function () {\r\n\t\t\t\t\t//\tthat.getdata();\r\n\t\t\t\t\t//}, 1000);\r\n\t\t\t\t} else if (res.status == 0) {\r\n\t\t\t\t\t//dialog(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    swiperChange: function (e) {\r\n      var that = this;\r\n      that.current = e.detail.current;\r\n    },\r\n    getdjs: function () {\r\n      var that = this;\r\n      if (that.product.starttime * 1 > that.nowtime * 1) {\r\n        var totalsec = that.product.starttime * 1 - that.nowtime * 1;\r\n      } else {\r\n        var totalsec = that.product.endtime * 1 - that.nowtime * 1;\r\n      }\r\n      if (totalsec <= 0) {\r\n        that.djsday = '00';\r\n        that.djshour = '00';\r\n        that.djsmin = '00';\r\n        that.djssec = '00';\r\n      } else {\r\n        var date = Math.floor(totalsec / 86400);\r\n        var houer = Math.floor((totalsec - date * 86400) / 3600);\r\n        var min = Math.floor((totalsec - date * 86400 - houer * 3600) / 60);\r\n        var sec = totalsec - date * 86400 - houer * 3600 - min * 60;\r\n        var djsday = (date < 10 ? '0' : '') + date;\r\n        var djshour = (houer < 10 ? '0' : '') + houer;\r\n        var djsmin = (min < 10 ? '0' : '') + min;\r\n        var djssec = (sec < 10 ? '0' : '') + sec;\r\n        that.djsday = djsday;\r\n        that.djshour = djshour;\r\n        that.djsmin = djsmin;\r\n        that.djssec = djssec;\r\n      }\r\n    },\r\n    joinin: function (e) {\r\n      var type = e.currentTarget.dataset.type;\r\n      var that = this;\r\n      var proid = that.product.id;\r\n      app.goto('join?proid=' + proid);\r\n    },\r\n    //收藏操作\r\n    addfavorite: function () {\r\n      var that = this;\r\n      var proid = that.product.id;\r\n\t\t\tapp.showLoading('加载中');\r\n      app.post('ApiKanjia/addfavorite', {proid: proid,type: 'kanjia'}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 1) {\r\n          that.isfavorite = !that.isfavorite;\r\n        }\r\n        app.success(data.msg);\r\n      });\r\n    },\r\n    tabClick: function (e) {\r\n      this.tabnum = e.currentTarget.dataset.num;\r\n    },\r\n    shareClick: function () {\r\n      this.sharetypevisible = true;\r\n    },\r\n    handleClickMask: function () {\r\n      this.sharetypevisible = false;\r\n    },\r\n    showPoster: function () {\r\n      var that = this;\r\n      that.showposter = true;\r\n      that.sharetypevisible = false;\r\n\t\t\tapp.showLoading('努力生成中');\r\n      app.post('ApiKanjia/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.alert(data.msg);\r\n        } else {\r\n          that.posterpic = data.poster;\r\n        }\r\n      });\r\n    },\r\n    posterDialogClose: function () {\r\n      this.showposter = false;\r\n    },\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/kanjia/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/activity/kanjia/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n<style>\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.kanjia_title{ width:100%;height:110rpx;display:flex;align-items:center;}\r\n.kanjia_title .f1{height:110rpx;background: linear-gradient(90deg,#FF3143,#FE6748);display:flex;flex-direction:column;flex:1;justify-content:center;position:relative;padding-left:20rpx}\r\n.kanjia_title .f1 .t1{font-size:24rpx;color:#fff}\r\n.kanjia_title .f1 .t2{font-size:24rpx;color:#fff;}\r\n.kanjia_title .f1 .t3{background:rgba(255, 255, 255,0.9);height:46prx;line-height:46rpx;border-radius:23rpx;padding:0 20rpx;color:#FF3143;font-size:24rpx;position:absolute;right:10rpx;top:30rpx;}\r\n.kanjia_title .f3{width:280rpx;height:110rpx;background:#FFDBDF;color:#333;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.kanjia_title .f3 .t2{color:#FF3143}\r\n.kanjia_title .djsspan{font-size:22rpx;border-radius:8rpx;background:#FF3143;color:#fff;text-align:center;padding:4rpx 8rpx;margin:0 4rpx}\r\n\r\n.header {width: 100%;padding: 0 3%;background: #fff;display:flex;flex-direction:column}\r\n.header .title {padding: 10px 0px;line-height:44rpx;font-size:32rpx;display:flex;}\r\n.header .title .lef{display:flex;flex-direction:column;justify-content: center;flex:1;color:#222222;font-weight:bold}\r\n.header .title .lef .t2{ font-size:26rpx;color:#999;padding-top:10rpx;font-weight:normal}\r\n.header .title .share{width:88rpx;height:88rpx;padding-left:20rpx;border-left:0 solid #f5f5f5;text-align:center;font-size:24rpx;color:#222;display:flex;flex-direction:column;align-items:center}\r\n.header .title .share image{width:32rpx;height:32rpx;margin-bottom:4rpx}\r\n.header .sales_stock{display:flex;justify-content:space-between;height:40rpx;line-height:40rpx;margin-bottom:20rpx;font-size:24rpx;color:#777777}\r\n\r\n.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 80rpx; line-height: 80rpx; padding: 0 3%; color: #505050; }\r\n.choose .f2{ width: 40rpx; height: 40rpx;}\r\n\r\n.joinlist{width:100%;display:flex;align-items:center;margin-top:20rpx;background: #fff;padding:30rpx 3%;}\r\n.joinlist .t1{margin-left: -10rpx;height:40rpx;}\r\n.joinlist .t1 image{width:50rpx;height:50rpx;border-radius: 50%;border:2px solid #fff;}\r\n.joinlist .t2{font-size:24rpx;color:#787878}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}\r\n.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}\r\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:80rpx;position:relative}\r\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\r\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\r\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;display:flex;align-items:center;justify-content:center}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839366255\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}