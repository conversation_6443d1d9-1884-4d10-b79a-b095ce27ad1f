{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/index.vue?d094", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/index.vue?b8ea", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/index.vue?1a6c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/index.vue?44d5", "uni-app:///activity/collage/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/index.vue?efd8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/index.vue?75b1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "bid", "pics", "pagenum", "st", "datalist", "nomore", "nodata", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "toDetail", "changetab", "uni", "scrollTop", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Dv1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAb;QAAAG;QAAAD;MAAA;QACAU;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;QACAD;MACA;QACA;UACAA;QACA;UACAA;QACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/collage/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/collage/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3e1a9938&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/collage/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3e1a9938&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.pics.length : null\n  var g1 = _vm.isload ? _vm.clist.length : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.t(\"color1\")\n        var m1 = _vm.t(\"color1\")\n        var m2 = _vm.t(\"color1\")\n        var m3 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<swiper v-if=\"pics.length>0\" class=\"swiper\" :indicator-dots=\"pics[1]?true:false\" autoplay=\"true\" :interval=\"5000\">\r\n\t\t\t\t<block v-for=\"(item, index) in pics\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<image :src=\"item\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<scroll-view class=\"category\" scroll-x=\"true\" v-if=\"clist.length>0\">\r\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"item\" @tap=\"changetab\" :data-st=\"item.id\">\r\n\t\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"item\" @tap=\"changetab\" data-st>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/all.png'\" style=\"border-radius:0\"></image>\r\n\t\t\t\t\t<text class=\"t1\">全部</text>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view class=\"datalist\">\r\n\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view @tap=\"toDetail\" :data-id=\"item.id\" :data-teamid=\"item.teamid\"  :data-type=\"item.collage_type\"  class=\"collage-product\">\r\n\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t<image :src=\"item.pic\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</view> \r\n\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t<view class=\"p1\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t\t<view class=\"p2-1\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t\t<view class=\"t1\">已拼成<text style=\"font-size:32rpx;color:#f40;padding:0 2rpx;\">{{item.sales}}</text>件</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\" :style=\"{borderColor:t('color1'),color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<text class=\"x1\" v-if=\"!item.collage_type\">{{item.teamnum}}人团</text>\r\n\t\t\t\t\t\t\t\t<text class=\"x2\" :style=\"{backgroundColor:t('color1')}\">去拼团</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t</view>\r\n\t\t<button class=\"covermy\" @tap=\"goto\" data-url=\"orderlist\">我的拼团</button>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tbid:'',\r\n\t\t\tpics: [],\r\n      pagenum: 1,\r\n      st: '',\r\n      datalist: [],\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.bid = this.opt.bid || '';\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  methods: {\r\n\t\tgetdata: function (loadmore) {\r\n\t\t\tvar that = this;\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar pagenum = that.pagenum;\r\n      var st = that.st;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n      that.nomore = false;\r\n      app.post('ApiCollage/index', {bid:that.bid,st: st,pagenum: pagenum}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.datalist;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.pics = res.pics;\r\n\t\t\t\t\tthat.clist = res.clist;\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n\t\t\t\tthat.loaded();\r\n      });\r\n\t\t},\r\n\t\ttoDetail:function(e){\r\n\t\t\tvar type = e.currentTarget.dataset.type;\r\n\t\t\tvar teamid = e.currentTarget.dataset.teamid;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tif(!type){\r\n\t\t\t\tapp.goto('product?id='+id);\r\n\t\t\t}else{\r\n\t\t\t\tif(teamid >0){\r\n\t\t\t\t\tapp.goto('/pagesB/collage/jtteam?teamid='+teamid);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.goto('product?id='+id);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n    changetab: function (e) {\r\n      var st = e.currentTarget.dataset.st;\r\n      this.pagenum = 1;\r\n\t\t\tthis.st = st;\r\n      this.datalist = [];\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n    },\r\n\t}\r\n}\r\n</script>\r\n<style>\r\n.container{background:#f4f4f4}\r\n.swiper {width:94%;margin:0 3%;height: 350rpx;margin-top: 20rpx;border-radius:20rpx;overflow:hidden}\r\n.swiper image {width: 100%;height: 350rpx;overflow: hidden;}\r\n\r\n.category{width:94%;margin:0 3%;padding-top: 10px;padding-bottom: 10px;flex-direction:row;white-space: nowrap; display:flex;}\r\n.category .item{width: 150rpx;display: inline-block; text-align: center;}\r\n.category .item image{width: 80rpx;height: 80rpx;margin: 0 auto;border-radius: 50%;}\r\n.category .item .t1{display: block;color: #666;}\r\n\r\n.datalist{width:94%;margin:0 3%;}\r\n.collage-product {display:flex;height:220rpx; background: #fff; padding:20rpx 20rpx;margin-top: 20rpx;border-radius:20rpx}\r\n.collage-product .product-pic {width: 180rpx;height: 180rpx; background: #ffffff;overflow:hidden}\r\n.collage-product .product-pic image{width: 100%;height:180rpx;}\r\n.collage-product .product-info {padding: 5rpx 10rpx;flex:1}\r\n.collage-product .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.collage-product .product-info .p2{font-size: 32rpx;height:40rpx;line-height: 40rpx}\r\n.collage-product .product-info .p2 .t1{color: #f40;}\r\n.collage-product .product-info .p2 .t2 {margin-left: 10rpx;font-size: 26rpx;color: #888;text-decoration: line-through;}\r\n.collage-product .product-info .p3{font-size: 24rpx;height:50rpx;line-height:50rpx;overflow:hidden;display:flex;}\r\n.collage-product .product-info .p3 .t1{color:#aaa;font-size:24rpx;flex:1}\r\n.collage-product .product-info .p3 .t2{height: 50rpx;line-height: 50rpx;overflow: hidden;border: 1px #FF3143 solid;border-radius:10rpx;}\r\n.collage-product .product-info .p3 .t2 .x1{padding: 10rpx 24rpx;}\r\n.collage-product .product-info .p3 .t2 .x2{padding: 14rpx 24rpx;background: #FF3143;color:#fff;}\r\n\r\n.covermy{position:absolute;z-index:99999;cursor:pointer;display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;z-index:9999;top:260rpx;right:0;color:#fff;background-color:rgba(17,17,17,0.3);width:140rpx;height:60rpx;font-size:26rpx;border-radius:30rpx 0px 0px 30rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839366338\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}