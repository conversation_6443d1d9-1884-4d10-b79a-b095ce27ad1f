{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/product.vue?f560", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/product.vue?8c0f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/product.vue?c447", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/product.vue?fb54", "uni-app:///activity/collage/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/product.vue?9b5a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/product.vue?2740"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "indexurl", "tabnum", "buydialogHidden", "num", "teamCount", "sysset", "shopset", "isfavorite", "btntype", "ggselected", "gui<PERSON><PERSON>", "gui<PERSON>", "ks", "gwcnum", "nodata", "product", "userinfo", "current", "pagecontent", "business", "commentcount", "commentlist", "nowtime", "teamList", "teamid", "sharetypevisible", "showposter", "posterpic", "kfurl", "tjdatalist", "showtoptabbar", "toptabbar_show", "toptabbar_index", "scrollToViewId", "scrollTop", "scrolltab0Height", "scrolltab1Height", "scrolltab2Height", "scrolltab3Height", "test", "pre_url", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "jt_st", "showJt", "jt_teammid", "teampid", "selfList", "onLoad", "onShow", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "that", "app", "id", "setInterval", "uni", "setTimeout", "view0", "size", "rect", "scrollOffset", "console", "view1", "view2", "swiper<PERSON><PERSON>e", "getdjs", "totalsec", "buydialogShow", "buydialogChange", "ggchange", "tobuy", "gwcplus", "gwcminus", "gwcinput", "addfavorite", "proid", "type", "tabClick", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata", "sharelink", "changetoptab", "scroll", "showgg1Dialog", "closegg1Dialog", "showgg2Dialog", "closegg2Dialog", "getTgdjs", "changeKh", "handleShowJt", "toJtTeam"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrPA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACibz1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACA;MACAD;MACAG;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UACA;QACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAG;UACAH;UACAA;QACA;QACA;QACA;UACAA;UACAG;YACAH;YACAA;UACA;QACA;QACAI;UACAX;QACA;QACAO;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAAP;UAAAC;QAAA;QAEAW;UACA;UACAC;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;YACAC;YACAV;UACA;UACA;UACAW;YACAJ;YAAA;YACAC;YAAA;YACAC;UACA;YACAC;YACAV;UACA;UACA;UACAY;YACAL;YAAA;YACAC;YAAA;YACAC;UACA;YACAC;YACAV;UACA;QACA;MACA;IACA;IACAa;MACA;MACAb;IACA;IACAc;MACA;MACA;MACA;QACA;UACA;UACA;UACA;YACAC;UACA;UACA;YACAf;UACA;YACA;YACA;YACA;YACA;YACAA;UACA;UACAA;UACAA;QACA;MACA;MACA;QACA;QACA;QACA;UACAe;QACA;QACA;UACAf;QACA;UACA;UACA;UACA;UACA;UACAA;QACA;QACAA;QACAA;MACA;IACA;IACA;IACAgB;MACA;MACA;QACA;MACA;MAEA;QACA;QACA;UACAf;UACA;QACA;MACA;MACA;MACA;MACA;IACA;IACAgB;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACArE;MACA;MACA;MACA;IACA;IACAsE;MACA;MACA;MACA;MACA;MACA;MACA;MACAlB;IACA;IACA;IACAmB;MACA;MACA;MACA;QACAnB;QACA;MACA;MACA;IACA;IACA;IACAoB;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAtB;MACAA;QAAAuB;QAAAC;MAAA;QACAxB;QACA;UACAD;QACA;QACAC;MACA;IACA;IACAyB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA7B;MACAA;MACAC;MACAA;QAAAuB;MAAA;QACAvB;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IACA8B;MACA;MAAA;IACA;IACAC;MACA9B;MACA;IACA;IACA+B;MACA;MACA5B;QACA6B;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACAhC;UACA;QACA;MACA;IACA;IACAkC;MACA;MACA;MACA;MACA;MACA5B;IACA;IACA6B;MACA;MACA;MACA;MACA;QACAvC;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAwC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;QAAA;QACA5C;QACAA;QACAA;QACAA;MACA;QACA;UAAA;UACAA;UACA;QACA;UAAA;UACAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;MACA;IACA;IACA6C;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MAEA9C;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC14BA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/collage/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/collage/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=050baa81&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/collage/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=050baa81&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var g0 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.tjdatalist.length\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    g0 > 0 &&\n    _vm.toptabbar_index == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1 && g0 > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g1 = _vm.isload ? _vm.product.pics.length : null\n  var g2 =\n    _vm.isload && _vm.product.collage_type == 1\n      ? _vm.product.bobaolist && _vm.product.bobaolist.length > 0\n      : null\n  var l0 =\n    _vm.isload && _vm.product.collage_type == 1\n      ? _vm.__map(_vm.teamList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g3 = _vm.teamList.length\n          return {\n            $orig: $orig,\n            g3: g3,\n          }\n        })\n      : null\n  var m8 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g4 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.commentlist.length\n      : null\n  var m9 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var g5 = _vm.isload ? _vm.tjdatalist.length : null\n  var l1 =\n    _vm.isload && g5 > 0\n      ? _vm.__map(_vm.tjdatalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.t(\"color1\")\n          var m12 = !item.collage_type ? _vm.t(\"color1rgb\") : null\n          var m13 = !item.collage_type ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m11: m11,\n            m12: m12,\n            m13: m13,\n          }\n        })\n      : null\n  var m14 =\n    _vm.isload && _vm.product.status == 1 && !_vm.product.collage_type\n      ? _vm.t(\"color2\")\n      : null\n  var m15 =\n    _vm.isload && _vm.product.status == 1 && !_vm.product.collage_type\n      ? _vm.t(\"color1\")\n      : null\n  var m16 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !!_vm.product.collage_type &&\n    _vm.product.teamid > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m17 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !!_vm.product.collage_type &&\n    !(_vm.product.teamid > 0) &&\n    _vm.product.is_start == 1 &&\n    _vm.product.is_end == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m18 = _vm.isload && _vm.btntype == 1 ? _vm.t(\"color2\") : null\n  var m19 = _vm.isload && _vm.btntype == 2 ? _vm.t(\"color1\") : null\n  var m20 = _vm.isload && _vm.btntype == 3 ? _vm.t(\"color1\") : null\n  var m21 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m22 =\n    _vm.isload && _vm.sharetypevisible && !(m21 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m23 =\n    _vm.isload && _vm.sharetypevisible && !(m21 == \"app\") && !(m22 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m24 = _vm.isload && _vm.showJt && _vm.jt_st == 1 ? _vm.t(\"color1\") : null\n  var m25 =\n    _vm.isload && _vm.showJt && !(_vm.jt_st == 1) ? _vm.t(\"color1\") : null\n  var m26 = _vm.isload && _vm.showJt ? _vm.t(\"color1\") : null\n  var m27 = _vm.isload && _vm.showJt && _vm.jt_st == 2 ? _vm.t(\"color1\") : null\n  var m28 =\n    _vm.isload && _vm.showJt && !(_vm.jt_st == 2) ? _vm.t(\"color1\") : null\n  var m29 = _vm.isload && _vm.showJt ? _vm.t(\"color1\") : null\n  var m30 =\n    _vm.isload &&\n    _vm.showJt &&\n    (_vm.jt_st == 1 || _vm.jt_st == 2) &&\n    (_vm.jt_st == 1 || _vm.jt_st == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m31 =\n    _vm.isload &&\n    _vm.showJt &&\n    (_vm.jt_st == 1 || _vm.jt_st == 2) &&\n    _vm.jt_st == 2\n      ? _vm.t(\"color2\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        g0: g0,\n        m6: m6,\n        m7: m7,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        m8: m8,\n        g4: g4,\n        m9: m9,\n        m10: m10,\n        g5: g5,\n        l1: l1,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"toptabbar_tab\" v-if=\"showtoptabbar==1 && toptabbar_show==1\">\r\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==0?'on':''\" :style=\"{color:toptabbar_index==0?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"0\">商品<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==1?'on':''\" :style=\"{color:toptabbar_index==1?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"1\">评价<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==2?'on':''\" :style=\"{color:toptabbar_index==2?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"2\">详情<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t<view class=\"item\" v-if=\"tjdatalist.length > 0\" :class=\"toptabbar_index==3?'on':''\" :style=\"{color:toptabbar_index==3?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"3\">推荐<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t</view>\r\n\r\n\t\t<scroll-view @scroll=\"scroll\" :scrollIntoView=\"scrollToViewId\" :scrollTop=\"scrollTop\" :scroll-y=\"true\" style=\"height:100%;overflow:scroll\">\r\n\t\t\r\n\t\t<view id=\"scroll_view_tab0\">\r\n\t\t\r\n\t\t\t<view class=\"swiper-container\">\r\n\t\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<view class=\"imageCount\">{{current+1}}/{{product.pics.length}}</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<block v-if=\"!product.collage_type\">\r\n\t\t\t\t<view class=\"collage_title\" >\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t\t<view class=\"x1\">￥</view>\r\n\t\t\t\t\t\t\t<view class=\"x2\">{{product.sell_price}}</view>\r\n\t\t\t\t\t\t\t<view class=\"x3\" >{{product.teamnum}}人团</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t2\">单买价：￥{{product.market_price}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">已拼{{product.sales}}件</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view style=\"background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0\" v-if=\"shopset.detail_guangao1\">\r\n\t\t\t\t\t<image :src=\"shopset.detail_guangao1\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"shopset.detail_guangao1\" @tap=\"showgg1Dialog\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-popup id=\"gg1Dialog\" ref=\"gg1Dialog\" type=\"dialog\" v-if=\"shopset.detail_guangao1 && shopset.detail_guangao1_t\">\r\n\t\t\t\t\t<image :src=\"shopset.detail_guangao1_t\" @tap=\"previewImage\" :data-url=\"shopset.detail_guangao1_t\" class=\"img\" mode=\"widthFix\" style=\"width:600rpx;height:auto;border-radius:10rpx;\"/>\r\n\t\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg1Dialog\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n        \r\n\t\t\t\t<view class=\"header\"> \r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view class=\"lef\">\r\n\t\t\t\t\t\t<text>{{product.name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/share.png'\"></image>\r\n\t\t\t\t\t\t\t<text>分享</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n          <view v-if=\"product.mangfan_status==1\" :style=\"{'color':product.mangfan_text_color,'lineHeight':'60rpx'}\">{{product.mangfan_text}}</view>\r\n\t\t\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\">{{product.sellpoint}}</view>\r\n\t\t\t\t\t<view class=\"sales_stock\">\r\n\t\t\t\t\t\t<view class=\"f2\">库存：{{product.stock}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n        <view v-if=\"product.teaminteam_status && shopset.team_in_team && shopset.teaminteam_word\" class=\"team_in_team\" :style=\"'background:'+shopset.teaminteam_bgcolor+';color:'+shopset.teaminteam_color\">{{shopset.teaminteam_word}}</view>\r\n\t\t\t</block>\r\n\t\t\t<!--阶梯拼团-->\r\n\t\t\t<block v-if=\" product.collage_type ==1\">\r\n\t\t\t\t<view class=\"bobaobox\" v-if=\"product.bobaolist && product.bobaolist.length>0\">\r\n\t\t\t\t\t<swiper style=\"position:relative;height:54rpx;width:450rpx;\" autoplay=\"true\" :interval=\"5000\"\r\n\t\t\t\t\t\tvertical=\"true\">\r\n\t\t\t\t\t\t<swiper-item v-for=\"(item, index) in product.bobaolist\" :key=\"index\" @tap=\"goto\"\r\n\t\t\t\t\t\t\t:data-url=\"item.tourl\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t<image :src=\"item.headimg\"\r\n\t\t\t\t\t\t\t\tstyle=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\">\r\n\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t<view style=\"width:400rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">\r\n\t\t\t\t\t\t\t\t<text style=\"padding-right:2px\">{{item.nickname}}</text>\r\n\t\t\t\t\t\t\t\t<text style=\"padding-right:4px\">{{item.showtime}}</text>\r\n\t\t\t\t\t\t\t\t<text style=\"padding-right:2px\" >发起拼团</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</swiper>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"seckill_title\" >\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/tghd.png'\" class=\"f0\"/>\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{product.sell_price}}\r\n\t\t\t\t\t\t\t<text class=\"x2\">￥{{product.market_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t2\" >火爆团购中</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f3\" >\r\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"product.is_start ==0\">未开始</view>\r\n\t\t\t\t\t\t<view class=\"t1\" v-else>距团购结束还剩</view>\r\n\t\t\t\t\t\t<view class=\"t2\" id=\"djstime\"><text class=\"djsspan\">{{djshour}}</text> : <text class=\"djsspan\">{{djsmin}}</text> : <text class=\"djsspan\">{{djssec}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"jtsales\">\r\n\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t浏览量：{{product.view_num}}次\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t成交量：{{product.sales}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"jtdata\" >\r\n\t\t\t\t\t<view class=\"item flex-bt\" v-for=\"(item,index) in product.jieti_data\">\r\n\t\t\t\t\t\t<view>满{{item.teamnum}}人<text style=\"color: red;\">团长送{{item.goodsname}}</text></view>\r\n\t\t\t\t\t\t<view>课时：{{item.goodsnum}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t<scroll-view :scroll-y=\"true\" class=\"jtcontent\">\r\n\t\t\t\t\t<view class=\"teamlist\"  style=\"margin-top: 0rpx;\" v-for=\"(item, index) in teamList\" :key=\"index\" v-if=\"teamList.length > 0\">\r\n\t\t\t\t\t\t<view class=\"label flex\" >\r\n\t\t\t\t\t\t\t<view style=\"background: red;padding: 8rpx 6rpx;border-radius: 20rpx;margin-right: 10rpx;\"></view>\r\n\t\t\t\t\t\t\t{{item.num}}人在拼单，可直接参与\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view  class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item2,index) in item.memberlist\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.headimg\" class=\"img\" ></image>\r\n\t\t\t\t\t\t\t\t</block> \r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-if=\"product.is_start ==1 && product.is_end ==0\">\r\n\t\t\t\t\t\t\t\t\t<button class=\"f3\" @tap=\"handleShowJt\" :data-id=\"item.id\">去参团</button>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<button class=\"f3\" style=\"background: #ccc;\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"product.is_start ==0\">未开始</text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"product.is_end ==1\">已结束</text>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</block>\r\n\t\t\t<!--阶梯拼团end-->\r\n      <view class=\"teamlist\" v-if=\"selfList && !product.collage_type\">\r\n      \t<scroll-view :scroll-y=\"true\" class=\"content\">\r\n      \t\t<view v-for=\"(item, index) in selfList\" :key=\"index\" class=\"item\">\r\n      \t\t\t<view class=\"f1\">\r\n      \t\t\t\t<image :src=\"item.headimg\"></image>\r\n      \t\t\t\t<view class=\"t1\">{{item.nickname}}</view>\r\n      \t\t\t</view>\r\n      \t\t\t<view class=\"f2\">\r\n      \t\t\t\t<view class=\"t1\" v-if=\"!item.collage_type\">还差{{item.teamnum - item.num}}人拼成</view>\r\n      \t\t\t\t<view class=\"t2\">剩余{{item.djs}}</view>\r\n      \t\t\t</view>\r\n            <button class=\"f3\"  @tap.stop=\"goto\" :data-url=\"'/activity/collage/team?teamid=' + item.id + '&teampid=' + item.id\">去分享</button>\r\n      \t\t</view>\r\n      \t</scroll-view>\r\n      </view>\r\n\t\t\t<view class=\"teamlist\" v-if=\"teamCount > 0 && !product.collage_type\">\r\n\t\t\t\t<view class=\"label\" >{{teamCount}}人在拼单，可直接参与</view>\r\n\t\t\t\t<scroll-view :scroll-y=\"true\" class=\"content\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in teamList\" :key=\"index\" class=\"item\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<image :src=\"item.headimg\"></image>\r\n\t\t\t\t\t\t\t<view class=\"t1\">{{item.nickname}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<view class=\"t1\" v-if=\"!item.collage_type\">还差{{item.teamnum - item.num}}人拼成</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\">剩余{{item.djs}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"f3\" @tap=\"buydialogShow\" data-btntype=\"3\" :data-teamid=\"item.id\">去拼单</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\r\n\t\t\t\r\n\t\t\t<view style=\"width:100%;height:auto;padding:20rpx 0 0\" v-if=\"shopset.detail_guangao2\">\r\n\t\t\t\t<image :src=\"shopset.detail_guangao2\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"shopset.detail_guangao2\" @tap=\"showgg2Dialog\"/>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"gg2Dialog\" ref=\"gg2Dialog\" type=\"dialog\" v-if=\"shopset.detail_guangao2 && shopset.detail_guangao2_t\">\r\n\t\t\t\t<image :src=\"shopset.detail_guangao2_t\" @tap=\"previewImage\" :data-url=\"shopset.detail_guangao2_t\" class=\"img\" mode=\"widthFix\" style=\"width:600rpx;height:auto;border-radius:10rpx;\"/>\r\n\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg2Dialog\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t</view>\r\n\r\n\t\t<view id=\"scroll_view_tab1\">\r\n\r\n\t\t\t<view class=\"commentbox\" v-if=\"shopset.comment==1 && commentcount > 0\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评度 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" :src=\"pre_url+'/static/img/arrowright.png'\" /></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"comment\">\r\n\t\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\r\n\t\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"pre_url+'/static/img/star' + (commentlist[0].score>item2?'2native':'') + '.png'\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\r\n\r\n\t\t</view>\r\n\r\n\t\t<view id=\"scroll_view_tab2\">\r\n\r\n\t\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\r\n\t\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"p4\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\" data-opentype=\"reLaunch\">进入店铺</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t\t<view class=\"detail\">\r\n\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t</view>\r\n\t\t\r\n\t\t<view id=\"scroll_view_tab3\">\r\n\r\n\t\t\t<view v-if=\"tjdatalist.length > 0\">\r\n\t\t\t\t<view class=\"xihuan\">\r\n\t\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t\t<view class=\"xihuan-text\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/xihuan.png'\" class=\"img\"/>\r\n\t\t\t\t\t\t<text class=\"txt\">为您推荐</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"prolist\">\r\n\t\t\t\t\t<view class=\"dp-collage-item\">\r\n\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in tjdatalist\" :style=\"'width:49%;margin-right:'+(index%2==0?'2%':0)\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/collage/product?id='+item.id\">\r\n\t\t\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t\t<view class=\"p1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"p2-1\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"!item.collage_type\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\">{{item.teamnum}}人团</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已拼成{{item.sales}}件</text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:110rpx;box-sizing:content-box\" class=\"notabbarbot\"></view>\r\n\r\n\t\t</scroll-view>\r\n\r\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"cart flex-col flex-x-center flex-y-center\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"cart flex-col flex-x-center flex-y-center\" v-else open-type=\"contact\" show-message-card=\"true\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t</button>\r\n\t\t\t<view class=\"favorite flex-col flex-x-center flex-y-center\" @tap=\"addfavorite\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\r\n\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tocart\" v-if=\"!product.collage_type\" :style=\"{background:t('color2')}\" @tap=\"buydialogShow\" data-btntype=\"1\"><text>单独购买</text><text>￥{{product.market_price}}</text></view>\r\n\t\t\t\r\n\t\t\t<block v-if=\"!product.collage_type\">\r\n\t\t\t\t<view class=\"tobuy\"  :style=\"{background:t('color1')}\" @tap=\"buydialogShow\" data-btntype=\"2\">\r\n\t\t\t\t\t<text>发起拼团</text>\r\n\t\t\t\t\t<text>￥{{product.sell_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<block v-if=\"product.teamid > 0\">\r\n\t\t\t\t\t<view class=\"tobuy tobuy_one\" @tap=\"toJtTeam\"   :style=\"{background:t('color1')}\" >\r\n\t\t\t\t\t\t<text >我的团</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"tobuy tobuy_one\"  :style=\"{background:t('color1')}\" @tap=\"buydialogShow\" data-btntype=\"2\" v-if=\"product.is_start ==1 && product.is_end ==0\">\r\n\t\t\t\t\t\t<text >我要开团</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tobuy tobuy_one\"  style=\"background-color: #ccc;\"  data-btntype=\"2\" v-if=\"product.is_start ==0 || product.is_end ==1\">\r\n\t\t\t\t\t\t<text v-if=\"product.is_start ==0\">未开始</text>\r\n\t\t\t\t\t\t<text v-if=\"product.is_end ==1\">已结束</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t</block>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view :hidden=\"buydialogHidden\">\r\n\t\t\t<view class=\"buydialog-mask\">\r\n\t\t\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t<image :src=\"guigelist[ks].pic?guigelist[ks].pic:product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"guigelist[ks].pic?guigelist[ks].pic:product.pic\"></image>\r\n\t\t\t\t\t\t<!-- <text class=\"name\">{{product.name}}</text> -->\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"btntype==1\"><text class=\"t1\">￥</text>{{guigelist[ks].market_price}}</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-else><text class=\"t1\">￥</text>{{guigelist[ks].sell_price}} <text v-if=\"guigelist[ks].market_price > guigelist[ks].sell_price\" class=\"t2\">￥{{guigelist[ks].market_price}}</text></view>\r\n\t\t\t\t\t\t<view class=\"choosename\">已选规格: {{guigelist[ks].name}}</view>\r\n\t\t\t\t\t\t<view class=\"stock\">剩余{{guigelist[ks].stock}}件</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"buynum flex flex-y-center\" v-if=\"!product.collage_type\">\r\n\t\t\t\t\t\t<view class=\"flex1\">购买数量：</view>\r\n\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t<view class=\"minus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" @tap=\"gwcminus\"/></view>\r\n\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t\t\t<view class=\"plus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\" @tap=\"gwcplus\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<block v-if=\"btntype==1\">\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{background:t('color2')}\" @tap=\"tobuy\" data-type=\"1\">确定</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"btntype==2\">\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{background:t('color1')}\" @tap=\"tobuy\" data-type=\"2\">下一步</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"btntype==3\">\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{background:t('color1')}\" @tap=\"tobuy\" data-type=\"3\">确 定</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- \t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 阶梯拼团 -->\r\n\t\t<view v-if=\"showJt\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleShowJt\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"jt_tab\" >\r\n\t\t\t\t\t\t<view class=\"x1\" @tap=\"changeKh\"  data-type=\"1\" :style=\"{backgroundColor:jt_st==1?t('color1'):'',color:jt_st==1?'#fff':t('color1'),borderColor:t('color1')}\">老客户</view>\r\n\t\t\t\t\t\t<view class=\"x2\" @tap=\"changeKh\" data-type=\"2\" :style=\"{backgroundColor:jt_st==2?t('color1'):'',color:jt_st==2?'#fff':t('color1'),borderColor:t('color1')}\">新客户</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btnlist\" v-if=\"jt_st ==1 || jt_st==2\">\r\n\t\t\t\t\t\t<view class=\"tocart\" v-if=\"jt_st ==1 ||jt_st ==2\"  :style=\"{background:t('color1')}\"  @tap=\"buydialogShow\" data-btntype=\"2\"><text>我要开团</text></view>\r\n\t\t\t\t\t\t<view class=\"tobuy\" v-if=\"jt_st ==2\" :style=\"{background:t('color2')}\" @tap=\"buydialogShow\" data-btntype=\"3\" :data-teamid=\"jt_teammid\"><text>我要参团</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<view style=\"display:none\">{{test}}</view>\r\n</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tindexurl:app.globalData.indexurl,\r\n\t\t\ttabnum: 1,\r\n      buydialogHidden: true,\r\n      num: 1,\r\n\t\t\tteamCount:0,\r\n\t\t\tsysset:{},\r\n\t\t\tshopset:{},\r\n      isfavorite: false,\r\n      btntype: 1,\r\n      ggselected: [],\r\n\t\t\tguigedata:[],\r\n\t\t\tguigelist:[],\r\n      ks: '',\r\n      gwcnum: 1,\r\n      nodata: 0,\r\n\t\t\tproduct:{},\r\n      userinfo: [],\r\n      current: 0,\r\n      pagecontent: \"\",\r\n\t\t\tbusiness:{},\r\n\t\t\tcommentcount:0,\r\n\t\t\tcommentlist:[],\r\n      nowtime: \"\",\r\n      teamList: [],\r\n      teamid: \"\",\r\n      sharetypevisible: false,\r\n      showposter: false,\r\n      posterpic: \"\",\r\n\t\t\tkfurl:'',\r\n\t\t\ttjdatalist:[],\r\n\t\t\tshowtoptabbar:0,\r\n\t\t\ttoptabbar_show:0,\r\n\t\t\ttoptabbar_index:0,\r\n      scrollToViewId: \"\",\r\n\t\t\tscrollTop:0,\r\n\t\t\tscrolltab0Height:0,\r\n\t\t\tscrolltab1Height:0,\r\n\t\t\tscrolltab2Height:0,\r\n\t\t\tscrolltab3Height:0,\r\n\t\t\ttest:'',\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tdjshour:'00',\r\n\t\t\tdjsmin:'00',\r\n\t\t\tdjssec:'00',\r\n\t\t\tjt_st:0,\r\n\t\t\tshowJt:false,\r\n\t\t\tjt_teammid:0,\r\n      teampid:0,\r\n      selfList:''\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n    this.teampid = this.opt.teampid || 0;\r\n  },\r\n  onShow:function(){\r\n\t  this.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiCollage/product', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar pagecontent = JSON.parse(res.product.detail);\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.commentcount = res.commentcount;\r\n\t\t\t\tthat.commentlist = res.commentlist;\r\n\t\t\t\tthat.ggselected = res.ggselected;\r\n\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.ks = res.ks;\r\n\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\tthat.product = res.product;\r\n\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tthat.teamCount = res.teamCount;\r\n\t\t\t\tthat.teamList = res.teamList;\r\n\t\t\t\tthat.tjdatalist = res.tjdatalist || [];\r\n\t\t\t\tthat.showtoptabbar = res.showtoptabbar || 0;\r\n\t\t\t\tsetInterval(function () {\r\n\t\t\t\t\tthat.nowtime = that.nowtime + 1;\r\n\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t}, 1000);\r\n\t\t\t\t//阶梯拼团\r\n\t\t\t\tif(that.product && that.product.collage_type ==1){\r\n\t\t\t\t\tthat.getTgdjs();\r\n\t\t\t\t\tsetInterval(function () {\r\n\t\t\t\t\t\tthat.nowtime = that.nowtime + 1;\r\n\t\t\t\t\t\tthat.getTgdjs();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: res.product.name\r\n\t\t\t\t});\r\n\t\t\t\tthat.kfurl = '/pages/kefu/index?bid='+res.product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n        if(res.selfList){\r\n          that.selfList = res.selfList;\r\n        }\r\n\t\t\t\tthat.loaded({title:res.product.name,pic:res.product.pic});\r\n\t\t\t\t\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tlet view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0')\r\n\t\t\t\t\tview0.fields({\r\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\r\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.scrolltab0Height = res.height\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t\tlet view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1')\r\n\t\t\t\t\tview1.fields({\r\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\r\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.scrolltab1Height = res.height\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t\tlet view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2')\r\n\t\t\t\t\tview2.fields({\r\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\r\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.scrolltab2Height = res.height\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t},500)\r\n\t\t\t});\r\n\t\t},\r\n    swiperChange: function (e) {\r\n      var that = this;\r\n      that.current = e.detail.current;\r\n    },\r\n    getdjs: function () {\r\n      var that = this;\r\n      var nowtime = that.nowtime;\r\n      if(that.selfList){\r\n        for (var i in that.selfList) {\r\n          var thisteam = that.selfList[i];\r\n          var totalsec = thisteam.createtime * 1 + thisteam.teamhour * 3600 - nowtime * 1;\r\n          if(thisteam.collage_type && thisteam.collage_type ==1 ){\r\n            totalsec = thisteam.endtime * 1  - nowtime * 1;\r\n          }\r\n          if (totalsec <= 0) {\r\n            that.selfList[i].djs = '00时00分00秒';\r\n          } else {\r\n            var houer = Math.floor(totalsec / 3600);\r\n            var min = Math.floor((totalsec - houer * 3600) / 60);\r\n            var sec = totalsec - houer * 3600 - min * 60;\r\n            var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n            that.selfList[i].djs = djs;\r\n          }\r\n          that.selfList = that.selfList;\r\n        \tthat.test = Math.random();\r\n        }\r\n      }\r\n      for (var i in that.teamList) {\r\n        var thisteam = that.teamList[i];\r\n        var totalsec = thisteam.createtime * 1 + thisteam.teamhour * 3600 - nowtime * 1;\r\n        if(thisteam.collage_type && thisteam.collage_type ==1 ){\r\n          totalsec = thisteam.endtime * 1  - nowtime * 1;\r\n        }\r\n        if (totalsec <= 0) {\r\n          that.teamList[i].djs = '00时00分00秒';\r\n        } else {\r\n          var houer = Math.floor(totalsec / 3600);\r\n          var min = Math.floor((totalsec - houer * 3600) / 60);\r\n          var sec = totalsec - houer * 3600 - min * 60;\r\n          var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n          that.teamList[i].djs = djs;\r\n        }\r\n        that.teamList = that.teamList;\r\n\t\t\t\tthat.test = Math.random();\r\n      }\r\n    },\r\n    //加入购物车\r\n    buydialogShow: function (e) {\r\n      var btntype = e.currentTarget.dataset.btntype;\r\n      if (btntype == 3) {\r\n        this.teamid = e.currentTarget.dataset.teamid\r\n      }\r\n\r\n\t  if(this.product.collage_type ==1){\r\n\t\t var teamid = this.product.teamid;\r\n\t\t  if(teamid > 0){\r\n\t\t\t  app.error('您已参团');\r\n\t\t\t  return;\r\n\t\t  }\r\n\t  }\r\n      this.btntype = btntype;\r\n      this.buydialogHidden = !this.buydialogHidden;\r\n\t  this.showJt = false;\r\n    },\r\n    buydialogChange: function (e) {\r\n      this.buydialogHidden = !this.buydialogHidden;\r\n    },\r\n    //选择规格\r\n    ggchange: function (e) {\r\n      var idx = e.currentTarget.dataset.idx;\r\n      var itemk = e.currentTarget.dataset.itemk;\r\n      var ggselected = this.ggselected;\r\n      ggselected[itemk] = idx;\r\n      var ks = ggselected.join(',');\r\n      this.ggselected = ggselected;\r\n      this.ks = ks;\r\n    },\r\n    tobuy: function (e) {\r\n      var type = e.currentTarget.dataset.type;\r\n      var that = this;\r\n      var ks = that.ks;\r\n      var proid = that.product.id;\r\n      var ggid = that.guigelist[ks].id;\r\n      var num = that.gwcnum;\r\n      app.goto('buy?proid=' + proid + '&ggid=' + ggid + '&num=' + num + '&buytype=' + type + (type == 3 ? '&teamid=' + that.teamid : '') + '&teampid=' + that.teampid);\r\n    },\r\n    //加\r\n    gwcplus: function (e) {\r\n      var gwcnum = this.gwcnum + 1;\r\n      var ggselected = this.ks;\r\n      if (gwcnum > this.guigelist[ggselected].stock) {\r\n        app.error('库存不足');\r\n        return;\r\n      }\r\n      this.gwcnum = this.gwcnum + 1\r\n    },\r\n    //减\r\n    gwcminus: function (e) {\r\n      var gwcnum = this.gwcnum - 1;\r\n      var ggselected = this.ks;\r\n      if (gwcnum <= 0) {\r\n        return;\r\n      }\r\n      this.gwcnum = this.gwcnum - 1\r\n    },\r\n    //输入\r\n    gwcinput: function (e) {\r\n      var ggselected = this.ks;\r\n      var gwcnum = parseInt(e.detail.value);\r\n      if (gwcnum < 1) return 1;\r\n\r\n      if (gwcnum > this.guigelist[ggselected].stock) {\r\n        return this.guigelist[ggselected].stock;\r\n      }\r\n      this.gwcnum = gwcnum;\r\n    },\r\n    //收藏操作\r\n    addfavorite: function () {\r\n      var that = this;\r\n      var proid = that.product.id;\r\n\t\t\tapp.showLoading('收藏中');\r\n      app.post('ApiCollage/addfavorite', {proid: proid,type: 'collage'}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 1) {\r\n          that.isfavorite = !that.isfavorite\r\n        }\r\n        app.success(data.msg);\r\n      });\r\n    },\r\n    tabClick: function (e) {\r\n      this.tabnum = e.currentTarget.dataset.num;\r\n    },\r\n    shareClick: function () {\r\n      this.sharetypevisible = true;\r\n    },\r\n    handleClickMask: function () {\r\n      this.sharetypevisible = false;\r\n    },\r\n    showPoster: function () {\r\n      var that = this;\r\n      that.showposter = true;\r\n      that.sharetypevisible = false;\r\n      app.showLoading('努力生成中');\r\n\t\t\tapp.post('ApiCollage/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.alert(data.msg);\r\n        } else {\r\n          that.posterpic = data.poster;\r\n        }\r\n      });\r\n    },\r\n    posterDialogClose: function () {\r\n      this.showposter = false;;\r\n    },\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/collage/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/activity/collage/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t},\r\n\t\tchangetoptab:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tthis.scrollToViewId = 'scroll_view_tab'+index;\r\n\t\t\tthis.toptabbar_index = index;\r\n\t\t\tif(index == 0) this.scrollTop = 0;\r\n\t\t\tconsole.log(index);\r\n\t\t},\r\n\t\tscroll:function(e){\r\n\t\t\tvar scrollTop = e.detail.scrollTop;\r\n\t\t\t//console.log(e)\r\n\t\t\tvar that = this;\r\n\t\t\tif (scrollTop > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollTop < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t\tif (scrollTop > 100) {\r\n\t\t\t\tthat.toptabbar_show = true;\r\n\t\t\t}\r\n\t\t\tif(scrollTop < 50) {\r\n\t\t\t\tthat.toptabbar_show = false\r\n\t\t\t}\r\n\t\t\tvar height0 = that.scrolltab0Height;\r\n\t\t\tvar height1 = that.scrolltab0Height + that.scrolltab1Height;\r\n\t\t\tvar height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;\r\n\t\t\t//var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;\r\n\t\t\t//console.log(that.scrolltab0Height);\r\n\t\t\tif(scrollTop >=0 && scrollTop < height0){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab0';\r\n\t\t\t\tthis.toptabbar_index = 0;\r\n\t\t\t}else if(scrollTop >= height0 && scrollTop < height1){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab1';\r\n\t\t\t\tthis.toptabbar_index = 1;\r\n\t\t\t}else if(scrollTop >= height1 && scrollTop < height2){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab2';\r\n\t\t\t\tthis.toptabbar_index = 2;\r\n\t\t\t}else if(scrollTop >= height2){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab3';\r\n\t\t\t\tthis.toptabbar_index = 3;\r\n\t\t\t}\r\n\t\t},\r\n\t\tshowgg1Dialog:function(){\r\n\t\t\tthis.$refs.gg1Dialog.open();\r\n\t\t},\r\n\t\tclosegg1Dialog:function(){\r\n\t\t\tthis.$refs.gg1Dialog.close();\r\n\t\t},\r\n\t\tshowgg2Dialog:function(){\r\n\t\t\tthis.$refs.gg2Dialog.open();\r\n\t\t},\r\n\t\tclosegg2Dialog:function(){\r\n\t\t\tthis.$refs.gg2Dialog.close();\r\n\t\t},\r\n\t\tgetTgdjs:function(){\r\n\t\t\tvar that = this\r\n\t\t\tvar nowtime = that.nowtime*1;\r\n\t\t\tvar starttime = that.product.starttime*1;\r\n\t\t\tvar endtime = that.product.endtime*1;\r\n\t\t\r\n\t\t\tif(endtime < nowtime || nowtime < starttime){ //已结束\r\n\t\t\t\tthat.tuangou_status = 2\r\n\t\t\t\tthat.djshour = '00';\r\n\t\t\t\tthat.djsmin = '00';\r\n\t\t\t\tthat.djssec = '00';\r\n\t\t\t}else{\r\n\t\t\t\tif(starttime > nowtime){ //未开始\r\n\t\t\t\t\tthat.tuangou_status = 0\r\n\t\t\t\t\tvar totalsec = starttime - nowtime;\r\n\t\t\t\t}else{ //进行中\r\n\t\t\t\t\tthat.tuangou_status = 1\r\n\t\t\t\t\tvar totalsec = endtime - nowtime;\r\n\t\t\t\t}\r\n\t\t\t\tvar houer = Math.floor(totalsec/3600);\r\n\t\t\t\tvar min = Math.floor((totalsec - houer *3600)/60);\r\n\t\t\t\tvar sec = totalsec - houer*3600 - min*60\r\n\t\t\t\tvar djs = (houer<10?'0':'')+houer+'时'+(min<10?'0':'')+min+'分'+(sec<10?'0':'')+sec+'秒';\r\n\t\t\t\tvar djshour = (houer<10?'0':'')+houer\r\n\t\t\t\tvar djsmin = (min<10?'0':'')+min\r\n\t\t\t\tvar djssec = (sec<10?'0':'')+sec\r\n\t\t\t\tthat.djshour = djshour;\r\n\t\t\t\tthat.djsmin = djsmin;\r\n\t\t\t\tthat.djssec = djssec;\r\n\t\t\t}\r\n\t\t},\r\n\t\tchangeKh:function(e){\r\n\t\t\tvar jt_status = e.currentTarget.dataset.type;\r\n\t\t\tthis.jt_st = jt_status;\r\n\t\t},\r\n\t\thandleShowJt:function(e){\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tthis.jt_teammid = id?id:'';\r\n\t\t\tthis.showJt = !this.showJt;\r\n\t\t\tthis.jt_st = 0\r\n\t\t},\r\n\t\ttoJtTeam:function(e){\r\n\t\t\tvar id = this.product.teamid;\r\n\t\t\t\r\n\t\t\tapp.goto('/pagesB/collage/jtteam?teamid='+id);\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n<style>\r\npage {position: relative;width: 100%;height: 100%;}\r\n.container{height:100%}\r\n\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.header {width: 100%;padding: 0 3%;background: #fff;}\r\n.header .title {padding: 10px 0px;line-height:44rpx;font-size:32rpx;display:flex;}\r\n.header .title .lef{display:flex;flex-direction:column;justify-content: center;flex:1;color:#222222;font-weight:bold}\r\n.header .title .lef .t2{ font-size:26rpx;color:#999;padding-top:10rpx;font-weight:normal}\r\n.header .title .share{width:88rpx;height:88rpx;padding-left:20rpx;border-left:0 solid #f5f5f5;text-align:center;font-size:24rpx;color:#222;display:flex;flex-direction:column;align-items:center}\r\n.header .title .share image{width:32rpx;height:32rpx;margin-bottom:4rpx}\r\n\r\n.header .sellpoint{font-size:28rpx;color: #666;padding-bottom:20rpx;}\r\n\r\n.header .price{height: 86rpx;overflow: hidden;line-height: 86rpx;border-top: 1px solid #eee;}\r\n.header .price .t1 .x1{ color: #e94745; font-size: 34rpx;}\r\n.header .price .t1 .x2{ color: #939393; margin-left: 10rpx; text-decoration: line-through;font-size:24rpx}\r\n.header .price .t2{color: #aaa; font-size: 24rpx;}\r\n.header .fuwupoint{width:100%;font-size:24rpx;color:#999;display:flex;flex-wrap:wrap;border-top:1px solid #eee;padding:10rpx 0}\r\n.header .fuwupoint .t{ padding:4rpx 20rpx 4rpx 0}\r\n.header .fuwupoint .t:before{content: \"\";\tdisplay: inline-block;\tvertical-align: middle;\tmargin-top: -4rpx;\tmargin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;\tbackground-size: 24rpx auto; }\r\n.header .sales_stock{display:flex;justify-content:space-between;height:60rpx;line-height:60rpx;margin-top:30rpx;font-size:24rpx;color:#777777}\r\n.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 80rpx; line-height: 80rpx; padding: 0 3%; color: #505050; }\r\n.choose .f2{ width: 40rpx; height: 40rpx;}\r\n\r\n.teamlist{ width:100%;background:#fff;padding:10rpx 20rpx;font-size:26rpx;margin-top:20rpx;display:flex;flex-direction:column}\r\n.teamlist .label{ width:100%;color:#222222;font-weight:bold;padding:10rpx 0;border-bottom:1px solid #eee}\r\n.teamlist .content{ width:100%;max-height:300rpx;overflow:scroll}\r\n.teamlist .item{width:100%;display:flex;align-items:center;padding:16rpx 3px;border-bottom:0px solid #f5f5f5}\r\n.teamlist .item .f1{width:300rpx;overflow:hidden;flex:auto;display:flex;align-items:center}\r\n.teamlist .item .f1 image{width:80rpx;height:80rpx;}\r\n.teamlist .item .f1 .t1{padding-left:6rpx;font-size:30rpx;color:#333}\r\n.teamlist .item .f2{ text-align:right;margin:0 8rpx}\r\n.teamlist .item .f2 .t1{font-size:24rpx;color:#333}\r\n.teamlist .item .f2 .t2{font-size:22rpx;color:#999}\r\n.teamlist .item .f3{ background: linear-gradient(90deg, #FF3143 0%, #FE6748 100%);color:#fff;border-radius:30rpx;padding:0 30rpx;height:60rpx;border:0;text-align:right;font-size:26rpx;display:flex;align-items:center}\r\n.teamlist .item .f3:after{border:0}\r\n.jtcontent{width:100%;max-height:350rpx;overflow:scroll}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.commentbox{width:100%;background:#fff;padding:0 3%;margin-top:20rpx}\r\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\r\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\r\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\r\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\r\n\r\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\r\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.comment .item .f2 .t2{display:flex;width:100%}\r\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\r\n\r\n.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;height:110rpx;align-items:center;box-sizing:content-box}\r\n.bottombar .favorite{width: 15%;color:#707070;font-size:26rpx}\r\n.bottombar .favorite .fa{ font-size:40rpx;height:50rpx;line-height:50rpx}\r\n.bottombar .favorite .img{ width:50rpx;height:50rpx}\r\n.bottombar .favorite .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .cart{width: 15%;font-size:26rpx;color:#707070}\r\n.bottombar .cart .img{ width:50rpx;height:50rpx}\r\n.bottombar .cart .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .tocart{ width:35%; height: 90rpx;border-radius:10rpx;color: #fff; background: #fa938a; font-size: 28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:45rpx 0 0 45rpx;padding-left:20rpx}\r\n.bottombar .tobuy{ width:35%; height: 90rpx;border-radius:10rpx;color: #fff; background: #df2e24; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:0 45rpx 45rpx 0;margin-right:16rpx;padding-right:20rpx}\r\n\r\n\r\n\r\n.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}\r\n.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}\r\n.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}\r\n.buydialog .close .image{ width: 30rpx; height:30rpx; }\r\n.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:20rpx 0px; border-bottom:0; height: 190rpx;}\r\n.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\r\n.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\r\n.buydialog .title .price .t1{ font-size:26rpx}\r\n.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa}\r\n.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n\r\n.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }\r\n.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}\r\n.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}\r\n.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}\r\n.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}\r\n.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }\r\n.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n.buydialog .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .img{width:24rpx;height:24rpx}\r\n.buydialog .addnum .input{flex:1;width:70rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx}\r\n.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:100rpx;}\r\n.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none; font-size:28rpx;font-weight:bold}\r\n.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;}\r\n.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}\r\n\r\n.collage_title{width:100%;height:110rpx;background: linear-gradient(90deg, #FF3143 0%, #FE6748 100%);display:flex;align-items:center;padding:0 40rpx}\r\n.collage_title .f1{flex:1;display:flex;flex-direction:column;}\r\n.collage_title .f1 .t1{display:flex;align-items:center;height:60rpx;line-height:60rpx}\r\n.collage_title .f1 .t1 .x1{font-size:28rpx;color:#fff}\r\n.collage_title .f1 .t1 .x2{font-size:48rpx;color:#fff;padding-right:20rpx}\r\n.collage_title .f1 .t1 .x3{font-size:24rpx;font-weight:bold;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:20rpx;background:#fff;color:#ED533A}\r\n.collage_title .f1 .t2{color:rgba(255,255,255,0.6);font-size:20rpx;}\r\n.collage_title .f2{color:#fff;font-size:28rpx;}\r\n\r\n.toptabbar_tab{display:flex;width:100%;height:90rpx;background: #fff;top:var(--window-top);z-index:11;position:fixed;border-bottom:1px solid #f3f3f3}\r\n.toptabbar_tab .item{flex:1;font-size:28rpx; text-align:center; color:#666; height: 90rpx; line-height: 90rpx;overflow: hidden;position:relative}\r\n.toptabbar_tab .item .after{display:none;position:absolute;left:50%;margin-left:-16rpx;bottom:10rpx;height:3px;border-radius:1.5px;width:32rpx}\r\n.toptabbar_tab .on{color: #323233;}\r\n.toptabbar_tab .on .after{display:block}\r\n\r\n.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:20rpx 160rpx;margin-top:20rpx}\r\n.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}\r\n.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\r\n.xihuan-text .txt{color:#111;font-size:30rpx}\r\n.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}\r\n.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}\r\n\r\n.dp-collage-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.dp-collage-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden}\r\n.dp-collage-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\r\n.dp-collage-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.dp-collage-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}\r\n.dp-collage-item .product-pic .tag{padding: 0 15rpx;line-height: 35rpx;display: inline-block;font-size: 24rpx;color: #fff;background: linear-gradient(to bottom right,#ff88c0,#ec3eda);position: absolute;left: 0;top: 0;border-radius: 0 0 10rpx 0}\r\n.dp-collage-item .product-info {padding:20rpx 20rpx;position: relative;}\r\n.dp-collage-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.dp-collage-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\r\n.dp-collage-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\r\n.dp-collage-item .product-info .p2-1 .t1{font-size:36rpx;}\r\n.dp-collage-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.dp-collage-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\r\n.dp-collage-item .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\r\n.dp-collage-item .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}\r\n.dp-collage-item .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\r\n.dp-collage-item .product-info .total{border-radius: 8rpx;border: 1rpx solid #FF3143;font-size: 24rpx;background: #ffeded;overflow: hidden;}\r\n.dp-collage-item .product-info .total .num{color: #fff;background: #FF3143;padding: 3rpx 8rpx;}\r\n.dp-collage-item .product-info .total .sales{color: #FF3143;padding: 3rpx 8rpx;}\r\n.dp-collage-item .product-info .price{position: relative;margin-top: 15rpx;}\r\n.dp-collage-item .product-info .price .text{color: #FF3143;font-weight: bold;font-size: 30rpx;}\r\n.dp-collage-item .product-info .price .add{height: 50rpx;width: 50rpx;border-radius: 100rpx;background: #FF3143;}\r\n.dp-collage-item .product-info .price .add image{height: 30rpx;width: 30rpx;display: block;}\r\n\r\n.ggdiaplog_close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.seckill_title{ width:100%;height:110rpx;background: linear-gradient(90deg, #FF3143 0%, #FD6647 100%);display:flex;align-items:center;}\r\n.seckill-title-end{background: #ccc;}\r\n.seckill_title .f0{width:88rpx;height:88rpx;margin-left:20rpx}\r\n.seckill_title .f1{flex:1;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.seckill_title .f1 .t1{font-size:40rpx;color:#fff;line-height:50rpx}\r\n.seckill_title .f1 .t1 .x2{padding-left:8rpx;font-size:26rpx;color:#fff;text-decoration:line-through}\r\n.seckill_title .f1 .t2{color:#fff;font-size:22rpx}\r\n.seckill_title .f3{width:250rpx;height:110rpx;background:#FFDBDF;color:#333;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.seckill_title .f3 .t2{color:#FF3143}\r\n.seckill_title .djsspan{font-size:22rpx;border-radius:8rpx;background:#FF3143;color:#fff;text-align:center;padding:4rpx 8rpx;margin:0 4rpx}\r\n\r\n/* 阶梯拼团 */\r\n.teamlist .item .f1 .img{border-radius: 50%;margin-left: -20rpx;}\r\n.teamlist .item .f1 .img:first-child{margin-left: 0;}\r\n.jtdata{background: #ffffff;padding: 10rpx 20rpx}\r\n.jtdata .label{font-weight: 700;}\r\n.jtdata .item{border-bottom: 2rpx solid #f5f5f5;line-height: 80rpx;}\r\n.bobaobox {\r\n\tposition: fixed;\r\n\ttop: calc(var(--window-top) + 50rpx);\r\n\tleft: 20rpx;\r\n\tz-index: 10;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tborder-radius: 30rpx;\r\n\tcolor: #fff;\r\n\tpadding: 0 10rpx\r\n}\r\n.jtsales{display: flex;justify-content: space-between;line-height: 60rpx;padding: 20rpx 20rpx 0 20rpx;background: #fff;}\r\n.tobuy_one{border-radius:45rpx!important;margin-left: 30%;} \r\n\r\n.jt_tab{overflow: hidden;display: flex;justify-content: center;margin-top: 20rpx;}\r\n.jt_tab .x1{width: 120rpx;height: 60rpx;line-height: 60rpx;border:1rpx solid #18be6c;text-align: center;}\r\n.jt_tab .x2{width: 120rpx;height: 60rpx;line-height: 60rpx;color:#fff;border:1rpx solid #18be6c;text-align: center;}\r\n\r\n.btnlist{display: flex;justify-content: space-evenly;margin-top: 70rpx;}\r\n.btnlist .tocart{ width:35%; height: 80rpx;border-radius:10rpx;color: #fff; background: #fa938a; font-size: 28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:45rpx;}\r\n .btnlist .tobuy{ width:35%; height: 80rpx;border-radius:10rpx;color: #fff; background: #df2e24; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:45rpx;}\r\n .team_in_team{line-height: 40rpx;padding: 20rpx 0;width: 98%;text-align: center;border-radius: 8rpx;margin:0 auto}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839366380\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}