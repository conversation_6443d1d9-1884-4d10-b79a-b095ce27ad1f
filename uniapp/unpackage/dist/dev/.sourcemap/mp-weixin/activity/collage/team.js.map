{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/team.vue?fa02", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/team.vue?ee05", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/team.vue?f03d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/team.vue?1ef8", "uni-app:///activity/collage/team.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/team.vue?6753", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/collage/team.vue?b36a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "tabnum", "ggselected", "gui<PERSON><PERSON>", "gui<PERSON>", "haveme", "ks", "gwcnum", "showdetail", "buydialogHidden", "team", "userlist", "product", "rtime", "rtimeformat", "isfavorite", "sharetypevisible", "showposter", "posterpic", "show_mingpian", "teamid", "teampid", "onLoad", "onShow", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "link", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "that", "app", "setInterval", "desc", "buydialogChange", "ggchange", "gwcplus", "gwcminus", "gwcinput", "tobuy", "getrtime", "shareClick", "handleClickMask", "showPoster", "proid", "posterDialogClose", "sharemp", "shareapp", "uni", "itemList", "success", "scene", "sharedata", "sharelink"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqJt1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MAAAC;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;IACA;MAAAH;MAAAC;MAAAC;IAAA;IACA;IACA;MACAF;MACAI;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAf;QAAAC;MAAA;QACAa;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;UACAA;UACAA;UACAE;YACAF;UACA;UACA;UACAA;YAAAR;YAAAW;YAAAV;YAAAC;UAAA;QACA;UACA;YACAO;cACA;YACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACArC;MACA;MACA;MACA;IACA;IACA;IACAsC;MACA;MACA;MAEA;QACAL;QACA;MACA;MACA;IACA;IACA;IACAM;MACA;MACA;MAEA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;;MAEAR;IACA;IACAS;MACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAb;MACAA;MACAC;MACAA;QAAAa;QAAA5B;MAAA;QACAe;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IACAe;MACA;IACA;IACAC;MACAf;MACA;IACA;IACAgB;MACA;MACAC;QACAC;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACAJ;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9YA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/collage/team.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/collage/team.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./team.vue?vue&type=template&id=f77c338a&\"\nvar renderjs\nimport script from \"./team.vue?vue&type=script&lang=js&\"\nexport * from \"./team.vue?vue&type=script&lang=js&\"\nimport style0 from \"./team.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/collage/team.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=template&id=f77c338a&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m2 =\n    _vm.isload && _vm.sharetypevisible && !(m1 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m3 =\n    _vm.isload && _vm.sharetypevisible && !(m1 == \"app\") && !(m2 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<view class=\"container\">\n\t\t\t<view class=\"topbg\">\n\t\t\t\t<image :src=\"pre_url + '/static/img/collage_teambg.png'\" class=\"image\"/>\n\t\t\t</view>\n\t\t\t<view class=\"topbox\" @tap=\"goto\" :data-url=\"'product?id=' + product.id + '&teampid='+teamid\">\n\t\t\t\t<view class=\"left\">\n\t\t\t\t\t<image :src=\"product.pic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"right\">\n\t\t\t\t\t<view class=\"f1\">{{product.name}}</view>\n\t\t\t\t\t<view class=\"f2\"><view class=\"t1\" v-if=\"!product.collage_type\">{{product.teamnum}}人团</view></view>\n\t\t\t\t\t<view class=\"f3\">\n\t\t\t\t\t\t<view class=\"t1\">￥</view>\n\t\t\t\t\t\t<view class=\"t2\">{{product.sell_price}}</view>\n\t\t\t\t\t\t<view class=\"t3\">{{product.sales}}人已拼</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"teambox\">\n\t\t\t\t<view class=\"userlist\">\n\t\t\t\t\t<view v-for=\"(item, index) in userlist\" :key=\"index\" class=\"item\">\n\t\t\t\t\t\t<image :src=\"item.headimg?item.headimg:pre_url+'/static/img/wh.png'\" class=\"f1\"></image>\n\t\t\t\t\t\t<text class=\"f2\" v-if=\"item.id == team.mid\">团长</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"join-text\" v-if=\"team.status==1 \">\n\t\t\t\t\t<view v-if=\"!product.collage_type\">仅剩<text class=\"join-te1\">{{team.teamnum-team.num}}</text>个名额</view>\n\t\t\t\t\t<view style=\"font-size:28rpx;color:#f80\"> {{rtimeformat}} 后结束</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"join-text\" v-if=\"team.status==2\">已满员,拼团成功</view>\n\t\t\t\t<view class=\"join-text\" v-if=\"team.status==3\">拼团失败</view>\n\t\t\t\t<button class=\"join-btn\" @tap=\"shareClick\" v-if=\"team.status==1 && haveme==1\">邀请好友参团</button>\n        <block v-if=\"team.status==1 && haveme==0\">\n          <button class=\"join-btn\" @tap=\"buydialogChange\">我要参团</button>\n          <button class=\"join-btn2\" @tap=\"goto\" :data-url=\"'product?id=' + product.id + '&teampid='+teamid\" style=\"margin-top: 30rpx;\">参与更多拼团</button>\n        </block>\n\t\t\t\t\n\t\t\t</view>\n\t\t\t<view class=\"teambox\" v-if=\"show_mingpian\">\n\t\t\t\t<block v-for=\"(item, index) in userlist\" :key=\"index\" class=\"item\">\n\t\t\t\t<view class=\"item1\" v-if=\"item.id>0\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image :src=\"item.headimg\"></image>\n\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t<text class=\"x1\">{{item.nickname}}</text>\n\t\t\t\t\t\t\t<text class=\"x2\" v-if=\"item.id == team.mid\">团长</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\" v-if=\"item.mingpian_id>0\" @tap=\"goto\" :data-url=\"'/pagesExt/mingpian/index?id='+item.mingpian_id\">\n\t\t\t\t\t\t<text class=\"x1\">查看名片</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t\n\t\t\t\n\t\t\t<view :hidden=\"buydialogHidden\">\n\t\t\t\t<view class=\"buydialog-mask\">\n\t\t\t\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"image\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"title\">\n\t\t\t\t\t\t\t<image :src=\"guigelist[ks].pic?guigelist[ks].pic:product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"guigelist[ks].pic?guigelist[ks].pic:product.pic\"></image>\n\t\t\t\t\t\t\t<!-- <text class=\"name\">{{product.name}}</text> -->\n\t\t\t\t\t\t\t<view class=\"price\" v-if=\"btntype==1\"><text class=\"t1\">￥</text>{{guigelist[ks].market_price}}</view>\n\t\t\t\t\t\t\t<view class=\"price\" v-else><text class=\"t1\">￥</text>{{guigelist[ks].sell_price}} <text v-if=\"guigelist[ks].market_price > guigelist[ks].sell_price\" class=\"t2\">￥{{guigelist[ks].market_price}}</text></view>\n\t\t\t\t\t\t\t<view class=\"choosename\">已选规格: {{guigelist[ks].name}}</view>\n\t\t\t\t\t\t\t<view class=\"stock\">剩余{{guigelist[ks].stock}}件</view>\n\t\t\t\t\t\t</view>\n\t\t\t\n\t\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\n\t\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\n\t\t\t\t\t\t\t<view class=\"item flex flex-y-center\">\n\t\t\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\n\t\t\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"buynum flex flex-y-center\" v-if=\"!product.collage_type\">\n\t\t\t\t\t\t\t<view class=\"flex1\">购买数量：</view>\n\t\t\t\t\t\t\t<view class=\"addnum\">\n\t\t\t\t\t\t\t\t<view class=\"minus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" @tap=\"gwcminus\"/></view>\n\t\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\n\t\t\t\t\t\t\t\t<view class=\"plus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\" @tap=\"gwcplus\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"op\">\n\t\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{background:t('color1')}\" @tap=\"tobuy\" data-type=\"3\">确 定</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\n\t\t\t\t<!-- <view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\n\t\t\t\t</view> -->\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"sharetypecontent\">\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\n\t\t\t<view class=\"main\">\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      tabnum: 1,\n      ggselected: [],\n\t\t\tguigedata: [],\n\t\t\tguigelist:[],\n\t\t\thaveme:0,\n      ks: '',\n      gwcnum: 1,\n      showdetail: false,\n      buydialogHidden: true,\n      team: [],\n      userlist: [],\n      product: [],\n      rtime: '',\n      rtimeformat: '',\n      isfavorite: \"\",\n      sharetypevisible: false,\n      showposter: false,\n      posterpic: \"\",\n\t    show_mingpian:false,\n      teamid:0,\n      teampid:0,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n    var teamid   = this.opt.teamid  || 0;\n\t\tthis.teamid  = teamid;\n    if(this.opt.tpid || this.opt.teampid){\n      this.teampid = teamid;\n    }\n  },\n  onShow: function (opt) {\n  \tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonShareAppMessage:function(){\n    var link = '/activity/collage/team?scene=id_'+this.product.id+'-pid_' + app.globalData.mid+'-teamid_'+this.team.id+'-tpid_1';\n\t\treturn this._sharewx({title:'就差你了，快来一起拼团~ ' + this.product.name,pic:this.product.pic,link:link});\n\t},\n\tonShareTimeline:function(){\n    var link = '/activity/collage/team?scene=id_'+this.product.id+'-pid_' + app.globalData.mid+'-teamid_'+this.team.id+'-tpid_1';\n\t\tvar sharewxdata = this._sharewx({title:'就差你了，快来一起拼团~ ' + this.product.name,pic:this.product.pic,link:link});\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiCollage/team', {teamid: that.teamid,teampid: that.teampid}, function (res) {\n\t\t\t\tthat.loading = false;\n        if(res.status == 1) {\n          that.ggselected  = res.ggselected;\n          that.guigedata  = res.guigedata;\n          that.guigelist  = res.guigelist;\n          that.haveme  = res.haveme;\n          that.ks  = res.ks;\n          that.product  = res.product;\n          that.rtime  = res.rtime;\n          that.shopset  = res.shopset;\n          that.sysset   = res.sysset;\n          that.team     = res.team;\n          that.teamid   = res.team.id;\n          if(that.teampid){\n            that.teampid  = res.team.id;\n          }\n          that.userlist = res.userlist;\n          that.show_mingpian = res.show_mingpian;\n          that.getrtime();\n          setInterval(function () {\n            that.getrtime();\n          }, 1000);\n          var link = '/activity/collage/team?scene=id_'+that.product.id+'-pid_' + app.globalData.mid+'-teamid_'+that.team.id+'-tpid_1';\n          that.loaded({title:'就差你了，快来一起拼团~ ',desc:res.product.name,pic:res.product.pic,link:link});\n        } else {\n          if (res.msg) {\n            app.alert(res.msg, function() {\n              if (res.url) app.goto(res.url);\n            });\n          } else if (res.url) {\n            if(res.opentype){\n              app.goto(res.url,res.opentype);\n            }else{\n              app.goto(res.url);\n            }\n          } else {\n            app.alert('您无查看权限');\n          }\n        }\n\t\t\t});\n\t\t},\n    buydialogChange: function (e) {\n      this.buydialogHidden = !this.buydialogHidden\n    },\n    //选择规格\n    ggchange: function (e) {\n      var idx = e.currentTarget.dataset.idx;\n      var itemk = e.currentTarget.dataset.itemk;\n      var ggselected = this.ggselected;\n      ggselected[itemk] = idx;\n      var ks = ggselected.join(',');\n      this.ggselected = ggselected;\n      this.ks = ks;\n    },\n    //加\n    gwcplus: function (e) {\n      var gwcnum = this.gwcnum + 1;\n      var ggselected = this.ks;\n\n      if (gwcnum > this.guigelist[ggselected].stock) {\n        app.error('库存不足');\n        return;\n      }\n      this.gwcnum = this.gwcnum + 1;\n    },\n    //减\n    gwcminus: function (e) {\n      var gwcnum = this.gwcnum - 1;\n      var ggselected = this.ks;\n\n      if (gwcnum <= 0) {\n        return;\n      }\n      this.gwcnum = this.gwcnum - 1;\n    },\n    //输入\n    gwcinput: function (e) {\n      var ggselected = this.ks;\n      var gwcnum = parseInt(e.detail.value);\n      if (gwcnum < 1) return 1;\n      if (gwcnum > this.guigelist[ggselected].stock) {\n        return this.guigelist[ggselected].stock;\n      }\n      this.gwcnum = gwcnum;\n    },\n    tobuy: function (e) {\n      var type = e.currentTarget.dataset.type;\n      var that = this;\n      var ggselected = that.ks;\n      var proid = that.product.id;\n      var ggid = that.guigelist[ggselected].id;\n      var num = that.gwcnum; //var prodata = proid + ',' + ggid + ',' + num;\n\n      app.goto('buy?proid=' + proid + '&num=' + num + '&ggid=' + ggid + '&buytype=' + type + '&teamid=' + that.team.id);\n    },\n    getrtime: function () {\n      var rtime = this.rtime - 1;\n      if (rtime < 0) {\n        this.rtimeformat = '0秒';\n        this.rtime = rtime;\n      } else {\n        var hours = Math.floor(rtime / 3600); //计算相差分钟数  \n        var leave2 = rtime % 3600; //计算小时数后剩余的毫秒数  \n        var minutes = Math.floor(leave2 / 60); //计算相差秒数  \n        var seconds = leave2 % 60; //计算分钟数后剩余的毫秒数\n        var rtimeformat = hours + \"小时\" + minutes + \"分\" + seconds + \"秒\";\n        this.rtimeformat = rtimeformat;\n        this.rtime = rtime;\n      }\n    },\n    shareClick: function () {\n      this.sharetypevisible = true;\n    },\n    handleClickMask: function () {\n      this.sharetypevisible = false;\n    },\n    showPoster: function () {\n      var that = this;\n      that.showposter = true;\n      that.sharetypevisible = false;\n\t\t\tapp.showLoading('努力生成中');\n      app.post('ApiCollage/getTeamPoster', {proid: that.product.id,teamid: that.team.id}, function (data) {\n\t\t\t\tapp.showLoading(false);\n        if (data.status == 0) {\n          app.alert(data.msg);\n        } else {\n          that.posterpic = data.poster;\n        }\n      });\n    },\n    posterDialogClose: function () {\n      this.showposter = false;\n    },\n\t\tsharemp:function(){\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\n\t\t\tthis.sharetypevisible = false\n\t\t},\n\t\tshareapp:function(){\n\t\t\tvar that = this;\n\t\t\tuni.showActionSheet({\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\n        success: function (res){\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\n\t\t\t\t\t\tif (res.tapIndex == 1) {\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar sharedata = {};\n\t\t\t\t\t\tsharedata.provider = 'weixin';\n\t\t\t\t\t\tsharedata.type = 0;\n\t\t\t\t\t\tsharedata.scene = scene;\n\t\t\t\t\t\tsharedata.title = that.product.name;\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/collage/team?scene=id_'+that.product.id+'-pid_' + app.globalData.mid+'-teamid_'+that.team.id+'-tpid_1';\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\n\t\t\t\t\t\tif(sharelist){\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/activity/collage/team'){\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.share(sharedata);\n\t\t\t\t\t}\n        }\n      });\n\t\t}\n  }\n};\n</script>\n<style>\n.topbg{width:100%;height:248rpx;position:relative;z-index:0}\n.topbg .image{width:100%;height:100%}\n.topbox{width:94%;margin:0 3%;margin-top:-140rpx;background:#fff;border-radius:16rpx;padding:24rpx;display:flex;position:relative;z-index:1}\n.topbox .left{flex-shrink:0;width:240rpx;height:240rpx;}\n.topbox .left image{width:100%;height:100%}\n.topbox .right{flex:1;padding-left:20rpx;padding-right:20rpx;display:flex;flex-direction:column}\n.topbox .right .f1{color:#32201B;height:80rpx;line-height:40rpx;font-size:30rpx;font-weight:bold;overflow:hidden}\n.topbox .right .f2{display:flex;margin-top:10rpx}\n.topbox .right .f2 .t1{display:flex;background:rgba(255, 49, 67,0.2);border-radius:20rpx;padding:0 20rpx;height:40rpx;line-height:40rpx;color:#FF3143;font-size:24rpx;}\n.topbox .right .f3{display:flex;align-items:center;color:#FF3143;margin-top:40rpx}\n.topbox .right .f3 .t1{font-size:28rpx}\n.topbox .right .f3 .t2{font-size:40rpx;font-weight:bold;flex:1}\n.topbox .right .f3 .t3{font-size:26rpx;font-weight:bold;}\n\n.teambox{width:94%;margin:0 3%;margin-top:20rpx;background:#fff;border-radius:16rpx;padding:24rpx;display:flex;flex-direction:column}\n\n.userlist{width: 100%;background: #fff;text-align: center;padding-top:40rpx;margin-top:20rpx;}\n.userlist .item{display: inline-block;width:120rpx; height:120rpx;position: relative;}\n.userlist .item .f1{width:100rpx; height:100rpx;border-radius: 50%;border: 1px #ffc32a solid;}\n.userlist .item .f2{background: #ffab33;border-radius:100rpx;padding:4rpx 16rpx;border:1px #fff solid;position: absolute;top: 0px; left: -20rpx;color: #9f7200;font-size: 30rpx;}\n\n.join-text{color:#000;padding: 30rpx 0;font-size:36rpx;font-weight: 600;background: #fff; text-align: center;width: 100%;}\n\n.join-btn{width: 90%;margin:20rpx 5%;background: linear-gradient(90deg, #FF3143 0%, #FE6748 100%);color: #fff;font-size: 30rpx;height:80rpx;border-radius:40rpx}\n.join-btn2{width: 90%;margin:20rpx 5%;border: 2rpx solid #FF3143;color: #FF3143;font-size: 30rpx;height:80rpx;border-radius:40rpx}\n\n.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}\n.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}\n.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}\n.buydialog .close .image{ width: 30rpx; height:30rpx; }\n.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:20rpx 0px; border-bottom:0; height: 190rpx;}\n.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\n.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\n.buydialog .title .price .t1{ font-size:26rpx}\n.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa}\n.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\n.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\n\n.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }\n.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}\n.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}\n.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}\n.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}\n.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }\n.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\n.buydialog .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.buydialog .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.buydialog .addnum .img{width:24rpx;height:24rpx}\n.buydialog .addnum .input{flex:1;width:70rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx}\n.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:100rpx;}\n.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none; font-size:28rpx;font-weight:bold}\n.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;}\n.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}\n\n.teambox .item1{width: 100%;padding:32rpx 20rpx;border-top: 1px #eaeaea solid;min-height: 112rpx;display:flex;align-items:center;}\n.teambox .item1 image{width: 90rpx;height: 90rpx;border-radius:4px}\n.teambox .item1 .f1{display:flex;flex:1;align-items:center;}\n.teambox .item1 .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}\n.teambox .item1 .f1 .t2 .x1{color: #333;font-size:26rpx;}\n\n.teambox .item1 .f2{display:flex;flex-direction:column;width:200rpx;border-left:1px solid #eee;text-align: right;}\n.teambox .item1 .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}\n.teambox .item1 .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}\n.teambox .item1 .f2 .t4{ display:flex;margin-top:10rpx;margin-left: 10rpx;color: #666; flex-wrap: wrap;font-size:18rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839366351\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}