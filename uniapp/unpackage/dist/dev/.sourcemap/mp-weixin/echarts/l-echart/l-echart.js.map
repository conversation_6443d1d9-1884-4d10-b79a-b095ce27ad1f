{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/echarts/l-echart/l-echart.vue?391c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/echarts/l-echart/l-echart.vue?b07e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/echarts/l-echart/l-echart.vue?8227", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/echarts/l-echart/l-echart.vue?11e4", "uni-app:///echarts/l-echart/l-echart.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/echarts/l-echart/l-echart.vue?4cf8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/echarts/l-echart/l-echart.vue?05ef"], "names": ["name", "props", "type", "default", "customStyle", "isDisableScroll", "isClickable", "enableHover", "beforeDelay", "data", "use2dCanvas", "aria<PERSON><PERSON><PERSON>", "width", "height", "nodeWidth", "nodeHeight", "config", "inited", "finished", "file", "platform", "isPC", "isDown", "isOffscreenCanvas", "offscreenWidth", "offscreenHeight", "computed", "canvasId", "offscreenCanvasId", "offscreenStyle", "canvasStyle", "<PERSON><PERSON><PERSON><PERSON>", "created", "mounted", "methods", "<PERSON><PERSON><PERSON>", "console", "callback", "setOption", "showLoading", "hideLoading", "clear", "dispose", "resize", "uni", "in", "select", "boundingClientRect", "exec", "canvasToTempFilePath", "success", "fail", "copyArgs", "init", "args", "arguments", "theme", "Array", "opts", "getContext", "context", "node", "canvas", "dpr", "devicePixelRatio", "getRelative", "clientY", "clientX", "x", "y", "wheelDelta", "getTouch", "touchStart", "dispatch", "handler", "clearTimeout", "next", "touchMove", "touchEnd"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuE11B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AACA;AAAA,eACA;EACAA;EACAC;IAEAC;MACAA;MACAC;IACA;IAMAC;IACAC;IACAC;MACAJ;MACAC;IACA;IACAI;IACAC;MACAN;MACAC;IACA;EACA;EACAM;IACA;MAEAC;MAKAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;EAMA;EAaAC;IAQA;MAAAZ;IACA;IAEA;EACA;EACAa;IAAA;IACA;MACA;IACA;EACA;EACAC;IAkBAC;MACA;QACAC;QACA;MACA;MACA;QACAC;MACA;IAMA;IACAC;MAAA;MACA;QACAF;QACA;MACA;MACA;IACA;IACAG;MACA;QAAA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UAAA;QAAA;MACA;QACA;UACAC,0BACAC,WACAC,uBACAC,qBACAC;YACA;cACA;gBAAApC;gBAAAC;cACA;cACA;cACA;gBAAAD;gBAAAC;cAAA;YACA;UACA;QACA;MAEA;IAEA;IACAoC;MAAA;MAAA;MAEA;QAAAtB;MACA;QACA;UACAA;UACAuB;UACAC;QACA;QACA;UACA;UACAC;QACA;QACAR;MACA;IAgBA;IACAS;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;kBAAAC;gBAAA;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBACAnB;gBAAA;cAAA;gBAIAoB;gBAEAC;kBACA;oBACApB;kBACA;kBACA;oBACAmB;kBACA;kBACA;oBACAE;kBACA;gBACA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA1C;gBAEA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAqB;gBAAA;gBAAA;cAAA;gBAAA,iCAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAYA;IACAsB;MAAA;MAcA;QAAAC;QAAA1D;MAAA;QACA;UACA;UACA;YAAAW;YAAAgD;UACA;UACA;UACA;UACA;YACA;YACAC;YACA;UACA;YAKAC;YAKA;YACA;YACA;YACA;YACAD;UACA;UACA;YAAAA;YAAAlD;YAAAC;YAAAmD;YAAAH;UAAA;QACA;UACA;QACA;MACA;IAEA;IAEAI;MACA;QAAAC;MACA;QACAC;QACAD;MACA;MACA;QAAAE;QAAAC;QAAAC;MAAA;IACA;IACAC;MACA;QAAAH;MACA;IACA;IACAI;MAAA;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACAC;UACAA;UACAC;UACAC;QACA;MAEA;MACA;QACA;UAAAf;QAAA;UACA;UACAgB;QACA;QACA;MACA;MACAA;IACA;IACAC;MACA;QAAA;MAAA;MACA;MACA;QACA;QACAJ;QACAC;MACA;IAEA;IACAI;MACA;MACA;QACA;QACA;UAAAV;QACA;QACA;QACA;QACAK;QACAC;QACA;UACAD;QACA;UACA;YACAA;cAAAL;cAAAC;YAAA;YACAI;cAAAL;cAAAC;YAAA;UACA;QACA;MACA;IACA;EASA;AACA;AAAA,2B;;;;;;;;;;;;;ACrdA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "echarts/l-echart/l-echart.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./l-echart.vue?vue&type=template&id=125a51fc&\"\nvar renderjs\nimport script from \"./l-echart.vue?vue&type=script&lang=js&\"\nexport * from \"./l-echart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./l-echart.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"echarts/l-echart/l-echart.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-echart.vue?vue&type=template&id=125a51fc&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-echart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-echart.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"lime-echart\" :style=\"customStyle\" v-if=\"canvasId\" ref=\"limeEchart\" :aria-label=\"ariaLabel\">\r\n\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t<canvas\r\n\t\t\tclass=\"lime-echart__canvas\"\r\n\t\t\tv-if=\"use2dCanvas\"\r\n\t\t\ttype=\"2d\"\r\n\t\t\t:id=\"canvasId\"\r\n\t\t\t:style=\"canvasStyle\"\r\n\t\t\t:disable-scroll=\"isDisableScroll\"\r\n\t\t\t@touchstart=\"touchStart\"\r\n\t\t\t@touchmove=\"touchMove\"\r\n\t\t\t@touchend=\"touchEnd\"\r\n\t\t/>\r\n\t\t<!-- <canvas\r\n\t\t\tclass=\"lime-echart__canvas\"\r\n\t\t\tv-else-if=\"isPC\"\r\n\t\t\t:style=\"canvasStyle\"\r\n\t\t\t:id=\"canvasId\"\r\n\t\t\t:canvas-id=\"canvasId\"\r\n\t\t\t:disable-scroll=\"isDisableScroll\"\r\n\t\t\t@mousedown=\"touchStart\"\r\n\t\t\t@mousemove=\"touchMove\"\r\n\t\t\t@mouseup=\"touchEnd\"\r\n\t\t/> -->\r\n\t\t<canvas\r\n\t\t\tclass=\"lime-echart__canvas\"\r\n\t\t\tv-else\r\n\t\t\t:width=\"nodeWidth\"\r\n\t\t\t:height=\"nodeHeight\"\r\n\t\t\t:style=\"canvasStyle\"\r\n\t\t\t:canvas-id=\"canvasId\"\r\n\t\t\t:id=\"canvasId\"\r\n\t\t\t:disable-scroll=\"isDisableScroll\"\r\n\t\t\t@touchstart=\"touchStart\"\r\n\t\t\t@touchmove=\"touchMove\"\r\n\t\t\t@touchend=\"touchEnd\"\r\n\t\t/>\r\n\t\t<view class=\"lime-echart__mask\"\r\n\t\t\tv-if=\"isPC\"\r\n\t\t\t@mousedown=\"touchStart\"\r\n\t\t\t@mousemove=\"touchMove\"\r\n\t\t\t@mouseup=\"touchEnd\"\r\n\t\t\t@touchstart=\"touchStart\"\r\n\t\t\t@touchmove=\"touchMove\"\r\n\t\t\t@touchend=\"touchEnd\">\r\n\t\t</view>\r\n\t\t<canvas v-if=\"isOffscreenCanvas\" :style=\"offscreenStyle\" :canvas-id=\"offscreenCanvasId\"></canvas>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t<web-view\r\n\t\t\tclass=\"lime-echart__canvas\"\r\n\t\t\t:id=\"canvasId\"\r\n\t\t\t:style=\"canvasStyle\"\r\n\t\t\t:webview-styles=\"webviewStyles\"\r\n\t\t\tref=\"webview\"\r\n\t\t\tsrc=\"/uni_modules/lime-echart/static/index.html\"\r\n\t\t\t@pagefinish=\"finished = true\"\r\n\t\t\t@onPostMessage=\"onMessage\"\r\n\t\t></web-view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n// #ifdef VUE3\r\n// #ifdef APP-PLUS\r\nglobal = {}\r\n// #endif\r\n// #endif\r\n// #ifndef APP-NVUE\r\nimport {Canvas, setCanvasCreator, dispatch} from './canvas';\r\nimport {wrapTouch, convertTouchesToArray, devicePixelRatio ,sleep, canIUseCanvas2d, getRect} from './utils';\r\n// #endif\r\n// #ifdef APP-NVUE\r\nimport { base64ToPath, sleep } from './utils';\r\nimport {Echarts} from './nvue'\r\n// #endif\r\nconst charts = {}\r\nconst echartsObj = {}\r\nexport default {\r\n\tname: 'lime-echart',\r\n\tprops: {\r\n\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '2d'\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef APP-NVUE\r\n\t\twebviewStyles: Object,\r\n\t\t// hybrid: Boolean,\r\n\t\t// #endif\r\n\t\tcustomStyle: String,\r\n\t\tisDisableScroll: Boolean,\r\n\t\tisClickable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\tenableHover: Boolean,\r\n\t\tbeforeDelay: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 30\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY\r\n\t\t\tuse2dCanvas: true,\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY\r\n\t\t\tuse2dCanvas: false,\r\n\t\t\t// #endif\r\n\t\t\tariaLabel: '图表',\r\n\t\t\twidth: null,\r\n\t\t\theight: null,\r\n\t\t\tnodeWidth: null,\r\n\t\t\tnodeHeight: null,\r\n\t\t\t// canvasNode: null,\r\n\t\t\tconfig: {},\r\n\t\t\tinited: false,\r\n\t\t\tfinished: false,\r\n\t\t\tfile: '',\r\n\t\t\tplatform: '',\r\n\t\t\tisPC: false,\r\n\t\t\tisDown: false,\r\n\t\t\tisOffscreenCanvas: false,\r\n\t\t\toffscreenWidth: 0,\r\n\t\t\toffscreenHeight: 0\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\tcanvasId() {\r\n\t\t\treturn `lime-echart${this._ && this._.uid || this._uid}`\r\n\t\t},\r\n\t\toffscreenCanvasId() {\r\n\t\t\treturn `${this.canvasId}_offscreen`\r\n\t\t},\r\n\t\toffscreenStyle() {\r\n\t\t\treturn `width:${this.offscreenWidth}px;height: ${this.offscreenHeight}px; position: fixed; left: 99999px; background: red`\r\n\t\t},\r\n\t\tcanvasStyle() {\r\n\t\t\treturn  this.width && this.height ? ('width:' + this.width + 'px;height:' + this.height + 'px') : ''\r\n\t\t}\r\n\t},\r\n\t// #ifndef VUE3\r\n\tbeforeDestroy() {\r\n\t\tthis.clear()\r\n\t\tthis.dispose()\r\n\t\t// #ifdef H5\r\n\t\tif(this.isPC) {\r\n\t\t\tdocument.removeEventListener('mousewheel', this.mousewheel)\r\n\t\t}\r\n\t\t// #endif\r\n\t},\r\n\t// #endif\r\n\t// #ifdef VUE3\r\n\tunmounted() {\r\n\t\tthis.clear()\r\n\t\tthis.dispose()\r\n\t\t// #ifdef H5\r\n\t\tif(this.isPC) {\r\n\t\t\tdocument.removeEventListener('mousewheel', this.mousewheel)\r\n\t\t}\r\n\t\t// #endif\r\n\t},\r\n\t// #endif\r\n\tcreated() {\r\n\t\t// #ifdef H5\r\n\t\tif(!('ontouchstart' in window)) {\r\n\t\t\tthis.isPC = true\r\n\t\t\tdocument.addEventListener('mousewheel', this.mousewheel)\r\n\t\t}\r\n\t\t// #endif\r\n\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY\r\n\t\tconst { platform } = uni.getSystemInfoSync();\r\n\t\tthis.isPC = /windows/i.test(platform)\r\n\t\t// #endif\r\n\t\tthis.use2dCanvas = this.type === '2d' && canIUseCanvas2d()\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$nextTick(() => {\r\n\t\t\tthis.$emit('finished')\r\n\t\t})\r\n\t},\r\n\tmethods: {\r\n\t\t// #ifdef APP-NVUE\r\n\t\tonMessage(e) {\r\n\t\t\tconst res = e?.detail?.data[0] || null;\r\n\t\t\tif (res?.event) {\r\n\t\t\t\tif(res.event === 'inited') {\r\n\t\t\t\t\tthis.inited = true\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit(res.event, JSON.parse(res.data));\r\n\t\t\t} else if(res?.file){\r\n\t\t\t\tthis.file = res.data\r\n\t\t\t} else if(!res[0] && JSON.stringify(res[0]) != '{}'){\r\n\t\t\t\tconsole.error(res);\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log(...res)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #endif\r\n\t\tsetChart(callback) {\r\n\t\t\tif(!this.chart) {\r\n\t\t\t\tconsole.warn(`组件还未初始化，请先使用 init`)\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif(typeof callback === 'function' && this.chart) {\r\n\t\t\t\tcallback(this.chart);\r\n\t\t\t}\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tif(typeof callback === 'function') {\r\n\t\t\t\tthis.$refs.webview.evalJs(`setChart(${JSON.stringify(callback.toString())}, ${JSON.stringify(this.chart.options)})`);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tsetOption() {\r\n\t\t\tif (!this.chart || !this.chart.setOption) {\r\n\t\t\t\tconsole.warn(`组件还未初始化，请先使用 init`)\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.chart.setOption(...arguments);\r\n\t\t},\r\n\t\tshowLoading() {\r\n\t\t\tif(this.chart) {\r\n\t\t\t\tthis.chart.showLoading(...arguments)\r\n\t\t\t}\r\n\t\t},\r\n\t\thideLoading() {\r\n\t\t\tif(this.chart) {\r\n\t\t\t\tthis.chart.hideLoading()\r\n\t\t\t}\r\n\t\t},\r\n\t\tclear() {\r\n\t\t\tif(this.chart) {\r\n\t\t\t\tthis.chart.clear()\r\n\t\t\t}\r\n\t\t},\r\n\t\tdispose() {\r\n\t\t\tif(this.chart) {\r\n\t\t\t\tthis.chart.dispose()\r\n\t\t\t}\r\n\t\t},\r\n\t\tresize(size) {\r\n\t\t\tif(size && size.width && size.height) {\r\n\t\t\t\tthis.height = size.height\r\n\t\t\t\tthis.width = size.width\r\n\t\t\t\tif(this.chart) {this.chart.resize(size)}\r\n\t\t\t} else {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t.select(`.lime-echart`)\r\n\t\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\t\t\tlet { width, height } = res[0];\r\n\t\t\t\t\t\t\t\tthis.width = width = width || 300;\r\n\t\t\t\t\t\t\t\tthis.height = height = height || 300;\r\n\t\t\t\t\t\t\t\tthis.chart.resize({width, height})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\tcanvasToTempFilePath(args = {}) {\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tconst { use2dCanvas, canvasId } = this;\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tconst copyArgs = Object.assign({\r\n\t\t\t\t\tcanvasId,\r\n\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\tfail: reject\r\n\t\t\t\t}, args);\r\n\t\t\t\tif (use2dCanvas) {\r\n\t\t\t\t\tdelete copyArgs.canvasId;\r\n\t\t\t\t\tcopyArgs.canvas = this.canvasNode;\r\n\t\t\t\t}\r\n\t\t\t\tuni.canvasToTempFilePath(copyArgs, this);\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis.file = ''\r\n\t\t\tthis.$refs.webview.evalJs(`canvasToTempFilePath()`);\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tthis.$watch('file', async (file) => {\r\n\t\t\t\t\tif(file) {\r\n\t\t\t\t\t\tconst tempFilePath = await base64ToPath(file)\r\n\t\t\t\t\t\tresolve(args.success({tempFilePath}))\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treject(args.fail({error: ``}))\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tasync init(echarts, ...args) {\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tif(arguments && arguments.length < 1) {\r\n\t\t\t\tconsole.error('缺少参数：init(echarts, theme?:string, opts?: object, callback?: function)')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tlet theme=null,opts={},callback;\r\n\t\t\t\r\n\t\t\tArray.from(arguments).forEach(item => {\r\n\t\t\t\tif(typeof item === 'function') {\r\n\t\t\t\t\tcallback = item\r\n\t\t\t\t}\r\n\t\t\t\tif(['string'].includes(typeof item)) {\r\n\t\t\t\t\ttheme = item\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof item === 'object') {\r\n\t\t\t\t\topts = item\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\tif(this.beforeDelay) {\r\n\t\t\t\tawait sleep(this.beforeDelay)\r\n\t\t\t}\r\n\t\t\tlet config = await this.getContext();\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tsetCanvasCreator(echarts, config)\r\n\t\t\tthis.chart = echarts.init(config.canvas, theme, Object.assign({}, config, opts))\r\n\t\t\tif(typeof callback === 'function') {\r\n\t\t\t\tcallback(this.chart)\r\n\t\t\t} else {\r\n\t\t\t\treturn this.chart\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis.chart = new Echarts(this.$refs.webview)\r\n\t\t\tthis.$refs.webview.evalJs(`init(null, null, ${JSON.stringify(opts)}, ${theme})`)\r\n\t\t\tif(callback) {\r\n\t\t\t\tcallback(this.chart)\r\n\t\t\t} else {\r\n\t\t\t\treturn this.chart\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tgetContext() {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tif(this.finished) {\r\n\t\t\t\treturn Promise.resolve(this.finished)\r\n\t\t\t}\r\n\t\t\treturn new Promise(resolve => {\r\n\t\t\t\tthis.$watch('finished', (val) => {\r\n\t\t\t\t\tif(val) {\r\n\t\t\t\t\t\tresolve(this.finished)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\treturn getRect(`#${this.canvasId}`, {context: this, type: this.use2dCanvas ? 'fields': 'boundingClientRect'}).then(res => {\r\n\t\t\t\tif(res) {\r\n\t\t\t\t\tlet dpr = devicePixelRatio\r\n\t\t\t\t\tlet {width, height, node} = res\r\n\t\t\t\t\tlet canvas;\r\n\t\t\t\t\tthis.width = width = width || 300;\r\n\t\t\t\t\tthis.height = height = height || 300;\r\n\t\t\t\t\tif(node) {\r\n\t\t\t\t\t\tconst ctx = node.getContext('2d');\r\n\t\t\t\t\t\tcanvas = new Canvas(ctx, this, true, node);\r\n\t\t\t\t\t\tthis.canvasNode = node\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\t\t\tdpr = !this.isPC ? devicePixelRatio : 1// 1.25\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifndef MP-ALIPAY || MP-TOUTIAO\r\n\t\t\t\t\t\tdpr = this.isPC ? devicePixelRatio : 1\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifdef MP-ALIPAY || MP-LARK\r\n\t\t\t\t\t\tdpr = devicePixelRatio\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tthis.rect = res\r\n\t\t\t\t\t\tthis.nodeWidth = width * dpr;\r\n\t\t\t\t\t\tthis.nodeHeight = height * dpr;\r\n\t\t\t\t\t\tconst ctx = uni.createCanvasContext(this.canvasId, this);\r\n\t\t\t\t\t\tcanvas =  new Canvas(ctx, this, false);\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn { canvas, width, height, devicePixelRatio: dpr, node };\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// #ifndef APP-NVUE\r\n\t\tgetRelative(e, touches) {\r\n\t\t\tlet { clientX, clientY } = e\r\n\t\t\tif(!(clientX && clientY) && touches && touches[0]) {\r\n\t\t\t\tclientX = touches[0].clientX\r\n\t\t\t\tclientY = touches[0].clientY\r\n\t\t\t}\r\n\t\t\treturn {x: clientX - this.rect.left, y: clientY - this.rect.top, wheelDelta: e.wheelDelta || 0}\r\n\t\t},\r\n\t\tgetTouch(e, touches) {\r\n\t\t\tconst {x} = touches && touches[0] || {}\r\n\t\t\treturn x ? touches[0] : this.getRelative(e, touches);\r\n\t\t},\r\n\t\ttouchStart(e) {\r\n\t\t\tthis.isDown = true\r\n\t\t\tconst next = () => {\r\n\t\t\t\tconst touches = convertTouchesToArray(e.touches)\r\n\t\t\t\tif(this.chart) {\r\n\t\t\t\t\tconst touch = this.getTouch(e, touches)\r\n\t\t\t\t\tthis.startX = touch.x\r\n\t\t\t\t\tthis.startY = touch.y\r\n\t\t\t\t\tthis.startT = new Date()\r\n\t\t\t\t\tconst handler = this.chart.getZr().handler;\r\n\t\t\t\t\tdispatch.call(handler, 'mousedown', touch)\r\n\t\t\t\t\tdispatch.call(handler, 'mousemove', touch)\r\n\t\t\t\t\thandler.processGesture(wrapTouch(e), 'start');\r\n\t\t\t\t\tclearTimeout(this.endTimer);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t\tif(this.isPC) {\r\n\t\t\t\tgetRect(`#${this.canvasId}`, {context: this}).then(res => {\r\n\t\t\t\t\tthis.rect = res\r\n\t\t\t\t\tnext()\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tnext()\r\n\t\t},\r\n\t\ttouchMove(e) {\r\n\t\t\tif(this.isPC && this.enableHover && !this.isDown) {this.isDown = true}\r\n\t\t\tconst touches = convertTouchesToArray(e.touches)\r\n\t\t\tif (this.chart && this.isDown) {\r\n\t\t\t\tconst handler = this.chart.getZr().handler;\r\n\t\t\t\tdispatch.call(handler, 'mousemove', this.getTouch(e, touches))\r\n\t\t\t\thandler.processGesture(wrapTouch(e), 'change');\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\ttouchEnd(e) {\r\n\t\t\tthis.isDown = false\r\n\t\t\tif (this.chart) {\r\n\t\t\t\tconst touches = convertTouchesToArray(e.changedTouches)\r\n\t\t\t\tconst {x} = touches && touches[0] || {}\r\n\t\t\t\tconst touch = (x ? touches[0] : this.getRelative(e, touches)) || {};\r\n\t\t\t\tconst handler = this.chart.getZr().handler;\r\n\t\t\t\tconst isClick = Math.abs(touch.x - this.startX) < 10 && new Date() - this.startT < 200;\r\n\t\t\t\tdispatch.call(handler, 'mouseup', touch)\r\n\t\t\t\thandler.processGesture(wrapTouch(e), 'end');\r\n\t\t\t\tif(isClick) {\r\n\t\t\t\t\tdispatch.call(handler, 'click', touch)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.endTimer = setTimeout(() => {\r\n\t\t\t\t\t\tdispatch.call(handler, 'mousemove', {x: 999999999,y: 999999999});\r\n\t\t\t\t\t\tdispatch.call(handler, 'mouseup', {x: 999999999,y: 999999999});\r\n\t\t\t\t\t},50)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef H5\r\n\t\tmousewheel(e){\r\n\t\t\tif(this.chart) {\r\n\t\t\t\tdispatch.call(this.chart.getZr().handler, 'mousewheel', this.getTouch(e))\r\n\t\t\t}\r\n\t\t}\r\n\t\t// #endif\r\n\t}\r\n};\r\n</script>\r\n<style>\t\r\n.lime-echart {\r\n\tposition: relative;\r\n\t/* #ifndef APP-NVUE */\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\t/* #endif */\r\n\t/* #ifdef APP-NVUE */\r\n\tflex: 1;\r\n\t/* #endif */\r\n}\r\n.lime-echart__canvas {\r\n\t/* #ifndef APP-NVUE */\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\t/* #endif */\r\n\t/* #ifdef APP-NVUE */\r\n\tflex: 1;\r\n\t/* #endif */\r\n}\r\n/* #ifndef APP-NVUE */\r\n.lime-echart__mask {\r\n\tposition: absolute;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\tz-index: 1;\n}\r\n/* #endif */\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-echart.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-echart.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839440584\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}