{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-drawer/uni-drawer.vue?4ba9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-drawer/uni-drawer.vue?645c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-drawer/uni-drawer.vue?2a9d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-drawer/uni-drawer.vue?f340", "uni-app:///components/uni-drawer/uni-drawer.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-drawer/uni-drawer.vue?67d2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-drawer/uni-drawer.vue?7a9c"], "names": ["name", "props", "mode", "type", "default", "mask", "maskClick", "width", "data", "visibleSync", "showDrawer", "rightMode", "watchTimer", "drawerWidth", "created", "methods", "clear", "close", "open", "_change", "clearTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;ACU51B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAH;MACAC;IACA;IACA;AACA;AACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEA;IAEA;EACA;EACAC;IACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;MACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAA6sC,CAAgB,6nCAAG,EAAC,C;;;;;;;;;;;ACAjuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-drawer/uni-drawer.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-drawer.vue?vue&type=template&id=56836304&scoped=true&\"\nvar renderjs\nimport script from \"./uni-drawer.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-drawer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-drawer.vue?vue&type=style&index=0&id=56836304&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"56836304\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-drawer/uni-drawer.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-drawer.vue?vue&type=template&id=56836304&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-drawer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-drawer.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"visibleSync\" :class=\"{ 'uni-drawer--visible': showDrawer }\" class=\"uni-drawer\" @touchmove.stop.prevent=\"clear\">\r\n\t\t<view class=\"uni-drawer__mask\" :class=\"{ 'uni-drawer__mask--visible': showDrawer && mask }\" @tap=\"close('mask')\" />\r\n\t\t<view class=\"uni-drawer__content\" :class=\"{'uni-drawer--right': rightMode,'uni-drawer--left': !rightMode, 'uni-drawer__content--visible': showDrawer}\" :style=\"{width:drawerWidth+'px'}\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * Drawer 抽屉\r\n\t * @description 抽屉侧滑菜单\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=26\r\n\t * @property {Boolean} mask = [true | false] 是否显示遮罩\r\n\t * @property {Boolean} maskClick = [true | false] 点击遮罩是否关闭\r\n\t * @property {Boolean} mode = [left | right] Drawer 滑出位置\r\n\t * \t@value left 从左侧滑出\r\n\t * \t@value right 从右侧侧滑出\r\n\t * @property {Number} width 抽屉的宽度 ，仅 vue 页面生效\r\n\t * @event {Function} close 组件关闭时触发事件\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniDrawer',\r\n\t\tprops: {\r\n\t\t\t/**\r\n\t\t\t * 显示模式（左、右），只在初始化生效\r\n\t\t\t */\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 蒙层显示状态\r\n\t\t\t */\r\n\t\t\tmask: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 遮罩是否可点击关闭\r\n\t\t\t */\r\n\t\t\tmaskClick: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 抽屉宽度\r\n\t\t\t */\r\n\t\t\twidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 220\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvisibleSync: false,\r\n\t\t\t\tshowDrawer: false,\r\n\t\t\t\trightMode: false,\r\n\t\t\t\twatchTimer: null,\r\n\t\t\t\tdrawerWidth: 220\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.drawerWidth = this.width\r\n\t\t\t// #endif\r\n\t\t\tthis.rightMode = this.mode === 'right'\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclear() {},\r\n\t\t\tclose(type) {\r\n\t\t\t\t// fixed by mehaotian 抽屉尚未完全关闭或遮罩禁止点击时不触发以下逻辑\r\n\t\t\t\tif ((type === 'mask' && !this.maskClick) || !this.visibleSync) return\r\n\t\t\t\tthis._change('showDrawer', 'visibleSync', false)\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\t// fixed by mehaotian 处理重复点击打开的事件\r\n\t\t\t\tif (this.visibleSync) return\r\n\t\t\t\tthis._change('visibleSync', 'showDrawer', true)\r\n\t\t\t},\r\n\t\t\t_change(param1, param2, status) {\r\n\t\t\t\tthis[param1] = status\r\n\t\t\t\tif (this.watchTimer) {\r\n\t\t\t\t\tclearTimeout(this.watchTimer)\r\n\t\t\t\t}\r\n\t\t\t\tthis.watchTimer = setTimeout(() => {\r\n\t\t\t\t\tthis[param2] = status\r\n\t\t\t\t\tthis.$emit('change', status)\r\n\t\t\t\t}, status ? 50 : 300)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.uni-drawer {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: block;\r\n\t\t/* #endif */\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\toverflow: hidden;\r\n\t\tz-index: 999;\r\n\t}\r\n\r\n\t.uni-drawer__content {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: block;\r\n\t\t/* #endif */\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\twidth: 220px;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: #ffffff;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t.uni-drawer--left {\r\n\t\tleft: 0;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\ttransform: translateX(-220px);\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\ttransform: translateX(-100%);\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-drawer--right {\r\n\t\tright: 0;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\ttransform: translateX(220px);\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\ttransform: translateX(100%);\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-drawer__content--visible {\r\n\t\ttransform: translateX(0px);\r\n\t}\r\n\r\n\t.uni-drawer__mask {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: block;\r\n\t\t/* #endif */\r\n\t\topacity: 0;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\r\n\t\ttransition: opacity 0.3s;\r\n\t}\r\n\r\n\t.uni-drawer__mask--visible {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: block;\r\n\t\t/* #endif */\r\n\t\topacity: 1;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-drawer.vue?vue&type=style&index=0&id=56836304&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-drawer.vue?vue&type=style&index=0&id=56836304&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839368016\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}