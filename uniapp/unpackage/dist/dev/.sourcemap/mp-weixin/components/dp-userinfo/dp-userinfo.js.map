{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-userinfo/dp-userinfo.vue?be3b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-userinfo/dp-userinfo.vue?461d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-userinfo/dp-userinfo.vue?5ac3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-userinfo/dp-userinfo.vue?160d", "uni-app:///components/dp-userinfo/dp-userinfo.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-userinfo/dp-userinfo.vue?60b2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-userinfo/dp-userinfo.vue?cfbb"], "names": ["data", "textset", "platform", "pre_url", "task_banner", "loading", "choujiang_id", "sy_count", "videoAd", "tabFlex", "custom_field", "NavigationCustomTop", "usercenterNavigationCustom", "navigationMenu", "props", "params", "mounted", "methods", "optionJump", "url", "app", "openLevelup", "opencard", "j<PERSON><PERSON>", "cardList", "cardId", "code", "success", "wx", "addmembercard", "card_id", "appId", "extraData", "fail", "complete", "weixinlogin", "currentPage", "totaskbanner", "adUnitId", "rewardedVideoAd", "console", "that", "toAddRecord"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,m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ldA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0jB71B;AACA;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAf;EACA;EACAgB;IAEA;IACA;MACA;MACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;EAYA;EACAC;IACAC;MACA;MACA;QACA;UACAC;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;MAAA;MAEAC;IACA;IACAC;MACA;MACA;QACAD;MACA;IACA;IACAE;MACA;MACA;MACA;QACA;QACAC;UACAC;YACAC;YACAC;UACA;UACAC;QACA;MACA;QACAC;UACAJ;YACAC;YACAC;UACA;UACAC;QACA;MACA;IAEA;IACA;IACAE;MACA;MACAT;QAAAU;MAAA;QACA;UACAV;UACA;QACA;QACAQ;UACAG;UAAA;UACAC;UAAA;UACAL;UACAM;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAf;UACA;YACAA;YACA;YACA;YACAgB;UACA;YACAhB;YAAA;YACA;UACA;QACA;MACA;QACAA;QAAA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAiB;MACA;MACA;MACA;MACA;MACAjB;QACA;UACA;YACAA;YACA;cACAA;gBAAAkB;cAAA;YACA;YACA;YACAC;cAAAnB;cAAAmB;YAAA;cAAAnB;YAAA;YACAmB;cACAnB;cACA;cACAoB;cACAD;cACAA;YACA;YACAA;cACAnB;cACA;gBACA;gBACAqB;cACA;gBACAD;cACA;cACAD;cACAA;YACA;UACA;YACA;UACA;QACA;UACAE;UACArB;YACAqB;YACArB;cACAqB;cACArB;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACAsB;MACA;MACAD;MACArB;QACAqB;QACA;QACA;QACAL;QACA;UACAK;UACAA;UACArB;YACAqB;YACArB;cACAqB;cACArB;YACA;UACA;QACA;UACAqB;UACArB;QACA;UACAA;UACA;QACA;MAEA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxzBA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-userinfo/dp-userinfo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-userinfo.vue?vue&type=template&id=3277608c&\"\nvar renderjs\nimport script from \"./dp-userinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-userinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-userinfo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-userinfo/dp-userinfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-userinfo.vue?vue&type=template&id=3277608c&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.params.style == 1 ? _vm.t(\"color1\") : null\n  var m1 = _vm.params.style == 1 ? _vm.t(\"color1rgb\") : null\n  var m2 =\n    _vm.params.style == 1 &&\n    _vm.data.userinfo &&\n    _vm.data.userinfo.show_commission_max\n      ? _vm.t(\"佣金上限\")\n      : null\n  var m3 =\n    _vm.params.style == 1 && _vm.params.moneyshow == 1 ? _vm.t(\"余额\") : null\n  var m4 =\n    _vm.params.style == 1 &&\n    _vm.params.commissionshow == 1 &&\n    _vm.data.userlevel &&\n    _vm.data.userlevel.can_agent > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m5 =\n    _vm.params.style == 1 && _vm.params.scoreshow == 1 ? _vm.t(\"积分\") : null\n  var m6 =\n    _vm.params.style == 1 && _vm.params.bscoreshow == 1 ? _vm.t(\"积分\") : null\n  var m7 =\n    _vm.params.style == 1\n      ? !_vm.isNull(_vm.data.userinfo.service_fee) &&\n        _vm.params.servicefeeshow == 1\n      : null\n  var m8 = _vm.params.style == 1 && m7 ? _vm.t(\"服务费\") : null\n  var m9 =\n    _vm.params.style == 1 && _vm.params.couponshow == 1 ? _vm.t(\"优惠券\") : null\n  var m10 =\n    _vm.params.style == 1 && _vm.data.userinfo.fuchi_money >= 0\n      ? _vm.t(\"扶持金\")\n      : null\n  var m11 =\n    _vm.params.style == 1 && _vm.data.userinfo.gongxian >= 0\n      ? _vm.t(\"贡献\")\n      : null\n  var m12 =\n    _vm.params.style == 1 && _vm.params.xiaofeishow == 1\n      ? _vm.t(\"冻结佣金\")\n      : null\n  var m13 =\n    _vm.params.style == 1 && _vm.params.freezecreditshow == 1\n      ? _vm.t(\"冻结账户\")\n      : null\n  var m14 =\n    _vm.params.style == 1 && _vm.params.bonuspoolshow == 1\n      ? _vm.t(\"贡献值\")\n      : null\n  var m15 =\n    _vm.params.style == 1 && _vm.data.userinfo.product_deposit_mode\n      ? _vm.t(\"押金\")\n      : null\n  var m16 =\n    _vm.params.style == 1 && _vm.data.userinfo.product_deposit_mode\n      ? _vm.t(\"信用额度\")\n      : null\n  var m17 =\n    _vm.params.style == 1 && _vm.params.commissionwithdrawscoreshow == 1\n      ? _vm.t(\"提现积分\")\n      : null\n  var m18 =\n    _vm.params.style == 1 && _vm.params.fhcopiesshow == 1\n      ? _vm.t(\"分红份数\")\n      : null\n  var m19 =\n    _vm.params.style == 1 && _vm.params.tongzhengshow == 1\n      ? _vm.t(\"通证\")\n      : null\n  var m20 =\n    _vm.params.style == 1 && _vm.params.greenscoreshow == 1\n      ? _vm.t(\"绿色积分\")\n      : null\n  var m21 =\n    _vm.params.style == 1 && _vm.params.activecoinshow == 1\n      ? _vm.t(\"激活币\")\n      : null\n  var m22 =\n    _vm.params.style == 1 && _vm.params.showsilvermoney == 1\n      ? _vm.t(\"银值\")\n      : null\n  var m23 =\n    _vm.params.style == 1 && _vm.params.showgoldmoney == 1\n      ? _vm.t(\"金值\")\n      : null\n  var m24 =\n    _vm.params.style == 1 && _vm.params.shopscoreshow == 1\n      ? _vm.t(\"产品积分\")\n      : null\n  var m25 =\n    _vm.params.style == 1 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额2\")\n      : null\n  var m26 =\n    _vm.params.style == 1 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额3\")\n      : null\n  var m27 =\n    _vm.params.style == 1 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额4\")\n      : null\n  var m28 =\n    _vm.params.style == 1 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额5\")\n      : null\n  var m29 =\n    _vm.params.style == 1 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"冻结金额\")\n      : null\n  var m30 =\n    _vm.params.style == 1 && _vm.data.parent_show && _vm.data.parent\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m31 =\n    _vm.params.style == 1 && _vm.data.parent_show && _vm.data.parent\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m32 =\n    _vm.params.style == 1 && _vm.data.parent_show && _vm.data.parent\n      ? _vm.t(\"推荐人\")\n      : null\n  var m33 =\n    _vm.params.style == 1 && _vm.data.parent_show && !_vm.data.parent\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m34 =\n    _vm.params.style == 1 &&\n    _vm.params.seticonsize &&\n    _vm.params.seticonsize > 0 &&\n    _vm.params.membercodeshow == \"1\" &&\n    !(_vm.usercenterNavigationCustom == 0)\n      ? Number(_vm.navigationMenu.width)\n      : null\n  var m35 =\n    _vm.params.style == 1 &&\n    _vm.params.seticonsize &&\n    _vm.params.seticonsize > 0 &&\n    _vm.params.membercodeshow == \"1\" &&\n    !(_vm.usercenterNavigationCustom == 0)\n      ? Number(_vm.params.seticonsize)\n      : null\n  var m36 =\n    _vm.params.style == 1 &&\n    _vm.params.seticonsize &&\n    _vm.params.seticonsize > 0 &&\n    _vm.params.membercodeshow == \"1\" &&\n    !(_vm.usercenterNavigationCustom == 0)\n      ? Number(_vm.params.seticonsize)\n      : null\n  var m37 =\n    _vm.params.style == 1 &&\n    _vm.params.seticonsize &&\n    _vm.params.seticonsize > 0 &&\n    _vm.params.seticonshow !== \"0\" &&\n    !(_vm.usercenterNavigationCustom == 0)\n      ? Number(_vm.navigationMenu.width)\n      : null\n  var m38 =\n    _vm.params.style == 1 &&\n    _vm.params.seticonsize &&\n    _vm.params.seticonsize > 0 &&\n    _vm.params.seticonshow !== \"0\" &&\n    !(_vm.usercenterNavigationCustom == 0)\n      ? Number(_vm.params.seticonsize)\n      : null\n  var m39 = _vm.params.style == 2 ? _vm.t(\"color1\") : null\n  var m40 = _vm.params.style == 2 ? _vm.t(\"color1rgb\") : null\n  var m41 =\n    _vm.params.style == 2 &&\n    _vm.data.userinfo &&\n    _vm.data.userinfo.show_commission_max &&\n    _vm.data.userinfo.commission_to_score == 1\n      ? _vm.t(\"佣金上限\")\n      : null\n  var m42 =\n    _vm.params.style == 2 &&\n    _vm.data.userinfo &&\n    _vm.data.userinfo.show_commission_max &&\n    !(_vm.data.userinfo.commission_to_score == 1)\n      ? _vm.t(\"佣金上限\")\n      : null\n  var m43 =\n    _vm.params.style == 2 && _vm.params.moneyshow == 1 ? _vm.t(\"余额\") : null\n  var m44 =\n    _vm.params.style == 2 &&\n    _vm.params.commissionshow == 1 &&\n    _vm.data.userlevel &&\n    _vm.data.userlevel.can_agent > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m45 =\n    _vm.params.style == 2 && _vm.params.scoreshow == 1 ? _vm.t(\"积分\") : null\n  var m46 =\n    _vm.params.style == 2 && _vm.params.bscoreshow == 1 ? _vm.t(\"积分\") : null\n  var m47 =\n    _vm.params.style == 2\n      ? !_vm.isNull(_vm.data.userinfo.service_fee) &&\n        _vm.params.servicefeeshow == 1\n      : null\n  var m48 = _vm.params.style == 2 && m47 ? _vm.t(\"服务费\") : null\n  var m49 =\n    _vm.params.style == 2 && _vm.params.couponshow == 1 ? _vm.t(\"优惠券\") : null\n  var m50 =\n    _vm.params.style == 2 && _vm.data.userinfo.fuchi_money >= 0\n      ? _vm.t(\"扶持金\")\n      : null\n  var m51 =\n    _vm.params.style == 2 && _vm.data.userinfo.gongxian >= 0\n      ? _vm.t(\"贡献\")\n      : null\n  var m52 =\n    _vm.params.style == 2 && _vm.params.xiaofeishow == 1\n      ? _vm.t(\"冻结佣金\")\n      : null\n  var m53 =\n    _vm.params.style == 2 && _vm.params.freezecreditshow == 1\n      ? _vm.t(\"冻结账户\")\n      : null\n  var m54 =\n    _vm.params.style == 2 && _vm.params.bonuspoolshow == 1\n      ? _vm.t(\"贡献值\")\n      : null\n  var m55 =\n    _vm.params.style == 2 && _vm.data.userinfo.product_deposit_mode\n      ? _vm.t(\"押金\")\n      : null\n  var m56 =\n    _vm.params.style == 2 && _vm.params.overdraftmoneyshow == 1\n      ? _vm.t(\"信用额度\")\n      : null\n  var m57 =\n    _vm.params.style == 2 && _vm.params.commissionwithdrawscoreshow == 1\n      ? _vm.t(\"提现积分\")\n      : null\n  var m58 =\n    _vm.params.style == 2 && _vm.params.fhcopiesshow == 1\n      ? _vm.t(\"分红份数\")\n      : null\n  var m59 =\n    _vm.params.style == 2 && _vm.params.tongzhengshow == 1\n      ? _vm.t(\"通证\")\n      : null\n  var m60 =\n    _vm.params.style == 2 && _vm.params.greenscoreshow == 1\n      ? _vm.t(\"绿色积分\")\n      : null\n  var m61 =\n    _vm.params.style == 2 && _vm.params.activecoinshow == 1\n      ? _vm.t(\"激活币\")\n      : null\n  var m62 =\n    _vm.params.style == 2 && _vm.params.showsilvermoney == 1\n      ? _vm.t(\"银值\")\n      : null\n  var m63 =\n    _vm.params.style == 2 && _vm.params.showgoldmoney == 1\n      ? _vm.t(\"金值\")\n      : null\n  var m64 =\n    _vm.params.style == 2 && _vm.params.shopscoreshow == 1\n      ? _vm.t(\"产品积分\")\n      : null\n  var m65 =\n    _vm.params.style == 2 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额2\")\n      : null\n  var m66 =\n    _vm.params.style == 2 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额3\")\n      : null\n  var m67 =\n    _vm.params.style == 2 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额4\")\n      : null\n  var m68 =\n    _vm.params.style == 2 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"余额5\")\n      : null\n  var m69 =\n    _vm.params.style == 2 && _vm.data.userinfo.othermoney_status == 1\n      ? _vm.t(\"冻结金额\")\n      : null\n  var m70 =\n    _vm.params.style == 2 && _vm.data.parent_show && _vm.data.parent\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m71 =\n    _vm.params.style == 2 && _vm.data.parent_show && _vm.data.parent\n      ? _vm.t(\"推荐人\")\n      : null\n  var m72 =\n    _vm.params.ordershow == 1 &&\n    (!_vm.params.showtype || _vm.params.showtype == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m73 =\n    _vm.params.ordershow == 1 &&\n    (!_vm.params.showtype || _vm.params.showtype == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m74 =\n    _vm.params.ordershow == 1 &&\n    (!_vm.params.showtype || _vm.params.showtype == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m75 =\n    _vm.params.ordershow == 1 &&\n    (!_vm.params.showtype || _vm.params.showtype == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m76 =\n    _vm.params.ordershow == 1 &&\n    (!_vm.params.showtype || _vm.params.showtype == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m77 =\n    _vm.params.ordershow == 1 &&\n    (!_vm.params.showtype || _vm.params.showtype == 0) &&\n    _vm.data.orderinfo.transfer_order_parent_check\n      ? _vm.t(\"color1\")\n      : null\n  var m78 = _vm.params.scoreshopordershow == 1 ? _vm.t(\"积分\") : null\n  var m79 = _vm.params.scoreshopordershow == 1 ? _vm.t(\"color1\") : null\n  var m80 = _vm.params.scoreshopordershow == 1 ? _vm.t(\"color1\") : null\n  var m81 = _vm.params.scoreshopordershow == 1 ? _vm.t(\"color1\") : null\n  var m82 = _vm.params.scoreshopordershow == 1 ? _vm.t(\"color1\") : null\n  var m83 =\n    _vm.params.scoreshopordershow == 1 && _vm.params.scoreshowrefund == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m84 = _vm.data.userinfo.show_green_score ? _vm.t(\"color1rgb\") : null\n  var m85 = _vm.data.userinfo.show_green_score ? _vm.t(\"绿色积分\") : null\n  var m86 = _vm.data.userinfo.show_cashback ? _vm.t(\"color1rgb\") : null\n  var m87 = _vm.data.userinfo.show_cashback ? _vm.t(\"释放积分\") : null\n  var m88 = _vm.data.userinfo.show_cashback_multiply ? _vm.t(\"color1rgb\") : null\n  var m89 = _vm.data.userinfo.show_cashback_multiply ? _vm.t(\"释放积分\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        m47: m47,\n        m48: m48,\n        m49: m49,\n        m50: m50,\n        m51: m51,\n        m52: m52,\n        m53: m53,\n        m54: m54,\n        m55: m55,\n        m56: m56,\n        m57: m57,\n        m58: m58,\n        m59: m59,\n        m60: m60,\n        m61: m61,\n        m62: m62,\n        m63: m63,\n        m64: m64,\n        m65: m65,\n        m66: m66,\n        m67: m67,\n        m68: m68,\n        m69: m69,\n        m70: m70,\n        m71: m71,\n        m72: m72,\n        m73: m73,\n        m74: m74,\n        m75: m75,\n        m76: m76,\n        m77: m77,\n        m78: m78,\n        m79: m79,\n        m80: m80,\n        m81: m81,\n        m82: m82,\n        m83: m83,\n        m84: m84,\n        m85: m85,\n        m86: m86,\n        m87: m87,\n        m88: m88,\n        m89: m89,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-userinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-userinfo.vue?vue&type=script&lang=js&\"", "<template>\r\n<view :style=\"{position: 'relative',marginTop:params.margin_y*2.2 + 'rpx'}\">\r\n\t<view v-if=\"params.style==1\" :style=\"'background: linear-gradient(180deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0) 100%);'\">\r\n\t\t<view class=\"dp-userinfo\" :style=\"{background:'url('+params.bgimg+') no-repeat',backgroundSize:'cover',margin:0+'rpx '+(params.margin_x*2.2)+'rpx',padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx '+'0rpx'}\">\r\n\t\t\t<view class=\"banner\" :style=\"{marginTop:usercenterNavigationCustom == 0 ? '120rpx':NavigationCustomTop + 10 +'px'}\">\r\n\t\t\t\t<view class='info'>\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t<image :src=\"data.userinfo.headimg\" background-size=\"cover\" class=\"headimg\" @tap=\"weixinlogin\"/>\r\n\t\t\t\t\t\t\t<view v-if=\"params.realnameVerifyShow == 1\" @tap=\"goto\" data-url=\"/pagesExt/my/setrealname\">\r\n\t\t\t\t\t\t\t\t<text class=\"tag-gray\" v-if=\"data.userinfo.realname_status != 1\">未实名</text>\r\n\t\t\t\t\t\t\t\t<text class=\"tag-renzheng\" v-if=\"data.userinfo.realname_status == 1\">实名认证</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t<view class=\"nickname\">{{data.userinfo.nickname}}</view>\r\n\t\t\t\t\t\t\t<text v-if=\"params.midshow=='1'\" style=\"font-size:26rpx;padding-left:10rpx\">(ID:{{data.userinfo.id}})</text>\r\n\t\t\t\t\t\t\t<view class=\"user-level\" @tap=\"openLevelup\" data-levelid=\"\" v-if=\"params.levelshow==1\">\r\n\t\t\t\t\t\t\t\t<image class=\"level-img\" :src=\"data.userlevel.icon\" v-if=\"data.userlevel.icon\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"level-name\">{{data.userlevel.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"user-level\" v-for=\"(item, index) in data.userlevelList\" :key=\"index\" @tap=\"openLevelup\" :data-levelid=\"item.id\" v-if=\"params.levelshow==1\">\r\n\t\t\t\t\t\t\t\t<image class=\"level-img\" :src='item.icon' v-if=\"item.icon\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"level-name\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"usermid\" style=\"margin-left:10rpx;font-size:24rpx;color:#999\" v-if=\"data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1\">邀请码：<text user-select=\"true\" selectable=\"true\">{{data.userinfo.yqcode}}</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"data.zhaopin && data.zhaopin.show_zhaopin\" class=\"flex\">\r\n\t\t\t\t\t\t\t<text v-if=\"data.zhaopin.is_qiuzhi_renzheng && !data.zhaopin.is_qiuzhi_renzheng\" class=\"tag-renzheng\">认证保障中</text>\r\n\t\t\t\t\t\t\t<text v-if=\"data.zhaopin.is_qiuzhi_qianyue\" class=\"tag-renzheng\">签约保障中</text>\r\n\t\t\t\t\t\t\t<text v-if=\"data.zhaopin.is_zhaopin_renzheng\" class=\"tag-renzheng\">认证企业</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex\" v-if=\"data.userinfo && data.userinfo.show_commission_max\">\r\n\t\t\t\t\t\t\t<text class=\"tag-renzheng\">{{t('佣金上限')}}:{{data.userinfo.remain_commission_max}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"platform=='wx'\">\r\n\t\t\t\t\t\t<view class=\"usercard\" v-if=\"params.cardshow==1 && data.userinfo.card_code\" @tap=\"opencard\" :data-card_id=\"data.userinfo.card_id\" :data-card_code=\"data.userinfo.card_code\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t\t\t\t\t<view class=\"usercard\" v-if=\"params.cardshow==1 && !data.userinfo.card_code\" @tap=\"addmembercard\" :data-card_id=\"data.card_id\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"platform=='mp'\">\r\n\t\t\t\t\t\t<view class=\"usercard\" v-if=\"params.cardshow==1 && data.userinfo.card_code\" @tap=\"opencard\" :data-card_id=\"data.userinfo.card_id\" :data-card_code=\"data.userinfo.card_code\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t\t\t\t\t<view class=\"usercard\" v-if=\"params.cardshow==1 && !data.userinfo.card_code\" @tap=\"goto\" :data-url=\"data.card_returl\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view ref=\"custom_field1\" :class=\"tabFlex?'custom_field_flex':'custom_field'\">\r\n\t\t\t\t\t<view class='item' v-if=\"params.moneyshow==1\" data-url='/pagesExt/money/recharge' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.money}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('余额')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0\" data-url='/pages/commission/index' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.commission}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('佣金')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.scoreshow==1\" data-url='/pagesExt/my/scorelog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.score}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('积分')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.winecoinshow==1\" data-url='/pagesExt/winecoin/index' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.wine_coin}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">酒币</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.bscoreshow==1\" data-url='/pagesExt/my/bscore' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.bscore+data.userinfo.score}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">总{{t('积分')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"!isNull(data.userinfo.service_fee) && params.servicefeeshow==1\" data-url='/pagesB/service_fee/recharge' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.service_fee}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('服务费')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.couponshow==1\" data-url='/pagesExt/coupon/mycoupon' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.couponcount}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('优惠券')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.formshow==1\" data-url='/pagesA/form/formlog?st=1' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.formcount}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{params.formtext}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"data.userinfo.fuchi_money>=0\" data-url='/pagesExt/my/fuchi' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.fuchi_money}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('扶持金')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"data.userinfo.gongxian>=0\" data-url='/pagesExt/my/gongxianLog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.gongxian?data.userinfo.gongxian:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('贡献')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.xiaofeishow==1\" data-url='/pagesA/my/xiaofeimoneylog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.xiaofei_money?data.userinfo.xiaofei_money:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('冻结佣金')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if=\"params.freezecreditshow==1\" data-url='/pagesB/my/freezecreditlog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.freeze_credit?data.userinfo.freeze_credit:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('冻结账户')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"params.bonuspoolshow==1\" class='item' data-url='/pagesA/bonuspool/withdraw' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.bonus_pool_money?data.userinfo.bonus_pool_money:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('贡献值')}}</text>\r\n\t\t\t\t\t</view>\r\n          <view v-if=\"data.userinfo.product_deposit_mode\" class='item' data-url='/pagesC/my/productdeposit' @tap='goto'>\r\n            <text class='t2'>{{data.userinfo.product_deposit?data.userinfo.product_deposit:0}}</text>\r\n            <text class=\"t1\">{{t('押金')}}</text>\r\n          </view>\r\n\t\t\t\t\t<view v-if=\"data.userinfo.product_deposit_mode\" class='item' data-url='/pagesA/overdraft/detail' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.overdraft_money?data.userinfo.overdraft_money:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('信用额度')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"params.commissionwithdrawscoreshow==1\" class='item' data-url='/pagesB/my/commissionwithdrawscorelog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.commission_withdraw_score?data.userinfo.commission_withdraw_score:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('提现积分')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"params.fhcopiesshow==1\" class='item' data-url='/pagesA/my/fhcopieslog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.fhcopies?data.userinfo.fhcopies:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('分红份数')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"params.tongzhengshow==1\" class='item' data-url='/pagesA/my/tongzhenglog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.tongzheng?data.userinfo.tongzheng:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('通证')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"params.greenscoreshow==1\" class='item' data-url='/pagesB/greenscore/greenscorelognew' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.green_score?data.userinfo.green_score:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('绿色积分')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"params.activecoinshow==1\" class='item' data-url='/pagesB/my/activecoinlog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.active_coin?data.userinfo.active_coin:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('激活币')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"params.scoreweightshow==1\" class='item' data-url='/pagesB/my/scoreweightlog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.buy_fenhong_score_weight?data.userinfo.buy_fenhong_score_weight:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">积分权</text>\r\n\t\t\t\t\t</view>\r\n          <view v-if=\"params.showsilvermoney==1\" class='item' data-url='/pagesB/my/silvermoneylog' @tap='goto'>\r\n          \t<text class='t2'>{{data.userinfo.silvermoney?data.userinfo.silvermoney:0}}</text>\r\n          \t<text class=\"t1\">{{t('银值')}}</text>\r\n          </view>\r\n          <view v-if=\"params.showgoldmoney==1\" class='item' data-url='/pagesB/my/goldmoneylog' @tap='goto'>\r\n          \t<text class='t2'>{{data.userinfo.goldmoney?data.userinfo.goldmoney:0}}</text>\r\n          \t<text class=\"t1\">{{t('金值')}}</text>\r\n          </view>\r\n          <view v-if=\"params.showinviteredpacket==1\" class='item' data-url='/pagesB/inviteredpacket/redpacketlist' @tap='goto'>\r\n          \t<text class='t2'>{{data.userinfo.inviteredpacketnum?data.userinfo.inviteredpacketnum:0}}</text>\r\n          \t<text class=\"t1\">红包</text>\r\n          </view>\r\n          <view v-if=\"params.shopscoreshow==1\" class='item' data-url='/pagesC/my/shopscorelog' @tap='goto'>\r\n          \t<text class='t2'>{{data.userinfo.shopscore?data.userinfo.shopscore:0}}</text>\r\n          \t<text class=\"t1\">{{t('产品积分')}}</text>\r\n          </view>\r\n          <view v-if=\"params.dedamountshow==1\" class='item' data-url='/pagesC/my/dedamountlog' @tap='goto'>\r\n          \t<text class='t2'>{{data.userinfo.dedamount?data.userinfo.dedamount:0}}</text>\r\n          \t<text class=\"t1\">抵扣金</text>\r\n          </view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"custom_field\" v-if=\"data.userinfo.othermoney_status==1\">\r\n\t\t\t\t\t<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.money2?data.userinfo.money2:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('余额2')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.money3?data.userinfo.money3:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('余额3')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money4' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.money4?data.userinfo.money4:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('余额4')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money5' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.money5?data.userinfo.money5:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('余额5')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' data-url='/pagesExt/othermoney/frozen_moneylog' @tap='goto'>\r\n\t\t\t\t\t\t<text class='t2'>{{data.userinfo.frozen_money?data.userinfo.frozen_money:0}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{t('冻结金额')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"data.parent_show\">\r\n\t\t\t\t\t<view class=\"parent\" v-if=\"data.parent\" :style=\"'background: rgba('+t('color1rgb')+',10%);'\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t<image class=\"parentimg\" :src=\"data.parent.headimg\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"parentimg-tag\" :style=\"'background: rgba('+t('color1rgb')+',100%);'\">{{t('推荐人')}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2 flex1\">\r\n\t\t\t\t\t\t\t\t<view class=\"nick\">{{data.parent.nickname}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"nick\" v-if=\"data.parent && data.parent.weixin\" @tap=\"copy\" :data-text=\"data.parent.weixin\">微信号：{{data.parent.weixin}}<image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\"></image></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"data.parent && data.parent.tel\" @tap=\"goto\" :data-url=\"'tel::'+data.parent.tel\"><image :src=\"pre_url+'/static/img/tel2.png'\" class=\"handle-img\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"parent\" v-else :style=\"'background: rgba('+t('color1rgb')+',10%);'\">\r\n\t\t\t\t\t\t<image class=\"f1 parentimg\" :src=\"data.sysset.logo\"/>\r\n\t\t\t\t\t\t<view class=\"f2 flex1\">\r\n\t\t\t\t\t\t\t<view class=\"nick\">{{data.sysset.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"nick\">{{data.sysset.tel}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'tel::'+data.sysset.tel\"><image :src=\"pre_url+'/static/img/tel2.png'\" class=\"handle-img\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"params.seticonsize && params.seticonsize>0\">\r\n\t\t\t\t\t<view class=\"userset usersetl\" @tap=\"goto\" data-url=\"/pagesA/my/memberCode\" v-if=\"params.membercodeshow=='1'\" \r\n\t\t\t\t\t:style=\"{right:usercenterNavigationCustom == 0 ? params.seticonsize*2.2*2.8+'rpx' : (Number(navigationMenu.width) + Number(params.seticonsize) + Number(params.seticonsize)+10) +'px',top: navigationMenu.top + 'px'}\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/qrcode.png'\" class=\"img\" :style=\"'width:'+params.seticonsize*2.2+'rpx'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"userset\" @tap=\"goto\" data-url=\"/pagesExt/my/set\" v-if=\"params.seticonshow!=='0'\"\r\n\t\t\t\t\t:style=\"{right:usercenterNavigationCustom == 0 ? params.seticonsize*2.2+'rpx' : (Number(navigationMenu.width) + Number(params.seticonsize)) +'px',top: navigationMenu.top + 'px'}\">\r\n\t\t\t\t\t\t\t<image :src=\"params.seticon?params.seticon:pre_url+'/static/img/set.png'\" class=\"img\" :style=\"'width:'+params.seticonsize*2.2+'rpx;height:'+params.seticonsize*2.2+'rpx'\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t    <view class=\"userset\" v-if=\"params.seticonshow!=='0'\" @tap=\"goto\" data-url=\"/pagesExt/my/set\">\r\n\t\t\t        <image :src=\"params.seticon?params.seticon:pre_url+'/static/img/set.png'\" class=\"img\"/>\r\n\t\t\t    </view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t\r\n\t<view v-if=\"params.style==2\" :style=\"'background: linear-gradient(45deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%);'\" style=\"width:100%;position: absolute;top: 0\">\r\n\t\t<view class=\"dp-userinfo2\" :style=\"{background:'url('+params.bgimg+') no-repeat',backgroundSize:'cover',margin:(0*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',height:data.userinfo.othermoney_status == 1?'600rpx':'490rpx'}\"></view>\r\n\t</view>\r\n\t<view class=\"dp-userinfo2\" v-if=\"params.style==2\" :style=\"{margin:(0*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',height:'auto'}\">\r\n\t\t<view class=\"info\">\r\n\t\t\t<image class=\"headimg\" :src=\"data.userinfo.headimg\" @tap=\"weixinlogin\"/>\r\n\t\t\t<view class=\"nickname\">\r\n\t\t\t\t<view class=\"nick\">{{data.userinfo.nickname}} </view>\r\n\t\t\t\t<view v-if=\"params.realnameVerifyShow == 1\" @tap=\"goto\" data-url=\"/pagesExt/my/setrealname\">\r\n\t\t\t\t\t<text class=\"tag-gray\" v-if=\"data.userinfo.realname_status != 1\">未实名</text>\r\n\t\t\t\t\t<text class=\"tag-renzheng\" v-if=\"data.userinfo.realname_status == 1\">实名认证</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"data.zhaopin && data.zhaopin.show_zhaopin\" class=\"flex\">\r\n\t\t\t\t\t<text v-if=\"data.zhaopin.is_qiuzhi_renzheng && !data.zhaopin.is_qiuzhi_qianyue\" class=\"tag-renzheng\">认证保障中</text>\r\n\t\t\t\t\t<text v-if=\"data.zhaopin.is_qiuzhi_qianyue\" class=\"tag-renzheng\">签约保障中</text>\r\n\t\t\t\t\t<text v-if=\"data.zhaopin.is_zhaopin_renzheng\" class=\"tag-renzheng\">认证企业</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"desc\">ID：{{userinfo.id}}</view> -->\r\n\t\t\t\t<view style=\"display: flex;\" v-if=\"params.levelshow==1\">\r\n\t\t\t\t\t<view class=\"user-level\" @tap=\"openLevelup\" data-levelid=\"\">\r\n\t\t\t\t\t\t<image class=\"level-img\" :src='data.userlevel.icon' v-if=\"data.userlevel.icon\"/>\r\n\t\t\t\t\t\t<view class=\"level-name\">{{data.userlevel.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"user-level\" v-for=\"(item, index) in data.userlevelList\" :key=\"index\" @tap=\"openLevelup\" :data-levelid=\"item.id\">\r\n\t\t\t\t\t\t<image class=\"level-img\" :src='item.icon' v-if=\"item.icon\"/>\r\n\t\t\t\t\t\t<view class=\"level-name\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\r\n\t\t\t\t<view class=\"usermid\" v-if=\"params.midshow=='1'\">会员ID：<text user-select=\"true\" selectable=\"true\">{{data.userinfo.id}}</text></view>\r\n\t\t\t\t<view class=\"usermid\" v-if=\"data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1\">邀请码：<text user-select=\"true\" selectable=\"true\">{{data.userinfo.yqcode}}</text></view>\r\n\t\t\t\t<view class=\"flex\" v-if=\"data.userinfo && data.userinfo.show_commission_max\">\r\n\t\t\t\t\t<text v-if=\"data.userinfo.commission_to_score==1\" @tap=\"goto\" data-url=\"/pagesB/my/commissionmaxtoscore\" class=\"tag-renzheng\">{{t('佣金上限')}}：{{data.userinfo.remain_commission_max}}</text>\r\n\t\t\t\t\t<text v-else class=\"tag-renzheng\">{{t('佣金上限')}}：{{data.userinfo.remain_commission_max}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n      <image class=\"level-img\" v-if=\"data.userinfo.tag_pic\" style=\"width:100rpx;height: 40rpx;margin-top: 15rpx\" :src='data.userinfo.tag_pic'/>\r\n\t\t\t<view class=\"ktnum\" v-if=\"params.ktnumshow=='1'\" style=\"display: flex; position: absolute; right: 30rpx; top:30%;color:#ffff; \" >开团次数：<text style=\"font-size: 24rpx;line-height: 40rpx;\" >{{data.userinfo.ktnum}}</text></view>\r\n\t\t</view>\r\n    <view class=\"custom_field\" style=\"margin-top:2rpx;padding:0;padding-left:168rpx;font-size: 24rpx;color:rgba(255,255,255,0.8)\" v-if=\"data.userinfo.agentarea\">{{data.userinfo.agentarea}}</view>\r\n\t\t<view class=\"custom_field\">\r\n\t\t\t<view class='item-style2' v-if=\"params.moneyshow==1\" data-url='/pagesExt/money/recharge' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.money}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('余额')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0\" data-url='/pages/commission/index' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.commission}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('佣金')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.scoreshow==1\" data-url='/pagesExt/my/scorelog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.score}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('积分')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.winecoinshow==1\" data-url='/pagesExt/winecoin/index' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.wine_coin}}</text>\r\n\t\t\t\t<text class=\"t1\">酒币</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.bscoreshow==1\" data-url='/pagesExt/my/bscore' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.bscore+data.userinfo.score}}</text>\r\n\t\t\t\t<text class=\"t1\">总{{t('积分')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"!isNull(data.userinfo.service_fee) && params.servicefeeshow==1\" data-url='/pagesB/service_fee/recharge' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.service_fee}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('服务费')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.couponshow==1\" data-url='/pagesExt/coupon/mycoupon' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.couponcount}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.formshow==1\" data-url='/pagesA/form/formlog?st=1' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.formcount}}</text>\r\n\t\t\t\t<text class=\"t1\">{{params.formtext}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"data.userinfo.fuchi_money>=0\" data-url='/pagesExt/my/fuchi' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.fuchi_money}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('扶持金')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"data.userinfo.gongxian>=0\" data-url='/pagesExt/my/gongxianLog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.gongxian?data.userinfo.gongxian:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('贡献')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.xiaofeishow==1\" data-url='/pagesA/my/xiaofeimoneylog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.xiaofei_money?data.userinfo.xiaofei_money:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('冻结佣金')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.freezecreditshow==1\" data-url='/pagesB/my/freezecreditlog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.freeze_credit?data.userinfo.freeze_credit:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('冻结账户')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.bonuspoolshow==1\" data-url='/pagesA/bonuspool/withdraw' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.bonus_pool_money?data.userinfo.bonus_pool_money:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('贡献值')}}</text>\r\n\t\t\t</view>\r\n      <view v-if=\"data.userinfo.product_deposit_mode\" class='item-style2' data-url='/pagesC/my/productdeposit' @tap='goto'>\r\n        <text class='t2'>{{data.userinfo.product_deposit?data.userinfo.product_deposit:0}}</text>\r\n        <text class=\"t1\">{{t('押金')}}</text>\r\n      </view>\r\n\t\t\t<view class='item-style2' v-if=\"params.overdraftmoneyshow==1\" data-url='/pagesA/overdraft/detail' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.overdraft_money?data.userinfo.overdraft_money:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('信用额度')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"params.commissionwithdrawscoreshow==1\" class='item-style2' data-url='/pagesB/my/commissionwithdrawscorelog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.commission_withdraw_score?data.userinfo.commission_withdraw_score:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('提现积分')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.fhcopiesshow==1\" data-url='/pagesA/my/fhcopieslog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.fhcopies?data.userinfo.fhcopies:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('分红份数')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' v-if=\"params.tongzhengshow==1\" data-url='/pagesA/my/tongzhenglog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.tongzheng?data.userinfo.tongzheng:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('通证')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"params.greenscoreshow==1\" class='item-style2' data-url='/pagesB/greenscore/greenscorelognew' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.green_score?data.userinfo.green_score:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('绿色积分')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"params.activecoinshow==1\" class='item-style2' data-url='/pagesB/my/activecoinlog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.active_coin?data.userinfo.active_coin:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('激活币')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"params.scoreweightshow==1\" class='item-style2' data-url='/pagesB/my/scoreweightlog' @tap='goto'>\r\n\t\t\t\t<text class='t2'>{{data.userinfo.buy_fenhong_score_weight?data.userinfo.buy_fenhong_score_weight:0}}</text>\r\n\t\t\t\t<text class=\"t1\">积分权</text>\r\n\t\t\t</view>\r\n      <view v-if=\"params.showsilvermoney==1\" class='item-style2' data-url='/pagesB/my/silvermoneylog' @tap='goto'>\r\n      \t<text class='t2'>{{data.userinfo.silvermoney?data.userinfo.silvermoney:0}}</text>\r\n      \t<text class=\"t1\">{{t('银值')}}</text>\r\n      </view>\r\n      <view v-if=\"params.showgoldmoney==1\" class='item-style2' data-url='/pagesB/my/goldmoneylog' @tap='goto'>\r\n      \t<text class='t2'>{{data.userinfo.goldmoney?data.userinfo.goldmoney:0}}</text>\r\n      \t<text class=\"t1\">{{t('金值')}}</text>\r\n      </view>\r\n      <view v-if=\"params.showinviteredpacket==1\" class='item-style2' data-url='/pagesB/inviteredpacket/redpacketlist' @tap='goto'>\r\n      \t<text class='t2'>{{data.userinfo.inviteredpacketnum?data.userinfo.inviteredpacketnum:0}}</text>\r\n      \t<text class=\"t1\">红包</text>\r\n      </view>\r\n      <view v-if=\"params.shopscoreshow==1\" class='item-style2' data-url='/pagesC/my/shopscorelog' @tap='goto'>\r\n      \t<text class='t2'>{{data.userinfo.shopscore?data.userinfo.shopscore:0}}</text>\r\n      \t<text class=\"t1\">{{t('产品积分')}}</text>\r\n      </view>\r\n      <view v-if=\"params.dedamountshow==1\" class='item-style2' data-url='/pagesC/my/dedamountlog' @tap='goto'>\r\n      \t<text class='t2'>{{data.userinfo.dedamount?data.userinfo.dedamount:0}}</text>\r\n      \t<text class=\"t1\">抵扣金</text>\r\n      </view>\r\n\t\t</view>\r\n\t\t<view class=\"custom_field\" v-if=\"data.userinfo.othermoney_status==1\">\r\n\t\t\t<view class='item-style2' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto' style=\"margin-right:0\">\r\n\t\t\t\t<text class='t2'>{{data.userinfo.money2?data.userinfo.money2:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('余额2')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto' style=\"margin-right:0\">\r\n\t\t\t\t<text class='t2'>{{data.userinfo.money3?data.userinfo.money3:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('余额3')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' data-url='/pagesExt/othermoney/withdraw?type=money4' @tap='goto' style=\"margin-right:0\">\r\n\t\t\t\t<text class='t2'>{{data.userinfo.money4?data.userinfo.money4:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('余额4')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class='item-style2' data-url='/pagesExt/othermoney/withdraw?type=money5' @tap='goto' style=\"margin-right:0\">\r\n\t\t\t\t<text class='t2'>{{data.userinfo.money5?data.userinfo.money5:0}}</text>\r\n\t\t\t\t<text class=\"t1\">{{t('余额5')}}</text>\r\n\t\t\t</view>\r\n\t\t\t\t<view class='item-style2' data-url='/pagesExt/othermoney/frozen_moneylog' @tap='goto' style=\"margin-right:0\">\r\n\t\t\t\t\t<text class='t2'>{{data.userinfo.frozen_money?data.userinfo.frozen_money:0}}</text>\r\n\t\t\t\t\t<text class=\"t1\">{{t('冻结金额')}}</text>\r\n\t\t\t\t</view>\r\n\t\t</view>\r\n\t\t<block v-if=\"params.seticonsize && params.seticonsize>0 \">\r\n\t\t\t\t<view class=\"userset usersetl\"\r\n\t\t\t\t @tap=\"goto\" data-url=\"/pagesA/my/memberCode\" v-if=\"params.membercodeshow=='1'\" :style=\"'right:'+params.seticonsize*2.2*2.8+'rpx;top:' + NavigationCustomTop + 'px'\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/qrcode.png'\" class=\"img\" :style=\"'width:'+params.seticonsize*2.2+'rpx;height:'+params.seticonsize*2.2+'rpx'\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"userset\" @tap=\"goto\" data-url=\"/pagesExt/my/set\" v-if=\"params.seticonshow!=='0'\" :style=\"'right:'+params.seticonsize*2.2+'rpx;top:' + NavigationCustomTop + 'px'\">\r\n\t\t\t\t\t\t<image :src=\"params.seticon?params.seticon:pre_url+'/static/img/set.png'\" class=\"img\" :style=\"'width:'+params.seticonsize*2.2+'rpx;height:'+params.seticonsize*2.2+'rpx'\"/>\r\n\t\t\t\t</view>\r\n\t\t</block>\r\n\t\t<block v-else>\r\n\t\t\t\t<view class=\"userset\" @tap=\"goto\" data-url=\"/pagesExt/my/set\" v-if=\"params.seticonshow!=='0'\">\r\n\t\t\t\t\t\t<image :src=\"params.seticon?params.seticon:pre_url+'/static/img/set.png'\" class=\"img\"/>\r\n\t\t\t\t</view>\r\n\t\t</block>\r\n\t        \r\n\t\t<block v-if=\"platform=='wx'\">\r\n\t\t\t<view class=\"usercard\" :style=\"{top:usercenterNavigationCustom == 0 ? '140rpx':NavigationCustomTop + 28 +'px'}\" v-if=\"params.cardshow==1 && data.userinfo.card_code\" @tap=\"opencard\" :data-card_id=\"data.userinfo.card_id\" :data-card_code=\"data.userinfo.card_code\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t\t<view class=\"usercard\" :style=\"{top:usercenterNavigationCustom == 0 ? '140rpx':NavigationCustomTop + 28 +'px'}\" v-if=\"params.cardshow==1 && !data.userinfo.card_code\" @tap=\"addmembercard\" :data-card_id=\"data.card_id\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t</block>\r\n\t\t<block v-if=\"platform=='mp'\">\r\n\t\t\t<view class=\"usercard\" v-if=\"params.cardshow==1 && data.userinfo.card_code\" @tap=\"opencard\" :data-card_id=\"data.userinfo.card_id\" :data-card_code=\"data.userinfo.card_code\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t\t<view class=\"usercard\" v-if=\"params.cardshow==1 && !data.userinfo.card_code\" @tap=\"goto\" :data-url=\"data.card_returl\"><image class=\"img\" :src=\"pre_url+'/static/img/ico-card2.png'\"/><text class=\"txt\">会员卡</text></view>\r\n\t\t</block>\r\n\t</view>\r\n\t<view class=\"dp-userinfo-order\" v-if=\"params.style==2 && data.parent_show\" :style=\"{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'}\">\r\n\t\t<view class=\"parent\" v-if=\"data.parent\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<image class=\"parentimg\" :src=\"data.parent.headimg\"></image>\r\n\t\t\t\t\t<view class=\"parentimg-tag\" :style=\"'background: rgba('+t('color1rgb')+',100%);'\">{{t('推荐人')}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2 flex1\">\r\n\t\t\t\t\t<view class=\"nick\">{{data.parent.nickname}}</view>\r\n\t\t\t\t\t<view class=\"nick\" v-if=\"data.parent && data.parent.weixin\" @tap=\"copy\" :data-text=\"data.parent.weixin\">微信号：{{data.parent.weixin}}<image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f3\" v-if=\"data.parent && data.parent.tel\" @tap=\"goto\" :data-url=\"'tel::'+data.parent.tel\"><image :src=\"pre_url+'/static/img/tel2.png'\" class=\"handle-img\"></image></view>\r\n\t\t</view>\r\n\t\t<view class=\"parent\" v-else>\r\n\t\t\t<image class=\"f1 parentimg\" :src=\"data.sysset.logo\"/>\r\n\t\t\t<view class=\"f2 flex1\">\r\n\t\t\t\t<view class=\"nick\">{{data.sysset.name}}</view>\r\n\t\t\t\t<view class=\"nick\">{{data.sysset.tel}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'tel::'+data.sysset.tel\"><image :src=\"pre_url+'/static/img/tel2.png'\" class=\"handle-img\"></image></view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"dp-userinfo-order\" :style=\"{'margin':((params.padding_y*2.2) || 20)+'rpx '+(params.padding_x*2.2)+'rpx','marginTop':((params.style==2 && !data.parent_show && data.userinfo.othermoney_status !=1) ? '20rpx':((params.padding_y*2.2) || 20)+'rpx ')}\" v-if=\"params.ordershow==1 && (!params.showtype || params.showtype == 0)\">\r\n\t\t<view class=\"head\">\r\n\t\t\t<text class=\"f1\">我的订单</text>\r\n\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/pagesExt/order/orderlist\"><text>查看全部订单</text><image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/></view>\r\n\t\t</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/pagesExt/order/orderlist?st=0\">\r\n\t\t\t\t\t<text class=\"iconfont icondaifukuan\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.orderinfo.count0>0\">{{data.orderinfo.count0}}</view>\r\n\t\t\t\t\t<text class=\"t3\">待付款</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/pagesExt/order/orderlist?st=1\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order2.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont icondaifahuo\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.orderinfo.count1>0\">{{data.orderinfo.count1}}</view>\r\n\t\t\t\t\t<text class=\"t3\">待发货</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/pagesExt/order/orderlist?st=2\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order3.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont icondaishouhuo\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.orderinfo.count2>0\">{{data.orderinfo.count2}}</view>\r\n\t\t\t\t\t<text class=\"t3\">待收货</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/pagesExt/order/orderlist?st=3\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order4.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont iconyiwancheng\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.orderinfo.count3>0\">{{data.orderinfo.count3}}</view>\r\n\t\t\t\t\t<text class=\"t3\">已完成</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/pagesExt/order/refundlist\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order4.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont icontuikuandingdan\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.orderinfo.count4>0\">{{data.orderinfo.count4}}</view>\r\n\t\t\t\t\t<text class=\"t3\">退款/售后</text>\r\n\t\t\t </view>\r\n      <view class=\"item\" v-if=\"data.orderinfo.transfer_order_parent_check\" @tap=\"goto\" data-url=\"/pagesC/transferorderparent/orderlist\">\r\n        <!-- <image src=\"/static/img/order4.png\" class=\"image\"/> -->\r\n        <text class=\"iconfont icondaifahuo\" :style=\"{color:t('color1')}\"></text>\r\n        <view class=\"t2\" v-if=\"data.orderinfo.count5>0\">{{data.orderinfo.count5}}</view>\r\n        <text class=\"t3\">审核订单</text>\r\n      </view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"dp-userinfo-order\" :style=\"{'margin':((params.padding_y*2.2) || 20)+'rpx '+(params.padding_x*2.2)+'rpx','marginTop':((params.style==2 && !data.parent_show && data.userinfo.othermoney_status !=1) ? '20rpx':((params.padding_y*2.2) || 20)+'rpx ')}\"\r\n\tv-if=\"params.ordershow==1 && params.showtype == 1\">\r\n\t\t<view class=\"head\">\r\n\t\t\t<text class=\"f1\">{{params.ordertitle}}</text>\r\n\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/pagesExt/order/orderlist\"><text>查看全部订单</text><image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/></view>\r\n\t\t</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t <view class=\"item\" v-for=\"(item,index) in params.orderData\" @click=\"optionJump(item.type)\" v-if=\"item.show != 0\">\r\n\t\t\t\t\t<view class=\"image-view\">\r\n\t\t\t\t\t\t<image :src=\"item.imgurl\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"item.num>0\">{{item.num}}</view>\r\n\t\t\t\t\t<text class=\"t3\">{{item.text}}</text>\r\n\t\t\t </view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"dp-userinfo-order\" :style=\"{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx','marginTop':(params.padding_y == 0 || params.padding_y > 10 ? '20rpx':(params.padding_y*2.2)+'rpx')}\" v-if=\"params.scoreshopordershow==1\">\r\n\t\t<view class=\"head\">\r\n\t\t\t<text class=\"f1\">{{t('积分')}}兑换订单</text>\r\n\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/activity/scoreshop/orderlist\"><text>查看全部订单</text><image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/></view>\r\n\t\t</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/activity/scoreshop/orderlist?st=0\">\r\n\t\t\t\t\t<text class=\"iconfont icondaifukuan\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.scoreshoporder.count0>0\">{{data.scoreshoporder.count0}}</view>\r\n\t\t\t\t\t<text class=\"t3\">待付款</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/activity/scoreshop/orderlist?st=1\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order2.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont icondaifahuo\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.scoreshoporder.count1>0\">{{data.scoreshoporder.count1}}</view>\r\n\t\t\t\t\t<text class=\"t3\">待发货</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/activity/scoreshop/orderlist?st=2\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order3.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont icondaishouhuo\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.scoreshoporder.count2>0\">{{data.scoreshoporder.count2}}</view>\r\n\t\t\t\t\t<text class=\"t3\">待收货</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/activity/scoreshop/orderlist?st=3\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order4.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont iconyiwancheng\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.scoreshoporder.count3>0\">{{data.scoreshoporder.count3}}</view>\r\n\t\t\t\t\t<text class=\"t3\">已完成</text>\r\n\t\t\t </view>\r\n\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"/activity/scoreshop/orderlist?st=10\" v-if=\"params.scoreshowrefund == 1\">\r\n\t\t\t\t\t<!-- <image src=\"/static/img/order4.png\" class=\"image\"/> -->\r\n\t\t\t\t\t<text class=\"iconfont icontuikuandingdan\" :style=\"{color:t('color1')}\"></text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"data.scoreshoporder.count4>0\">{{data.scoreshoporder.count4}}</view>\r\n\t\t\t\t\t<text class=\"t3\">退款/售后</text>\r\n\t\t\t </view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<view class=\"task_list\" :style=\"{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx;'+ (params.padding_y == 0 || params.padding_y > 10 ? 'margin-top:20rpx':'')}\" v-if=\"platform=='wx' && data.sysset.task_banner\" @click=\"totaskbanner\">\r\n\t\t<view class=\"item\" @tap=\"goto\" data-url=\"moneylog\">\r\n\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/task_banner.png'\"></image></view>\r\n\t\t\t<view class=\"f2\">广告任务</view>\r\n\t\t\t<text class=\"f3\">余量{{sy_count !=''?sy_count:data.sysset.sy_count}}次</text>\r\n\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"f4\"></image>\r\n\t\t</view>\r\n\t</view>\r\n\t<view v-if=\"data.userinfo.show_green_score\" class=\"task_list\" :style=\"{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx','marginTop':(params.padding_y == 0 || params.padding_y > 10 ? '20rpx':(params.padding_y*2.2)+'rpx'),'background':'rgba('+t('color1rgb')+',1)'}\">\r\n\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pagesB/greenscore/greenscorelognew\">\r\n\t\t\t<view class=\"icon-view\"><image :src=\"pre_url+'/static/img/points.png'\"></image></view>\r\n\t\t\t<view class=\"title-text\">{{t('绿色积分')}}</view>\r\n\t\t\t<view class=\"data-num-view flex-col\">\r\n\t\t\t\t<view class=\"top-num-text\">{{data.userinfo.green_score}}</view>\r\n\t\t\t\t<view class=\"bot-num-text\">{{data.userinfo.green_score_price}}</view>\r\n\t\t\t</view>\r\n\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_arrowright.png'\" class=\"jiantou-icon\"></image>\r\n\t\t</view>\r\n\t</view>\r\n\t<view v-if=\"data.userinfo.show_cashback\" class=\"task_list\" :style=\"{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx','marginTop':(params.padding_y == 0 || params.padding_y > 10 ? '20rpx':(params.padding_y*2.2)+'rpx'),'background':'rgba('+t('color1rgb')+',1)'}\">\r\n\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pagesC/releasePoints/details\">\r\n\t\t\t<view class=\"icon-view\"><image :src=\"pre_url+'/static/img/releasepoints.png'\"></image></view>\r\n\t\t\t<view class=\"title-text\">{{t('释放积分')}}</view>\r\n\t\t\t<view class=\"data-num-view flex-col\">\r\n\t\t\t\t<view class=\"top-num-text\">{{data.userinfo.cashback_price}}</view>\r\n\t\t\t\t<view class=\"bot-num-text\">+{{data.userinfo.last_cashback_price}}</view>\r\n\t\t\t</view>\r\n\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_arrowright.png'\" class=\"jiantou-icon\"></image>\r\n\t\t</view>\r\n\t</view>\r\n\t<view v-if=\"data.userinfo.show_cashback_multiply\" class=\"task_list\" :style=\"{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx','marginTop':(params.padding_y == 0 || params.padding_y > 10 ? '20rpx':(params.padding_y*2.2)+'rpx'),'background':'rgba('+t('color1rgb')+',1)'}\">\r\n\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pagesC/releasePoints/cashbacklog\">\r\n\t\t\t<view class=\"icon-view\"><image :src=\"pre_url+'/static/img/zengzhi.png'\"></image></view>\r\n\t\t\t<view class=\"title-text\">增值{{t('释放积分')}}</view>\r\n\t\t\t<view class=\"data-num-view flex-col\">\r\n\t\t\t\t<view class=\"top-num-text\">{{data.userinfo.cashback_price_multiply}}</view>\r\n\t\t\t\t<view class=\"bot-num-text\">+{{data.userinfo.last_cashback_price_multiply}}</view>\r\n\t\t\t</view>\r\n\t\t\t<image :src=\"pre_url+'/static/img/shortvideo_arrowright.png'\" class=\"jiantou-icon\"></image>\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tlet videoAd = null;\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\ttextset:app.globalData.textset,\r\n\t\t\t\tplatform:app.globalData.platform,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\ttask_banner:false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tchoujiang_id:0,\r\n\t\t\t\tsy_count:'',\r\n\t\t\t\tvideoAd:'',\r\n\t\t\t\ttabFlex:false,\r\n\t\t\t\tcustom_field:false,\r\n\t\t\t\tNavigationCustomTop:'20',\r\n\t\t\t\tusercenterNavigationCustom:0,\r\n\t\t\t\tnavigationMenu:{}\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.usercenterNavigationCustom = app.globalData.usercenterNavigationCustom;\r\n\t\t\tif(app.globalData.usercenterNavigationCustom != 0){\r\n\t\t\t\tthis.navigationMenu = wx.getMenuButtonBoundingClientRect();\r\n\t\t\t\tthis.NavigationCustomTop = this.navigationMenu.height + this.navigationMenu.top;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef H5\r\n\t\t\tlet arr = [];\r\n\t\t\tif(this.params.moneyshow == 1) arr.push(true);\r\n\t\t\tif(this.params.commissionshow == 1 && this.data.userlevel && this.data.userlevel.can_agent>0) arr.push(true);\r\n\t\t\tif(this.params.scoreshow == 1) arr.push(true);\r\n\t\t\tif(this.params.bscoreshow == 1) arr.push(true);\r\n\t\t\tif(this.params.couponshow == 1) arr.push(true);\r\n\t\t\tif(this.data.userinfo.fuchi_money >= 0) arr.push(true);\r\n\t\t\tif(this.data.userinfo.gongxian >= 0) arr.push(true);\r\n\t\t\tif(this.params.bonuspoolshow == 1) arr.push(true);\r\n\t\t\tif(this.params.xiaofeishow == 1) arr.push(true);\r\n\t\t\tif(this.params.overdraftmoneyshow == 1) arr.push(true);\r\n\t\t\tif(this.params.formshow == 1) arr.push(true);\r\n\t\t\tif(this.params.fhcopiesshow == 1) arr.push(true);\r\n\t\t\tif(arr.length < 4){\r\n\t\t\t\tthis.tabFlex = false\r\n\t\t\t}else{\r\n\t\t\t\tthis.tabFlex = true\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\tif(this.params.style == 1){\r\n\t\t\t\tconst tabNum = this.$refs.custom_field1.$children.length;\r\n\t\t\t\tif(tabNum<4){\r\n\t\t\t\t\tthis.tabFlex = false\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.tabFlex = true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\toptionJump(type){\r\n\t\t\t\tlet url = '';\r\n\t\t\t\tswitch (type){\r\n\t\t\t\t\tcase 'daifukuan':\r\n\t\t\t\t\turl = '/pagesExt/order/orderlist?st=0'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'daifahuo':\r\n\t\t\t\t\turl = '/pagesExt/order/orderlist?st=1'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'daishouhuo':\r\n\t\t\t\t\turl = '/pagesExt/order/orderlist?st=2'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'wancheng':\r\n\t\t\t\t\turl = '/pagesExt/order/orderlist?st=3'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'tuikuan':\r\n\t\t\t\t\turl = '/pagesExt/order/orderlist?st=4'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tapp.goto(url)\r\n\t\t\t},\r\n\t\t\topenLevelup:function(e){\r\n\t\t\t\tvar levelid = e.currentTarget.dataset.levelid\r\n\t\t\t\tif(parseInt(this.params.levelclick) !== 0){\r\n\t\t\t\t\tapp.goto('/pagesExt/my/levelinfo?id='+levelid)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topencard:function(e){\r\n\t\t\t\tvar cardId = e.currentTarget.dataset.card_id\r\n\t\t\t\tvar code = e.currentTarget.dataset.card_code\r\n\t\t\t\tif(app.globalData.platform == 'mp') {\r\n\t\t\t\t\tvar jweixin = require('jweixin-module');\r\n\t\t\t\t\tjweixin.openCard({\r\n\t\t\t\t\t\tcardList: [{\r\n\t\t\t\t\t\t\tcardId: cardId,\r\n\t\t\t\t\t\t\tcode: code\r\n\t\t\t\t\t\t}],\r\n\t\t\t\t\t\tsuccess:function(res) { }\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\twx.openCard({\r\n\t\t\t\t\t\tcardList: [{\r\n\t\t\t\t\t\t\tcardId: cardId,\r\n\t\t\t\t\t\t\tcode: code\r\n\t\t\t\t\t\t}],\r\n\t\t\t\t\t\tsuccess:function(res) { }\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t//领取微信会员卡\r\n\t\t\taddmembercard:function(e){\r\n\t\t\t\tvar cardId = e.currentTarget.dataset.card_id\r\n\t\t\t\tapp.post('ApiCoupon/getmembercardparam',{card_id:cardId},function(res){\r\n\t\t\t\t\tif(res.status==0){\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\twx.navigateToMiniProgram({\r\n\t\t\t\t\t\tappId: 'wxeb490c6f9b154ef9', // 固定为此appid，不可改动\r\n\t\t\t\t\t\textraData: res.extraData, // 包括encrypt_card_id outer_str biz三个字段，须从step3中获得的链接中获取参数\r\n\t\t\t\t\t\tsuccess: function() {},\r\n\t\t\t\t\t\tfail: function() {},\r\n\t\t\t\t\t\tcomplete: function() {}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tweixinlogin:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif(that.data.userinfo.nickname != '未登录' || that.data.userinfo.id != 0) return; // 判断当前登录状态\r\n\t\t\t\tif(app.globalData.platform == 'wx' || app.globalData.platform == 'mp'){\r\n\t\t\t\t\tapp.authlogin(function(res){\r\n\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\t\tvar pages = getCurrentPages(); //获取加载的页面\r\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n\t\t\t\t\t\t\tcurrentPage.$vm.getdata();\r\n\t\t\t\t\t\t} else {\r\n              app.goto('/pages/index/login');return;//后台有设置的强制事件，插件无法处理，直接跳转登录页面\r\n\t\t\t\t\t\t\t//app.error(res.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n          app.goto('/pages/index/login');return;//后台有设置的强制事件，插件无法处理，直接跳转登录页面\r\n        }\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\tgetdata(){\r\n\t\t\t\tvar that  = this;\r\n\t\t\t\tif (app.globalData.platform == 'wx' && that.data.sysset.rewardedvideoad && !that.videoAd && wx.createRewardedVideoAd) {\r\n\t\t\t\t\tconsole.log('开始2');\r\n\t\t\t\t\tthat.videoAd = wx.createRewardedVideoAd({\r\n\t\t\t\t\t\tadUnitId: that.data.sysset.rewardedvideoad\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.videoAd.onLoad(() => {})\r\n\t\t\t\t\tthat.videoAd.onError((err) => {})\r\n\t\t\t\t\tthat.videoAd.onClose(res2 => {\r\n\t\t\t\t\t\tthat.isdoing = false;\r\n\t\t\t\t\t\tconsole.log(res2,'res');\r\n\t\t\t\t\t\r\n\t\t\t\t\t\tif (res2 && res2.isEnded) {\r\n\t\t\t\t\t\t\tconsole.log('开始走addRecord');\r\n\t\t\t\t\t\t\tthat.toAddRecord();\t\t\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('不走啊啊啊');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t*/\r\n\t\t\ttotaskbanner(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t//if(!that.videoAd){\r\n\t\t\t\t//\tthat.getdata();\r\n\t\t\t\t//}\r\n\t\t\t\tapp.post('ApiTaskBanner/getStatus', {}, function(data) {\r\n\t\t\t\t\tif(data.status ==1){\r\n\t\t\t\t\t\tif (app.globalData.platform == 'wx' && that.data.sysset.rewardedvideoad && wx.createRewardedVideoAd) {\r\n\t\t\t\t\t\t\tapp.showLoading();\r\n\t\t\t\t\t\t\tif(!app.globalData.rewardedVideoAd[that.data.sysset.rewardedvideoad]){\r\n\t\t\t\t\t\t\t\tapp.globalData.rewardedVideoAd[that.data.sysset.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: that.data.sysset.rewardedvideoad});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar rewardedVideoAd = app.globalData.rewardedVideoAd[that.data.sysset.rewardedvideoad];\r\n\t\t\t\t\t\t\trewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});\r\n\t\t\t\t\t\t\trewardedVideoAd.onError((err) => {\r\n\t\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\t\t//app.alert(err.errMsg);\r\n\t\t\t\t\t\t\t\tconsole.log('onError event emit', err)\r\n\t\t\t\t\t\t\t\trewardedVideoAd.offLoad()\r\n\t\t\t\t\t\t\t\trewardedVideoAd.offClose();\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\trewardedVideoAd.onClose(res => {\r\n\t\t\t\t\t\t\t\tapp.globalData.rewardedVideoAd[that.data.sysset.rewardedvideoad] = null;\r\n\t\t\t\t\t\t\t\tif (res && res.isEnded) {\r\n\t\t\t\t\t\t\t\t\t//app.alert('播放结束 发放奖励');\r\n\t\t\t\t\t\t\t\t\tthat.toAddRecord();\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log('播放中途退出，不下发奖励');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\trewardedVideoAd.offLoad()\r\n\t\t\t\t\t\t\t\trewardedVideoAd.offClose();\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else if(data.status ==233){\r\n\t\t\t\t\t\tthat.choujiang_id = data.choujiang_id;\r\n\t\t\t\t\t\tapp.confirm('您可以进行抽奖活动，是否继续?', function () {\r\n\t\t\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\t\t\tapp.post('ApiTaskBanner/setChoujiangStatus', {}, function (data) {\r\n\t\t\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\t\t\tapp.goto('/activity/xydzp/index?id='+that.choujiang_id);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoAddRecord(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiTaskBanner/addRecord', {}, function(data) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tvar pages = getCurrentPages(); //获取加载的页面\r\n\t\t\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n\t\t\t\t\tcurrentPage.$vm.getdata();\r\n\t\t\t\t\tif(data.status ==233){\t\r\n\t\t\t\t\t\tthat.sy_count = data.sy_count;\r\n\t\t\t\t\t\tthat.choujiang_id = data.choujiang_id;\r\n\t\t\t\t\t\tapp.confirm('您可以进行抽奖活动，是否继续?', function () {\r\n\t\t\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\t\t\tapp.post('ApiTaskBanner/setChoujiangStatus', {}, function (data) {\r\n\t\t\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\t\t\tapp.goto('/activity/xydzp/index?id='+that.choujiang_id);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else if(data.status ==1){\r\n\t\t\t\t\t\tthat.sy_count = data.sy_count;\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-userinfo{position:relative;overflow: hidden;}\r\n.dp-userinfo .banner{width:100%;margin-top:120rpx;border-radius:16rpx;background:#fff;padding:0 20rpx 10rpx;color:#333;position:relative;}\r\n.dp-userinfo .banner .info{display:flex;align-items:flex-end}\r\n.dp-userinfo .banner .info .f1{display:flex;flex-direction:column;}\r\n.dp-userinfo .banner .headimg{ margin-top:-60rpx;width:148rpx;height:148rpx;border-radius:50%;margin-right:20rpx;border:3px solid #eee;}\r\n.dp-userinfo .banner .info{margin-left:20rpx;display:flex;flex:auto;}\r\n.dp-userinfo .banner .info .nickname{min-width:140rpx;max-width:460rpx;text-align:center;height:80rpx;line-height:80rpx;font-size:34rpx;font-weight:bold;max-width: 300rpx;overflow: hidden;white-space: nowrap;}\r\n.dp-userinfo .banner .getbtn{ width:120rpx;height:44rpx;padding:0 3px;line-height:44rpx;font-size: 24rpx;background: #09BB07;color:#fff;position: absolute;top:76rpx;left:10rpx;}\r\n.dp-userinfo .banner .user-level{margin-left:5px;color:#b48b36;background-color:#ffefd4;margin-top:2px;width:auto;height:36rpx;border-radius:18rpx;padding:0 20rpx;display:flex;align-items:center}\r\n.dp-userinfo .banner .user-level .level-img{width:32rpx;height:32rpx;margin-right:3px;margin-left:-14rpx;border-radius:50%;}\r\n.dp-userinfo .banner .user-level .level-name{font-size:24rpx;}\r\n.dp-userinfo .banner .user-level image{border-radius:50%;}\r\n.dp-userinfo .banner .usercard{position:absolute;right:32rpx;top:28rpx;width:160rpx;height:60rpx;text-align:center;border:1px solid #FFB2B2;border-radius:8rpx;color:#FC4343;font-size:24rpx;font-weight:bold;display:flex;align-items:center;justify-content:center}\r\n.dp-userinfo .banner .usercard .img{width:30rpx;height:30rpx;margin-right:8rpx;padding-bottom:4rpx}\r\n\r\n.dp-userinfo .custom_field{display:flex;width:100%;align-items:center;padding:16rpx 8rpx;background:#fff;}\r\n.dp-userinfo .custom_field .item{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;}\r\n.dp-userinfo .custom_field_flex{display:flex;width:100%;align-items:center;padding:16rpx 8rpx;background:#fff;flex-wrap: wrap;}\r\n.dp-userinfo .custom_field_flex .item{flex:1; display:flex;flex-direction:column;justify-content:center;text-align:center;width: auto;margin-bottom: 10rpx;margin-right: 10rpx;}\r\n\r\n.dp-userinfo .item .t1{color:#666;font-size:26rpx;align-items:center;min-width: 140rpx;text-align: center;}\r\n.dp-userinfo .item .t2{color:#111;font-weight:bold;font-size:36rpx;align-items:center;min-width: 140rpx;text-align: center;}\r\n\r\n.dp-userinfo .userset{width:54rpx;height:54rpx;padding:10rpx;position:absolute;top:40rpx;right:30rpx}\r\n.dp-userinfo .userset .img{width:100%;height:100%}\r\n\r\n.dp-userinfo2{height:490rpx;display:flex;flex-direction:column;position:relative}\r\n.dp-userinfo2 .info{display:flex;margin-top:60rpx;margin-left:40rpx}\r\n.dp-userinfo2 .info .headimg{width:108rpx;height:108rpx;background:#fff;border:3rpx solid rgba(255,255,255,0.7);border-radius:50%}\r\n.dp-userinfo2 .info .nickname{margin-left:20rpx;display:flex;flex-direction:column;justify-content:center}\r\n.dp-userinfo2 .info .nickname .nick{font-size:36rpx;font-weight:bold;color:#fff;height:60rpx;line-height:60rpx;max-width:400rpx;overflow:hidden;margin-right:10rpx}\r\n.dp-userinfo2 .info .nickname .desc{font-size:24rpx;color:rgba(255,255,255,0.6);height:40rpx;line-height:40rpx}\r\n.dp-userinfo2 .info .nickname .user-level{color:rgba(255,255,255,0.6);margin-top:2px;width:auto;height:36rpx;border-radius:18rpx;padding:0 20rpx;display:flex;align-items:center}\r\n.dp-userinfo2 .info .nickname .user-level .level-img{width:32rpx;height:32rpx;margin-right:3px;margin-left:-14rpx;border-radius:50%;}\r\n.dp-userinfo2 .info .nickname .user-level .level-name{font-size:24rpx;}\r\n.dp-userinfo2 .info .nickname .usermid{color:rgba(255,255,255,0.8);font-size:24rpx;}\r\n\r\n.dp-userinfo2 .custom_field{display:flex;width:100%;align-items:center;padding:16rpx 8rpx;margin-top:20rpx;overflow-x: scroll;}\r\n.dp-userinfo2 .custom_field .item-style2{display:flex;flex-direction:column;justify-content:center;align-items:center;flex:1;margin-right: 10rpx;}\r\n.dp-userinfo2 .custom_field .item-style2 .t1{color:rgba(255,255,255,0.6);font-size:24rpx;margin-top:10rpx;min-width: 140rpx;text-align: center;}\r\n.dp-userinfo2 .custom_field .item-style2 .t2{color:#FFFFFF;font-weight:bold;font-size:32rpx;}\r\n/* .dp-userinfo2 .custom_field .item{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;min-width: 140rpx;} */\r\n/* .dp-userinfo2 .custom_field .item .t1{color:rgba(255,255,255,0.6);font-size:24rpx;margin-top:10rpx} */\r\n/* .dp-userinfo2 .custom_field .item .t2{color:#FFFFFF;font-weight:bold;font-size:32rpx;} */\r\n\r\n.dp-userinfo2 .usercard{width:154rpx;height:54rpx;background:#fff;border-radius: 27rpx 0 0 27rpx;display:flex;align-items:center;padding-left:20rpx;position:absolute;top:140rpx;right:0}\r\n.dp-userinfo2 .usercard .img{width:32rpx;height:32rpx;margin-right:6rpx}\r\n.dp-userinfo2 .usercard .txt{color:#F4504C;font-size:24rpx;font-weight:bold}\r\n.dp-userinfo2 .userset{width:54rpx;height:54rpx;padding:10rpx;position:absolute;top:40rpx;right:30rpx}\r\n.dp-userinfo2 .userset .img{width:100%;height:100%}\r\n\r\n.dp-userinfo-order{background:#fff;padding:0 20rpx;border-radius:16rpx;position: relative;}\r\n.dp-userinfo-order .head{ display:flex;align-items:center;width:100%;padding:16rpx 0;}\r\n.dp-userinfo-order .head .f1{flex:auto;font-size:30rpx;padding-left:16rpx;font-weight:bold;color:#333}\r\n.dp-userinfo-order .head .f2{ display:flex;align-items:center;color:#999;width:auto;padding:10rpx 0;text-align:right;justify-content:flex-end}\r\n.dp-userinfo-order .head .f2 .image{ width:30rpx;height:30rpx;}\r\n.dp-userinfo-order .head .t3{ width:40rpx; height:40rpx;}\r\n.dp-userinfo-order .content{ display:flex;width:100%;padding:0 0 10rpx 0;align-items:center;font-size:24rpx}\r\n.dp-userinfo-order .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\r\n.dp-userinfo-order .content .item .image{ width:50rpx;height:50rpx}\r\n.dp-userinfo-order .content .item .iconfont{font-size:60rpx}\r\n.dp-userinfo-order .content .item .t3{ padding-top:3px}\r\n.dp-userinfo-order .content .item .t2{display:flex;align-items:center;justify-content:center;background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:20rpx;width:35rpx;height:35rpx;text-align:center;}\r\n\r\n.dp-userinfo-order .content .item .image-view{width: 60rpx;height: 60rpx;}\r\n.dp-userinfo-order .content .item .image-view image{width: 60rpx;height: 60rpx;}\r\n\r\n.parent {padding:20rpx;border-radius:16rpx;justify-content: center;display:flex;align-items:center;font-size:24rpx; margin-bottom: 10rpx;}\r\n.parent .parentimg{ width: 100rpx; height:100rpx; border-radius: 50%; z-index: 10;}\r\n.parent .parentimg-tag { color: #fff; text-align: center; margin-top: -20rpx; z-index: 11; border-radius: 12rpx; padding: 2rpx 4rpx; position: relative; bottom: 2rpx;}\r\n.parent .copyicon {width: 26rpx; height: 26rpx; margin-left: 8rpx; position: relative; top: 4rpx;}\r\n.parent .f1 { position: relative;}\r\n.parent .f2 { padding: 0 30rpx;}\r\n.parent .handle-img {width: 60rpx; height: 60rpx;}\r\n.parent .btn-box { padding: 20rpx 0;}\r\n.parent button { padding: 0 40rpx; color: #fff; border-radius:20rpx; line-height: 60rpx;}\r\n\r\n.tag-renzheng{color:#eeda65;background:#3a3a3a;border-radius: 8rpx;padding: 4rpx 8rpx;margin: 0 4rpx;font-size: 22rpx;}\r\n.tag-gray{color:#fff;background:#999;border-radius: 8rpx;padding: 4rpx 8rpx;margin: 0 4rpx;font-size: 22rpx;}\r\n\r\n.task_list{ background: #fff;padding:0 20rpx;font-size:30rpx;border-radius:16rpx;position: relative;}\r\n.task_list .item{ height:100rpx;display:flex;align-items:center;border-bottom:1px solid #eee}\r\n.task_list .item:last-child{border-bottom:0;}\r\n.task_list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center}\r\n.task_list .f1 image{ width:44rpx;height:44rpx;}\r\n.task_list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}\r\n.task_list .f2{color:#222}\r\n.task_list .f3{ color: #666;text-align:right;flex:1}\r\n.task_list .f4{ width: 40rpx; height: 40rpx;}\r\n\r\n.task_list .title-text{color: #fff;font-size: 30rpx;font-weight: 500;}\r\n.task_list .item .icon-view{width: 55rpx;height: 55rpx;margin-right: 15rpx;}\r\n.task_list .item .icon-view image{width: 100%;height: 100%;}\r\n.task_list .data-num-view{flex: 1;justify-content: flex-end;text-align: right;}\r\n.task_list .data-num-view .top-num-text{color: #fff;font-size: 30rpx;font-weight: 500;}\r\n.task_list .data-num-view .bot-num-text{color: #ecdd36;font-size: 24rpx;}\r\n.task_list .jiantou-icon{width: 45rpx; height: 45rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-userinfo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-userinfo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839375476\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}