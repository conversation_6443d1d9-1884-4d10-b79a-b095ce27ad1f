{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/waterfall-article/waterfall-article.vue?73fa", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/waterfall-article/waterfall-article.vue?2a06", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/waterfall-article/waterfall-article.vue?7af4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/waterfall-article/waterfall-article.vue?423b", "uni-app:///components/waterfall-article/waterfall-article.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/waterfall-article/waterfall-article.vue?6462", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/waterfall-article/waterfall-article.vue?eb72"], "names": ["props", "list", "type", "required", "offset", "default", "id<PERSON><PERSON>", "imageSrcKey", "cols", "validator", "imageStyle", "showtime", "showreadcount", "data", "topArr", "allPositionArr", "allHeightArr", "height", "oldNum", "num", "created", "methods", "imageLoadHandle", "query", "size", "shorterIndex", "shorterValue", "longerIndex", "longerValue", "top", "left", "exec", "refresh", "arr"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACqC;;;AAGrG;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+0B,CAAgB,+yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuDn2B;EACAA;IACAC;MAAAC;MAAAC;IAAA;IACA;IACAC;MAAAF;MAAAG;IAAA;IACA;IACAC;MAAAJ;MAAAG;IAAA;IACA;IACAE;MAAAL;MAAAG;IAAA;IACA;IACAG;MAAAN;MAAAG;MAAAI;QAAA;MAAA;IAAA;IACAC;MAAAR;IAAA;IACAS;MAAAT;MAAAG;IAAA;IACAO;MAAAV;MAAAG;IAAA;EACA;EACAQ;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;MACAA;QAAAC;MAAA;QACA;QACA;QACA;UACA;YACA;cACA;gBAAA;cAAA;cACA;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;YACA;cAAAH;cAAAC;YACA;cACAG;cACAC;YACA;YACA;YACA,6BACAJ;YACA;UACA;UACA;UACA;QACA;MACA,GACAK;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAotC,CAAgB,ooCAAG,EAAC,C;;;;;;;;;;;ACAxuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/waterfall-article/waterfall-article.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./waterfall-article.vue?vue&type=template&id=7a9d9e08&scoped=true&\"\nvar renderjs\nimport script from \"./waterfall-article.vue?vue&type=script&lang=js&\"\nexport * from \"./waterfall-article.vue?vue&type=script&lang=js&\"\nimport style0 from \"./waterfall-article.vue?vue&type=style&index=0&id=7a9d9e08&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a9d9e08\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/waterfall-article/waterfall-article.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./waterfall-article.vue?vue&type=template&id=7a9d9e08&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./waterfall-article.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./waterfall-article.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"waterfalls-box\" :style=\"{ height: height + 'px' }\">\n    <view\n      v-for=\"(item, index) of list\"\n      class=\"waterfalls-list\"\n      :key=\"item[idKey]\"\n      :id=\"'waterfalls-list-id-' + item[idKey]\"\n      :ref=\"'waterfalls-list-id-' + item[idKey]\"\n      :style=\"{\n        '--offset': offset + 'px',\n        '--cols': cols,\n        top: allPositionArr[index] ? allPositionArr[index].top : 0,\n        left: allPositionArr[index] ? allPositionArr[index].left : 0,\n      }\"\n      @click=\"goto\" :data-url=\"'/pagesExt/article/detail?id='+item[idKey]\"\n    >\n      <image\n        class=\"waterfalls-list-image\"\n        mode=\"widthFix\"\n        :style=\"imageStyle\"\n        :src=\"item[imageSrcKey] || ' '\"\n        @load=\"imageLoadHandle(index)\"\n        @error=\"imageLoadHandle(index)\"\n      />\n      <view class=\"article-waterfall-info\">\n\t\t\t\t<view class=\"p1\">{{item.name}}</view>\r\n                <block v-if=\"item.po_status && item.po_status==1\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                        {{item.po_name}} {{item.po_content}}\r\n                    </view>\r\n                </block>\r\n                <block v-if=\"item.pt_status && item.pt_status==1\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                        {{item.pt_name}} {{item.pt_content}}\r\n                    </view>\r\n                </block>\r\n                <block v-if=\"item.pth_status && item.pth_status==1\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                        {{item.pth_name}} {{item.pth_content}}\r\n                    </view>\r\n                </block>\r\n                <block v-if=\"item.pf_status && item.pf_status==1\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                        {{item.pf_name}} {{item.pf_content}}\r\n                    </view>\r\n                </block>\n\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\" v-if=\"showtime=='1'\">{{item.createtime}}</text>\n\t\t\t\t\t<text style=\"overflow:hidden\" v-if=\"showreadcount=='1'\">阅读 {{item.readcount}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  props: {\n    list: { type: Array, required: true },\n    // offset 间距，单位为 px\n    offset: { type: Number, default: 8 },\n    // 列表渲染的 key 的键名，值必须唯一，默认为 id\n    idKey: { type: String, default: \"id\" },\n    // 图片 src 的键名\n    imageSrcKey: { type: String, default: \"pic\" },\n    // 列数\n    cols: { type: Number, default: 2, validator: (num) => num >= 2 },\n    imageStyle: { type: Object },\n\t\tshowtime: {type: String, default: \"1\" },\n\t\tshowreadcount: {type: String, default: \"1\" }\n  },\n  data() {\n    return {\n      topArr: [], // left, right 多个时依次表示第几列的数据\n      allPositionArr: [], // 保存所有的位置信息\n      allHeightArr: [], // 保存所有的 height 信息\n      height: 0, // 外层包裹高度\n      oldNum: 0,\n      num: 0,\n    };\n  },\n  created() {\n    this.refresh();\n  },\n  methods: {\n    imageLoadHandle(index) {\n      const id = \"waterfalls-list-id-\" + this.list[index][this.idKey],\n        query = uni.createSelectorQuery().in(this);\n\t\tquery.select(\"#\" + id).fields({ size: true }, (data) => {\n          this.num++;\n          this.$set(this.allHeightArr, index, data.height);\n          if (this.num === this.list.length) {\n            for (let i = this.oldNum; i < this.num; i++) {\n              const getTopArrMsg = () => {\n                let arrtmp = [...this.topArr].sort((a, b) => a - b);\n                return {\n                  shorterIndex: this.topArr.indexOf(arrtmp[0]),\n                  shorterValue: arrtmp[0],\n                  longerIndex: this.topArr.indexOf(arrtmp[this.cols - 1]),\n                  longerValue: arrtmp[this.cols - 1],\n                };\n              };\n              const { shorterIndex, shorterValue } = getTopArrMsg();\n              const position = {\n                top: shorterValue + \"px\",\n                left: (data.width + this.offset) * shorterIndex + \"px\",\n              };\n              this.$set(this.allPositionArr, i, position);\n              this.topArr[shorterIndex] =\n                shorterValue + this.allHeightArr[i] + this.offset;\n              this.height = getTopArrMsg().longerValue - this.offset;\n            }\n            this.oldNum = this.num;\n            this.$emit(\"image-load\");\n          }\n        })\n        .exec();\n    },\n    refresh() {\n      let arr = [];\n      for (let i = 0; i < this.cols; i++) {\n        arr.push(0);\n      }\n      this.topArr = arr;\n      this.num = 0;\n      this.oldNum = 0;\n      this.height = 0;\n    },\n  },\n};\n</script>\n<style scoped>\n.waterfalls-box {position: relative;width: 100%;overflow: hidden;}\n.waterfalls-box .waterfalls-list {width: calc((100% - var(--offset) * (var(--cols) - 1)) / var(--cols));position: absolute;background-color: #fff;border-radius: 8rpx;left: calc(-50% - var(--offset));}\n.waterfalls-box .waterfalls-list .waterfalls-list-image {width: 100%;will-change: transform;border-radius:8rpx 8rpx 0 0;display: block;}\n\n.article-waterfall-info{padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\n.article-waterfall-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.article-waterfall-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\r\n.p3{color:#8c8c8c;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./waterfall-article.vue?vue&type=style&index=0&id=7a9d9e08&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./waterfall-article.vue?vue&type=style&index=0&id=7a9d9e08&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839377437\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}