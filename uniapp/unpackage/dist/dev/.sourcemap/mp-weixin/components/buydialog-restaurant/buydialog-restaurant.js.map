{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-restaurant/buydialog-restaurant.vue?1a54", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-restaurant/buydialog-restaurant.vue?06c8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-restaurant/buydialog-restaurant.vue?6053", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-restaurant/buydialog-restaurant.vue?84e2", "uni-app:///components/buydialog-restaurant/buydialog-restaurant.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-restaurant/buydialog-restaurant.vue?e8ae", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-restaurant/buydialog-restaurant.vue?fb67"], "names": ["data", "ks", "product", "gui<PERSON>", "gui<PERSON><PERSON>", "ggselected", "nowguige", "<PERSON><PERSON><PERSON><PERSON>", "jlprice", "jltitle", "gwcnum", "isload", "loading", "canaddcart", "shopset", "totalprice", "jlselected", "jlselectdata", "jlselectindex", "not_selected", "pre_url", "props", "btntype", "default", "menuindex", "controller", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proid", "mounted", "that", "methods", "getdata", "app", "id", "title", "limit_num", "num", "price", "buydialogChange", "showLinkChange", "ggchange", "jlchange", "jlselect", "jladdnum", "total_num", "selectnum", "selectdata", "jlsub<PERSON>", "computejlprice", "totaljlprice", "tobuy", "addcart", "ggid", "j<PERSON><PERSON>", "gwcplus", "gwcminus", "gwcinput"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AACwE;AACL;AACa;;;AAGhF;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,kzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuFt2B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACAC;MACA;MAEAF;MACAG;QAAAC;MAAA;QACAJ;QACA;UACAG;UACA;QACA;QACAH;QACAA;QACA;UACAA;QACA;QAEAA;QACAA;QACAA;QACA;QACA;QACA;QACA;UACA;YAAAI;YAAAC;YAAAC;YAAAC;YAAAC;UAAA;UACApB;QACA;QACAY;QACA;;QAEA;QACA;QACA;UACAxB;QACA;QACAwB;QACAA;QACAA;QACA,mCACAA,6CAEAA;QACAA;QACA;UAAA;UACAA;QACA;QACA;UAEAA;UACAA;QACA;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACAnC;MACA;MACA;QACA2B;QACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MAAA;MAEA;IACA;IACAS;MAEA;MACA;MACA;MACA;MACA;QACA;UACAjC;UACA0B;UACAQ;QACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;MACA;QACAZ;QACA;MACA;MACA;QACAa;QACAb;QACA;MACA;MAEAc;MACAjB;MACAA;IACA;IACAkB;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACAD;MACAjB;MACAA;IACA;IACAmB;MACA;MACA;MACA;MACA;QACA;QACAC;MACA;MACApB;IACA;IACAqB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAlB;QACA;MACA;MACA;MACA;MACA;MACA;QACAA;MACA;QACAA;MACA;IACA;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAP;QACA;MACA;MACA;MAGA;MACA;QACAZ;QACA;MACA;MACA;QAEAA;UAAAL;UAAAyB;UAAAhB;UAAA5B;UACAC;UAAA4C;QAAA;UACA;YACArB;UACA;YACAA;UACA;QACA;MACA;MAEA;QAAAL;QAAAyB;QAAAhB;QAAA5B;QAAAC;QAAA4C;MAAA;MACA;MACAxB;IACA;IACA;IACAyB;MACA;MACA;MACA;QACAtB;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;IACA;IACA;IACAuB;MACA;MACA;MACA;QACA;UACA;YACAvB;UACA;UACA;QACA;MACA;QACA;UACA;YACAA;UACA;UACA;QACA;MACA;MAEA;IACA;IACA;IACAwB;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;UACA;YACAxB;UACA;UACAtB;QACA;MACA;QACA;UACA;YACAsB;UACA;UACAtB;QACA;MAEA;MACA;QACAsB;QACAtB;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClZA;AAAA;AAAA;AAAA;AAA+rC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACAntC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/buydialog-restaurant/buydialog-restaurant.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./buydialog-restaurant.vue?vue&type=template&id=0569c3f8&\"\nvar renderjs\nimport script from \"./buydialog-restaurant.vue?vue&type=script&lang=js&\"\nexport * from \"./buydialog-restaurant.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buydialog-restaurant.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/buydialog-restaurant/buydialog-restaurant.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-restaurant.vue?vue&type=template&id=0569c3f8&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload\n    ? _vm.jlselectdata.length > 0 &&\n      (_vm.controller == \"ApiRestaurantShop\" ||\n        _vm.controller == \"ApiRestaurantTakeaway\")\n    : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.jlselectdata, function (jitem, jindex) {\n          var $orig = _vm.__get_orig(jitem)\n          var m0 = jitem.num > 0 ? _vm.t(\"color1\") : null\n          var m1 = jitem.num > 0 ? _vm.t(\"color1\") : null\n          var m2 = _vm.t(\"color1\")\n          var m3 = _vm.t(\"color1\")\n          var m4 = _vm.t(\"color1\")\n          var m5 = jitem.num > 0 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.guigedata, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var l1 = _vm.__map(item.items, function (item2, index2) {\n          var $orig = _vm.__get_orig(item2)\n          var m6 = _vm.ggselected[item.k] == item2.k ? _vm.t(\"color1\") : null\n          var m7 = _vm.ggselected[item.k] == item2.k ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n          }\n        })\n        return {\n          $orig: $orig,\n          l1: l1,\n        }\n      })\n    : null\n  var g1 = _vm.isload\n    ? _vm.jialiaodata.length > 0 &&\n      (_vm.controller == \"ApiRestaurantShop\" ||\n        _vm.controller == \"ApiRestaurantTakeaway\")\n    : null\n  var l3 =\n    _vm.isload && g1\n      ? _vm.__map(_vm.jialiaodata, function (jlitem, jlindex) {\n          var $orig = _vm.__get_orig(jlitem)\n          var m8 = jlitem.active ? _vm.t(\"color1\") : null\n          var m9 = jlitem.active ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m8: m8,\n            m9: m9,\n          }\n        })\n      : null\n  var m10 = _vm.isload ? _vm.t(\"color1\") : null\n  var m11 =\n    _vm.isload &&\n    _vm.shopset &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.nowguige.commission > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m12 =\n    _vm.isload &&\n    _vm.shopset &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.nowguige.commission > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m13 =\n    _vm.isload &&\n    !(_vm.nowguige.stock <= 0 || _vm.nowguige.stock_daily <= 0) &&\n    _vm.btntype == 0 &&\n    _vm.canaddcart\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    !(_vm.nowguige.stock <= 0 || _vm.nowguige.stock_daily <= 0) &&\n    _vm.btntype == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m15 =\n    _vm.isload &&\n    !(_vm.nowguige.stock <= 0 || _vm.nowguige.stock_daily <= 0) &&\n    _vm.btntype == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m16 =\n    _vm.isload &&\n    !(_vm.nowguige.stock <= 0 || _vm.nowguige.stock_daily <= 0) &&\n    _vm.btntype == 2\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l2: l2,\n        g1: g1,\n        l3: l3,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-restaurant.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-restaurant.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<view v-if=\"isload\">\r\n\t\t<view class=\"buydialog-mask flex-xy-center\">\r\n\t\t\t<view class=\"buydialog-back\" @tap=\"buydialogChange\"></view>\r\n\t\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot2'\" @touchmove.stop.prevent=\"\">\r\n\t\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"image\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view scroll-y=\"true\">\r\n\t\t\t\t\t<scroll-view scroll-y=\"true\" class=\"page-scroll\">\r\n\t\t\t\t\t\t<view class=\"box\">\r\n\t\t\t\t\t\t\t<image :src=\"nowguige.pic || product.pic\" class=\"banner\" mode=\"aspectFit\" @tap=\"previewImage\" :data-url=\"nowguige.pic || product.pic\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!--产品描述-->\r\n\t\t\t\t\t\t<view style=\"max-height:50vh;overflow:scroll\" v-if=\"product.sellpoint\">\r\n\t\t\t\t\t\t\t<view class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">产品描述</view>\r\n\t\t\t\t\t\t\t\t<view  class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"description\">{{product.sellpoint}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!--jialiao-->\r\n\t\t\t\t\t\t<view style=\"height:auto;\" v-if=\"jlselectdata.length > 0 && (controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway')\">\r\n\t\t\t\t\t\t\t<view   class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">加料</view>\r\n\t\t\t\t\t\t\t\t<view  class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(jitem, jindex) in jlselectdata\" :key=\"jindex\"  class=\"jlitem\" :style=\"jitem.num >0? 'color:' + t('color1') + ';border-color:' + t('color1'):''\"  >\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"addsub\" :style=\"'color:'+t('color1')+';border:4rpx solid '+ t('color1')\" @click=\"jlsubnum(jindex)\">－</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text\">{{jitem.title}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"addsub\" :style=\"'color:#fff;background-color:'+ t('color1')\" @click=\"jladdnum(jindex)\">＋</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"jlnum\" :style=\"'background-color:'+t('color1')\" v-if=\"jitem.num >0\">{{jitem.num}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"height:auto;\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" class=\"item2\" :style=\"ggselected[item.k]==item2.k? 'color:' + t('color1') + ';' + 'border-color:' + t('color1'):''\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!--加料-->\r\n\t\t\t\t\t\t<view style=\"height:auto;\" v-if=\"jialiaodata.length > 0 && (controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway')\">\r\n\t\t\t\t\t\t\t<view   class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">加料</view>\r\n\t\t\t\t\t\t\t\t<view  class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(jlitem, jlindex) in jialiaodata\" :key=\"jlindex\"  class=\"item2\" :style=\"jlitem.active? 'color:' + t('color1') + ';' + 'border-color:' + t('color1'):''\" @click=\"jlchange(jlindex)\">{{jlitem.jltitle}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t<view class=\"page_price\" :style=\"{color:t('color1')}\">￥{{totalprice}}</view>\r\n\t\t\t\t\t<view class=\"page_content\">已选规格: {{nowguige.name}}{{jltitle}}</view>\r\n\t\t\t\t\t<view class=\"buynum flex flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"flex1\">购买数量：</view>\r\n\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t<view class=\"minus\" @click.stop=\"gwcminus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" /></view>\r\n\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t\t\t<view class=\"plus\" @click.stop=\"gwcplus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips-text\" :style=\"{color:t('color1')}\" v-if=\"shopset && shopset.showcommission==1 && nowguige.commission > 0\">分享好友购买预计可得{{t('佣金')}}：<text style=\"font-weight:bold;padding:0 2px\">{{nowguige.commission}}</text>{{nowguige.commission_desc}}</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<block v-if=\"nowguige.stock <= 0 || nowguige.stock_daily <=0\">\r\n\t\t\t\t\t\t\t<button class=\"nostock\">库存不足</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color1')}\" @tap=\"addcart\" v-if=\"btntype==0 && canaddcart\">加入购物车</button>\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==0\">立即购买</button>\r\n\t\t\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color1')}\" @tap=\"addcart\" v-if=\"btntype==1\">确 定</button>\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==2\">确 定</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tks:'',\r\n\t\t\t\tproduct:{},\r\n\t\t\t\tguigelist:{},\r\n\t\t\t\tguigedata:{},\r\n\t\t\t\tggselected:{},\r\n\t\t\t\tnowguige:{},\r\n\t\t\t\tjialiaodata:[],\r\n\t\t\t\tjlprice:0,\r\n\t\t\t\tjltitle:'',\r\n\t\t\t\tgwcnum:1,\r\n\t\t\t\tisload:false,\r\n\t\t\t\tloading:false,\r\n\t\t\t\tcanaddcart:true,\r\n\t\t\t\tshopset:{},\r\n\t\t\t\ttotalprice:0,\r\n\t\t\t\tjlselected:[],\r\n\t\t\t\tjlselectdata:[],//新加料\r\n\t\t\t\tjlselectindex:[],\r\n\t\t\t\tnot_selected:[],\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tbtntype:{default:0},\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tcontroller:{default:'ApiShop'},\r\n\t\t\tneedaddcart:{default:true},\r\n\t\t\tproid:{}\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.getdata();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(this.controller+'/getproductdetail',{id:that.proid},function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status == 0){\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\tif(!that.product.limit_start){\r\n\t\t\t\t\t\tthat.product.limit_start = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\t\tthat.not_selected =res.not_selected?res.not_selected:[];\r\n\t\t\t\t\t//新加料\r\n\t\t\t\t\tvar jldata = res.jldata;\r\n\t\t\t\t\tvar jlselectdata = [];\r\n\t\t\t\t\tfor(var i =0;i<jldata.length; i++){\r\n\t\t\t\t\t\tvar sdata = {id:jldata[i].id,title:jldata[i].title,limit_num:jldata[i].limit_num,num:0,price:jldata[i].price};\r\n\t\t\t\t\t\tjlselectdata.push(sdata);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.jlselectdata = jlselectdata;\r\n\t\t\t\t\t//新加料结束\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar guigedata = res.guigedata;\r\n\t\t\t\t\tvar ggselected = [];\r\n\t\t\t\t\tfor (var i = 0; i < guigedata.length; i++) {\r\n\t\t\t\t\t\tggselected.push(0);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.ks = ggselected.join(','); \r\n\t\t\t\t\tthat.nowguige = that.guigelist[that.ks];\r\n\t\t\t\t\tthat.ggselected = ggselected;\r\n\t\t\t\t\tif(that.nowguige.limit_start > 0)\r\n\t\t\t\t\t\tthat.gwcnum = that.nowguige.limit_start;\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\tthat.gwcnum = that.product.limit_start;\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\r\n\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.controller =='ApiRestaurantShop' ||that.controller =='ApiRestaurantTakeaway'){\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.jialiaodata = res.jialiaodata;\r\n\t\t\t\t\t\tthat.totalprice = that.nowguige.sell_price;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tbuydialogChange:function(){\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t},\r\n\t\t\tshowLinkChange:function () {\r\n\t\t\t\tthis.$emit('showLinkChange');\r\n\t\t\t},\r\n\t\t\t//选择规格\r\n\t\t\tggchange: function (e){\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar itemk = e.currentTarget.dataset.itemk;\r\n\t\t\t\tvar ggselected = JSON.parse(JSON.stringify(this.ggselected));\r\n\t\t\t\tggselected[itemk] = idx;\r\n\t\t\t\tvar ks = ggselected.join(',');\r\n\t\t\t\tif(this.not_selected.includes(ks)){\r\n\t\t\t\t\tapp.error('暂不支持该规格组合，可变更其他规格下单');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.ggselected = ggselected;\r\n\t\t\t\tthis.ks = ks;\r\n\t\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (this.gwcnum < this.nowguige.limit_start) {\r\n\t\t\t\t\t\tthis.gwcnum = this.nowguige.limit_start;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.totalprice = parseFloat( parseFloat(this.nowguige.sell_price) +this.jlprice).toFixed(2); ;\r\n\t\t\t\r\n\t\t\t\tthis.computejlprice();\r\n\t\t\t},\r\n\t\t\tjlchange:function(index){\r\n\t\t\t\t\r\n\t\t\t\tthis.jialiaodata[index].active =this.jialiaodata[index].active==true?false: true;\r\n\t\t\t\tvar jlprice = 0;\r\n\t\t\t\tvar title = '';\r\n\t\t\t\tlet jlselect = [];\r\n\t\t\t\tfor(let i=0;i<this.jialiaodata.length;i++){\r\n\t\t\t\t\tif(this.jialiaodata[i].active){\r\n\t\t\t\t\t\tjlprice = jlprice+parseFloat(this.jialiaodata[i].price);\t\r\n\t\t\t\t\t\ttitle +=','+this.jialiaodata[i].jltitle;\r\n\t\t\t\t\t\tjlselect.push(this.jialiaodata[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.jltitle =title;\r\n\t\t\t\tthis.jlprice = jlprice;\r\n\t\t\t \tthis.totalprice =parseFloat( parseFloat(this.nowguige.sell_price) +jlprice).toFixed(2);\r\n\t\t\t\tthis.jlselected = jlselect;\r\n\t\t\t\t\r\n\t\t\t\tthis.jialiaodata = this.jialiaodata;\r\n\t\t\t},\r\n\t\t\tjladdnum:function(index){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar jlselectdata = that.jlselectdata;\r\n\t\t\t\tvar selectdata = jlselectdata[index];\r\n\t\t\t\tvar selectnum = selectdata.num + 1;\r\n\t\t\t\t\r\n\t\t\t\t//判断总份数\r\n\t\t\t\tvar total_num = 1;\r\n\t\t\t\tfor(var i=0; i<jlselectdata.length;i++ ){\r\n\t\t\t\t\ttotal_num = total_num + jlselectdata[i].num;\r\n\t\t\t\t}\r\n\t\t\t\tif(total_num > that.product.jl_total_limit && that.product.jl_total_limit >0){\r\n\t\t\t\t\tapp.error('最多加'+that.product.jl_total_limit+'份');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(selectnum > selectdata.limit_num){\r\n\t\t\t\t\tselectnum = selectdata.num - 1;\r\n\t\t\t\t\tapp.error('限购'+selectdata.limit_num+'份');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tselectdata.num = selectnum;\r\n\t\t\t\tthat.jlselectdata[index] = selectdata;\r\n\t\t\t\tthat.computejlprice();\r\n\t\t\t},\r\n\t\t\tjlsubnum:function(index){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar jlselectdata = that.jlselectdata;\r\n\t\t\t\tvar selectdata = jlselectdata[index];\r\n\t\t\t\tvar selectnum = selectdata.num - 1;\r\n\t\t\t\tif(selectnum < 0){\r\n\t\t\t\t\tvar selectnum = 0;\r\n\t\t\t\t}\r\n\t\t\t\tselectdata.num = selectnum;\r\n\t\t\t\tthat.jlselectdata[index] = selectdata;\r\n\t\t\t\tthat.computejlprice();\r\n\t\t\t},\r\n\t\t\tcomputejlprice(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar jlselectdata = that.jlselectdata;\r\n\t\t\t\tvar totaljlprice = 0;\r\n\t\t\t\tfor(var i=0; i<jlselectdata.length;i++ ){\r\n\t\t\t\t\tvar price = parseFloat(jlselectdata[i].num * jlselectdata[i].price).toFixed(2);\r\n\t\t\t\t\ttotaljlprice =parseFloat(parseFloat(totaljlprice) + parseFloat(price)).toFixed(2); \r\n\t\t\t\t}\r\n\t\t\t\tthat.totalprice =parseFloat( parseFloat(that.nowguige.sell_price) +parseFloat(totaljlprice)).toFixed(2);\t\r\n\t\t\t},\r\n\t\t\ttobuy: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\tif (stock < num) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\r\n\t\t\t\tvar jldata=that.jlprice+'-'+that.jltitle;\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\tif(this.controller == 'ApiRestaurantTakeaway'){\r\n\t\t\t\t\tapp.goto('/restaurant/takeaway/buy?prodata=' + prodata+'&jldata='+jldata+'&btype=1');\r\n\t\t\t\t}else if(this.controller == 'ApiRestaurantShop'){\r\n\t\t\t\t\tapp.goto('/restaurant/shop/buy?prodata=' + prodata+'&jldata='+jldata+'&btype=1');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//加入购物车操作\r\n\t\t\taddcart: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\t//新加料 如果设置了必选\r\n\t\t\t\tvar total_num = 0;\r\n\t\t\t\tif(that.product.jl_is_selected ==1){\r\n\t\t\t\t\tvar jlselectdata = that.jlselectdata;\r\n\t\t\t\t\tfor(var i=0; i<jlselectdata.length;i++ ){\r\n\t\t\t\t\t\ttotal_num = total_num + jlselectdata[i].num;\r\n\t\t\t\t\t}\t\r\n\t\t\t\t}\r\n\t\t\t\tif(that.product.jl_is_selected ==1 && total_num ==0) return app.error('请选择加料');\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\tif (stock < num) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\t\r\n\t\t\t\tif(this.needaddcart){\r\n\t\t\t\t\t\r\n\t\t\t\t\tapp.post(this.controller+'/addcart', {proid: proid,ggid: ggid,num: num,jlprice: that.jlprice,\r\n\t\t\t\t\tjltitle: that.jltitle,jldata:that.jlselectdata}, function (res) {\r\n\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\tapp.success('添加成功');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.$emit('addcart',{proid: proid,ggid: ggid,num: num,jlprice:this.jlprice,jltitle:this.jltitle,jldata:JSON.stringify(that.jlselectdata)});\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\tthat.jlselectdata = [];\r\n\t\t\t},\r\n\t\t\t//加\r\n\t\t\tgwcplus: function (e) {\r\n\t\t\t\tvar gwcnum = this.gwcnum + 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = this.gwcnum + 1;\r\n\t\t\t},\r\n\t\t\t//减\r\n\t\t\tgwcminus: function (e) {\r\n\t\t\t\tvar gwcnum = this.gwcnum - 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.gwcnum = this.gwcnum - 1;\r\n\t\t\t},\r\n\t\t\t//输入\r\n\t\t\tgwcinput: function (e) {\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\t\tif (gwcnum < 1) return 1;\r\n\t\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\t\treturn this.guigelist[ks].stock > 0 ? this.guigelist[ks].stock : 1;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tgwcnum = this.nowguige.limit_start;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tgwcnum = this.product.limit_start;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\t\tgwcnum = this.product.perlimitdan;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.gwcnum = gwcnum;\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.buydialog-mask{height:100%;}\r\n.buydialog-back{position: absolute;height: 100%;width: 100%;top: 0;left: 0;}\r\n.buydialog{ position: relative; width: 690rpx;background: #fff;border-radius:20rpx;overflow: hidden;}\r\n.buydialog .box{ padding: 20rpx 20rpx 0 20rpx; }\r\n.buydialog .banner{ height:300rpx;display: block;margin: 0 auto;}\r\n.buydialog .close{z-index: 5;}\r\n\r\n.buydialog .title{ width: 92%;position: relative; margin: 0 4%; padding:30rpx 0px 20rpx 0; border-bottom:0; height: 190rpx;}\r\n.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 30rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\r\n.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\r\n\r\n.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.page-scroll{height: 55vh;}\r\n.page_price{\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #FC4343;\r\n\tpadding: 30rpx 30rpx 0 30rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n}\r\n.page_text{\r\n\tfont-size: 24rpx;\r\n\tcolor: #888888;\r\n\tfont-weight: normal;\r\n\tmargin-left: 10rpx;\r\n}\r\n.page_content{\r\n\tfont-size: 24rpx;\r\n\tcolor: #888888;\r\n\tfont-weight: normal;\r\n\tmargin-top: 30rpx;\r\n\tpadding: 0 30rpx;\r\n}\r\n.buydialog .guigelist{ width: 92%; position: relative; margin: 0 4%; padding:0px 0px 40rpx 0px; border-bottom: 0; }\r\n.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:100rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;border: 1px solid #eee;}\r\n\r\n.buydialog .guigelist .jlitem{width: 48%;line-height:60rpx;margin-bottom:20rpx; border-radius:20rpx; color:#666666;  font-size:26rpx;border: 1px solid #eee;display: flex;padding: 10rpx 10rpx;align-items: center;position: relative;}\r\n.buydialog .guigelist .jlitem:nth-child(2n){margin-left: 3%;}\r\n.buydialog .guigelist .jlitem .addsub{width: 45rpx;height: 45rpx;border-radius: 50%;display:flex;align-items:center;justify-content:center;line-height: 45rpx;}\r\n.buydialog .guigelist .jlitem .img{width:24rpx;height:24rpx}\r\n.buydialog .guigelist .jlitem  .text{width: 65%;text-align: center;word-break: keep-all}\r\n.buydialog .guigelist .jlitem .jlnum{width: 35rpx;height: 35rpx;position: absolute;top: -12rpx;right: -10rpx;border-radius: 10rpx 10rpx 10rpx 0;color: #fff;text-align: center;line-height: 35rpx;}\r\n\r\n.buydialog .guigelist .on{color:#FC4343;border: 1rpx solid #FC4343;}\r\n.buydialog .buynum{ width: 92%; position: relative; margin: 0 4%; padding:10px 0px 0px 0px; }\r\n.buydialog .op{;margin:30rpx 5%;}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-restaurant.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-restaurant.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839431314\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}