{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/wxxieyi/wxxieyi.vue?b4ce", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/wxxieyi/wxxieyi.vue?d627", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/wxxieyi/wxxieyi.vue?04c5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/wxxieyi/wxxieyi.vue?f661", "uni-app:///components/wxxieyi/wxxieyi.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/wxxieyi/wxxieyi.vue?f73d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/wxxieyi/wxxieyi.vue?2cbd"], "names": ["data", "needAuthorization", "privacyContractName", "resolvePrivacyAuthorization", "mounted", "wx", "methods", "getPrivacySetting", "success", "uni", "fail", "app", "handleAgreePrivacyAuthorization", "buttonId", "event", "handleOpenPrivacyContract"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACcz1B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAEAC;MACA;MACA;MACA;IACA;EAEA;EACAC;IACAC;MAAA;MACAF;QACAG;UACA;YACAC;YACA;YACA;YACA;UACA;QACA;;QACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;QAAAC;QAAAC;MAAA;IACA;IACAC;MACAV;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/wxxieyi/wxxieyi.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wxxieyi.vue?vue&type=template&id=98f4ce68&\"\nvar renderjs\nimport script from \"./wxxieyi.vue?vue&type=script&lang=js&\"\nexport * from \"./wxxieyi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wxxieyi.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/wxxieyi/wxxieyi.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxxieyi.vue?vue&type=template&id=98f4ce68&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.needAuthorization ? _vm.t(\"color1\") : null\n  var m1 = _vm.needAuthorization && !(m0 == undefined) ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxxieyi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxxieyi.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"needAuthorization\" class=\"xieyibox\" @touchmove.stop.prevent=''>\r\n\t\t<view class=\"wxxieyibox-content\">\r\n\t\t\t<view class=\"title-view\">隐私政策协议</view>\r\n\t\t\t<view class=\"content-text\">尊敬的用户，我们将按照相关法律法规的要求，尽力保护您的个人信息安全可控。请您点击同意之前，仔细阅读<text class=\"link-text\"  @click=\"handleOpenPrivacyContract\">{{privacyContractName || '《小程序隐私保护协议》'}}</text>并充分理解，请点击“同意”开始使用。</view>\r\n\t\t\t<view class=\"but-view flex-col\">\r\n\t\t\t\t<button class=\"but-class\" :style=\"{background: (t('color1') == undefined ? '#FD4A46':t('color1'))}\" id=\"agree-btn\" open-type=\"agreePrivacyAuthorization\" @agreeprivacyauthorization=\"handleAgreePrivacyAuthorization\">同意</button>\r\n\t\t\t\t<navigator open-type=\"exit\" target=\"miniProgram\" class=\"but-class-no\" hover-class=\"none\">不同意并退出</navigator>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\n</template>\n\n<script>\r\n\tvar app = getApp();\r\n\texport default{\r\n\t\tdata() {\r\n\t\t\treturn{\r\n\t\t\t\tneedAuthorization: false,\r\n\t\t\t\tprivacyContractName:'',\r\n\t\t\t\tresolvePrivacyAuthorization:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\twx.onNeedPrivacyAuthorization((resolve, eventInfo) => {\r\n\t\t\t\t\t// console.log('触发本次事件的接口是：' + eventInfo.referrer)\r\n\t\t\t\t\tthis.getPrivacySetting();\r\n\t\t\t\t\tthis.resolvePrivacyAuthorization = resolve\r\n\t\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetPrivacySetting(){\r\n\t\t\t\twx.getPrivacySetting({\r\n\t\t\t\t\tsuccess:(res) => {\r\n\t\t\t\t\t\tif(res.needAuthorization){\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tthis.needAuthorization = res.needAuthorization;\r\n\t\t\t\t\t\t\tthis.privacyContractName = res.privacyContractName;\r\n\t\t\t\t\t\t\t// console.log(res,'是否需要弹出隐私指引')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail:(err) => {\r\n\t\t\t\t\t\tapp.error(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandleAgreePrivacyAuthorization(){\r\n\t\t\t\tthis.needAuthorization = false;\r\n\t\t\t\tthis.resolvePrivacyAuthorization({ buttonId: 'agree-btn', event: 'agree' })\r\n\t\t\t},\r\n\t\t\thandleOpenPrivacyContract(){\r\n\t\t\t\twx.openPrivacyContract();\r\n\t\t\t},\r\n\t\t}\r\n\t}\n</script>\n\n<style>\r\n\t.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:9999999999999;background:rgba(0,0,0,0.7)}\r\n\t.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px;}\r\n\t.wxxieyibox-content{width:80%;margin:0 auto;margin-top:50%;background:#fff;color:#333;padding:0rpx 15px;border-radius:20rpx;}\r\n\t.wxxieyibox-content .title-view{width: 100%;font-size: 30rpx;text-align: center;color: #333;padding: 35rpx 0rpx 20rpx;font-weight: bold;}\r\n\t.wxxieyibox-content .content-text{font-size: 26rpx;color: #333;line-height: 42rpx;letter-spacing: 4rpx;padding: 0rpx 10rpx;}\r\n\t.wxxieyibox-content .content-text .link-text{color: #51B1F5;font-weight: bold;letter-spacing: 2rpx;}\r\n\t.wxxieyibox-content .but-view{padding: 20rpx 0rpx;margin-top: 20rpx;}\r\n\t.wxxieyibox-content .but-view .but-class{width:100%;font-size: 28rpx;color: #fff;border-radius: 40rpx;margin-bottom: 10rpx;padding: 5rpx;}\r\n\t.wxxieyibox-content .but-view .but-class-no{color: #aaa;width:100%;font-size: 28rpx;text-align: center;padding: 13rpx 0rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxxieyi.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxxieyi.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839368375\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}