{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-notice/dp-notice.vue?bc03", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-notice/dp-notice.vue?3e90", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-notice/dp-notice.vue?b36f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-notice/dp-notice.vue?a1c7", "uni-app:///components/dp-notice/dp-notice.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-notice/dp-notice.vue?c2ea", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-notice/dp-notice.vue?b10d"], "names": ["props", "params", "data", "animationDuration", "animationPlayState", "paddingLift", "textAlign", "mounted", "methods", "init", "that", "boxWidth", "textWidth", "animationAdd", "setTimeout", "clientWidth", "uni", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2B31B;EACAA;IACAC;IACAC;EACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBAAA;gBAAA,OACAD;cAAA;gBAAAC;gBAAA;gBAAA,OACAD;cAAA;gBAAAE;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAF;gBACAA;gBACAA;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;gBACAC;gBAAA;gBAAA,OACAD;cAAA;gBAAAC;gBAAA;gBAAA,OACAD;cAAA;gBAAAE;gBACAF;gBACAA;gBACAI;kBACAJ;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAK;MAAA;MACA;QACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-notice/dp-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-notice.vue?vue&type=template&id=5e7f7910&\"\nvar renderjs\nimport script from \"./dp-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-notice.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-notice/dp-notice.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-notice.vue?vue&type=template&id=5e7f7910&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.data && _vm.data.length == 1\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-notice.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-notice\" :style=\"{\r\n\tcolor:params.color,\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx' + ' 0rpx',\r\n\tpadding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'\r\n}\">\r\n\r\n\t<view class=\"left\" v-if=\"params.showimg==1\"><image class=\"image\" :src=\"params.img\" mode=\"heightFix\"/></view>\r\n\t<view class=\"right\">\r\n\t\t<image v-if=\"params.showicon==1\" class=\"ico\" :src=\"params.icon\"/>\r\n\t\t<block v-if=\"data && data.length == 1\">\r\n\t\t\t<view class=\"dp-notice-content\" :style=\"{textAlign:textAlign}\">\r\n\t\t\t\t<view class=\"dp-content-text\" :style=\"{fontSize:(params.fontsize*2.2)+'rpx',animationDuration:animationDuration,animationPlayState:animationPlayState,paddingLift:paddingLift}\" @click=\"goto\" :data-url=\"data[0].hrefurl\">\r\n\t\t\t\t\t{{data[0].title}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<block v-else>\r\n\t\t\t<swiper style=\"position:relative;height:40rpx;\" autoplay=\"true\" :interval=\"params.scroll*1000\" vertical=\"true\" class=\"itemlist\" circular>\r\n\t\t\t\t<swiper-item class=\"item\" v-for=\"item in data\" :key=\"item.id\" :style=\"{fontSize:(params.fontsize*2.2)+'rpx'}\" @click=\"goto\" :data-url=\"item.hrefurl\">{{item.title}}</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</block>\r\n\t</view>\r\n</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tanimationDuration:'0',\r\n\t\t\t\tanimationPlayState: 'paused',\r\n\t\t\t\tpaddingLift:'0',\r\n\t\t\t\ttextAlign:'left',\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted(){\r\n\t\t\t// 轮播内容为一条\r\n\t\t\tif(this.data && this.data.length == 1){\r\n\t\t\t\tthis.init()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync init(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet boxWidth = 0,textWidth = 0;\r\n\t\t\t\tboxWidth = (await that.clientWidth('.dp-notice-content')).width;\r\n\t\t\t\ttextWidth = (await that.clientWidth('.dp-content-text')).width;\r\n\t\t\t\tif(textWidth <= boxWidth) return;\r\n\t\t\t\tthat.paddingLift = '100%';\r\n\t\t\t\tthat.textAlign = 'right';\r\n\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\tthat.animationAdd();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync animationAdd(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet boxWidth = 0,textWidth = 0;\r\n\t\t\t\tboxWidth = (await that.clientWidth('.dp-notice-content')).width;\r\n\t\t\t\ttextWidth = (await that.clientWidth('.dp-content-text')).width;\r\n\t\t\t\tthat.animationDuration = `${textWidth / 40}s`;\r\n\t\t\t\tthat.animationPlayState = 'paused'\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthat.animationPlayState = 'running'\r\n\t\t\t\t}, 100)\r\n\t\t\t},\r\n\t\t\tclientWidth(select){\r\n\t\t\t\treturn new Promise((resolve) => {\r\n\t\t\t\t\tuni.createSelectorQuery().in(this).select(select).boundingClientRect((rect) => {\r\n\t\t\t\t\t\t resolve(rect)\r\n\t\t\t\t\t}).exec()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n<style>\r\n.dp-notice{height: auto;background: #fff; font-size: 28rpx; color: #666666;overflow: hidden; white-space:nowrap; position: relative;display:flex;align-items:center;padding:2px 4px}\r\n.dp-notice .left{position:relative;padding-right:20rpx;margin-right:20rpx;height:40rpx;display:flex;align-items: center;}\r\n.dp-notice .left:before { content: \" \"; position: absolute; width: 0; top: 2px; right: 0; bottom: 2px; border-right: 1px solid #e2e2e2; }\r\n.dp-notice .image{position:relative;height:36rpx;width:auto}\r\n.dp-notice .right{flex-grow:1;display:flex;align-items: center;overflow:hidden}\r\n.dp-notice .right .ico{width:36rpx;height:36rpx;margin-right:10rpx}\r\n.dp-notice .itemlist{width:100%;height:100%;line-height:40rpx;font-size:28rpx;}\r\n.dp-notice-content{width: 100%;height: 40rpx;line-height: 40rpx;flex: 1;overflow: hidden;}\r\n.dp-notice-content .dp-content-text{white-space: nowrap;height: 100%;display: inline-block;animation: loop-animation 10s linear infinite both;word-break: keep-all;}\r\n@keyframes loop-animation {\n\t0% {\n\t\ttransform: translate3d(0, 0, 0);\n\t}\n\t100% {\n\t\ttransform: translate3d(-100%, 0, 0);\n\t}\n}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-notice.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-notice.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839371267\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}