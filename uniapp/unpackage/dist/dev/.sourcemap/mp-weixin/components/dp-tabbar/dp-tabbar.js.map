{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tabbar/dp-tabbar.vue?d322", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tabbar/dp-tabbar.vue?0b61", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tabbar/dp-tabbar.vue?d8bc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tabbar/dp-tabbar.vue?c4c5", "uni-app:///components/dp-tabbar/dp-tabbar.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tabbar/dp-tabbar.vue?e296", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tabbar/dp-tabbar.vue?8106"], "names": ["data", "menudata", "currentIndex", "i", "opentype", "mounted", "that", "setTimeout", "props", "opt", "methods", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "console", "<PERSON><PERSON><PERSON><PERSON>", "menu2data"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACqC;;;AAG7F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjHA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwE31B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;IACA;MACAC;QACAD;MACA;IACA;EACA;EACAE;IACAC;EACA;EACAC;IACAC;MACA;MACA;QACAJ;UACAD;UACAA;QACA;MACA;QACA;QACA;;QAEA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;QACA;UACAM;QACA;UACAA;QACA;UACAA;QACA;UACAA;QACA;UACAA;QACA;UACA;UACAA;QACA;QACAC;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;QACA;QAEA;QACA;QACA;QACA;UACA;YACAX;YACAY;YACAb;UACA;YACAA;UACA;QACA;QACA;UACA;UACA;YACA;YACA;cACAW;cACAA;YACA;YACA;cACA;gBACAG;cACA;gBACA;gBACAA;cACA;cACA;gBACAA;cACA;cACA;gBACAD;gBACAZ;gBACAD;gBACA;kBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;kBACA;oBACAA;oBACA;sBACAA;oBACA;kBACA;oBACAA;kBACA;gBACA;gBACAG;cACA;YACA;UACA;QACA;QACAE;QACAA;QACAA;QACA;QACAO;QACAP;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3MA;AAAA;AAAA;AAAA;AAA4sC,CAAgB,4nCAAG,EAAC,C;;;;;;;;;;;ACAhuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-tabbar/dp-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-tabbar.vue?vue&type=template&id=bf0a1df8&scoped=true&\"\nvar renderjs\nimport script from \"./dp-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-tabbar.vue?vue&type=style&index=0&id=bf0a1df8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bf0a1df8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-tabbar/dp-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tabbar.vue?vue&type=template&id=bf0a1df8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.currentIndex > -1\n      ? _vm.menudata.menustyle == 1 &&\n        (_vm.menudata.list.length == 3 || _vm.menudata.list.length == 5)\n      : null\n  var g1 = _vm.currentIndex > -1 && g0 ? _vm.menudata.list.length : null\n  var l0 =\n    _vm.currentIndex > -1 && g0 && g1 == 5\n      ? _vm.__map(_vm.menudata.list, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = index < 2 ? index != 0 && _vm.getplatform() == \"baidu\" : null\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    _vm.currentIndex > -1 && g0 && g1 == 5\n      ? _vm.__map(_vm.menudata.list, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 =\n            index == 2 ? index != 0 && _vm.getplatform() == \"baidu\" : null\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var l2 =\n    _vm.currentIndex > -1 && g0 && g1 == 5\n      ? _vm.__map(_vm.menudata.list, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 =\n            index >= 3 ? index != 0 && _vm.getplatform() == \"baidu\" : null\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var g2 = _vm.currentIndex > -1 && g0 ? _vm.menudata.list.length : null\n  var l3 =\n    _vm.currentIndex > -1 && g0 && g2 == 3\n      ? _vm.__map(_vm.menudata.list, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = index < 1 ? index != 0 && _vm.getplatform() == \"baidu\" : null\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var l4 =\n    _vm.currentIndex > -1 && g0 && g2 == 3\n      ? _vm.__map(_vm.menudata.list, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 =\n            index == 1 ? index != 0 && _vm.getplatform() == \"baidu\" : null\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  var l5 =\n    _vm.currentIndex > -1 && g0 && g2 == 3\n      ? _vm.__map(_vm.menudata.list, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m5 =\n            index >= 2 ? index != 0 && _vm.getplatform() == \"baidu\" : null\n          return {\n            $orig: $orig,\n            m5: m5,\n          }\n        })\n      : null\n  var l6 =\n    _vm.currentIndex > -1 && !g0\n      ? _vm.__map(_vm.menudata.list, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = index != 0 && _vm.getplatform() == \"baidu\"\n          return {\n            $orig: $orig,\n            m6: m6,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        g2: g2,\n        l3: l3,\n        l4: l4,\n        l5: l5,\n        l6: l6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tabbar.vue?vue&type=script&lang=js&\"", "<template>\r\n<view v-if=\"currentIndex>-1\">\r\n\t<view class=\"dp-tabbar\" v-if=\"menudata.menustyle==1 && ((menudata.list).length==3 || (menudata.list).length==5)\">\r\n\t\t<view class=\"dp-tabbar-bot\"></view>\r\n\t\t<view v-if=\"(menudata.list).length==5\" class=\"dp-tabbar-module\">\r\n\t\t\t<view class=\"dp-tabbar-cut\" :style=\"{backgroundColor:menudata.backgroundColor}\"></view>\r\n\t\t\t<view class=\"dp-tabbar-sideL dp-tabbar-sideLP\" :style=\"{backgroundColor:menudata.backgroundColor}\">\r\n\t\t\t\t<view @click=\"goto\" :data-url=\"item.pagePath\" :data-index=\"index\" :data-opentype=\"index!=0 && getplatform()=='baidu'?'':opentype\" class=\"dp-tabbar-item\" v-for=\"(item,index) in menudata.list\" :key=\"item.id\" v-if=\"index<2\">\r\n\t\t\t\t\t<view class=\"dp-tabbar-image-box\">\r\n\t\t\t\t\t\t<image v-if=\"currentIndex===index\" class=\"dp-tabbar-icon\" :src=\"item.selectedIconPath\"></image>\r\n\t\t\t\t\t\t<image v-else class=\"dp-tabbar-icon\" :src=\"item.iconPath\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-tabbar-text\" :style=\"{color:item.color}\">{{item.text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view @click=\"goto\" :data-url=\"item.pagePath\" :data-index=\"index\" :data-opentype=\"index!=0 && getplatform()=='baidu'?'':opentype\" v-for=\"(item,index) in menudata.list\" :key=\"item.id\" v-if=\"index==2\" class=\"dp-tabbar-center\" :style=\"'background-image: radial-gradient(circle at top, rgba(0,0,0,0) 55rpx, ' + menudata.backgroundColor +' 55rpx);'\">\r\n\t\t\t\t<image :src=\"item.iconPath\"></image>\r\n\t\t\t\t<view :style=\"{color:item.color}\">{{item.text}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"dp-tabbar-sideR dp-tabbar-sideRP\" :style=\"{backgroundColor:menudata.backgroundColor}\">\r\n\t\t\t\t<view @click=\"goto\" :data-url=\"item.pagePath\" :data-index=\"index\" :data-opentype=\"index!=0 && getplatform()=='baidu'?'':opentype\" class=\"dp-tabbar-item\" v-for=\"(item,index) in menudata.list\" :key=\"item.id\" v-if=\"index>=3\">\r\n\t\t\t\t\t<view class=\"dp-tabbar-image-box\">\r\n\t\t\t\t\t\t<image v-if=\"currentIndex===index\" class=\"dp-tabbar-icon\" :src=\"item.selectedIconPath\"></image>\r\n\t\t\t\t\t\t<image v-else class=\"dp-tabbar-icon\" :src=\"item.iconPath\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-tabbar-text\" :style=\"{color:item.color}\">{{item.text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"(menudata.list).length==3\" class=\"dp-tabbar-module\">\r\n\t\t\t<view class=\"dp-tabbar-cut\" :style=\"{backgroundColor:menudata.backgroundColor}\"></view>\r\n\t\t\t<view class=\"dp-tabbar-sideL\" :style=\"{backgroundColor:menudata.backgroundColor}\">\r\n\t\t\t\t<view @click=\"goto\" :data-url=\"item.pagePath\" :data-index=\"index\" :data-opentype=\"index!=0 && getplatform()=='baidu'?'':opentype\" class=\"dp-tabbar-item\" v-for=\"(item,index) in menudata.list\" :key=\"item.id\" v-if=\"index<1\">\r\n\t\t\t\t\t<view class=\"dp-tabbar-image-box\">\r\n\t\t\t\t\t\t<image v-if=\"currentIndex===index\" class=\"dp-tabbar-icon\" :src=\"item.selectedIconPath\"></image>\r\n\t\t\t\t\t\t<image v-else class=\"dp-tabbar-icon\" :src=\"item.iconPath\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-tabbar-text\" :style=\"{color:item.color}\">{{item.text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view @click=\"goto\" :data-url=\"item.pagePath\" :data-index=\"index\" :data-opentype=\"index!=0 && getplatform()=='baidu'?'':opentype\" v-for=\"(item,index) in menudata.list\" :key=\"item.id\" v-if=\"index==1\" class=\"dp-tabbar-center\" :style=\"'background-image: radial-gradient(circle at top, rgba(0,0,0,0) 55rpx, ' + menudata.backgroundColor +' 55rpx);'\">\r\n\t\t\t\t<image :src=\"item.iconPath\"></image>\r\n\t\t\t\t<view :style=\"{color:item.color}\">{{item.text}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"dp-tabbar-sideR\" :style=\"{backgroundColor:menudata.backgroundColor}\">\r\n\t\t\t\t<view @click=\"goto\" :data-url=\"item.pagePath\" :data-index=\"index\" :data-opentype=\"index!=0 && getplatform()=='baidu'?'':opentype\" class=\"dp-tabbar-item\" v-for=\"(item,index) in menudata.list\" :key=\"item.id\" v-if=\"index>=2\">\r\n\t\t\t\t\t<view class=\"dp-tabbar-image-box\">\r\n\t\t\t\t\t\t<image v-if=\"currentIndex===index\" class=\"dp-tabbar-icon\" :src=\"item.selectedIconPath\"></image>\r\n\t\t\t\t\t\t<image v-else class=\"dp-tabbar-icon\" :src=\"item.iconPath\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-tabbar-text\" :style=\"{color:item.color}\">{{item.text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"dp-tabbar\" v-else>\r\n\t\t<view class=\"dp-tabbar-bot\"></view>\r\n\t\t<view class=\"dp-tabbar-bar\" :style=\"{backgroundColor:menudata.backgroundColor}\">\r\n\t\t\t<view @click=\"goto\" :data-url=\"item.pagePath\" :data-index=\"index\"\r\n\t\t\t\t:data-opentype=\"index!=0 && getplatform()=='baidu'?'':opentype\" class=\"dp-tabbar-item\" \r\n\t\t\t\tv-for=\"(item,index) in menudata.list\" :key=\"item.id\">\r\n\t\t\t\t<view class=\"dp-tabbar-image-box\">\r\n\t\t\t\t\t<image v-if=\"currentIndex===index\" class=\"dp-tabbar-icon\" :src=\"item.selectedIconPath\"></image>\r\n\t\t\t\t\t<image v-else class=\"dp-tabbar-icon\" :src=\"item.iconPath\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"dp-tabbar-text\" :style=\"{color:item.color}\">{{item.text}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenudata: {},\r\n\t\t\t\tcurrentIndex: -1,\r\n\t\t\t\ti: 0,\r\n\t\t\t\topentype: 'reLaunch',\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted: function() {\r\n\t\t\tvar that = this\r\n\t\t\tthat.settabbar();\r\n\t\t\tif (app.globalData.platform == 'toutiao') {\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tthat.settabbar();\r\n\t\t\t\t}, 100)\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\topt: {}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsettabbar: function() {\r\n\t\t\t\tvar that = this\r\n\t\t\t\tif (!app.globalData.isinit && this.i < 100) {\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tthat.i++;\r\n\t\t\t\t\t\tthat.settabbar();\r\n\t\t\t\t\t}, 100)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar opentype = 'reLaunch';\r\n\t\t\t\t\t//var currenturl = app._url();\r\n\r\n\t\t\t\t\tvar pages = getCurrentPages(); //获取加载的页面\r\n\t\t\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n\t\t\t\t\tvar currenturl = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \r\n\t\t\t\t\tif (app.globalData.platform == 'baidu') {\r\n\t\t\t\t\t\tvar opts = currentPage.options;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar opts = currentPage.$vm.opt;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(opts && opts.id && opts.bid && currenturl != '/pagesExt/business/index'){\r\n\t\t\t\t\t\tcurrenturl += '?id=' + opts.id +'&bid=' + opts.bid\r\n\t\t\t\t\t}else if (opts && opts.id) {\r\n\t\t\t\t\t\tcurrenturl += '?id=' + opts.id\r\n\t\t\t\t\t} else if (opts && opts.cid) {\r\n\t\t\t\t\t\tcurrenturl += '?cid=' + opts.cid\r\n\t\t\t\t\t} else if (opts && opts.gid) {\r\n\t\t\t\t\t\tcurrenturl += '?gid=' + opts.gid\r\n\t\t\t\t\t} else if (opts && opts.bid) {\r\n\t\t\t\t\t\tcurrenturl += '?bid=' + opts.bid\r\n\t\t\t\t\t} else if(opts && opts.type && opts.type == 'firstbuy'){\r\n\t\t\t\t\t\t//首消界面 平台导航\r\n\t\t\t\t\t\tcurrenturl += '?type=' + opts.type\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(currenturl)\r\n\t\t\t\t\tif(!that.opt.defaultIndex){\r\n\t\t\t\t\t\tif(that.opt.reloadthispage && that.opt.reloadthispage ==1){\r\n\t\t\t\t\t\t\tvar currentIndex = 0;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tvar currentIndex = -1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tvar currentIndex = that.opt.defaultIndex;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar hastabbar = false;\r\n\t\t\t\t\tvar menudata = JSON.parse(JSON.stringify(app.globalData.initdata.menudata));\r\n\t\t\t\t\tvar tablist = menudata['list'];\r\n\t\t\t\t\tfor (var i = 0; i < tablist.length; i++) {\r\n\t\t\t\t\t\tif (tablist[i]['pagePath'] == currenturl) {\r\n\t\t\t\t\t\t\tcurrentIndex = i;\r\n\t\t\t\t\t\t\thastabbar = true;\r\n\t\t\t\t\t\t\tmenudata['list'][i].color = menudata['selectedColor']\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tmenudata['list'][i].color = menudata['color']\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (hastabbar == false) {\r\n\t\t\t\t\t\tvar menu2data = JSON.parse(JSON.stringify(app.globalData.initdata.menu2data))\r\n\t\t\t\t\t\tif (menu2data.length > 0) {\r\n\t\t\t\t\t\t\t//首消界面 商户导航\r\n\t\t\t\t\t\t\tif(opts && opts.type == 'firstbuy' && opts.typebid){\r\n\t\t\t\t\t\t\t\tcurrenturl = currenturl.split('?')[0];\r\n\t\t\t\t\t\t\t\tcurrenturl += '?id=' + opts.typebid;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tfor (var i in menu2data) {\r\n\t\t\t\t\t\t\t\tif (opts && opts.bid){\r\n\t\t\t\t\t\t\t\t\tmenu2data[i].indexurl = (menu2data[i].indexurl).replace('[bid]', opts.bid);\r\n\t\t\t\t\t\t\t\t}else if(opts && opts.type == 'firstbuy' && opts.typebid){\r\n\t\t\t\t\t\t\t\t\t//首消界面 商户默认导航\r\n\t\t\t\t\t\t\t\t\tmenu2data[i].indexurl = (menu2data[i].indexurl).replace('[bid]', opts.typebid);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif(menu2data[i].indexurl.split('?')[0] == '/pagesA/livepay/mobile_recharge' || menu2data[i].indexurl.split('?')[0] == '/pagesA/livepay/ordinary_recharge' || menu2data[i].indexurl.split('?')[0] == '/pagesA/livepay/record_recharge'){\r\n\t\t\t\t\t\t\t\t\tmenu2data[i].indexurl = menu2data[i].indexurl.split('?')[0];\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (menu2data[i].indexurl == currenturl) {\r\n\t\t\t\t\t\t\t\t\thastabbar = true;\r\n\t\t\t\t\t\t\t\t\tcurrentIndex = 10;\r\n\t\t\t\t\t\t\t\t\tmenudata = menu2data[i]\r\n\t\t\t\t\t\t\t\t\tfor (var j in menudata.list) {\r\n\t\t\t\t\t\t\t\t\t\tif (opts && opts.bid){\r\n\t\t\t\t\t\t\t\t\t\t\tmenudata.list[j].pagePath = (menudata.list[j].pagePath).replace('[bid]',opts.bid);\r\n\t\t\t\t\t\t\t\t\t\t}else if(opts && opts.type == 'firstbuy' && opts.typebid){\r\n\t\t\t\t\t\t\t\t\t\t\t//首消界面 商户默认导航\r\n\t\t\t\t\t\t\t\t\t\t\tmenudata.list[j].pagePath = (menudata.list[j].pagePath).replace('[bid]',opts.typebid);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif (menudata.list[j].pagePath == currenturl && menudata['selectedColor']) {\r\n\t\t\t\t\t\t\t\t\t\t\tmenudata['list'][j].color = menudata['selectedColor'];\r\n\t\t\t\t\t\t\t\t\t\t\tif (menudata['list'][j]['selectedIconPath']) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tmenudata['list'][j].iconPath = menudata['list'][j]['selectedIconPath'];\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t} else if (menudata['color']) {\r\n\t\t\t\t\t\t\t\t\t\t\tmenudata['list'][j].color = menudata['color'];\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\topentype = '';\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.opentype = opentype\r\n\t\t\t\t\tthat.currentIndex = currentIndex\r\n\t\t\t\t\tthat.menudata = menudata\r\n\t\t\t\t\t//app.globalData.currentIndex = currentIndex;\r\n\t\t\t\t\tconsole.log(currentIndex);\r\n\t\t\t\t\tthat.$emit('getmenuindex', currentIndex)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped>\r\n\t.dp-tabbar {\r\n\t\theight: auto;\r\n\t\tposition: relative;\r\n\t}\r\n\t.dp-tabbar-icon {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t}\r\n\t.dp-tabbar-bar {\r\n\t\tposition: fixed;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-direction: row;\r\n\t\twidth: 100%;\r\n\t\theight: 110rpx;\r\n\t\tbottom: 0;\r\n\t\tbackground: #fff;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tborder-top: 1px solid #efefef;\r\n\t\tz-index: 999999;\r\n\t\tbox-sizing: content-box;\r\n\t}\r\n\t.dp-tabbar-item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\toverflow: hidden;\r\n\t\talign-items: center\r\n\t}\r\n\t.dp-tabbar-image-box {\r\n\t\theight: 54rpx;\r\n\t\tmargin-bottom: 4rpx;\r\n\t}\r\n\t.dp-tabbar-text {\r\n\t\tline-height: 30rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\t.dp-tabbar-bot {\r\n\t\theight: 110rpx;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: content-box\r\n\t}\r\n\t.dp-tabbar-module {\r\n\t\tposition: fixed;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-direction: row;\r\n\t\twidth: 100%;\r\n\t\theight: 110rpx;\r\n\t\tbottom: 0;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tz-index: 999999;\r\n\t\tbox-sizing: content-box;\r\n\t}\r\n\t.dp-tabbar-cut{\r\n\t\tposition: absolute;\r\n\t\theight: 40%;\r\n\t\twidth: 100%;\r\n\t\ttop: 60%;\r\n\t\tbackground: #fff;\r\n\t}\r\n\t.dp-tabbar-sideL{\r\n\t\tposition: relative;\r\n\t\theight: 100%;\r\n\t\tbackground: #fff;\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 0 28rpx 0 0;\r\n\t}\r\n\t.dp-tabbar-sideLP{\r\n\t\tpadding-right: 40rpx;\r\n\t}\r\n\t.dp-tabbar-sideR{\r\n\t\tposition: relative;\r\n\t\theight: 100%;\r\n\t\tbackground: #fff;\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 28rpx 0 0 0;\r\n\t}\r\n\t.dp-tabbar-sideRP{\r\n\t\tpadding-left: 40rpx;\r\n\t}\r\n\t.dp-tabbar-center{\r\n\t\tposition: relative;\r\n\t\twidth: 104rpx;\r\n\t\theight: 100%;\r\n\t\tmargin: 0 -2rpx;\r\n\t}\r\n\t.dp-tabbar-center image{\r\n\t\tposition: absolute;\r\n\t\ttop: -45rpx;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: 0 auto;\r\n\t\theight: 90rpx;\r\n\t\twidth: 90rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t}\r\n\t.dp-tabbar-center view{\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\tline-height: 1;\r\n\t\ttop: 72rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t@supports(bottom: env(safe-area-inset-bottom)) {\r\n\t\t.dp-tabbar-bot {\r\n\t\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\t}\r\n\t\t.dp-tabbar-bar {\r\n\t\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\t}\r\n\t\t.dp-tabbar-module {\r\n\t\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tabbar.vue?vue&type=style&index=0&id=bf0a1df8&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tabbar.vue?vue&type=style&index=0&id=bf0a1df8&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839368489\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}