{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/gk-city/gk-city.vue?7f24", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/gk-city/gk-city.vue?6855", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/gk-city/gk-city.vue?4f8d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/gk-city/gk-city.vue?bc74", "uni-app:///components/gk-city/gk-city.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/gk-city/gk-city.vue?5bab", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/gk-city/gk-city.vue?af47"], "names": ["scrollTimer", "tabBar", "name", "props", "headtitle", "type", "default", "pickerSize", "data", "provincedata", "<PERSON><PERSON><PERSON><PERSON>", "rightIcon", "scrollLeft", "scrollTop", "enableScroll", "tabCurrentIndex", "tabbars", "showData", "pickersize", "showPicker", "watch", "methods", "show", "windowWidth", "text", "value", "console", "findSameId", "deepSearch", "retNode", "hide", "changeTab", "setTimeout", "getElSize", "el", "size", "scrollOffset", "rect", "res", "changCity", "setScroll", "width", "nowWidth", "i", "result", "getScroll", "uni"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmCz1B;EAAAA;EAAAC;AAAA,gBACA;EACAC;EACAC;IACAC;MAAA;MACAC;MACAC;IACA;IACAC;MAAA;MACAF;MACAC;IACA;IACAE;MAAA;MACAH;MACAC;QACA;MACA;IACA;IACAG;MAAA;MACAJ;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;MACAE;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAD;MACA;QACA;MACA;IACA;IACAV;MACA;IACA;EACA;EACAY;IACAC;MAAA;MACA;MACAC;MACA;QACA;QACA;UACAC;UACAC;QACA;QACA;QACA;QACA;QACA;MACA;QACAC;QACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACA;YACAC;UACA;UACA;UACA;UACA;UACA;YACAC;YACA;UACA;YACAA;YACA;UACA;QACA;MACA;MACAD;MACA;IACA;IAEAE;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACAC;UACAC;UACAC;UACAC;QACA;UACAC;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;UACA;QACA;QACA;UACA;YACA;cACAf;cACAC;YACA;YACA;YACA;YACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAH;gBACA;kBACAC;gBACA;cAAA;gBALAC;gBAAA;gBAAA;cAAA;gBAOA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MAAA;MACAC;QACAA;UACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACpNA;AAAA;AAAA;AAAA;AAAoiD,CAAgB,o6CAAG,EAAC,C;;;;;;;;;;;ACAxjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/gk-city/gk-city.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./gk-city.vue?vue&type=template&id=0f29916e&\"\nvar renderjs\nimport script from \"./gk-city.vue?vue&type=script&lang=js&\"\nexport * from \"./gk-city.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gk-city.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/gk-city/gk-city.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gk-city.vue?vue&type=template&id=0f29916e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gk-city.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gk-city.vue?vue&type=script&lang=js&\"", "\r\n<template>\r\n\t<view>\r\n\t\t<view class=\"mask\" :class=\"{'maskShow' : showPicker}\" @click=\"hide\" @click.stop.prevent @touchmove.stop.prevent catchtouchmove=\"true\"></view>\r\n\t\t<view class=\"cpicker-content\" :class=\"{'cpickerShow' : showPicker}\">\r\n\t\t\t<!-- 优惠券页面，仿mt -->\r\n\t\t\t<view class=\"city-head\" @click.stop.prevent @touchmove.stop.prevent catchtouchmove=\"true\">\r\n\t\t\t\t<view class=\"city-head-title\">{{headtitle}}</view>\r\n\t\t\t\t<text v-if=\"rightIcon\" class=\"rightIcon iconfont icon-quxiao\" @click=\"hide('self')\"></text>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view id=\"nav-bar\" class=\"nav-bar\"scroll-x=\"true\" scroll-with-animation=\"true\" :scroll-left=\"scrollLeft\" >\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-for=\"(item,index) in tabbars\" \r\n\t\t\t\t\tclass=\"nav-item\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t:id=\"'tab'+index\"\r\n\t\t\t\t\t@click=\"changeTab(index)\"\r\n\t\t\t\t\t :class=\"{'current': index === tabCurrentIndex}\"\r\n\t\t\t\t><text class=\"nav-bar-title\">{{item.text}}</text></view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view class=\"city_content\"> \r\n\t\t\t\t<scroll-view class=\"panel-scroll-box\" :scroll-y=\"enableScroll\" :cscrollTop=\"scrollTop\" :current=\"tabCurrentIndex\" :scroll-top=\"scrollTop\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in showData\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"flex-row-c-c\" @click=\"changCity(tabCurrentIndex,item)\">\r\n\t\t\t\t\t\t\t<icon type=\"success_no_circle\" v-if=\"tabbars[tabCurrentIndex].value==item.value\" :id=\"'show'+tabCurrentIndex\" class=\"ischeck\" size=\"14\" color=\"#00B1B7\" ></icon>\r\n\t\t\t\t\t\t\t<text class=\"city-text\">{{item.text}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet windowWidth = 0,scrollTimer = false, tabBar;\r\n\texport default {\r\n\t\tname: 'UniCityNvue',\r\n\t\tprops: {\r\n\t\t\theadtitle: {    // 使用多少个tab\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tpickerSize: {    // 使用多少个tab\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 1\r\n\t\t\t},\r\n\t\t\tdata: {      // 默认的省市区id，如果不使用id的情况下则为[]；\r\n\t\t\t\ttype:Array,\r\n\t\t\t\tdefault:function(){\r\n\t\t\t\t\treturn  [];\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tprovincedata: {      // 默认的省市区id，如果不使用id的情况下则为[]；\r\n\t\t\t\ttype:Array,\r\n\t\t\t\tdefault:function(){\r\n\t\t\t\t\treturn  [];\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisFirst: true,\r\n\t\t\t\trightIcon:true,\r\n\t\t\t\tscrollLeft: 500, //顶部选项卡左滑距离\r\n\t\t\t\tscrollTop:0,\r\n\t\t\t\tenableScroll: true,\r\n\t\t\t\ttabCurrentIndex: 0, //当前选项卡索引\r\n\t\t\t\ttabbars:this.provincedata,\r\n\t\t\t\tshowData:this.data,\r\n\t\t\t\tpickersize: this.pickerSize,\r\n\t\t\t\tshowPicker: false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tshowPicker(){\r\n\t\t\t\tif(this.isFirst){\r\n\t\t\t\t\tthis.isFirst = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tprovincedata(val){\r\n\t\t\t\tthis.tabbars=val;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tshow(){\r\n\t\t\t\tthis.showPicker = true;\r\n\t\t\t\twindowWidth = uni.getSystemInfoSync().windowWidth;\r\n\t\t\t\tif(this.provincedata.length>0&&this.provincedata.length<this.pickerSize&&this.isFirst&&this.provincedata[this.provincedata.length-1].value!=\"\"){\r\n\t\t\t\t\tthis.showData=this.findSameId(this.data,this.tabbars[this.provincedata.length],this.tabbars[this.provincedata.length-1]);\r\n\t\t\t\t\tvar current={\r\n\t\t\t\t\t\t\ttext:\"请选择\",\r\n\t\t\t\t\t\t\tvalue:\"\",\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\tthis.tabbars.push(current);\r\n\t\t\t\t\tthis.tabCurrentIndex=this.provincedata.length-1;\r\n\t\t\t\t\tthis.scrollTop=0;\r\n\t\t\t\t\tthis.setScroll(this.tabCurrentIndex);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tconsole.log(this.tabbars)\r\n\t\t\t\t\tthis.showData=this.findSameId(this.data,this.tabbars[this.provincedata.length-1],this.tabbars[this.provincedata.length-2]);\r\n\t\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\t\tthis.tabCurrentIndex=this.provincedata.length-1;\r\n\t\t\t\t\t\tthis.scrollTop=0;\r\n\t\t\t\t\t\tthis.setScroll(this.tabCurrentIndex);\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfindSameId(tree, currentTab,preTab) {\r\n\t\t\t\tlet retNode = null;\r\n\t\t\t\tfunction deepSearch(tree, currentTab,preTab) {\r\n\t\t\t\t\tfor (var i = 0; i < tree.length; i++) {\r\n\t\t\t\t\t\tif (tree[i].children && tree[i].children.length > 0) {\r\n\t\t\t\t\t\t\tdeepSearch(tree[i].children, currentTab,preTab);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar flag=currentTab==undefined?true:(currentTab.value===\"\"?true:false);\r\n\t\t\t\t\t\tvar value=tree[i].value+\"\";\r\n\t\t\t\t\t\tvar text=tree[i].text;\r\n\t\t\t\t\t\tif (preTab!=null&&flag&&preTab.text=== text&&preTab.value+\"\" ===value) {\r\n\t\t\t\t\t\t\tretNode=tree[i].children;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}else if (currentTab!=null&&currentTab.text=== text&&currentTab.value+\"\" === value) {\r\n\t\t\t\t\t\t\tretNode=tree;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tdeepSearch(tree, currentTab,preTab);\r\n\t\t\t\treturn retNode==null?tree:retNode;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\thide(){\r\n\t\t\t\tthis.showPicker = false;\r\n\t\t\t},\r\n\t\t\t//tab切换\r\n\t\t\tchangeTab(e){\r\n\t\t\t\tlet index = e;\r\n\t\t\t\tthis.setScroll(index);\r\n\t\t\t\t//延迟300ms,等待swiper动画结束再修改tabbar\r\n\t\t\t\tthis.tabCurrentIndex = index; \r\n\t\t\t\tthis.showData=this.findSameId(this.data,this.tabbars[index],index===0?this.tabbars[index]:this.tabbars[index-1]);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.getScroll(\"show\"+index);\r\n\t\t\t\t}, 10)\r\n\t\t\t},\r\n\t\t\t//获得元素的size\r\n\t\t\tgetElSize(id) { \r\n\t\t\t\treturn new Promise((res, rej) => {\r\n\t\t\t\t\tlet el = uni.createSelectorQuery().in(this).select('#' + id);\r\n\t\t\t\t\tel.fields({\r\n\t\t\t\t\t\tsize: true,\r\n\t\t\t\t\t\tscrollOffset: true,\r\n\t\t\t\t\t\trect: true\r\n\t\t\t\t\t}, (data) => {\r\n\t\t\t\t\t\tres(data);\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t changCity(index,item) {\r\n\t\t\t\tif(this.tabbars[index].value!=item.value){\r\n\t\t\t\t\tthis.tabbars[index].text=item.text;\r\n\t\t\t\t\tthis.tabbars[index].value=item.value;\r\n\t\t\t\t\tif(index<(this.tabbars.length-1)){\r\n\t\t\t\t\t\tthis.tabbars.splice(index+1,this.tabbars.length-index-1)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(item.children && item.children.length > 0){\r\n\t\t\t\t\t\tif(this.tabbars.length<this.pickersize){\r\n\t\t\t\t\t\t\tvar current={\r\n\t\t\t\t\t\t\t\t\ttext:\"请选择\",\r\n\t\t\t\t\t\t\t\t\tvalue:\"\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.showData=item.children;\r\n\t\t\t\t\t\t\tthis.tabbars.push(current);\r\n\t\t\t\t\t\t\tthis.tabCurrentIndex++; \r\n\t\t\t\t\t\t\tthis.scrollTop=0;\r\n\t\t\t\t\t\t\tthis.setScroll(index);\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthis.$emit('funcvalue',this.tabbars);\r\n\t\t\t\t\t\t\tthis.hide();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.$emit('funcvalue',this.tabbars);\r\n\t\t\t\t\t\tthis.hide();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync setScroll(index){\r\n\t\t\t\tlet width = 0;\r\n\t\t\t\tlet nowWidth = 0;\r\n\t\t\t\tfor (let i = 0; i <= index; i++) {\r\n\t\t\t\t\tlet result = await this.getElSize('tab' + i);\r\n\t\t\t\t\twidth += result.width;\r\n\t\t\t\t\tif(i === index){\r\n\t\t\t\t\t\tnowWidth = result.width;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif ((width+nowWidth)>windowWidth) {\r\n\t\t\t\t\tthis.scrollLeft=width+nowWidth;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.scrollLeft = 0;\r\n\t\t\t\t} \r\n\t\t\t},\r\n\t\t\tgetScroll(id) {\r\n\t\t\t\tuni.createSelectorQuery().in(this).select('.panel-scroll-box').boundingClientRect((data)=>{\r\n\t\t\t\t\tuni.createSelectorQuery().in(this).select('#' + id).boundingClientRect((res)=>{\r\n\t\t\t\t\t\tif(res != undefined && res != null && res != ''){\r\n\t\t\t\t\t\t\tthis.scrollTop=res.top-data.top;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).exec()\r\n\t\t\t\t}).exec();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 优惠券面板 */\r\n.mask {\r\n    visibility: hidden;\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    bottom: 0;\r\n    z-index: 1000;\r\n    background: rgba(0, 0, 0, .6);\r\n    opacity: 0;\r\n    transition: all .3s ease;\r\n}\r\n.maskShow {\r\n    visibility: visible;\r\n    opacity: 1;\r\n}\r\n.cpicker-content {\r\n    position: fixed;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    background-color: #FFFFFF;\r\n    transition: all .3s ease;\r\n    transform: translateY(100%);\r\n    z-index: 3000;\r\n}\r\n.cpickerShow {\r\n    transform: translateY(0);\r\n}\r\n.city-head {\r\n    width: 750rpx;\r\n    height: 88rpx;\r\n    flex-direction: column;\r\n    border-bottom-width: 1px;\r\n    border-bottom-color: #F4F4F4;\r\n    border-bottom-style: solid;\r\n}\r\n.city-head-title {\r\n    font-size: 15px;\r\n    line-height: 88rpx;\r\n    align-items: center;\r\n    /* #ifndef APP-NVUE */\r\n    text-align: center;\r\n    /* #endif */\r\n}\r\n.rightIcon {\r\n    position: absolute;\r\n    right: 15px;\r\n    top: 9px;\r\n    font-size: 30px;\r\n    color: #BEBEBE;\r\n}\r\n.nav-bar {\r\n    position: relative;\r\n    z-index: 10;\r\n    height: 90upx;\r\n    white-space: nowrap;\r\n    box-shadow: 0 2upx 8upx rgba(0,0,0,.06);\r\n    background-color: #fff;\r\n}\r\n.nav-bar::-webkit-scrollbar {\n  display: none;\n}\r\n.nav-item {\r\n    /* #ifndef APP-NVUE */\r\n    display: inline-flex!important;\r\n    /* #endif */\r\n    /* #ifdef APP-NVUE */\r\n    flex-direction: row!important;\r\n    /* #endif */\r\n    width: 170rpx;\r\n    padding: 7px 0px;\r\n    line-height: 26px;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #303133;\r\n    position: relative;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n.nav-bar-title {\r\n    font-size: 12px;\r\n}\r\n.current {\r\n\r\n    border-bottom-width: 4rpx;\r\n    border-bottom-style: solid;\r\n}\r\n.current:after {\r\n    width: 50%;\r\n}\r\n.panel-scroll-box {\r\n    height: 516rpx;\r\n    margin-top: 8px;\r\n}\r\n.flex-row-c-c {\r\n    /* #ifndef APP-NVUE */\r\n    display: block;\r\n    /* #endif */\r\n    /* #ifdef APP-NVUE */\r\n    flex-direction: row;\r\n    /* #endif */\r\n    padding-left: 25px;\r\n}\r\n.city-text {\r\n    /* #ifdef APP-NVUE */\r\n    flex-direction: row;\r\n    /* #endif */\r\n    height: 35px;\r\n    line-height: 35px;\r\n    font-size: 13px;\r\n}\r\n.hide {\r\n    opacity: 0;\r\n}\r\n.ischeck {\r\n    /* #ifndef APP-NVUE */\r\n    display: inline-flex!important;\r\n    /* #endif */\r\n    /* #ifdef APP-NVUE */\r\n    flex-direction: column;\r\n    /* #endif */\r\n    margin-right: 5px;\r\n    vertical-align: middle;\r\n}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gk-city.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gk-city.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839412654\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}