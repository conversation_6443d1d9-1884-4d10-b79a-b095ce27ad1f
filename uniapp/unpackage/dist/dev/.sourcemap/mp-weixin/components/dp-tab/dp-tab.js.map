{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tab/dp-tab.vue?bb1e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tab/dp-tab.vue?16fe", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tab/dp-tab.vue?70bc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tab/dp-tab.vue?9e33", "uni-app:///components/dp-tab/dp-tab.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tab/dp-tab.vue?d4a6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tab/dp-tab.vue?1edb"], "names": ["data", "loading", "tabindex", "pagecontent", "latitude", "longitude", "tabtop", "needfixed", "homeNavigationCustom", "currentPage", "props", "params", "tabid", "default", "menuindex", "richtype", "<PERSON><PERSON><PERSON>", "navigationHieght", "mounted", "that", "currentTarget", "dataset", "index", "setTimeout", "view0", "size", "rect", "scrollOffset", "uni", "methods", "changetab", "getdata", "app", "tabindexid", "console", "getIndexdata"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,iHAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,iNAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,uQAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,uNAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,iQAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,uNAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7PA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0Jx1B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAX;IACAY;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;MAAAJ;IAAA;EACA;EACAK;IACA;IACA;IACAC;IACA;MAAAC;QAAAC;UAAAC;QAAA;MAAA;IAAA;IACA;MACAC;QACA;QACAC;UACAC;UAAA;UACAC;UAAA;UACAC;QACA;UACA;YACAR;UACA;YACAA;UACA;QACA;MACA;MACAS;QACAT;QACA;UACA;UACAA;QACA;UACAA;QACA;MACA;IACA;EACA;EACAU;IACAC;MACA;MACA;MACAX;MACA;MACA;MACAA;IACA;IACAY;MACA;MACAZ;MACAa;QAAApB;QAAAqB;QAAA7B;QAAAC;MAAA;QACAc;QACAA;QACA;UACA;UACAa;YACA;YACAb;YACAA;YACAA;UACA;YACAe;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjPA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-tab/dp-tab.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-tab.vue?vue&type=template&id=54aef9c4&\"\nvar renderjs\nimport script from \"./dp-tab.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-tab.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-tab.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-tab/dp-tab.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tab.vue?vue&type=template&id=54aef9c4&\"", "var components\ntry {\n  components = {\n    dpNotice: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-notice/dp-notice\" */ \"@/components/dp-notice/dp-notice.vue\"\n      )\n    },\n    dpBanner: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-banner/dp-banner\" */ \"@/components/dp-banner/dp-banner.vue\"\n      )\n    },\n    dpSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-search/dp-search\" */ \"@/components/dp-search/dp-search.vue\"\n      )\n    },\n    dpText: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-text/dp-text\" */ \"@/components/dp-text/dp-text.vue\"\n      )\n    },\n    dpTitle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-title/dp-title\" */ \"@/components/dp-title/dp-title.vue\"\n      )\n    },\n    dpDhlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-dhlist/dp-dhlist\" */ \"@/components/dp-dhlist/dp-dhlist.vue\"\n      )\n    },\n    dpLine: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-line/dp-line\" */ \"@/components/dp-line/dp-line.vue\"\n      )\n    },\n    dpBlank: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-blank/dp-blank\" */ \"@/components/dp-blank/dp-blank.vue\"\n      )\n    },\n    dpMenu: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-menu/dp-menu\" */ \"@/components/dp-menu/dp-menu.vue\"\n      )\n    },\n    dpMap: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-map/dp-map\" */ \"@/components/dp-map/dp-map.vue\"\n      )\n    },\n    dpCube: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cube/dp-cube\" */ \"@/components/dp-cube/dp-cube.vue\"\n      )\n    },\n    dpPicture: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-picture/dp-picture\" */ \"@/components/dp-picture/dp-picture.vue\"\n      )\n    },\n    dpPictures: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-pictures/dp-pictures\" */ \"@/components/dp-pictures/dp-pictures.vue\"\n      )\n    },\n    dpVideo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-video/dp-video\" */ \"@/components/dp-video/dp-video.vue\"\n      )\n    },\n    dpTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tab/dp-tab\" */ \"@/components/dp-tab/dp-tab.vue\"\n      )\n    },\n    dpShop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-shop/dp-shop\" */ \"@/components/dp-shop/dp-shop.vue\"\n      )\n    },\n    dpProduct: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product/dp-product\" */ \"@/components/dp-product/dp-product.vue\"\n      )\n    },\n    dpCollage: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-collage/dp-collage\" */ \"@/components/dp-collage/dp-collage.vue\"\n      )\n    },\n    dpLuckycollage: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-luckycollage/dp-luckycollage\" */ \"@/components/dp-luckycollage/dp-luckycollage.vue\"\n      )\n    },\n    dpKanjia: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kanjia/dp-kanjia\" */ \"@/components/dp-kanjia/dp-kanjia.vue\"\n      )\n    },\n    dpYuyue: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-yuyue/dp-yuyue\" */ \"@/components/dp-yuyue/dp-yuyue.vue\"\n      )\n    },\n    dpSeckill: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-seckill/dp-seckill\" */ \"@/components/dp-seckill/dp-seckill.vue\"\n      )\n    },\n    dpScoreshop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-scoreshop/dp-scoreshop\" */ \"@/components/dp-scoreshop/dp-scoreshop.vue\"\n      )\n    },\n    dpTuangou: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tuangou/dp-tuangou\" */ \"@/components/dp-tuangou/dp-tuangou.vue\"\n      )\n    },\n    dpKecheng: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kecheng/dp-kecheng\" */ \"@/components/dp-kecheng/dp-kecheng.vue\"\n      )\n    },\n    dpRestaurantProduct: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-restaurant-product/dp-restaurant-product\" */ \"@/components/dp-restaurant-product/dp-restaurant-product.vue\"\n      )\n    },\n    dpCoupon: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-coupon/dp-coupon\" */ \"@/components/dp-coupon/dp-coupon.vue\"\n      )\n    },\n    dpArticle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-article/dp-article\" */ \"@/components/dp-article/dp-article.vue\"\n      )\n    },\n    dpBusiness: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-business/dp-business\" */ \"@/components/dp-business/dp-business.vue\"\n      )\n    },\n    dpShortvideo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-shortvideo/dp-shortvideo\" */ \"@/components/dp-shortvideo/dp-shortvideo.vue\"\n      )\n    },\n    dpLiveroom: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-liveroom/dp-liveroom\" */ \"@/components/dp-liveroom/dp-liveroom.vue\"\n      )\n    },\n    dpButton: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-button/dp-button\" */ \"@/components/dp-button/dp-button.vue\"\n      )\n    },\n    dpHotspot: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-hotspot/dp-hotspot\" */ \"@/components/dp-hotspot/dp-hotspot.vue\"\n      )\n    },\n    dpCover: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cover/dp-cover\" */ \"@/components/dp-cover/dp-cover.vue\"\n      )\n    },\n    dpRichtext: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-richtext/dp-richtext\" */ \"@/components/dp-richtext/dp-richtext.vue\"\n      )\n    },\n    dpForm: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-form/dp-form\" */ \"@/components/dp-form/dp-form.vue\"\n      )\n    },\n    dpFormLog: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-form-log/dp-form-log\" */ \"@/components/dp-form-log/dp-form-log.vue\"\n      )\n    },\n    dpUserinfo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-userinfo/dp-userinfo\" */ \"@/components/dp-userinfo/dp-userinfo.vue\"\n      )\n    },\n    dpWxad: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-wxad/dp-wxad\" */ \"@/components/dp-wxad/dp-wxad.vue\"\n      )\n    },\n    dpJidian: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-jidian/dp-jidian\" */ \"@/components/dp-jidian/dp-jidian.vue\"\n      )\n    },\n    dpCycle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cycle/dp-cycle\" */ \"@/components/dp-cycle/dp-cycle.vue\"\n      )\n    },\n    dpCarhailing: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-carhailing/dp-carhailing\" */ \"@/components/dp-carhailing/dp-carhailing.vue\"\n      )\n    },\n    dpTour: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tour/dp-tour\" */ \"@/components/dp-tour/dp-tour.vue\"\n      )\n    },\n    dpFormdata: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-formdata/dp-formdata\" */ \"@/components/dp-formdata/dp-formdata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tab.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tab.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-tab\" :style=\"{\r\n\tmargin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',\r\n\tborderRadius:params.borderradius*2.2+'rpx'\r\n}\">\r\n\t<view v-if=\"needfixed==1\" style=\"width:100%;height:90rpx;\"></view>\r\n\t<view class=\"dsn-tab-box\" :class=\"needfixed==1 ? ((homeNavigationCustom > 1 && currentPage == 'pages/index/index') ? 'fixed-custom':'fixed'):''\" :style=\"{background:params.bgcolor,padding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx',Top:(homeNavigationCustom > 1 && currentPage == 'pages/index/index' && navigationHieght > 0) ? navigationHieght+'px' :''}\">\r\n\t<scroll-view scroll-x=\"true\" style=\"overflow: visible !important\">\r\n\t\t<view class=\"dsn-tab-box-content\">\r\n\t\t\t<view v-for=\"(item,index) in data\" :key=\"index\" class=\"dp-tab-item\" @tap=\"changetab\" :data-index=\"index\" :style=\"{fontSize:params.fontsize1*2+'rpx',color:(tabindex==index?params.color2:params.color1)}\">{{item.name}}<view class=\"dp-tab-item-after\" :style=\"{background:params.arrowcolor}\" v-if=\"params.arrowshow==1 && tabindex==index\"></view></view>\r\n\t\t</view>\r\n\t</scroll-view>\r\n\t</view>\r\n\t<view class=\"dp-tab-content\" :style=\"{background:params.bgcolor2,padding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx'}\">\r\n\t\t<block v-for=\"(setData, index) in pagecontent\" :key=\"index\">\r\n\t\t\t<block v-if=\"setData.temp=='notice'\">\r\n\t\t\t\t<dp-notice :params=\"setData.params\" :data=\"setData.data\"></dp-notice>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='banner'\">\r\n\t\t\t\t<dp-banner :params=\"setData.params\" :data=\"setData.data\"></dp-banner> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='search'\">\r\n\t\t\t\t<dp-search :params=\"setData.params\" :data=\"setData.data\"></dp-search>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='text'\">\r\n\t\t\t\t<dp-text :params=\"setData.params\" :data=\"setData.data\"></dp-text>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='title'\">\r\n\t\t\t\t<dp-title :params=\"setData.params\" :data=\"setData.data\"></dp-title>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='dhlist'\">\r\n\t\t\t\t<dp-dhlist :params=\"setData.params\" :data=\"setData.data\"></dp-dhlist>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='line'\">\r\n\t\t\t\t<dp-line :params=\"setData.params\" :data=\"setData.data\"></dp-line>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='blank'\">\r\n\t\t\t\t<dp-blank :params=\"setData.params\" :data=\"setData.data\"></dp-blank>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='menu'\">\r\n\t\t\t\t<dp-menu :params=\"setData.params\" :data=\"setData.data\"></dp-menu> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='map'\">\r\n\t\t\t\t<dp-map :params=\"setData.params\" :data=\"setData.data\"></dp-map> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='cube'\">\r\n\t\t\t\t<dp-cube :params=\"setData.params\" :data=\"setData.data\" :tabcub=\"params\"></dp-cube> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='picture'\">\r\n\t\t\t\t<dp-picture :params=\"setData.params\" :data=\"setData.data\"></dp-picture> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='pictures'\"> \r\n\t\t\t\t<dp-pictures :params=\"setData.params\" :data=\"setData.data\"></dp-pictures> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='video'\">\r\n\t\t\t\t<dp-video :params=\"setData.params\" :data=\"setData.data\"></dp-video> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='tab'\">\r\n\t\t\t\t<dp-tab :params=\"setData.params\" :data=\"setData.data\" :tabid=\"setData.id\" :menuindex=\"menuindex\"></dp-tab> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='shop'\">\r\n\t\t\t\t<dp-shop :params=\"setData.params\" :data=\"setData.data\" :shopinfo=\"setData.shopinfo\"></dp-shop> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='product'\">\r\n\t\t\t\t<dp-product :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-product> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='collage'\">\r\n\t\t\t\t<dp-collage :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-collage> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='luckycollage'\">\r\n\t\t\t\t<dp-luckycollage :params=\"setData.params\" :data=\"setData.data\"></dp-luckycollage> \r\n\t\t\t</block>\r\n\r\n\t\t\t<block v-if=\"setData.temp=='kanjia'\">\r\n\t\t\t\t<dp-kanjia :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-kanjia> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='yuyue'\">\r\n\t\t\t\t<dp-yuyue :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-yuyue>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='seckill'\">\r\n\t\t\t\t<dp-seckill :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-seckill> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='scoreshop'\">\r\n\t\t\t\t<dp-scoreshop :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-scoreshop> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='tuangou'\">\r\n\t\t\t\t<dp-tuangou :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-tuangou> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='kecheng'\">\r\n\t\t\t\t<dp-kecheng :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-kecheng> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='restaurant_product'\">\r\n\t\t\t\t<dp-restaurant-product :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-restaurant-product> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='coupon'\">\r\n\t\t\t\t<dp-coupon :params=\"setData.params\" :data=\"setData.data\"></dp-coupon> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='article'\">\r\n\t\t\t\t<dp-article :params=\"setData.params\" :data=\"setData.data\"></dp-article> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='business'\">\r\n\t\t\t\t<dp-business :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-business> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='shortvideo'\">\r\n\t\t\t\t<dp-shortvideo :params=\"setData.params\" :data=\"setData.data\"></dp-shortvideo> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='liveroom'\">\r\n\t\t\t\t<dp-liveroom :params=\"setData.params\" :data=\"setData.data\"></dp-liveroom> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='button'\">\r\n\t\t\t\t<dp-button :params=\"setData.params\" :data=\"setData.data\"></dp-button> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='hotspot'\">\r\n\t\t\t\t<dp-hotspot :params=\"setData.params\" :data=\"setData.data\"></dp-hotspot> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='cover'\">\r\n\t\t\t\t<dp-cover :params=\"setData.params\" :data=\"setData.data\"></dp-cover> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='richtext'\">\r\n\t\t\t\t<dp-richtext :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\" :richtype=\"richtype\" :richurl=\"richurl\"></dp-richtext>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='form'\">\r\n\t\t\t\t<dp-form :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-form> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='form-log'\">\r\n\t\t\t\t<dp-form-log :params=\"setData.params\" :data=\"setData.data\"></dp-form-log> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='userinfo'\">\r\n\t\t\t\t<dp-userinfo :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-userinfo> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='wxad'\">\r\n\t\t\t\t<dp-wxad :params=\"setData.params\" :data=\"setData.data\"></dp-wxad> \r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='jidian'\">\r\n\t\t\t\t<dp-jidian :params=\"setData.params\" :data=\"setData.data\"></dp-jidian>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='cycle'\">\r\n\t\t\t\t<dp-cycle :params=\"setData.params\" :data=\"setData.data\" @getdata=\"getdata\"></dp-cycle>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"setData.temp=='carhailing'\">\r\n\t\t\t\t<dp-carhailing :params=\"setData.params\" :data=\"setData.data\" @getdata=\"getdata\"></dp-carhailing>\r\n\t\t\t</block>\r\n      <block v-if=\"setData.temp=='tour'\">\r\n      \t<dp-tour :params=\"setData.params\" :data=\"setData.data\" @getdata=\"getIndexdata\"></dp-tour>\r\n      </block>\r\n      <block v-if=\"setData.temp=='formdata'\">\r\n      \t<dp-formdata :params=\"setData.params\" :data=\"setData.data\" @getdata=\"getIndexdata\"></dp-formdata>\r\n      </block>\r\n\t\t</block>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tloading:false,\r\n\t\t\t\ttabindex:0,\r\n\t\t\t\tpagecontent:[],\r\n\t\t\t\tlatitude:'',\r\n\t\t\t\tlongitude:'',\r\n\t\t\t\ttabtop:0,\r\n\t\t\t\tneedfixed:0,\r\n\t\t\t\thomeNavigationCustom: app.globalData.homeNavigationCustom,\r\n\t\t\t\tcurrentPage:'',\r\n\t\t\t}\r\n    },\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{},\r\n\t\t\ttabid:{default:''},\r\n\t\t\tmenuindex:{default:-1},\r\n      richtype:{default:0},\r\n      richurl:{default:''},\r\n\t\t\tnavigationHieght:{default:0},\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tthat.currentPage = pages[pages.length - 1].route; //获取当前页面的对象\r\n\t\t\tthis.changetab({currentTarget:{dataset:{index:0}}});\r\n\t\t\tif(this.params.fixedtop == 1){\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tlet view0 = uni.createSelectorQuery().in(that).select('.dsn-tab-box')\r\n\t\t\t\t\tview0.fields({\r\n\t\t\t\t\t\tsize: false,//是否返回节点尺寸（width height）\r\n\t\t\t\t\t\trect: true,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\tif(app.globalData.homeNavigationCustom > 1 && that.currentPage == 'pages/index/index'){\r\n\t\t\t\t\t\t\tthat.tabtop = res.top - 91;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.tabtop = res.top;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t},100);\r\n\t\t\t\tuni.$on('onPageScroll',function(data){\r\n\t\t\t\t\tthat.scrollTop = data.scrollTop;\r\n\t\t\t\t\tif(data.scrollTop > 0 && data.scrollTop >= that.tabtop){\r\n\t\t\t\t\t\t//console.log('------');\r\n\t\t\t\t\t\tthat.needfixed = 1;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.needfixed = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tchangetab:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar idx = e.currentTarget.dataset.index;\r\n\t\t\t\tthat.tabindex = idx;\r\n\t\t\t\t//console.log(that.data[idx].id)\r\n\t\t\t\t//console.log(that.tabid)\r\n\t\t\t\tthat.getdata();\r\n\t\t\t},\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiIndex/gettabcontent', {tabid: that.tabid,tabindexid:that.data[that.tabindex].id,latitude:that.latitude,longitude:that.longitude}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\t\tif(that.latitude=='' && that.longitude=='' && res.needlocation){\r\n\t\t\t\t\t\t//console.log('getLocation')\r\n\t\t\t\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\t\t\t\t//console.log(res)\r\n\t\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t},function(res2){\r\n\t\t\t\t\t\t\tconsole.log(res2)\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetIndexdata:function(){\r\n\t\t\t\t\tthis.$emit('getdata');\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-tab {height:auto;position:relative;background: rgb(255,255,255,0);overflow:hidden}\r\n.dsn-tab-box{display:flex;width:100%;height:90rpx;background: #fff;}\r\n.dsn-tab-box.fixed{position:fixed;top:0;z-index:9;}\r\n.dsn-tab-box.fixed-custom{position:fixed;top:89px;z-index:100;}\r\n.dsn-tab-box-content{flex-grow: 0;flex-shrink: 0;display:flex;align-items:center;flex-wrap:nowrap;position:relative;}\r\n.dp-tab-item{flex-grow:1;padding:0 20rpx;flex-shrink: 0;font-size:28rpx; text-align:center; color:#666; height: 90rpx; line-height: 90rpx;overflow: hidden;position:relative}\r\n.dp-tab-item-after{position:absolute;left:50%;margin-left:-24rpx;bottom:10rpx;height:3px;border-radius:1.5px;width:48rpx}\r\n.dp-tab-content{min-height:100rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tab.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tab.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839375914\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}