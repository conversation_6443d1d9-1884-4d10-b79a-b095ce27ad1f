{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-load-more/uni-load-more.vue?4abb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-load-more/uni-load-more.vue?7aeb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-load-more/uni-load-more.vue?d914", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-load-more/uni-load-more.vue?eec7", "uni-app:///components/uni-load-more/uni-load-more.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-load-more/uni-load-more.vue?b838", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-load-more/uni-load-more.vue?2340"], "names": ["name", "props", "status", "type", "default", "showIcon", "iconType", "iconSize", "color", "contentText", "contentdown", "contentrefresh", "contentnomore", "data", "webviewHide", "platform", "computed", "iconSnowWidth", "mounted", "methods", "onClick", "detail"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA20B,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8B/1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,gBAkBA;EACAA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;QACA;UACAM;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC,6BAYA;EACAC;IACAC;MACA;QACAC;UACAnB;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAAwrC,CAAgB,wmCAAG,EAAC,C;;;;;;;;;;;ACA5sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-load-more/uni-load-more.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-load-more.vue?vue&type=template&id=5f6e5104&\"\nvar renderjs\nimport script from \"./uni-load-more.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-load-more.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-load-more.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-load-more/uni-load-more.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-load-more.vue?vue&type=template&id=5f6e5104&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-load-more.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-load-more.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-load-more\" @click=\"onClick\">\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t<loading-indicator v-if=\"!webviewHide && status === 'loading' && showIcon\" :style=\"{color: color,width:iconSize+'px',height:iconSize+'px'}\" :animating=\"true\" class=\"uni-load-more__img uni-load-more__img--nvue\"></loading-indicator>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<svg width=\"24\" height=\"24\" viewBox=\"25 25 50 50\" v-if=\"!webviewHide && (iconType==='circle' || iconType==='auto' && platform === 'android') && status === 'loading' && showIcon\"\r\n\t\t:style=\"{width:iconSize+'px',height:iconSize+'px'}\" class=\"uni-load-more__img uni-load-more__img--android-H5\">\r\n\t\t\t<circle cx=\"50\" cy=\"50\" r=\"20\" fill=\"none\" :style=\"{color:color}\" :stroke-width=\"3\"></circle>\r\n\t\t</svg>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifndef APP-NVUE || H5 -->\r\n\t\t<view v-if=\"!webviewHide && (iconType==='circle' || iconType==='auto' && platform === 'android') && status === 'loading' && showIcon\"\r\n\t\t:style=\"{width:iconSize+'px',height:iconSize+'px'}\" class=\"uni-load-more__img uni-load-more__img--android-MP\">\r\n\t\t\t<view :style=\"{borderTopColor:color,borderTopWidth:iconSize/12}\"></view>\r\n\t\t\t<view :style=\"{borderTopColor:color,borderTopWidth:iconSize/12}\"></view>\r\n\t\t\t<view :style=\"{borderTopColor:color,borderTopWidth:iconSize/12}\"></view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t<view v-else-if=\"!webviewHide && status === 'loading' && showIcon\" :style=\"{width:iconSize+'px',height:iconSize+'px'}\" class=\"uni-load-more__img uni-load-more__img--ios-H5\">\n\t\t\t<image src=\"data:image/png;base64,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\"\n\t\t\t\t\t\t mode=\"widthFix\"></image>\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<text class=\"uni-load-more__text\" :style=\"{color: color}\">{{ status === 'more' ? contentText.contentdown : status === 'loading' ? contentText.contentrefresh : contentText.contentnomore }}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst platform = uni.getSystemInfoSync().platform\n\n\t/**\n\t * LoadMore 加载更多\n\t * @description 用于列表中，做滚动加载使用，展示 loading 的各种状态\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=29\n\t * @property {String} status = [more|loading|noMore] loading 的状态\n\t * \t@value more loading前\n\t * \t@value loading loading中\n\t * \t@value noMore 没有更多了\n\t * @property {Number} iconSize 指定图标大小\n\t * @property {Boolean} iconSize = [true|false] 是否显示 loading 图标\n\t * @property {String} iconType = [snow|circle|auto] 指定图标样式\n\t * \t@value snow ios雪花加载样式\n\t * \t@value circle 安卓唤醒加载样式\n\t * \t@value auto 根据平台自动选择加载样式\n\t * @property {String} color 图标和文字颜色\n\t * @property {Object} contentText 各状态文字说明，值为：{contentdown: \"上拉显示更多\",contentrefresh: \"正在加载...\",contentnomore: \"没有更多数据了\"}\n\t * @event {Function} clickLoadMore 点击加载更多时触发\n\t */\r\n\texport default {\r\n\t\tname: 'UniLoadMore',\r\n\t\tprops: {\r\n\t\t\tstatus: {\r\n\t\t\t\t// 上拉的状态：more-loading前；loading-loading中；noMore-没有更多了\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'more'\r\n\t\t\t},\r\n\t\t\tshowIcon: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\ticonType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'auto'\r\n\t\t\t},\r\n\t\t\ticonSize: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 24\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#777777'\r\n\t\t\t},\r\n\t\t\tcontentText: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tcontentdown: '上拉显示更多',\r\n\t\t\t\t\t\tcontentrefresh: '正在加载...',\r\n\t\t\t\t\t\tcontentnomore: '没有更多数据了'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n                webviewHide: false,\r\n\t\t\t\tplatform: platform\r\n\t\t\t}\r\n\t\t},\n\t\t// #ifndef APP-NVUE\n\t\tcomputed:{\n\t\t\ticonSnowWidth(){\n\t\t\t\treturn (Math.floor(this.iconSize/24)||1)*2\n\t\t\t}\n\t\t},\n\t\t// #endif\r\n\t\tmounted() {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tvar page = pages[pages.length - 1];\r\n\t\t\tvar currentWebview = page.$getAppWebview();\r\n\t\t\tcurrentWebview.addEventListener('hide', () => {\r\n\t\t\t\tthis.webviewHide = true\r\n\t\t\t})\r\n\t\t\tcurrentWebview.addEventListener('show', () => {\r\n\t\t\t\tthis.webviewHide = false\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonClick() {\r\n\t\t\t\tthis.$emit('clickLoadMore', {\r\n\t\t\t\t\tdetail: {\r\n\t\t\t\t\t\tstatus: this.status,\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\r\n\t.uni-load-more {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\theight: 40px;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.uni-load-more__text {\r\n\t\tfont-size: 15px;\r\n\t}\r\n\r\n\t.uni-load-more__img {\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\t\tmargin-right: 8px;\r\n\t}\r\n\r\n\t.uni-load-more__img--nvue {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.uni-load-more__img--android,\r\n\t.uni-load-more__img--ios {\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.uni-load-more__img--android {\r\n\t\tanimation: loading-ios 1s 0s linear infinite;\r\n\t}\r\n\r\n\t@keyframes loading-android {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\n\t.uni-load-more__img--ios-H5 {\n\t\tposition: relative;\n\t\tanimation: loading-ios-H5 1s 0s step-end infinite;\n\t}\n\n\t.uni-load-more__img--ios-H5>image {\n\t\tposition: absolute;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tleft: 0;\n\t\ttop: 0;\n\t}\n\n\t@keyframes loading-ios-H5 {\n\t\t0% {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\n\t\t8% {\n\t\t\ttransform: rotate(30deg);\n\t\t}\n\n\t\t16% {\n\t\t\ttransform: rotate(60deg);\n\t\t}\n\n\t\t24% {\n\t\t\ttransform: rotate(90deg);\n\t\t}\n\n\t\t32% {\n\t\t\ttransform: rotate(120deg);\n\t\t}\n\n\t\t40% {\n\t\t\ttransform: rotate(150deg);\n\t\t}\n\n\t\t48% {\n\t\t\ttransform: rotate(180deg);\n\t\t}\n\n\t\t56% {\n\t\t\ttransform: rotate(210deg);\n\t\t}\n\n\t\t64% {\n\t\t\ttransform: rotate(240deg);\n\t\t}\n\n\t\t73% {\n\t\t\ttransform: rotate(270deg);\n\t\t}\n\n\t\t82% {\n\t\t\ttransform: rotate(300deg);\n\t\t}\n\n\t\t91% {\n\t\t\ttransform: rotate(330deg);\n\t\t}\n\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t/* #ifdef H5 */\r\n\t.uni-load-more__img--android-H5 {\r\n\t\tanimation: loading-android-H5-rotate 2s linear infinite;\r\n\t\ttransform-origin: center center;\r\n\t}\r\n\r\n\t.uni-load-more__img--android-H5>circle {\r\n\t\tdisplay: inline-block;\r\n\t\tanimation: loading-android-H5-dash 1.5s ease-in-out infinite;\r\n\t\tstroke: currentColor;\r\n\t\tstroke-linecap: round;\r\n\t}\r\n\r\n\t@keyframes loading-android-H5-rotate {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes loading-android-H5-dash {\r\n\t\t0% {\r\n\t\t\tstroke-dasharray: 1, 200;\r\n\t\t\tstroke-dashoffset: 0;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\tstroke-dasharray: 90, 150;\r\n\t\t\tstroke-dashoffset: -40;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tstroke-dasharray: 90, 150;\r\n\t\t\tstroke-dashoffset: -120;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\n\n\t/* #ifndef APP-NVUE || H5 */\n\t.uni-load-more__img--android-MP {\n\t\tposition: relative;\n\t\twidth: 24px;\n\t\theight: 24px;\n\t\ttransform: rotate(0deg);\r\n\t\tanimation: loading-ios 1s 0s ease infinite;\n\t}\n\n\t.uni-load-more__img--android-MP>view {\n\t\tposition: absolute;\n\t\tbox-sizing: border-box;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 50%;\n\t\tborder: solid 2px transparent;\n\t\tborder-top: solid 2px #777777;\n\t\ttransform-origin: center;\n\t}\n\n\t.uni-load-more__img--android-MP>view:nth-child(1){\n\t\tanimation: loading-android-MP-1 1s 0s linear infinite;\n\t}\n\n\t.uni-load-more__img--android-MP>view:nth-child(2){\n\t\tanimation: loading-android-MP-2 1s 0s linear infinite;\n\t}\n\n\t.uni-load-more__img--android-MP>view:nth-child(3){\n\t\tanimation: loading-android-MP-3 1s 0s linear infinite;\n\t}\n\n\t@keyframes loading-android {\n\t\t0% {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\n\t@keyframes loading-android-MP-1{\n\t\t0%{\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\t50%{\n\t\t\ttransform: rotate(90deg);\n\t\t}\n\t\t100%{\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\t@keyframes loading-android-MP-2{\n\t\t0%{\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\t50%{\n\t\t\ttransform: rotate(180deg);\n\t\t}\n\t\t100%{\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\t@keyframes loading-android-MP-3{\n\t\t0%{\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\t50%{\n\t\t\ttransform: rotate(270deg);\n\t\t}\n\t\t100%{\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\t/* #endif */\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-load-more.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-load-more.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839380856\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}