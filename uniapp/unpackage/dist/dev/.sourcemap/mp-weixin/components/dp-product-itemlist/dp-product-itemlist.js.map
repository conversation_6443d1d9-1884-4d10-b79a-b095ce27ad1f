{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-itemlist/dp-product-itemlist.vue?9016", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-itemlist/dp-product-itemlist.vue?ce46", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-itemlist/dp-product-itemlist.vue?ca18", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-itemlist/dp-product-itemlist.vue?abaa", "uni-app:///components/dp-product-itemlist/dp-product-itemlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-itemlist/dp-product-itemlist.vue?4ebb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-itemlist/dp-product-itemlist.vue?425b"], "names": ["data", "buydialogShow", "proid", "showLinkStatus", "lx_bname", "lx_name", "lx_bid", "lx_tel", "productType", "ggNum", "pre_url", "props", "menuindex", "default", "saleimg", "showname", "namecolor", "showprice", "showcost", "showsales", "showstock", "showcart", "cartimg", "idfield", "probgcolor", "showcommission", "showbname", "showbdistance", "methods", "buydialogChange", "showLinkChange", "that", "toDetail", "url", "app"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AACuE;AACL;AACa;;;AAG/E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtTA;AAAA;AAAA;AAAA;AAAi1B,CAAgB,izBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwLr2B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;MAAAJ;IAAA;IACAK;MAAAL;IAAA;IACAM;MAAAN;IAAA;IACAO;MAAAP;IAAA;IACAQ;MAAAR;IAAA;IACAS;MAAAT;IAAA;IACAb;IACAuB;MAAAV;IAAA;IACAW;MAAAX;IAAA;IACAY;MACAZ;IACA;IACAa;MACAb;IACA;IACAc;MACAd;IACA;EACA;EACAe;IACAC;MAAA;MACA;QACA;QACA;UACA;YACA;YACA;cACA;gBACA;cACA;gBACA;cACA;YACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACAC;MACAA;MACAA;MACAA;MACAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACAC;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5QA;AAAA;AAAA;AAAA;AAA8rC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACAltC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-product-itemlist/dp-product-itemlist.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-product-itemlist.vue?vue&type=template&id=1bf8f066&\"\nvar renderjs\nimport script from \"./dp-product-itemlist.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-product-itemlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-product-itemlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-product-itemlist/dp-product-itemlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-itemlist.vue?vue&type=template&id=1bf8f066&\"", "var components\ntry {\n  components = {\n    buydialogPifa: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa/buydialog-pifa\" */ \"@/components/buydialog-pifa/buydialog-pifa.vue\"\n      )\n    },\n    buydialogPifa2: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa2/buydialog-pifa2\" */ \"@/components/buydialog-pifa2/buydialog-pifa2.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.data, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = item.sellpoint ? _vm.t(\"color2\") : null\n    var m1 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"0\" || !item.price_show_type) &&\n      !item.price_color\n        ? _vm.t(\"color1\")\n        : null\n    var m2 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"0\" || !item.price_show_type) &&\n      !item.usd_sellprice\n        ? !_vm.isNull(item.service_fee) &&\n          item.service_fee_switch &&\n          item.service_fee > 0\n        : null\n    var m3 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"0\" || !item.price_show_type) &&\n      !item.usd_sellprice &&\n      m2\n        ? _vm.t(\"服务费\")\n        : null\n    var m4 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"0\" || !item.price_show_type) &&\n      !item.usd_sellprice &&\n      item.product_type == 2 &&\n      item.unit_price &&\n      item.unit_price > 0\n        ? _vm.t(\"color1\")\n        : null\n    var g0 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"0\" || !item.price_show_type) &&\n      !item.usd_sellprice\n        ? item.fwlist && item.fwlist.length > 0\n        : null\n    var l0 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"0\" || !item.price_show_type) &&\n      !item.usd_sellprice &&\n      g0\n        ? _vm.__map(item.fwlist, function (fw, fwidx) {\n            var $orig = _vm.__get_orig(fw)\n            var m5 = _vm.t(\"color2rgb\")\n            var m6 = _vm.t(\"color2\")\n            return {\n              $orig: $orig,\n              m5: m5,\n              m6: m6,\n            }\n          })\n        : null\n    var m7 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"0\"\n        ? _vm.t(\"color1\")\n        : null\n    var m8 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"0\" &&\n      item.price_show_type == \"2\" &&\n      item.lvprice == 1\n        ? _vm.t(\"color1\")\n        : null\n    var m9 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"0\" &&\n      item.price_show_type == \"2\" &&\n      item.lvprice == 1\n        ? !_vm.isNull(item.level_name) && !_vm.isEmpty(item.level_name)\n        : null\n    var m10 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"0\" &&\n      item.price_show_type == \"2\" &&\n      item.lvprice == 1 &&\n      m9\n        ? _vm.t(\"color1\")\n        : null\n    var m11 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"0\" &&\n      item.price_show_type == \"2\" &&\n      item.lvprice == 1\n        ? _vm.t(\"color1\")\n        : null\n    var m12 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"1\"\n        ? _vm.t(\"color1\")\n        : null\n    var m13 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"1\"\n        ? !_vm.isNull(item.level_name) && !_vm.isEmpty(item.level_name)\n        : null\n    var m14 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"1\" &&\n      m13\n        ? _vm.t(\"color1\")\n        : null\n    var m15 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"1\"\n        ? _vm.t(\"color1\")\n        : null\n    var m16 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"1\" &&\n      item.service_fee_switch &&\n      item.service_fee > 0\n        ? _vm.t(\"服务费\")\n        : null\n    var m17 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice != \"0\" &&\n      (item.price_show_type == \"1\" || item.price_show_type == \"2\") &&\n      item.is_vip == \"1\"\n        ? _vm.t(\"color1\")\n        : null\n    var m18 =\n      item.xunjia_text && item.price_type == 1 && item.sell_price <= 0\n        ? _vm.t(\"color1\")\n        : null\n    var m19 =\n      item.xunjia_text &&\n      item.price_type == 1 &&\n      item.sell_price <= 0 &&\n      item.xunjia_text &&\n      item.price_type == 1\n        ? _vm.t(\"color1\")\n        : null\n    var g1 = item.priceshows && item.priceshows.length > 0\n    var m20 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"color2rgb\")\n        : null\n    var m21 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"color2\")\n        : null\n    var m22 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"佣金\")\n        : null\n    var m23 =\n      _vm.showcart == 1 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m24 =\n      _vm.showcart == 1 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1\")\n        : null\n    var m25 =\n      _vm.showcart == 2 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m26 =\n      _vm.showcart == 2 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1\")\n        : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      g0: g0,\n      l0: l0,\n      m7: m7,\n      m8: m8,\n      m9: m9,\n      m10: m10,\n      m11: m11,\n      m12: m12,\n      m13: m13,\n      m14: m14,\n      m15: m15,\n      m16: m16,\n      m17: m17,\n      m18: m18,\n      m19: m19,\n      g1: g1,\n      m20: m20,\n      m21: m21,\n      m22: m22,\n      m23: m23,\n      m24: m24,\n      m25: m25,\n      m26: m26,\n    }\n  })\n  var m27 = _vm.showLinkStatus && _vm.lx_tel ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        m27: m27,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-itemlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-itemlist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view style=\"width:100%\">\r\n\t<view class=\"dp-product-itemlist\">\r\n\t\t<view class=\"dp-product-item\" v-for=\"(item,index) in data\" :key=\"item.id\">\r\n\t\t<view class=\"item\"  :style=\"{backgroundColor:probgcolor}\" @click=\"toDetail(index)\">\r\n\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t<image class=\"saleimg\" :src=\"saleimg\" v-if=\"saleimg!=''\" mode=\"widthFix\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product-info\">\r\n\t\t\t\t<view class=\"p1\" v-if=\"showname == 1\">{{item.name}}</view>\r\n\t\t\t\t<view class=\"p5\" :style=\"{color:t('color2')}\" v-if=\"item.sellpoint\"><text>{{item.sellpoint}}</text></view>\r\n\t\t\t\t<view :style=\"{color:item.cost_color?item.cost_color:'#999',fontSize:'36rpx'}\" v-if=\"item.show_cost && item.price_type != 1\"><text style=\"font-size: 24rpx;\">{{item.cost_tag}}</text>{{item.cost_price}}</view>\r\n\t\t\t\t<view class=\"p2\" v-if=\"(!item.show_sellprice || (item.show_sellprice && item.show_sellprice==true)) && ( item.price_type != 1 || item.sell_price > 0) && showprice != '0'\">\r\n\t\t\t\t\t<view v-if=\"item.price_show_type=='0' || !item.price_show_type \">\r\n\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:item.price_color?item.price_color:t('color1')}\">\r\n\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{item.usd_sellprice}}\r\n\t\t\t\t\t\t\t\t<text style=\"font-size: 28rpx;\"><text style=\"font-size:24rpx;padding-right:1px\">￥</text>{{item.sell_price}}</text><text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\r\n                <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx;padding-right:1px\">{{item.price_tag?item.price_tag:'￥'}}</text>{{item.sell_price}}\r\n                <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\r\n                <text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text><text v-if=\"!isNull(item.service_fee) && item.service_fee_switch && item.service_fee > 0\" style=\"font-size: 28rpx;\">+{{item.service_fee}}{{t('服务费')}}</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.product_type==2 && item.unit_price && item.unit_price>0\" class=\"t1-m\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t(约{{item.unit_price}}元/斤)\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t<!-- 称重商品 单价 -->\r\n\t\t\t\t\t\t\t\t<view class=\"p6\" v-if=\"item.fwlist && item.fwlist.length>0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"p6-m\" :style=\"'background:rgba('+t('color2rgb')+',0.15);color:'+t('color2')+';'\" v-for=\"(fw,fwidx) in item.fwlist\" :key=\"fwidx\">\r\n\t\t\t\t\t\t\t\t\t\t{{fw}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"item.price_show_type=='1' || item.price_show_type=='2'\">\r\n\t\t\t\t\t\t<view v-if=\"item.is_vip == '0'\">\r\n\r\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{item.usd_sellprice}}\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 28rpx;\">\r\n                    <text style=\"font-size:20rpx;padding-right:1px\">￥</text>\r\n                    <text style=\"font-size: 32rpx;\">{{item.sell_price}}</text>\r\n                    <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:20rpx;padding-right:1px\">￥</text>\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-style: 32rpx;\">{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t<view class=\"member flex\" v-if=\"item.price_show_type=='2' && item.lvprice==1 \">\r\n\t\t\t\t\t\t\t\t<view class=\"member_module flex\" :style=\"'border-color:' + t('color1')\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"!isNull(item.level_name) && !isEmpty(item.level_name)\" :style=\"{background:t('color1')}\" class=\"member_lable flex-y-center\">{{item.level_name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view :style=\"'color:' + t('color1')\" class=\"member_value\">\r\n\t\t\t\t\t\t\t\t\t\t￥<text style=\"font-size: 26rpx ;\">{{item.sell_price_origin}}</text>\r\n                    <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.is_vip == '1'\">\r\n\t\t\t\t\t\t\t<view class=\"member flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"member_module flex\" :style=\"'border-color:' + t('color1')\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"!isNull(item.level_name) && !isEmpty(item.level_name)\" :style=\"{background:t('color1')}\" class=\"member_lable flex-y-center\">{{item.level_name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view :style=\"'color:' + t('color1')\" class=\"member_value\" >\r\n\t\t\t\t\t\t\t\t\t\t￥<text style=\"font-size: 32rpx;\">{{item.sell_price}}<text v-if=\"item.service_fee_switch && item.service_fee > 0\" style=\"font-size: 28rpx;\">+{{item.service_fee}}{{t('服务费')}}</text></text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{item.usd_sellprice}}\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size: 30rpx;\"><text style=\"font-size:20rpx;padding-right:1px\">￥</text>\r\n\t\t\t\t\t\t\t\t\t<text :style=\"item.lvprice =='1'?'font-size:26rpx;':'font-size:32rpx;'\">{{item.sell_price_origin}}</text>\t\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text >\t\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:20rpx;padding-right:1px\" v-if=\"item.sell_price_origin\">￥</text >\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<text :style=\"item.lvprice =='1'?'font-size:26rpx;':'font-size:32rpx;'\"> {{item.sell_price_origin}}</text>\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.show_sellprice && item.market_price*1 > item.sell_price*1 && showprice == '1'\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<text class=\"t3\" v-if=\"item.juli\">{{item.juli}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"p2\" v-if=\"item.xunjia_text && item.price_type == 1 && item.sell_price <= 0\" style=\"height: 50rpx;line-height: 44rpx;\">\r\n\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1'),fontSize:'30rpx'}\">询价</text>\r\n\t\t\t\t\t\t<block v-if=\"item.xunjia_text && item.price_type == 1\">\r\n\t\t\t\t\t\t\t<view class=\"lianxi\" :style=\"{background:t('color1')}\" @tap.stop=\"showLinkChange\" :data-lx_name=\"item.lx_name\" :data-lx_bid=\"item.lx_bid\" :data-lx_bname=\"item.lx_bname\" :data-lx_tel=\"item.lx_tel\" data-btntype=\"2\">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n        <!-- 商品处显示会员价 -->\r\n        <view v-if=\"item.price_show && item.price_show == 1\" style=\"line-height: 46rpx;\">\r\n          <text style=\"font-size:26rpx\">￥{{item.sell_putongprice}}</text>\r\n        </view>\r\n        <view v-if=\"item.priceshows && item.priceshows.length>0\">\r\n          <view v-for=\"(item2,index2) in item.priceshows\" style=\"line-height: 46rpx;\">\r\n            <text style=\"font-size:26rpx\">￥{{item2.sell_price}}</text>\r\n            <text style=\"margin-left: 15rpx;font-size: 22rpx;font-weight: 400;\">{{item2.price_show_text}}</text>\r\n          </view>\r\n        </view>\r\n\t\t\t\t<!-- 是否显示 佣金 S-->\r\n\t\t\t\t<view class=\"couponitem\" v-if=\"showcommission == 1 && item.commission_price>0\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t\" :style=\"{background:'rgba('+t('color2rgb')+',0.1)',color:t('color2')}\">\r\n\t\t\t\t\t\t\t<text>{{t('佣金')}}{{item.commission_price}}{{item.commission_desc}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 是否显示 佣金 E-->\r\n\t\t\t\t<view class=\"p1\" v-if=\"item.merchant_name\" style=\"color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal\"><text>{{item.merchant_name}}</text></view>\r\n\t\t\t\t<view class=\"p1\" v-if=\"item.main_business\" style=\"color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;\"><text>{{item.main_business}}</text></view>\r\n        <view class=\"p3\" v-if=\"item.product_type==3\">\r\n        \t<view class=\"p3-1\"><text style=\"overflow:hidden\">手工费: ￥{{item.hand_fee?item.hand_fee:0}}</text></view>\r\n        </view>\r\n        <view class=\"p3\">\r\n\t\t\t\t\t<view class=\"p3-1\" style=\"flex-grow:1;text-align: left;\" v-if=\"showsales=='1' && item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\r\n\t\t\t\t\t<view class=\"p3-1\" style=\"flex-grow:1;text-align: left;\" v-if=\"showstock=='1' && item.stock>0\"><text style=\"overflow:hidden\">库存{{item.stock}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"(showsales !='1' ||  item.sales<=0) && item.main_business\" style=\"height: 44rpx;\"></view>\r\n\t\t\t\t<view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"showcart==1 && !item.price_type && item.hide_cart!=true\" @click.stop=\"buydialogChange\" :data-proid=\"item[idfield]\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t<view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"showcart==2 && !item.price_type && item.hide_cart!=true\" @click.stop=\"buydialogChange\" :data-proid=\"item[idfield]\"><image :src=\"cartimg\" class=\"img\"/></text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 是否显示商家 距离 S-->\r\n\t\t<view class=\"binfo flex-bt\" v-if=\"(showbname=='1' || showbdistance=='1') && item.binfo\">\r\n\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t<block  v-if=\"showbname=='1'\">\r\n\t\t\t\t\t\t<image :src=\"item.binfo.logo\" class=\"t1\">\r\n\t\t\t\t\t\t<text class=\"t2\">{{item.binfo.name}}</text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"t2\" v-if=\"showbdistance=='1' && item.binfo.distance\">{{item.binfo.distance}}</text>\r\n\t\t</view>\r\n\t\t<!-- 是否显示商家 距离 E-->\r\n\t\t</view>\r\n\t</view>\r\n\t<block v-if=\"productType == 4\">\r\n\t\t<block v-if=\"ggNum == 2\">\r\n\t\t\t<buydialog-pifa v-if=\"buydialogShow\" :proid=\"proid\"  @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" />\r\n\t\t</block>\r\n\t\t<block v-else>\r\n\t\t\t<buydialog-pifa2 v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" />\r\n\t\t</block>\r\n\t</block>\r\n\t<block v-else>\r\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\"></buydialog>\r\n\t</block>\r\n    <view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\r\n    \t<view class=\"main\">\r\n    \t\t<view class=\"close\" @tap=\"showLinkChange\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n    \t\t<view class=\"content\">\r\n    \t\t\t<view class=\"title\">{{lx_name}}</view>\r\n    \t\t\t<view class=\"row\" v-if=\"lx_bid > 0\">\r\n    \t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">店铺名称</view>\r\n    \t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;display: flex;\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+lx_bid\">\r\n              <view style=\"width: 100%;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;\">{{lx_bname}}</view>\r\n              <view style=\"flex: 1;\"></view>\r\n              <image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/>\r\n            </view>\r\n    \t\t\t</view>\r\n    \t\t\t<view class=\"row\" v-if=\"lx_tel\">\r\n    \t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">联系电话</view>\r\n    \t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;\" @tap=\"goto\" :data-url=\"'tel::'+lx_tel\" :style=\"{color:t('color1')}\">{{lx_tel}}<image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"lx_tel\"></image></view>\r\n    \t\t\t</view>\r\n    \t\t</view>\r\n    \t</view>\r\n    </view>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tbuydialogShow:false,\r\n\t\t\t\tproid:0,\r\n        showLinkStatus:false,\r\n        lx_bname:'',\r\n        lx_name:'',\r\n        lx_bid:'',\r\n        lx_tel:'',\r\n\t\t\t\tproductType:'',\r\n\t\t\t\tggNum:'',\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tsaleimg:{default:''},\r\n\t\t\tshowname:{default:1},\r\n\t\t\tnamecolor:{default:'#333'},\r\n\t\t\tshowprice:{default:'1'},\r\n\t\t\tshowcost:{default:'0'},\r\n\t\t\tshowsales:{default:'1'},\r\n\t\t\tshowstock:{default:'0'},\r\n\t\t\tshowcart:{default:'1'},\r\n\t\t\tcartimg:{default:'/static/imgsrc/cart.svg'},\r\n\t\t\tdata:{},\r\n\t\t\tidfield:{default:'id'},\r\n\t\t\tprobgcolor:{default:'#fff'},\r\n\t\t\tshowcommission: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowbname: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowbdistance: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tbuydialogChange: function (e) {\r\n\t\t\t\tif(!this.buydialogShow){\r\n\t\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t\t\tthis.data.forEach(item => {\r\n\t\t\t\t\t\tif(item[this.idfield] == this.proid){\r\n\t\t\t\t\t\t\tthis.productType = item.product_type;\r\n\t\t\t\t\t\t\tif(item.product_type == 4){\r\n\t\t\t\t\t\t\t\tif(item.gg_num){\r\n\t\t\t\t\t\t\t\t\tthis.ggNum = item.gg_num;\r\n\t\t\t\t\t\t\t\t}else if(item.guigedata){\r\n\t\t\t\t\t\t\t\t\tthis.ggNum = Object.keys(JSON.parse(item.guigedata)).length;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t},\r\n            showLinkChange: function (e) {\r\n                var that = this;\r\n            \tthat.showLinkStatus = !that.showLinkStatus;\r\n                that.lx_name = e.currentTarget.dataset.lx_name;\r\n                that.lx_bid = e.currentTarget.dataset.lx_bid;\r\n                that.lx_bname = e.currentTarget.dataset.lx_bname;\r\n                that.lx_tel = e.currentTarget.dataset.lx_tel;\r\n            },\r\n\t\t\ttoDetail:function(key){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar item = that.data[key];\r\n\t\t\t\tvar id = item[that.idfield];\r\n\t\t\t\tvar url = '/pages/shop/product?id='+id;//默认链接\r\n\t\t\t\t//来自商品柜\r\n\t\t\t\tif(item.device_id){\r\n\t\t\t\t\tvar dgid = item.id;\r\n\t\t\t\t\tvar deviceno = item.device_no;\r\n\t\t\t\t\tvar lane = item.goods_lane;\r\n\t\t\t\t\tvar prodata  = id+','+item.ggid+','+item.stock;\r\n\t\t\t\t\tvar devicedata = deviceno+','+lane;\r\n\t\t\t\t\turl = url+'&dgprodata='+prodata+'&devicedata='+devicedata;\r\n\t\t\t\t}\r\n\t\t\t\tapp.goto(url);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-product-item{margin-bottom: 12rpx;padding:20rpx;width: 100%;border-bottom: 1rpx solid #f6f6f6;}\r\n.dp-product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.dp-product-itemlist .item{width:100%;display: inline-block;position: relative;background: #fff;display:flex;border-radius:10rpx;align-items: center;}\r\n.dp-product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.dp-product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.dp-product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\r\n.dp-product-itemlist .product-info {width: 70%;padding:6rpx 10rpx 5rpx 20rpx;position: relative;}\r\n.dp-product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.dp-product-itemlist .product-info .p2{margin-top:10rpx;overflow:hidden;}\r\n.dp-product-itemlist .product-info .p2 .t1{font-size:36rpx;}\r\n.dp-product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.dp-product-itemlist .product-info .p2 .t3 {margin-left:10rpx;font-size:24rpx;color: #888;}\r\n.dp-product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\r\n.dp-product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}\r\n.dp-product-itemlist .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\r\n.dp-product-itemlist .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}\r\n.dp-product-itemlist .product-info .p4 .img{width:100%;height:100%}\r\n.dp-product-itemlist .product-info .p2 .t1-m {font-size: 32rpx;padding-left: 8rpx;}\r\n.dp-product-itemlist .product-info .p5{font-size:24rpx;font-weight: bold;margin: 6rpx 0;}\r\n.dp-product-itemlist .product-info .p6{font-size:24rpx;display: flex;flex-wrap: wrap;margin-top: 6rpx;}\r\n.dp-product-itemlist .product-info .p6-m{text-align: center;padding:6rpx 10rpx;border-radius: 6rpx;margin: 6rpx;}\r\n.dp-product-itemlist .binfo {\r\n\t\tpadding-top:6rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.dp-product-itemlist .binfo .t1 {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.dp-product-itemlist .binfo .t2 {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: normal;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.dp-product-itemlist .couponitem {\r\n\t\twidth: 100%;\r\n\t\t/* padding: 0 20rpx 20rpx 20rpx; */\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.dp-product-itemlist .couponitem .f1 {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.dp-product-itemlist .couponitem .f1 .t {\r\n\t\tmargin-right: 10rpx;\r\n\t\tborder-radius: 3px;\r\n\t\tfont-size: 22rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tpadding-right: 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\r\n.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}\r\n\r\n.member{padding: 5rpx 0;}\r\n.member_module{position: relative;border-radius: 8rpx;border: 1rpx solid #fd4a46;overflow: hidden;box-sizing: content-box;}\r\n.member_lable{height: 100%;font-size: 22rpx;color: #fff;background: #fd4a46;padding: 0 15rpx;}\r\n.member_value{padding: 0 15rpx;font-size: 20rpx;color: #fd4a46;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-itemlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-itemlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839380479\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}