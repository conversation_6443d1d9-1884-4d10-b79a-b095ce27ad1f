{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?7998", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?0e5e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?6fdf", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?a8a6", "uni-app:///components/ksp-cropper/ksp-cropper.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?3d6b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?8c56", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?0f3d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/ksp-cropper/ksp-cropper.vue?afda"], "names": ["props", "mode", "type", "default", "url", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "data", "canvasId", "real", "target", "body", "frame", "left", "top", "image", "rotate", "transit", "modeValue", "methods", "imageLoad", "query", "init", "rate", "updateData", "trimImage", "setTimeout", "rotateAngle", "onok", "oncancle", "cropWx", "mx", "uni", "in", "select", "fields", "node", "exec", "resolve", "canvas", "title", "context", "wx", "destWidth", "destHeight", "fileType", "success", "path", "complete", "cropAppH5", "computeMatrix", "mul", "sw", "sh", "angle", "ox", "oy", "sx", "sy", "x", "y", "w", "h", "dr", "tw", "th", "dx", "dy", "dw", "dh", "parsePoint", "result", "parseRect", "parseBlob", "u8arr"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0iW;AAC1iW;AAC+D;AACL;AACqC;;;AAG/F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,wgWAAM;AACR,EAAE,ihWAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4gWAAU;AACZ;AACA;;AAEA;AACmG;AACnG,WAAW,qHAAM,iBAAiB,6HAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuC71B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,eAWA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;MACAC;QACAN;QACAC;MACA;MACAM;QACAP;QACAC;MACA;MACAO;QACAR;QACAC;MACA;MACAQ;QACAC;QACAC;QACAX;QACAC;MACA;MACAW;QACAF;QACAC;QACAX;QACAC;MACA;MACAY;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAnB;MACA;QACAC;MACA;MACA;MACA;MACA;QACAS;QACAC;QACAX;QACAC;MACA;MACAmB;MACApB;MACAC;MACA;QACAA;MACA;QACAD;MACA;MACAU;MACAC;MACA;QACAD;QACAC;QACAX;QACAC;MACA;IACA;IACAoB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACAtB;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAS;QACAC;QACAX;QACAC;MACA;MACAD;MACAC;MACAS;MACAC;MACA;QACAD;QACAC;QACAX;QACAC;MACA;MACA;QACA;QACAsB;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAxB;QACAC;MACA;MACA;QACAA;QACAD;MACA;MACA;QACAU;MACA;MACA;QACAC;MACA;MACA;QACAD;MACA;MACA;QACAC;MACA;MACA;QACAD;QACAC;QACAX;QACAC;MACA;MACA;MACAsB;QACA;MACA;IACA;IACAE;MAEA;IAKA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;kBACA5B;kBACAC;gBACA;gBAAA;gBAAA,OACA;kBACA4B,0BACAC,WACAC,kBACAC;oBAAAC;kBAAA,GACAC;oBACA;oBACAC;kBACA;gBACA;cAAA;gBATAC;gBAUAA;gBACAA;gBACAP;kBACAQ;gBACA;gBAAA;gBAAA,OACA;kBACAd;gBACA;cAAA;gBACAe;gBACA1B;gBAAA;gBAAA,OACA;kBACAA;kBACAA;kBACAA;gBACA;cAAA;gBACA0B;gBACAA;gBACAA;gBACAA;gBACAC;kBACAH;kBACAI;kBACAC;kBACAC;kBACAC;oBACA;oBACA;sBACAC;oBACA;kBACA;kBACAC;oBACAhB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAlB;gBACA;kBACA5B;kBACAC;gBACA;gBACA4B;kBACAQ;gBACA;gBAAA;gBAAA,OACA;kBACAd;gBACA;cAAA;gBACAe;gBACAA;gBACAA;gBACAA;gBACAA;gBAAA;gBAAA,OACA;kBACAA;gBACA;cAAA;gBACAT;kBACAxB;kBACAmC;kBACAC;kBACAC;kBACAC;oBACA;kBAcA;kBACAE;oBACAhB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAkB;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAhD;QACAC;MACA;MACA;MACA;QACAD;QACAC;MACA;MACA;QACAA;QACAD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACAiD;QACAC;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;QACA;QACAC;QACAC;MACA;MACA;QACA;QACA;QACAD;QACAC;MACA;MACA;QACA;QACA;QACAD;QACAC;MACA;MACAC;MACAC;MACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACA;QACAC;QACAC;QACAR;QACAC;QACAN;QACAC;QACAa;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACAC;MACAA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QAAAb;QAAAC;MAAA;MACA;QAAAD;QAAAC;MAAA;MACA;MACAW;MACAA;MACAA;MACAA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;QAAA1E;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjcA;AAAA;AAAA;AAAA;AAA8sC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACAluC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAolB,CAAgB,kmBAAG,EAAC,C;;;;;;;;;;;;ACAxmB;AAAe;AACf;AACA;AACA;AACA;AACA,M", "file": "components/ksp-cropper/ksp-cropper.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ksp-cropper.vue?vue&type=template&id=58c8ee16&scoped=true&filter-modules=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%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%2BIGZyYW1lLnRvcCkge1xuXHRcdGltYWdlLnRvcCA9IGZyYW1lLnRvcDtcblx0fVxuXHRpZiAoaW1hZ2UubGVmdCArIGltYWdlLndpZHRoIDwgZnJhbWUubGVmdCArIGZyYW1lLndpZHRoKSB7XG5cdFx0aW1hZ2UubGVmdCA9IGZyYW1lLmxlZnQgKyBmcmFtZS53aWR0aCAtIGltYWdlLndpZHRoOyBcblx0fVxuXHRpZiAoaW1hZ2UudG9wICsgaW1hZ2UuaGVpZ2h0IDwgZnJhbWUudG9wICsgZnJhbWUuaGVpZ2h0KSB7XG5cdFx0aW1hZ2UudG9wID0gZnJhbWUudG9wICsgZnJhbWUuaGVpZ2h0IC0gaW1hZ2UuaGVpZ2h0OyBcblx0fVxuXHR1cGRhdGVTdHlsZShvaSk7XG59XG5mdW5jdGlvbiBzY2FsZUZyYW1lKHRhLCB0Yiwgb2kpIHtcblx0dmFyIGF4ID0gdGIuY2xpZW50WCAtIHRhLmNsaWVudFg7XG5cdHZhciBheSA9IHRiLmNsaWVudFkgLSB0YS5jbGllbnRZO1xuXHR2YXIgeDEgPSBzdGFydC5mcmFtZS5sZWZ0O1xuXHR2YXIgeTEgPSBzdGFydC5mcmFtZS50b3A7XG5cdHZhciB4MiA9IHN0YXJ0LmZyYW1lLmxlZnQgKyBzdGFydC5mcmFtZS53aWR0aDtcblx0dmFyIHkyID0gc3RhcnQuZnJhbWUudG9wICsgc3RhcnQuZnJhbWUuaGVpZ2h0O1xuXHR2YXIgY3gxID0gZmFsc2U7XG5cdHZhciBjeTEgPSBmYWxzZTtcblx0dmFyIGN4MiA9IGZhbHNlO1xuXHR2YXIgY3kyID0gZmFsc2U7XG5cdHZhciBtaXggPSAzMDtcblx0dmFyIHJhdGUgPSBmcmFtZS53aWR0aCAvIGZyYW1lLmhlaWdodDtcblx0aWYgKHRvdWNoVHlwZSA9PSBcImxlZnQtdG9wXCIpIHtcblx0XHR4MSArPSBheDtcblx0XHR5MSArPSBheTtcblx0XHRjeDEgPSB0cnVlO1xuXHRcdGN5MSA9IHRydWU7XG5cdH0gZWxzZSBpZiAodG91Y2hUeXBlID09IFwibGVmdC1ib3R0b21cIikge1xuXHRcdHgxICs9IGF4O1xuXHRcdHkyICs9IGF5O1xuXHRcdGN4MSA9IHRydWU7XG5cdFx0Y3kyID0gdHJ1ZTtcblx0fSBlbHNlIGlmICh0b3VjaFR5cGUgPT0gXCJyaWdodC10b3BcIikge1xuXHRcdHgyICs9IGF4O1xuXHRcdHkxICs9IGF5O1xuXHRcdGN4MiA9IHRydWU7XG5cdFx0Y3kxID0gdHJ1ZTtcblx0fSBlbHNlIGlmICh0b3VjaFR5cGUgPT0gXCJyaWdodC1ib3R0b21cIikge1xuXHRcdHgyICs9IGF4O1xuXHRcdHkyICs9IGF5O1xuXHRcdGN4MiA9IHRydWU7XG5cdFx0Y3kyID0gdHJ1ZTtcblx0fVxuXHRpZiAoeDEgPCBpbWFnZS5sZWZ0KSB7XG5cdFx0eDEgPSBpbWFnZS5sZWZ0O1xuXHR9XG5cdGlmICh5MSA8IGltYWdlLnRvcCkge1xuXHRcdHkxID0gaW1hZ2UudG9wO1xuXHR9XG5cdGlmICh4MiA%2BIGltYWdlLmxlZnQgKyBpbWFnZS53aWR0aCkge1xuXHRcdHgyID0gaW1hZ2UubGVmdCArIGltYWdlLndpZHRoO1xuXHR9XG5cdGlmICh5MiA%2BIGltYWdlLnRvcCArIGltYWdlLmhlaWdodCkge1xuXHRcdHkyID0gaW1hZ2UudG9wICsgaW1hZ2UuaGVpZ2h0O1xuXHR9XG5cdGlmIChjeDEpIHtcblx0XHRpZiAoeDEgPiB4MiAtIG1peCkge1xuXHRcdFx0eDEgPSB4MiAtIG1peDtcblx0XHR9XG5cdH1cblx0aWYgKGN5MSkge1xuXHRcdGlmICh5MSA%2BIHkyIC0gbWl4KSB7XG5cdFx0XHR5MSA9IHkyIC0gbWl4O1xuXHRcdH1cblx0fVxuXHRpZiAoY3gyKSB7XG5cdFx0aWYgKHgyIDwgeDEgKyBtaXgpIHtcblx0XHRcdHgyID0geDEgKyBtaXg7XG5cdFx0fVxuXHR9XG5cdGlmIChjeTIpIHtcblx0XHRpZiAoeTIgPCB5MSArIG1peCkge1xuXHRcdFx0eTIgPSB5MSArIG1peDtcblx0XHR9XG5cdH1cblx0aWYgKGN4MSkge1xuXHRcdGlmIChtb2RlICE9IFwiZnJlZVwiKSB7XG5cdFx0XHR2YXIgdmFsID0geDIgLSByYXRlICogKHkyIC0geTEpO1xuXHRcdFx0aWYgKHgxIDwgdmFsKSB7XG5cdFx0XHRcdHgxID0gdmFsO1xuXHRcdFx0fVxuXHRcdH1cblx0fVxuXHRpZiAoY3kxKSB7XG5cdFx0aWYgKG1vZGUgIT0gXCJmcmVlXCIpIHtcblx0XHRcdHZhciB2YWwgPSB5MiAtICh4MiAtIHgxKSAvIHJhdGU7XG5cdFx0XHRpZiAoeTEgPCB2YWwpIHtcblx0XHRcdFx0eTEgPSB2YWw7XG5cdFx0XHR9XG5cdFx0fVxuXHR9XG5cdGlmIChjeDIpIHtcblx0XHRpZiAobW9kZSAhPSBcImZyZWVcIikge1xuXHRcdFx0dmFyIHZhbCA9IHJhdGUgKiAoeTIgLSB5MSkgKyB4MTtcblx0XHRcdGlmICh4MiA%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%3D&\"\nvar renderjs\nimport script from \"./ksp-cropper.vue?vue&type=script&lang=js&\"\nexport * from \"./ksp-cropper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ksp-cropper.vue?vue&type=style&index=0&id=58c8ee16&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"58c8ee16\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./ksp-cropper.vue?vue&type=custom&index=0&blockType=script&module=mwx&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/ksp-cropper/ksp-cropper.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ksp-cropper.vue?vue&type=template&id=58c8ee16&scoped=true&filter-modules=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%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%2BIGZyYW1lLnRvcCkge1xuXHRcdGltYWdlLnRvcCA9IGZyYW1lLnRvcDtcblx0fVxuXHRpZiAoaW1hZ2UubGVmdCArIGltYWdlLndpZHRoIDwgZnJhbWUubGVmdCArIGZyYW1lLndpZHRoKSB7XG5cdFx0aW1hZ2UubGVmdCA9IGZyYW1lLmxlZnQgKyBmcmFtZS53aWR0aCAtIGltYWdlLndpZHRoOyBcblx0fVxuXHRpZiAoaW1hZ2UudG9wICsgaW1hZ2UuaGVpZ2h0IDwgZnJhbWUudG9wICsgZnJhbWUuaGVpZ2h0KSB7XG5cdFx0aW1hZ2UudG9wID0gZnJhbWUudG9wICsgZnJhbWUuaGVpZ2h0IC0gaW1hZ2UuaGVpZ2h0OyBcblx0fVxuXHR1cGRhdGVTdHlsZShvaSk7XG59XG5mdW5jdGlvbiBzY2FsZUZyYW1lKHRhLCB0Yiwgb2kpIHtcblx0dmFyIGF4ID0gdGIuY2xpZW50WCAtIHRhLmNsaWVudFg7XG5cdHZhciBheSA9IHRiLmNsaWVudFkgLSB0YS5jbGllbnRZO1xuXHR2YXIgeDEgPSBzdGFydC5mcmFtZS5sZWZ0O1xuXHR2YXIgeTEgPSBzdGFydC5mcmFtZS50b3A7XG5cdHZhciB4MiA9IHN0YXJ0LmZyYW1lLmxlZnQgKyBzdGFydC5mcmFtZS53aWR0aDtcblx0dmFyIHkyID0gc3RhcnQuZnJhbWUudG9wICsgc3RhcnQuZnJhbWUuaGVpZ2h0O1xuXHR2YXIgY3gxID0gZmFsc2U7XG5cdHZhciBjeTEgPSBmYWxzZTtcblx0dmFyIGN4MiA9IGZhbHNlO1xuXHR2YXIgY3kyID0gZmFsc2U7XG5cdHZhciBtaXggPSAzMDtcblx0dmFyIHJhdGUgPSBmcmFtZS53aWR0aCAvIGZyYW1lLmhlaWdodDtcblx0aWYgKHRvdWNoVHlwZSA9PSBcImxlZnQtdG9wXCIpIHtcblx0XHR4MSArPSBheDtcblx0XHR5MSArPSBheTtcblx0XHRjeDEgPSB0cnVlO1xuXHRcdGN5MSA9IHRydWU7XG5cdH0gZWxzZSBpZiAodG91Y2hUeXBlID09IFwibGVmdC1ib3R0b21cIikge1xuXHRcdHgxICs9IGF4O1xuXHRcdHkyICs9IGF5O1xuXHRcdGN4MSA9IHRydWU7XG5cdFx0Y3kyID0gdHJ1ZTtcblx0fSBlbHNlIGlmICh0b3VjaFR5cGUgPT0gXCJyaWdodC10b3BcIikge1xuXHRcdHgyICs9IGF4O1xuXHRcdHkxICs9IGF5O1xuXHRcdGN4MiA9IHRydWU7XG5cdFx0Y3kxID0gdHJ1ZTtcblx0fSBlbHNlIGlmICh0b3VjaFR5cGUgPT0gXCJyaWdodC1ib3R0b21cIikge1xuXHRcdHgyICs9IGF4O1xuXHRcdHkyICs9IGF5O1xuXHRcdGN4MiA9IHRydWU7XG5cdFx0Y3kyID0gdHJ1ZTtcblx0fVxuXHRpZiAoeDEgPCBpbWFnZS5sZWZ0KSB7XG5cdFx0eDEgPSBpbWFnZS5sZWZ0O1xuXHR9XG5cdGlmICh5MSA8IGltYWdlLnRvcCkge1xuXHRcdHkxID0gaW1hZ2UudG9wO1xuXHR9XG5cdGlmICh4MiA%2BIGltYWdlLmxlZnQgKyBpbWFnZS53aWR0aCkge1xuXHRcdHgyID0gaW1hZ2UubGVmdCArIGltYWdlLndpZHRoO1xuXHR9XG5cdGlmICh5MiA%2BIGltYWdlLnRvcCArIGltYWdlLmhlaWdodCkge1xuXHRcdHkyID0gaW1hZ2UudG9wICsgaW1hZ2UuaGVpZ2h0O1xuXHR9XG5cdGlmIChjeDEpIHtcblx0XHRpZiAoeDEgPiB4MiAtIG1peCkge1xuXHRcdFx0eDEgPSB4MiAtIG1peDtcblx0XHR9XG5cdH1cblx0aWYgKGN5MSkge1xuXHRcdGlmICh5MSA%2BIHkyIC0gbWl4KSB7XG5cdFx0XHR5MSA9IHkyIC0gbWl4O1xuXHRcdH1cblx0fVxuXHRpZiAoY3gyKSB7XG5cdFx0aWYgKHgyIDwgeDEgKyBtaXgpIHtcblx0XHRcdHgyID0geDEgKyBtaXg7XG5cdFx0fVxuXHR9XG5cdGlmIChjeTIpIHtcblx0XHRpZiAoeTIgPCB5MSArIG1peCkge1xuXHRcdFx0eTIgPSB5MSArIG1peDtcblx0XHR9XG5cdH1cblx0aWYgKGN4MSkge1xuXHRcdGlmIChtb2RlICE9IFwiZnJlZVwiKSB7XG5cdFx0XHR2YXIgdmFsID0geDIgLSByYXRlICogKHkyIC0geTEpO1xuXHRcdFx0aWYgKHgxIDwgdmFsKSB7XG5cdFx0XHRcdHgxID0gdmFsO1xuXHRcdFx0fVxuXHRcdH1cblx0fVxuXHRpZiAoY3kxKSB7XG5cdFx0aWYgKG1vZGUgIT0gXCJmcmVlXCIpIHtcblx0XHRcdHZhciB2YWwgPSB5MiAtICh4MiAtIHgxKSAvIHJhdGU7XG5cdFx0XHRpZiAoeTEgPCB2YWwpIHtcblx0XHRcdFx0eTEgPSB2YWw7XG5cdFx0XHR9XG5cdFx0fVxuXHR9XG5cdGlmIChjeDIpIHtcblx0XHRpZiAobW9kZSAhPSBcImZyZWVcIikge1xuXHRcdFx0dmFyIHZhbCA9IHJhdGUgKiAoeTIgLSB5MSkgKyB4MTtcblx0XHRcdGlmICh4MiA%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%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ksp-cropper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ksp-cropper.vue?vue&type=script&lang=js&\"", "<template>\r\n<view v-show=\"url\" :mode=\"modeValue\" :change:mode=\"mwx.changeMode\" :rotate=\"rotate\" :change:rotate=\"mwx.changeRotate\">\r\n\t<!-- #ifdef MP-WEIXIN -->\r\n\t<canvas type=\"2d\" class=\"canvas\" :style=\"{width: target.width + 'px', height: target.height + 'px'}\"></canvas>\r\n\t<!-- #endif -->\r\n\t<!-- #ifdef APP-PLUS || H5 -->\r\n\t<canvas :canvas-id=\"canvasId\" :style=\"{width: target.width + 'px', height: target.height + 'px'}\"></canvas>\r\n\t<!-- #endif -->\r\n\t<view class=\"panel\">\r\n\t\t<view class=\"body\" @touchstart=\"mwx.touchstart\" @touchmove=\"mwx.touchmove\" @touchend=\"mwx.touchend\" @touchcancel=\"mwx.touchcancel\">\r\n\t\t\t<view class=\"image-wrap\" :class=\"{transit: transit}\" :change:rect=\"mwx.changeImage\" :rect=\"image\">\r\n\t\t\t\t<image class=\"image\" :class=\"{transit: transit}\" :src=\"url\" @load=\"imageLoad\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"mask\"></view>\r\n\t\t\t<view class=\"frame\" :class=\"{transit: transit}\" :change:rect=\"mwx.changeFrame\" :rect=\"frame\">\r\n\t\t\t\t<view class=\"rect\">\r\n\t\t\t\t\t<view class=\"image-rect\" :class=\"{transit: transit}\">\r\n\t\t\t\t\t\t<image class=\"image\" :class=\"{transit: transit}\" :src=\"url\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"line-one\"></view>\r\n\t\t\t\t<view class=\"line-two\"></view>\r\n\t\t\t\t<view class=\"line-three\"></view>\r\n\t\t\t\t<view class=\"line-four\"></view>\r\n\t\t\t\t<view class=\"frame-left-top\" @touchstart=\"mwx.touchstart\"></view>\r\n\t\t\t\t<view class=\"frame-left-bottom\" @touchstart=\"mwx.touchstart\"></view>\r\n\t\t\t\t<view class=\"frame-right-top\" @touchstart=\"mwx.touchstart\"></view>\r\n\t\t\t\t<view class=\"frame-right-bottom\" @touchstart=\"mwx.touchstart\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"toolbar\">\r\n\t\t\t<view @tap=\"oncancle\" class=\"btn-cancel\">取消</view>\r\n\t\t\t<view @tap=\"rotateAngle\" class=\"btn-rotate\">旋转</view>\r\n\t\t\t<view @tap=\"onok\" class=\"btn-ok\">确定</view>\r\n\t\t</view>\n\t</view>\r\n</view>\n</template>\n<script>\r\n/**\r\n * @property {String} mode 模式\r\n *  @value fixed 固定模式，裁剪出固定大小\r\n *  @value ratio 等比模式，宽高等比缩放\r\n *  @value free 自由模式，不限制宽高比\r\n * @property {String} url 图片路径\r\n * @property {Number} width 宽度\r\n * @property {Number} height 高度\r\n * @property {Number} maxWidth 最大宽带\r\n * @property {Number} minHeight 最大高度 \r\n */\r\nexport default {\r\n\tprops: {\r\n\t\tmode: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"free\"\r\n\t\t},\r\n\t\turl: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"\"\r\n\t\t},\r\n\t\twidth: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 200\r\n\t\t},\r\n\t\theight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 200\r\n\t\t},\r\n\t\tmaxWidth: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 1024\r\n\t\t},\r\n\t\tmaxHeight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 1024\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcanvasId: Math.random().toString(36).slice(-6),\r\n\t\t\treal: {\r\n\t\t\t\twidth: 100,\r\n\t\t\t\theight: 100\r\n\t\t\t},\r\n\t\t\ttarget: {\r\n\t\t\t\twidth: 100,\r\n\t\t\t\theight: 100\r\n\t\t\t},\r\n\t\t\tbody: {\r\n\t\t\t\twidth: 100,\r\n\t\t\t\theight: 100\r\n\t\t\t},\r\n\t\t\tframe: {\r\n\t\t\t\tleft: 50,\r\n\t\t\t\ttop: 50,\r\n\t\t\t\twidth: 200,\r\n\t\t\t\theight: 300\r\n\t\t\t},\r\n\t\t\timage: {\r\n\t\t\t\tleft: 20,\r\n\t\t\t\ttop: 20,\r\n\t\t\t\twidth: 300,\r\n\t\t\t\theight: 400\r\n\t\t\t},\r\n\t\t\trotate: 0,\r\n\t\t\ttransit: false,\r\n\t\t\tmodeValue: \"\"\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\timageLoad(event) {\r\n\t\t\tthis.real.width = event.detail.width;\r\n\t\t\tthis.real.height = event.detail.height;\r\n\t\t\tthis.target = {};\r\n\t\t\tvar query = uni.createSelectorQuery().in(this);\r\n\t\t\tquery.select(\".body\").boundingClientRect((data) => {\r\n\t\t\t\tthis.body.width = data.width;\r\n\t\t\t\tthis.body.height = data.height;\r\n\t\t\t\tthis.init();\r\n\t\t\t}).exec();\r\n\t\t},\r\n\t\tinit() {\r\n\t\t\tthis.modeValue = this.mode;\r\n\t\t\tthis.rotate = 0;\r\n\t\t\tvar rate = this.width / this.height;\r\n\t\t\tvar width = this.body.width * 0.7;\r\n\t\t\tvar height = this.body.height * 0.7;\r\n\t\t\tif (width / height > rate) {\r\n\t\t\t\twidth = height * rate;\r\n\t\t\t} else {\r\n\t\t\t\theight = width / rate;\r\n\t\t\t}\r\n\t\t\tvar left = (this.body.width - width) / 2;\r\n\t\t\tvar top = (this.body.height - height) / 2;\r\n\t\t\tthis.frame = {\r\n\t\t\t\tleft: left,\r\n\t\t\t\ttop: top,\r\n\t\t\t\twidth: width,\r\n\t\t\t\theight: height\r\n\t\t\t};\r\n\t\t\trate = this.real.width / this.real.height;\r\n\t\t\twidth = this.frame.width;\r\n\t\t\theight = this.frame.height;\r\n\t\t\tif (width / height > rate) {\r\n\t\t\t\theight = width / rate;\r\n\t\t\t} else {\r\n\t\t\t\twidth = height * rate;\r\n\t\t\t}\r\n\t\t\tleft = (this.frame.width - width) / 2 + this.frame.left;\r\n\t\t\ttop = (this.frame.height - height) / 2 + this.frame.top;\r\n\t\t\tthis.image = {\r\n\t\t\t\tleft: left,\r\n\t\t\t\ttop: top,\r\n\t\t\t\twidth: width,\r\n\t\t\t\theight: height\r\n\t\t\t};\r\n\t\t},\r\n\t\tasync updateData(data) {\r\n\t\t\tthis.frame = data.frame;\r\n\t\t\tthis.image = data.image;\r\n\t\t\tawait this.$nextTick();\r\n\t\t\tthis.trimImage();\r\n\t\t},\r\n\t\ttrimImage() {\r\n\t\t\tvar rate = this.frame.width / this.frame.height;\r\n\t\t\tvar width = this.body.width * 0.7;\r\n\t\t\tvar height = this.body.height * 0.7;\r\n\t\t\tif (width / height > rate) {\r\n\t\t\t\twidth = height * rate;\r\n\t\t\t} else {\r\n\t\t\t\theight = width / rate;\r\n\t\t\t}\r\n\t\t\tvar left = (this.body.width - width) / 2;\r\n\t\t\tvar top = (this.body.height - height) / 2;\r\n\t\t\tvar mul = width / this.frame.width;\r\n\t\t\tvar ox = this.frame.left - this.image.left;\r\n\t\t\tvar oy = this.frame.top - this.image.top;\r\n\t\t\tthis.frame = {\r\n\t\t\t\tleft: left,\r\n\t\t\t\ttop: top,\r\n\t\t\t\twidth: width,\r\n\t\t\t\theight: height\r\n\t\t\t};\r\n\t\t\twidth = this.image.width * mul;\r\n\t\t\theight = this.image.height * mul;\r\n\t\t\tleft = this.frame.left - ox * mul;\r\n\t\t\ttop = this.frame.top - oy * mul;\r\n\t\t\tthis.image = {\r\n\t\t\t\tleft: left,\r\n\t\t\t\ttop: top,\r\n\t\t\t\twidth: width,\r\n\t\t\t\theight: height\r\n\t\t\t};\r\n\t\t\tif (mul != 1) {\r\n\t\t\t\tthis.transit = true;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.transit = false;\r\n\t\t\t\t}, 300);\r\n\t\t\t}\r\n\t\t},\r\n\t\trotateAngle() {\r\n\t\t\tthis.rotate -= 90;\r\n\t\t\tvar width = this.image.height;\r\n\t\t\tvar height = this.image.width;\r\n\t\t\tvar left = this.image.left;\r\n\t\t\tvar top = this.image.top;\r\n\t\t\tvar rate = width / height;\r\n\t\t\tif (width < this.frame.width) {\r\n\t\t\t\twidth = this.frame.width;\r\n\t\t\t\theight = width / rate;\r\n\t\t\t}\r\n\t\t\tif (height < this.frame.height) {\r\n\t\t\t\theight = this.frame.height;\r\n\t\t\t\twidth = height * rate;\r\n\t\t\t}\r\n\t\t\tif (left > this.frame.left) {\r\n\t\t\t\tleft = this.frame.left;\r\n\t\t\t}\r\n\t\t\tif (top > this.frame.top) {\r\n\t\t\t\ttop = this.frame.top;\r\n\t\t\t}\r\n\t\t\tif (left + width < this.frame.left + this.frame.width) {\r\n\t\t\t\tleft = this.frame.left + this.frame.width - width; \r\n\t\t\t}\r\n\t\t\tif (top + height < this.frame.top + this.frame.height) {\r\n\t\t\t\ttop = this.frame.top + this.frame.height - height; \r\n\t\t\t}\r\n\t\t\tthis.image = {\r\n\t\t\t\tleft: left,\r\n\t\t\t\ttop: top,\r\n\t\t\t\twidth: width,\r\n\t\t\t\theight: height\r\n\t\t\t};\r\n\t\t\tthis.transit = true;\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.transit = false;\r\n\t\t\t}, 300);\r\n\t\t},\r\n\t\tonok() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.cropWx();\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS || H5\r\n\t\t\tthis.cropAppH5();\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\toncancle() {\r\n\t\t\tthis.$emit(\"cancel\");\r\n\t\t},\r\n\t\tasync cropWx() {\r\n\t\t\tvar mx = this.computeMatrix();\r\n\t\t\tthis.target = {\r\n\t\t\t\twidth: mx.tw,\r\n\t\t\t\theight: mx.th\r\n\t\t\t};\r\n\t\t\tvar canvas = await new Promise((resolve) => {\r\n\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t.in(this)\r\n\t\t\t\t.select(\".canvas\")\r\n\t\t\t\t.fields({node: true})\r\n\t\t\t\t.exec((rst) => {\r\n\t\t\t\t\tvar node = rst[0].node;\r\n\t\t\t\t\tresolve(node);\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\tcanvas.width = mx.tw;\r\n\t\t\tcanvas.height = mx.th;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: \"处理中\"\r\n\t\t\t});\r\n\t\t\tawait new Promise((resolve) => {\r\n\t\t\t\tsetTimeout(resolve, 200);\r\n\t\t\t});\r\n\t\t\tvar context = canvas.getContext(\"2d\");\r\n\t\t\tvar image = canvas.createImage();\r\n\t\t\tawait new Promise((resolve, reject) => {\r\n\t\t\t\timage.onload = resolve;\r\n\t\t\t\timage.onerror = reject;\r\n\t\t\t\timage.src = this.url;\r\n\t\t\t});\r\n\t\t\tcontext.save();\r\n\t\t\tcontext.rotate(this.rotate * Math.PI / 180);\r\n\t\t\tcontext.drawImage(image, mx.sx, mx.sy, mx.sw, mx.sh, mx.dx, mx.dy, mx.dw, mx.dh);\r\n\t\t\tcontext.restore();\r\n\t\t\twx.canvasToTempFilePath({\r\n\t\t\t\tcanvas: canvas,\r\n\t\t\t\tdestWidth: mx.tw,\r\n\t\t\t\tdestHeight: mx.th,\r\n\t\t\t\tfileType:'png',\r\n\t\t\t\tsuccess: (rst) => {\r\n\t\t\t\t\tvar path = rst.tempFilePath;\r\n\t\t\t\t\tthis.$emit(\"ok\", {\r\n\t\t\t\t\t\tpath: path\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tcomplete: () => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tasync cropAppH5() {\r\n\t\t\tvar mx = this.computeMatrix();\r\n\t\t\tthis.target = {\r\n\t\t\t\twidth: mx.tw,\r\n\t\t\t\theight: mx.th\r\n\t\t\t};\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: \"处理中\"\r\n\t\t\t});\r\n\t\t\tawait new Promise((resolve) => {\r\n\t\t\t\tsetTimeout(resolve, 200);\r\n\t\t\t});\r\n\t\t\tvar context = uni.createCanvasContext(this.canvasId, this);\r\n\t\t\tcontext.save();\r\n\t\t\tcontext.rotate(this.rotate * Math.PI / 180);\r\n\t\t\tcontext.drawImage(this.url, mx.sx, mx.sy, mx.sw, mx.sh, mx.dx, mx.dy, mx.dw, mx.dh);\r\n\t\t\tcontext.restore();\r\n\t\t\tawait new Promise((resolve) => {\r\n\t\t\t\tcontext.draw(false, resolve);\r\n\t\t\t});\r\n\t\t\tuni.canvasToTempFilePath({ \r\n\t\t\t\tcanvasId: this.canvasId,\r\n\t\t\t\tdestWidth: mx.tw,\r\n\t\t\t\tdestHeight: mx.th,\r\n\t\t\t\tfileType:'png',\r\n\t\t\t\tsuccess: (rst) => {\r\n\t\t\t\t\tvar path = rst.tempFilePath;\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tvar base64 = path;\r\n\t\t\t\t\tpath = this.parseBlob(path);\r\n\t\t\t\t\tthis.$emit(\"ok\", {\r\n\t\t\t\t\t\tpath: path,\r\n\t\t\t\t\t\tbase64: base64\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tthis.$emit(\"ok\", {\r\n\t\t\t\t\t\tpath: path\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t},\r\n\t\t\t\tcomplete: () => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t}, this);\r\n\t\t},\r\n\t\tcomputeMatrix() {\r\n\t\t\tvar width = this.width;\r\n\t\t\tvar height = this.height;\r\n\t\t\tvar mul = this.image.width / this.real.width;\r\n\t\t\tif (this.rotate % 180 != 0) {\r\n\t\t\t\tmul = this.image.height / this.real.width;\r\n\t\t\t}\r\n\t\t\tif (this.mode != \"fixed\") {\r\n\t\t\t\twidth = this.frame.width / mul;\r\n\t\t\t\theight = this.frame.height / mul;\r\n\t\t\t}\r\n\t\t\tvar rate = width / height;\r\n\t\t\tif (width > this.maxWidth) {\r\n\t\t\t\twidth = this.maxWidth;\r\n\t\t\t\theight = width / rate;\r\n\t\t\t}\r\n\t\t\tif (height > this.maxHeight) {\r\n\t\t\t\theight = this.maxHeight;\r\n\t\t\t\twidth = height * rate;\r\n\t\t\t}\r\n\t\t\tvar sx = (this.frame.left - this.image.left) / mul;\r\n\t\t\tvar sy = (this.frame.top - this.image.top) / mul;\r\n\t\t\tvar sw = this.frame.width / mul;\r\n\t\t\tvar sh = this.frame.height / mul;\r\n\t\t\tvar ox = sx + sw / 2;\r\n\t\t\tvar oy = sy + sh / 2;\r\n\t\t\tif (this.rotate % 180 != 0) {\r\n\t\t\t\tvar temp = sw;\r\n\t\t\t\tsw = sh;\r\n\t\t\t\tsh = temp;\r\n\t\t\t}\r\n\t\t\tvar angle = this.rotate % 360;\r\n\t\t\tif (angle < 0) {\r\n\t\t\t\tangle += 360;\r\n\t\t\t}\r\n\t\t\tif (angle == 270) {\r\n\t\t\t\tvar x = this.real.width - oy;\r\n\t\t\t\tvar y = ox;\r\n\t\t\t\tox = x;\r\n\t\t\t\toy = y;\r\n\t\t\t}\r\n\t\t\tif (angle == 180) {\r\n\t\t\t\tvar x = this.real.width - ox;\r\n\t\t\t\tvar y = this.real.height - oy;\r\n\t\t\t\tox = x;\r\n\t\t\t\toy = y;\r\n\t\t\t}\r\n\t\t\tif (angle == 90) {\r\n\t\t\t\tvar x = oy;\r\n\t\t\t\tvar y = this.real.height - ox;\r\n\t\t\t\tox = x;\r\n\t\t\t\toy = y;\r\n\t\t\t}\r\n\t\t\tsx = ox - sw / 2;\r\n\t\t\tsy = oy - sh / 2;\r\n\t\t\tvar dr = {x: 0, y: 0, w: width, h: height};\r\n\t\t\tdr = this.parseRect(dr, -this.rotate);\r\n\t\t\treturn {\r\n\t\t\t\ttw: width,\r\n\t\t\t\tth: height,\r\n\t\t\t\tsx: sx,\r\n\t\t\t\tsy: sy,\r\n\t\t\t\tsw: sw,\r\n\t\t\t\tsh: sh,\r\n\t\t\t\tdx: dr.x,\r\n\t\t\t\tdy: dr.y,\r\n\t\t\t\tdw: dr.w,\r\n\t\t\t\tdh: dr.h\r\n\t\t\t};\r\n\t\t},\r\n\t\tparsePoint(point, angle) {\r\n\t\t\tvar result = {};\r\n\t\t\tresult.x = point.x * Math.cos(angle * Math.PI / 180) - point.y * Math.sin(angle * Math.PI / 180);\r\n\t\t\tresult.y = point.y * Math.cos(angle * Math.PI / 180) + point.x * Math.sin(angle * Math.PI / 180);\r\n\t\t\treturn result;\r\n\t\t},\r\n\t\tparseRect(rect, angle) {\r\n\t\t\tvar x1 = rect.x;\r\n\t\t\tvar y1 = rect.y;\r\n\t\t\tvar x2 = rect.x + rect.w;\r\n\t\t\tvar y2 = rect.y + rect.h;\r\n\t\t\tvar p1 = this.parsePoint({x: x1, y: y1}, angle);\r\n\t\t\tvar p2 = this.parsePoint({x: x2, y: y2}, angle);\r\n\t\t\tvar result = {};\r\n\t\t\tresult.x = Math.min(p1.x, p2.x);\r\n\t\t\tresult.y = Math.min(p1.y, p2.y);\r\n\t\t\tresult.w = Math.abs(p2.x - p1.x);\r\n\t\t\tresult.h = Math.abs(p2.y - p1.y);\r\n\t\t\treturn result;\r\n\t\t},\r\n\t\tparseBlob(base64) {\r\n\t\t\tvar arr = base64.split(',');\r\n\t\t\tvar mime = arr[0].match(/:(.*?);/)[1];\r\n\t\t\tvar bstr = atob(arr[1]);\r\n\t\t\tvar n = bstr.length;\r\n\t\t\tvar u8arr = new Uint8Array(n);\r\n\t\t\tfor(var i = 0; i < n; i++) {\r\n\t\t\t\tu8arr[i] = bstr.charCodeAt(i);\r\n\t\t\t}\r\n\t\t\tvar url = URL || webkitURL;\r\n\t\t\treturn url.createObjectURL(new Blob([u8arr], {type: mime}));\r\n\t\t},\r\n\t}\r\n};\n</script>\r\n\r\n<script module=\"mwx\" lang=\"wxs\">\r\n\tvar mode = \"\";\r\n\tvar rotate = 0;\r\n\tvar image = {\r\n\t\tleft: 0,\r\n\t\ttop: 0,\r\n\t\twidth: 0,\r\n\t\theight: 0\r\n\t};\r\n\tvar frame = {\r\n\t\tleft: 0,\r\n\t\ttop: 0,\r\n\t\twidth: 0,\r\n\t\theight: 0\r\n\t};\r\n\tvar touches = [];\r\n\tvar touchType = \"\";\r\n\tvar start = {\r\n\t\tframe: {\r\n\t\t\tleft: 0,\r\n\t\t\ttop: 0,\r\n\t\t\twidth: 0,\r\n\t\t\theight: 0\r\n\t\t},\r\n\t\timage: {\r\n\t\t\tleft: 0,\r\n\t\t\ttop: 0,\r\n\t\t\twidth: 0,\r\n\t\t\theight: 0\r\n\t\t}\r\n\t};\r\n\tfunction changeMode(value) {\r\n\t\tmode = value;\r\n\t}\r\n\tfunction changeRotate(value, old, oi, instance) {\r\n\t\trotate = value;\r\n\t\tupdateStyle(oi);\r\n\t}\r\n\tfunction changeImage(value, old, oi, instance) {\r\n\t\timage = value;\r\n\t\tupdateStyle(oi);\r\n\t}\r\n\tfunction changeFrame(value, old, oi, instance) {\r\n\t\tframe = value;\r\n\t\tupdateStyle(oi);\r\n\t}\r\n\tfunction touchstart(event, oi) {\r\n\t\t// #ifdef APP-PLUS || H5\r\n\t\tevent.preventDefault();\r\n\t\tevent.stopPropagation();\r\n\t\t// #endif\r\n\t\ttouches = event.touches;\r\n\t\tvar instance = event.instance;\r\n\t\tif (instance.hasClass(\"body\")) {\r\n\t\t\ttouchType = \"body\";\r\n\t\t} else if (instance.hasClass(\"frame-left-top\")) {\r\n\t\t\ttouchType = \"left-top\";\r\n\t\t} else if (instance.hasClass(\"frame-left-bottom\")) {\r\n\t\t\ttouchType = \"left-bottom\";\r\n\t\t} else if (instance.hasClass(\"frame-right-top\")) {\r\n\t\t\ttouchType = \"right-top\";\r\n\t\t} else if (instance.hasClass(\"frame-right-bottom\")) {\r\n\t\t\ttouchType = \"right-bottom\";\r\n\t\t}\r\n\t\tstart.frame.left = frame.left;\r\n\t\tstart.frame.top = frame.top;\r\n\t\tstart.frame.width = frame.width;\r\n\t\tstart.frame.height = frame.height;\r\n\t\tstart.image.left = image.left;\r\n\t\tstart.image.top = image.top;\r\n\t\tstart.image.width = image.width;\r\n\t\tstart.image.height = image.height;\r\n\t\treturn false;\r\n\t}\r\n\tfunction touchmove(event, oi) {\r\n\t\t// #ifdef APP-PLUS || H5\r\n\t\tevent.preventDefault();\r\n\t\tevent.stopPropagation();\r\n\t\t// #endif\r\n\t\tvar instance = event.instance;\r\n\t\tif (touches.length == 1) {\r\n\t\t\tif (touchType == \"body\") {\r\n\t\t\t\tmoveImage(touches[0], event.touches[0], oi);\r\n\t\t\t} else {\r\n\t\t\t\tscaleFrame(touches[0], event.touches[0], oi);\r\n\t\t\t}\r\n\t\t} else if (touches.length == 2 && event.touches.length == 2) {\r\n\t\t\tvar ta = touches[0];\r\n\t\t\tvar tb = touches[1];\r\n\t\t\tvar tc = event.touches[0];\r\n\t\t\tvar td = event.touches[1];\r\n\t\t\tif (ta.identifier != tc.identifier) {\r\n\t\t\t\tvar temp = tc;\r\n\t\t\t\ttc = td;\r\n\t\t\t\ttd = temp;\r\n\t\t\t}\r\n\t\t\tscaleImage(ta, tb, tc, td, oi);\r\n\t\t}\r\n\t}\r\n\tfunction touchend(event, oi) {\r\n\t\ttouches = [];\r\n\t\toi.callMethod(\"updateData\", {frame: frame, image: image});\r\n\t}\r\n\tfunction touchcancel(event, oi) {\r\n\t\ttouches = [];\r\n\t\toi.callMethod(\"updateData\", {frame: frame, image: image});\r\n\t}\r\n\tfunction moveImage(ta, tb, oi) {\r\n\t\tvar ax = tb.clientX - ta.clientX;\r\n\t\tvar ay = tb.clientY - ta.clientY;\r\n\t\timage.left = start.image.left + ax;\r\n\t\timage.top = start.image.top + ay;\r\n\t\tvar left = frame.left;\r\n\t\tvar top = frame.top;\r\n\t\tvar width = frame.width;\r\n\t\tvar height = frame.height;\r\n\t\tif (image.left > left) {\r\n\t\t\timage.left = left;\r\n\t\t}\r\n\t\tif (image.top > top) {\r\n\t\t\timage.top = top;\r\n\t\t}\r\n\t\tif (image.left + image.width < left + width) {\r\n\t\t\timage.left = left + width - image.width; \r\n\t\t}\r\n\t\tif (image.top + image.height < top + height) {\r\n\t\t\timage.top = top + height - image.height;\r\n\t\t}\r\n\t\tupdateStyle(oi);\r\n\t}\r\n\tfunction scaleImage(ta, tb, tc, td, oi) {\r\n\t\tvar x1 = ta.clientX;\r\n\t\tvar y1 = ta.clientY;\r\n\t\tvar x2 = tb.clientX;\r\n\t\tvar y2 = tb.clientY;\r\n\t\tvar x3 = tc.clientX;\r\n\t\tvar y3 = tc.clientY;\r\n\t\tvar x4 = td.clientX;\r\n\t\tvar y4 = td.clientY;\r\n\t\tvar ol = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));\r\n\t\tvar el = Math.sqrt((x3 - x4) * (x3 - x4) + (y3 - y4) * (y3 - y4));\r\n\t\tvar ocx = (x1 + x2) / 2;\r\n\t\tvar ocy = (y1 + y2) / 2;\r\n\t\tvar ecx = (x3 + x4) / 2;\r\n\t\tvar ecy = (y3 + y4) / 2;\r\n\t\tvar ax = ecx - ocx;\r\n\t\tvar ay = ecy - ocy;\r\n\t\tvar scale = el / ol;\r\n\t\tif (start.image.width * scale < frame.width) {\r\n\t\t\tscale = frame.width / start.image.width;\r\n\t\t}\r\n\t\tif (start.image.height * scale < frame.height) {\r\n\t\t\tscale = frame.height / start.image.height;\r\n\t\t}\r\n\t\tif (start.image.width * scale < frame.width) {\r\n\t\t\tscale = frame.width / start.image.width;\r\n\t\t}\r\n\t\timage.left = start.image.left + ax - (ocx - start.image.left) * (scale - 1);\r\n\t\timage.top = start.image.top + ay - (ocy - start.image.top) * (scale - 1);\r\n\t\timage.width = start.image.width * scale;\r\n\t\timage.height = start.image.height * scale;\r\n\t\tif (image.left > frame.left) {\r\n\t\t\timage.left = frame.left;\r\n\t\t}\r\n\t\tif (image.top > frame.top) {\r\n\t\t\timage.top = frame.top;\r\n\t\t}\r\n\t\tif (image.left + image.width < frame.left + frame.width) {\r\n\t\t\timage.left = frame.left + frame.width - image.width; \r\n\t\t}\r\n\t\tif (image.top + image.height < frame.top + frame.height) {\r\n\t\t\timage.top = frame.top + frame.height - image.height; \r\n\t\t}\r\n\t\tupdateStyle(oi);\r\n\t}\r\n\tfunction scaleFrame(ta, tb, oi) {\r\n\t\tvar ax = tb.clientX - ta.clientX;\r\n\t\tvar ay = tb.clientY - ta.clientY;\r\n\t\tvar x1 = start.frame.left;\r\n\t\tvar y1 = start.frame.top;\r\n\t\tvar x2 = start.frame.left + start.frame.width;\r\n\t\tvar y2 = start.frame.top + start.frame.height;\r\n\t\tvar cx1 = false;\r\n\t\tvar cy1 = false;\r\n\t\tvar cx2 = false;\r\n\t\tvar cy2 = false;\r\n\t\tvar mix = 30;\r\n\t\tvar rate = frame.width / frame.height;\r\n\t\tif (touchType == \"left-top\") {\r\n\t\t\tx1 += ax;\r\n\t\t\ty1 += ay;\r\n\t\t\tcx1 = true;\r\n\t\t\tcy1 = true;\r\n\t\t} else if (touchType == \"left-bottom\") {\r\n\t\t\tx1 += ax;\r\n\t\t\ty2 += ay;\r\n\t\t\tcx1 = true;\r\n\t\t\tcy2 = true;\r\n\t\t} else if (touchType == \"right-top\") {\r\n\t\t\tx2 += ax;\r\n\t\t\ty1 += ay;\r\n\t\t\tcx2 = true;\r\n\t\t\tcy1 = true;\r\n\t\t} else if (touchType == \"right-bottom\") {\r\n\t\t\tx2 += ax;\r\n\t\t\ty2 += ay;\r\n\t\t\tcx2 = true;\r\n\t\t\tcy2 = true;\r\n\t\t}\r\n\t\tif (x1 < image.left) {\r\n\t\t\tx1 = image.left;\r\n\t\t}\r\n\t\tif (y1 < image.top) {\r\n\t\t\ty1 = image.top;\r\n\t\t}\r\n\t\tif (x2 > image.left + image.width) {\r\n\t\t\tx2 = image.left + image.width;\r\n\t\t}\r\n\t\tif (y2 > image.top + image.height) {\r\n\t\t\ty2 = image.top + image.height;\r\n\t\t}\r\n\t\tif (cx1) {\r\n\t\t\tif (x1 > x2 - mix) {\r\n\t\t\t\tx1 = x2 - mix;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (cy1) {\r\n\t\t\tif (y1 > y2 - mix) {\r\n\t\t\t\ty1 = y2 - mix;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (cx2) {\r\n\t\t\tif (x2 < x1 + mix) {\r\n\t\t\t\tx2 = x1 + mix;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (cy2) {\r\n\t\t\tif (y2 < y1 + mix) {\r\n\t\t\t\ty2 = y1 + mix;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (cx1) {\r\n\t\t\tif (mode != \"free\") {\r\n\t\t\t\tvar val = x2 - rate * (y2 - y1);\r\n\t\t\t\tif (x1 < val) {\r\n\t\t\t\t\tx1 = val;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (cy1) {\r\n\t\t\tif (mode != \"free\") {\r\n\t\t\t\tvar val = y2 - (x2 - x1) / rate;\r\n\t\t\t\tif (y1 < val) {\r\n\t\t\t\t\ty1 = val;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (cx2) {\r\n\t\t\tif (mode != \"free\") {\r\n\t\t\t\tvar val = rate * (y2 - y1) + x1;\r\n\t\t\t\tif (x2 > val) {\r\n\t\t\t\t\tx2 = val;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (cy2) {\r\n\t\t\tif (mode != \"free\") {\r\n\t\t\t\tvar val = (x2 - x1) / rate + y1;\r\n\t\t\t\tif (y2 > val) {\r\n\t\t\t\t\ty2 = val;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tframe.left = x1;\r\n\t\tframe.top = y1;\r\n\t\tframe.width = x2 - x1;\r\n\t\tframe.height = y2 - y1;\r\n\t\tupdateStyle(oi);\r\n\t}\r\n\tfunction updateStyle(oi) {\r\n\t\toi.selectComponent(\".frame\").setStyle({\r\n\t\t\t\"left\": frame.left + \"px\",\r\n\t\t\t\"top\": frame.top + \"px\",\r\n\t\t\t\"width\": frame.width + \"px\",\r\n\t\t\t\"height\": frame.height + \"px\"\r\n\t\t});\r\n\t\toi.selectComponent(\".image-wrap\").setStyle({\r\n\t\t\t\"left\": image.left + \"px\",\r\n\t\t\t\"top\": image.top + \"px\",\r\n\t\t\t\"width\": image.width + \"px\",\r\n\t\t\t\"height\": image.height + \"px\"\r\n\t\t});\r\n\t\toi.selectComponent(\".image-rect\").setStyle({\r\n\t\t\t\"left\": image.left - frame.left + \"px\",\r\n\t\t\t\"top\": image.top - frame.top + \"px\",\r\n\t\t\t\"width\": image.width + \"px\",\r\n\t\t\t\"height\": image.height + \"px\"\r\n\t\t});\r\n\t\tvar left = 0;\r\n\t\tvar top = 0;\r\n\t\tvar width = image.width;\r\n\t\tvar height = image.height;\r\n\t\tif (rotate % 180 != 0) {\r\n\t\t\twidth = image.height;\r\n\t\t\theight = image.width;\r\n\t\t\ttop = width / 2 - height / 2;\r\n\t\t\tleft = height / 2 - width/ 2;\r\n\t\t}\r\n\t\toi.selectComponent(\".image-wrap .image\").setStyle({\r\n\t\t\t\"left\": left + \"px\",\r\n\t\t\t\"top\": top + \"px\",\r\n\t\t\t\"width\": width + \"px\",\r\n\t\t\t\"height\": height + \"px\",\r\n\t\t\t\"transform\": \"rotate(\" + rotate + \"deg)\"\r\n\t\t});\r\n\t\toi.selectComponent(\".image-rect .image\").setStyle({\r\n\t\t\t\"left\": left + \"px\",\r\n\t\t\t\"top\": top + \"px\",\r\n\t\t\t\"width\": width + \"px\",\r\n\t\t\t\"height\": height + \"px\",\r\n\t\t\t\"transform\": \"rotate(\" + rotate + \"deg)\"\r\n\t\t});\r\n\t}\r\n\tmodule.exports = {\r\n\t\tchangeMode: changeMode,\r\n\t\tchangeRotate: changeRotate,\r\n\t\tchangeImage: changeImage,\r\n\t\tchangeFrame: changeFrame,\r\n\t\ttouchstart: touchstart,\r\n\t\ttouchmove: touchmove,\r\n\t\ttouchend: touchend,\r\n\t\ttouchcancel: touchcancel\r\n\t};\r\n</script>\r\n\n<style scoped>\n.panel {\r\n\tposition: fixed;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tz-index: 9999999;\r\n\toverflow: hidden;\r\n}\r\n.canvas {\r\n\tposition: absolute;\r\n\ttop: 5000px;\r\n\tleft: 5000px;\r\n}\n.toolbar {\r\n\tposition: absolute;\r\n\twidth: 100%;\r\n\theight: 100rpx;\r\n\tleft: 0rpx;\r\n\tbottom: 0rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\talign-items: center;\r\n}\r\n.btn-cancel {\r\n\tfont-size: 40rpx;\r\n\tcolor: #d5dfe5;\r\n\tfont-weight: bold;\r\n}\r\n.btn-ok {\r\n\tfont-size: 40rpx;\r\n\tcolor: #FFFFFF;\r\n\tfont-weight: bold;\r\n}\r\n.btn-rotate {\r\n\tfont-size: 40rpx;\r\n\tcolor: #d5dfe5;\r\n\tfont-weight: bold;\r\n}\r\n.body {\r\n\tposition: absolute;\r\n\tleft: 0rpx;\r\n\tright: 0rpx;\r\n\ttop: 0rpx;\r\n\tbottom: 0rpx;\r\n\tbackground: black;\r\n\toverflow: hidden;\r\n}\r\n.mask {\r\n\tposition: absolute;\r\n\tleft: 0rpx;\r\n\tright: 0rpx;\r\n\ttop: 0rpx;\r\n\tbottom: 0rpx;\r\n\tbackground: black;\r\n\topacity: 0.4;\r\n}\r\n.plank {\r\n\tposition: absolute;\r\n\tleft: 0rpx;\r\n\tright: 0rpx;\r\n\ttop: 0rpx;\r\n\tbottom: 0rpx;\r\n}\r\n.image-wrap {\r\n\tposition: absolute;\r\n}\r\n.image-rect {\r\n\tposition: absolute;\r\n}\r\n.image {\r\n\tposition: absolute;\r\n}\r\n.frame {\r\n\tposition: absolute;\r\n\tleft: 100px;\r\n\ttop: 100px;\r\n\twidth: 200px;\r\n\theight: 200px;\r\n}\r\n.rect {\r\n\tposition: absolute;\r\n\tleft: -2px;\r\n\ttop: -2px;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder: 2px solid white;\r\n\toverflow: hidden;\r\n\tbox-sizing:content-box;\r\n}\r\n.line-one {\r\n\tposition: absolute;\r\n\twidth: 100%;\r\n\theight: 1px;\r\n\tbackground: white;\r\n\tleft: 0;\r\n\ttop: 33.3%;\r\n\tbox-sizing:content-box;\r\n}\r\n.line-two {\r\n\tposition: absolute;\r\n\twidth: 100%;\r\n\theight: 1px;\r\n\tbackground: white;\r\n\tleft: 0;\r\n\ttop: 66.7%;\r\n\tbox-sizing:content-box;\r\n}\r\n.line-three {\r\n\tposition: absolute;\r\n\twidth: 1px;\r\n\theight: 100%;\r\n\tbackground: white;\r\n\ttop: 0;\r\n\tleft: 33.3%;\r\n\tbox-sizing:content-box;\r\n}\r\n.line-four {\r\n\tposition: absolute;\r\n\twidth: 1px;\r\n\theight: 100%;\r\n\tbackground: white;\r\n\ttop: 0;\r\n\tleft: 66.7%;\r\n\tbox-sizing:content-box;\r\n}\r\n.frame-left-top {\r\n\tposition: absolute;\r\n\twidth: 20px;\r\n\theight: 20px;\r\n\tleft: -6px;\r\n\ttop: -6px;\r\n\tborder-left: 4px solid red;\r\n\tborder-top: 4px solid red;\r\n\tbox-sizing:content-box;\r\n}\r\n.frame-left-bottom {\r\n\tposition: absolute;\r\n\twidth: 20px;\r\n\theight: 20px;\r\n\tleft: -6px;\r\n\tbottom: -6px;\r\n\tborder-left: 4px solid red;\r\n\tborder-bottom: 4px solid red;\r\n\tbox-sizing:content-box;\r\n}\r\n.frame-right-top {\r\n\tposition: absolute;\r\n\twidth: 20px;\r\n\theight: 20px;\r\n\tright: -6px;\r\n\ttop: -6px;\r\n\tborder-right: 4px solid red;\r\n\tborder-top: 4px solid red;\r\n\tbox-sizing:content-box;\r\n}\r\n.frame-right-bottom {\r\n\tposition: absolute;\r\n\twidth: 20px;\r\n\theight: 20px;\r\n\tright: -6px;\r\n\tbottom: -6px;\r\n\tborder-right: 4px solid red;\r\n\tborder-bottom: 4px solid red;\r\n\tbox-sizing:content-box;\r\n}\r\n.transit {\r\n\ttransition: width 0.3s, height 0.3s, left 0.3s, top 0.3s, transform 0.3s;\r\n}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ksp-cropper.vue?vue&type=style&index=0&id=58c8ee16&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ksp-cropper.vue?vue&type=style&index=0&id=58c8ee16&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839462159\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ksp-cropper.vue?vue&type=custom&index=0&blockType=script&module=mwx&lang=wxs\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ksp-cropper.vue?vue&type=custom&index=0&blockType=script&module=mwx&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       Component.options.wxsCallMethods.push('updateData')\n     }"], "sourceRoot": ""}