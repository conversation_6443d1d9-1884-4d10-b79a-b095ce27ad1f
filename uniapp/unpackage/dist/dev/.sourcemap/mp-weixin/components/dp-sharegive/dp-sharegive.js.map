{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-sharegive/dp-sharegive.vue?d4e2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-sharegive/dp-sharegive.vue?a41c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-sharegive/dp-sharegive.vue?bca8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-sharegive/dp-sharegive.vue?c94d", "uni-app:///components/dp-sharegive/dp-sharegive.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-sharegive/dp-sharegive.vue?d93b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-sharegive/dp-sharegive.vue?1510"], "names": ["data", "pre_url", "isload", "loading", "isanswer", "tkdata", "isActive", "currentindex", "showanswer", "nums", "hasnum", "right", "error", "dtid", "is_right", "djs", "right_options", "rightnums", "props", "controller", "default", "sharegive", "mounted", "methods", "getdata", "that", "app", "id", "interval", "toanswer", "giveid", "toclosed", "selectOption", "finish", "right_option", "clearInterval", "toshare", "getdjs"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuF91B;AACA;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAH;MACAI;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACAJ;MACAC;QAAAI;MAAA;QACA;UACAL;UACA;UACAA;UACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACAK;MACA;MACAN;IACA;IACAO;MACA;MACA;MACA;QACAP;MACA;QACA;UACAA;QACA;UACAA;QACA;;QACAA;MACA;IACA;IACAQ;MACA;MACA;MACAC;MACA;QACAR;QAAA;MACA;MAEAA;QACAb;QACAqB;MACA;QACAR;QACA;UACAD;UACAA;UACAA;UACAA;UACAA;UACAA;UACAU;QACA;UACAT;UAAA;QACA;MACA;IACA;IACAU;MACA;MACA;QACAV;QAAA;MACA;MACA;MACA;QAAAI;MAAA;IACA;IACAO;MACA;MACA;MACA;QACAZ;QACAC;UAAAI;QAAA;UACA;YACAJ;UACA;UACAS;QACA;MACA;QACA;QACA;QACAV;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9NA;AAAA;AAAA;AAAA;AAAurC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-sharegive/dp-sharegive.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-sharegive.vue?vue&type=template&id=1ed12c38&\"\nvar renderjs\nimport script from \"./dp-sharegive.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-sharegive.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-sharegive.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-sharegive/dp-sharegive.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-sharegive.vue?vue&type=template&id=1ed12c38&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sharegive.isanswer ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload\n    ? _vm.getplatform() == \"h5\" ||\n      _vm.getplatform() == \"mp\" ||\n      _vm.getplatform() == \"app\"\n    : null\n  var m2 = _vm.isload && m1 ? _vm.t(\"color2\") : null\n  var m3 = _vm.isload && !m1 ? _vm.t(\"color2\") : null\n  var l0 =\n    _vm.isload && _vm.showanswer && !_vm.is_right && _vm.tkdata.rightcount > 1\n      ? _vm.__map(_vm.tkdata.option, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = _vm.isActive.indexOf(index)\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  var m4 =\n    _vm.isload && _vm.showanswer && !_vm.is_right ? _vm.t(\"color1\") : null\n  var m5 =\n    _vm.isload && _vm.showanswer && _vm.is_right == 1 && _vm.hasnum < _vm.nums\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload && _vm.showanswer && _vm.is_right == 2 && _vm.hasnum < _vm.nums\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        l0: l0,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-sharegive.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-sharegive.vue?vue&type=script&lang=js&\"", "<template>\r\n<view v-if=\"isload\">\r\n\t<view  class=\"content\">\r\n\t\t<view class=\"djs\" v-if=\"djs>0\">{{djs}}</view>\r\n\t\t<view class=\"djs\" v-else><text class=\"t1\">已阅</text></view>\r\n\t\t<view class=\"bottom\">\r\n\t\t\t<view class=\"tips\" v-if=\"sharegive.isanswer\"> 请浏览<text class=\"t1\">{{djs?djs:0}}</text>秒并回答问题在分享</view>\r\n\t\t\t<view class=\"tips\" v-else> 请浏览<text class=\"t1\">{{djs?djs:0}}</text>秒后在分享</view>\r\n\t\t\t<view class=\"btnbox\">\r\n\t\t\t\t<view class=\"btn1\" v-if=\"sharegive.isanswer\" :style=\"{backgroundColor:t('color1')}\" @tap=\"toanswer\" >回答问题</view>\r\n\t\t\t\t\r\n\t\t\t\t<button :class=\"'btn2 '+(sharegive.isanswer?'btn3':'') \" :style=\"{backgroundColor:t('color2')}\"  @tap.stop=\"toshare\" v-if=\"getplatform() == 'h5' || getplatform() == 'mp' || getplatform() == 'app'\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/share_white.png'\">我要分享\r\n\t\t\t\t</button>\r\n\t\t\t\t<button :class=\"'btn2 '+(sharegive.isanswer?'btn3':'') \" v-else :style=\"{backgroundColor:t('color2')}\"   open-type=\"share\" data-callback='sharegive' :data-id='sharegive.id' :data-isanswer='isanswer' >\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/share_white.png'\">我要分享</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"modal\" v-if=\"showanswer\">\r\n\t\t<view class=\"question\" >\r\n\t\t\t<view class=\"close\" @tap=\"toclosed\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"buydialog-canimg\"></image>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"!is_right\">\r\n\t\t\t\t<view class=\"title\" >\r\n\t\t\t\t\t题目：{{tkdata.title}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"tkdata.rightcount==1\">\r\n\t\t\t\t\t<view class=\"option_group\" >\r\n\t\t\t\t\t\t<view :class=\"'option flex ' +(index==currentindex?'on':'') \"  v-for=\"(item, index) in tkdata.option\" :key=\"index\" @tap=\"selectOption\" :data-index='index'>\r\n\t\t\t\t\t\t{{tkdata.sorts[index]}}\r\n\t\t\t\t\t\t<view class=\"after\" ></view> \r\n\t\t\t\t\t\t<view class=\"t1\">{{item}}</view></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"tkdata.rightcount>1\">\r\n\t\t\t\t\t<view class=\"option_group\" >\r\n\t\t\t\t\t\t<view :class=\"'option flex '+(isActive.indexOf(index)!=-1?'on':'')\"  v-for=\"(item, index) in tkdata.option\" :key=\"index\" @tap.stop=\"selectOption\" :data-index='index'>\r\n\t\t\t\t\t\t{{tkdata.sorts[index]}}\r\n\t\t\t\t\t\t<view class=\"after\" ></view> \r\n\t\t\t\t\t\t<view class=\"t1\">{{item}}</view></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"bottoms flex\">\r\n\t\t\t\t\t<button @tap.stop=\"finish\" data-dttype=\"down\" class=\"downbtn flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >提交</button>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\t<view class=\"right_content\"  v-if=\"is_right==1\">\r\n\t\t\t\t\t\t\t<view class=\"right-image\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/share_right.png'\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">回答正确</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\r\n\t\t\t\t\t\t\t<view class=\"bottoms flex\" v-if=\"hasnum<nums\">\r\n\t\t\t\t\t\t\t\t<button @tap=\"toanswer\" data-dttype=\"down\" class=\"downbtn flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >下一题</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"end\" v-else-if=\"rightnums>=nums\">\r\n\t\t\t\t\t\t\t\t\t答题完成，立即分享赚取奖励吧\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"end\" v-else-if=\"rightnums<nums\">\r\n\t\t\t\t\t\t\t\t\t未正确完成所有题目，请再接再励！\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right_content\" v-if=\"is_right==2\">\r\n\t\t\t\t\t\t<view class=\"right-image\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/share_error.png'\" />\r\n\t\t\t\t\t\t\t<text class=\"t1\">回答错误</text>\r\n\t\t\t\t\t\t\t<text class=\"t2\">正确答案：{{right_options}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"bottoms flex\" v-if=\"hasnum<nums\">\r\n\t\t\t\t\t\t\t<button @tap=\"toanswer\" data-dttype=\"down\" class=\"downbtn flex-x-center flex-y-center\"  :style=\"{background:t('color1')}\" >下一题</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"end\" v-else>\r\n\t\t\t\t\t\t\t\t未正确完成所有题目，请再接再励！\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\t\r\n\t\t\t\t\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\tvar interval = null;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\tisload:false,\r\n\t\t\t\tloading:false,\r\n\t\t\t\tdata:[],\r\n\t\t\t\tisanswer:false,\r\n\t\t\t\ttkdata:[],\r\n\t\t\t\tisActive: [],\r\n\t\t\t\tcurrentindex:'-1',\r\n\t\t\t\tshowanswer:false,\r\n\t\t\t\tnums:0,\r\n\t\t\t\thasnum:0,\r\n\t\t\t\tright:false,\r\n\t\t\t\terror:false,\r\n\t\t\t\tdtid:0,\r\n\t\t\t\tis_right:0,\r\n\t\t\t\tdjs:'',\r\n\t\t\t\tright_options:'',\r\n\t\t\t\trightnums:0\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tcontroller:{default:'ApiShareGive'},\r\n\t\t\tsharegive:{},\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(this.controller+'/getdetail',{id:that.sharegive.id},function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.data = res.data;\r\n\t\t\t\t\tthat.isanswer = res.isanswer\r\n\t\t\t\t\tthat.lefttime = res.data.readtimes\r\n\t\t\t\t\tif (that.lefttime > 0) {\r\n          \tinterval = setInterval(function () {\r\n          \t\tthat.lefttime = that.lefttime - 1;\r\n          \t\tthat.getdjs();\r\n          \t}, 1000);\r\n          }\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoanswer:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tthat.is_right=false\r\n\t\t\t\tapp.post('ApiShareGive/getTiku', {giveid: that.data.id}, function(res){\r\n\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\tthat.tkdata = res.data.tkdata\r\n\t\t\t\t\t\t//that.nums   = res.data.nums\r\n\t\t\t\t\t\tthat.dtid = res.data.dtid\r\n\t\t\t\t\t\t//that.hasnum = res.data.hasnum\r\n\t\t\t\t\t\tthat.showanswer = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.error('题目获取失败')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoclosed:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tthat.showanswer = false;\r\n\t\t\t},\r\n\t\t\tselectOption: function (e) {\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t  var that = this;\r\n\t\t\t  if(that.tkdata.rightcount==1){\r\n\t\t\t\tthat.currentindex = index;\r\n\t\t\t  }else{\r\n\t\t\t\t\tif (that.isActive.indexOf(index) == -1) {\r\n\t\t\t\t\t\tthat.isActive.push(index); //选中添加到数组里\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.isActive.splice(that.isActive.indexOf(index), 1); //取消\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.currentindex = that.isActive;\r\n\t\t\t  }\r\n\t\t\t}, \r\n\t\t\tfinish:function(){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar right_option='';\r\n\t\t\t\tright_option = that.currentindex;\r\n\t\t\t\tif(right_option==-1){\r\n\t\t\t\t\tapp.error('请选择答案');return;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tapp.post('ApiShareGive/tofinish', {\r\n\t\t\t\t\tdtid:that.dtid,\r\n\t\t\t\t\tright_option:right_option,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\tthat.is_right = res.is_right\r\n\t\t\t\t\t\tthat.nums = res.nums\r\n\t\t\t\t\t\tthat.hasnum = res.hasnum\r\n\t\t\t\t\t\tthat.rightnums = res.rightnums\r\n\t\t\t\t\t\tthat.right_options = res.right_options\r\n\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\tclearInterval(interval);\t\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.error('服务器错误');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoshare:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif(!that.isanswer){\r\n\t\t\t\t\t\tapp.error('请先回答问题');return;\r\n\t\t\t\t}\t\r\n\t\t\t\tvar giveid = that.sharegive.id\r\n\t\t\t\tthis.$emit('toshare',{giveid: giveid});\r\n\t\t\t},\r\n\t\t\tgetdjs: function () {\r\n\t\t\t  var that = this;\r\n\t\t\t  var totalsec = that.lefttime;\r\n\t\t\t  if (totalsec <= 0) {\r\n\t\t\t    that.djs = '00';\r\n\t\t\t\t\tapp.post('ApiShareGive/addread', {giveid: that.sharegive.id}, function (res) {\r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\t\tapp.success(res.msg)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tclearInterval(interval);\t\r\n\t\t\t\t\t})\r\n\t\t\t  } else {\r\n\t\t\t    var sec = totalsec;\r\n\t\t\t    var djs =  (sec < 10 ? '0' : '') + sec;\r\n\t\t\t    that.djs = djs;\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.djs{ border-radius: 50%; position: fixed; right:5%; top:30rpx; width: 80rpx; height: 80rpx; right: 10rpx; text-align: center; line-height: 80rpx; font-size: 28rpx;background: rgba(0,0,0, 0.4); color: #fff; }\r\n\r\n.bottom{ width: 100%;position: fixed; bottom:0;background: #fff;align-items: center; }\r\n.bottom .btnbox{ display: flex;}\r\n.bottom .tips{ height: 60rpx;line-height: 60rpx;text-align: center; }\r\n.bottom .tips .t1{ color: red;font-weight: bold;}\r\n.bottom .btn1{ width: 50%;text-align: center;color: #fff;height: 80rpx;line-height: 80rpx;}\r\n.bottom .btn2{width: 100%; height: 80rpx;line-height: 80rpx;text-align: center;color: #fff; display: flex;justify-content: center;align-items: center;}\r\n.bottom .btn2 image{ width: 40rpx; height:40rpx;margin-right: 10rpx;}\r\n.bottom .btn2.btn3{ width: 50%;} \r\n\r\n.bottoms .upbut{width:240rpx;height: 88rpx; line-height: 88rpx;color: #fff;  border-radius: 44rpx;border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; }\t\r\n.bottoms .upbut.hui{ background:#E3E3E3;}\r\n.bottoms .downbtn{margin-left:50rpx;width:360rpx;height: 88rpx; border-radius: 44rpx; line-height: 72rpx;color: #fff;  border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; }\t\r\n.bottoms{ margin-top: 30rpx; padding: 30rpx;}\r\n\r\n\r\n.right_content{ background: #fff; margin: 30rpx; border-radius: 10rpx; padding: 30rpx;text-align: center;}\r\n.right_content .t1{ color: #333; font-weight: 30rpx; font-weight: bold; display: block; margin-bottom: 20rpx;}\r\n.right_content .t2{ color:#93949E;font-size: 26rpx;}\r\n\r\n.right_content .right-image image{ width: 80rpx;height: 80rpx;}\r\n\r\n.modal{ position: fixed;background: rgba(0,0,0, 0.4); width: 100%; height:100%; top:0;display: flex;justify-content: center;}\r\n.question{ position: absolute;background: #fff; width: 90%; padding:30rpx;margin-top: 130rpx;border-radius: 10rpx;}\r\n\r\n.question .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:9999}\r\n.question .close image{ width: 30rpx; height:30rpx; }\r\n\r\n.question .title{ font-size: 30rpx; color: #333; font-weight: bold;}\r\n.option_group .option{ position: relative; padding:20rpx 30rpx; background:#F8F4F4 ; margin-top: 30rpx; border-radius: 48rpx;border-color: #bbb; }\r\n.option_group .option .t1{ margin-left: 20rpx;border-left: 1px solid;padding-left: 20rpx;}\r\n.option_group .option.on{ background: #FDF1F1; color:#FF5347 ; border: 1px solid #FFAEA8 ; border-color: #FF8D8D;}\r\n\r\n\r\n\r\n.end{ margin-top: 30rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-sharegive.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-sharegive.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839386265\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}