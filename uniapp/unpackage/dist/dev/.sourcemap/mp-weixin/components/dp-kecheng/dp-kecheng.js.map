{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-kecheng/dp-kecheng.vue?efd2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-kecheng/dp-kecheng.vue?7335", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-kecheng/dp-kecheng.vue?9da3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-kecheng/dp-kecheng.vue?e6bf", "uni-app:///components/dp-kecheng/dp-kecheng.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-kecheng/dp-kecheng.vue?c75a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-kecheng/dp-kecheng.vue?34a8"], "names": ["props", "menuindex", "default", "params", "data"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,2PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCc51B;EACAA;IACAC;MAAAC;IAAA;IACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-kecheng/dp-kecheng.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-kecheng.vue?vue&type=template&id=954e2df8&\"\nvar renderjs\nimport script from \"./dp-kecheng.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-kecheng.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-kecheng.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-kecheng/dp-kecheng.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-kecheng.vue?vue&type=template&id=954e2df8&\"", "var components\ntry {\n  components = {\n    dpKechengItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kecheng-item/dp-kecheng-item\" */ \"@/components/dp-kecheng-item/dp-kecheng-item.vue\"\n      )\n    },\n    dpKechengItemlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kecheng-itemlist/dp-kecheng-itemlist\" */ \"@/components/dp-kecheng-itemlist/dp-kecheng-itemlist.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-kecheng.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-kecheng.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-product\" :style=\"{\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',\r\n\tpadding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx',\r\n\twidth:'calc(100% - '+params.margin_x*2.2*2+'rpx)'\r\n}\">\r\n\t<!--123排-->\r\n\t<dp-kecheng-item v-if=\"params.style=='1' || params.style=='2' || params.style=='3'\" :showstyle=\"params.style\" :data=\"data\" :saleimg=\"params.saleimg\" :showname=\"params.showname\" :showprice=\"params.showprice\" :showsales=\"params.showsales\" idfield=\"proid\" :menuindex=\"menuindex\"></dp-kecheng-item>\r\n\t<!--横排-->\r\n\t<dp-kecheng-itemlist v-if=\"params.style=='list'\" :data=\"data\" :saleimg=\"params.saleimg\" :showpic=\"params.showpic\" :showname=\"params.showname\" :showprice=\"params.showprice\" :showsales=\"params.showsales\" idfield=\"proid\" :menuindex=\"menuindex\"></dp-kecheng-itemlist>\r\n</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-product{width:100%;height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-kecheng.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-kecheng.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839376041\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}