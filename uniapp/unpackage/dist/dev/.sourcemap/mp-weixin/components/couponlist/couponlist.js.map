{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/couponlist/couponlist.vue?dc6b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/couponlist/couponlist.vue?4109", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/couponlist/couponlist.vue?f0b0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/couponlist/couponlist.vue?2e4b", "uni-app:///components/couponlist/couponlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/couponlist/couponlist.vue?5c7f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/couponlist/couponlist.vue?86fc"], "names": ["data", "props", "menuindex", "default", "couponlist", "couponstyle", "bid", "selectedrid", "selectedrids", "type", "choosecoupon", "methods", "getcoupon", "app", "adUnitId", "rewardedVideoAd", "console", "that", "getcouponconfirm", "id", "chooseCoupon", "rid", "key"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+C51B;AAAA,gBACA;EACAA;IACA,QAEA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;IACAC;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;MAAAJ;IAAA;IACAK;MAAAC;MAAAN;QAAA;MAAA;IAAA;IACAO;MAAAP;IAAA;EACA;EACAQ;IACAC;MACA;MACA;MACA;MACA;MACA;QACAC;QACA;UACAA;YAAAC;UAAA;QACA;QACA;QACAC;UAAAF;UAAAE;QAAA;UAAAF;QAAA;QACAE;UACAF;UACAA;UACAG;UACAD;UACAA;QACA;QACAA;UACAF;UACA;YACA;YACAI;UACA;YACAD;UACA;UACAD;UACAA;QACA;MACA;QACAE;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAL;UAAAM;QAAA;UACA;YACAN;UACA;YACAA;UACA;QACA;QACA;MACA;MACA;QACAA;UACAA;UACAA;YAAAM;UAAA;YACAN;YACA;cACAA;YACA;cACAA;cACAI;YACA;UACA;QACA;MACA;QACAJ;QACAA;UAAAM;QAAA;UACAN;UACA;YACAA;UACA;YACAA;YACAI;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACA;QAAAC;QAAAf;QAAAgB;MAAA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACjJA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/couponlist/couponlist.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./couponlist.vue?vue&type=template&id=1e88b144&\"\nvar renderjs\nimport script from \"./couponlist.vue?vue&type=script&lang=js&\"\nexport * from \"./couponlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./couponlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/couponlist/couponlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./couponlist.vue?vue&type=template&id=1e88b144&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.couponlist, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = item.type == 1 ? _vm.t(\"color1\") : null\n    var m1 = item.type == 10 ? _vm.t(\"color1\") : null\n    var m2 = item.type == 2 ? _vm.t(\"color1\") : null\n    var m3 = item.type == 3 ? _vm.t(\"color1\") : null\n    var m4 = item.type == 4 ? _vm.t(\"color1\") : null\n    var m5 = item.type == 11 ? _vm.t(\"color1\") : null\n    var m6 = item.type == 5 ? _vm.t(\"color1\") : null\n    var m7 = item.type == 1 ? _vm.t(\"color1rgb\") : null\n    var m8 = item.type == 1 ? _vm.t(\"color1\") : null\n    var m9 = item.type == 2 ? _vm.t(\"color1rgb\") : null\n    var m10 = item.type == 2 ? _vm.t(\"color1\") : null\n    var m11 = item.type == 3 ? _vm.t(\"color1rgb\") : null\n    var m12 = item.type == 3 ? _vm.t(\"color1\") : null\n    var m13 = item.type == 4 ? _vm.t(\"color1rgb\") : null\n    var m14 = item.type == 4 ? _vm.t(\"color1\") : null\n    var m15 = item.type == 5 ? _vm.t(\"color1rgb\") : null\n    var m16 = item.type == 5 ? _vm.t(\"color1\") : null\n    var m17 = item.type == 6 ? _vm.t(\"color1rgb\") : null\n    var m18 = item.type == 6 ? _vm.t(\"color1\") : null\n    var m19 = _vm.choosecoupon ? _vm.dateFormat(item.endtime) : null\n    var m20 = !_vm.choosecoupon ? _vm.dateFormat(item.yxqdate) : null\n    var m21 = _vm.choosecoupon\n      ? _vm.selectedrid == item.id || _vm.inArray(item.id, _vm.selectedrids)\n      : null\n    var m22 = _vm.choosecoupon && !m21 ? _vm.t(\"color1\") : null\n    var m23 = _vm.choosecoupon && !m21 ? _vm.t(\"color1rgb\") : null\n    var m24 =\n      !_vm.choosecoupon &&\n      !(item.haveget >= item.perlimit && item.perlimit > 0) &&\n      !(item.stock <= 0)\n        ? _vm.t(\"color1\")\n        : null\n    var m25 =\n      !_vm.choosecoupon &&\n      !(item.haveget >= item.perlimit && item.perlimit > 0) &&\n      !(item.stock <= 0)\n        ? _vm.t(\"color1rgb\")\n        : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n      m6: m6,\n      m7: m7,\n      m8: m8,\n      m9: m9,\n      m10: m10,\n      m11: m11,\n      m12: m12,\n      m13: m13,\n      m14: m14,\n      m15: m15,\n      m16: m16,\n      m17: m17,\n      m18: m18,\n      m19: m19,\n      m20: m20,\n      m21: m21,\n      m22: m22,\n      m23: m23,\n      m24: m24,\n      m25: m25,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./couponlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./couponlist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"couponlist\">\r\n\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"coupon\" :style=\"couponstyle\">\r\n\t\t<view :class=\"item.type==1?'pt_img1':'pt_img2'\"></view>\r\n\t\t<view class=\"pt_left\" @tap=\"goto\" :data-url=\"'/pagesExt/coupon/coupondetail?'+(choosecoupon?'rid':'id')+'=' + item.id\">\r\n\t\t\t<view class=\"pt_left-content\">\r\n\t\t\t\t<view class=\"f1\" v-if=\"item.type==1\" :style=\"{color:t('color1')}\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"item.type==10\" :style=\"{color:t('color1')}\"><text class=\"t1\">{{item.discount/10}}</text><text class=\"t0\">折</text></view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"item.type==2\" :style=\"{color:t('color1')}\">礼品券</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"item.type==3\" :style=\"{color:t('color1')}\"><text class=\"t1\">{{item.limit_count}}</text><text class=\"t2\">次</text></view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"item.type==4\" :style=\"{color:t('color1')}\">抵运费</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"item.type==11\" :style=\"{color:t('color1')}\">兑换券</view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==5\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t<view class=\"f2\" v-if=\"item.type==1 || item.type==4 || item.type==5 || item.type==6\">\r\n\t\t\t\t\t<text v-if=\"item.minprice>0\">满{{item.minprice}}元可用</text>\r\n\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"pt_right\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"t1\">{{choosecoupon ? item.couponname : item.name}}</view>\r\n\t\t\t\t<view style=\"height: 45rpx;\">\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.type==1\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">代金券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.type==2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">礼品券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.type==3\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">计次券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.type==4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">运费抵扣券</text>\r\n\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==5\">餐饮券</text>\r\n\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==6\">酒店券</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t3\" style=\"item.bid>0?'margin-top:0':'margin-top:10rpx'\">有效期至 {{choosecoupon ? dateFormat(item.endtime) : dateFormat(item.yxqdate)}}</view>\r\n\t\t\t\t<view class=\"t4\" v-if=\"item.bid>0\">适用商家：{{item.bname}}</view>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"choosecoupon\">\r\n\t\t\t\t<button class=\"btn\" style=\"width:160rpx;background:#555\" @tap=\"chooseCoupon\" :data-rid=\"item.id\" :data-key=\"index\" v-if=\"selectedrid==item.id || inArray(item.id,selectedrids)\">取消选择</button>\r\n\t\t\t\t<button class=\"btn\" style=\"width:160rpx;\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"chooseCoupon\" :data-rid=\"item.id\" :data-key=\"index\" v-else>选择使用</button>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<button class=\"btn\" v-if=\"(item.haveget>=item.perlimit) && (item.perlimit > 0)\" style=\"background:#9d9d9d\">已领取</button>\r\n\t\t\t\t<button class=\"btn\" v-else-if=\"item.stock<=0\" style=\"background:#9d9d9d\">已抢光了</button>\r\n\t\t\t\t<button class=\"btn\" v-else :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"getcoupon\" :data-id=\"item.id\" :data-price=\"item.price\" :data-score=\"item.score\" :data-key=\"index\">领取</button>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tcouponlist:{},\r\n\t\t\tcouponstyle:{default:''},\r\n\t\t\tbid:{default:''},\r\n\t\t\tselectedrid:{default:''},\r\n\t\t\tselectedrids:{type:Array,default(){ return []}},\r\n\t\t\tchoosecoupon:{default:false}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetcoupon:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar couponlist = that.couponlist;\r\n\t\t\t\tvar key = e.currentTarget.dataset.key;\r\n\t\t\t\tvar couponinfo = couponlist[key];\r\n\t\t\t\tif (app.globalData.platform == 'wx' && couponinfo && couponinfo.rewardedvideoad && wx.createRewardedVideoAd) {\r\n\t\t\t\t\tapp.showLoading();\r\n\t\t\t\t\tif(!app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad]){\r\n\t\t\t\t\t\tapp.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: couponinfo.rewardedvideoad});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar rewardedVideoAd = app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad];\r\n\t\t\t\t\trewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});\r\n\t\t\t\t\trewardedVideoAd.onError((err) => {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tapp.alert(err.errMsg);\r\n\t\t\t\t\t\tconsole.log('onError event emit', err)\r\n\t\t\t\t\t\trewardedVideoAd.offLoad()\r\n\t\t\t\t\t\trewardedVideoAd.offClose();\r\n\t\t\t\t\t});\r\n\t\t\t\t\trewardedVideoAd.onClose(res => {\r\n\t\t\t\t\t\tapp.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = null;\r\n\t\t\t\t\t\tif (res && res.isEnded) {\r\n\t\t\t\t\t\t\t//app.alert('播放结束 发放奖励');\r\n\t\t\t\t\t\t\tthat.getcouponconfirm(e);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('播放中途退出，不下发奖励');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\trewardedVideoAd.offLoad()\r\n\t\t\t\t\t\trewardedVideoAd.offClose();\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.getcouponconfirm(e);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetcouponconfirm:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\t\tvar score = e.currentTarget.dataset.score;\r\n\t\t\t\tvar price = e.currentTarget.dataset.price;\r\n\t\t\t\tif(price > 0){\r\n\t\t\t\t\tapp.post('ApiCoupon/buycoupon', {id: id}, function (res) {\r\n\t\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.goto('/pagesExt/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(score > 0){\r\n\t\t\t\t\tapp.confirm('确定要消耗'+score+''+that.t('积分')+'兑换吗?',function(){\r\n\t\t\t\t\t\tapp.showLoading('兑换中');\r\n\t\t\t\t\t\tapp.post('ApiCoupon/getcoupon',{id:id},function(data){\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\tif(data.status==0){\r\n\t\t\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\t\t\tthat.$emit('getcoupon');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.showLoading('领取中');\r\n\t\t\t\t\tapp.post('ApiCoupon/getcoupon',{id:id},function(data){\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tif(data.status==0){\r\n\t\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\t\tthat.$emit('getcoupon');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchooseCoupon:function(e){\r\n\t\t\t\tvar rid = e.currentTarget.dataset.rid\r\n\t\t\t\tvar key = e.currentTarget.dataset.key\r\n\t\t\t\tthis.$emit('chooseCoupon',{rid:rid,bid:this.bid,key:key});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.couponlist{width:94%;margin:0 3%;padding:20rpx}\r\n.coupon{width:100%;display:flex;margin-bottom:20rpx;border-radius:10rpx;overflow:hidden;border:1px solid #eee}\r\n.coupon .pt_left{background: #fff;height:200rpx;color: #FFF;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left-content{width:100%;height:100%;margin:30rpx 0;border-right:1px solid #EEEEEE;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left .f1{font-size:40rpx;font-weight:bold;text-align:center;}\r\n.coupon .pt_left .t0{padding-right:0;}\r\n.coupon .pt_left .t1{font-size:60rpx;}\r\n.coupon .pt_left .t2{padding-left:10rpx;}\r\n.coupon .pt_left .f2{font-size:20rpx;color:#4E535B;text-align:center;}\r\n.coupon .pt_right{background: #fff;width:70%;display:flex;height:220rpx;text-align: left;padding:20rpx 0 20rpx 20rpx;position:relative}\r\n.coupon .pt_right .f1{flex-grow: 1;flex-shrink: 1;}\r\n.coupon .pt_right .f1 .t1{font-size:28rpx;color:#2B2B2B;font-weight:bold;height:60rpx;line-height:60rpx;overflow:hidden}\r\n.coupon .pt_right .f1 .t2{display:inline-block;height:36rpx;line-height:36rpx;font-size:20rpx;font-weight:bold;padding:0 16rpx;border-radius:4rpx}\r\n.coupon .pt_right .f1 .t3{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\r\n.coupon .pt_right .f1 .t4{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;max-width: 76%;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;}\r\n.coupon .pt_right .btn{position:absolute;right:20rpx;top:50%;margin-top:-28rpx;border-radius:28rpx;width:140rpx;height:56rpx;line-height:56rpx;color:#fff}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./couponlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./couponlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839367989\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}