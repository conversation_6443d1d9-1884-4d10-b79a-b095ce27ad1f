{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-video/dp-video.vue?1fd7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-video/dp-video.vue?e009", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-video/dp-video.vue?3959", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-video/dp-video.vue?aa19", "uni-app:///components/dp-video/dp-video.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-video/dp-video.vue?5549", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-video/dp-video.vue?2107"], "names": ["props", "params", "data", "Height", "pre_url", "<PERSON><PERSON><PERSON><PERSON>", "mounted", "uni", "success", "that", "<PERSON><PERSON><PERSON>", "menu2data"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmB11B;AAAA,eACA;EACAA;IACAC;IACAC;EACA;EACAA;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACAC;IACA;MACAA;IACA;MACAA;IACA;MACAA;IACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;MACA;MAEA;QACA;UACA,sBACAC;UACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-video/dp-video.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-video.vue?vue&type=template&id=60870344&\"\nvar renderjs\nimport script from \"./dp-video.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-video.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-video.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-video/dp-video.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-video.vue?vue&type=template&id=60870344&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = (_vm.Height * 0.3).toFixed(2)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-video.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-video.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-video\" :style=\"{\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',\r\n\tpadding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',\r\n\theight: 'calc('+Height * (params.videoheight ? params.videoheight:30) / 100+'px - '+ `${hastabbar ? '110rpx - env(safe-area-inset-bottom)':'env(safe-area-inset-bottom)'}`+')',\r\n\tminHeight:(Height*0.3).toFixed(2)+'px'\r\n}\">\r\n\t\t<!-- 普通视频 -->\r\n\t\t<video v-if=\"!params.type || params.type==0\" class=\"dp-video-video\" :src=\"params.src\" :show-mute-btn=\"(params.muted ? params.muted:0) == 1\" :play-btn-position=\"(params.butposition ? params.butposition:'center')\" :object-fit=\"(params.objectfit ? params.objectfit:'contain')\" :poster=\"params.pic\" :controls=\"(params.controls ? params.controls:1) == 1\" :autoplay='(params.autoplay ? params.autoplay:0) == 1' :loop=\"(params.loop ? params.loop:0) == 1\" :muted=\"params.muted == 1\" :show-fullscreen-btn=\"(params.fullscreen ? params.fullscreen:1) == 1\" ></video>\r\n\t\t<!-- #ifdef MP-WEIXIN  -->\r\n\t\t<!-- 内嵌视频号视频 -->\r\n\t\t<channel-video v-if=\"params.type==1\" class=\"dp-video-video\" :feed-token=\"params.video_feedtoken\" :feed-id=\"params.video_feedid\" :finder-user-name=\"params.video_finderuser\" :object-fit=\"(params.objectfit ? params.objectfit:'contain')\" :poster=\"params.pic\" :autoplay='(params.autoplay ? params.autoplay:0) == 1' :loop=\"(params.loop ? params.loop:0) == 1\" :muted=\"params.muted == 1\"></channel-video>\r\n\t\t<!-- 跳转视频号首页 -->\r\n\t\t<view v-if=\"params.type==2\" class=\"dp-video-poster\" :style=\"'height:100%;background:#222222;background-image:url('+params.pic+');background-size:cover;'\" @tap=\"goto\" :data-url=\"params.src\"><image class=\"dp-video-playicon\" :src=\"pre_url+'/static/img/shortvideo_playnum.png'\"></view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tHeight:'',\r\n\t\t\t\tpre_url:getApp().globalData.pre_url,\r\n\t\t\t\thastabbar:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet that = this;\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tthat.Height = res.windowHeight;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tvar pages = getCurrentPages(); //获取加载的页面\r\n\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n\t\t\tvar currenturl = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \r\n\t\t\tif (app.globalData.platform == 'baidu') {\r\n\t\t\t\tvar opts = currentPage.options;\r\n\t\t\t} else {\r\n\t\t\t\tvar opts = currentPage.$vm.opt;\r\n\t\t\t}\r\n\t\t\tif (opts && opts.id) {\r\n\t\t\t\tcurrenturl += '?id=' + opts.id\r\n\t\t\t} else if (opts && opts.cid) {\r\n\t\t\t\tcurrenturl += '?cid=' + opts.cid\r\n\t\t\t} else if (opts && opts.gid) {\r\n\t\t\t\tcurrenturl += '?gid=' + opts.gid\r\n\t\t\t} else if (opts && opts.bid) {\r\n\t\t\t\tcurrenturl += '?bid=' + opts.bid\r\n\t\t\t}\r\n\t\t\tvar menudata = JSON.parse(JSON.stringify(app.globalData.initdata.menudata));\r\n\t\t\tvar tablist = menudata['list'];\r\n\t\t\tfor (var i = 0; i < tablist.length; i++) {\r\n\t\t\t\tif (tablist[i]['pagePath'] == currenturl) {\r\n\t\t\t\t\tthis.hastabbar = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (this.hastabbar == false) {\r\n\t\t\t\tvar menu2data = JSON.parse(JSON.stringify(app.globalData.initdata.menu2data))\r\n\t\t\t\t\r\n\t\t\t\tif (menu2data.length > 0) {\r\n\t\t\t\t\tfor (var i in menu2data) {\r\n\t\t\t\t\t\tif (opts && opts.bid)\r\n\t\t\t\t\t\t\tmenu2data[i].indexurl = (menu2data[i].indexurl).replace('[bid]', opts.bid);\r\n\t\t\t\t\t\tif (menu2data[i].indexurl == currenturl) {\r\n\t\t\t\t\t\t\tthis.hastabbar = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-video{ position: relative; font-size: 0;}\r\n.dp-video-video{width: 100%; margin: 0px; padding: 0px;height: 100%;}\r\n.dp-video-poster{display: flex;align-items: center;justify-content: center;}\r\n.dp-video-playicon{width: 60rpx;height: 60rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-video.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-video.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839375696\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}