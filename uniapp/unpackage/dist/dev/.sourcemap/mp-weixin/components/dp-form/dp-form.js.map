{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-form/dp-form.vue?2fd8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-form/dp-form.vue?3f81", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-form/dp-form.vue?1a89", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-form/dp-form.vue?a6e2", "uni-app:///components/dp-form/dp-form.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-form/dp-form.vue?163a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-form/dp-form.vue?667d"], "names": ["data", "pre_url", "editorFormdata", "test", "regiondata", "items", "tmplids", "submitDisabled", "formdata", "formvaldata", "authphone", "platform", "feetotal", "yearList", "area", "adr_lon", "adr_lat", "timer", "keyboardHeight", "xystatus", "<PERSON><PERSON><PERSON><PERSON>", "xytitle", "xycontent", "xytitlePos", "isagree", "agree_button", "mid", "radios", "radiokeys", "<PERSON><PERSON><PERSON><PERSON>", "totalprice", "props", "params", "latitude", "longitude", "htsignatureurl", "updated", "mounted", "year", "that", "app", "url", "uni", "method", "header", "success", "fromrecord", "formid", "thisval", "res", "console", "methods", "inputBlur", "inputFocus", "clearTimeout", "query", "getScrollOffset", "resolve", "scrollToInput", "scrollTop", "duration", "onchange", "setfield", "editorFormSubmit", "subdata", "feenum", "feedata", "posturl", "edit_id", "price", "from<PERSON>l", "setTimeout", "editorChooseImage", "pics", "removeimg", "yearChange", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "getPhoneNumber", "wx", "iv", "encryptedData", "code", "pid", "download", "filePath", "showMenu", "chooseFile", "count", "maxsize", "name", "fail", "complete", "checkPayMoney", "feeitmes", "feeChange", "feeitems", "upVideo", "sourceType", "<PERSON><PERSON><PERSON><PERSON>", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "selradio", "key", "index", "paymoney", "value", "calculatePrice"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpGA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Sz1B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;IACAhC;IACAiC;IACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAC;IACAC;MACA;MACA;QACAC;MACA;MACAC;QACAD;QACAzC;QACA2C;QACAC;UAAA;QAAA;QACAC;UACAN;QACA;MACA;IACA;IACAA;IAEA;IACA;IACA;IACA;IACA;IACA;MACAO;IACA;IACAP;IACAA;IACAA;IACAA;IACAA;IACAC;MAAAO;MAAAD;IAAA;MACA;QACA;QACA;QACA;QACArC;QACA;UACA;UACA;YACA8B;UACA;UACA;YACA;cACA;gBACAS;cACA;YACA;UACA;UACA;YACA;cACAC;YACA;cACAA;YACA;UACA;UACA/C;UACAO;UAEA;YACA;YACAyC;YACA;cACA;cACA;gBACAX;gBACAA;cACA;gBACAA;gBACAA;cACA;YACA;UACA;QAEA;QACAA;QACAA;QACAA;MACA;QACA;QACA9B;QACA8B;MACA;IACA;EACA;EACAY;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAb;QAAA;MACA;MACA;MACA;QACAc;MACA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACAC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;QACA;UAAAL;QAAA;MACA;IACA;IACA;IACAM;MAAA;MACA;QACA;UACA;UACAD;YACAE;UACA;QACA;UACAA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACA;YACAhB;cACA;cACAiB;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACAX;MACA;MACA;MACA;QACAV;QAAA;MACA;IACA;IACAsB;MACA;MACA;MACA;MACAvB;MACA;MACAW;MACA;MACA;MACA;QACAV;QAAA;MACA;MACA;MACA;QACA;QACA;UACAD;UACAA;QACA;MACA;QACA;UACA;UACAW;UACA;YACA;YACA;cACAX;cACAA;YACA;cACAA;cACAA;YACA;UACA;QACA;MACA;IACA;IACAwB;MAAA;MACA;MACA;QACAvB;QACA;MACA;MACA;MACA;QACAA;QAAA;MACA;MAEA;MACA;MACA;MACA;MACAU;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAc;QACA;QACA;UACA;YACAxB;YAAA;UACA;QACA;QACA;UACA;YACAwB;UACA;YACAA;UACA;QACA;QACA;UACAA;QACA;QACA;UACA;YAAA;YACA;cACAxB;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;QACA;QACAhC;MACA;;MAEA;MACA;QACAgC;QAAA;MACA;MACA;MACA;QACA;UACA;UACA;UACA;UACA;YACA;cACAyB;cACArD;cACAsD;YACA;UACA;UACA;YACA1B;YACA;UACA;QACA;MACA;QACAhC;QACAwD;MACA;MAEA;QAEA;QACAd;QACA;UACAV;UAAA;QACA;MACA;;MAEA;MACA;QACAD;MACA;MACAC;MAEA;MACA;MACA;MACA;MAEA;MACA;QACA2B;MACA;MAEA;MACA;QACA;UACAC;QACA;MACA;MACAJ;MACAA;MACA;QACAjB;QACAvC;QACA6D;QACAC;QACArC;QACAC;QACAkC;QACAF;QACAtD;QACAiB;QACAM;MACA;MACAK;QACAD;QACAC;QACA;UACA;UACA+B;YACA/B;UACA;UACAD;UACA;QACA;UAAA;UACA;YACAA;YACAC;YAAA;UACA;UACAD;YACAgC;cACA/B;YACA;YACA+B;cACA/B;YACA;UACA;UACA;QACA;UACAD;YACAgC;cACA/B;YACA;UACA;QACA;QACAD;MACA;IACA;IACAiC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAhC;QAAA;MACA;MACA;MACAA;QACA;UACA;UACA;YACAiC;UACA;UACA;YACAA;UACA;UACAvB;UACAhD;UACAqC;UACAW;UACAA;UACAX;UACA;UACAA;QACA;UACArC;UACAgD;UACAX;UACAA;UAEA;UACAA;QACA;MAGA;IACA;IACAmC;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;QACA;QACAD;QACAvE;QACAqC;QACAA;QACAA;MACA;QACArC;QACAqC;QACAA;QACAA;MACA;IACA;IACAoC;MACA;MACA;MACA;MACA;MACAzE;MACA;MACA;MACA;MACA;MACA;MACA;QACAsC;QAAA;MACA;IACA;IACAoC;MACA;MACA;MACA;MACA;MACA;MACA1E;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;QACAsC;QAAA;MACA;MACA;MACA;QACA;QACAU;QACA;UACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;MACA;IAEA;IACA2B;MACA;MACA;MACA;MACA;QACAtC;QACAA;QACAA;QACA;MACA;MACA;QACAC;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAsC;QAAAjC;UACAK;UACAA;UACA;UACA;UACAV;YAAAuC;YAAAC;YAAAC;YAAAC;UAAA;YACA;cACA3C;cACAA;cACAA;cACAA;YACA;cACAC;YACA;YACA;UACA;QACA;MAAA;IACA;IACA2C;MACA;MACA;MAMAzC;QACAD;QACAI;UACA;UACA;YACAH;cACA0C;cACAC;cACAxC;gBACAK;cACA;YACA;UACA;QACA;MACA;IAEA;IACAoC;MACA;MACA;MACA;MAEA;MACA;MAEA;MACA;MACA;QACA9C;QAAA;MACA;MA6CAsC;QACAS;QACA;QACA1C;UACA;UACA;UACAK;UAEA;YACA;YACA;cACAsC;cACA;gBACAhD;gBAAA;cACA;YACA;UACA;;UAEA;UACAA;UACAU;UACAR;YACAD;YACA2C;YACAK;YACA5C;cACAL;cACA;cACA;gBACAD;gBAEArC;gBACAqC;gBACAA;cACA;gBACAC;cACA;YACA;YACAkD;cACAlD;cACAA;YACA;UACA;UACA;QACA;QACAmD;UACAzC;QACA;MACA;IAEA;IACA0C;MACA;MACA;MACA;MACA;QACA;QACA;UACAhF;UACAiF;QACA;QACAtD;QACAA;MACA;MACAA;IACA;IACAuD;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;QACA;UACAnF;QACA;MACA;MACA2B;MACAA;MACAA;IACA;IACAyD;MACA;MACA;MACA;MACA;MACA;MACA;QACAxD;QAAA;MACA;MACA;MACA;MACA;MACAE;QACAuD;QACApD;UACA;UACA;YACA;YACA;cACA2C;cACA;gBACAhD;gBAAA;cACA;YACA;UACA;UAEAA;UACAU;UACAR;YACAD;YACA2C;YACAK;YACA5C;cACAL;cACA;cACA;gBACAD;gBAEArC;gBACAqC;gBACAA;cACA;gBACAC;cACA;YACA;YACAkD;cACAlD;cACAA;YACA;UACA;QACA;MACA;IACA;IACA0D;MACA;MACA;MACA;MACA;MACA;QACA1D;QAAA;MACA;MACAE;QACAG;UACAK;UACAX;UACA;UACAA;UACAA;UACAA;UACAA;QACA;QACAmD;UACAxC;UACA;YACA;YACAV;cACAE;YACA;UACA;QACA;MACA;IACA;IACAyD;MACA;MACA;QACA;MACA;QACA;MACA;MACAjD;IACA;IACAkD;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;;MAEA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;QACA9E;QACAD;QACA;UACAC;UACAD;QACA;UACAC;UACAD;QACA;MACA;QACA;UACAC;UACAD;QACA;UACAC;UACAD;QACA;MACA;MACA;MACA;QACA;QACA;UACAE;QACA;MACA;MACAU;MACAA;MACAA;MACAA;IACA;IACAoE;MACA;MACA;MACA;QACA7E;MACA;QACAA;MACA;MACA;QACAA;MACA;MACAS;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjrCA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-form/dp-form.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-form.vue?vue&type=template&id=7a956e60&\"\nvar renderjs\nimport script from \"./dp-form.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-form.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-form.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-form/dp-form.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-form.vue?vue&type=template&id=7a956e60&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.xystatus == 1 && _vm.xytitlePos == \"top\" ? _vm.t(\"color1\") : null\n  var m1 = _vm.xystatus == 1 && _vm.xytitlePos == \"top\" ? _vm.t(\"color1\") : null\n  var l1 = _vm.__map(_vm.data.content, function (item, idx) {\n    var $orig = _vm.__get_orig(item)\n    var l0 =\n      (!item.linkitem || item.linkshow) && item.key == \"checkbox\"\n        ? _vm.__map(item.val2, function (item1, idx1) {\n            var $orig = _vm.__get_orig(item1)\n            var m2 =\n              _vm.formdata[\"form\" + idx] &&\n              _vm.inArray(item1, _vm.formdata[\"form\" + idx])\n            return {\n              $orig: $orig,\n              m2: m2,\n            }\n          })\n        : null\n    var g0 =\n      (!item.linkitem || item.linkshow) &&\n      item.key == \"upload_pics\" &&\n      _vm.editorFormdata &&\n      _vm.editorFormdata[idx]\n        ? _vm.editorFormdata[idx].join(\",\")\n        : null\n    return {\n      $orig: $orig,\n      l0: l0,\n      g0: g0,\n    }\n  })\n  var l2 =\n    _vm.data.payset == 1 && _vm.data.is_other_fee == 1\n      ? _vm.__map(_vm.data.fee_items, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var m4 =\n    _vm.xystatus == 1 && _vm.xytitlePos == \"bottom\" ? _vm.t(\"color1\") : null\n  var m5 =\n    _vm.xystatus == 1 && _vm.xytitlePos == \"bottom\" ? _vm.t(\"color1\") : null\n  var m6 = _vm.showxieyi ? _vm.t(\"color1\") : null\n  var m7 = _vm.showxieyi ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        l1: l1,\n        l2: l2,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-form.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-form\" :style=\"{\r\n\tcolor:params.color,\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',\r\n  marginTop:(params.margin_top*2.2)+(params.margin_y*2.2)+'rpx',\r\n\tpadding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',\r\n\tfontSize:(params.fontsize*2)+'rpx'\r\n}\">\r\n\r\n\t<view class=\"submember\" v-if=\"data.submember_data && params.show_submember == 1\">\r\n\t\t<view class=\"submember-title\">报名会员已有：<text style=\"color: red\">{{data.submember_data_sum}}</text>人</view>\r\n\t\t\r\n\t\t<view class=\"member-items\" v-if=\"data.submember_data_sum > 0\">\r\n\t\t\t<view class=\"member-item\" v-for=\"(i,d) in data.submember_data\" :key=\"d\">\r\n\t\t\t\t<image :src=\"i.headimg || pre_url+'/static/img/touxiang.png'\" class=\"avatar\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"member-item more\" v-if=\"data.submember_data_sum > 20\" @tap=\"goto\" :data-url=\"'/pagesB/form/submember?id='+data.id\">\r\n\t\t\t\t<view class=\"more-text\">更多</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"empty\" v-else>暂无报名会员</view>\r\n\t</view>\r\n\t<form @submit=\"editorFormSubmit\" :data-formcontent=\"data.content\" :data-tourl=\"params.hrefurl\" :data-formid=\"data.id\">\r\n\t\t<view style=\"display:none\">{{test}}</view>\r\n\t\t\r\n\t\t<block v-if=\"xystatus==1 && xytitlePos=='top'\">\r\n\t\t\t<view class=\"xycss1\">\r\n\t\t\t  <checkbox-group @change=\"isagreeChange\" style=\"display: inline-block;\">\r\n\t\t\t\t  <checkbox style=\"transform: scale(0.6)\"  value=\"1\" :checked=\"isagree\" :color=\"t('color1')\"/>\r\n\t\t\t  </checkbox-group>\r\n\t\t\t\t<text>我已阅读并同意</text>\r\n\t\t\t  <text @tap=\"showxieyiFun\" :style=\"{color:t('color1')}\">{{xytitle}}</text>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<view :class=\"params.style==1?'dp-form-item':'dp-form-item2'\" v-for=\"(item,idx) in data.content\" :style=\"{borderColor:params.linecolor,background:item.bgcolor?item.bgcolor:'transparent'}\" :key=\"item.id\" v-if=\"!item.linkitem || item.linkshow\">\r\n\t\t\t<block v-if=\"item.key=='separate'\">\r\n\t\t\t\t<view class=\"dp-form-separate\" :class=\"item.val8=='1'?'dp-form-blod':''\">{{item.val1}}</view>\r\n\t\t\t</block>\r\n\t\t\t<view v-if=\"item.key!='separate'\" class=\"label\" :class=\"item.val8=='1'?'dp-form-blod':''\">{{item.val1}}<text v-if=\"item.val3==1&&params.showmuststar\" style=\"color:red\"> *</text></view>\r\n\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t<block v-if=\"params.style==1\">\r\n\t\t\t\t\t<text v-if=\"item.val5\" style=\"margin-right:10rpx\">{{item.val5}}</text>\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<block v-if=\"item.val4==2 && item.val6==1\">\r\n\t\t\t\t\t\t<input @focus=\"inputFocus\" @blur=\"inputBlur\" :type=\"(item.val4==1 || item.val4==2) ? 'digit' : 'text'\" disabled=\"true\" :name=\"'form'+idx\" class=\"input disabled\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':'#efefef'}\" :value=\"formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\r\n\t\t\t\t\t\t<button class=\"authtel\" :style=\"{backgroundColor:params.btnbgcolor,color:params.btncolor}\"  open-type=\"getPhoneNumber\" type=\"primary\" @getphonenumber=\"getPhoneNumber\" :data-idx=\"idx\">一键填写</button>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<input :adjust-position=\"false\"\t@focus=\"inputFocus\" @blur=\"inputBlur\"\t:type=\"(item.val4==1 || item.val4==2) ? 'digit' : 'text'\"\treadonly\t:name=\"'form'+idx\" \tclass=\"input\" :class=\"'form'+idx\"\t:placeholder=\"item.val2\" \tplaceholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}\" :value=\"formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t\t\t\t<block>\r\n\t\t\t\t\t\t<input :type=\"(item.val4==1 || item.val4==2) ? 'digit' : 'text'\" readonly :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}\" :value=\"formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<view v-if=\"params.style==2\" class=\"value\">\r\n\t\t\t\t\t<text v-if=\"item.val5\" style=\"margin-right:10rpx\">{{item.val5}}</text>\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<block v-if=\"item.val4==2 && item.val6==1\">\r\n\t\t\t\t\t\t<input :type=\"(item.val4==1 || item.val4==2) ? 'digit' : 'text'\" disabled=\"true\" :name=\"'form'+idx\" class=\"input disabled\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':'#efefef'}\" :value=\"formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\r\n\t\t\t\t\t\t<button class=\"authtel\" :style=\"{backgroundColor:params.btnbgcolor,color:params.btncolor}\"  open-type=\"getPhoneNumber\" type=\"primary\" @getphonenumber=\"getPhoneNumber\" :data-idx=\"idx\">一键填写</button>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<input :type=\"(item.val4==1 || item.val4==2) ? 'digit' : 'text'\" readonly :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}\" :value=\"formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t\t\t\t<block>\r\n\t\t\t\t\t\t<input :type=\"(item.val4==1 || item.val4==2) ? 'digit' : 'text'\" readonly :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}\" :value=\"formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t<textarea :adjust-position=\"false\" @focus=\"inputFocus\"\t @blur=\"inputBlur\" :name=\"'form'+idx\" class='textarea' :class=\"'form'+idx\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}\" :value=\"formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t<radio-group :name=\"'form'+idx\" :class=\"item.val10=='1'?'rowalone':'flex'\" style=\"flex-wrap:wrap\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\" :class=\"[item.val11=='1'?'checkborder':'',item.val10=='1'?'':'rowmore']\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor,padding:'0 10rpx',marginTop:'10rpx',borderRadius: '10rpx'}\" @tap=\"selradio\" :data-idx=\"idx\" :data-index=\"idx1\" :data-paymoney=\"data.radio_paymoney==1 && item.val19?item.val19[idx1]:0\" :data-value=\"item1\">\r\n\t\t\t\t\t\t\t<radio  class=\"radio\" :value=\"item1\" :checked=\"formdata['form'+idx] && formdata['form'+idx]==item1 ? true : false\" />{{item1}}\r\n\t\t\t\t\t</label>\r\n\t\t\t\t</radio-group>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t<checkbox-group :name=\"'form'+idx\" :class=\"item.val4=='1'?'rowalone':'flex'\" style=\"flex-wrap:wrap\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\" :class=\"[item.val9=='1'?'checkborder':'',item.val4=='1'?'':'rowmore']\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor,padding:'0 10rpx',marginTop:'10rpx',borderRadius: '10rpx'}\">\r\n\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\" :checked=\"formdata['form'+idx] && inArray(item1,formdata['form'+idx]) ? true : false\"/>{{item1}}\r\n\t\t\t\t\t</label>\r\n\t\t\t\t</checkbox-group>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t<view class=\"flex-y-center flex-bt\" v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\">\r\n\t\t\t\t\t\t<text>{{item.val2[editorFormdata[idx]]}}</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-form-normal flex-y-center flex-bt\" v-else>\r\n\t\t\t\t\t\t<text>请选择</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" :value=\"formdata['form'+idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t<view class=\"flex-y-center flex-bt\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t<text>{{editorFormdata[idx]}}</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-form-normal flex-y-center flex-bt\" v-else>\r\n\t\t\t\t\t\t<text>请选择</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" :value=\"formdata['form'+idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t<view class=\"flex-y-center flex-bt\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t<text>{{editorFormdata[idx]}}</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-form-normal flex-y-center flex-bt\" v-else>\r\n\t\t\t\t\t\t<text>请选择</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='year'\">\r\n\t\t\t\t<picker class=\"picker\" :name=\"'form'+idx\" :value=\"formdata['form'+idx]\" @change=\"yearChange\" :data-idx=\"idx\" :range=\"yearList\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t<view class=\"flex-y-center flex-bt\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t<text>{{editorFormdata[idx]}}</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dp-form-normal flex-y-center flex-bt\" v-else>\r\n\t\t\t\t\t\t<text>请选择</text>\r\n\t\t\t\t\t\t<view class=\"arrow-area\">\r\n\t\t\t\t\t\t\t<view class=\"input-arrow\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</block>\r\n\r\n\t\t\t<block v-if=\"item.key=='region'\">\r\n\t\t\t\t<uni-data-picker style=\"flex: 1;width: 100%;\" :localdata=\"items\" popup-title=\"请选择省市区\" :placeholder=\"formdata['form'+idx] || '请选择省市区'\" @change=\"onchange\" :data-formidx=\"'form'+idx\"></uni-data-picker>\r\n\t\t\t\t<!-- <picker class=\"picker\" mode=\"region\" :name=\"'form'+idx\" value=\"\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view> \r\n\t\t\t\t\t<view v-else>请选择省市区</view>\r\n\t\t\t\t</picker> -->\r\n\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"regiondata ? regiondata : formdata['form'+idx]\"/>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t<view class=\"dp-form-imgbox\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t<view class=\"dp-form-imgbox-close\" @tap=\"removeimg\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image></view>\r\n\t\t\t\t\t\t<view class=\"dp-form-imgbox-img\"><image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"aspectFit\" :data-idx=\"idx\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n      <!-- #ifdef H5 || MP-WEIXIN -->\r\n      <block v-if=\"item.key=='upload_file'\">\r\n        <input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\r\n        <view class=\"flex-y-center\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n          <view class=\"dp-form-imgbox\" v-if=\"editorFormdata[idx]\">\r\n            <view class=\"dp-form-imgbox-close\" @tap=\"removeimg\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image>\r\n\t\t\t\t\t\t</view>\r\n            <view  style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;width: 530rpx;\" @tap=\"download\" :data-file=\"editorFormdata[idx]\" >\r\n\t\t\t\t\t\t\t文件已上传成功\r\n\t\t\t\t\t\t</view>\r\n          </view>\r\n          <view v-else class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"chooseFile\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\" style=\"margin-right:20rpx;\"></view>\r\n\t\t\t\t\t<view v-if=\"item.val2\" style=\"color:#999\">{{item.val2}}</view>\r\n        </view>\r\n      </block>\r\n      <!-- #endif -->\r\n      <block v-if=\"item.key=='upload_video'\">\r\n        <input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\r\n        <view class=\"flex-y-center\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n          <view class=\"dp-form-imgbox\" v-if=\"editorFormdata[idx]\">\r\n            <view class=\"dp-form-imgbox-close\" @tap=\"removeimg\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n                <image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image>\r\n            </view>\r\n            <view  style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;width: 430rpx;\">\r\n                <video  :src=\"editorFormdata[idx]\" style=\"width: 100%;\"/></video>\r\n            </view>\r\n          </view>\r\n          <view v-else class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"upVideo\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\" style=\"margin-right:20rpx;\"></view>\r\n\t\t\t\t\t<view v-if=\"item.val2\" style=\"color:#999\">{{item.val2}}</view>\r\n        </view>\r\n      </block>\r\n      <block v-if=\"item.key=='map'\">\r\n        <input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\r\n        <view class=\"flex-y-center\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n            <text class=\"flex1\" style=\"text-align:right\" :style=\"area ? '' : 'color:#BBBBBB'\" @click=\"selectzuobiao\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\" >{{area ? area : '请选择您的位置'}}</text>\r\n        </view>\r\n      </block>\r\n      <block v-if=\"item.key=='upload_pics'\">\r\n      \t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata && editorFormdata[idx]?editorFormdata[idx].join(','):''\" maxlength=\"-1\"/>\r\n      \t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n      \t\t<view v-for=\"(item2, index2) in editorFormdata[idx]\" :key=\"index2\" class=\"dp-form-imgbox\" >\r\n      \t\t\t<view class=\"dp-form-imgbox-close\" @tap=\"removeimg\" :data-index=\"index2\" data-type=\"pics\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image></view>\r\n      \t\t\t<view class=\"dp-form-imgbox-img\" style=\"margin-bottom: 10rpx;\"><image class=\"image\" :src=\"item2\" @click=\"previewImage\" :data-url=\"item2\" mode=\"aspectFit\" :data-idx=\"idx\"/></view>\r\n      \t\t</view>\r\n      \t\t<view class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3',marginBottom: '10rpx'}\" @click=\"editorChooseImage\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\" data-type=\"pics\"></view>\r\n      \t</view>\r\n      </block>\r\n\t\t</view>\r\n\t\t<block v-if=\"data.payset==1\">\r\n      \r\n\t\t\t<block v-if=\"data.is_other_fee==1\">\r\n        <view :class=\"params.style==1?'dp-form-item-fee':'dp-form-item-fee2'\" style=\"border: none;\">\r\n          <view class=\"dp-form-label\">费用明细</view>\r\n          <view class=\"dp-form-feelist\">\r\n          <checkbox-group name=\"feelist\">\r\n            <view class=\"dp-fee-item\" v-for=\"(item,index) in data.fee_items\" :key=\"index\">\r\n              <view class=\"dp-fee-name\">{{item.name}}</view>\r\n              <view class=\"dp-fee-money\">{{item.money}}元</view>\r\n              <view class=\"dp-fee-check\"><checkbox @click=\"feeChange\" :data-index=\"index\" :value=\"''+index\" :checked=\"item.checked?true:false\" :color=\"t('color1')\" style=\"transform: scale(0.7);\"></checkbox></view>\r\n            </view>\r\n          </checkbox-group>\r\n          <!-- <view class=\"dp-fee-item sum\">\r\n            <view class=\"dp-fee-name\" style=\"width:200rpx ;flex: unset;\">费用明细合计：</view>\r\n            <view class=\"dp-fee-money\"><text>{{feetotal}}</text>元</view>\r\n            <view class=\"dp-fee-check\"></view>\r\n          </view> -->\r\n          </view>\r\n        </view>\r\n\t\t\t</block>\r\n      <block v-else>\r\n        <view class=\"dp-form-item\" v-if=\"data.priceedit==1 || (data.priceedit!=1 && data.price>0)\" style=\"border: none;padding-left: 32rpx;\">\r\n          <text class=\"dp-form-label\" style=\"font-weight: bold;width:200rpx ;\">支付金额：</text>\r\n          <input type=\"text\" class=\"input\" name=\"price\" :value='data.price' v-if=\"data.priceedit==1\" @input=\"setfield\" data-formidx=\"price\" style=\"font-weight: bold;\"/>\r\n          <text v-if=\"data.priceedit==0\" style=\"font-weight: bold;\">{{data.price}}</text>\r\n          <text style=\"font-weight: bold;\">元</text>\r\n        </view>\r\n      </block>\r\n      <view v-if=\"data.is_other_fee==1 || (data.radio_paymoney==1 && radiopaymoney>0)\" :class=\"params.style==1?'dp-form-item-fee':'dp-form-item-fee2'\" style=\"border: none;\">\r\n        <view class=\"dp-form-feelist\">\r\n          <view class=\"dp-fee-item sum\">\r\n            <view class=\"dp-fee-name\" style=\"width:200rpx ;flex: unset;\">费用合计：</view>\r\n            <view class=\"dp-fee-money\">\r\n              <text >{{totalprice}}</text>元\r\n            </view>\r\n            <view class=\"dp-fee-check\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\t\t</block>\r\n\t\t<block v-if=\"xystatus==1 && xytitlePos=='bottom'\">\r\n\t\t\t<view class=\"xycss1 dp-form-item\">\r\n\t\t\t  <checkbox-group @change=\"isagreeChange\" style=\"display: inline-block;\">\r\n\t\t\t\t  <checkbox style=\"transform: scale(0.6)\"  value=\"1\" :checked=\"isagree\" :color=\"t('color1')\"/>\r\n\t\t\t  </checkbox-group>\r\n\t\t\t\t<text>我已阅读并同意</text>\r\n\t\t\t  <text @tap=\"showxieyiFun\" :style=\"{color:t('color1')}\">{{xytitle}}</text>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t\t\t\r\n\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\r\n\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t<parse :content=\"xycontent\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi\">{{agree_button}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n    <!--合同签名-->\r\n    <block v-if=\"data.is_ht && data.is_contract == 1 && data.is_contract_sign == 1\">\r\n      <view class=\"dp-form-separate button\" style=\"margin-top: 20rpx;\">\r\n        <text  v-if=\"!htsignatureurl\" style=\"padding: 25rpx 41%;\" :style=\"{backgroundColor:params.btnbgcolor,color:params.btncolor,borderRadius:(params.btnradius*2.2)+'rpx',fontSize:(params.btnfontsize*2)+'rpx',}\" @tap=\"goto\" data-url=\"/pagesB/form/signature\">在线签名</text>\r\n        <text v-else style=\"padding: 25rpx 43%;color:green\" :style=\"{backgroundColor:params.btnbgcolor,borderRadius:(params.btnradius*2.2)+'rpx',fontSize:(params.btnfontsize*2)+'rpx',}\">已签名</text>\r\n      </view>\r\n    </block>\r\n\r\n\t\t<button @tap=\"editorFormSubmit\" v-if=\"data != ''\" class=\"dp-form-btn flex-xy-center\" :style=\"{backgroundColor:params.btnbgcolor,border:'1px solid '+params.btnbordercolor,fontSize:(params.btnfontsize*2)+'rpx',color:params.btncolor,width:(params.btnwidth*2.2)+'rpx',height:(params.btnheight*2.2)+'rpx',lineHeight:(params.btnheight*2.2)+'rpx',borderRadius:(params.btnradius*2.2)+'rpx'}\" :data-formcontent=\"data.content\" :data-tourl=\"params.hrefurl\" :data-formid=\"data.id\">{{params.btntext}}</button>\r\n\t\t<view :style=\"{height:`${keyboardHeight}`+'px'}\"></view>\r\n\t</form>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tpre_url:getApp().globalData.pre_url,\r\n\t\t\t\teditorFormdata:[],\r\n\t\t\t\ttest:'test',\r\n\t\t\t\tregiondata:'',\r\n\t\t\t\titems: [],\r\n\t\t\t\ttmplids: [],\r\n\t\t\t\tsubmitDisabled:false,\r\n\t\t\t\tformdata:{},\r\n\t\t\t\tformvaldata:{},\r\n\t\t\t\tauthphone:'',\r\n\t\t\t\tplatform:'',\r\n\t\t\t\tfeetotal:0,\r\n\t\t\t\tyearList:[],\r\n        area: '',\r\n        adr_lon:'',\r\n        adr_lat:'',\r\n\t\t\t\ttimer:'',\r\n\t\t\t\tkeyboardHeight:'0',\r\n\t\t\t\txystatus:0,\r\n\t\t\t\tshowxieyi:false,\r\n\t\t\t\txytitle:'',\r\n\t\t\t\txycontent:'',\r\n\t\t\t\txytitlePos:'bottom',\r\n\t\t\t\tisagree:false,\r\n\t\t\t\tagree_button:'',\r\n        mid:app.globalData.mid,\r\n        radios:[],//单选集合\r\n        radiokeys:[],//单选key集合\r\n        radiopaymoney:0,//单选支付金额\r\n        totalprice:0,//总费用合计\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{},\r\n\t\t\tlatitude:'',\r\n\t\t\tlongitude:'',\r\n      htsignatureurl:'',\r\n\t\t},\r\n    updated:function(){\r\n      this.calculatePrice();\r\n    },\r\n\t\tmounted:function(){\r\n\t\t\tvar that = this;\r\n      //app.setCache(that.mid+'htsignatureurl','');\r\n\t\t\tlet year = [];\r\n\t\t\tfor(let i=0;i<that.data.content.length;i++){\r\n\t\t\t\tif(that.data.content[i].key=='year'){\r\n\t\t\t\t\tfor(let j=that.data.content[i].val2[0];j<=that.data.content[i].val2[1];j++){\r\n\t\t\t\t\t\tyear.push(j);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.yearList = year.reverse();\r\n\t\t\tthat.platform = app.getplatform();\r\n\t\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\r\n\t\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\r\n\t\t\t\tif(customs.data.includes('plug_zhiming')) {\r\n\t\t\t\t\turl = app.globalData.pre_url+'/static/area_gaoxin.json';\r\n\t\t\t\t}\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\r\n\t\t\t\t\tdata: {},\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\tthat.checkPayMoney()\r\n\r\n\t\t\tvar pages = getCurrentPages(); //获取加载的页面\r\n\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n\t\t\tvar thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \r\n\t\t\tvar opts = currentPage.$vm.opt;\r\n\t\t\tvar fromrecord = 0;\r\n\t\t\tif(opts && opts.fromrecord){\r\n\t\t\t\tfromrecord = opts.fromrecord;\r\n\t\t\t}\r\n\t\t\tthat.xystatus = that.data.show_agree;\r\n\t\t\tthat.xycontent = that.data.agree_desc;\r\n\t\t\tthat.xytitle = that.data.agree_title;\r\n\t\t\tthat.agree_button = that.data.agree_button;\r\n\t\t\tthat.xytitlePos = that.data.agree_title_pos;\r\n\t\t\tapp.get('ApiForm/getlastformdata',{formid:that.data.id,fromrecord:fromrecord}, function (res) {\r\n\t\t\t\tif(res && res.status == 1 && res.data){\r\n\t\t\t\t\tvar formcontent = that.data.content;\r\n\t\t\t\t\tvar editorFormdata = [];\r\n\t\t\t\t\tvar formvaldata = {};\r\n\t\t\t\t\tformvaldata.price = that.data.price\r\n\t\t\t\t\tfor(var i in formcontent){\r\n\t\t\t\t\t\tvar thisval = res.data['form'+i];\r\n\t\t\t\t\t\tif (formcontent[i].key == 'region') {\r\n\t\t\t\t\t\t\tthat.regiondata = thisval;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (formcontent[i].key == 'selector') {\r\n\t\t\t\t\t\t\tfor(var j in formcontent[i].val2){\r\n\t\t\t\t\t\t\t\tif(formcontent[i].val2[j] == res.data['form'+i]){\r\n\t\t\t\t\t\t\t\t\tthisval = j;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (formcontent[i].key == 'checkbox') {\r\n\t\t\t\t\t\t\tif(res.data['form'+i]){\r\n\t\t\t\t\t\t\t\tres.data['form'+i] = (res.data['form'+i]).split(',');\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tres.data['form'+i] = [];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\teditorFormdata.push(thisval);\r\n\t\t\t\t\t\tformvaldata['form'+i] = thisval;\r\n\r\n\t\t\t\t\t\tif(formcontent[i].key == 'radio' || formcontent[i].key=='selector'){\r\n\t\t\t\t\t\t\tvar linkitem = formcontent[i].val1 + '|' + formcontent[i].val2[thisval];\r\n\t\t\t\t\t\t\tconsole.log(linkitem)\r\n\t\t\t\t\t\t\tfor(var j in that.data.content){\r\n\t\t\t\t\t\t\t\tvar thislinkitem = that.data.content[j].linkitem;\r\n\t\t\t\t\t\t\t\tif(thislinkitem == linkitem){\r\n\t\t\t\t\t\t\t\t\tthat.data.content[j].linkshow = true;\r\n\t\t\t\t\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t\t\t\t\t}else if(thislinkitem && thislinkitem.split('|')[0] == formcontent[i].val1){\r\n\t\t\t\t\t\t\t\t\tthat.data.content[j].linkshow = false;\r\n\t\t\t\t\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.editorFormdata = editorFormdata;\r\n\t\t\t\t\tthat.formvaldata = formvaldata;\r\n\t\t\t\t\tthat.formdata = res.data;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvar formvaldata = {};\r\n\t\t\t\t\tformvaldata.price = that.data.price;\r\n\t\t\t\t\tthat.formvaldata = formvaldata;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\t// 失去焦点\r\n\t\t\tinputBlur(){\r\n\t\t\t\tthis.$set(this,'keyboardHeight',0)\r\n\t\t\t},\r\n\t\t\t// 获取焦点\r\n\t\t\tinputFocus(event){\r\n\t\t\t\t//判断活动是否结束\r\n\t\t\t\tif(this.data.is_endtime == 1){\r\n\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t}\r\n\t\t\t\t// if(this.props.data)\r\n\t\t\t\tif (this.timer) {\r\n\t\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t}\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.timer = null\r\n\t\t\t\t\tconst height = event.detail.height; //键盘高度\r\n\t\t\t\t\tconst formidx = event.target.dataset.formidx\r\n\t\t\t\t\tif(height === 0){\r\n\t\t\t\t\t\tthis.scrollToInput(0);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\ttry{\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tquery.select(`.${formidx}`).boundingClientRect((res) => {\r\n\t\t\t\t\t\tconst windowHeight = uni.getSystemInfoSync().windowHeight;\r\n\t\t\t\t\t\t// 除去键盘的剩余高度\r\n\t\t\t\t\t\tlet restHeight = windowHeight - height;\r\n\t\t\t\t\t\t// 元素左下角坐标\r\n\t\t\t\t\t\tlet bottom = res.bottom;\r\n\t\t\t\t\t\t// 只有当元素被软键盘覆盖的时候才上推页面\r\n\t\t\t\t\t\tif (bottom <= restHeight) return;\r\n\t\t\t\t\t\t// 现阶段需要滚动的大小\r\n\t\t\t\t\t\tlet scrollTop = bottom - restHeight;\r\n\t\t\t\t\t\tthis.scrollToInput(height, scrollTop);\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t} catch(err){console.log(err)}\r\n\t\t\t\t},300)\r\n\t\t\t},\r\n\t\t\t// 获取页面滚动条位置\r\n\t\t\tgetScrollOffset() {\r\n\t\t\t  return new Promise((resolve) => {\r\n\t\t\t    try {\r\n\t\t\t     const query = uni.createSelectorQuery().in(this);\r\n\t\t\t        query.selectViewport().scrollOffset((res) => {\r\n\t\t\t          resolve(res.scrollTop);\r\n\t\t\t        }).exec();\r\n\t\t\t    } catch (error) {\r\n\t\t\t      resolve(0);\r\n\t\t\t    }\r\n\t\t\t  });\r\n\t\t\t},\r\n\t\t\t// 监听页面键盘弹起推动页面\r\n\t\t\tscrollToInput(height,scrollTop){\r\n\t\t\t\tthis.$set(this,'keyboardHeight',height)\r\n\t\t\t\t if (scrollTop) {\r\n\t\t\t\t    try {\r\n\t\t\t\t      this.getScrollOffset().then((lastScrollTop) => {\r\n\t\t\t\t        uni.pageScrollTo({\r\n\t\t\t\t          // 如果已经存在滚动，在此基础上继续滚\r\n\t\t\t\t          scrollTop: lastScrollTop ? lastScrollTop + scrollTop : scrollTop,\r\n\t\t\t\t          duration: 0,\r\n\t\t\t\t        });\r\n\t\t\t\t      });\r\n\t\t\t\t    } catch (error) {}\r\n\t\t\t\t  }\r\n\t\t\t},\r\n\t\t\tonchange(e) {\r\n        const value = e.detail.value\r\n\t\t\t\tconsole.log(value[0].text + ',' + value[1].text + ',' + value[2].text)\r\n\t\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;\r\n\t\t\t\t//判断活动是否结束\r\n\t\t\t\tif(this.data.is_endtime == 1){\r\n\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t}\r\n      },\r\n\t\t\tsetfield:function(e){\r\n        var that = this;\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tvar value = e.detail.value;\r\n\t\t\t\tthat.formvaldata[field] = value;\r\n\t\t\t\tvar idx = field.replace('form','');\r\n\t\t\t\tconsole.log(idx);\r\n\t\t\t\tvar thiscontent = that.data.content[idx]\r\n\t\t\t\t//判断活动是否结束\r\n\t\t\t\tif(that.data.is_endtime == 1){\r\n\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t}\r\n        //如果是输入价格\r\n        if(field == 'price'){\r\n          //可自定义价格\r\n          if(that.data.priceedit==1){\r\n            that.data.price = value;\r\n            that.calculatePrice();\r\n          }\r\n        }else{\r\n          if(thiscontent.key == 'radio' || thiscontent.key=='selector'){\r\n          \tvar linkitem = thiscontent.val1 + '|' + value;\r\n          \tconsole.log(linkitem)\r\n          \tfor(var i in that.data.content){\r\n          \t\tvar thislinkitem = that.data.content[i].linkitem;\r\n          \t\tif(thislinkitem == linkitem){\r\n          \t\t\tthat.data.content[i].linkshow = true;\r\n          \t\t\tthat.test = Math.random();\r\n          \t\t}else if(thislinkitem && thislinkitem.split('|')[0] == thiscontent.val1){\r\n          \t\t\tthat.data.content[i].linkshow = false;\r\n          \t\t\tthat.test = Math.random();\r\n          \t\t}\r\n          \t}\r\n          }\r\n        }\r\n\t\t\t},\r\n\t\t\teditorFormSubmit:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\t  app.error('请先阅读并同意'+that.xytitle);\r\n\t\t\t\t  return false;\r\n\t\t\t\t}\r\n\t\t\t\t//判断活动是否结束\r\n\t\t\t\tif(that.data.is_endtime == 1){\r\n\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif(this.submitDisabled && app.globalData.mid > 0) return ;\r\n\t\t\t\t//console.log('form发生了submit事件，携带数据为：', e.detail.value)\r\n\t\t\t\tvar subdata = e.detail.value;\r\n\t\t\t\tvar subdata = JSON.parse(JSON.stringify(this.formvaldata));\r\n\t\t\t\tconsole.log(subdata)\r\n\t\t\t\tvar formcontent = e.currentTarget.dataset.formcontent;\r\n\t\t\t\tvar formid = e.currentTarget.dataset.formid;\r\n\t\t\t\tvar tourl = e.currentTarget.dataset.tourl;\r\n\t\t\t\tvar formdata = new Array();\r\n\t\t\t\tfor (var i = 0; i < formcontent.length;i++){\r\n\t\t\t\t\t//console.log(subdata['form' + i]);\r\n\t\t\t\t\tif (formcontent[i].key == 'region') {\r\n\t\t\t\t\t\t\tsubdata['form' + i] = that.regiondata;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formcontent[i].key!='separate' && formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === null || subdata['form' + i] === undefined || subdata['form' + i].length==0)){\r\n\t\t\t\t\t\tif(formcontent[i].linkitem == '' || formcontent[i].linkshow){\r\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 必填');return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formcontent[i].key =='switch'){\r\n\t\t\t\t\t\t\tif (subdata['form' + i]==false){\r\n\t\t\t\t\t\t\t\t\tsubdata['form' + i] = '否'\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tsubdata['form' + i] = '是'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formcontent[i].key == 'selector') {\r\n\t\t\t\t\t\t\tsubdata['form' + i] = formcontent[i].val2[subdata['form' + i]]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formcontent[i].key == 'input' && formcontent[i].val4 && subdata['form' + i]!==''){\r\n\t\t\t\t\t\tif(formcontent[i].val4 == '2'){ //手机号\r\n\t\t\t\t\t\t\tif (!app.isPhone(subdata['form' + i])) {\r\n\t\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(formcontent[i].val4 == '3'){ //身份证号\r\n\t\t\t\t\t\t\tif (!/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(subdata['form' + i])) {\r\n\t\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(formcontent[i].val4 == '4'){ //邮箱\r\n\t\t\t\t\t\t\tif (!/^(.+)@(.+)$/.test(subdata['form' + i])) {\r\n\t\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tformdata.push(subdata['form' + i])\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t//范围\r\n\t\t\t\tif(that.data.fanwei == 1 && (that.latitude == '' || that.longitude == '')) {\r\n\t\t\t\t\tapp.alert('请定位您的位置或者刷新重试');return;\r\n\t\t\t\t}\r\n\t\t\t\tvar feedata = [];\r\n\t\t\t\tif(that.data.payset==1){\r\n          if(that.data.is_other_fee==1){\r\n            var feeitems = that.data.fee_items\r\n            var feenum = 0;\r\n            var feetotal  = 0;\r\n            for(let i in feeitems){\r\n            \tif(feeitems[i].checked){\r\n            \t\tfeenum++;\r\n            \t\tfeetotal = feetotal + parseFloat(feeitems[i].money);\r\n            \t\tfeedata.push(feeitems[i])\r\n            \t}\r\n            }\r\n            if(feenum<1){\r\n            \tapp.error('请选择费用明细');\r\n            \treturn;\r\n            }\r\n          }\r\n\t\t\t\t}else{\r\n          formdata.price = 0;\r\n          subdata.price  = 0;\r\n        }\r\n\r\n        if(that.data.is_ht && that.data.is_contract == 1 && that.data.is_contract_sign == 1){\r\n\r\n          //that.htsignatureurl = that.opt.htsignatureurl ?? '';\r\n          console.log(that.htsignatureurl)\r\n          if(!that.htsignatureurl || that.htsignatureurl == '' || that.htsignatureurl == undefined){\r\n            app.alert('请先签名');return;\r\n          }\r\n        }\r\n\r\n\t\t\t\t//console.log(formdata);\r\n\t\t\t\tif(app.globalData.mid > 0){\r\n\t\t\t\t\tthat.submitDisabled = true;\r\n\t\t\t\t}\r\n\t\t\t\tapp.showLoading('提交中');\r\n\r\n\t\t\t\tvar pages = getCurrentPages(); //获取加载的页面\r\n\t\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n\t\t\t\tvar thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \r\n\t\t\t\tvar opts = currentPage.$vm.opt;\r\n\r\n\t\t\t\tvar posturl = 'ApiForm/formsubmit';\r\n\t\t\t\tif(that.params.isquery == '1'){\r\n\t\t\t\t\tposturl = 'ApiForm/formquery';\r\n\t\t\t\t}\r\n                \r\n        var edit_id = 0;\r\n        if(opts && opts.fromrecord && opts.type){\r\n          if(opts.type == 'edit'){\r\n                edit_id = opts.fromrecord;\r\n            }\r\n        }\r\n        subdata.adr_lon = that.adr_lon;\r\n        subdata.adr_lat = that.adr_lat;\r\n        var data = {\r\n          formid:formid,\r\n          formdata:subdata,\r\n          price:subdata.price,\r\n          fromurl:thispath+'?id='+opts.id,\r\n          latitude:that.latitude,\r\n          longitude:that.longitude,\r\n          edit_id:edit_id,\r\n          feedata:feedata,\r\n          feetotal:that.feetotal,\r\n          radiopaymoney:that.radiopaymoney,\r\n          htsignatureurl:that.htsignatureurl ?? '',\r\n        }\r\n\t\t\t\tapp.post(posturl,data,function(data){\r\n\t\t\t\t\tthat.tmplids = data.tmplids;\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\t//that.showsuccess(res.data.msg);\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\t}, 100)\r\n\t\t\t\t\t\tthat.submitDisabled = false;\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}else if(data.status == 1) { //无需付款\r\n\t\t\t\t\t\tif(that.params.isquery == '1'){\r\n\t\t\t\t\t\t\tthat.submitDisabled = false;\r\n\t\t\t\t\t\t\tapp.goto(data.tourl);return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\t\t}, 100)\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tapp.goto(tourl);\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}else if(data.status==2){\r\n\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tapp.goto('/pagesExt/pay/pay?id='+data.payorderid+'&tourl='+tourl);\r\n\t\t\t\t\t\t\t}, 100);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.submitDisabled = false;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\teditorChooseImage: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n        //判断活动是否结束\r\n        if(this.data.is_endtime == 1){\r\n        \tapp.alert('活动已经结束');return;\r\n        }\r\n        var type = e.currentTarget.dataset.type;\r\n\t\t\t\tapp.chooseImage(function(data){\r\n          if(type == 'pics'){\r\n            var pics = editorFormdata[idx];\r\n            if(!pics){\r\n              pics = [];\r\n            }\r\n            for(var i=0;i<data.length;i++){\r\n            \tpics.push(data[i]);\r\n            }\r\n            console.log(pics)\r\n            editorFormdata[idx] = pics;\r\n            that.editorFormdata = editorFormdata\r\n            console.log(editorFormdata[idx])\r\n            console.log(editorFormdata)\r\n            that.test = Math.random();\r\n            var field = e.currentTarget.dataset.formidx;\r\n            that.formvaldata[field] = pics;\r\n          }else{\r\n            editorFormdata[idx] = data[0];\r\n            console.log(editorFormdata)\r\n            that.editorFormdata = editorFormdata\r\n            that.test = Math.random();\r\n            \r\n            var field = e.currentTarget.dataset.formidx;\r\n            that.formvaldata[field] = data[0];\r\n          }\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tremoveimg:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n        if(!editorFormdata) editorFormdata = [];\r\n        \r\n        var type  = e.currentTarget.dataset.type;\r\n        var index = e.currentTarget.dataset.index;\r\n        if(type == 'pics'){\r\n          var pics = editorFormdata[idx]\r\n          pics.splice(index,1);\r\n          editorFormdata[idx] = pics;\r\n          that.editorFormdata = editorFormdata\r\n          that.test = Math.random();\r\n          that.formvaldata[field] = pics;\r\n        }else{\r\n          editorFormdata[idx] = '';\r\n          that.editorFormdata = editorFormdata\r\n          that.test = Math.random();\r\n          that.formvaldata[field] = '';\r\n        }\r\n\t\t\t},\r\n\t\t\tyearChange:function(e){\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar val = this.yearList[e.detail.value];\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\tthis.editorFormdata = editorFormdata;\r\n\t\t\t\tthis.test = Math.random();\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tthis.formvaldata[field] = val;\r\n\t\t\t\t//判断活动是否结束\r\n\t\t\t\tif(this.data.is_endtime == 1){\r\n\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\teditorBindPickerChange:function(e){\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\tthis.editorFormdata = editorFormdata\r\n\t\t\t\tthis.test = Math.random();\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tthis.formvaldata[field] = val;\r\n\r\n\t\t\t\tvar idx = field.replace('form','');\r\n\t\t\t\tvar thiscontent = this.data.content[idx]\r\n\t\t\t\t//判断活动是否结束\r\n\t\t\t\tif(this.data.is_endtime == 1){\r\n\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t}\r\n\t\t\t\t//console.log(thiscontent);\r\n\t\t\t\tif(thiscontent.key == 'radio' || thiscontent.key=='selector'){\r\n\t\t\t\t\tvar linkitem = thiscontent.val1 + '|' + thiscontent.val2[val];\r\n\t\t\t\t\tconsole.log(linkitem)\r\n\t\t\t\t\tfor(var i in this.data.content){\r\n\t\t\t\t\t\tvar thislinkitem = this.data.content[i].linkitem;\r\n\t\t\t\t\t\tif(thislinkitem == linkitem){\r\n\t\t\t\t\t\t\tthis.data.content[i].linkshow = true;\r\n\t\t\t\t\t\t\tthis.test = Math.random();\r\n\t\t\t\t\t\t}else if(thislinkitem && thislinkitem.split('|')[0] == thiscontent.val1){\r\n\t\t\t\t\t\t\tthis.data.content[i].linkshow = false;\r\n\t\t\t\t\t\t\tthis.test = Math.random();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tgetPhoneNumber: function (e) {\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar field = 'form'+idx;\r\n\t\t\t\tif(that.authphone){\r\n\t\t\t\t\tthat.test = Math.random()\r\n\t\t\t\t\tthat.formdata['form'+idx] = that.authphone;\r\n\t\t\t\t\tthat.formvaldata[field] = that.authphone;\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t\tif(e.detail.errMsg == \"getPhoneNumber:fail user deny\"){\r\n\t\t\t\t\tapp.error('请同意授权获取手机号');return;\r\n\t\t\t\t}\r\n\t\t\t\tif(!e.detail.iv || !e.detail.encryptedData){\r\n\t\t\t\t\tapp.error('请同意授权获取手机号');return;\r\n\t\t\t\t}\r\n\t\t\t\twx.login({success (res1){\r\n\t\t\t\t\tconsole.log('res1')\r\n\t\t\t\t\tconsole.log(res1);\r\n\t\t\t\t\tvar code = res1.code;\r\n\t\t\t\t\t//用户允许授权\r\n\t\t\t\t\tapp.post('ApiIndex/authphone',{ iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid},function(res2){\r\n\t\t\t\t\t\tif (res2.status == 1) {\r\n\t\t\t\t\t\t\tthat.authphone = res2.tel;\r\n\t\t\t\t\t\t\tthat.test = Math.random()\r\n\t\t\t\t\t\t\tthat.formdata['form'+idx] = that.authphone;\r\n\t\t\t\t\t\t\tthat.formvaldata[field] = that.authphone;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.error(res2.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t})\r\n\t\t\t\t}});\r\n\t\t\t},\r\n      download:function(e){\r\n          var that = this;\r\n          var file = e.currentTarget.dataset.file;\r\n          // #ifdef H5\r\n              window.location.href= file;\r\n          // #endif\r\n          \r\n          // #ifdef MP-WEIXIN\r\n          uni.downloadFile({\r\n            url: file, \r\n            success: (res) => {\r\n                  var filePath = res.tempFilePath;\r\n              if (res.statusCode === 200) {\r\n                uni.openDocument({\r\n                        filePath: filePath,\r\n                        showMenu: true,\r\n                        success: function (res) {\r\n                          console.log('打开文档成功');\r\n                        }\r\n                      });\r\n              }\r\n            }\r\n          });\r\n          // #endif\r\n      },\r\n      chooseFile:function(e){\r\n          var that = this;\r\n          var idx = e.currentTarget.dataset.idx;\r\n          var field = e.currentTarget.dataset.formidx;\r\n          \r\n          var editorFormdata = this.editorFormdata;\r\n          if(!editorFormdata) editorFormdata = [];\r\n          \r\n          var up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;\r\n          //判断活动是否结束\r\n\t\t\t\t\tif(that.data.is_endtime == 1){\r\n\t\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t\t}\r\n          // #ifdef H5\r\n          uni.chooseFile({\r\n              count: 1, //默认100\r\n              success: function (res) {\r\n                  const tempFilePaths = res.tempFiles;\r\n\r\n                  if(tempFilePaths[0].size > 0){\r\n                    var maxsize = that.data.content[idx].val11;\r\n                    if(maxsize){\r\n                      maxsize = parseFloat(maxsize);\r\n                      if(maxsize > 0 && maxsize * 1024 * 1024 < tempFilePaths[0].size){\r\n                        app.alert('文件过大');return;\r\n                      }\r\n                    }\r\n                  }\r\n                  //for (var i = 0; i < tempFilePaths.length; i++) {\r\n                    app.showLoading('上传中');\r\n                    uni.uploadFile({\r\n                      url: up_url,\r\n                      filePath: tempFilePaths[0]['path'],\r\n                      name: 'file',\r\n                      success: function(res) {\r\n                        app.showLoading(false);\r\n                        var data = JSON.parse(res.data);\r\n                        if (data.status == 1) {\r\n                                  that.formvaldata[field] = data.url;\r\n                                  \r\n                                  editorFormdata[idx] = data.url;\r\n                                  that.editorFormdata = editorFormdata;\r\n                                  that.$set(that.editorFormdata, idx,data.url)\r\n                        } else {\r\n                          app.alert(data.msg);\r\n                        }\r\n                      },\r\n                      fail: function(res) {\r\n                        app.showLoading(false);\r\n                        app.alert(res.errMsg);\r\n                      }\r\n                    });\r\n                  //}\r\n              }\r\n          });\r\n          // #endif\r\n          // #ifdef MP-WEIXIN\r\n              wx.chooseMessageFile({\r\n                count: 1,\r\n                //type: 'file',\r\n                success (res) {\r\n                  // tempFilePath可以作为 img 标签的 src 属性显示图片\r\n                  const tempFilePaths = res.tempFiles\r\n                  console.log(tempFilePaths);\r\n                  \r\n                  if(tempFilePaths[0].size > 0){\r\n                    var maxsize = that.data.content[idx].val11;\r\n                    if(maxsize){\r\n                      maxsize = parseFloat(maxsize);\r\n                      if(maxsize > 0 && maxsize * 1024 * 1024 < tempFilePaths[0].size){\r\n                        app.alert('文件过大');return;\r\n                      }\r\n                    }\r\n                  }\r\n                 \r\n                  //for (var i = 0; i < tempFilePaths.length; i++) {\r\n                    app.showLoading('上传中');\r\n                      console.log(tempFilePaths[0]);\r\n                    uni.uploadFile({\r\n                      url: up_url,\r\n                      filePath: tempFilePaths[0]['path'],\r\n                      name: 'file',\r\n                      success: function(res) {\r\n                        app.showLoading(false);\r\n                        var data = JSON.parse(res.data);\r\n                        if (data.status == 1) {\r\n                                  that.formvaldata[field] = data.url;\r\n                                  \r\n                                  editorFormdata[idx] = data.url;\r\n                                  that.editorFormdata = editorFormdata;\r\n                                  that.$set(that.editorFormdata, idx,data.url)\r\n                        } else {\r\n                          app.alert(data.msg);\r\n                        }\r\n                      },\r\n                      fail: function(res) {\r\n                        app.showLoading(false);\r\n                        app.alert(res.errMsg);\r\n                      }\r\n                    });\r\n                  //}\r\n                },\r\n                complete(res){\r\n                    console.log(res)\r\n                }\r\n              })\r\n          // #endif\r\n      },\r\n      checkPayMoney:function(){\r\n        var that = this\r\n        var data = that.data\r\n        var feetotal = 0;\r\n        if(data && data.is_other_fee){\r\n          var feeitmes = data.fee_items;\r\n          for(let i in feeitmes){\r\n            feetotal += parseFloat(feeitmes[i].money)\r\n            feeitmes[i]['checked'] = false\r\n          }\r\n          that.data.fee_items = feeitmes\r\n          that.feetotal = feetotal.toFixed(2)\r\n        }\r\n        that.calculatePrice();\r\n      },\r\n      feeChange:function(e){\r\n        var that = this;\r\n        var index = e.currentTarget.dataset.index\r\n        var feeitems = that.data.fee_items\r\n        if(feeitems[index].checked){\r\n          feeitems[index].checked = false\r\n        }else{\r\n          feeitems[index].checked = true\r\n        }\r\n        var feetotal = 0;\r\n        for(let i in feeitems){\r\n          if(feeitems[i].checked){\r\n            feetotal = feetotal + parseFloat(feeitems[i].money)\r\n          }\r\n        }\r\n        that.feetotal = feetotal.toFixed(2);\r\n        that.data.fee_items = feeitems;\r\n        that.calculatePrice();\r\n      },\r\n      upVideo:function(e){\r\n          var that = this;\r\n          var that = this;\r\n          var idx = e.currentTarget.dataset.idx;\r\n          var field = e.currentTarget.dataset.formidx;\r\n          //判断活动是否结束\r\n\t\t\t\t\tif(that.data.is_endtime == 1){\r\n\t\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t\t}\r\n          var editorFormdata = this.editorFormdata;\r\n          if(!editorFormdata) editorFormdata = [];\r\n          var up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;\r\n          uni.chooseVideo({\r\n            sourceType: ['camera', 'album'],\r\n            success: function (res) {\r\n              var path = res.tempFilePath;\r\n              if(res.size > 0){\r\n                var maxsize = that.data.content[idx].val11;\r\n                if(maxsize){\r\n                  maxsize = parseFloat(maxsize);\r\n                  if(maxsize > 0 && maxsize * 1024 * 1024 < res.size){\r\n                    app.alert('视频文件过大');return;\r\n                  }\r\n                }\r\n              }\r\n\r\n              app.showLoading('上传中');\r\n              console.log(path );\r\n              uni.uploadFile({\r\n                url: up_url,\r\n                filePath: path,\r\n                name: 'file',\r\n                success: function(res) {\r\n                  app.showLoading(false);\r\n                  var data = JSON.parse(res.data);\r\n                  if (data.status == 1) {\r\n                    that.formvaldata[field] = data.url;\r\n\r\n                    editorFormdata[idx] = data.url;\r\n                    that.editorFormdata = editorFormdata;\r\n                    that.$set(that.editorFormdata, idx,data.url)\r\n                  } else {\r\n                    app.alert(data.msg);\r\n                  }\r\n                },\r\n                fail: function(res) {\r\n                  app.showLoading(false);\r\n                  app.alert(res.errMsg);\r\n                }\r\n              });\r\n            }\r\n          });\r\n      },\r\n      selectzuobiao: function (e) {\r\n        var that = this;\r\n        var idx = e.currentTarget.dataset.idx;\r\n        var field = 'form'+idx;\r\n\t\t\t\t//判断活动是否结束\r\n\t\t\t\tif(that.data.is_endtime == 1){\r\n\t\t\t\t\tapp.alert('活动已经结束');return;\r\n\t\t\t\t}\r\n        uni.chooseLocation({\r\n          success: function (res) {\r\n            console.log(res);\r\n            that.area = res.address;\r\n            // that.address = res.name;\r\n            that.adr_lat = res.latitude;\r\n            that.adr_lon = res.longitude;\r\n            that.formdata['form'+idx] = res.address;\r\n            that.formvaldata[field] = res.address;\r\n          },\r\n          fail: function (res) {\r\n            console.log(res)\r\n            if (res.errMsg == 'chooseLocation:fail auth deny') {\r\n              //$.error('获取位置失败，请在设置中开启位置信息');\r\n              app.confirm('获取位置失败，请在设置中开启位置信息', function () {\r\n                uni.openSetting({});\r\n              });\r\n            }\r\n          }\r\n        });\r\n      },\r\n      isagreeChange: function (e) {\r\n        var val = e.detail.value;\r\n        if (val.length > 0) {\r\n          this.isagree = true;\r\n        } else {\r\n          this.isagree = false;\r\n        }\r\n        console.log(this.isagree);\r\n      },\r\n      showxieyiFun: function () {\r\n        this.showxieyi = true;\r\n      },\r\n      hidexieyi: function () {\r\n        this.showxieyi = false;\r\n        this.isagree = true;\r\n      },\r\n      selradio:function(e){\r\n        var that = this;\r\n        var key      = e.currentTarget.dataset.idx;\r\n        var index    = e.currentTarget.dataset.index;\r\n        var paymoney = parseFloat(e.currentTarget.dataset.paymoney);\r\n        var value    = e.currentTarget.dataset.value;\r\n        \r\n        var radiokeys = that.radiokeys;\r\n        var radios    = that.radios;\r\n\r\n        //添加\r\n        var data = {\r\n          key:key,\r\n          index:index,\r\n          paymoney:paymoney,\r\n          value:value\r\n        }\r\n        //查询是否已添加，存在则删除\r\n        var pos = radiokeys && radiokeys.length>0?radiokeys.indexOf(key):-1;\r\n        if(pos>=0){\r\n          radiokeys.splice(pos, 1);\r\n          radios.splice(pos, 1);\r\n          if(radios && radios.length>0){\r\n            radiokeys.push(key);\r\n            radios.push(data);\r\n          }else{\r\n            radiokeys = [key];\r\n            radios    = [data];\r\n          }\r\n        }else{\r\n          if(radios && radios.length>0){\r\n            radiokeys.push(key);\r\n            radios.push(data);\r\n          }else{\r\n            radiokeys = [key];\r\n            radios    = [data];\r\n          }\r\n        }\r\n        var radiopaymoney = 0;\r\n        if(radios){\r\n          var len = radios.length;\r\n          for(var i=0;i<len;i++){\r\n            radiopaymoney += parseFloat(radios[i].paymoney);\r\n          }\r\n        }\r\n        that.radiokeys     = radiokeys;\r\n        that.radios        = radios;\r\n        that.radiopaymoney = radiopaymoney;\r\n        that.calculatePrice();\r\n      },\r\n      calculatePrice:function(){\r\n        var that = this;\r\n        var totalprice = 0;\r\n        if(that.data.is_other_fee==1){\r\n          totalprice += parseFloat(that.feetotal);\r\n        }else{\r\n          totalprice += that.data.price?parseFloat(that.data.price):0;\r\n        }\r\n        if(that.data.radio_paymoney==1){\r\n          totalprice += parseFloat(that.radiopaymoney);\r\n        }\r\n        that.totalprice = totalprice;\r\n      }\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-form{height: auto; position: relative;overflow: hidden; padding: 10rpx 0px; background: #fff;}\r\n.dp-form .radio{transform:scale(.7);}\r\n.dp-form .checkbox{transform:scale(.7);}\r\n.dp-form-item{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 10rpx;display:flex;align-items: center;}\r\n.dp-form-item:last-child{border:0}\r\n.dp-form-item .label{line-height: 70rpx;width:140rpx;margin-right: 10px;flex-shrink:0;text-align: right;}\r\n.dp-form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 15rpx;background:#fff;flex: 1;}\r\n.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:5px;padding:15rpx}\r\n.dp-form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.dp-form-item .radio2{display:flex;align-items:center;}\r\n.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.dp-form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.dp-form-item .layui-form-switch{}\r\n.dp-form-item .picker{min-height: 70rpx;line-height:70rpx;flex:1;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 5px;}\r\n\r\n.dp-form-item2{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 10rpx;display:flex;flex-direction:column;align-items: flex-start;}\r\n.dp-form-item2:last-child{border:0}\r\n.dp-form-item2 .label{line-height: 85rpx;width:100%;margin-right: 10px;}\r\n.dp-form-item2 .value{display: flex;justify-content: flex-start;width: 100%;flex: 1;}\r\n.dp-form-item2 .input{height: 70rpx;line-height: 70rpx;overflow: hidden;width:100%;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 15rpx;background:#fff;flex: 1;}\r\n.dp-form-item2 .textarea{height:180rpx;line-height:40rpx;overflow: hidden;width:100%;border:1px solid #eee;border-radius:5px;padding:15rpx}\r\n.dp-form-item2 .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}\r\n.dp-form-item2 .radio2{display:flex;align-items:center;}\r\n.dp-form-item2 .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.dp-form-item2 .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}\r\n.dp-form-item2 .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.dp-form-item2 .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.dp-form-item2 .layui-form-switch{}\r\n.dp-form-item2 .picker{min-height: 70rpx;line-height:70rpx;flex:1;width:100%;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 5px;}\r\n.dp-form-btn{margin: 0 auto;background: #ff4f4f;color: #fff;margin-top: 15px;text-align:center}\r\n.dp-form-blod{font-weight: bold;}\r\n.dp-form-imgbox{margin-right:16rpx;font-size:24rpx;position: relative;}\r\n.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff;z-index:9;border-radius:50%}\r\n.dp-form-imgbox-close .image{width:100%;height:100%}\r\n.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden;}\r\n.dp-form-imgbox-img>.image{width:100%;height:100%}\r\n.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n.dp-form-separate{width: 100%;padding: 20rpx;text-align: center;padding: 20rpx;font-size: 36rpx;font-weight: 500;color: #454545;}\r\n.authtel{border-radius: 10rpx; line-height: 68rpx;margin-left: 20rpx;padding: 0 20rpx;}\r\n.input.disabled{background: #EFEFEF;}\r\n.dp-form-item-fee .dp-form-label{line-height: 70rpx;width:140rpx;text-align: right;}\r\n.dp-form-feelist{flex: 1;padding-left: 32rpx;}\r\n.dp-fee-item{display: flex;justify-content: flex-start;align-items: center;color: #a3a3a3;font-size: 24rpx;}\r\n.dp-fee-item.sum{color: #222222;font-weight: bold;font-size: 28rpx;padding-top: 10rpx;}\r\n.dp-form-label{flex-shrink: 0;}\r\n.dp-fee-name{flex: 1;width: 60%;}\r\n.dp-fee-money{width: 30%;flex-shrink: 0;}\r\n.dp-fee-check{width: 10%;flex-shrink: 0;}\r\n\r\n.dp-form-item-fee2 .dp-form-label{padding-top: 20rpx;}\r\n.dp-form-item-fee2 .dp-form-feelist{flex: 1;padding: 4rpx 0;}\r\n\r\n.dp-form-normal{color: grey;}\r\n.arrow-area {\r\n\tposition: relative;\r\n\twidth: 20px;\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: flex;\r\n\tmargin-left: auto;\r\n\t/* #endif */\r\n\tjustify-content: center;\r\n\ttransform: rotate(-45deg);\r\n\ttransform-origin: center;\r\n}\r\n.input-arrow {width: 7px;height: 7px;border-left: 1px solid #999;border-bottom: 1px solid #999;}\r\n.checkborder{border: 1px solid #dcdfe6;border-radius: 5px;margin-top: 15rpx;min-width: 300rpx;padding: 0 10rpx;}\r\n.rowalone{width: 100%;}\r\n.rowmore{margin-right: 20rpx;}\r\n\r\n.xycss1{line-height: 60rpx;font-size: 24rpx;overflow: hidden;margin-top: 20rpx;}\r\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\r\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\r\n.submember{margin-bottom: 20rpx;}\r\n.submember-title {display: flex;height: 80rpx;line-height: 80rpx;align-items: center;font-size: 32rpx;}\r\n.member-items {display: flex;align-items: center;flex-wrap: wrap;padding: 0 10rpx;}\r\n.member-item{width: 16%;margin: 0 3rpx;text-align: center;}\t\r\n.avatar{width: 82rpx;height:82rpx;border-radius: 50%;}\r\n.more-text{width: 82rpx;height: 82rpx;text-align: center;line-height: 80rpx;border-radius: 50%;font-size: 24rpx;background-color: rgba(0,0,0,.6);color: #fff;display: inline-block;}\r\n.empty{width: 100%;padding: 30rpx;text-align: center;color: #909399;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-form.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-form.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839376074\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}