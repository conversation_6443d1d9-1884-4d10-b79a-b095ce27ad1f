{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-tuangou/buydialog-tuangou.vue?d0f2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-tuangou/buydialog-tuangou.vue?91bd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-tuangou/buydialog-tuangou.vue?37d5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-tuangou/buydialog-tuangou.vue?557d", "uni-app:///components/buydialog-tuangou/buydialog-tuangou.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-tuangou/buydialog-tuangou.vue?832b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-tuangou/buydialog-tuangou.vue?dca1"], "names": ["data", "ks", "product", "gwcnum", "isload", "loading", "canaddcart", "pre_url", "props", "btntype", "default", "menuindex", "controller", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proid", "mounted", "methods", "getdata", "that", "app", "id", "buydialogChange", "tobuy", "gwcplus", "gwcminus", "gwcinput"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACa;;;AAG7E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA+0B,CAAgB,+yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwCn2B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACA;UACAA;QACA;QACAA;QACAA;QACA;UAAA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAH;QACA;MACA;MACA;MACA;MACA;QACAA;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;QACAJ;QACA;MACA;MACA;IACA;IACA;IACAK;MACA;MACA;MACA;QACA;UACAL;QACA;QACA;MACA;MACA;IACA;IACA;IACAM;MACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAA4rC,CAAgB,4mCAAG,EAAC,C;;;;;;;;;;;ACAhtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/buydialog-tuangou/buydialog-tuangou.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./buydialog-tuangou.vue?vue&type=template&id=c03e83f4&\"\nvar renderjs\nimport script from \"./buydialog-tuangou.vue?vue&type=script&lang=js&\"\nexport * from \"./buydialog-tuangou.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buydialog-tuangou.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/buydialog-tuangou/buydialog-tuangou.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-tuangou.vue?vue&type=template&id=c03e83f4&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 =\n    _vm.isload && !(_vm.product.stock <= 0) && _vm.btntype == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload && !(_vm.product.stock <= 0) && _vm.btntype == 2\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-tuangou.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-tuangou.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<view v-if=\"isload\">\r\n\t\t<view class=\"buydialog-mask\" @tap=\"buydialogChange\"></view>\r\n\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"image\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<image :src=\"product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"product.pic\"/>\r\n\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t<block v-if=\"product.price_dollar\">\t<text style=\"margin-right: 10rpx;\">${{product.usdsell_price}}</text></block>\r\n\t\t\t\t\t\t￥{{product.sell_price}} \r\n\t\t\t\t\t<text v-if=\"product.market_price*1 > product.sell_price*1\" class=\"t2\">￥{{product.market_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stock\">库存：{{product.stock}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"buynum flex flex-y-center\">\r\n\t\t\t\t<view class=\"flex1\">购买数量：</view>\r\n\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" /></view>\r\n\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\">\r\n\t\t\t\t<block  v-if=\"product.stock <= 0\">\r\n\t\t\t\t\t<button class=\"nostock\">库存不足</button>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block  v-else>\r\n\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==0\">立即购买</button>\r\n\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==2\">确 定</button>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tks:'',\r\n\t\t\t\tproduct:{},\r\n\t\t\t\tgwcnum:1,\r\n\t\t\t\tisload:false,\r\n\t\t\t\tloading:false,\r\n\t\t\t\tcanaddcart:true,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tbtntype:{default:0},\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tcontroller:{default:'ApiTuangou'},\r\n\t\t\tneedaddcart:{default:true},\r\n\t\t\tproid:{}\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(this.controller+'/getproductdetail',{id:that.proid},function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\tif(!that.product.limit_start){\r\n\t\t\t\t\t\tthat.product.limit_start = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.gwcnum = that.product.limit_start;\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\r\n\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.product.istg == 1){\r\n\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.controller == 'ApiTuangou'){\r\n\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tbuydialogChange:function(){\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t},\r\n\t\t\ttobuy: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\tif (this.product.stock < num) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar prodata = proid + ',' + num;\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\tif(this.controller == 'ApiTuangou'){\r\n\t\t\t\t\tapp.goto('/activity/tuangou/buy?prodata=' + prodata);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//加\r\n\t\t\tgwcplus: function (e) {\r\n\t\t\t\tvar gwcnum = this.gwcnum + 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tif (gwcnum > this.product.stock) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = this.gwcnum + 1;\r\n\t\t\t},\r\n\t\t\t//减\r\n\t\t\tgwcminus: function (e) {\r\n\t\t\t\tvar gwcnum = this.gwcnum - 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = this.gwcnum - 1;\r\n\t\t\t},\r\n\t\t\t//输入\r\n\t\t\tgwcinput: function (e) {\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\t\tif (gwcnum < 1) return 1;\r\n\t\t\t\tif (gwcnum > this.product.stock) {\r\n\t\t\t\t\treturn this.product.stock > 0 ? this.product.stock : 1;\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = gwcnum;\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.buydialog .title{padding:20rpx 0px;height: 190rpx;}\r\n.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\r\n.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\r\n.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.buydialog .guigelist{padding:0px 0px 10px 0px; }\r\n.buydialog .op{margin-top:100rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-tuangou.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-tuangou.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370240\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}