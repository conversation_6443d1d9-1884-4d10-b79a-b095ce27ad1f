{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-collage/dp-collage.vue?8cfe", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-collage/dp-collage.vue?a13f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-collage/dp-collage.vue?877a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-collage/dp-collage.vue?c282", "uni-app:///components/dp-collage/dp-collage.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-collage/dp-collage.vue?098e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-collage/dp-collage.vue?2d97"], "names": ["data", "pre_url", "props", "params"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjHA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuH51B;EACAA;IACA;MACAC;IACA;EACA;EACAC;IACAC;IACAH;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjIA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-collage/dp-collage.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-collage.vue?vue&type=template&id=2a6ccc84&\"\nvar renderjs\nimport script from \"./dp-collage.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-collage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-collage.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-collage/dp-collage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-collage.vue?vue&type=template&id=2a6ccc84&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.params.style == \"1\" ||\n    _vm.params.style == \"2\" ||\n    _vm.params.style == \"3\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.params.showprice != \"0\" ? _vm.t(\"color1\") : null\n          var m1 = !item.collage_type ? _vm.t(\"color1rgb\") : null\n          var m2 = !item.collage_type ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var l1 =\n    _vm.params.style == \"list\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.params.showprice != \"0\" ? _vm.t(\"color1\") : null\n          var m4 = _vm.t(\"color1rgb\")\n          var m5 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var l2 =\n    _vm.params.style == \"line\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.params.showprice != \"0\" ? _vm.t(\"color1\") : null\n          var m7 = _vm.t(\"color1rgb\")\n          var m8 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n          }\n        })\n      : null\n  var l3 =\n    _vm.params.style == \"2\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m9 = _vm.t(\"color1rgb\")\n          var m10 = _vm.t(\"color1\")\n          var m11 = _vm.t(\"color1rgb\")\n          var m12 = _vm.t(\"color1\")\n          var m13 = _vm.t(\"color1rgb\")\n          var m14 = _vm.t(\"color1\")\n          var m15 = _vm.t(\"color1rgb\")\n          return {\n            $orig: $orig,\n            m9: m9,\n            m10: m10,\n            m11: m11,\n            m12: m12,\n            m13: m13,\n            m14: m14,\n            m15: m15,\n          }\n        })\n      : null\n  var l4 =\n    _vm.params.style == \"list\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m16 = _vm.t(\"color1rgb\")\n          var m17 = _vm.t(\"color1\")\n          var m18 = _vm.t(\"color1rgb\")\n          var m19 = _vm.t(\"color1\")\n          var m20 = _vm.t(\"color1\")\n          var m21 = _vm.t(\"color1\")\n          var m22 = _vm.t(\"color1rgb\")\n          return {\n            $orig: $orig,\n            m16: m16,\n            m17: m17,\n            m18: m18,\n            m19: m19,\n            m20: m20,\n            m21: m21,\n            m22: m22,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        l4: l4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-collage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-collage.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"dp-collage\" :style=\"{\n\tbackgroundColor:params.bgcolor,\n\tmargin:(params.margin_y*2.2)+'rpx '+params.margin_x*2.2+'rpx 0',\n\tpadding:(params.padding_y*2.2)+'rpx '+params.padding_x*2.2+'rpx'\n}\">\n\t<view v-show=\"!params.shopstyle||params.shopstyle==1\">\n\t\t<view class=\"dp-collage-item\" v-if=\"params.style=='1' || params.style=='2' || params.style=='3'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :style=\"params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/collage/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t<view class=\"p2-1\" v-if=\"params.showprice != '0'\">\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\n\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"params.showprice == '1' && item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t<view class=\"p3-1\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\" v-if=\"!item.collage_type\">{{item.teamnum}}人团</view>\n\t\t\t\t\t\t<view class=\"p3-2-nowrap\" v-if=\"params.showsales=='1' && item.sales>0\"><text style=\"overflow:hidden;\">已拼成{{item.sales}}件</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"dp-collage-itemlist\" v-if=\"params.style=='list'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/collage/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"p2\" v-if=\"params.showprice != '0'\">\n\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx;padding-right:1px\">￥</text>{{item.sell_price}}</text>\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"params.showprice == '1' && item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t<view class=\"p3-1\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\">{{item.teamnum}}人团</view>\n\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"params.showsales=='1' && item.sales>0\"><text style=\"overflow:hidden\">已拼成{{item.sales}}件</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"dp-collage-itemline\" v-if=\"params.style=='line'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/collage/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t<view class=\"p2-1\" v-if=\"params.showprice != '0'\">\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\n\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"params.showprice == '1' && item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t<view class=\"p3-1\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\">{{item.teamnum}}人团</view>\n\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"params.showsales=='1' && item.sales>0\"><text style=\"overflow:hidden\">已拼成{{item.sales}}件</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t<view v-show=\"params.shopstyle==2\">\n\t\t<view class=\"dp-collage-item\" v-if=\"params.style=='2'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :style=\"params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/collage/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"tag\">拼团</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"flex\">\n\t\t\t\t\t\t<view :style=\"{'border-color':t('color1')}\" class=\"total flex\">\n\t\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"num\">{{item.teamnum}}人团</view>\n\t\t\t\t\t\t\t<view :style=\"{color:t('color1'),background:'rgba('+t('color1rgb')+',0.1)'}\" class=\"sales\">已团{{item.sales}}件</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"price flex-bt flex-y-center\">\n\t\t\t\t\t\t<text :style=\"{color:t('color1')}\" class=\"text\">￥{{item.sell_price}}</text>\n\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"add flex-xy-center\">\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/decoration_add.png'\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"dp-collage-itemlist\" v-if=\"params.style=='list'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/collage/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"tag\">拼团</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view :style=\"{color:t('color1')}\" class=\"text\">已团{{item.sales}}件</view>\n\t\t\t\t\t<view class=\"total flex-y-center\">\n\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\" class=\"tag\">{{item.teamnum}}人团</view>\n\t\t\t\t\t\t<view class=\"flex-y-bottom\">\n\t\t\t\t\t\t\t<view :style=\"{color:t('color1')}\" class=\"unit\">￥</view>\n\t\t\t\t\t\t\t<view :style=\"{color:t('color1')}\" class=\"price\">{{item.sell_price}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"btn flex-xy-center\">去拼团</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</view>\n</template>\n<script>\n\texport default {\n\t\tdata(){\n\t\t\treturn {\n\t\t\t\tpre_url:getApp().globalData.pre_url\n\t\t\t}\n\t\t},\n\t\tprops: {\n\t\t\tparams:{},\n\t\t\tdata:{}\n\t\t}\n\t}\n</script>\n<style>\n.dp-collage{height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}\n.dp-collage-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.dp-collage-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden}\n.dp-collage-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\n.dp-collage-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.dp-collage-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}\n.dp-collage-item .product-pic .tag{padding: 0 15rpx;line-height: 35rpx;display: inline-block;font-size: 24rpx;color: #fff;background: linear-gradient(to bottom right,#ff88c0,#ec3eda);position: absolute;left: 0;top: 0;border-radius: 0 0 10rpx 0}\n.dp-collage-item .product-info {padding:20rpx 20rpx;position: relative;}\n.dp-collage-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-collage-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\n.dp-collage-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\n.dp-collage-item .product-info .p2-1 .t1{font-size:36rpx;}\n.dp-collage-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-collage-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\n.dp-collage-item .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\n.dp-collage-item .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 20rpx;font-size:24rpx}\n.dp-collage-item .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\r\n.dp-collage-item .product-info .p3-2-nowrap{color:#999999;font-size:20rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}\n.dp-collage-item .product-info .total{border-radius: 8rpx;border: 1rpx solid #FF3143;font-size: 24rpx;background: #ffeded;overflow: hidden;}\n.dp-collage-item .product-info .total .num{color: #fff;background: #FF3143;padding: 3rpx 8rpx;}\n.dp-collage-item .product-info .total .sales{color: #FF3143;padding: 3rpx 8rpx;}\n.dp-collage-item .product-info .price{position: relative;margin-top: 15rpx;}\n.dp-collage-item .product-info .price .text{color: #FF3143;font-weight: bold;font-size: 30rpx;}\n.dp-collage-item .product-info .price .add{height: 50rpx;width: 50rpx;border-radius: 100rpx;background: #FF3143;}\n.dp-collage-item .product-info .price .add image{height: 30rpx;width: 30rpx;display: block;}\n\n.dp-collage-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.dp-collage-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:20rpx;border-radius:10rpx}\n.dp-collage-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\n.dp-collage-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.dp-collage-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\n.dp-collage-itemlist .product-pic .tag{padding: 0 15rpx;line-height: 35rpx;display: inline-block;font-size: 24rpx;color: #fff;background: linear-gradient(to bottom right,#ff88c0,#ec3eda);position: absolute;left: 0;top: 0;border-radius: 0 0 10rpx 0}\n.dp-collage-itemlist .product-info {width: 70%;padding:6rpx 10rpx 5rpx 20rpx;position: relative;}\n.dp-collage-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-collage-itemlist .product-info .p2{margin-top:20rpx;height:56rpx;line-height:56rpx;overflow:hidden;}\n.dp-collage-itemlist .product-info .p2 .t1{font-size:36rpx;}\n.dp-collage-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-collage-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\n.dp-collage-itemlist .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}\n.dp-collage-itemlist .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\n.dp-collage-itemlist .product-info .text{font-size:24rpx;margin-top: 30rpx;}\n.dp-collage-itemlist .product-info .total{margin-top: 20rpx;}\n.dp-collage-itemlist .product-info .total .tag{width: auto;background: #ffeded;color: #fff;line-height: 35rpx;padding: 0 8rpx;border-radius: 8rpx;font-size: 24rpx;margin-right: 5rpx;}\n.dp-collage-itemlist .product-info .total .unit{font-size: 24rpx;font-weight: bold;line-height: 28rpx;}\n.dp-collage-itemlist .product-info .total .price{font-size: 35rpx;font-weight: bold;line-height: 35rpx;}\n.dp-collage-itemlist .product-info .btn{width: 140rpx;height: 60rpx;color: #fff;border-radius: 100rpx;background: rgb(253, 70, 62);font-size: 26rpx;position: absolute;right: 0;bottom: 0;font-weight: bold;}\n\n.dp-collage-itemline{width:100%;display:flex;overflow-x:scroll;overflow-y:hidden}\n.dp-collage-itemline .item{width: 220rpx;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;margin-right:4px}\n.dp-collage-itemline .product-pic {width:220rpx;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\n.dp-collage-itemline .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.dp-collage-itemline .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}\n.dp-collage-itemline .product-info {padding:20rpx 20rpx;position: relative;}\n.dp-collage-itemline .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-collage-itemline .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\n.dp-collage-itemline .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\n.dp-collage-itemline .product-info .p2-1 .t1{font-size:36rpx;}\n.dp-collage-itemline .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-collage-itemline .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\n.dp-collage-itemline .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\n.dp-collage-itemline .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}\n.dp-collage-itemline .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-collage.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-collage.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839375718\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}