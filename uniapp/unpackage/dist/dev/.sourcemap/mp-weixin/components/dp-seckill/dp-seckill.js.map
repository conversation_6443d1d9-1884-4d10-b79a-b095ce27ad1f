{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-seckill/dp-seckill.vue?8d7e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-seckill/dp-seckill.vue?2bca", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-seckill/dp-seckill.vue?2d13", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-seckill/dp-seckill.vue?000d", "uni-app:///components/dp-seckill/dp-seckill.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-seckill/dp-seckill.vue?e5d2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-seckill/dp-seckill.vue?4f00"], "names": ["data", "pre_url", "props", "params"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjJA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuO51B;EACAA;IACA;MACAC;IACA;EACA;EACAC;IACAC;IACAH;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjPA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-seckill/dp-seckill.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-seckill.vue?vue&type=template&id=4adc59f8&\"\nvar renderjs\nimport script from \"./dp-seckill.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-seckill.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-seckill.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-seckill/dp-seckill.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-seckill.vue?vue&type=template&id=4adc59f8&\"", "var components\ntry {\n  components = {\n    uniCountdown: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-countdown/uni-countdown\" */ \"@/components/uni-countdown/uni-countdown.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    (!_vm.params.shopstyle || _vm.params.shopstyle == 1) &&\n    (_vm.params.style == \"1\" ||\n      _vm.params.style == \"2\" ||\n      _vm.params.style == \"3\")\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.params.showprice != \"0\" ? _vm.t(\"color1\") : null\n          var m1 = _vm.t(\"color1rgb\")\n          var m2 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var l1 =\n    (!_vm.params.shopstyle || _vm.params.shopstyle == 1) &&\n    _vm.params.style == \"list\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.params.showprice != \"0\" ? _vm.t(\"color1\") : null\n          var m4 = _vm.t(\"color1rgb\")\n          var m5 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var l2 =\n    (!_vm.params.shopstyle || _vm.params.shopstyle == 1) &&\n    _vm.params.style == \"line\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.params.showprice != \"0\" ? _vm.t(\"color1\") : null\n          var m7 = _vm.t(\"color1rgb\")\n          var m8 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n          }\n        })\n      : null\n  var l3 =\n    _vm.params.shopstyle == 2 && _vm.params.style == \"2\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m9 = item.usd_sellprice ? _vm.t(\"color1\") : null\n          var m10 = !item.usd_sellprice ? _vm.t(\"color1\") : null\n          var m11 = _vm.t(\"color1rgb\")\n          return {\n            $orig: $orig,\n            m9: m9,\n            m10: m10,\n            m11: m11,\n          }\n        })\n      : null\n  var l4 =\n    _vm.params.shopstyle == 2 && _vm.params.style == \"list\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m12 = item.usd_sellprice ? _vm.t(\"color1\") : null\n          var m13 = !item.usd_sellprice ? _vm.t(\"color1\") : null\n          var m14 = _vm.t(\"color1rgb\")\n          return {\n            $orig: $orig,\n            m12: m12,\n            m13: m13,\n            m14: m14,\n          }\n        })\n      : null\n  var l5 =\n    _vm.params.shopstyle == 2 && _vm.params.style == \"line\"\n      ? _vm.__map(_vm.data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m15 = _vm.t(\"color1rgb\")\n          var m16 = _vm.t(\"color1rgb\")\n          var m17 = _vm.t(\"color1\")\n          var m18 = _vm.t(\"color1\")\n          var m19 = _vm.t(\"color1rgb\")\n          return {\n            $orig: $orig,\n            m15: m15,\n            m16: m16,\n            m17: m17,\n            m18: m18,\n            m19: m19,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        l4: l4,\n        l5: l5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-seckill.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-seckill.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"dp-seckill\" :style=\"{\n\tbackgroundColor:params.bgcolor,\n\tmargin:(params.margin_y*2.2)+'rpx '+params.margin_x*2.2+'rpx 0',\n\tpadding:(params.padding_y*2.2)+'rpx '+params.padding_x*2.2+'rpx'\n}\">\n    <view v-if=\"params.shopstyle=='2'\">\n    \t<view v-if=\"params.showtitle=='1'\">\n\t\t\t<view v-if=\"params.titlestyle==1\" class=\"dp-time flex-y-center\">\n\t\t\t\t<image mode=\"widthFix\" class=\"dp-time-back\" :src=\"pre_url+'/static/imgsrc/decoration_crush.png'\" alt=\"\"/>\n\t\t\t\t<view class=\"dp-time-module flex flex-bt flex-y-center\">\n\t\t\t\t\t<text class=\"dp-time-title\">限时秒杀</text>\n\t\t\t\t\t<view class=\"dp-time-content\">\n\t\t\t\t\t\t<text v-if=\"data[0].seckill_status == 0\">距开抢</text>\n\t\t\t\t\t\t<text v-if=\"data[0].seckill_status == 1\">还剩余</text>\n\t\t\t\t\t\t<text v-if=\"data[0].seckill_status == 2\">活动已结束</text>\n\t\t\t\t\t\t<uni-countdown v-if=\"data[0].seckill_status != 2\" :show-day=\"false\" color=\"#fd4a46\" background-color=\"#fff\" :hour=\"data[0].hour\" :minute=\"data[0].minute\" :second=\"data[0].second\" splitorColor=\"#fff\"></uni-countdown>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"params.titlestyle==2\" class=\"dp-bTime flex-y-center\">\n\t\t\t\t<image mode=\"widthFix\" class=\"dp-bTime-back\" :src=\"pre_url+'/static/imgsrc/decoration_crush.png'\" alt=\"\"/>\n\t\t\t\t<view class=\"dp-bTime-module flex flex-bt flex-y-center\">\n\t\t\t\t\t<text class=\"dp-bTime-title\">限时秒杀</text>\n\t\t\t\t\t<view class=\"dp-bTime-content\">\n\t\t\t\t\t\t<text v-if=\"data[0].seckill_status == 0\">距开抢</text>\n\t\t\t\t\t\t<text v-if=\"data[0].seckill_status == 1\">还剩余</text>\n\t\t\t\t\t\t<text v-if=\"data[0].seckill_status == 2\">活动已结束</text>\n\t\t\t\t\t\t<uni-countdown v-if=\"data[0].seckill_status != 2\" :show-day=\"false\" color=\"#fff\" background-color=\"#000\" :hour=\"data[0].hour\" :minute=\"data[0].minute\" :second=\"data[0].second\" splitorColor=\"#999ca7\"></uni-countdown>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n    </view>\n\t\n\t<view v-if=\"!params.shopstyle||params.shopstyle==1\">\n\t\t<view class=\"dp-seckill-item\" v-if=\"params.style=='1' || params.style=='2' || params.style=='3'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :style=\"params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/seckill/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t<view class=\"p2-1\" v-if=\"params.showprice != '0'\">\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">${{item.usd_sellprice}}</text>\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size: 28rpx;\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"params.showprice == '1'\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"params.showtime == 1 && params.style!='3'\" style=\"color:#333;font-size:24rpx\">\n\t\t\t\t\t\t<view v-if=\"item.seckill_status == 2\">活动已结束</view>\n\t\t\t\t\t\t<view v-if=\"item.seckill_status == 1\" class=\"flex-row\"><view class=\"h24\">还剩余</view><view class=\"flex1\"></view><uni-countdown :show-day=\"false\" color=\"#FFFFFF\" background-color=\"#fd4a46\" :hour=\"item.hour\" :minute=\"item.minute\" :second=\"item.second\" splitorColor=\"#333\"></uni-countdown></view>\n\t\t\t\t\t\t<view v-if=\"item.seckill_status == 0\" class=\"flex-row\"><view class=\"h24\">距开抢</view><view class=\"flex1\"></view><uni-countdown :show-day=\"false\" color=\"#FFFFFF\" background-color=\"#fd4a46\" :hour=\"item.hour\" :minute=\"item.minute\" :second=\"item.second\" splitorColor=\"#333\"></uni-countdown></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t<view class=\"p3-1\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\">秒杀</view>\n\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"params.showsales=='1' && item.sales>0\"><text style=\"overflow:hidden\">已抢购{{item.sales}}件</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"dp-seckill-itemlist\" v-if=\"params.style=='list'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/seckill/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"p2\" v-if=\"params.showprice != '0'\">\n\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{item.usd_sellprice}}\r\n\t\t\t\t\t\t\t\t<text style=\"font-size: 28rpx;\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</text>\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"params.showprice == '1'\">￥{{item.market_price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"params.showtime == 1\" style=\"color:#333;font-size:24rpx\">\n\t\t\t\t\t\t<view v-if=\"item.seckill_status == 2\">活动已结束</view>\n\t\t\t\t\t\t<view v-if=\"item.seckill_status == 1\" class=\"flex-row\"><view class=\"h24\">距活动结束</view><view class=\"flex1\"></view>\n\t\t\t\t\t\t\t<uni-countdown v-if=\"item.day > 0\" :show-day=\"true\" color=\"#FFFFFF\" background-color=\"#fd4a46\" :day=\"item.day\" :hour=\"item.day_hour\" :minute=\"item.minute\" :second=\"item.second\" splitorColor=\"#333\"></uni-countdown>\n\t\t\t\t\t\t\t<uni-countdown v-else :show-day=\"false\" color=\"#FFFFFF\" background-color=\"#fd4a46\" :day=\"item.day\" :hour=\"item.hour\" :minute=\"item.minute\" :second=\"item.second\"></uni-countdown>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"item.seckill_status == 0\" class=\"flex-row\"><view class=\"h24\">距活动开始</view><view class=\"flex1\"></view>\n\t\t\t\t\t\t\t<uni-countdown v-if=\"item.day > 0\" :show-day=\"true\" color=\"#FFFFFF\" background-color=\"#fd4a46\" :day=\"item.day\" :hour=\"item.day_hour\" :minute=\"item.minute\" :second=\"item.second\" splitorColor=\"#333\"></uni-countdown>\n\t\t\t\t\t\t\t<uni-countdown v-else :show-day=\"false\" color=\"#FFFFFF\" background-color=\"#fd4a46\" :day=\"item.day\" :hour=\"item.hour\" :minute=\"item.minute\" :second=\"item.second\"></uni-countdown>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t<view class=\"p3-1\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\">秒杀</view>\n\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"params.showsales=='1' && item.sales>0\"><text style=\"overflow:hidden\">已抢购{{item.sales}}件</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"dp-seckill-itemline\" v-if=\"params.style=='line'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/seckill/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t<view class=\"p2-1\" v-if=\"params.showprice != '0'\">\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{item.usd_sellprice}}\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size: 28rpx;\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"params.showprice == '1'\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t<view class=\"p3-1\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\">秒杀</view>\n\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"params.showsales=='1' && item.sales>0\"><text style=\"overflow:hidden\">已抢购{{item.sales}}件</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t\n\t<view v-if=\"params.shopstyle==2\">\n\t\t<view class=\"dp-seckill-item\" style=\"overflow: visible;\" v-if=\"params.style=='2'\">\n\t\t\t<view class=\"item\" style=\"overflow: visible;\" v-for=\"(item,index) in data\" :style=\"params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/seckill/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"rate flex-y-center flex-bt\">\n\t\t\t\t\t\t<view class=\"rate_module\">\n\t\t\t\t\t\t\t<view :style=\"{width:(item.sales/item.stock)*100 + '%'}\" class=\"rate_item\">\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/decoration_tag.png'\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text>仅剩{{item.stock - item.sales}}件</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"cost\">\n\t\t\t\t\t\t原价：<text>￥{{item.market_price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"price flex-y-center flex-bt\">\r\n\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t<text :style=\"{color:t('color1')}\">${{item.usd_sellprice}} ￥{{item.sell_price}}</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<text :style=\"{color:t('color1')}\">￥{{item.sell_price}}</text>\r\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"flex-xy-center\">\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/decoration_add.png'\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"dp-seckill-itemlist1\" v-if=\"params.style=='list'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/seckill/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<!-- <view class=\"text\">平价实用</view> -->\n\t\t\t\t\t<view class=\"rate flex-y-center flex-bt\">\n\t\t\t\t\t\t<view class=\"rate_module\">\n\t\t\t\t\t\t\t<view :style=\"{width:(item.sales/item.stock)*100 + '%'}\" class=\"rate_item\">\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/decoration_tag.png'\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text>仅剩{{item.stock - item.sales}}件</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"cost\">\n\t\t\t\t\t\t原价：<text>￥{{item.market_price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"price flex-y-center flex-bt\">\r\n\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t<text :style=\"{color:t('color1')}\">${{item.usd_sellprice}} ￥{{item.sell_price}}</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<text :style=\"{color:t('color1')}\">￥{{item.sell_price}}</text>\r\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"flex-xy-center\">马上抢</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"dp-seckill-itemline\" v-if=\"params.style=='line'\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/seckill/product?id='+item.proid\">\n\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t<image class=\"saleimg\" :src=\"params.saleimg\" v-if=\"params.saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"tag\">秒杀</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<view class=\"p1\" v-if=\"params.showname == 1\">{{item.name}}</view>\n\t\t\t\t\t<view class=\"flex\">\n\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\" class=\"tag\">剩余{{item.stock - item.sales}}件</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"price flex-y-center flex-bt\">\n\t\t\t\t\t\t<text :style=\"{color:t('color1')}\">￥{{item.sell_price}}</text>\n\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" class=\"flex-xy-center\">\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/imgsrc/decoration_add.png'\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</view>\n</template>\n<script>\n\texport default {\n\t\tdata(){\n\t\t\treturn {\n\t\t\t\tpre_url:getApp().globalData.pre_url\n\t\t\t}\n\t\t},\n\t\tprops: {\n\t\t\tparams:{},\n\t\t\tdata:{}\n\t\t}\n\t}\n</script>\n<style>\n.dp-time{position: relative;border-radius: 10rpx;overflow: hidden;margin-bottom: 20rpx;}\n.dp-time-back{width: 100%;overflow: hidden;}\n.dp-time-module{position: absolute;height: 100%;width: 100%;top: 0;left: 0;padding: 0 20rpx;}\n.dp-time-title{font-size: 33rpx;color: rgb(255, 255, 255);font-style: italic;font-weight: bold;}\n.dp-time-content{display: flex;align-items: center;font-size: 22rpx;color: #fff;}\n.dp-time-tag{background: #fff;border-radius: 4rpx;color: #fd463e;text-align: center;line-height: 36rpx;margin: 0 5rpx;padding: 0 5rpx;}\n\n.dp-bTime{position: relative;border-radius: 10rpx;overflow: hidden;margin-bottom: 20rpx;}\n.dp-bTime-back{width: 100%;overflow: hidden;opacity: 0;}\n.dp-bTime-module{position: absolute;height: 100%;width: 100%;top: 0;left: 0;padding: 0 20rpx;}\n.dp-bTime-title{font-size: 33rpx;color: #000;font-weight: bold;}\n.dp-bTime-content{display: flex;align-items: center;font-size: 22rpx;color: rgb(153, 156, 167);}\n.dp-bTime-tag{background: rgb(55, 56, 58);border-radius: 4rpx;color: #fff;text-align: center;line-height: 36rpx;margin: 0 5rpx;padding: 0 5rpx;}\n\n.dp-seckill{height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}\n.dp-seckill-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.dp-seckill-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden}\n.dp-seckill-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\n.dp-seckill-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.dp-seckill-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}\n.dp-seckill-item .product-info {padding:20rpx 10rpx;position: relative;}\n.dp-seckill-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-seckill-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\n.dp-seckill-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\n.dp-seckill-item .product-info .p2-1 .t1{font-size:36rpx;}\n.dp-seckill-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-seckill-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\n.dp-seckill-item .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\n.dp-seckill-item .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}\n.dp-seckill-item .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\n.dp-seckill-item .product-info .h24 {height: 48rpx; line-height: 48rpx;padding: 2rpx 0; margin: 4rpx 0;}\n\n.dp-seckill-item .product-info .rate{font-size:24rpx;color: #FF3143;}\n.dp-seckill-item .product-info .rate_module{width: 190rpx;height: 15rpx;border-radius: 500rpx;background: #ffc1c1;}\n.dp-seckill-item .product-info .rate_item{height: 15rpx;background: #ff065e;border-radius: 500rpx;position: relative;}\n.dp-seckill-item .product-info .rate_item image{position: absolute;top: 0;bottom: 0;right: -15rpx;margin: auto 0;height: 30rpx;width: 30rpx;}\n.dp-seckill-item .product-info .cost{font-size:24rpx;color: #999ca7;margin-top: 10rpx;}\n.dp-seckill-item .product-info .cost text{text-decoration: line-through;}\n.dp-seckill-item .product-info .price{font-weight: bold;color: #fd463e;position: relative;margin-top: 20rpx;font-size: 32rpx;}\n.dp-seckill-item .product-info .price view{height: 50rpx;width: 50rpx;background: #fd463e;border-radius: 100rpx;}\n.dp-seckill-item .product-info .price view image{height: 30rpx;width: 30rpx;display: block;}\n\n.dp-seckill-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.dp-seckill-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:20rpx;border-radius:10rpx}\n.dp-seckill-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\n.dp-seckill-itemlist .product-pic .image{width: 100%;height:auto}\n.dp-seckill-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\n.dp-seckill-itemlist .product-info {width: 70%;padding:6rpx 0rpx 5rpx 20rpx;position: relative;}\n.dp-seckill-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-seckill-itemlist .product-info .p2 {height:56rpx;line-height:56rpx;overflow:hidden;}\n.dp-seckill-itemlist .product-info .p2 .t1{font-size:36rpx;}\n.dp-seckill-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-seckill-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\n.dp-seckill-itemlist .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}\n.dp-seckill-itemlist .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\n.dp-seckill-itemlist .product-info .h24 {height: 48rpx; line-height: 48rpx;padding: 2rpx 0; margin: 4rpx 0;}\n\n.dp-seckill-itemlist1{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.dp-seckill-itemlist1 .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:20rpx;border-radius:10rpx}\n.dp-seckill-itemlist1 .product-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 33%;position: relative;border-radius:4px;}\n.dp-seckill-itemlist1 .product-pic .image{width: 100%;height:auto}\n.dp-seckill-itemlist1 .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\n.dp-seckill-itemlist1 .product-info {width: 70%;padding:6rpx 0rpx 5rpx 20rpx;position: relative;}\n.dp-seckill-itemlist1 .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-seckill-itemlist1 .product-info .p2 {height:56rpx;line-height:56rpx;overflow:hidden;}\n.dp-seckill-itemlist1 .product-info .p2 .t1{font-size:36rpx;}\n.dp-seckill-itemlist1 .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-seckill-itemlist1 .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\n.dp-seckill-itemlist1 .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}\n.dp-seckill-itemlist1 .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\n.dp-seckill-itemlist1 .product-info .h24 {height: 48rpx; line-height: 48rpx;padding: 2rpx 0; margin: 4rpx 0;}\n.dp-seckill-itemlist1 .product-info .rate{font-size:24rpx;color: #FF3143;margin-top: 15rpx;}\n.dp-seckill-itemlist1 .product-info .rate_module{width: 200rpx;height: 15rpx;border-radius: 500rpx;background: #ffc1c1;}\n.dp-seckill-itemlist1 .product-info .rate_item{height: 15rpx;background: #ff065e;border-radius: 500rpx;position: relative;}\n.dp-seckill-itemlist1 .product-info .rate_item image{position: absolute;top: 0;bottom: 0;right: -15rpx;margin: auto 0;height: 30rpx;width: 30rpx;}\n.dp-seckill-itemlist1 .product-info .text{font-size:24rpx;color: #999ca7;margin-top: 10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.dp-seckill-itemlist1 .product-info .cost{font-size:24rpx;color: #999ca7;margin-top: 10rpx;}\n.dp-seckill-itemlist1 .product-info .cost text{text-decoration: line-through;}\n.dp-seckill-itemlist1 .product-info .price{font-weight: bold;color: #fd463e;position: relative;margin-top: 15rpx;font-size: 32rpx;}\n.dp-seckill-itemlist1 .product-info .price view{position: absolute;right: 0;bottom: 0;background: rgb(253, 70, 62);color: rgb(255, 255, 255);line-height: 60rpx;border-radius: 100rpx;font-size: 26rpx;font-weight: 700;width: 140rpx;}\n\n.dp-seckill-itemline{width:100%;display:flex;overflow-x:scroll;overflow-y:hidden}\n.dp-seckill-itemline .item{width: 220rpx;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;margin-right:4px}\n.dp-seckill-itemline .product-pic {width:220rpx;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\n.dp-seckill-itemline .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.dp-seckill-itemline .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}\n.dp-seckill-itemline .product-pic .tag{padding: 0 15rpx;line-height: 35rpx;display: inline-block;font-size: 24rpx;color: #fff;background: linear-gradient(to bottom right,#ff88c0,#ec3eda);position: absolute;left: 0;bottom: 0;border-radius: 0 10rpx 0 0}\n.dp-seckill-itemline .product-info {padding:20rpx 20rpx;position: relative;}\n.dp-seckill-itemline .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-seckill-itemline .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\n.dp-seckill-itemline .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\n.dp-seckill-itemline .product-info .p2-1 .t1{font-size:36rpx;}\n.dp-seckill-itemline .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-seckill-itemline .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\n.dp-seckill-itemline .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}\n.dp-seckill-itemline .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}\n.dp-seckill-itemline .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\n.dp-seckill-itemline .product-info .tag{padding: 2rpx 8rpx;background: #ffe7e7;color: #FF3143;font-size: 24rpx;}\n.dp-seckill-itemline .product-info .price{font-weight: bold;color: #fd463e;position: relative;margin-top: 10rpx;font-size: 27rpx;}\n.dp-seckill-itemline .product-info .price view{height: 45rpx;width: 45rpx;background: #fd463e;border-radius: 100rpx;}\n.dp-seckill-itemline .product-info .price view image{height: 25rpx;width: 25rpx;display: block;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-seckill.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-seckill.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839376025\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}