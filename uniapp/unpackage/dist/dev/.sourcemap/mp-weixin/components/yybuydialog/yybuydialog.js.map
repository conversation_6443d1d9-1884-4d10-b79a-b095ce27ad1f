{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/yybuydialog/yybuydialog.vue?c5e7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/yybuydialog/yybuydialog.vue?0efd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/yybuydialog/yybuydialog.vue?b810", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/yybuydialog/yybuydialog.vue?c4b1", "uni-app:///components/yybuydialog/yybuydialog.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/yybuydialog/yybuydialog.vue?9bbe", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/yybuydialog/yybuydialog.vue?b109"], "names": ["data", "ks", "product", "gui<PERSON>", "gui<PERSON><PERSON>", "ggselected", "nowguige", "gwcnum", "isload", "loading", "canaddcart", "yuyue_numtext", "pre_url", "props", "btntype", "default", "menuindex", "controller", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proid", "<PERSON><PERSON><PERSON>", "mounted", "methods", "getdata", "that", "app", "id", "buydialogChange", "ggchange", "tobuy", "addtobuy", "addcart", "ggid", "num", "gwcplus", "gwcminus", "gwcinput", "console", "gwcinputblur"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0C71B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;QACA;UACAnB;QACA;QACAmB;QACAA;QACAA;QACAA;QACA;UAAA;UACAA;QACA;MACA;IACA;IACAG;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACAvB;MACA;MACA;MACA;MACA;IACA;IACAwB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA7B;MACAA;MACAA;MACAA;MACA;MACA;IACA;IACA8B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAAL;QAAA;MAAA;MACAA;IACA;IACA;IACAM;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAN;QACA;MACA;MACA;QACAA;UAAAN;UAAAa;UAAAC;QAAA;UACA;YACAR;UACA;YACAA;UACA;QACA;MACA;MACA;QAAAN;QAAAa;QAAAC;MAAA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;UACAV;UACA;QACA;MACA;MAEA;QACA;MACA;MACA;IACA;IACA;IACAW;MACAC;MACA;MACA;MACA;;MAEA;MACA;MACA;MACAA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACAb;UACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/yybuydialog/yybuydialog.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./yybuydialog.vue?vue&type=template&id=7927503c&\"\nvar renderjs\nimport script from \"./yybuydialog.vue?vue&type=script&lang=js&\"\nexport * from \"./yybuydialog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./yybuydialog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/yybuydialog/yybuydialog.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yybuydialog.vue?vue&type=template&id=7927503c&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.nowguige.balance_price ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.isfuwu ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload && !_vm.isfuwu ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yybuydialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yybuydialog.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<view v-if=\"isload\">\r\n\t\t<view class=\"buydialog-mask\" @tap=\"buydialogChange\"></view>\r\n\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':''\">\r\n\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"image\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<image :src=\"nowguige.pic || product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"nowguige.pic || product.pic\"/>\r\n\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\">￥{{nowguige.sell_price}} <text v-if=\"nowguige.market_price > nowguige.sell_price\" class=\"t2\">￥{{nowguige.market_price}}</text></view>\r\n\t\t\t\t<view class=\"choosename\">已选规格: {{nowguige.name}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"nowguige.balance_price\" style=\"width:94%;margin:10rpx 3%;font-size:24rpx;\" :style=\"{color:t('color1')}\">首付款金额：{{nowguige.advance_price}}元，尾款金额：{{nowguige.balance_price}}元</view>\r\n\t\t\t<view style=\"max-height:50vh;overflow:scroll\">\r\n\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"buynum flex flex-y-center\">\r\n\t\t\t\t<view class=\"flex1\">{{yuyue_numtext?yuyue_numtext:'购买数量'}}：</view>\r\n\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t<view class=\"minus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" @tap=\"gwcminus\"/></view>\r\n\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\" @blur='gwcinputblur'></input>\r\n\t\t\t\t\t<view class=\"plus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\" @tap=\"gwcplus\"/></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\">\r\n\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"addtobuy\" v-if=\"isfuwu\" >确 定</button>\r\n\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-else >确 定</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tks:'',\r\n\t\t\t\tproduct:{},\r\n\t\t\t\tguigelist:{},\r\n\t\t\t\tguigedata:{},\r\n\t\t\t\tggselected:{},\r\n\t\t\t\tnowguige:{},\r\n\t\t\t\tgwcnum:1,\r\n\t\t\t\tisload:false,\r\n\t\t\t\tloading:false,\r\n\t\t\t\tcanaddcart:true,\r\n\t\t\t\tyuyue_numtext:'',\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tbtntype:{default:0},\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tcontroller:{default:'ApiYuyue'},\r\n\t\t\tneedaddcart:{default:true},\r\n\t\t\tproid:{},\r\n\t\t\tisfuwu:false\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(this.controller+'/getproductdetail',{id:that.proid},function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\tthat.gwcnum = res.product.minbuynum?res.product.minbuynum:1\r\n\t\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\t\tthat.guigedata = res.guigedata\r\n\t\t\t\t\tthat.yuyue_numtext = res.set.yuyue_numtext\r\n\t\t\t\t\tvar guigedata = res.guigedata;\r\n\t\t\t\t\tvar ggselected = [];\r\n\t\t\t\t\tfor (var i = 0; i < guigedata.length; i++) {\r\n\t\t\t\t\t\tggselected.push(0);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.ks = ggselected.join(','); \r\n\t\t\t\t\tthat.nowguige = that.guigelist[that.ks];\r\n\t\t\t\t\tthat.ggselected = ggselected\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\r\n\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tbuydialogChange:function(){\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t},\r\n\t\t\t//选择规格\r\n\t\t\tggchange: function (e){\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar itemk = e.currentTarget.dataset.itemk;\r\n\t\t\t\tvar ggselected = this.ggselected;\r\n\t\t\t\tggselected[itemk] = idx;\r\n\t\t\t\tvar ks = ggselected.join(',');\r\n\t\t\t\tthis.ggselected = ggselected;\r\n\t\t\t\tthis.ks = ks;\r\n\t\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\t},\r\n\t\t\ttobuy: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\tvar ggname = that.guigelist[ks].name;\r\n\t\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\r\n\t\t\t\tvar data = [];\r\n\t\t\t\tdata.ggname = ggname;\r\n\t\t\t\tdata.proid = proid;\r\n\t\t\t\tdata.ggid = ggid;\r\n\t\t\t\tdata.num = num;\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\tthis.$emit('currgg',data);\r\n\t\t\t},\r\n\t\t\taddtobuy: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\r\n\t\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\r\n\t\t\t\tif(!ggid || ggid==undefined){ app.error('请选择服务'); return;}\r\n\t\t\t\tapp.goto('/activity/yuyue/buy?prodata=' + prodata);\r\n\t\t\t},\r\n\t\t\t//加入购物车操作\r\n\t\t\taddcart: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\tif (stock < num) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.needaddcart){\r\n\t\t\t\t\tapp.post(this.controller+'/addcart', {proid: proid,ggid: ggid,num: num}, function (res) {\r\n\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\tapp.success('添加成功');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('addcart',{proid: proid,ggid: ggid,num: num});\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t},\r\n\t\t\t//加\r\n\t\t\tgwcplus: function (e) {\r\n\t\t\t\tvar gwcnum = this.gwcnum + 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tthis.gwcnum = this.gwcnum + 1;\r\n\t\t\t},\r\n\t\t\t//减\r\n\t\t\tgwcminus: function (e) {\r\n\t\t\t\tvar gwcnum = this.gwcnum - 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tif(this.product.minbuynum > 0) {\r\n\t\t\t\t\tif (gwcnum < this.product.minbuynum) {\r\n\t\t\t\t\t\tapp.error('该服务最少购买' + this.product.minbuynum + '份');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\tif (gwcnum <= 0) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = this.gwcnum - 1;\r\n\t\t\t},\r\n\t\t\t//输入\r\n\t\t\tgwcinput: function (e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\t\tif (gwcnum < 1) return 1;\r\n\t\t\t\t\r\n\t\t\t\t//if (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\t//\treturn this.guigelist[ks].stock > 0 ? this.guigelist[ks].stock : 1;\r\n\t\t\t\t//}\r\n\t\t\t\tconsole.log(gwcnum);\r\n\t\t\t\tthis.gwcnum = gwcnum;\r\n\t\t\t},\r\n\t\t\t// 输入失去焦点 起售数量判断\r\n\t\t\tgwcinputblur(e){\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\t\tif (gwcnum < 1) return 1;\r\n\t\t\t\tif(this.product.minbuynum > 0) {\r\n\t\t\t\t\tif (gwcnum < this.product.minbuynum) {\r\n\t\t\t\t\t\tapp.error('该服务最少购买' + this.product.minbuynum + '份');\r\n\t\t\t\t\t\treturn this.product.minbuynum;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = gwcnum;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\r\n.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}\r\n.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}\r\n.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}\r\n.buydialog .close .image{ width: 30rpx; height:30rpx; }\r\n.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:20rpx 0px; border-bottom:0; height: 190rpx;}\r\n.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\r\n.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\r\n.buydialog .title .price .t1{ font-size:26rpx}\r\n.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa}\r\n.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n\r\n.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }\r\n.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}\r\n.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}\r\n.buydialog .guigelist .item2{display: flex;align-items: center;min-height:40rpx;height:auto;margin-bottom:4px;border:0; border-radius:4rpx; padding:10rpx 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4;}\r\n.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}\r\n.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }\r\n.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n.buydialog .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .img{width:24rpx;height:24rpx}\r\n.buydialog .addnum .input{flex:1;width:70rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx}\r\n\r\n.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:100rpx;}\r\n.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yybuydialog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yybuydialog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839388982\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}