{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-countdown/uni-countdown.vue?256c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-countdown/uni-countdown.vue?2552", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-countdown/uni-countdown.vue?c88e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-countdown/uni-countdown.vue?f4df", "uni-app:///components/uni-countdown/uni-countdown.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-countdown/uni-countdown.vue?c756", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/uni-countdown/uni-countdown.vue?8246"], "names": ["name", "props", "showDay", "type", "default", "showColon", "start", "backgroundColor", "borderColor", "color", "splitorColor", "day", "hour", "minute", "second", "timestamp", "data", "timer", "syncFlag", "d", "h", "i", "s", "leftTime", "seconds", "watch", "immediate", "handler", "clearInterval", "created", "<PERSON><PERSON><PERSON><PERSON>", "methods", "to<PERSON><PERSON><PERSON><PERSON>", "timeUp", "countDown", "startData", "changeFlag"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA20B,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiB/1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,eAiBA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAd;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAR;MACAoB;MACAC;QACA;UACA;QACA;UACA;UACAC;QACA;MACA;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAF;EACA;EACAG;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAL;MACA;IACA;IACAM;MACA;MACA;QAAAtB;QAAAC;QAAAC;MACA;QACAH;QACA;UACAA;QACA;QACAC;QACAC;QACAC;MACA;QACA;MACA;MACA;QACAH;MACA;MACA;QACAC;MACA;MACA;QACAC;MACA;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAqB;MAAA;MACA;MACA;QACA;MACA;MACAP;MACA;MACA;QACA;QACA;UACA;UACA;QACA;QACA;MACA;IACA;IACAQ;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrMA;AAAA;AAAA;AAAA;AAAwrC,CAAgB,wmCAAG,EAAC,C;;;;;;;;;;;ACA5sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-countdown/uni-countdown.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-countdown.vue?vue&type=template&id=6cd74fee&\"\nvar renderjs\nimport script from \"./uni-countdown.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-countdown.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-countdown.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-countdown/uni-countdown.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-countdown.vue?vue&type=template&id=6cd74fee&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-countdown.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-countdown.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-countdown\">\r\n\t\t<text v-if=\"showDay\" :style=\"{ borderColor: borderColor, color: color, backgroundColor: backgroundColor }\"\r\n\t\t\tclass=\"uni-countdown__number\">{{ d }}</text>\r\n\t\t<text v-if=\"showDay\" :style=\"{ color: splitorColor }\" class=\"uni-countdown__splitor\">天</text>\r\n\t\t<text :style=\"{ borderColor: borderColor, color: color, backgroundColor: backgroundColor }\"\r\n\t\t\tclass=\"uni-countdown__number\">{{ h }}</text>\r\n\t\t<text :style=\"{ color: splitorColor }\" class=\"uni-countdown__splitor\">{{ showColon ? ':' : '时' }}</text>\r\n\t\t<text :style=\"{ borderColor: borderColor, color: color, backgroundColor: backgroundColor }\"\r\n\t\t\tclass=\"uni-countdown__number\">{{ i }}</text>\r\n\t\t<text :style=\"{ color: splitorColor }\" class=\"uni-countdown__splitor\">{{ showColon ? ':' : '分' }}</text>\r\n\t\t<text :style=\"{ borderColor: borderColor, color: color, backgroundColor: backgroundColor }\"\r\n\t\t\tclass=\"uni-countdown__number\">{{ s }}</text>\r\n\t\t<text v-if=\"!showColon\" :style=\"{ color: splitorColor }\" class=\"uni-countdown__splitor\">秒</text>\r\n\t</view>\r\n</template>\r\n<script>\r\n\t/**\r\n\t * Countdown 倒计时\r\n\t * @description 倒计时组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=25\r\n\t * @property {String} backgroundColor 背景色\r\n\t * @property {String} color 文字颜色\r\n\t * @property {Number} day 天数\r\n\t * @property {Number} hour 小时\r\n\t * @property {Number} minute 分钟\r\n\t * @property {Number} second 秒\r\n\t * @property {Number} timestamp 时间戳\r\n\t * @property {Boolean} showDay = [true|false] 是否显示天数\r\n\t * @property {Boolean} showColon = [true|false] 是否以冒号为分隔符\r\n\t * @property {String} splitorColor 分割符号颜色\r\n\t * @event {Function} timeup 倒计时时间到触发事件\r\n\t * @example <uni-countdown :day=\"1\" :hour=\"1\" :minute=\"12\" :second=\"40\"></uni-countdown>\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniCountdown',\r\n\t\tprops: {\r\n\t\t\tshowDay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowColon: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tstart: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#FFFFFF'\r\n\t\t\t},\r\n\t\t\tborderColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#000000'\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#000000'\r\n\t\t\t},\r\n\t\t\tsplitorColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#000000'\r\n\t\t\t},\r\n\t\t\tday: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\thour: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tminute: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tsecond: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\ttimestamp: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tsyncFlag: false,\r\n\t\t\t\td: '00',\r\n\t\t\t\th: '00',\r\n\t\t\t\ti: '00',\r\n\t\t\t\ts: '00',\r\n\t\t\t\tleftTime: 0,\r\n\t\t\t\tseconds: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tday(val) {\r\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\thour(val) {\r\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\tminute(val) {\r\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\tsecond(val) {\r\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\tstart: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal, oldVal) {\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tthis.startData();\r\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (!oldVal) return\r\n\t\t\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated: function(e) {\r\n\t\t\tthis.seconds = this.toSeconds(this.timestamp, this.day, this.hour, this.minute, this.second)\r\n\t\t\tthis.countDown()\r\n\t\t},\r\n\t\tbeforeDestroy() {\r\n\t\t\tclearInterval(this.timer)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoSeconds(timestamp, day, hours, minutes, seconds) {\r\n\t\t\t\tif (timestamp) {\r\n\t\t\t\t\treturn timestamp - parseInt(new Date().getTime() / 1000, 10)\r\n\t\t\t\t}\r\n\t\t\t\treturn day * 60 * 60 * 24 + hours * 60 * 60 + minutes * 60 + seconds\r\n\t\t\t},\r\n\t\t\ttimeUp() {\r\n\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\tthis.$emit('timeup')\r\n\t\t\t},\r\n\t\t\tcountDown() {\r\n\t\t\t\tlet seconds = this.seconds\r\n\t\t\t\tlet [day, hour, minute, second] = [0, 0, 0, 0]\r\n\t\t\t\tif (seconds > 0) {\r\n\t\t\t\t\tday = Math.floor(seconds / (60 * 60 * 24))\r\n\t\t\t\t\tif(!this.showDay){\r\n\t\t\t\t\t\tday = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t\thour = Math.floor(seconds / (60 * 60)) - (day * 24)\r\n\t\t\t\t\tminute = Math.floor(seconds / 60) - (day * 24 * 60) - (hour * 60)\r\n\t\t\t\t\tsecond = Math.floor(seconds) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.timeUp()\r\n\t\t\t\t}\r\n\t\t\t\tif (day < 10) {\r\n\t\t\t\t\tday = '0' + day\r\n\t\t\t\t}\r\n\t\t\t\tif (hour < 10) {\r\n\t\t\t\t\thour = '0' + hour\r\n\t\t\t\t}\r\n\t\t\t\tif (minute < 10) {\r\n\t\t\t\t\tminute = '0' + minute\r\n\t\t\t\t}\r\n\t\t\t\tif (second < 10) {\r\n\t\t\t\t\tsecond = '0' + second\r\n\t\t\t\t}\r\n\t\t\t\tthis.d = day\r\n\t\t\t\tthis.h = hour\r\n\t\t\t\tthis.i = minute\r\n\t\t\t\tthis.s = second\r\n\t\t\t},\r\n\t\t\tstartData() {\r\n\t\t\t\tthis.seconds = this.toSeconds(this.timestamp, this.day, this.hour, this.minute, this.second)\r\n\t\t\t\tif (this.seconds <= 0) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\n\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\tthis.countDown()\r\n\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\tthis.seconds--\r\n\t\t\t\t\tif (this.seconds < 0) {\r\n\t\t\t\t\t\tthis.timeUp()\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.countDown()\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\tchangeFlag() {\r\n\t\t\t\tif (!this.syncFlag) {\r\n\t\t\t\t\tthis.seconds = this.toSeconds(this.timestamp, this.day, this.hour, this.minute, this.second)\r\n\t\t\t\t\tthis.startData();\r\n\t\t\t\t\tthis.syncFlag = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.uni-countdown {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: flex-start;\r\n\t\tpadding: 2rpx 0;\r\n\t}\r\n\r\n\t.uni-countdown__splitor {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tline-height: 40rpx;\r\n\t\tpadding: 5rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.uni-countdown__number {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\twidth: 44rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tmargin: 5rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-countdown.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-countdown.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839377499\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}