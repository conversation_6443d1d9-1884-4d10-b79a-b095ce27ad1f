{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-location/dp-location.vue?da72", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-location/dp-location.vue?7f55", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-location/dp-location.vue?6a09", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-location/dp-location.vue?75ac", "uni-app:///components/dp-location/dp-location.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-location/dp-location.vue?6788", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-location/dp-location.vue?a3ee"], "names": ["data", "pre_url", "mid", "data_placeholder", "data_hrefurl", "keyword", "showsearch", "sysset", "latitude", "longitude", "showlevel", "curent_address", "arealist", "area", "show_nearbyarea", "ischangeaddress", "nearbyplacelist", "myaddresslist", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placekeyword", "suggestionplacelist", "mendianid", "mendian", "mendianlist", "mendianindex", "isshowmendianmodal", "needRefresh<PERSON><PERSON><PERSON><PERSON>", "headericonlist", "cacheExpireTime", "locationCache", "address", "poilist", "loc_area_type", "loc_range_type", "loc_range", "mendian_id", "mendian_name", "statusBarHeight", "homeNavigationCustom", "shengzhixiaxian", "props", "params", "mounted", "that", "methods", "getdata", "searchgoto", "url", "app", "checkMode", "checkLocation", "mendian_isinit", "checkAreaByShowlevel", "initCityAreaList", "uni", "method", "header", "success", "item2", "newchildren", "item1", "newlist", "areachange", "area_name", "showarea", "firstareaname", "closeNearbyBox", "showNearbyBox", "nearbylist", "changeAddress", "add<PERSON>y<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "cancelChangeAddress", "refreshAddress", "console", "showAllAddress", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chooseSuggestionAddress", "refreshNearbyPlace", "placekeywordInput", "searchPlace", "region", "showMendianModal", "hideMendianModal", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6J71B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACArB;QACAC;QACAI;QACAiB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA1B;MACA;MACA2B;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;IACAzC;EACA;EACA0C;IACA;IACA;IACAC;IACAA;IACAA;IACAA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACAC;IACA;IACAC;MACA;MACAD;QACAL;QACA;UACAK;UACAA;UACA;YACAL;UACA;UACAA;QACA;UACAA;UACAK;UACAA;UACAL;QACA;MACA;IACA;IACA;IACAO;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAF;YACAL;YACAA;YACA;YACAK;cAAAxC;cAAAC;YAAA;cACA;gBACAkC;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACA;kBACA;oBACAA;oBACAA;kBACA;oBACAA;oBACAA;kBACA;oBACAA;oBACAA;kBACA;kBACAA;kBACAA;kBACAK;kBACAL;gBACA;kBACAA;kBACAA;kBACAA;kBACAA;kBACAK;kBACAL;kBACAA;gBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;QACA;UACAK;YACAL;YACAA;YACA;YACA;YACA;cACAR;YACA;YACA;cACAgB;YACA;YACAH;cAAAxC;cAAAC;cAAA0B;cAAAgB;YAAA;cACA;gBACAR;gBACAA;gBACAA;gBACAA;gBACAA;gBACAK;gBACA;kBACAL;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;YACAvC;UACA;YACAA;UACA;YACAA;UACA;UACA8B;UACAK;QACA;MACA;IACA;IACAK;MACA;MACA;MACA;QACAC;UACAP;UACA/C;UACAuD;UACAC;YAAA;UAAA;UACAC;YACA;cACA;cACA;cACA;gBACA;gBACA;kBACA;kBACA;kBACA;oBACA;oBACA;oBACA;sBACA;wBACAd;sBACA;oBACA;oBACA;oBACAe;oBACAC;kBACA;kBACAC;gBACA;kBACAA;gBACA;;gBACAC;cACA;cACAlB;YACA;cACAA;YACA;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;QACA;UACAC;QACA;MACA;MAEAtB;MACAA;MACA;MACAA;MACAA;MAEA;QACA;QACA;UACAoB;QACA;QACA;QACAf;UACAlB;QACA;UACAa;UACA;YACAA;YACAA;YACAA;YACAA;YACAK;YACAL;UACA;YACAK;UACA;QACA;MACA;IACA;IACAkB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;MACA;QACAzB;MACA;MACA;MACA;QACAA;QACAA;MACA;IACA;IACA0B;MACA;IACA;IACAC;MACA;MACA;MACAtB;IACA;IACAuB;MACA;MACA5B;MACAK;QACAwB;MACA;QACA7B;QACA;UACAA;QACA;MACA;IACA;IACA8B;MACA;IACA;IACAC;MACA;MACA/B;MACAK;QACA;QACA;QACA;QACAA;UACAxC;UACAC;UACA+D;QACA;UACA7B;UACA;YACA;YAEAA;YACAA;YACAA;YACAA;YAEAA;YACAA;YACAA;YACAA;YACAA;YACAK;YACAL;YACAA;UACA;QACA;MACA;QACAgC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAlC;MACAA;MACAA;MAEAA;MACAA;MACAA;MACAA;MACAA;MACAA;MAEAK;MACAL;MACAA;MACAA;IACA;IACAmC;MACA;MACA;MACA;MACAnC;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAK;MACAL;MACAA;MACAA;IACA;IACAoC;MACA;MACA;MACA;MACApC;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAK;MACAL;MACAA;MACAA;IACA;IACAqC;MAAA;MAAA;MACA;MACA;QACAxE;QACAC;MACA;MACA;QACAkC;QACAK;UACAxC;UACAC;UACA+D;QACA;UACA7B;UACA;YACA;YACAA;YACAK;UACA;QACA;MACA;IACA;IACAiC;MACA;IACA;IACAC;MACA;MACA;QACAvC;QACA;MACA;MACA;MAEA;MACA;QACA;QACA;UACAwC;QACA;UACAA;QACA;UACAA;QACA;MACA;MACAxC;MACAK;QACAxC;QACAC;QACA0E;QACA9E;MACA;QACAsC;QACA;UACAA;QACA;MACA;IACA;IACA;;IAEA;IACAyC;MACA;MACA;QACAzC;MACA;QACAK;UACAxC;UACAC;QACA;UACAkC;UACA;YACAA;YACAA;UACA;YACAK;UACA;QACA;MACA;IACA;IACAqC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAtC;MACAA;MACAA;MACA;MACAL;MACA;MACArB;MACAA;MACAA;MACAA;MACAA;MACAO;MACAmB;MACAL;MACAA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5rBA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-location/dp-location.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-location.vue?vue&type=template&id=ab42199c&\"\nvar renderjs\nimport script from \"./dp-location.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-location.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-location.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-location/dp-location.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-location.vue?vue&type=template&id=ab42199c&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.params.showicon == 1 && _vm.data.length > 0\n  var m0 =\n    _vm.sysset.mode == 2 && _vm.sysset.loc_area_type == 1 && _vm.show_nearbyarea\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.sysset.mode == 2 && _vm.sysset.loc_area_type == 1 && _vm.show_nearbyarea\n      ? _vm.t(\"color1\")\n      : null\n  var g1 =\n    _vm.sysset.mode == 2 && _vm.sysset.loc_area_type == 1 && _vm.show_nearbyarea\n      ? _vm.suggestionplacelist.length\n      : null\n  var g2 =\n    _vm.sysset.mode == 2 && _vm.sysset.loc_area_type == 1 && _vm.show_nearbyarea\n      ? _vm.myaddresslist.length\n      : null\n  var m2 =\n    _vm.sysset.mode == 2 && _vm.sysset.loc_area_type == 1 && _vm.show_nearbyarea\n      ? _vm.t(\"color1\")\n      : null\n  var l0 =\n    _vm.sysset.mode == 3 && _vm.isshowmendianmodal && _vm.isshowmendianmodal\n      ? _vm.__map(_vm.mendianlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 =\n            item.id == _vm.locationCache.mendian_id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        g1: g1,\n        g2: g2,\n        m2: m2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-location.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-location.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-search\" :style=\"{\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',\r\n\tpadding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',\r\n\tborderColor:params.bordercolor,\r\n\tborderRadius:params.borderradius+'px',\r\n\tcolor:params.color,\r\n\tborderStyle:'solid',\r\n\tborderWidth:'1px'\r\n}\">\r\n    <block>\r\n    \t<view class=\"dp-header-location-box\">\r\n    \t\t<!-- 显示定位：城市|地标 Start -->\r\n    \t\t<!-- 当前城市 -->\r\n    \t\t<view v-if=\"sysset.mode==2 && sysset.loc_area_type==0\" class=\"dp-header-location\">\r\n\t\t\t\t\t<uni-data-picker class=\"dp-header-picker\" :class=\"params.showsearch==1?'dp-header-address-widthfixed':'dp-header-address'\" :localdata=\"arealist\" popup-title=\"地区\" @change=\"areachange\"  :placeholder=\"'地区'\">\r\n\t\t\t\t\t\t<view>{{locationCache.address?locationCache.address:'请选择定位'}}</view>\r\n\t\t\t\t\t</uni-data-picker>\r\n    \t\t\t<view class=\"dp-header-more\"><text class=\"iconfont iconjiantou\" style=\"font-size: 24rpx;\"></text></view>\r\n    \t\t</view>\r\n    \t\t<!-- 当前地址（商圈地址等） -->\r\n    \t\t<view v-if=\"sysset.mode==2 && sysset.loc_area_type==1\" class=\"dp-header-location\">\r\n    \t\t\t<view class=\"flex-y-center\" @tap=\"showNearbyBox\">\r\n    \t\t\t\t<view :class=\"params.showsearch==1?'dp-header-address-widthfixed':'dp-header-address'\">{{locationCache.address?locationCache.address:'请选择定位'}}</view>\r\n    \t\t\t\t<view class=\"dp-header-more\"><text class=\"iconfont iconjiantou\" style=\"font-size: 24rpx;\"></text></view>\r\n    \t\t\t</view>\r\n    \t\t</view>\r\n\t\t\t\t<view v-if=\"sysset.mode==3\" class=\"dp-header-location\">\r\n\t\t\t\t\t<view class=\"flex-y-center\" @tap=\"showMendianModal\">\r\n\t\t\t\t\t\t<view :class=\"params.showsearch==1?'dp-header-address-widthfixed':'dp-header-address'\">{{locationCache.mendian_name?locationCache.mendian_name:'请选择门店'}}</view>\r\n\t\t\t\t\t\t<view class=\"dp-header-more\"><text class=\"iconfont iconjiantou\" style=\"font-size: 24rpx;\"></text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"dp-location-search\" v-if=\"params.showsearch==1\">\r\n\t\t\t\t\t<input type=\"text\" v-model=\"keyword\" :placeholder=\"params.placeholder\" placeholder-style=\"color:#c0c0c0;font-size:24rpx\" style=\"flex: 1;\"  @confirm=\"searchgoto\" :data-url=\"params.hrefurl\">\r\n\t\t\t\t\t<image class=\"dp-location-search-icon\" :src=\"pre_url+'/static/img/search.png'\"  @tap=\"searchgoto\" :data-url=\"params.hrefurl\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"dp-location-iconlist\" v-if=\"params.showicon==1 && data.length>0\">\r\n\t\t\t\t\t<view class=\"dp-location-icon\" v-for=\"(item,index) in data\" :key=\"index\" @tap=\"goto\" :data-url=\"item.hrefurl\">\r\n\t\t\t\t\t\t<image :src=\"item.imgurl\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n    \t</view>\r\n    </block>\r\n    <!-- 显示定位：城市|地标 End -->\r\n\t\t<!-- modal start-->\r\n\t\t<!-- 附近商圈地址 -->\r\n\t\t<view v-if=\"sysset.mode==2 && sysset.loc_area_type==1 && show_nearbyarea\" class=\"dp-location-modal\">\r\n\t\t\t<view :style=\"{height:(34+statusBarHeight)+'px'}\" v-if='homeNavigationCustom != 0'></view>\r\n\t\t\t<view class=\"dp-location-modal-content\">\r\n\t\t\t\t<view class=\"dp-header-nearby-box\">\r\n\t\t\t\t\t<view class=\"dp-header-nearby-body\">\r\n\t\t\t\t\t\t<view class=\"dp-header-nearby-search\">\r\n\t\t\t\t\t\t\t<view class=\"dp-header-nearby-close\" @tap=\"closeNearbyBox\"><image :src=\"pre_url+'/static/img/location/close-dark.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"dp-header-nearby-input\" >\r\n\t\t\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"商圈/大厦/住宅\" placeholder-style=\"font-size:26rpx\" :value=\"placekeyword\" @input=\"placekeywordInput\" @confirm=\"searchPlace\"/>\r\n\t\t\t\t\t\t\t\t<button class=\"searchbtn\" :style=\"{borderColor:t('color1'),color:'#FFF',backgroundColor:t('color1')}\" @tap=\"searchPlace\">搜索</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"dp-suggestion-box\" v-if=\"suggestionplacelist.length>0\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in suggestionplacelist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"dp-suggestion-place\" @tap=\"chooseSuggestionAddress\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"s-title\">{{item.title}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"s-info flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"s-area\">{{item.city}} {{item.district}} </text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"s-address\">{{item.address}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"dp-header-nearby-content flex-bt\">\r\n\t\t\t\t\t\t\t<view>已选：{{curent_address}}</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-xy-center\" @tap=\"refreshAddress\">\r\n\t\t\t\t\t\t\t\t<image class=\"dp-header-nearby-imgicon\" :src=\"pre_url+'/static/img/location/location-dark.png'\">\r\n\t\t\t\t\t\t\t\t<text class=\"dp-header-nearby-tip\">重新定位</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"dp-header-nearby-content\" style=\"margin-top: 20rpx;\">\r\n\t\t\t\t\t\t\t<view class=\"dp-header-nearby-title flex-y-center\">\r\n\t\t\t\t\t\t\t\t<image class=\"dp-header-nearby-imgicon\" :src=\"pre_url+'/static/img/location/home-dark.png'\"></image>\r\n\t\t\t\t\t\t\t\t<text>我的地址</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"dp-header-nearby-list\">\r\n\t\t\t\t\t\t\t\t<view class=\"dp-header-nearby-info\" v-for=\"(item,index) in myaddresslist\" :key=\"index\" v-if=\"index>3?(isshowalladdress?1==1:1==2):1==1\" @tap=\"chooseMyAddress\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"dp-header-nearby-txt\">{{item.name}} {{item.tel}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"dp-header-nearby-all flex-y-center\" @tap=\"showAllAddress\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"myaddresslist.length>0\">\r\n\t\t\t\t\t\t\t\t\t<text>{{isshowalladdress?'收起全部地址':'展开更多地址'}} </text><image :src=\"pre_url+'/static/img/location/'+(isshowalladdress?'up-grey.png':'down-grey.png')\"></image>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<text v-else>-暂无地址-</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 附近地址 -->\r\n\t\t\t\t\t\t<view class=\"dp-header-nearby-content\" style=\"margin-top: 20rpx;\">\r\n\t\t\t\t\t\t\t<view class=\"dp-header-nearby-title flex-y-center\">\r\n\t\t\t\t\t\t\t\t<image class=\"dp-header-nearby-imgicon\" :src=\"pre_url+'/static/img/location/address-dark.png'\"></image>\r\n\t\t\t\t\t\t\t\t<text>附近地址</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"dp-header-nearby-list\">\r\n\t\t\t\t\t\t\t\t<view class=\"dp-header-nearby-info\"  v-for=\"(item,index) in nearbyplacelist\" :key=\"index\"  @tap=\"chooseNearbyAddress\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"dp-header-nearby-txt\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"dp-header-location-bottom flex-xy-center\" :style=\"{color:t('color1')}\" @tap=\"addMyAddress\">\r\n\t\t\t\t\t<text class=\"dp-location-add-address\">+</text><text style=\"padding-top: 10rpx;\">新增收货地址</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 附近商圈地址 -->\r\n\t\t<!-- 门店选择start -->\r\n\t\t<view v-if=\"sysset.mode==3 && isshowmendianmodal\" class=\"dp-location-modal dp-location-modal-mendian\">\r\n\t\t\t<view class=\"dp-location-modal-content\">\r\n\t\t\t\t<view class=\"popup__container popup_mendian\" v-if=\"isshowmendianmodal\" style=\"z-index: 999999;\">\r\n\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideMendianModal\"></view>\r\n\t\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">请选择门店</text>\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideMendianModal\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in mendianlist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"mendian-info\" @tap=\"changeMendian\" :data-index=\"index\" :data-id=\"item.id\" :style=\"{background:(item.id==locationCache.mendian_id?'rgba('+t('color1rgb')+',0.1)':'')}\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"b1\"><image :src=\"item.pic\"></image></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"b2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t2 flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"mendian-distance\">{{item.distance}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.address || item.area\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"line\" v-if=\"item.distance\"> </view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"mendian-address\"> {{item.address?item.address:item.area}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 门店选择end -->\r\n\t\t<!-- modal end -->\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app =getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tpre_url:getApp().globalData.pre_url,\r\n\t\t\t\tmid:app.globalData.mid,\r\n\t\t\t\tdata_placeholder:'',//搜索提示\r\n\t\t\t\tdata_hrefurl:'',\r\n\t\t\t\tkeyword:'',\r\n\t\t\t\tshowsearch:2,\r\n\t\t\t\t\r\n\t\t\t\t//定位模式\r\n\t\t\t\tsysset:{},\r\n\t\t\t\tlatitude:'',\r\n\t\t\t\tlongitude:'',\r\n\t\t\t\tshowlevel:2,\r\n\t\t\t\tcurent_address:'',//当前位置: 省市县或者地标\r\n\t\t\t\tarealist:[],\r\n\t\t\t\tarea:'',//地址拼接 北京,北京市,朝阳区\r\n\t\t\t\tshow_nearbyarea:false,\r\n\t\t\t\tischangeaddress:false,\r\n\t\t\t\tnearbyplacelist:[],\r\n\t\t\t\tmyaddresslist:[],\r\n\t\t\t\tisshowalladdress:false,\r\n\t\t\t\tplacekeyword:'',\r\n\t\t\t\tsuggestionplacelist:[],\r\n\t\t\t\t\r\n\t\t\t\t//门店模式 显示最近的一个门店\r\n\t\t\t\tmendianid:0,\r\n\t\t\t\tmendian:{},\r\n\t\t\t\tmendianlist:[],\r\n\t\t\t\tmendianindex:-1,\r\n\t\t\t\tisshowmendianmodal:false,\r\n\t\t\t\tneedRefreshMyaddress:false,\r\n\t\t\t\theadericonlist:[],\r\n\t\t\t\tcacheExpireTime:10,//缓存过期时间10分钟\r\n\t\t\t\tlocationCache:{\r\n\t\t\t\t\tlatitude:'',\r\n\t\t\t\t\tlongitude:'',\r\n\t\t\t\t\tarea:'',\r\n\t\t\t\t\taddress:'',\r\n\t\t\t\t\tpoilist:[],\r\n\t\t\t\t\tloc_area_type:-1,\r\n\t\t\t\t\tloc_range_type:-1,\r\n\t\t\t\t\tloc_range:'',\r\n\t\t\t\t\tmendian_id:0,\r\n\t\t\t\t\tmendian_name:'',\r\n\t\t\t\t\tshowlevel:2\r\n\t\t\t\t},\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\thomeNavigationCustom: app.globalData.homeNavigationCustom,\r\n\t\t\t\tshengzhixiaxian:{},//省直辖县级行政区划、自治区直辖县级行政区划下的第一个市\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tparams: {},\r\n\t\t\tdata: {}\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\t\t\t\tthat.statusBarHeight = sysinfo.statusBarHeight;\r\n\t\t\t\tthat.showlevel = that.params.showlevel || 2;\r\n\t\t\t\tthat.locationCache  = app.getLocationCache();\r\n\t\t\t\tthat.checkMode();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tthis.checkAreaByShowlevel();\r\n\t\t\t\tthis.$emit('getdata');\r\n\t\t\t},\r\n\t\t\tsearchgoto:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar keyword = that.keyword;\r\n\t\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\t\tif (url.indexOf('?') > 0) {\r\n\t\t\t\t\t\turl += '&keyword='+keyword;\r\n\t\t\t\t}else{\r\n\t\t\t\t\t\turl += '?keyword='+keyword;\r\n\t\t\t\t}\r\n\t\t\t\tvar opentype = e.currentTarget.dataset.opentype\r\n\t\t\t\tapp.goto(url,opentype);\r\n\t\t\t},\r\n\t\t\tcheckMode:function(){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tapp.get('ApiIndex/checkMode', {}, function(res) {\r\n\t\t\t\t\tthat.sysset = res.sysset\r\n\t\t\t\t\tif(that.sysset.mode==2){\r\n\t\t\t\t\t\tapp.setLocationCache('mendian_id',0)\r\n\t\t\t\t\t\tapp.setLocationCache('mendian_name','')\r\n\t\t\t\t\t\tif(that.sysset.loc_area_type==0){\r\n\t\t\t\t\t\t\tthat.initCityAreaList()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.checkLocation()\r\n\t\t\t\t\t}else if(that.sysset.mode==3){\r\n\t\t\t\t\t\tthat.area = ''\r\n\t\t\t\t\t\tapp.setLocationCache('area','')\r\n\t\t\t\t\t\tapp.setLocationCache('address','')\r\n\t\t\t\t\t\tthat.checkLocation()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//头部定位start\r\n\t\t\tcheckLocation:function(){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\tif(that.sysset.mode==2){\r\n\t\t\t\t\tvar loc_area_type = that.sysset.loc_area_type;\r\n\t\t\t\t\tvar loc_range_type = that.sysset.loc_range_type;\r\n\t\t\t\t\tvar loc_range = that.sysset.loc_range;\r\n\t\t\t\t\tvar cache_loc_area_type = locationCache.loc_area_type;\r\n\t\t\t\t\tvar cache_loc_range_type = locationCache.loc_range_type;\r\n\t\t\t\t\tvar cache_loc_range = locationCache.loc_range;\r\n\t\t\t\t\tvar cachearea = locationCache.area;\r\n\t\t\t\t\tvar cacheshowarea = locationCache.address;\r\n\t\t\t\t\t//缓存为空 或 显示城市和当前地址切换 或 同城和自定义范围切换 或 显示距离发生变化\r\n\t\t\t\t\tif(!cacheshowarea || (loc_area_type==0 && locationCache.showlevel!=that.showlevel) || (cacheshowarea && (cache_loc_area_type!=loc_area_type || cache_loc_range_type!=loc_range_type || cache_loc_range!=loc_range))){\r\n\t\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t\t\t//如果从当前地址切到当前城市，则重新定位用户位置\r\n\t\t\t\t\t\t\t\tapp.post('ApiAddress/getAreaByLocation', {latitude:that.latitude,longitude:that.longitude}, function(res) {\r\n\t\t\t\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.loc_area_type = loc_area_type\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.loc_range_type = loc_range_type\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.loc_range = loc_range\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.latitude = that.latitude\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.longitude = that.longitude\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.showlevel = that.showlevel\r\n\t\t\t\t\t\t\t\t\t\tif(loc_area_type==0){\r\n\t\t\t\t\t\t\t\t\t\t\tif(that.showlevel==1){\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.address = res.province\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.area = res.province\r\n\t\t\t\t\t\t\t\t\t\t\t}else if(that.showlevel==2){\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.address = res.city\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.area = res.province+','+res.city\r\n\t\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.address = res.district\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.area = res.province+','+res.city+','+res.district\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tthat.area = that.locationCache.area\r\n\t\t\t\t\t\t\t\t\t\t\tthat.curent_address = that.locationCache.address\r\n\t\t\t\t\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\t\t\t\t\t}else if(loc_area_type==1){\r\n\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.address = res.landmark\r\n\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.area = res.province+','+res.city+','+res.district\r\n\t\t\t\t\t\t\t\t\t\t\tthat.area = that.locationCache.area\r\n\t\t\t\t\t\t\t\t\t\t\tthat.curent_address = that.locationCache.address\r\n\t\t\t\t\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\t\t\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.area = that.locationCache.area\r\n\t\t\t\t\t\tthat.curent_address = that.locationCache.address\r\n\t\t\t\t\t\tthat.latitude = that.locationCache.latitude\r\n\t\t\t\t\t\tthat.longitude = that.locationCache.longitude\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(that.sysset.mode==3){\r\n\t\t\t\t\tif(!locationCache.latitude || !locationCache.longitude || !locationCache.mendian_id){\r\n\t\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t\t\tvar mendian_id = 0;\r\n\t\t\t\t\t\t\t\tvar mendian_isinit = 0;\r\n\t\t\t\t\t\t\t\tif(locationCache.mendian_id){\r\n\t\t\t\t\t\t\t\t\tmendian_id = locationCache.mendian_id\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif(locationCache.mendian_isinit){\r\n\t\t\t\t\t\t\t\t\tmendian_isinit = locationCache.mendian_isinit\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tapp.post('ApiMendian/getNearByMendian', {latitude:that.latitude,longitude:that.longitude,mendian_id:mendian_id,mendian_isinit:mendian_isinit}, function(data) {\r\n\t\t\t\t\t\t\t\t\tif(data.status==1){\r\n\t\t\t\t\t\t\t\t\t\t that.locationCache.latitude = that.latitude;\r\n\t\t\t\t\t\t\t\t\t\t that.locationCache.longitude = that.longitude\r\n\t\t\t\t\t\t\t\t\t\t that.locationCache.mendian_id = data.mendian.id\r\n\t\t\t\t\t\t\t\t\t\t that.locationCache.mendian_name = data.mendian.name\r\n\t\t\t\t\t\t\t\t\t\t that.locationCache.mendian_isinit = 0\r\n\t\t\t\t\t\t\t\t\t\t app.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t\t\t\t if(data.mendian_id!=mendian_id){\r\n\t\t\t\t\t\t\t\t\t\t\t that.getdata();\r\n\t\t\t\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcheckAreaByShowlevel:function(){\r\n\t\t\t\tvar that  = this\r\n\t\t\t\tif(that.sysset.mode==2 && that.sysset.loc_range_type==0){\r\n\t\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\t\tif(locationCache && locationCache.area){\r\n\t\t\t\t\t\tvar area = '';\r\n\t\t\t\t\t\tvar areaArr = locationCache.area.split(',');\r\n\t\t\t\t\t\tvar showlevel = locationCache.showlevel?locationCache.showlevel:that.showlevel\r\n\t\t\t\t\t\tif(showlevel==1 && areaArr.length>0){\r\n\t\t\t\t\t\t\tarea = areaArr[0]\r\n\t\t\t\t\t\t}else if(showlevel==2 && areaArr.length>1){\r\n\t\t\t\t\t\t\tarea = areaArr[0] + ','+areaArr[1]\r\n\t\t\t\t\t\t}else if(showlevel==3 && areaArr.length>2){\r\n\t\t\t\t\t\t\tarea = areaArr[0] + ','+areaArr[1] + ','+areaArr[2]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.locationCache.area = area;\r\n\t\t\t\t\t\tapp.setLocationCache('area',area,that.cacheExpireTime)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinitCityAreaList:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t//地区加载\r\n\t\t\t\tif(that.arealist.length==0){\r\n\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\r\n\t\t\t\t\t\tdata: {},\r\n\t\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\t\tif(that.showlevel<3){\r\n\t\t\t\t\t\t\t\tvar newlist = [];\r\n\t\t\t\t\t\t\t\tvar arealist = res2.data\r\n\t\t\t\t\t\t\t\tfor(var i in arealist){\r\n\t\t\t\t\t\t\t\t\tvar item1 = arealist[i]\r\n\t\t\t\t\t\t\t\t\tif(that.showlevel==2){\r\n\t\t\t\t\t\t\t\t\t\tvar children = item1.children //市\r\n\t\t\t\t\t\t\t\t\t\tvar newchildren = [];\r\n\t\t\t\t\t\t\t\t\t\tfor(var j in children){\r\n\t\t\t\t\t\t\t\t\t\t\tvar item2 = children[j]\r\n\t\t\t\t\t\t\t\t\t\t\t//获取 [自治区直辖县级行政区划]等特殊地区下的第一个市 start\r\n\t\t\t\t\t\t\t\t\t\t\tif(item2.text && (item2.text == '自治区直辖县级行政区划' || item2.text == '省直辖县级行政区划')){\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (item2.children.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat.shengzhixiaxian[item2.value] = item2.children[0]['text'];\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t//end\r\n\t\t\t\t\t\t\t\t\t\t\titem2.children = []; //去掉三级-县的数据\r\n\t\t\t\t\t\t\t\t\t\t\tnewchildren.push(item2)\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\titem1.children = newchildren\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\titem1.children = []; ////去掉二级-市的数据\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tnewlist.push(item1)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.arealist = newlist\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tthat.arealist = res2.data\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tareachange:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tconst value = e.detail.value\r\n\t\t\t\tvar area_name = [];\r\n\t\t\t\tvar showarea = ''\r\n\t\t\t\tvar firstareaname = ''; //[自治区直辖县级行政区划]等特殊地区下的第一个市；\r\n\t\t\t\tfor(var i=0;i<that.showlevel;i++){\r\n\t\t\t\t\tarea_name.push(value[i].text)\r\n\t\t\t\t\tshowarea = value[i].text\r\n\t\t\t\t\tif(that.shengzhixiaxian.hasOwnProperty(value[i].value)){\r\n\t\t\t\t\t\tfirstareaname = that.shengzhixiaxian[value[i].value];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.area = area_name.join(',')\r\n\t\t\t\tthat.curent_address = showarea\r\n\t\t\t\t//全局缓存\r\n\t\t\t\tthat.locationCache.area = area_name.join(',')\r\n\t\t\t\tthat.locationCache.address = showarea\r\n\t\t\t\r\n\t\t\t\tif(that.sysset.loc_area_type==0){\r\n\t\t\t\t\t//拼接市区\r\n\t\t\t\t\tif(firstareaname){\r\n\t\t\t\t\t\tarea_name.push(firstareaname)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//获取地址中心地标\r\n\t\t\t\t\tapp.post('ApiAddress/addressToZuobiao', {\r\n\t\t\t\t\t\taddress:area_name.join('')\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tthat.latitude = resp.latitude\r\n\t\t\t\t\t\t\tthat.longitude = resp.longitude\r\n\t\t\t\t\t\t\tthat.locationCache.latitude = resp.latitude;\r\n\t\t\t\t\t\t\tthat.locationCache.longitude = resp.longitude;\r\n\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.error('地址解析错误');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcloseNearbyBox:function(){\r\n\t\t\t\tthis.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\tshowNearbyBox:function(){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tthis.show_nearbyarea = true\r\n\t\t\t\tthis.placekeyword = ''\r\n\t\t\t\tthis.suggestionplacelist = []\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\tvar nearbylist = locationCache.poilist\r\n\t\t\t\tif(!nearbylist){\r\n\t\t\t\t\tnearbylist = [];\r\n\t\t\t\t}\r\n\t\t\t\tif(nearbylist && nearbylist.length>0){\r\n\t\t\t\t\tthis.nearbyplacelist = nearbylist\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.refreshNearbyPlace()\r\n\t\t\t\t}\r\n\t\t\t\t//获取我的收货地址\r\n\t\t\t\tif(app.globalData.mid){\r\n\t\t\t\t\tthat.loading = true\r\n\t\t\t\t\tthat.getMyAddress()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangeAddress:function(){\r\n\t\t\t\tthis.ischangeaddress = true\r\n\t\t\t},\r\n\t\t\taddMyAddress:function(e){\r\n\t\t\t\tthis.needRefreshMyaddress = true;\r\n\t\t\t\tthis.show_nearbyarea = false\r\n\t\t\t\tapp.goto(\"/pagesB/address/addressadd?type=1\")\r\n\t\t\t},\r\n\t\t\tgetMyAddress:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true\r\n\t\t\t\tapp.post('ApiAddress/address', {\r\n\t\t\t\t\ttype:1\r\n\t\t\t\t}, function(resp) {\r\n\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\tthat.myaddresslist = resp.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcancelChangeAddress:function(){\r\n\t\t\t\tthis.ischangeaddress = false\r\n\t\t\t},\r\n\t\t\trefreshAddress:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tthat.loading = true\r\n\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\t//请求当前地址[取商圈地址]\r\n\t\t\t\t\tapp.post('ApiAddress/getAreaByLocation', {\r\n\t\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\t\ttype:1\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tvar data = resp.data\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthat.latitude = latitude\r\n\t\t\t\t\t\t\tthat.longitude = longitude\r\n\t\t\t\t\t\t\tthat.curent_address = data.address_reference.landmark\r\n\t\t\t\t\t\t\tthat.nearbyplacelist = data.pois\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthat.locationCache.area = data.address_component.province+','+data.address_component.city+','+data.address_component.district\r\n\t\t\t\t\t\t\tthat.locationCache.address = data.address_reference.landmark\r\n\t\t\t\t\t\t\tthat.locationCache.latitude = latitude\r\n\t\t\t\t\t\t\tthat.locationCache.longitude = longitude\r\n\t\t\t\t\t\t\tthat.locationCache.poilist = data.pois\r\n\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t},function(res){\r\n\t\t\t\t\tconsole.error(res);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowAllAddress:function(){\r\n\t\t\t\tthis.isshowalladdress = this.isshowalladdress?false:true\r\n\t\t\t},\r\n\t\t\tchooseMyAddress:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tvar info = that.myaddresslist[index]\r\n\t\t\t\tthat.curent_address = info.address\r\n\t\t\t\tthat.latitude = info.latitude\r\n\t\t\t\tthat.longitude = info.longitude\r\n\t\t\t\t\r\n\t\t\t\tthat.locationCache.area = info.province+','+info.city+','+info.district\r\n\t\t\t\tthat.locationCache.address = info.address\r\n\t\t\t\tthat.locationCache.latitude = info.latitude\r\n\t\t\t\tthat.locationCache.longitude = info.longitude\r\n\t\t\t\tthat.locationCache.poilist = info.pois\r\n\t\t\t\tthat.area = that.locationCache.area\r\n\t\t\t\t\r\n\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\tthat.getdata()\r\n\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\tchooseNearbyAddress:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tvar info = that.nearbyplacelist[index]\r\n\t\t\t\tthat.curent_address = info.title\r\n\t\t\t\tthat.latitude = info.location.lat\r\n\t\t\t\tthat.longitude = info.location.lng\r\n\t\t\t\tthat.locationCache.area = info.ad_info.province+','+info.ad_info.city+','+info.ad_info.district\r\n\t\t\t\tthat.locationCache.address = that.curent_address\r\n\t\t\t\tthat.locationCache.latitude = that.latitude\r\n\t\t\t\tthat.locationCache.longitude = that.longitude\r\n\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\tthat.getdata()\r\n\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\tchooseSuggestionAddress:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tvar info = that.suggestionplacelist[index]\r\n\t\t\t\tthat.curent_address = info.title\r\n\t\t\t\tthat.latitude = info.location.lat\r\n\t\t\t\tthat.longitude = info.location.lng\r\n\t\t\t\tthat.locationCache.area = info.province+','+info.city+','+info.district\r\n\t\t\t\tthat.locationCache.address = that.curent_address\r\n\t\t\t\tthat.locationCache.latitude = that.latitude\r\n\t\t\t\tthat.locationCache.longitude = that.longitude\r\n\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\tthat.getdata()\r\n\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\trefreshNearbyPlace:function(latitude='',longitude=''){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tif(latitude=='' && longitude==''){\r\n\t\t\t\t\tlatitude = that.latitude\r\n\t\t\t\t\tlongitude = that.longitude\r\n\t\t\t\t}\r\n\t\t\t\tif(latitude && longitude){\r\n\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\tapp.post('ApiAddress/getAreaByLocation', {\r\n\t\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\t\ttype:1\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tvar data = resp.data\r\n\t\t\t\t\t\t\tthat.nearbyplacelist = data.pois\r\n\t\t\t\t\t\t\tapp.setLocationCache('poilist',that.nearbyplacelist,that.cacheExpireTime)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tplacekeywordInput:function(e){\r\n\t\t\t\tthis.placekeyword = e.detail.value\r\n\t\t\t},\r\n\t\t\tsearchPlace:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tif(that.placekeyword==''){\r\n\t\t\t\t\tthat.suggestionplacelist = []\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar locationCacheData = app.getLocationCache();\r\n\t\t\r\n\t\t\t\tvar region = '';\r\n\t\t\t\tif(locationCacheData && locationCacheData.area){\r\n\t\t\t\t\tvar areaArr = locationCacheData.area.split(',')\r\n\t\t\t\t\tif(areaArr.length==2){\r\n\t\t\t\t\t\tregion = areaArr[1]\r\n\t\t\t\t\t}else if(areaArr.length==3){\r\n\t\t\t\t\t\tregion = areaArr[2]\r\n\t\t\t\t\t}else if(areaArr.length==1){\r\n\t\t\t\t\t\tregion = areaArr[0]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loading = true\r\n\t\t\t\tapp.post('ApiAddress/suggestionPlace', {\r\n\t\t\t\t\tlatitude: locationCacheData.latitude,\r\n\t\t\t\t\tlongitude: locationCacheData.longitude,\r\n\t\t\t\t\tregion:region,\r\n\t\t\t\t\tkeyword:that.placekeyword\r\n\t\t\t\t}, function(resp) {\r\n\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\tthat.suggestionplacelist = resp.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//头部定位end\r\n\t\t\t\r\n\t\t\t//门店模式start\r\n\t\t\tshowMendianModal:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif(that.mendianlist.length>0){\r\n\t\t\t\t\tthat.isshowmendianmodal = true\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.post('ApiMendian/mendianlist', {\r\n\t\t\t\t\t\tlatitude: app.getLocationCache('latitude'),\r\n\t\t\t\t\t\tlongitude: app.getLocationCache('longitude'),\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tthat.mendianlist = resp.data\r\n\t\t\t\t\t\t\tthat.isshowmendianmodal = true\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.error(resp.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thideMendianModal:function(){\r\n\t\t\t\tthis.isshowmendianmodal = false\r\n\t\t\t},\r\n\t\t\tchangeMendian:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t// var mendianid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar mendianinfo = that.mendianlist[index]\r\n\t\t\t\tapp.setLocationCache('mendian_id',mendianinfo.id)\r\n\t\t\t\tapp.setLocationCache('mendian_name',mendianinfo.name)\r\n\t\t\t\tapp.setLocationCache('mendian_isinit',0)\r\n\t\t\t\tvar locationCache = app.getLocationCache()\r\n\t\t\t\tthat.locationCache = locationCache\r\n\t\t\t\tvar mendian = {};\r\n\t\t\t\tmendian.id= mendianinfo.id;\r\n\t\t\t\tmendian.name = mendianinfo.name\r\n\t\t\t\tmendian.area = mendianinfo.area\r\n\t\t\t\tmendian.address = mendianinfo.address\r\n\t\t\t\tmendian.distance = mendianinfo.distance\r\n\t\t\t\tlocationCache.mendian = mendian\r\n\t\t\t\tapp.setLocationCache('mendian',mendian,that.cacheExpireTime)\r\n\t\t\t\tthat.isshowmendianmodal = false\r\n\t\t\t\tthat.getdata()\r\n\t\t\t},\r\n\t\t\t//门店模式end\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-header-location{display: flex;align-items: center;}\r\n.dp-header-mendian-box{padding: 14rpx 20rpx;}\r\n.dp-header-mendian{display: flex;align-items: center;}\r\n.dp-header-mendian-address{font-size: 24rpx;color: #999;height: 30rpx;flex-wrap: nowrap;margin-top: 6rpx;line-height: 30rpx;align-self: flex-start;padding-left: 6rpx;}\r\n.dp-header-mendian-address .f1{max-width: 80%;text-overflow: ellipsis;white-space:nowrap;overflow: hidden;}\r\n.dp-header-mendian-address .f2{flex-shrink: 0;padding-left: 16rpx;}\r\n.dp-header-mendian .header-icon{width: 28rpx;height: 28rpx;}\r\n.dp-header-location-search {\r\n\twidth: 150px;\r\n\theight: 32px;\r\n\tbackground: #f2f2f2;\r\n\tborder-radius: 16px;\r\n\tcolor: #232323;\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tfont-size: 14px\r\n}\r\n.dp-location-search{flex:1;background: #FFFFFF;height: 70rpx;border-radius: 10rpx;padding: 0 20rpx;display: flex; align-items: center;margin-left: 6rpx;}\r\n.dp-header-location-search input{flex: 1;display: inline-block;font-size: 24rpx;}\r\n.dp-location-search-icon{width: 30rpx;height: 30rpx;}\r\n.dp-header-location-search image {\r\n\twidth: 14px;\r\n\theight: 15px;\r\n\tmargin-right: 6px\r\n}\r\n.dp-header-address-widthfixed{max-width: 150rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}\r\n.dp-header-location-title{width: 160px;text-align: center;font-weight: bold;}\r\n.dp-location-iconlist{display: flex;flex-shrink: 0;align-items: center;justify-content: flex-end;}\r\n.dp-location-icon{width: 36rpx; height: 36rpx;margin-left: 2px;}\r\n.dp-location-icon image{width:100%;height: 100%;}\r\n.dp-header-icon{width: 34rpx;height: 34rpx;}\r\n.dp-header-more{margin-left: 4px;}\r\n.dp-header-picker .uni-data-tree-dialog{color: #333333;}\r\n\r\n.dp-location-modal {\r\n\t\tposition: fixed;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\talign-items: flex-end;\r\n\t\tmargin: 0;\r\n\t\tbackground: #f6f6f6;\r\n\t\tz-index: 1900000;\r\n\t}\r\n\t.dp-location-modal-mendian{background: rgba(0, 0, 0, 0.5);}\r\n\r\n\t.dp-location-modal-content {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow-y: auto;\r\n\t}\r\n.dp-header-nearby-box{background-color: #f6f6f6;}\r\n.dp-header-nearby-search{padding:20rpx;display: flex;align-items: center;}\r\n.dp-header-nearby-close image{width: 36rpx; height: 36rpx;}\r\n.dp-header-nearby-input{background-color: #FFFFFF;border-radius: 50rpx;margin-left: 10rpx;height: 70rpx;line-height: 70rpx;flex: 1;display: flex;align-items: center;justify-content: space-between;padding-left: 40rpx;color: #222222;}\r\n.dp-header-nearby-input .input{flex:1}\r\n.dp-header-nearby-input .searchbtn{border: #f6f6f6 1rpx solid;height: 50rpx;line-height: 50rpx;width: 90rpx;margin: 6rpx 10rpx;border-radius: 50rpx;font-size: 24rpx;display: flex;align-items: center;justify-content: center;}\r\n.dp-header-nearby-content{background: #FFFFFF;padding: 20rpx;color: #333;}\r\n.dp-header-nearby-imgicon{width: 30rpx;height: 30rpx;margin-right: 10rpx;}\r\n.dp-header-nearby-title{font-weight: bold;}\r\n.dp-header-nearby-list{}\r\n.dp-header-nearby-info{padding: 20rpx 0 20rpx 40rpx;line-height: 40rpx;border-bottom: 1rpx solid #f6f6f6;}\r\n.dp-header-nearby-info:last-child{border: none;}\r\n.dp-header-nearby-txt{font-size: 24rpx;color: #888;}\r\n.dp-header-nearby-tip{color: #707070;font-weight: normal;font-size: 26rpx;}\r\n.dp-header-nearby-all{padding-top: 20rpx;color: #707070;font-size: 26rpx;padding-left: 40rpx;}\r\n.dp-header-nearby-all image{width: 28rpx; height: 28rpx;margin-left: 10rpx;}\r\n.dp-header-location-bottom{background: #FFFFFF;position: fixed;bottom: 0;width: 100%;height: 90rpx;border-top: 1rpx solid #f6f6f6;padding-bottom: 8rpx;line-height: 90rpx;}\r\n.dp-location-add-address{font-size: 42rpx;font-weight: bold;padding-right: 6rpx; line-height: 90rpx;}\r\n.dp-header-nearby-body{padding-bottom: 110rpx;}\r\n.dp-suggestion-box{background: #FFFFFF;padding: 20rpx;color: #333;}\r\n.dp-suggestion-place{padding: 20rpx;border-bottom: 1rpx solid #f6f6f6;z-index: 9999;}\r\n.dp-suggestion-place image{width: 40rpx;height: 40rpx;}\r\n.dp-suggestion-place .s-title{font-size: 30rpx;}\r\n.dp-suggestion-place .s-info{padding-top: 10rpx;font-size: 24rpx;padding-left: 40rpx;}\r\n.dp-suggestion-place .s-area{flex-shrink: 0;padding-right: 8rpx;}\r\n.dp-suggestion-place .s-address{color: #797979;}\r\n.dp-header-location-box{font-size: 28rpx;display: flex;align-items: center;justify-content: space-between;}\r\n\r\n\r\n/* 门店 */\r\n.popup_mendian .popup__content{padding: 0 20rpx;}\r\n.popup_mendian .popup__modal{min-height: auto;}\r\n.popup_mendian .mendian-info{display: flex;align-items: center;width: 100%;background:#F6F6F6;padding: 20rpx; margin-bottom: 20rpx;border-radius: 6rpx;}\r\n.popup_mendian .mendian-info .b1{background-color: #fbfbfb;}\r\n.popup_mendian .mendian-info .b1 image{height: 100rpx;width:100rpx;border-radius: 6rpx;border: 1px solid #e8e8e8;}\r\n.popup_mendian .mendian-info .b2{flex:1;line-height: 38rpx;margin-left: 20rpx;overflow: hidden;}\r\n.popup_mendian .mendian-info .b2 .t1{padding-bottom: 10rpx;}\r\n.popup_mendian .mendian-info .b2 .t2{font-size: 24rpx;color: #999;}\r\n.popup_mendian .mendian-info .b3{display: flex;justify-content: flex-end;flex-shrink: 0;padding-left: 20rpx;}\r\n.popup_mendian .mendian-info .b3 image{width: 40rpx;height: 40rpx;}\r\n.popup_mendian .mendian-info .tag{padding:0 10rpx;margin-right: 10rpx;display: inline-block;font-size: 22rpx;border-radius: 8rpx;flex-shrink: 0;}\r\n.popup_mendian .mendian-info .mendian-address{text-overflow: ellipsis;flex:1;width: 300rpx;white-space: nowrap;}\r\n.popup_mendian .mendian-info .line{border-right: 1rpx solid #999;width: 10rpx;flex-shrink: 0;height: 16rpx;padding-left:10rpx;margin-right: 12rpx;}\r\n.popup_mendian .mendian-info .mendian-distance{color: #3b3b3b;font-weight: 600;flex-shrink: 0;}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-location.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-location.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839376922\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}