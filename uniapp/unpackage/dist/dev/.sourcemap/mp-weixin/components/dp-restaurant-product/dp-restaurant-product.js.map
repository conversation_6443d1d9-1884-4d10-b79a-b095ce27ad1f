{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-restaurant-product/dp-restaurant-product.vue?84eb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-restaurant-product/dp-restaurant-product.vue?db3a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-restaurant-product/dp-restaurant-product.vue?317f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-restaurant-product/dp-restaurant-product.vue?acb2", "uni-app:///components/dp-restaurant-product/dp-restaurant-product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-restaurant-product/dp-restaurant-product.vue?8d59", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-restaurant-product/dp-restaurant-product.vue?73fa"], "names": ["props", "menuindex", "default", "params", "data"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AACyE;AACL;AACa;;;AAGjF;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2FAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAm1B,CAAgB,mzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCev2B;EACAA;IACAC;MAAAC;IAAA;IACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAgsC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACAptC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-restaurant-product/dp-restaurant-product.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-restaurant-product.vue?vue&type=template&id=74183974&\"\nvar renderjs\nimport script from \"./dp-restaurant-product.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-restaurant-product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-restaurant-product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-restaurant-product/dp-restaurant-product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-restaurant-product.vue?vue&type=template&id=74183974&\"", "var components\ntry {\n  components = {\n    dpRestaurantProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-restaurant-product-item/dp-restaurant-product-item\" */ \"@/components/dp-restaurant-product-item/dp-restaurant-product-item.vue\"\n      )\n    },\n    dpRestaurantProductItemlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-restaurant-product-itemlist/dp-restaurant-product-itemlist\" */ \"@/components/dp-restaurant-product-itemlist/dp-restaurant-product-itemlist.vue\"\n      )\n    },\n    dpRestaurantProductItemline: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-restaurant-product-itemline/dp-restaurant-product-itemline\" */ \"@/components/dp-restaurant-product-itemline/dp-restaurant-product-itemline.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-restaurant-product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-restaurant-product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-product\" :style=\"{\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',\r\n\tpadding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx'\r\n}\">\r\n\t<!--123排-->\r\n\t<dp-restaurant-product-item v-if=\"params.style=='1' || params.style=='2' || params.style=='3'\" :showstyle=\"params.style\" :data=\"data\" :saleimg=\"params.saleimg\" :showname=\"params.showname\" :showtype=\"params.showtype\" :showprice=\"params.showprice\" :showsales=\"params.showsales\" :showcart=\"params.showcart\" :cartimg=\"params.cartimg\" idfield=\"proid\" :menuindex=\"menuindex\"></dp-restaurant-product-item>\r\n\t<!--横排-->\r\n\t<dp-restaurant-product-itemlist v-if=\"params.style=='list'\" :data=\"data\" :saleimg=\"params.saleimg\" :showname=\"params.showname\" :showtype=\"params.showtype\" :showprice=\"params.showprice\" :showsales=\"params.showsales\" :cartimg=\"params.showcart\" idfield=\"proid\" :menuindex=\"menuindex\"></dp-restaurant-product-itemlist>\r\n\t<!--左右滑动-->\r\n\t<dp-restaurant-product-itemline v-if=\"params.style=='line'\" :data=\"data\" :saleimg=\"params.saleimg\" :showname=\"params.showname\" :showtype=\"params.showtype\" :showprice=\"params.showprice\" :showsales=\"params.showsales\" :cartimg=\"params.showcart\" idfield=\"proid\" :menuindex=\"menuindex\"></dp-restaurant-product-itemline>\r\n</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-product{width:100%;height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-restaurant-product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-restaurant-product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839376118\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}