{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/parse/components/wxParseVideo.vue?2763", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/parse/components/wxParseVideo.vue?8bcd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/parse/components/wxParseVideo.vue?a4c7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/parse/components/wxParseVideo.vue?ac17", "uni-app:///components/parse/components/wxParseVideo.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/parse/components/wxParseVideo.vue?37df", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/parse/components/wxParseVideo.vue?9df1"], "names": ["name", "props", "node", "data", "playState", "videoStyle", "methods", "play", "console", "mounted", "uni"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsB72B;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAF;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAA4sC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACAhuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/parse/components/wxParseVideo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wxParseVideo.vue?vue&type=template&id=3b4da81b&\"\nvar renderjs\nimport script from \"./wxParseVideo.vue?vue&type=script&lang=js&\"\nexport * from \"./wxParseVideo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wxParseVideo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/parse/components/wxParseVideo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=template&id=3b4da81b&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=script&lang=js&\"", "<template>\n\t<!-- 这个模板用来解决原生video总是浮在最上层的问题，使用view替换video，播放是再替换回，监听一个事件，用来被遮盖时做替换video -->\r\n  <!--增加video标签支持，并循环添加-->\r\n  <view @click=\"play\">\n\t  <view v-if=\"!playState\" :class=\"node.classStr\" :style=\"node.styleStr\" style=\"display: inline-block;margin: auto;max-width: 100%;\" class=\"video-video\">\n\t\t  <view style=\"display: flex;width: 100%;height:100%;flex-direction: row; justify-content: center;align-items: center;\">\n\t\t\t  <image src=\"https://gwbj.tongwenkeji.com/html/static/play.png\" style=\"width: 20%;\" mode=\"widthFix\"></image>\n\t\t  </view>\n\t  </view>\n\t  <video\n\t  :autoplay=\"false\" \n\t  :class=\"node.classStr\" \n\t  :style=\"node.styleStr\" \n\t  class=\"video-video\"\n\t  v-if=\"playState\"\n\t  :src=\"node.attr.src\"></video>\n\t  \r\n  </view>\n  \r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'wxParseVideo',\r\n  props: {\r\n    node: {},\r\n  },\n  data(){\n\t  return{\n\t\t  playState:true,\n\t\t  videoStyle:'width: 100%;'\n\t  }\n  },\n  methods:{\n\t  play(){\n\t\t  console.log('点击了video 播放')\n\t\t  //显示播放器并播放播放器\n\t\t  this.playState = !this.playState\n\t  }\n  },\n  mounted() {\n\t  //捕获侧滑菜单的遮盖行为，隐藏video\n  \tuni.$on('slideMenuShow',e=>{\n\t\tconsole.log('捕获事件：'+e)\n\t\tif(e == 'show' && this.playState){\n\t\t\t//正在播放则停止\n\t\t\tthis.playState = false\n\t\t}\n\t})\n  }\r\n};\r\n</script>\n<style>\n\t.video-video{background: #000000;}\n</style>\r\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839392489\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}