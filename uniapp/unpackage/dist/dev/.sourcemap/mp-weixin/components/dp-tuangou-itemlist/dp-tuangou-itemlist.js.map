{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue?bc1f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue?eb91", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue?d678", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue?43ef", "uni-app:///components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue?8a8a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue?ca53"], "names": ["data", "buydialogShow", "proid", "props", "menuindex", "default", "saleimg", "showname", "namecolor", "showprice", "showsales", "showcart", "cartimg", "idfield", "methods", "buydialogChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AACuE;AACL;AACa;;;AAG/E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAAi1B,CAAgB,izBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkCr2B;EACAA;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;MAAAJ;IAAA;IACAK;MAAAL;IAAA;IACAM;MAAAN;IAAA;IACAO;MAAAP;IAAA;IACAL;IACAa;MAAAR;IAAA;EACA;EACAS;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA8rC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACAltC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-tuangou-itemlist/dp-tuangou-itemlist.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-tuangou-itemlist.vue?vue&type=template&id=29ac6786&\"\nvar renderjs\nimport script from \"./dp-tuangou-itemlist.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-tuangou-itemlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-tuangou-itemlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-tuangou-itemlist/dp-tuangou-itemlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tuangou-itemlist.vue?vue&type=template&id=29ac6786&\"", "var components\ntry {\n  components = {\n    buydialogTuangou: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-tuangou/buydialog-tuangou\" */ \"@/components/buydialog-tuangou/buydialog-tuangou.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.showprice != \"0\" ? _vm.t(\"color1\") : null\n  var m1 = _vm.showcart == 1 ? _vm.t(\"color1rgb\") : null\n  var m2 = _vm.showcart == 1 ? _vm.t(\"color1\") : null\n  var m3 = _vm.showcart == 2 ? _vm.t(\"color1rgb\") : null\n  var m4 = _vm.showcart == 2 ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tuangou-itemlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tuangou-itemlist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view style=\"width:100%\">\r\n\t<view class=\"dp-tuangou-itemlist\">\r\n\t\t<view class=\"item\" v-for=\"(item,index) in data\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/activity/tuangou/product?id='+item[idfield]\">\r\n\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t<image class=\"saleimg\" :src=\"saleimg\" v-if=\"saleimg!=''\" mode=\"widthFix\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product-info\">\r\n\t\t\t\t<view class=\"p1\" v-if=\"showname == 1\">{{item.name}}</view>\r\n\t\t\t\t<view class=\"p2\" v-if=\"showprice != '0'\">\r\n\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t<block v-if=\"item.usd_minprice\">\r\n\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">低至$</text>{{item.usd_minprice}}\r\n\t\t\t\t\t\t\t<text style=\"font-size:24rpx;padding-right:3px\">￥{{item.min_price}}</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<text style=\"font-size:24rpx;padding-right:1px\">低至￥</text>{{item.min_price}}\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"showprice == '1' && item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t<view class=\"p3-1\" v-if=\"showsales=='1' && item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"showcart==1\" @click.stop=\"buydialogChange\" :data-proid=\"item[idfield]\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t<view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"showcart==2\" @click.stop=\"buydialogChange\" :data-proid=\"item[idfield]\"><image :src=\"cartimg\" class=\"img\"/></text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<buydialog-tuangou v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" controller=\"ApiTuangou\" :menuindex=\"menuindex\"></buydialog-tuangou>\r\n</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tbuydialogShow:false,\r\n\t\t\t\tproid:0,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tsaleimg:{default:''},\r\n\t\t\tshowname:{default:1},\r\n\t\t\tnamecolor:{default:'#333'},\r\n\t\t\tshowprice:{default:'1'},\r\n\t\t\tshowsales:{default:'1'},\r\n\t\t\tshowcart:{default:'1'},\r\n\t\t\tcartimg:{default:'/static/imgsrc/cart.svg'},\r\n\t\t\tdata:{},\r\n\t\t\tidfield:{default:'id'}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tbuydialogChange: function (e) {\r\n\t\t\t\tif(!this.buydialogShow){\r\n\t\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t\t}\r\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-tuangou-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.dp-tuangou-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:20rpx;border-radius:10rpx}\r\n.dp-tuangou-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.dp-tuangou-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.dp-tuangou-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\r\n.dp-tuangou-itemlist .product-info {width: 70%;padding:6rpx 10rpx 5rpx 20rpx;position: relative;}\r\n.dp-tuangou-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.dp-tuangou-itemlist .product-info .p2{margin-top:20rpx;height:56rpx;line-height:56rpx;overflow:hidden;}\r\n.dp-tuangou-itemlist .product-info .p2 .t1{font-size:36rpx;}\r\n.dp-tuangou-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.dp-tuangou-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\r\n.dp-tuangou-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}\r\n.dp-tuangou-itemlist .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\r\n.dp-tuangou-itemlist .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}\r\n.dp-tuangou-itemlist .product-info .p4 .img{width:100%;height:100%}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tuangou-itemlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-tuangou-itemlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839370165\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}