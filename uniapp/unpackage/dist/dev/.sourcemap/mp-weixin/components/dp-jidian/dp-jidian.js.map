{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-jidian/dp-jidian.vue?e56a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-jidian/dp-jidian.vue?5a6f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-jidian/dp-jidian.vue?f6fb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-jidian/dp-jidian.vue?9236", "uni-app:///components/dp-jidian/dp-jidian.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-jidian/dp-jidian.vue?7382", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-jidian/dp-jidian.vue?2a7e"], "names": ["name", "data", "pre_url", "props", "params", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqB31B;AAAA,eACA;EACAA;EACAC;IACA;MACAC;IAEA;EACA;EACAC;IACAC;IACAH;EACA;EACAI,UACA;AACA;AAAA,2B;;;;;;;;;;;;ACpCA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-jidian/dp-jidian.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-jidian.vue?vue&type=template&id=9e1d182c&\"\nvar renderjs\nimport script from \"./dp-jidian.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-jidian.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-jidian.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-jidian/dp-jidian.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-jidian.vue?vue&type=template&id=9e1d182c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-jidian.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-jidian.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"dp-jidian\" :style=\"{backgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',\r\n\tborderRadius:params.borderradius+'px'}\">\r\n\t\t<view class=\"f1 flex-y-center\" :style=\"{backgroundImage:'url('+pre_url+'/static/img/dzp/jiangpin.png)'}\"></view>\r\n\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/activity/yx/jidian?bid='+params.bid\">\r\n\t\t\t<view class=\"text-big\">{{data.name}}</view>\r\n\t\t\t<view>再下<text>{{data.reward_num-data.have_num}}单</text>得<text>{{data.reward_name}}</text>优惠券</view>\r\n\t\t</view>\r\n\t\t<view class=\"f3\">\r\n\t\t\t<block v-for=\"(item,index) in data.have_num\">\r\n\t\t\t\t<view class=\"circle circleCheck\"><view class=\"gou\"></view></view>\r\n\t\t\t</block>\r\n\t\t\t<block v-for=\"(item,index) in data.total_num-data.have_num\">\r\n\t\t\t\t<view class=\"circle\"></view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\n</template>\n\n<script>\r\n\tvar app =getApp();\n\texport default {\n\t\tname:\"dp-jidian\",\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tpre_url:getApp().globalData.pre_url\r\n\t\t\t\t\n\t\t\t};\n\t\t},\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{},\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t}\n\t}\n</script>\n\n<style>\n.dp-jidian{padding:10rpx 0;height: auto; position: relative; display: flex;align-items: center;}\n.dp-jidian .f1{height:48px;width:36px;color: #666;border: 0px;padding: 0px;margin: 0px;background-position: center;background-repeat: no-repeat;background-size:20px;}\r\n.dp-jidian .f2{height:48px;flex:1; color: #666;}\n.dp-jidian .f3{width:35%; text-align: right; padding-right: 10rpx;}\n.dp-jidian .text-big {font-size: 16px; color: #333;}\n.dp-jidian .circle { border: 1px solid #D4D5D0; border-radius: 50%; width: 15px;height: 15px;margin: 0 4rpx; display: inline-block; background: #fff;}\n.dp-jidian .circleCheck { border:none; border-radius: 50%; background: #F9806D; position: relative;overflow: hidden;}\n.dp-jidian .circleCheck .gou {\n    width: 12px;\n    height: 6px;\n    display: inline-block;\n    border: 1px solid #ffffff;\n    border-width: 0 0 2px 2px;\n    transform: rotate(-45deg);\n    -ms-transform: rotate(-45deg);\n    -moz-transform: rotate(-45deg);\n    -webkit-transform: rotate(-45deg);\n    -o-transform: rotate(-45deg);\n    vertical-align: baseline;\n    position: absolute;\n    right: 0;\n    top: 2px;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-jidian.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-jidian.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839375018\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}