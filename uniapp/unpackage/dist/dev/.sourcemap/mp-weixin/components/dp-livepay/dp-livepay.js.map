{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-livepay/dp-livepay.vue?e512", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-livepay/dp-livepay.vue?0010", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-livepay/dp-livepay.vue?0348", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-livepay/dp-livepay.vue?2233", "uni-app:///components/dp-livepay/dp-livepay.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-livepay/dp-livepay.vue?8a68", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-livepay/dp-livepay.vue?18aa"], "names": ["data", "loading", "arealist", "area", "showlevel", "locationCache", "latitude", "longitude", "address", "poilist", "loc_area_type", "loc_range_type", "loc_range", "mendian_id", "mendian_name", "optionsData", "cacheExpireTime", "props", "params", "mounted", "that", "methods", "gotoChange", "app", "ApiLivepay", "city_name", "res", "item", "checkMode", "checkLocation", "areachange", "area_name", "showarea", "initCityAreaList", "uni", "url", "method", "header", "success", "item2", "newchildren", "item1", "newlist"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8B51B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAJ;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAV;MACA;MACAE;MACAC;MACAQ;MACAC;IACA;EACA;;EACAC;IACAC;IACAlB;EACA;EACAmB;IACA;IACAC;IACAA;EACA;EACAC;IACAC;MACA;QACAC;MACA;QACAA;MACA;IACA;IACAC;MACA;MACAJ;MACAG;QAAAE;MAAA;QACAL;QACAA;QACAA;UACAM;YACA;cACAC;YACA;UACA;QACA;QACAP;MACA;IACA;IACAQ;MACA;MACAR;MAWA;QACAA;MACA;QACAA;MACA;IAEA;IACAS;MACA;MACA;MACA;;MAOA;MACA;MACA;MACAN;QACA;QACAH;QACAA;QACA;QACAG;UAAAjB;UAAAC;QAAA;UACA;UACA;YACAa;YACAA;YACAA;YACAA;YACAA;YACA;YACA;cACA;gBACAA;gBACAA;gBACA;kBACAA;gBACA;gBACAA;cACA;gBACAA;gBACAA;cACA;cACAA;cACAA;cACAG;YACA;cACAH;cACAA;cACAA;cACAA;cACAG;YACA;cACA;YACA;UACA;QACA;MACA;QACAH;QACAA;MACA;IACA;IACAU;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA;MACAZ;MACAA;MACA;MACAA;MACAA;MACA;MACAG;QACAf;MACA;QACAY;QACA;UACAA;UACAA;UACAA;UACAA;UACAG;QACA;UACAA;QACA;MACA;MACAH;IACA;IACAa;MACA;MACA;MACA;QACAC;UACAC;UACAnC;UACAoC;UACAC;YAAA;UAAA;UACAC;YACA;cACA;cACA;cACA;gBACA;gBACA;kBACA;kBACA;kBACA;oBACA;oBACAC;oBACAC;kBACA;kBACAC;gBACA;kBACAA;gBACA;;gBACAC;cACA;cACAtB;YACA;cACAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7OA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-livepay/dp-livepay.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-livepay.vue?vue&type=template&id=674aae38&\"\nvar renderjs\nimport script from \"./dp-livepay.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-livepay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-livepay.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-livepay/dp-livepay.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-livepay.vue?vue&type=template&id=674aae38&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-livepay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-livepay.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-menu\" :style=\"{\r\n\tfontSize:params.fontsize*2+'rpx',\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',\r\n\tpadding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx',\r\n\tborderRadius:params.boxradius*2.2+'rpx'\r\n}\">\r\n\t<view style=\"padding-top:16rpx;\">\r\n\t\t<view class=\"menu-title flex-bt\">\r\n\t\t\t<view :style=\"{color:params.titlecolor,fontSize:params.titlesize*2+'rpx'}\">{{params.title}}</view>\r\n\t\t\t<view class='positioning-class'>\r\n\t\t\t\t<uni-data-picker class=\"dp-header-picker\" :localdata=\"arealist\" popup-title=\"地区\" @change=\"areachange\" :placeholder=\"'地区'\">\r\n\t\t\t\t\t<view>{{locationCache.address?locationCache.address:'北京市'}}</view>\r\n\t\t\t\t</uni-data-picker>\r\n\t\t\t\t<img style='width: 22rpx;height: 22rpx;margin-left: 6rpx;' src='https://v2d.diandashop.com/static/admin/img/down-icon.png' />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"swiper-item\">\r\n\t\t\t<view v-for=\"(item,index) in optionsData\" :key=\"index\" class=\"menu-nav6\" @click=\"gotoChange(item)\" :style=\"{'backgroundColor':'#fafafa','border-radius':params.radius + 'px'}\">\r\n\t\t\t\t<image :src=\"item.imgurl\" :style=\"{width:params.iconsize*2+'rpx',height:params.iconsize*2+'rpx'}\" :class=\"(item.is_open && item.is_open == 1) ? '':'grayscale'\"></image>\r\n\t\t\t\t<view class=\"menu-text\" :style=\"{color:item.color,height:params.fontheight*2+'rpx',lineHeight:params.fontheight*2+'rpx'}\">{{item.text|| '按钮文字'}}</view>\r\n\t\t\t\t<view class=\"no-opened\" v-if=\"!item.is_open\" >暂未开通</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app =getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tloading:false,\r\n\t\t\t\tarealist:[],\r\n\t\t\t\tarea:'',\r\n\t\t\t\tshowlevel:2,\r\n\t\t\t\tlocationCache:{\r\n\t\t\t\t\tlatitude:'',\r\n\t\t\t\t\tlongitude:'',\r\n\t\t\t\t\tarea:'',\r\n\t\t\t\t\taddress:'',\r\n\t\t\t\t\tpoilist:[],\r\n\t\t\t\t\tloc_area_type:-1,\r\n\t\t\t\t\tloc_range_type:-1,\r\n\t\t\t\t\tloc_range:'',\r\n\t\t\t\t\tmendian_id:0,\r\n\t\t\t\t\tmendian_name:'',\r\n\t\t\t\t\tshowlevel:2\r\n\t\t\t\t},\r\n\t\t\t\tlatitude:'',\r\n\t\t\t\tlongitude:'',\r\n\t\t\t\toptionsData:[],\r\n\t\t\t\tcacheExpireTime:10,//缓存过期时间10分钟\r\n\t\t\t}\r\n    },\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t},\r\n\t\tmounted(){\r\n\t\t\tlet that = this;\r\n\t\t\tthat.locationCache  = app.getLocationCache();\r\n\t\t\tthat.checkMode();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgotoChange(item){\r\n\t\t\t\tif(item.is_open && item.is_open == 1){\r\n\t\t\t\t\tapp.goto(item.hrefurl.split('?')[0]+'?item='+encodeURIComponent(JSON.stringify(item))+'&address='+this.locationCache.address);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.error(\"暂未开通此功能！\")\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tApiLivepay(address){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiLivepay/itemlist',{city_name:(address || '北京市')},function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.optionsData = [];\r\n\t\t\t\t\tthat.data.forEach(item => {\r\n\t\t\t\t\t\tres.data.forEach(items => {\r\n\t\t\t\t\t\t\tif(items.type == item.type){\r\n\t\t\t\t\t\t\t\titem.is_open = items.is_open\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.optionsData = that.data;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcheckMode:function(){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tthat.initCityAreaList();\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// console.log(that.locationCache,'checkMode')\r\n\t\t\t\tthat.checkLocation();\r\n\t\t\t\tif(that.locationCache.mendian_id == 0 && that.locationCache.mendian_name == '' && that.locationCache.address == ''){\r\n\t\t\t\t\tthat.checkLocation();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.ApiLivepay(that.locationCache.address)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tif(that.locationCache.mendian_id == 0 && that.locationCache.mendian_name == ''){\r\n\t\t\t\t\tthat.checkLocation();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.ApiLivepay(that.locationCache.address)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tcheckLocation:function(){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\t// console.log(locationCache,'app.getLocationCache()')\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(locationCache.address){\r\n\t\t\t\t\tthat.locationCache.address = locationCache.address;\r\n\t\t\t\t\tthat.ApiLivepay(that.locationCache.address);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t\tvar loc_area_type = 0;\r\n\t\t\t\t\tvar loc_range_type = 0;\r\n\t\t\t\t\tvar loc_range = 10;\r\n\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t// console.log(res,'uni.getLocation')\r\n\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t//如果从当前地址切到当前城市，则重新定位用户位置\r\n\t\t\t\t\t\tapp.post('ApiAddress/getAreaByLocation', {latitude:that.latitude,longitude:that.longitude}, function(res) {\r\n\t\t\t\t\t\t\t// console.log(res,'getAreaByLocation')\r\n\t\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\t\tthat.locationCache.loc_area_type = loc_area_type\r\n\t\t\t\t\t\t\t\tthat.locationCache.loc_range_type = loc_range_type\r\n\t\t\t\t\t\t\t\tthat.locationCache.loc_range = loc_range\r\n\t\t\t\t\t\t\t\tthat.locationCache.latitude = that.latitude\r\n\t\t\t\t\t\t\t\tthat.locationCache.longitude = that.longitude\r\n\t\t\t\t\t\t\t\t// that.locationCache.showlevel = that.showlevel\r\n\t\t\t\t\t\t\t\tif(loc_area_type==0){\r\n\t\t\t\t\t\t\t\t\tif(that.showlevel==2){\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.address = res.city\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.area = res.province+','+res.city\r\n\t\t\t\t\t\t\t\t\t\tif(that.locationCache.address == null){\r\n\t\t\t\t\t\t\t\t\t\t\tthat.locationCache.address = '北京市';\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tthat.ApiLivepay(that.locationCache.address)\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.address = res.district\r\n\t\t\t\t\t\t\t\t\t\tthat.locationCache.area = res.province+','+res.city+','+res.district\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tthat.area = that.locationCache.area\r\n\t\t\t\t\t\t\t\t\tthat.curent_address = that.locationCache.address\r\n\t\t\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t\t}else if(loc_area_type==1){\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.address = res.landmark\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.area = res.province+','+res.city+','+res.district\r\n\t\t\t\t\t\t\t\t\tthat.area = that.locationCache.area\r\n\t\t\t\t\t\t\t\t\tthat.curent_address = that.locationCache.address\r\n\t\t\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},function(res){\r\n\t\t\t\t\t\tthat.locationCache.address = '北京市';\r\n\t\t\t\t\t\tthat.ApiLivepay(that.locationCache.address);\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tareachange:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tconst value = e.detail.value\r\n\t\t\t\tvar area_name = [];\r\n\t\t\t\tvar showarea = ''\r\n\t\t\t\tfor(var i=0;i<that.showlevel;i++){\r\n\t\t\t\t\tarea_name.push(value[i].text)\r\n\t\t\t\t\tshowarea = value[i].text\r\n\t\t\t\t}\r\n\t\t\t\tthat.area = area_name.join(',')\r\n\t\t\t\tthat.curent_address = showarea\r\n\t\t\t\t//全局缓存\r\n\t\t\t\tthat.locationCache.area = area_name.join(',')\r\n\t\t\t\tthat.locationCache.address = showarea\r\n\t\t\t\t\t//获取地址中心地标\r\n\t\t\t\t\tapp.post('ApiAddress/addressToZuobiao', {\r\n\t\t\t\t\t\taddress:area_name.join('')\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tthat.latitude = resp.latitude\r\n\t\t\t\t\t\t\tthat.longitude = resp.longitude\r\n\t\t\t\t\t\t\tthat.locationCache.latitude = resp.latitude;\r\n\t\t\t\t\t\t\tthat.locationCache.longitude = resp.longitude;\r\n\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.error('地址解析错误');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\tthat.ApiLivepay(that.locationCache.address)\r\n\t\t\t},\r\n\t\t\tinitCityAreaList:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t//地区加载\r\n\t\t\t\tif(that.arealist.length==0){\r\n\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\r\n\t\t\t\t\t\tdata: {},\r\n\t\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\t\tif(that.showlevel<3){\r\n\t\t\t\t\t\t\t\tvar newlist = [];\r\n\t\t\t\t\t\t\t\tvar arealist = res2.data\r\n\t\t\t\t\t\t\t\tfor(var i in arealist){\r\n\t\t\t\t\t\t\t\t\tvar item1 = arealist[i]\r\n\t\t\t\t\t\t\t\t\tif(that.showlevel==2){\r\n\t\t\t\t\t\t\t\t\t\tvar children = item1.children //市\r\n\t\t\t\t\t\t\t\t\t\tvar newchildren = [];\r\n\t\t\t\t\t\t\t\t\t\tfor(var j in children){\r\n\t\t\t\t\t\t\t\t\t\t\tvar item2 = children[j]\r\n\t\t\t\t\t\t\t\t\t\t\titem2.children = []; //去掉三级-县的数据\r\n\t\t\t\t\t\t\t\t\t\t\tnewchildren.push(item2)\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\titem1.children = newchildren\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\titem1.children = []; ////去掉二级-市的数据\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tnewlist.push(item1)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.arealist = newlist\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tthat.arealist = res2.data\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-header-picker .uni-data-tree-dialog{color: #333333;}\r\n.dp-menu {height:auto;position:relative;padding-left:20rpx; padding-right:20rpx; background: #fff;}\r\n.dp-menu .menu-title{width:100%;font-size:30rpx;color:#333333;font-weight:bold;padding:0 24rpx 32rpx 24rpx}\r\n.dp-menu .swiper-item{display:flex;flex-wrap:wrap;flex-direction: row;height:auto;overflow: hidden;align-items: flex-start;}\r\n.dp-menu .menu-nav6 {width:31%;text-align:center;padding:55rpx 0px 43rpx;margin: 1.1%;position: relative;}\r\n.positioning-class{font-weight:normal;color: #666;font-size: 28rpx;display:flex;align-items:center;}\r\n.grayscale{filter: grayscale(100%);}\r\n.no-opened{color: #c5c5c5;font-size: 20rpx;width: 100%;position: absolute;bottom: 25rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-livepay.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-livepay.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839373141\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}