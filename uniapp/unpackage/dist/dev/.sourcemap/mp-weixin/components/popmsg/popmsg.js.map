{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/popmsg/popmsg.vue?c392", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/popmsg/popmsg.vue?f373", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/popmsg/popmsg.vue?ba55", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/popmsg/popmsg.vue?7937", "uni-app:///components/popmsg/popmsg.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/popmsg/popmsg.vue?878c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/popmsg/popmsg.vue?b004"], "names": ["props", "data", "popmsg", "logo", "title", "desc", "tourl", "duration", "pre_url", "methods", "open", "console", "innerAudioContext", "clearTimeout", "close", "got<PERSON>l"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCWx1B;EACAA;IACA;IACA;IACA;EAAA,CACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACAC;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;QACAD;MACA;MAGA;MACAE;MACA;QACA;MACA;IACA;IACAC;MACA;MACAD;IACA;IACAE;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/popmsg/popmsg.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./popmsg.vue?vue&type=template&id=39ec2e44&\"\nvar renderjs\nimport script from \"./popmsg.vue?vue&type=script&lang=js&\"\nexport * from \"./popmsg.vue?vue&type=script&lang=js&\"\nimport style0 from \"./popmsg.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/popmsg/popmsg.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./popmsg.vue?vue&type=template&id=39ec2e44&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./popmsg.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./popmsg.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"popmsg\" :class=\"popmsg==1?'popmsg-show':(popmsg==2?'popmsg-hide':'')\" @tap=\"gotourl\" :data-url=\"tourl\">\r\n\t\t<image class=\"popmsg-pic flex0\" :src=\"logo\" v-if=\"logo\"/>\r\n\t\t<view class=\"popmsg-content\">\r\n\t\t\t<view class=\"popmsg-content-title\">{{title}}</view>\r\n\t\t\t<view class=\"popmsg-content-desc\" v-if=\"desc\">{{desc}}</view>\r\n\t\t</view>\r\n\t\t<view class=\"popmsg-close\" @tap.stop=\"close\"><image :src=\"pre_url+'/static/img/close2.png'\" class=\"popmsg-close-img\"/></view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\t//popmsg:{default:false},\r\n\t\t\t//popdata:{default:{}},\r\n\t\t\t//duration: {type: Number,default: 3000}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpopmsg:0,\r\n\t\t\t\tlogo:'',\r\n\t\t\t\ttitle:'',\r\n\t\t\t\tdesc:'',\r\n\t\t\t\ttourl:'',\r\n\t\t\t\tduration:0,\r\n\t\t\t\tpre_url:getApp().globalData.pre_url\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\topen(data) {\r\n\t\t\t\tconsole.log(data);\r\n\t\t\t\tif(data.type=='tokefu'){\r\n\t\t\t\t\tthis.logo = data.data.headimg;\r\n\t\t\t\t\tthis.title = data.data.nickname+' 发来消息';\r\n\t\t\t\t\tthis.desc = data.data.content;\r\n\t\t\t\t\tthis.tourl = '/admin/kefu/message?mid='+data.data.mid;\r\n\t\t\t\t}else if(data.type=='tokehu'){\r\n\t\t\t\t\tthis.logo = data.data.uheadimg;\r\n\t\t\t\t\tthis.title = data.data.unickname+' 发来消息';\r\n\t\t\t\t\tthis.desc = data.data.content;\r\n\t\t\t\t\tthis.tourl = '/pages/kefu/index?bid='+data.data.bid;\r\n\t\t\t\t}else if(data.type=='peisong'){\r\n\t\t\t\t\tthis.logo = '';\r\n\t\t\t\t\tthis.title = data.data.title;\r\n\t\t\t\t\tthis.desc = data.data.desc;\r\n\t\t\t\t\tif(data.data.type==1){\r\n\t\t\t\t\t\tthis.tourl = '/activity/peisong/orderlist';\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.tourl = '/activity/peisong/dating';\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.logo = '';\r\n\t\t\t\t\tthis.title = data.data.title;\r\n\t\t\t\t\tthis.desc = data.data.desc;\r\n\t\t\t\t\tthis.tourl = data.data.url;\r\n\t\t\t\t}\r\n\t\t\t\tthis.popmsg = 1;\r\n\t\t\t\tconst innerAudioContext = uni.createInnerAudioContext();\r\n\t\t\t\tinnerAudioContext.autoplay = true;\r\n\t\t\t\tinnerAudioContext.src = this.pre_url + '/static/chat/default.mp3';\r\n\t\t\t\tinnerAudioContext.onPlay(() => {\r\n\t\t\t\t\tconsole.log('开始播放');\r\n\t\t\t\t});\r\n\r\n\r\n\t\t\t\tif (this.duration === 0) return\r\n\t\t\t\tclearTimeout(this.popuptimer)\r\n\t\t\t\tthis.popuptimer = setTimeout(() => {\r\n\t\t\t\t\tthis.close();\r\n\t\t\t\t}, this.duration);\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.popmsg = 2;\r\n\t\t\t\tclearTimeout(this.popuptimer)\r\n\t\t\t},\r\n\t\t\tgotourl(e){\r\n\t\t\t\tthis.close();\r\n\t\t\t\tthis.goto(e);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.popmsg{width: 100%;height:100rpx;background:rgba(0,0,0,0.7);position: fixed;z-index:999;color:#fff;font-size:28rpx;top:-100rpx;text-align: left;padding:15rpx 20rpx;overflow:hidden;display:none}\r\n.popmsg-show{display:flex;top: 0rpx;animation: popmsg-show 0.2s;animation-timing-function:ease;}\r\n@keyframes popmsg-show{from {top:-100rpx;}to {top:0rpx;}}\r\n.popmsg-hide{top: -100rpx;animation: popmsg-hide 0.2s;animation-timing-function:ease;}\r\n@keyframes popmsg-hide{from {top:0rpx;}to {top:-100rpx;}}\r\n\r\n.popmsg-pic{width:70rpx;height:70rpx;border-radius:8rpx;margin-right:10rpx;}\r\n.popmsg-content{flex:1;display:flex;flex-direction:column;justify-content:center;overflow:hidden}\r\n.popmsg-content-title{height:40rpx;line-height:40rpx;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.popmsg-content-desc{color:#eee;height:30rpx;line-height:30rpx;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.popmsg-close{margin-top:6rpx;width:50rpx;height:50rpx;border-radius:50%;border:1px solid #aaa;display:flex;align-items:center;justify-content:center;opacity:0.7}\r\n.popmsg-close-img{width:22rpx;height:22rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./popmsg.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./popmsg.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839368355\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}