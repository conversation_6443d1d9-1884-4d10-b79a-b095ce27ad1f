{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-business/dp-business.vue?87ec", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-business/dp-business.vue?c7a9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-business/dp-business.vue?c0b5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-business/dp-business.vue?8fa4", "uni-app:///components/dp-business/dp-business.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-business/dp-business.vue?a769", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-business/dp-business.vue?73d3"], "names": ["data", "pre_url", "buydialogShow", "proid", "props", "menuindex", "default", "params", "methods", "buydialogChange", "console", "addcart", "got<PERSON>l", "tourl", "app", "callphone", "uni", "phoneNumber", "fail"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpRA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiM71B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;IACAP;EACA;EACAQ;IACAC;MACA;QACA;MACA;MACA;MACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;QACA;UACAA;QACA;QACAH;QACA;UACAG;QACA;QACA;QACA;QACA;UACAA;QACA;MACA;QACAA;MACA;MACAC;IACA;IACAC;MACA;MACAC;QACAC;QACAC,uBACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7PA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-business/dp-business.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-business.vue?vue&type=template&id=3920bfc8&\"\nvar renderjs\nimport script from \"./dp-business.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-business.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-business.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-business/dp-business.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-business.vue?vue&type=template&id=3920bfc8&\"", "var components\ntry {\n  components = {\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showdistance\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showmaidan == \"1\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m2 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showmaidan == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showmaidan == \"1\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m4 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showmaidan == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showmaidan == \"1\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m6 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showmaidan == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m19 =\n    (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n    _vm.params.showinstore == \"1\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l3 = _vm.__map(_vm.data, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m7 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      item.rate_back &&\n      item.rate_back > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m8 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      item.rate_back &&\n      item.rate_back > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m9 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      item.rate_back &&\n      item.rate_back > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m10 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      item.scoredkmaxpercent &&\n      item.scoredkmaxpercent > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m11 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      item.scoredkmaxpercent &&\n      item.scoredkmaxpercent > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m12 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      item.scoredkmaxpercent &&\n      item.scoredkmaxpercent > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m13 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      _vm.params.showqueuefreeratio == \"1\" &&\n      item.queue_free_set == 1 &&\n      item.queue_free_rate_back > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m14 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      _vm.params.showqueuefreeratio == \"1\" &&\n      item.queue_free_set == 1 &&\n      item.queue_free_rate_back > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m15 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      _vm.params.showqueuefreeratio == \"1\" &&\n      item.queue_free_set == 1 &&\n      item.queue_free_rate_back > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m16 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      _vm.params.showdedamount == \"1\" &&\n      item.dedamount_maxdkpercent &&\n      item.dedamount_maxdkpercent > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m17 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      _vm.params.showdedamount == \"1\" &&\n      item.dedamount_maxdkpercent &&\n      item.dedamount_maxdkpercent > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m18 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      _vm.params.showdedamount == \"1\" &&\n      item.dedamount_maxdkpercent &&\n      item.dedamount_maxdkpercent > 0\n        ? _vm.t(\"color1\")\n        : null\n    var l0 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") &&\n      _vm.params.showproduct == 1\n        ? _vm.__map(item.prolist, function (item2, index2) {\n            var $orig = _vm.__get_orig(item2)\n            var m20 = _vm.t(\"color1\")\n            var g0 = item2.priceshows && item2.priceshows.length > 0\n            var m21 = item2.module == \"yuyue\" ? _vm.t(\"color1rgb\") : null\n            var m22 = item2.module == \"yuyue\" ? _vm.t(\"color1\") : null\n            var m23 =\n              item2.module != \"yuyue\" && !item2.price_type\n                ? _vm.t(\"color1rgb\")\n                : null\n            var m24 =\n              item2.module != \"yuyue\" && !item2.price_type\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m20: m20,\n              g0: g0,\n              m21: m21,\n              m22: m22,\n              m23: m23,\n              m24: m24,\n            }\n          })\n        : null\n    var g1 =\n      !_vm.params.showstyle || _vm.params.showstyle == \"1\"\n        ? _vm.params.showbusinessmiandan == 1 &&\n          item.miandanset_status == 1 &&\n          item.miandanprolist.length > 0\n        : null\n    var g2 =\n      !_vm.params.showstyle || _vm.params.showstyle == \"1\"\n        ? _vm.params.showscoreshop == 1 && item.scoreshopprolist.length > 0\n        : null\n    var l1 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") && g2\n        ? _vm.__map(item.scoreshopprolist, function (item2, index2) {\n            var $orig = _vm.__get_orig(item2)\n            var m25 = _vm.t(\"color1\")\n            var m26 = _vm.t(\"积分\")\n            return {\n              $orig: $orig,\n              m25: m25,\n              m26: m26,\n            }\n          })\n        : null\n    var g3 =\n      !_vm.params.showstyle || _vm.params.showstyle == \"1\"\n        ? (_vm.params.showseckill == 1 ||\n            _vm.params.showtuangou == 1 ||\n            _vm.params.showkanjia == 1 ||\n            _vm.params.showcollage == 1 ||\n            _vm.params.showluckycollage == 1) &&\n          item.cuxiaoprolist.length > 0\n        : null\n    var m27 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") && g3\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m28 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") && g3\n        ? _vm.t(\"color1rgb\")\n        : null\n    var l2 =\n      (!_vm.params.showstyle || _vm.params.showstyle == \"1\") && g3\n        ? _vm.__map(item.cuxiaoprolist, function (item2, index2) {\n            var $orig = _vm.__get_orig(item2)\n            var m29 = _vm.t(\"color1\")\n            var m30 = _vm.t(\"color1rgb\")\n            var m31 = _vm.t(\"color1\")\n            return {\n              $orig: $orig,\n              m29: m29,\n              m30: m30,\n              m31: m31,\n            }\n          })\n        : null\n    return {\n      $orig: $orig,\n      m7: m7,\n      m8: m8,\n      m9: m9,\n      m10: m10,\n      m11: m11,\n      m12: m12,\n      m13: m13,\n      m14: m14,\n      m15: m15,\n      m16: m16,\n      m17: m17,\n      m18: m18,\n      l0: l0,\n      g1: g1,\n      g2: g2,\n      l1: l1,\n      g3: g3,\n      m27: m27,\n      m28: m28,\n      l2: l2,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m19: m19,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-business.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-business.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-business\" :style=\"{\r\n\tcolor:params.color,\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',\r\n\tpadding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',\r\n\tfontSize:(params.fontsize*2)+'rpx'\r\n}\">\r\n\t<view class=\"busbox\" v-for=\"(item,index) in data\" :key=\"item.id\" v-if=\"!params.showstyle || params.showstyle =='1'\">\r\n\t\t<view class=\"businfo\" @click=\"gotourl\" :data-bid=\"item.bid\" :data-tourl=\"item.tourl\" :data-key=\"index\">\r\n\t\t\t<view class=\"f1\"><image class=\"image\" lazy-load=\"true\" lazy-load-margin=\"0\" :src=\"item.logo\"/></view>\r\n\t\t\t<view class=\"f2\">\r\n\t\t\t\t<view class=\"title\">{{item.name}}</view>\r\n\t\t\t\t<view class=\"score\" v-if=\"params.showpingfen!=='0'\"><image class=\"image\" :src=\"pre_url+'/static/img/star'+item.commentscore+'.png'\"/>{{item.comment_score}}分</view>\r\n        <view class=\"address\" v-if=\"params.showcategroy=='1'\">{{item.catname}}</view>\r\n\t\t\t\t<view class=\"sales\" v-if=\"params.showturnover!=='0' && item.turnover_show==1\"><text>营业额：</text>{{item.turnover}}</view>\r\n\t\t\t\t<view class=\"sales\" v-if=\"params.showsales!=='0'\"><text>销量：</text>{{item.sales}}</view>\r\n\t\t\t\t<view class=\"address\" v-if=\"params.showjianjie=='1'\"><text :decode=\"true\">{{item.content}}</text></view>\r\n\t\t\t\t<view class=\"address flex\"><view class=\"flex1\"><text v-if=\"params.showaddress!=='0'\">{{item.address}}</text></view><view v-if=\"params.showdistance\" :style=\"{color:t('color1')}\">{{item.juli}}</view></view>\r\n\t\t\t\t<view class=\"address flex flex-bt\" v-if=\"params.showmaidan=='1'\">\r\n\t\t\t\t\t<view class=\"btn\" @tap.stop=\"showMap\" :data-name=\"item.name\" :data-address=\"item.address\" :data-latitude=\"item.latitude\" :data-longitude=\"item.longitude\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">导航</view>\r\n\t\t\t\t\t<view class=\"btn\" @tap.stop=\"goto\" :data-url=\"'/pages/maidan/pay?bid='+item.bid\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">买单</view>\r\n\t\t\t\t\t<view class=\"btn\" @tap.stop=\"goto\" :data-url=\"'tel::'+item.tel\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">咨询电话</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"color: #999;font-size: 24rpx;\" v-if=\"item.activity_time && item.activity_time_status==1\" >\r\n\t\t\t\t\t活动时间：<text class=\"x1\">{{item.activity_time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ratio-list flex\">\r\n\t\t\t\t\t<view class=\"ratio-label flex-y-center\" v-if=\"item.rate_back && item.rate_back > 0\" :style=\"{color:t('color1'),borderColor:t('color1')}\">\r\n\t\t\t\t\t\t<view class=\"label\" :style=\"{backgroundColor:t('color1')}\">返</view>\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.rate_back}}%</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"ratio-label flex-y-center\" v-if=\"item.scoredkmaxpercent && item.scoredkmaxpercent > 0\"  :style=\"{color:t('color1'),borderColor:t('color1')}\">\r\n\t\t\t\t\t\t<view class=\"label\" :style=\"{backgroundColor:t('color1')}\">积</view>\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.scoredkmaxpercent}}%</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex queue-free\" v-if=\"params.showqueuefreeratio =='1' && item.queue_free_set ==1 && item.queue_free_rate_back >0\">\r\n\t\t\t\t\t<view class=\"queue-free-ratio flex\" :style=\"{color:t('color1'),borderColor:t('color1')}\">\r\n\t\t\t\t\t\t<view class=\"icon-div\" :style=\"{backgroundColor:t('color1')}\">\r\n\t\t\t\t\t\t\t<image class=\"icon\" :src=\"pre_url+'/static/img/qianbao.png'\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t最高排队补贴 {{item.queue_free_rate_back}}%\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n        <block v-if=\"params.showdedamount =='1'\">\r\n          <view class=\"flex queue-free\" v-if=\"item.dedamount_maxdkpercent && item.dedamount_maxdkpercent >0\" style=\"justify-content: flex-start;\">\r\n            <view class=\"queue-free-ratio flex\" :style=\"{color:t('color1'),borderColor:t('color1')}\">\r\n              <view class=\"icon-div\" :style=\"{backgroundColor:t('color1')}\">\r\n                <image class=\"icon\" :src=\"pre_url+'/static/img/qianbao.png'\"></image>\r\n              </view>\r\n              抵扣 {{item.dedamount_maxdkpercent}}%\r\n            </view>\r\n          </view>\r\n        </block>\r\n\t\t\t\t<view class=\"instore\" v-if=\"params.showinstore=='1'\" @tap.stop=\"gotourl\" :data-bid=\"item.bid\" :data-tourl=\"item.tourl\"  :style=\"{background:'rgba('+t('color1rgb')+')'}\">\r\n\t\t\t\t\t\t<text >进店</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 店铺商品 -->\r\n\t\t<view class=\"buspro\" v-if=\"params.showproduct == 1\">\r\n\t\t\t<view class=\"item\" v-for=\"(item2,index2) in item.prolist\" :style=\"'width:23%;margin-right:'+(index2%4!=3?'2%':0)\" :key=\"item2.id\" @click=\"goto\" :data-url=\"item2.module == 'yuyue' ? '/activity/yuyue/product?id='+item2.id : '/pages/shop/product?id='+item2.id\">\r\n\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item2.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t<view class=\"p1\">{{item2.name}}</view>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<view class=\"p2-1\">\r\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{item2.sell_price}}\r\n              <text v-if=\"item2.price_show && item2.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item2.price_show_text}}</text>\r\n              <text v-if=\"item2.module == 'yuyue'\" style=\"font-size:24rpx\">/{{item2.danwei}}</text></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n          <!-- 商品处显示会员价 -->\r\n          <view v-if=\"item2.price_show && item2.price_show == 1\" style=\"line-height: 44rpx;\">\r\n            <text style=\"font-size:24rpx\">￥{{item2.sell_putongprice}}</text>\r\n          </view>\r\n          <view v-if=\"item2.priceshows && item2.priceshows.length>0\">\r\n            <view v-for=\"(item3,index3) in item2.priceshows\" style=\"line-height: 44rpx;\">\r\n              <text style=\"font-size:24rpx\">￥{{item3.sell_price}}</text>\r\n              <text style=\"margin-left: 15rpx;font-size: 22rpx;font-weight: 400;\">{{item3.price_show_text}}</text>\r\n            </view>\r\n          </view>\r\n\t\t\t\t\t<view v-if=\"item2.module == 'yuyue'\" class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" @click.stop=\"goto\" :data-url=\"'/activity/yuyue/product?id='+item2.id\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t\t<view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item2.module != 'yuyue' && !item2.price_type\" @click.stop=\"buydialogChange\" :data-proid=\"item2.id\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 店铺商品 -->\r\n\t\t<!-- 秒杀商品 -->\r\n\t\t<!-- <view class=\"buspro\" v-if=\"params.showseckill == 1 && item.seckillprolist.length>0\">\r\n\t\t\t<view class=\"buspro-title\">限时秒杀</view>\r\n\t\t\t<scroll-view scroll-x=\"true\" style=\"width: 100%;white-space: nowrap;\">\r\n\t\t\t\t<view class=\"item\" v-for=\"(item2,index2) in item.seckillprolist\" :style=\"'width:23%;margin-right:'+(index2%4!=3?'2%':0)\" :key=\"item2.id\" @click=\"goto\" :data-url=\"'/activity/seckill/product?id='+item2.id\">\r\n\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t<image class=\"image\" :src=\"item2.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t<view class=\"p1\">{{item2.name}}</view>\r\n\t\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t\t<view class=\"p2-1\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{item2.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item2.market_price && item2.market_price>item2.sell_price\"><text style=\"font-size:24rpx\">￥</text>{{item2.market_price}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view> -->\r\n\t\t<!-- 秒杀商品 -->\r\n\t\t<view class=\"free_product\" v-if=\"params.showbusinessmiandan == 1 && item.miandanset_status == 1 && item.miandanprolist.length>0 \" >\r\n\t\t\t<view class=\"product\" v-for=\"(item3,index3) in item.miandanprolist\" @click=\"gotourl\" :data-bid=\"item.bid\" :data-tourl=\"item.tourl\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"hui\">免</view>\r\n\t\t\t\t\t<view class=\"price\">￥{{item3.sell_price}}</view>\r\n\t\t\t\t\t<view class=\"proname\">{{item3.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"buspro\" v-if=\"params.showscoreshop == 1 && item.scoreshopprolist.length>0\">\r\n\t\t\t<scroll-view scroll-x=\"true\" style=\"width: 100%;white-space: nowrap;\">\r\n\t\t\t\t<view class=\"item\" v-for=\"(item2,index2) in item.scoreshopprolist\" :style=\"'width:23%;margin-right:'+(index2%4!=3?'2%':0)\" :key=\"item2.id\" @click=\"goto\" :data-url=\"'/activity/scoreshop/product?id='+item2.id\">\r\n\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t<image class=\"image\" :src=\"item2.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t<view class=\"p1\">{{item2.name}}</view>\r\n\t\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t\t<view class=\"p2-1\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">{{item2.score_price}}{{t('积分')}}<text v-if=\"item2.money_price>0\">+{{item2.money_price}}元</text></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"cuxiaopro\" :style=\"{background:'linear-gradient(180deg,rgba('+t('color1rgb')+',0.2),rgba('+t('color1rgb')+',0) 60%)'}\" v-if=\"(params.showseckill == 1 || params.showtuangou == 1 || params.showkanjia == 1 || params.showcollage == 1 || params.showluckycollage == 1) && item.cuxiaoprolist.length>0\">\r\n\t\t\t<view class=\"item\" v-for=\"(item2,index2) in item.cuxiaoprolist\"  :key=\"item2.id\" @click=\"goto\" :data-url=\"'/activity/'+item2.product_type+'/product?id='+item2.id\">\r\n\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item2.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t<view class=\"p1\">{{item2.name}}</view>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<view class=\"p2-1\">\r\n\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"item2.product_type=='tuangou'\" class=\"t1-1\">低至</text>\r\n\t\t\t\t\t\t\t\t<text>￥</text>{{item2.sell_price}}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"(item2.product_type=='seckill') && item2.market_price && item2.market_price>item2.sell_price\"><text style=\"font-size:24rpx\">￥</text>{{item2.market_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t<view class=\"p3-1\" :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\">\r\n\t\t\t\t\t\t\t<text v-if=\"item2.product_type=='seckill'\">秒杀</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item2.product_type=='kanjia'\">砍价</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item2.product_type=='tuangou'\">团购</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item2.product_type=='collage'\">拼团</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item2.product_type=='luckycollage'\">幸运拼团</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"p3-2\" v-if=\"item2.product_type=='seckill'\">已抢购{{item2.sales}}件</text>\r\n\t\t\t\t\t\t<text class=\"p3-2\" v-if=\"item2.product_type=='kanjia'\">已砍走{{item2.sales}}件</text>\r\n\t\t\t\t\t\t<text class=\"p3-2\" v-if=\"item2.product_type=='collage' || item2.product_type=='luckycollage'\">{{item2.teamnum}}人拼</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"busbox2\" v-if=\"params.showstyle && params.showstyle =='2'\" v-for=\"(item,index) in data\" :key=\"item.id\" @tap=\"goto\"  :data-url=\"'/pagesA/business/businessindex?id='+item.bid\" >\r\n\t\t<view class=\"new_blist\" >\r\n\t\t\t<view class=\"f1\"><image class=\"image\" :src=\"item.logo\" mode=\"aspectFill\"  /></view>\r\n\t\t\t<view class=\"f2\">\r\n\t\t\t\t<view class=\"t1\">{{item.name}}</view>\r\n\t\t\t\t<view class=\"t2\"><image class=\"image\" :src=\"pre_url+'/static/img/telphone.png'\" mode=\"widthFix\"/>{{item.tel}}</view>\r\n\t\t\t\t<view class=\"t2\" v-if=\"params.showaddress ==1\"><image class=\"image\" :src=\"pre_url+'/static/img/position.png'\" mode=\"widthFix\"/>\r\n\t\t\t\t\t<text class=\"text\">{{item.address}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f3\">\r\n\t\t\t\t<image class=\"image\" :src=\"pre_url+'/static/img/calltel.png'\" @tap.stop=\"callphone\" :data-tel=\"item.tel\" mode=\"widthFix\" />\r\n\t\t\t\t<view  v-if=\"params.showdistance ==1\" style=\"color: #EC4149;font-size: 26rpx;margin-top: 10rpx;\">{{item.juli}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @addcart=\"addcart\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\"></buydialog>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tpre_url:getApp().globalData.pre_url,\r\n\t\t\t\tbuydialogShow:false,\r\n\t\t\t\tproid:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tbuydialogChange: function (e) {\r\n\t\t\t\tif(!this.buydialogShow){\r\n\t\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t\t}\r\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t\tconsole.log(this.buydialogShow);\r\n\t\t\t},\r\n\t\t\taddcart:function(){\r\n\t\t\t\tthis.$emit('addcart');\r\n\t\t\t},\r\n\t\t\tgotourl:function(e){\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar tourl = e.currentTarget.dataset.tourl;\r\n\t\t\t\tif(tourl){\r\n\t\t\t\t\ttourl = tourl;\r\n\t\t\t\t}else if(this.params.intotype==1){\r\n\t\t\t\t\ttourl = '/restaurant/takeaway/index?bid='+bid\r\n\t\t\t\t}else if(this.params.intotype==2){\r\n\t\t\t\t\ttourl = '/restaurant/shop/index?bid='+bid;\r\n\t\t\t\t\tif(this.params.fzcode){\r\n\t\t\t\t\t\ttourl +='&fzcode='+this.params.fzcode;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(this.params,'this.params');\r\n\t\t\t\t\tif(this.params.tablebid){\r\n\t\t\t\t\t\ttourl +='&tableId='+this.params.tablebid;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar key = e.currentTarget.dataset.key;\r\n\t\t\t\t\tvar mdid = this.data[key].mdid;\r\n\t\t\t\t\tif(!this.params.tablebid && mdid){\r\n\t\t\t\t\t\ttourl +='&tableId=0&mdid='+mdid;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\ttourl ='/pagesExt/business/index?id='+bid;\r\n\t\t\t\t}\r\n\t\t\t\tapp.goto(tourl);\r\n\t\t\t},\r\n\t\t\tcallphone:function(e) {\r\n\t\t\t\tvar phone = e.currentTarget.dataset.tel;\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\tfail: function () {\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-business{height: auto; position: relative;}\n.dp-business .busbox{background: #fff;padding:16rpx;overflow: hidden;margin-bottom:16rpx;width:100%}\n.dp-business .businfo{display:flex;width:100%}\n.dp-business .businfo .f1{width:200rpx;height:200rpx; margin-right:20rpx;flex-shrink:0}\n.dp-business .businfo .f1 .image{ width: 100%;height:100%;border-radius:20rpx;object-fit: cover;}\n.dp-business .businfo .f2{flex:1; position: relative;}\n.dp-business .businfo .f2 .title{font-size:28rpx;font-weight:bold; color: #222;line-height:46rpx;margin-bottom:3px;}\n.dp-business .businfo .f2 .score{font-size:24rpx;color:#f99716;}\n.dp-business .businfo .f2 .score .image{width:140rpx; height:50rpx; vertical-align: middle;margin-bottom:3px; margin-right:3px;}\n.dp-business .businfo .f2 .sales{font-size:24rpx; color:#31C88E;margin-bottom:3px;}\n.dp-business .businfo .f2 .address{color:#999;font-size:24rpx;line-height:40rpx;margin-bottom:3px;}\r\n.dp-business .businfo .btn {font-size:28rpx;line-height:56rpx;border-radius: 28rpx;padding: 0 30rpx;}\n\n.dp-business .buspro{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap;margin-top:32rpx}\n.dp-business .buspro .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden;}\n.dp-business .buspro .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\n.dp-business .buspro .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.dp-business .buspro .product-info {padding:20rpx 0;position: relative;}\n.dp-business .buspro .product-info .p1 {color:#323232;font-weight:bold;font-size:24rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;height:36rpx}\n.dp-business .buspro .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\n.dp-business .buspro .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\n.dp-business .buspro .product-info .p2-1 .t1{font-size:28rpx;}\n.dp-business .buspro .product-info .p2-1 .t2 {margin-left:10rpx;font-size:22rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-business .buspro .product-info .p2-1 .t3 {margin-left:10rpx;font-size:22rpx;color: #999;}\n.dp-business .buspro .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\n.dp-business .buspro .product-info .p3{color:#999999;font-size:20rpx;margin-top:10rpx}\n.dp-business .buspro .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:20rpx;right:0;text-align:center;}\n.dp-business .buspro .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}\n.dp-business .buspro .product-info .p4 .img{width:100%;height:100%;}\r\n\r\n.dp-business .cuxiaopro{width: 100%;padding: 10rpx  20rpx;border-radius: 10rpx;margin-top: 20rpx;}\n.dp-business .buspro-title{line-height: 60rpx;font-size:16px;font-weight: bold}\n.dp-business .cuxiaopro .item{display: flex;align-items: center;margin-bottom: 20rpx;overflow: hidden;border-bottom: 1rpx solid #f2f2f2;}\n.dp-business .cuxiaopro .product-pic{width: 140rpx;height: 140rpx;flex-shrink: 0;overflow: hidden;border-radius: 6rpx;}\n.dp-business .cuxiaopro .product-pic .image{width: 100%;height:auto;object-fit: cover;border-radius: 6rpx;}\n.dp-business .cuxiaopro .product-info {padding:10rpx 0;flex: 1;margin-left: 10px;align-self: flex-start;}\n.dp-business .cuxiaopro .product-info .p1 {color:#323232;font-weight:bold;font-size:12rpx;line-height:18px;margin-bottom:5px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;height:18px}\n.dp-business .cuxiaopro .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\n.dp-business .cuxiaopro .product-info .p2-1{flex-grow:1;flex-shrink:1;height:20px;line-height:20px;overflow:hidden;white-space: nowrap}\n.dp-business .cuxiaopro .product-info .p2-1 .t1{font-size:30rpx;font-weight: bold;}\r\n.dp-business .cuxiaopro .product-info .p2-1 .t1-1{font-size:24rpx;font-weight:400;}\n.dp-business .cuxiaopro .product-info .p2-1 .t2 {margin-left:5px;font-size:11px;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-business .cuxiaopro .product-info .p2-2{font-size:10px;height:20px;line-height:20px;text-align:right;padding-left:10px;color:#999}\n.dp-business .cuxiaopro .product-info .p3{font-size:10px;margin-top:5px;display: flex;justify-content: space-between;}\r\n.dp-business .cuxiaopro .product-info .p3-1{border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:4rpx 10rpx;font-size:24rpx;min-width: 80rpx;flex-shrink: 0;text-align: center;}\r\n.dp-business .cuxiaopro .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}\n\r\n.dp-business .busbox2{background: #fff;padding: 8px;overflow: hidden;width: 100%;border-bottom: 2rpx solid #f5f5f5;}\r\n.dp-business .busbox2:last-child{border: none;}\r\n.dp-business .new_blist{display:flex;width:100%;align-items: center;}\n.dp-business .new_blist .f1{width:130rpx;height:130rpx; margin-right: 30rpx;flex-shrink:0}\n.dp-business .new_blist .f1 .image{ width: 100%;height:100%;border-radius:50%;object-fit: cover;}\n.dp-business .new_blist .f2{flex:1}\n.dp-business .new_blist .f2 .t1{font-size:28rpx; color: #222;font-weight:bold;line-height:60rpx}\n.dp-business .new_blist .f2 .t2{font-size:28rpx; color: #222;line-height:40rpx;align-items: center;margin: 10rpx 0;display: flex}\n.dp-business .new_blist .f2 .t2 .image{width: 32rpx;  height:32rpx ; line-height: 60rpx;margin-right: 20rpx;}\n.dp-business .new_blist .f2 .t2 .text{width: 350rpx;overflow: hidden;text-overflow: ellipsis; white-space: nowrap}\n.dp-business .new_blist .f3{display: flex;align-items: center;flex-direction: column; justify-content: center}\n.dp-business .new_blist .f3 .image{width: 80rpx;height: 80rpx}\r\n\r\n.dp-business .instore{ position: absolute; top:10rpx;right:15rpx; color: #fff; padding:6rpx 10rpx; border-radius: 5rpx;}\r\n\r\n.free_product .product{width:100%;overflow:hidden;margin-right:24rpx;margin: 10rpx 0;}\r\n.free_product .product .item{ display: flex;align-items: center; padding:4rpx 0}\r\n.free_product .product .item .hui{ background: #FAF0ED; color: #E16954;font-weight: bold;padding:2rpx 10rpx; border-radius:10rpx;height: 36rpx;font-size: 24rpx;}\r\n.free_product .product .item .price{font-size:28rpx;color:#FC5648;margin-right: 20rpx;}\r\n\n.free_product .f1{width:108rpx;height:108rpx;border-radius:8rpx;background:#f6f6f6}\n.free_product .product .desc{ display: inline-flex; border:1rpx solid #E8A7AE; color:#DF474E;padding: 10rpx 0 ;padding:5rpx 20rpx; border-radius:10rpx;margin-top: 10rpx;font-size: 24rpx;}\r\n/* 最高排队免单比例 */\r\n.queue-free{justify-content: flex-end}\r\n.queue-free-ratio{line-height: 50rpx;text-align: center;border-radius: 10rpx;border:4rpx solid #FC5D2B;color: #FC5D2B;font-size: 28rpx;padding-right: 10rpx;}\r\n.queue-free-ratio .icon-div{height: 50rpx;width: 50rpx;display: flex;align-items: center;justify-content: center;margin-right: 10rpx}\n.queue-free-ratio .icon-div .icon{width: 40rpx;height: 40rpx}\r\n/*返利 和 积分显示*/\r\n.ratio-list{padding-top: 10rpx;}\r\n.ratio-label{height: 40rpx;border-radius: 10rpx;width:160rpx;border: 2rpx solid;margin-right:20rpx;}\r\n.ratio-label .label{width: 55rpx ;height: 40rpx;line-height: 40rpx;border-radius: 10rpx 20rpx 5rpx 10rpx;color: #fff;text-align: center;}\r\n.ratio-label .t1{text-align: center;width: 65%;font-size: 28rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-business.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-business.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839375394\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}