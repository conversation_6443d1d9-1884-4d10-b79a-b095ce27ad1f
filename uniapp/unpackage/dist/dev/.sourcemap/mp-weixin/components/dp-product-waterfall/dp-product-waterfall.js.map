{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-waterfall/dp-product-waterfall.vue?1de0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-waterfall/dp-product-waterfall.vue?1453", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-waterfall/dp-product-waterfall.vue?f518", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-waterfall/dp-product-waterfall.vue?ea5c", "uni-app:///components/dp-product-waterfall/dp-product-waterfall.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-waterfall/dp-product-waterfall.vue?dd48", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-waterfall/dp-product-waterfall.vue?9c1e"], "names": ["props", "list", "type", "required", "offset", "default", "idfield", "imageSrcKey", "cols", "validator", "imageStyle", "showstyle", "menuindex", "saleimg", "showname", "namecolor", "showprice", "showcost", "showsales", "showcart", "cartimg", "showstock", "showbname", "showcoupon", "showcommission", "probgcolor", "data", "topArr", "allPositionArr", "allHeightArr", "height", "oldNum", "num", "buydialogShow", "proid", "showLinkStatus", "lx_bname", "lx_name", "lx_bid", "lx_tel", "productType", "ggNum", "pre_url", "created", "methods", "buydialogChange", "addcart", "showLinkChange", "that", "imageLoadHandle", "query", "select", "fields", "size", "shorterIndex", "shorterValue", "longerIndex", "longerValue", "getTopArrMsg", "top", "left", "exec", "refresh", "arr", "toDetail", "url", "app"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACqC;;;AAGxG;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,kzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8It2B;AAAA,eACA;EACAA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAG;IACA;IACA;IACAC;MACAJ;MACAG;IACA;IACA;IACAE;MACAL;MACAG;IACA;IACA;IACAG;MACAN;MACAG;MACAI;QAAA;MAAA;IACA;IACAC;MACAR;IACA;IAEAS;MACAN;IACA;IACAO;MACAP;IACA;IACAQ;MACAR;IACA;IACAS;MACAT;IACA;IACAU;MACAV;IACA;IACAW;MACAX;IACA;IACAY;MACAZ;IACA;IACAa;MACAb;IACA;IACAc;MACAd;IACA;IACAe;MACAf;IACA;IACAgB;MACAhB;IACA;IACAiB;MACAjB;IACA;IACAkB;MACAlB;IACA;IACAmB;MACAnB;IACA;IACAoB;MAAApB;IAAA;EACA;EACAqB;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;YACA;YACA;cACA;gBACA;cACA;gBACA;cACA;YACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAC;MACAA;MACAA;MACAA;MACAA;IACA;IAEAC;MAAA;MACA;QACAC;MACAA,MACAC,iBACAC;QACAC;MACA;QACA;QACA;QACA;UACA;YACA;cACA;gBAAA;cAAA;cACA;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;YACA,oBAGAC;cAFAJ;cACAC;YAEA;cACAI;cACAC;YACA;YACA;YACA,8BACAL;YACA;UACA;UACA;UACA;QACA;MACA,GACAM;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACAC;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnVA;AAAA;AAAA;AAAA;AAAutC,CAAgB,uoCAAG,EAAC,C;;;;;;;;;;;ACA3uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-product-waterfall/dp-product-waterfall.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-product-waterfall.vue?vue&type=template&id=2dd26264&scoped=true&\"\nvar renderjs\nimport script from \"./dp-product-waterfall.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-product-waterfall.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-product-waterfall.vue?vue&type=style&index=0&id=2dd26264&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2dd26264\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-product-waterfall/dp-product-waterfall.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-waterfall.vue?vue&type=template&id=2dd26264&scoped=true&\"", "var components\ntry {\n  components = {\n    buydialogPifa: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa/buydialog-pifa\" */ \"@/components/buydialog-pifa/buydialog-pifa.vue\"\n      )\n    },\n    buydialogPifa2: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa2/buydialog-pifa2\" */ \"@/components/buydialog-pifa2/buydialog-pifa2.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice == \"1\" &&\n      !item.price_color\n        ? _vm.t(\"color1\")\n        : null\n    var m1 =\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true)) &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      _vm.showprice == \"1\" &&\n      item.product_type == 2 &&\n      item.unit_price &&\n      item.unit_price > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m2 =\n      item.xunjia_text &&\n      item.price_type == 1 &&\n      item.sell_price <= 0 &&\n      _vm.showstyle != 1\n        ? _vm.t(\"color1\")\n        : null\n    var m3 =\n      item.xunjia_text &&\n      item.price_type == 1 &&\n      item.sell_price <= 0 &&\n      _vm.showstyle == 1\n        ? _vm.t(\"color1\")\n        : null\n    var m4 =\n      item.xunjia_text &&\n      item.price_type == 1 &&\n      item.sell_price <= 0 &&\n      item.xunjia_text &&\n      item.price_type == 1\n        ? _vm.t(\"color1\")\n        : null\n    var g0 = item.priceshows && item.priceshows.length > 0\n    var m5 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"color2rgb\")\n        : null\n    var m6 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"color2\")\n        : null\n    var m7 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"佣金\")\n        : null\n    var m8 =\n      _vm.showcart == 1 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m9 =\n      _vm.showcart == 1 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1\")\n        : null\n    var m10 =\n      _vm.showcart == 2 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m11 =\n      _vm.showcart == 2 && !item.price_type && item.hide_cart != true\n        ? _vm.t(\"color1\")\n        : null\n    var g1 = _vm.showcoupon == 1 && item.couponlist.length > 0\n    var l0 = g1\n      ? _vm.__map(item.couponlist, function (coupon, index2) {\n          var $orig = _vm.__get_orig(coupon)\n          var m12 = _vm.t(\"color1rgb\")\n          var m13 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m12: m12,\n            m13: m13,\n          }\n        })\n      : null\n    var m14 = item.hongbaoEdu > 0 ? _vm.t(\"color2\") : null\n    var m15 = item.hongbaoEdu > 0 ? _vm.t(\"color2rgb\") : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      g0: g0,\n      m5: m5,\n      m6: m6,\n      m7: m7,\n      m8: m8,\n      m9: m9,\n      m10: m10,\n      m11: m11,\n      g1: g1,\n      l0: l0,\n      m14: m14,\n      m15: m15,\n    }\n  })\n  var m16 = _vm.showLinkStatus && _vm.lx_tel ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        m16: m16,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-waterfall.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-waterfall.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"waterfalls-box\" :style=\"{ height: height + 'px' }\">\r\n\t\t<view v-for=\"(item, index) of list\" class=\"waterfalls-list\" :key=\"item[idfield]\"\r\n\t\t\t:id=\"'waterfalls-list-id-' + item[idfield]\" :ref=\"'waterfalls-list-id-' + item[idfield]\" :style=\"{\r\n        '--offset': offset + 'px',\r\n        '--cols': cols,\r\n\t\t\t\t'background':probgcolor,\r\n        top: allPositionArr[index] ? allPositionArr[index].top : 0,\r\n        left: allPositionArr[index] ? allPositionArr[index].left : 0,\r\n      }\"  @click=\"toDetail(index)\">\r\n\t\t\t<image class=\"waterfalls-list-image\" mode=\"widthFix\" :style=\"imageStyle\" :src=\"item[imageSrcKey] || ' '\"\r\n\t\t\t\t@load=\"imageLoadHandle(index)\" @error=\"imageLoadHandle(index)\" />\r\n\t\t\t<image class=\"saleimg\" :src=\"saleimg\" v-if=\"saleimg!=''\" mode=\"widthFix\" />\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t<view class=\"p1\" v-if=\"showname == 1\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"binfo flex-y-center\" v-if=\"showbname&&item.binfo\">\r\n\t\t\t\t\t\t<image :src=\"item.binfo.logo\" class=\"t1\"><text class=\"t2\">{{item.binfo.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view  v-if=\"item.show_cost && item.price_type != 1\" :style=\"{color:item.cost_color?item.cost_color:'#999',fontSize:'36rpx'}\"><text style=\"font-size: 24rpx;\">{{item.cost_tag}}</text>{{item.cost_price}}</view>      \r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<view class=\"p2-1 flex-y-center\" v-if=\"(!item.show_sellprice || (item.show_sellprice && item.show_sellprice==true)) && ( item.price_type != 1 || item.sell_price > 0) && showprice == '1'\">\r\n\t\t\t\t\t\t\t<view class=\"t1\" :style=\"{color:item.price_color?item.price_color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">{{item.price_tag?item.price_tag:'￥'}}</text>{{item.sell_price}}<text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\r\n                <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 22rpx;font-weight: 400;\">{{item.price_show_text}}</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.product_type==2 && item.unit_price && item.unit_price>0\" class=\"t1-m\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t(约{{item.unit_price}}元/斤)\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"t2\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.show_sellprice == '1' && item.market_price*1 > item.sell_price*1  && showprice == '1'\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\" v-if=\"item.juli\" style=\"color:#888;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"p2-1\" v-if=\"item.xunjia_text && item.price_type == 1 && item.sell_price <= 0\"\r\n\t\t\t\t\t\t\tstyle=\"height: 50rpx;line-height: 44rpx;\">\r\n\t\t\t\t\t\t\t<text v-if=\"showstyle!=1\" class=\"t1\" :style=\"{color:t('color1'),fontSize:'30rpx'}\">询价</text>\r\n\t\t\t\t\t\t\t<text v-if=\"showstyle==1\" class=\"t1\" :style=\"{color:t('color1')}\">询价</text>\r\n\t\t\t\t\t\t\t<block v-if=\"item.xunjia_text && item.price_type == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"lianxi\" :style=\"{background:t('color1')}\" @tap.stop=\"showLinkChange\"\r\n\t\t\t\t\t\t\t\t\t:data-lx_name=\"item.lx_name\" :data-lx_bid=\"item.lx_bid\"\r\n\t\t\t\t\t\t\t\t\t:data-lx_bname=\"item.lx_bname\" :data-lx_tel=\"item.lx_tel\" data-btntype=\"2\">\r\n\t\t\t\t\t\t\t\t\t{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n          <!-- 商品处显示会员价 -->\r\n          <view v-if=\"item.price_show && item.price_show == 1\" style=\"line-height: 46rpx;\">\r\n            <text style=\"font-size:24rpx\">￥{{item.sell_putongprice}}</text>\r\n          </view>\r\n          <view v-if=\"item.priceshows && item.priceshows.length>0\">\r\n            <view v-for=\"(item2,index2) in item.priceshows\" style=\"line-height: 46rpx;\">\r\n              <text style=\"font-size:24rpx\">￥{{item2.sell_price}}</text>\r\n              <text style=\"margin-left: 15rpx;font-size: 22rpx;font-weight: 400;\">{{item2.price_show_text}}</text>\r\n            </view>\r\n          </view>\r\n\t\t\t\t\t<view class=\"couponitem\" v-if=\"showcommission == 1 && item.commission_price > 0\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<view class=\"t\" :style=\"{background:'rgba('+t('color2rgb')+',0.1)',color:t('color2')}\">\r\n\t\t\t\t\t\t\t\t<text>{{t('佣金')}}{{item.commission_price}}{{item.commission_desc}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"p1\" v-if=\"item.merchant_name\"\r\n\t\t\t\t\t\tstyle=\"color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal;\">\r\n\t\t\t\t\t\t<text>{{item.merchant_name}}</text></view>\r\n\t\t\t\t\t<view class=\"p1\" v-if=\"item.main_business\"\r\n\t\t\t\t\t\tstyle=\"color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;\">\r\n\t\t\t\t\t\t<text>{{item.main_business}}</text></view>\r\n          <view class=\"p3\" v-if=\"item.product_type==3\">\r\n          \t<text>手工费: ￥{{item.hand_fee?item.hand_fee:0}}</text>\r\n          </view>\r\n\t\t\t\t\t<view class=\"p3\" v-if=\"(showsales=='1' && item.sales>0) || showstock=='1'\">\r\n\t\t\t\t\t\t<text v-if=\"showsales=='1' && item.sales>0\">已售{{item.sales}}</text>\r\n\t\t\t\t\t\t<text v-if=\"(showsales=='1' && item.sales>0) && showstock=='1'\"\r\n\t\t\t\t\t\t\tstyle=\"padding:0 4px;font-size:22rpx\">|</text>\r\n\t\t\t\t\t\t<text v-if=\"showstock=='1'\">仅剩{{item.stock}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"(showsales !='1' ||  item.sales<=0) && item.main_business\" style=\"height: 44rpx;\">\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"\r\n\t\t\t\t\t\tv-if=\"showcart==1 && !item.price_type && item.hide_cart!=true\" @click.stop=\"buydialogChange\"\r\n\t\t\t\t\t\t:data-proid=\"item[idfield]\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t\t<view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"\r\n\t\t\t\t\t\tv-if=\"showcart==2 && !item.price_type && item.hide_cart!=true\" @click.stop=\"buydialogChange\"\r\n\t\t\t\t\t\t:data-proid=\"item[idfield]\">\r\n\t\t\t\t\t\t<image :src=\"cartimg\" class=\"img\" /></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"showcoupon==1 && (item.couponlist).length>0\" class=\"couponitem\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view v-for=\"(coupon, index2) in item.couponlist\" :key=\"index2\" class=\"t\"\r\n\t\t\t\t\t\t\t:style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">\r\n\t\t\t\t\t\t\t<text v-if=\"coupon.minprice > 0\">满{{coupon.minprice}}减{{coupon.money}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"coupon.minprice == 0\">{{coupon.money}}元无门槛</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bg-desc\" v-if=\"item.hongbaoEdu > 0\"\r\n\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t\t可获额度 +{{item.hongbaoEdu}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<block v-if=\"productType == 4\">\r\n\t\t\t<block v-if=\"ggNum == 2\">\r\n\t\t\t\t<buydialog-pifa v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" />\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<buydialog-pifa2 v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" />\r\n\t\t\t</block>\r\n\t\t</block>\r\n\t\t<block v-else>\r\n\t\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @addcart=\"addcart\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\"></buydialog>\r\n\t\t</block>\r\n\t\t<view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"showLinkChange\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/close.png'\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"title\">{{lx_name}}</view>\r\n\t\t\t\t\t<view class=\"row\" v-if=\"lx_bid > 0\">\r\n\t\t\t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">店铺名称</view>\r\n\t\t\t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;display: flex;\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+lx_bid\">\r\n\t\t\t\t\t\t  <view style=\"width: 100%;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;\">{{lx_bname}}</view>\r\n\t\t\t\t\t\t  <view style=\"flex: 1;\"></view>\r\n\t\t\t\t\t\t  <image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"row\" v-if=\"lx_tel\">\r\n\t\t\t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">联系电话</view>\r\n\t\t\t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;\" @tap=\"goto\" :data-url=\"'tel::'+lx_tel\" :style=\"{color:t('color1')}\">{{lx_tel}}\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"lx_tel\">\r\n\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\trequired: true\r\n\t\t\t},\r\n\t\t\t// offset 间距，单位为 px\r\n\t\t\toffset: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 8\r\n\t\t\t},\r\n\t\t\t// 列表渲染的 key 的键名，值必须唯一，默认为 id\r\n\t\t\tidfield: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"id\"\r\n\t\t\t},\r\n\t\t\t// 图片 src 的键名\r\n\t\t\timageSrcKey: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"pic\"\r\n\t\t\t},\r\n\t\t\t// 列数\r\n\t\t\tcols: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 2,\r\n\t\t\t\tvalidator: (num) => num >= 2\r\n\t\t\t},\r\n\t\t\timageStyle: {\r\n\t\t\t\ttype: Object\r\n\t\t\t},\r\n\r\n\t\t\tshowstyle: {\r\n\t\t\t\tdefault: 2\r\n\t\t\t},\r\n\t\t\tmenuindex: {\r\n\t\t\t\tdefault: -1\r\n\t\t\t},\r\n\t\t\tsaleimg: {\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tshowname: {\r\n\t\t\t\tdefault: 1\r\n\t\t\t},\r\n\t\t\tnamecolor: {\r\n\t\t\t\tdefault: '#333'\r\n\t\t\t},\r\n\t\t\tshowprice: {\r\n\t\t\t\tdefault: '1'\r\n\t\t\t},\r\n\t\t\tshowcost: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowsales: {\r\n\t\t\t\tdefault: '1'\r\n\t\t\t},\r\n\t\t\tshowcart: {\r\n\t\t\t\tdefault: '1'\r\n\t\t\t},\r\n\t\t\tcartimg: {\r\n\t\t\t\tdefault: '/static/imgsrc/cart.svg'\r\n\t\t\t},\r\n\t\t\tshowstock: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowbname: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowcoupon: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowcommission: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tprobgcolor:{default:'#fff'}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttopArr: [], // left, right 多个时依次表示第几列的数据\r\n\t\t\t\tallPositionArr: [], // 保存所有的位置信息\r\n\t\t\t\tallHeightArr: [], // 保存所有的 height 信息\r\n\t\t\t\theight: 0, // 外层包裹高度\r\n\t\t\t\toldNum: 0,\r\n\t\t\t\tnum: 0,\r\n\t\t\t\tbuydialogShow: false,\r\n\t\t\t\tproid: 0,\r\n\t\t\t\tshowLinkStatus: false,\r\n        lx_bname:'',\r\n\t\t\t\tlx_name: '',\r\n\t\t\t\tlx_bid: '',\r\n\t\t\t\tlx_tel: '',\r\n\t\t\t\tproductType:'',\r\n\t\t\t\tggNum:'',\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.refresh();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tbuydialogChange: function(e) {\r\n\t\t\t\tif (!this.buydialogShow) {\r\n\t\t\t\t\tthis.proid = e.currentTarget.dataset.proid;\r\n\t\t\t\t\tthis.list.forEach(item => {\r\n\t\t\t\t\t\tif(item[this.idfield] == this.proid){\r\n\t\t\t\t\t\t\tthis.productType = item.product_type;\r\n\t\t\t\t\t\t\tif(item.product_type == 4){\r\n\t\t\t\t\t\t\t\tif(item.gg_num){\r\n\t\t\t\t\t\t\t\t\tthis.ggNum = item.gg_num;\r\n\t\t\t\t\t\t\t\t}else if(item.guigedata){\r\n\t\t\t\t\t\t\t\t\tthis.ggNum = Object.keys(JSON.parse(item.guigedata)).length;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t},\r\n\t\t\taddcart: function() {\r\n\t\t\t\tthis.$emit('addcart');\r\n\t\t\t},\r\n\t\t\tshowLinkChange: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.showLinkStatus = !that.showLinkStatus;\r\n\t\t\t\tthat.lx_name = e.currentTarget.dataset.lx_name;\r\n\t\t\t\tthat.lx_bid = e.currentTarget.dataset.lx_bid;\r\n\t\t\t\tthat.lx_bname = e.currentTarget.dataset.lx_bname;\r\n\t\t\t\tthat.lx_tel = e.currentTarget.dataset.lx_tel;\r\n\t\t\t},\r\n\r\n\t\t\timageLoadHandle(index) {\r\n\t\t\t\tconst id = \"waterfalls-list-id-\" + this.list[index][this.idfield],\r\n\t\t\t\t\tquery = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery\r\n\t\t\t\t\t.select(\"#\" + id)\r\n\t\t\t\t\t.fields({\r\n\t\t\t\t\t\tsize: true\r\n\t\t\t\t\t}, (data) => {\r\n\t\t\t\t\t\tthis.num++;\r\n\t\t\t\t\t\tthis.$set(this.allHeightArr, index, data.height);\r\n\t\t\t\t\t\tif (this.num === this.list.length) {\r\n\t\t\t\t\t\t\tfor (let i = this.oldNum; i < this.num; i++) {\r\n\t\t\t\t\t\t\t\tconst getTopArrMsg = () => {\r\n\t\t\t\t\t\t\t\t\tlet arrtmp = [...this.topArr].sort((a, b) => a - b);\r\n\t\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\t\tshorterIndex: this.topArr.indexOf(arrtmp[0]),\r\n\t\t\t\t\t\t\t\t\t\tshorterValue: arrtmp[0],\r\n\t\t\t\t\t\t\t\t\t\tlongerIndex: this.topArr.indexOf(arrtmp[this.cols - 1]),\r\n\t\t\t\t\t\t\t\t\t\tlongerValue: arrtmp[this.cols - 1],\r\n\t\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\t\t\tshorterIndex,\r\n\t\t\t\t\t\t\t\t\tshorterValue\r\n\t\t\t\t\t\t\t\t} = getTopArrMsg();\r\n\t\t\t\t\t\t\t\tconst position = {\r\n\t\t\t\t\t\t\t\t\ttop: shorterValue + \"px\",\r\n\t\t\t\t\t\t\t\t\tleft: (data.width + this.offset) * shorterIndex + \"px\",\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\tthis.$set(this.allPositionArr, i, position);\r\n\t\t\t\t\t\t\t\tthis.topArr[shorterIndex] =\r\n\t\t\t\t\t\t\t\t\tshorterValue + this.allHeightArr[i] + this.offset;\r\n\t\t\t\t\t\t\t\tthis.height = getTopArrMsg().longerValue - this.offset;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.oldNum = this.num;\r\n\t\t\t\t\t\t\tthis.$emit(\"image-load\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec();\r\n\t\t\t},\r\n\t\t\trefresh() {\r\n\t\t\t\tlet arr = [];\r\n\t\t\t\tfor (let i = 0; i < this.cols; i++) {\r\n\t\t\t\t\tarr.push(0);\r\n\t\t\t\t}\r\n\t\t\t\tthis.topArr = arr;\r\n\t\t\t\tthis.num = 0;\r\n\t\t\t\tthis.oldNum = 0;\r\n\t\t\t\tthis.height = 0;\r\n\t\t\t},\r\n\t\t\ttoDetail:function(key){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar item = that.list[key];\r\n\t\t\t\tvar id = item[that.idfield];\r\n\t\t\t\tvar url = '/pages/shop/product?id='+id;//默认链接\r\n\t\t\t\t//来自商品柜\r\n\t\t\t\tif(item.device_id){\r\n\t\t\t\t\tvar dgid = item.id;\r\n\t\t\t\t\tvar deviceno = item.device_no;\r\n\t\t\t\t\tvar lane = item.goods_lane;\r\n\t\t\t\t\tvar prodata  = id+','+item.ggid+','+item.stock;\r\n\t\t\t\t\tvar devicedata = deviceno+','+lane;\r\n\t\t\t\t\turl = url+'&dgprodata='+prodata+'&devicedata='+devicedata;\r\n\t\t\t\t}\r\n\t\t\t\tapp.goto(url);\r\n\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n<style scoped>\r\n\t.waterfalls-box {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.waterfalls-box .waterfalls-list {\r\n\t\twidth: calc((100% - var(--offset) * (var(--cols) - 1)) / var(--cols));\r\n\t\tposition: absolute;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tleft: calc(-50% - var(--offset));\r\n\t}\r\n\r\n\t.waterfalls-box .waterfalls-list .waterfalls-list-image {\r\n\t\twidth: 100%;\r\n\t\twill-change: transform;\r\n\t\tborder-radius: 8rpx 8rpx 0 0;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\r\n\t.waterfalls-box .saleimg {\r\n\t\tposition: absolute;\r\n\t\twidth: 60px;\r\n\t\theight: auto;\r\n\t\ttop: 0;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info {\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p1 {\r\n\t\tcolor: #323232;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\toverflow: hidden;\r\n\t\tpadding: 2px 0\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p2-1 {\r\n\t\tflex-grow: 1;\r\n\t\tflex-shrink: 1;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p2-1 .t1 {\r\n\t\tfont-size: 36rpx;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p2-1 .t2 {\r\n\t\tmargin-left: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #aaa;\r\n\t\ttext-decoration: line-through;\r\n\t\t/*letter-spacing:-1px*/\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p2-1 .t3 {\r\n\t\tmargin-left: 10rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p2-2 {\r\n\t\tfont-size: 20rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\ttext-align: right;\r\n\t\tpadding-left: 20rpx;\r\n\t\tcolor: #999\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p3 {\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 20rpx;\r\n\t\tmargin-top: 10rpx\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p4 {\r\n\t\twidth: 52rpx;\r\n\t\theight: 52rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: absolute;\r\n\t\tdisplay: relative;\r\n\t\tbottom: 16rpx;\r\n\t\tright: 20rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p4 .icon_gouwuche {\r\n\t\tfont-size: 30rpx;\r\n\t\theight: 52rpx;\r\n\t\tline-height: 52rpx\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .p4 .img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\t.waterfalls-box .product-info .p2 .t1-m {font-size: 32rpx;padding-left: 8rpx;}\r\n\r\n\t.waterfalls-box .product-info .binfo {\r\n\t\tpadding: 6rpx 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .binfo .t1 {\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.waterfalls-box .product-info .binfo .t2 {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: normal;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.waterfalls-box .couponitem {\r\n\t\twidth: 100%;\r\n\t\t/* padding: 0 20rpx 20rpx 20rpx; */\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.waterfalls-box .couponitem .f1 {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.waterfalls-box .couponitem .f1 .t {\r\n\t\tmargin-right: 10rpx;\r\n\t\tborder-radius: 3px;\r\n\t\tfont-size: 22rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.bg-desc {\r\n\t\tcolor: #fff;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t}\r\n\r\n\t.lianxi {\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50rpx 50rpx;\r\n\t\tline-height: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 22rpx;\r\n\t\tpadding: 0 14rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tfloat: right;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-waterfall.vue?vue&type=style&index=0&id=2dd26264&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-waterfall.vue?vue&type=style&index=0&id=2dd26264&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839380596\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}