{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-pifa2/buydialog-pifa2.vue?6b66", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-pifa2/buydialog-pifa2.vue?309b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-pifa2/buydialog-pifa2.vue?cd5d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-pifa2/buydialog-pifa2.vue?d5a1", "uni-app:///components/buydialog-pifa2/buydialog-pifa2.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-pifa2/buydialog-pifa2.vue?84ed", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-pifa2/buydialog-pifa2.vue?694b"], "names": ["data", "ks", "product", "gui<PERSON>", "gui<PERSON><PERSON>", "ggselected", "nowguige", "<PERSON><PERSON><PERSON><PERSON>", "jlprice", "jltitle", "gwcnum", "isload", "loading", "canaddcart", "shopset", "glassrecord", "showglass", "totalprice", "jlselected", "hasglassrecord", "grid", "jietidiscountArr", "guiged<PERSON>last", "jietiDiscountType", "total_quantity", "total_price", "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pre_url", "props", "btntype", "default", "menuindex", "controller", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proid", "mounted", "uni", "that", "<PERSON><PERSON><PERSON><PERSON>", "methods", "ProductQuantity", "prodataArr", "priceArr", "getdata", "app", "id", "Object", "item", "thisKeysArr", "guigelastArr", "buydialogChange", "getglassrecord", "pagenum", "listrow", "showLinkChange", "ggchange", "deleggselected", "item2", "jlchange", "title", "jlselect", "tobuy", "prodataIdArr", "addcart", "glass_record_id", "prodata", "num", "ggid", "gwcplus", "g<PERSON><PERSON><PERSON><PERSON>", "gwcminus", "gwcinput"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5XA;AAAA;AAAA;AAAA;AAA60B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoOj2B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;EACA;EACAC;IACA;IACAC;MACAC;IACA;IACAA;EACA;EACAC;IACAF;EACA;EACAG;IACAC;MACA;MACA;QAAA;MAAA;MACA;QACA;UAAA;QAAA;QACAH;QACA;QACAI;UACAC;QACA;QACAL;UAAA;QAAA;MACA;QACAA;QACAA;MACA;IACA;IACAM;MACA;MACAN;MACAO;QAAAC;MAAA;QACAR;QACA;UACAO;UACA;QACA;QACAP;QACA;UACAA;QACA;UACAA;QACA;;QACAA;QACAA;QACA;UACAA;QACA;QACAA;QACAS;UAAA;QAAA;QACAT;QACA;UACA;UACA;YACAA;cAAA;YAAA;YACAA;cACA;gBACAU;cACA;YACA;UACA;UACA;QACA;UACAV;UACAA;YAAA;UAAA;UACA;QACA;;QACA;QACA;QACA;UACAhC;QACA;QACAgC;QACA;QACAA;QACAA;QACAA;QACA;QACA;UACA;YAEAA;cACAU;cACAA;cACAA;YACA;;YAEA;UACA;YACA;YACA;YACAC;cACA;gBACAC;cACA;YACA;YACAA;cACAZ;cACAA;cACAA;cACAA;cACAA;YACA;UACA;QACA;QACA,mCACAA,6CAEAA;QACAA;QACA;UAAA;UACAA;QACA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;MACA;MACA;QACAP;UAAAQ;UAAAC;UAAAR;QAAA;UACA;UACA;YACAR;YACA;cACAA;cACA;gBACA;kBACAA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAiB;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACAlD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAmD;MACA;MACA;MACAR;QACA;UACAC;QACA;MACA;MACAA;QACA;UACA;YACAQ;UACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MACA;QACA;QACA;QACAT;UACA;YACAC;UACA;QACA;QACAA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MACA;IACA;IACAS;MACA;MACA;MACA;MACA;MACA;QACA;UACAlD;UACAmD;UACAC;QACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAA;MAAA;MACA;MACA;MACA;QAAA;MAAA;MACA;QACA;UACA;YACA;cACAjB;YACA;YACA;UACA;QACA;UACA;YACA;UACA;QACA;MACA;QACA;UAAA;QAAA;QACA;MACA;MACA;MACA;QACAkB;MACA;MACA;MACA;QACAlB;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;IACA;IACA;IACAmB;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QAAA;MAAA;MACA;MACA;MACA;QAAA;MAAA;MACA;QACA;UACA;YACA;cACApB;YACA;YACA;UACA;QACA;UACA;YACA;UACA;QACA;MACA;QACA;UAAA;QAAA;QACA;MACA;MACA;MACA;QACAkB;MACA;MACA;QACAlB;UAAAqB;UAAAC;UAAAF;QAAA;UACA;YACApB;YACAP;cAAAH;cAAAiC;cAAAD;cAAA1D;cAAAC;YAAA;YACA4B;UACA;YACAO;UACA;QACA;MACA;QACAA;UAAAqB;UAAAC;UAAAF;QAAA;UACA;YACApB;YACAP;YACAA;UACA;YACAO;UACA;QACA;MACA;IACA;IACA;IACAwB;MACA;MACA;MACA;QACA/D;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACA;QACAuC;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QACA;QACA;QACAyB;QACA;MACA;QACA;QACA;QACAA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAjE;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;cACAuC;YACA;YACA;UACA;QACA;UACA;YACA;cACAA;YACA;YACA;UACA;QACA;MACA;MACA;MACA;QACA;QACA;QACAyB;QACA;MACA;QACA;QACA;QACAA;QACA;MACA;MACA;IACA;IACA;IACAE;MACA;MACA;QACAlE;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UAAA;QAAA;UAAA;QAAA;QACA;QACA;QACAgE;QACA;QACA;MACA;MACA;MACA;QACA;UACA;YACA;cACAzB;YACA;YACAlC;UACA;QACA;UACA;YACA;cACAkC;YACA;YACAlC;UACA;QACA;MACA;MACA;QACAkC;QACAlC;MACA;MACA;MACA;QACA;QACA;QACA2D;QACA;MACA;QACA;QACA;QACAA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChuBA;AAAA;AAAA;AAAA;AAAktC,CAAgB,koCAAG,EAAC,C;;;;;;;;;;;ACAtuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/buydialog-pifa2/buydialog-pifa2.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./buydialog-pifa2.vue?vue&type=template&id=39dc7604&scoped=true&\"\nvar renderjs\nimport script from \"./buydialog-pifa2.vue?vue&type=script&lang=js&\"\nexport * from \"./buydialog-pifa2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buydialog-pifa2.vue?vue&type=style&index=0&id=39dc7604&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"39dc7604\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/buydialog-pifa2/buydialog-pifa2.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-pifa2.vue?vue&type=template&id=39dc7604&scoped=true&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    (_vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\")\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    ((_vm.shopset &&\n      (_vm.shopset.price_show_type == \"0\" || !_vm.shopset.price_show_type)) ||\n      !_vm.shopset) &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    ((_vm.shopset &&\n      (_vm.shopset.price_show_type == \"0\" || !_vm.shopset.price_show_type)) ||\n      !_vm.shopset) &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? Number(_vm.nowguige.market_price)\n      : null\n  var m3 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    ((_vm.shopset &&\n      (_vm.shopset.price_show_type == \"0\" || !_vm.shopset.price_show_type)) ||\n      !_vm.shopset) &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? Number(_vm.nowguige.sell_price)\n      : null\n  var m4 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"0\" &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"0\" &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? Number(_vm.nowguige.market_price)\n      : null\n  var m6 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"0\" &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? Number(_vm.nowguige.sell_price)\n      : null\n  var m7 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.shopset.price_show_type == \"2\" &&\n    _vm.product.lvprice == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m8 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.shopset.price_show_type == \"2\" &&\n    _vm.product.lvprice == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.shopset.price_show_type == \"2\" &&\n    _vm.product.lvprice == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m11 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m12 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m13 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.shopset &&\n    (_vm.shopset.price_show_type == \"1\" ||\n      _vm.shopset.price_show_type == \"2\") &&\n    _vm.product.is_vip == \"1\" &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload && _vm.product.product_type == 4 ? _vm.t(\"color1rgb\") : null\n  var m15 = _vm.isload && _vm.product.product_type == 4 ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.jietidiscountArr.length : null\n  var l0 =\n    _vm.isload && g0 && _vm.nowguige.sell_price != \"请先登录\"\n      ? _vm.__map(_vm.jietidiscountArr, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 = (item.ratio * _vm.nowguige.sell_price * 0.01).toFixed(2)\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var m16 = _vm.isload && _vm.showglass ? _vm.t(\"color1rgb\") : null\n  var m17 = _vm.isload && _vm.nowguige.balance_price ? _vm.t(\"color1\") : null\n  var g2 = _vm.isload ? _vm.guigedata.length : null\n  var l1 =\n    _vm.isload && g2 < 2\n      ? _vm.__map(_vm.guigedata, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m18 = _vm.product.product_type == 6 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m18: m18,\n          }\n        })\n      : null\n  var l2 =\n    _vm.isload && !(g2 < 2)\n      ? _vm.__map(_vm.guigedata, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g3 = _vm.guigedata.length\n          return {\n            $orig: $orig,\n            g3: g3,\n          }\n        })\n      : null\n  var l3 =\n    _vm.isload && !(g2 < 2)\n      ? _vm.__map(_vm.guigedatalast, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m19 = _vm.product.product_type == 6 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m19: m19,\n          }\n        })\n      : null\n  var g4 = _vm.isload\n    ? _vm.jialiaodata.length > 0 &&\n      (_vm.controller == \"ApiRestaurantShop\" ||\n        _vm.controller == \"ApiRestaurantTakeaway\")\n    : null\n  var m20 =\n    _vm.isload &&\n    _vm.product.product_type == 6 &&\n    _vm.nowguige.sell_price != \"请先登录\"\n      ? _vm.t(\"color1\")\n      : null\n  var m21 =\n    _vm.isload &&\n    _vm.product.product_type == 6 &&\n    !(_vm.nowguige.sell_price != \"请先登录\")\n      ? _vm.t(\"color1\")\n      : null\n  var m22 = _vm.isload && _vm.product.price_type == 1 ? _vm.t(\"color2\") : null\n  var m23 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    _vm.shopset &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.nowguige.commission > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m24 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    _vm.shopset &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.nowguige.commission > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m25 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      (_vm.nowguige.stock <= 0 && !_vm.product.yuding_stock) ||\n      (_vm.product.yuding_stock &&\n        _vm.nowguige.stock <= 0 &&\n        _vm.product.yuding_stock <= 0)\n    ) &&\n    _vm.btntype == 0 &&\n    _vm.canaddcart\n      ? _vm.t(\"color2\")\n      : null\n  var m26 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      (_vm.nowguige.stock <= 0 && !_vm.product.yuding_stock) ||\n      (_vm.product.yuding_stock &&\n        _vm.nowguige.stock <= 0 &&\n        _vm.product.yuding_stock <= 0)\n    ) &&\n    _vm.btntype == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m27 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      (_vm.nowguige.stock <= 0 && !_vm.product.yuding_stock) ||\n      (_vm.product.yuding_stock &&\n        _vm.nowguige.stock <= 0 &&\n        _vm.product.yuding_stock <= 0)\n    ) &&\n    _vm.btntype == 1\n      ? _vm.t(\"color2\")\n      : null\n  var m28 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      (_vm.nowguige.stock <= 0 && !_vm.product.yuding_stock) ||\n      (_vm.product.yuding_stock &&\n        _vm.nowguige.stock <= 0 &&\n        _vm.product.yuding_stock <= 0)\n    ) &&\n    _vm.btntype == 2\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        g0: g0,\n        l0: l0,\n        m16: m16,\n        m17: m17,\n        g2: g2,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        g4: g4,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-pifa2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-pifa2.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<view v-if=\"isload\">\r\n\t\t<view class=\"buydialog-mask\" @tap=\"buydialogChange\" @touchmove.stop.prevent=\" \"></view>\r\n\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t<scroll-view scroll-y style=\"height: auto;max-height: 70vh;\">\r\n\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"image\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"title flex\">\r\n\t\t\t\t<image :src=\"nowguige.pic || product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"nowguige.pic || product.pic\" mode=\"aspectFill\"/>\r\n\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t<view v-if=\"controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway'\" >\r\n\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\" >￥{{totalprice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<view v-if=\"(shopset && (shopset.price_show_type =='0' || !shopset.price_show_type)) || !shopset\" >\r\n\t\t\t\t\t\t\t <view  class=\"price\" :style=\"{color:t('color1')}\" v-if=\"product.price_type != 1 || nowguige.sell_price > 0\"  >\r\n\t\t\t\t\t\t\t\t<block v-if=\"product.price_dollar && nowguige.usdsell_price>0\">\t\r\n\t\t\t\t\t\t\t\t<text style=\"margin-right: 10rpx;\">${{nowguige.usdsell_price}}</text></block>\r\n\t\t\t\t\t\t\t\t￥{{nowguige.sell_price}}\r\n\t\t\t\t\t\t\t\t<text v-if=\"Number(nowguige.market_price) > Number(nowguige.sell_price)\" class=\"t2\">￥{{nowguige.market_price}}</text>\r\n\t\t\t\t\t\t\t </view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"shopset && (shopset.price_show_type =='1' ||shopset.price_show_type =='2') \">\r\n\t\t\t\t\t\t\t<view v-if=\"product.is_vip=='0' \">\r\n\t\t\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\" v-if=\"product.price_type != 1 || nowguige.sell_price > 0\" >\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"product.price_dollar && nowguige.usdsell_price>0\">\t\r\n\t\t\t\t\t\t\t\t\t<text style=\"margin-right: 10rpx;\">${{nowguige.usdsell_price}}</text></block>\r\n\t\t\t\t\t\t\t\t\t￥{{nowguige.sell_price}}\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"Number(nowguige.market_price) > Number(nowguige.sell_price)\" class=\"t2\">￥{{nowguige.market_price}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"member flex\" v-if=\"shopset.price_show_type=='2' &&  product.lvprice ==1 \">\r\n\t\t\t\t\t\t\t\t\t<view class=\"member_module flex\" :style=\"'border-color:' + t('color1')\">\r\n\t\t\t\t\t\t\t\t\t\t<view :style=\"{background:t('color1')}\" class=\"member_lable flex-y-center\">{{nowguige.level_name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view :style=\"'color:' + t('color1')\" class=\"member_value\">\r\n\t\t\t\t\t\t\t\t\t\t\t￥<text>{{nowguige.sell_price_origin}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"product.is_vip=='1'\">\r\n\t\t\t\t\t\t\t\t<view class=\"member flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"member_module flex\" :style=\"'border-color:' + t('color1')\">\r\n\t\t\t\t\t\t\t\t\t\t<view :style=\"{background:t('color1')}\" class=\"member_lable flex-y-center\">{{nowguige.level_name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view :style=\"'color:' + t('color1')\" class=\"member_value\" style=\"font-size: 36rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t￥<text>{{nowguige.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\" v-if=\"product.price_type != 1 || nowguige.sell_price > 0\" >\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"product.price_dollar && nowguige.usdsell_price>0\">\t\r\n\t\t\t\t\t\t\t\t\t<text style=\"margin-right: 10rpx;\">${{nowguige.usdsell_price}}</text></block>\r\n\t\t\t\t\t\t\t\t\t<text :style=\"product.lvprice =='1'?'font-size:30rpx;':'font-size:36rpx;'\">\r\n\t\t\t\t\t\t\t\t\t\t￥{{nowguige.sell_price_origin}}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"choosename\" v-if=\"product.limit_start > 1\"> {{product.limit_start}}件起售</text>\r\n\t\t\t\t\t<view class=\"stock\" v-if=\"!shopset || shopset.hide_stock!=1\">库存：{{nowguige.stock}}</view>\r\n\t\t\t\t\t<view class=\"choosename\" v-if=\"product.limit_start<=1\">已选规格: {{nowguige.name}}{{jltitle}}</view>\r\n\t\t\t\t\t<view class=\"pifa-tag\" v-if=\"product.product_type==4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">{{ jietiDiscountType ? '整批':'混批'}}</view>\r\n          <view class=\"choosename\" v-if=\"product.product_type==3\">手工费: ￥{{nowguige.hand_fee?nowguige.hand_fee:0}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--产品描述-->\r\n\t\t\t<view style=\"max-height:50vh;overflow:scroll\" v-if=\"product.sellpoint && product.product_type != 6\">\r\n\t\t\t\t<view   class=\"guigelist flex-col\">\r\n\t\t\t\t\t<view class=\"name\">产品描述</view>\r\n\t\t\t\t\t<view  class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"description\">{{product.sellpoint}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 价格区间 -->\r\n\t\t\t<view style=\"max-height:50vh;overflow:scroll;\" v-if=\"jietidiscountArr.length\">\r\n\t\t\t\t<view class=\"guigelist flex-col\" style=\"padding-bottom: 0rpx;\">\r\n\t\t\t\t\t<view class=\"name\">阶梯价格</view>\r\n\t\t\t\t\t<view class=\"name\" v-if=\"nowguige.sell_price == '请先登录'\">{{nowguige.sell_price}}</view>\r\n\t\t\t\t\t<view class=\"pricerange-view flex-y-center\" v-if=\"nowguige.sell_price != '请先登录'\">\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap;\">\r\n\t\t\t\t\t\t\t<view class=\"price-range flex-col\" v-for=\"(item,index) in jietidiscountArr\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"price-text flex-y-center\"><text style=\"font-size: 24rpx;\">￥</text>{{(item.ratio*nowguige.sell_price*0.01).toFixed(2)}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"range-text\" v-if=\"item.end_num\">{{item.start_num}} - {{item.end_num}}{{product.product_unit ? product.product_unit :''}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"range-text\" v-else> >{{item.start_num}}{{product.product_unit ? product.product_unit :''}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\t\r\n\t\t\t<block v-if=\"showglass\">\r\n\t\t\t\t<view class=\"glassinfo\" @tap=\"goto\" :data-url=\"'/pagesExt/glass/index?c=1'\" :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\">\r\n\t\t\t\t\t<view class=\"g-title\">\r\n\t\t\t\t\t\t视力档案\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex flex-e\">\r\n\t\t\t\t\t\t<text>{{glassrecord.id>0?glassrecord.name:'请选择'}}</text>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<view v-if=\"nowguige.balance_price\" style=\"width:94%;margin:10rpx 3%;font-size:24rpx;\" :style=\"{color:t('color1')}\">首付款金额：{{nowguige.advance_price}}元，尾款金额：{{nowguige.balance_price}}元</view>\r\n\t\t\t\r\n\t\t\t<block v-if=\"guigedata.length < 2\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t<view class=\"name flex-bt\" v-if=\"product.product_type == 6\">\r\n\t\t\t\t\t\t\t<view>{{item.title}}</view>\r\n\t\t\t\t\t\t\t<view>单价</view>\r\n\t\t\t\t\t\t\t<view>数量</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name\" v-else>{{item.title}}</view>\r\n\t\t\t\t\t\t<view v-for=\"(item2, index2) in item.items\" :key=\"index2\" class=\"buynum flex flex-y-center\" :style=\"product.product_type == 6 ? 'border-top:1px rgba(0,0,0,0.1) solid;padding-top:15rpx':''\">\r\n\t\t\t\t\t\t\t<view class=\"buynumleft-view\">\r\n\t\t\t\t\t\t\t\t<view class=\"image-view\" v-if='item2.pic'>\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.pic\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='ggname-text'>{{item2.title}}</view>\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"ggprice-prounits flex-col\" v-if=\"product.product_type == 6\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<view class=\"gg-price\">￥{{item2.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"gg-prounits\">{{item2.prounits}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"stock-addnum flex-col\">\r\n\t\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus(item2,index2)\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\"/></view>\r\n\t\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"item2.num\" @input=\"gwcinput($event,item2,index2)\"></input>\r\n\t\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus(item2,index2)\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"stock-text-class\" v-if=\"product.product_type == 6\">库存：{{item2.stock}} {{item2.prounit ? item2.prounit :''}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<view style=\"max-height:50vh;overflow:scroll\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\" v-if=\"index != guigedata.length-1\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"max-height:50vh;overflow:scroll;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in guigedatalast\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t<view class=\"name flex-bt\" v-if=\"product.product_type == 6\">\r\n\t\t\t\t\t\t\t<view>{{item.title}}</view>\r\n\t\t\t\t\t\t\t<view>单价</view>\r\n\t\t\t\t\t\t\t<view>数量</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name\" v-else>{{item.title}}</view>\r\n\t\t\t\t\t\t<view v-for=\"(item2, index2) in item.items\" :key=\"index2\" class=\"buynum flex flex-y-center\"  :style=\"product.product_type == 6 ? 'border-top:1px rgba(0,0,0,0.1) solid;padding-top:15rpx':''\">\r\n\t\t\t\t\t\t\t<view class=\"buynumleft-view\">\r\n\t\t\t\t\t<!-- \t\t\t<view class=\"image-view\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.pic\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t<view class='ggname-text'>{{item2.title}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"ggprice-prounits flex-col\" v-if=\"product.product_type == 6\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<view class=\"gg-price\">￥{{item2.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"gg-prounits\">{{item2.prounits}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"stock-addnum flex-col\">\r\n\t\t\t\t\t\t\t\t<view class=\"addnum\" :class=\"product.product_type == '6' ? 'addnumtype6':''\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus(item2,index2,item.k)\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\"/></view>\r\n\t\t\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"item2.num\" @input=\"gwcinput($event,item2,index2,item.k)\"></input>\r\n\t\t\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus(item2,index2,item.k)\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"stock-text-class\" v-if=\"product.product_type == 6\">库存：{{item2.stock}} {{item2.prounit ? item2.prounit :''}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<!--加料-->\r\n\t\t\t<view style=\"max-height:50vh;overflow:scroll\" v-if=\"jialiaodata.length > 0 && (controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway')\">\r\n\t\t\t\t<view   class=\"guigelist flex-col\">\r\n\t\t\t\t\t<view class=\"name\">加料</view>\r\n\t\t\t\t\t<view  class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view v-for=\"(jlitem, jlindex) in jialiaodata\" :key=\"jlindex\"  class=\"item2\" :class=\"jlitem.active?'on':''\" @click=\"jlchange(jlindex)\">{{jlitem.jltitle}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t\t<view class=\"bottom-but\">\r\n\t\t\t\t<view class=\"total-view flex-y-center\" v-if=\"product.product_type == 6\">\r\n\t\t\t\t\t<view>已选 {{total_quantity}} 种</view>\r\n\t\t\t\t\t<view class=\"flex-y-center\">商品金额：<view class=\"price\" :style=\"{color:t('color1')}\" v-if=\"nowguige.sell_price != '请先登录'\">￥{{total_price}}</view>\r\n\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\" v-else>请先登录</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"product.price_type == 1\">\r\n\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"showLinkChange\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</button>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"tips-text\" :style=\"{color:t('color1')}\" v-if=\"shopset && shopset.showcommission==1 && nowguige.commission > 0\">分享好友购买预计可得{{t('佣金')}}：\r\n\t\t\t\t\t\t<block v-if=\"nowguige.commission > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{nowguige.commission}}</text>{{nowguige.commission_desc}}</block>\r\n\t\t\t\t\t\t<block v-if=\"nowguige.commission > 0 && nowguige.commissionScore > 0\">+</block>\r\n\t\t\t\t\t\t<block v-if=\"nowguige.commissionScore > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{nowguige.commissionScore}}</text>{{nowguige.commission_desc_score}}</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<block v-if=\"(nowguige.stock <= 0  && !product.yuding_stock) ||( product.yuding_stock && nowguige.stock <= 0 && product.yuding_stock <= 0 )  \">\r\n\t\t\t\t\t\t\t<button class=\"nostock\">库存不足</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"addcart\" v-if=\"btntype==0 && canaddcart\">加入购物车</button>\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==0\">立即购买</button>\r\n\t\t\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"addcart\" v-if=\"btntype==1\">确 定</button>\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==2\">确 定</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tks:'',\r\n\t\t\t\tproduct:{},\r\n\t\t\t\tguigelist:{},\r\n\t\t\t\tguigedata:{},\r\n\t\t\t\tggselected:{},\r\n\t\t\t\tnowguige:{},\r\n\t\t\t\tjialiaodata:[],\r\n\t\t\t\tjlprice:0,\r\n\t\t\t\tjltitle:'',\r\n\t\t\t\tgwcnum:1,\r\n\t\t\t\tisload:false,\r\n\t\t\t\tloading:false,\r\n\t\t\t\tcanaddcart:true,\r\n\t\t\t\tshopset:{},\r\n\t\t\t\tglassrecord:{},\r\n\t\t\t\tshowglass:false,\r\n\t\t\t\ttotalprice:0,\r\n\t\t\t\tjlselected:[],\r\n\t\t\t\thasglassrecord:0,\r\n\t\t\t\tgrid:0,\r\n\t\t\t\tjietidiscountArr:[],\r\n\t\t\t\tguigedatalast:[],\r\n\t\t\t\tjietiDiscountType:'',\r\n\t\t\t\ttotal_quantity:1,\r\n\t\t\t\ttotal_price:\"\",\r\n\t\t\t\tguigeProunit:'',\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tbtntype:{default:0},\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tcontroller:{default:'ApiShop'},\r\n\t\t\tneedaddcart:{default:true},\r\n\t\t\tproid:{}\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.$on('getglassrecord', function(data) {\r\n\t\t\t\t that.getglassrecord()\r\n\t\t\t});\r\n\t\t\tthat.getdata();\r\n\t\t},\r\n\t\tbeforeDestroy(){\r\n\t\t\tuni.$off('getglassrecord')\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tProductQuantity(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet prodataArr = Object.values(this.guigelist).filter(item => item.num > 0);\r\n\t\t\t\tif(prodataArr.length){\r\n\t\t\t\t\tlet num = prodataArr.reduce((acc,curr) => acc + curr.num,0)\r\n\t\t\t\t\tthat.total_quantity = num;\r\n\t\t\t\t\tlet priceArr = [];\r\n\t\t\t\t\tprodataArr.forEach((item,index) => {\r\n\t\t\t\t\t\t\tpriceArr[index] = item.num*item.sell_price*1;\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.total_price = priceArr.reduce((total,current) => total+current).toFixed(2);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.total_quantity = 0;\r\n\t\t\t\t\tthat.total_price = 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(this.controller+'/getproductdetail',{id:that.proid},function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status != 1){\r\n\t\t\t\t\t\tapp.alert(res.msg)\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\tif(that.product.jieti_discount_data){\r\n\t\t\t\t\t\tthat.jietidiscountArr = JSON.parse(that.product.jieti_discount_data); // 价格区间优惠\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.jietidiscountArr = []; // 价格区间优惠\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.jietiDiscountType = that.product.jieti_discount_type; //阶梯优惠价格类型 0-混 1-整\r\n\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\tif(!that.product.limit_start){\r\n\t\t\t\t\t\tthat.product.limit_start = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\t\tObject.values(that.guigelist).map(item => item.num = 0);//向规格list 加入数量初始值\r\n\t\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\t\tif(that.guigedata.length < 2){\r\n\t\t\t\t\t\t// 判断商品几个规格-向规格内添加商品图片\r\n\t\t\t\t\t\tif(that.guigedata.length == 1){\r\n\t\t\t\t\t\t\tthat.guigedata[0].items.map(item => item.num = 0); //向2规格加入初始值\r\n\t\t\t\t\t\t\tthat.guigedata[0].items.forEach((item,index) => {\r\n\t\t\t\t\t\t\t\tif(item.k == Object.values(that.guigelist)[index].ks){\r\n\t\t\t\t\t\t\t\t\titem.pic = Object.values(that.guigelist)[index].pic;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// that.guigedata[0].items[0].num = 1; //默认选中第一个规格\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.guigedatalast.push(that.guigedata[that.guigedata.length - 1]);\r\n\t\t\t\t\t\tthat.guigedatalast[0].items.map(item => item.num = 0);\r\n\t\t\t\t\t\t// that.guigedata[that.guigedata.length-1].items[0].num = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar guigedata = res.guigedata;\r\n\t\t\t\t\tvar ggselected = [];\r\n\t\t\t\t\tfor (var i = 0; i < guigedata.length; i++) {\r\n\t\t\t\t\t\tggselected.push(0);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.ks = ggselected.join(','); \r\n\t\t\t\t\t// that.guigelist[that.ks].num = that.guigedata[that.guigedata.length-1].items[0].num;\r\n\t\t\t\t\tthat.nowguige = that.guigelist[that.ks];\r\n\t\t\t\t\tthat.ProductQuantity(); //默认第一次计算价格\r\n\t\t\t\t\tthat.ggselected = ggselected;\r\n\t\t\t\t\t// 判断此商品类型\r\n\t\t\t\t\tif(that.product.product_type == 6){\r\n\t\t\t\t\t\tif(that.guigedata.length == 1){\r\n\r\n\t\t\t\t\t\t\t\tthat.guigedata[0].items.forEach((item,index) => {\r\n\t\t\t\t\t\t\t\t\titem.prounits = Object.values(that.guigelist)[index].prounits ? Object.values(that.guigelist)[index].prounits : '';\r\n\t\t\t\t\t\t\t\t\titem.sell_price = Object.values(that.guigelist)[index].sell_price ? Object.values(that.guigelist)[index].sell_price : '';\r\n\t\t\t\t\t\t\t\t\titem.stock = Object.values(that.guigelist)[index].stock ? Object.values(that.guigelist)[index].stock : '';\r\n\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t// that.guigedata[0].items[0].num = 1; //默认选中第一个规格\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tlet thisKeysArr = Object.keys(that.guigelist);\r\n\t\t\t\t\t\t\tlet guigelastArr = [];\r\n\t\t\t\t\t\t\tthisKeysArr.forEach((item,index) => {\r\n\t\t\t\t\t\t\t\tif(item.substr(0, item.length - 1) == that.ks.substr(0, that.ks.length - 1)){\r\n\t\t\t\t\t\t\t\t\tguigelastArr.push(Object.values(that.guigelist)[index])\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tguigelastArr.forEach((item,index) => {\r\n\t\t\t\t\t\t\t\tthat.guigedatalast[0].items[index].prounits = item.prounits ? item.prounits : '';\r\n\t\t\t\t\t\t\t\tthat.guigedatalast[0].items[index].sell_price = item.sell_price ? item.sell_price : '';\r\n\t\t\t\t\t\t\t\tthat.guigedatalast[0].items[index].stock = item.stock ? item.stock : '';\r\n\t\t\t\t\t\t\t\tthat.guigedatalast[0].items[index].prounit = item.prounit ? item.prounit : '';\r\n\t\t\t\t\t\t\t\tthat.guigeProunit = item.prounit ? item.prounit : '';\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.nowguige.limit_start > 0)\r\n\t\t\t\t\t\tthat.gwcnum = that.nowguige.limit_start;\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\tthat.gwcnum = that.product.limit_start;\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\r\n\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//是否是眼睛产品\r\n\t\t\t\t\tif(that.product.product_type==1){\r\n\t\t\t\t\t\tthat.showglass = true\r\n\t\t\t\t\t\tthat.getglassrecord()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.controller =='ApiRestaurantShop' ||that.controller =='ApiRestaurantTakeaway'){\r\n\t\t\t\t\t\tthat.jialiaodata = res.jialiaodata;\r\n\t\t\t\t\t\tthat.totalprice = that.nowguige.sell_price;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tbuydialogChange:function(){\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t},\r\n\t\t\tgetglassrecord:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar grid = app.getCache('_glass_record_id');\r\n\t\t\t\tif(that.showglass===true && (!that.glassrecord || (that.glassrecord && that.glassrecord.id!=grid))){\r\n\t\t\t\t\tapp.post('ApiGlass/myrecord', {pagenum:1,listrow:1,id:grid}, function (resG) {\r\n\t\t\t\t\t\tvar datalist = resG.data;\r\n\t\t\t\t\t\tif(datalist.length>0){\r\n\t\t\t\t\t\t\tthat.hasglassrecord = 1;\r\n\t\t\t\t\t\t\tif(grid>0){\r\n\t\t\t\t\t\t\t\tthat.grid = grid\r\n\t\t\t\t\t\t\t\tfor(let i in datalist){\r\n\t\t\t\t\t\t\t\t\tif(datalist[i].id==grid){\r\n\t\t\t\t\t\t\t\t\t\tthat.glassrecord = datalist[i]\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowLinkChange:function () {\r\n\t\t\t\tthis.$emit('showLinkChange');\r\n\t\t\t},\r\n\t\t\t//选择规格\r\n\t\t\tggchange: function (e){\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar itemk = e.currentTarget.dataset.itemk;\r\n\t\t\t\tvar ggselected = this.ggselected;\r\n\t\t\t\tggselected[itemk] = idx;\r\n\t\t\t\tvar ks = ggselected.join(',');\r\n\t\t\t\tthis.ggselected = ggselected;\r\n\t\t\t\tthis.ks = ks;\r\n\t\t\t\t// 切换规格 清空初始值\r\n\t\t\t\t// Object.values(this.guigelist).map(item => item.num = 0);\r\n\t\t\t\t// this.guigedatalast[0].items.map(item => item.num = 0);\r\n\t\t\t\t// 切换规格 保留初始值\r\n\t\t\t\tlet deleggselected = JSON.parse(JSON.stringify(this.ggselected));\r\n\t\t\t\tdeleggselected.pop();\r\n\t\t\t\tlet thisKeysArr = Object.keys(this.guigelist);\r\n\t\t\t\tlet guigelastArr = [];\r\n\t\t\t\tthisKeysArr.forEach((item,index) => {\r\n\t\t\t\t\tif(item.slice(0, item.length - 2) == deleggselected.join(',')){\r\n\t\t\t\t\t\tguigelastArr.push(item)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tguigelastArr.forEach((itemm,index) => {\r\n\t\t\t\t\tthis.guigedatalast[0].items.forEach((item2,index2) => {\r\n\t\t\t\t\t\tif(this.guigelist[itemm].ks.split(',')[this.guigedata.length-1] == item2.k){\r\n\t\t\t\t\t\t\titem2.num = this.guigelist[itemm].num \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\t\tif(itemk == 0){\r\n\t\t\t\t\tthis.nowguige.pic = this.guigedata[0].items[idx].ggpic_wholesale;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (this.gwcnum < this.nowguige.limit_start) {\r\n\t\t\t\t\t\tthis.gwcnum = this.nowguige.limit_start;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 判断此商品类型\r\n\t\t\t\tif(this.product.product_type == 6){\r\n\t\t\t\t\tlet thisKeysArr = Object.keys(this.guigelist);\r\n\t\t\t\t\tlet guigelastArr = [];\r\n\t\t\t\t\tthisKeysArr.forEach((item,index) => {\r\n\t\t\t\t\t\tif(item.substr(0, item.length - 1) == this.ks.substr(0, this.ks.length - 1)){\r\n\t\t\t\t\t\t\tguigelastArr.push(Object.values(this.guigelist)[index])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tguigelastArr.forEach((item,index) => {\r\n\t\t\t\t\t\tthis.guigedatalast[0].items[index].prounits = item.prounits ? item.prounits : '';\r\n\t\t\t\t\t\tthis.guigedatalast[0].items[index].sell_price = item.sell_price ? item.sell_price : '';\r\n\t\t\t\t\t\tthis.guigedatalast[0].items[index].stock = item.stock ? item.stock : '';\r\n\t\t\t\t\t\tthis.guigedatalast[0].items[index].prounit = item.prounit ? item.prounit : '';\r\n\t\t\t\t\t\tthis.guigeProunit = item.prounit ? item.prounit : '';\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.totalprice = parseFloat( parseFloat(this.nowguige.sell_price) +this.jlprice).toFixed(2);\r\n\t\t\t},\r\n\t\t\tjlchange:function(index){\r\n\t\t\t\tthis.jialiaodata[index].active =this.jialiaodata[index].active==true?false: true;\r\n\t\t\t\tvar jlprice = 0;\r\n\t\t\t\tvar title = '';\r\n\t\t\t\tlet jlselect = [];\r\n\t\t\t\tfor(let i=0;i<this.jialiaodata.length;i++){\r\n\t\t\t\t\tif(this.jialiaodata[i].active){\r\n\t\t\t\t\t\tjlprice = jlprice+parseFloat(this.jialiaodata[i].price);\t\r\n\t\t\t\t\t\ttitle +=','+this.jialiaodata[i].jltitle;\r\n\t\t\t\t\t\tjlselect.push(this.jialiaodata[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.jltitle =title;\r\n\t\t\t\tthis.jlprice = jlprice;\r\n\t\t\t \tthis.totalprice =parseFloat( parseFloat(this.nowguige.sell_price) +jlprice).toFixed(2);\r\n\t\t\t\tthis.jlselected = jlselect;\r\n\t\t\t\t\r\n\t\t\t\tthis.jialiaodata = this.jialiaodata;\r\n\t\t\t},\r\n\t\t\ttobuy: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tlet prodataArr = Object.values(that.guigelist).filter(item => item.num > 0);\r\n\t\t\t\tif(!prodataArr.length) return app.error(\"数量不能为0\");\r\n\t\t\t\t// 混批起售数量判断\r\n\t\t\t\tlet gwcnum = prodataArr.reduce((acc,curr) => acc + curr.num,0)\r\n\t\t\t\tif(this.jietiDiscountType == 0){\r\n\t\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1 && (gwcnum != 0)) {\r\n\t\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1 && (gwcnum != 0)) {\r\n\t\t\t\t\t\t\tif(this.product.limit_start > 1) return app.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(this.product.limit_start > 0){\r\n\t\t\t\t\tlet prodnum = prodataArr.filter(item => item.num < this.product.limit_start)\r\n\t\t\t\t\tif(prodnum.length) return app.error('该商品每种规格' + this.product.limit_start + '件起售');\r\n\t\t\t\t}\r\n\t\t\t\tlet prodataIdArr = [];\r\n\t\t\t\tfor (var i = 0; i < prodataArr.length; i++) {\r\n\t\t\t\t  prodataIdArr.push(proid + ',' + prodataArr[i].id + ',' + prodataArr[i].num);\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\tif(this.controller == 'ApiShop'){\r\n\t\t\t\t\tapp.goto('/pages/shop/buy?prodata=' + prodataIdArr.join('-'));\r\n\t\t\t\t}else if(this.controller == 'ApiSeckill'){\r\n\t\t\t\t\tapp.goto('/activity/seckill/buy?prodata=' + prodata);\r\n\t\t\t\t}else if(this.controller == 'ApiSeckill2'){\r\n\t\t\t\t\tapp.goto('/activity/seckill2/buy?prodata=' + prodata);\r\n\t\t\t\t}else if(this.controller == 'ApiRestaurantTakeaway'){\r\n\t\t\t\t\tapp.goto('/restaurant/takeaway/buy?prodata=' + prodata);\r\n\t\t\t\t}else if(this.controller == 'ApiRestaurantShop'){\r\n\t\t\t\t\tapp.goto('/restaurant/shop/buy?prodata=' + prodata);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//加入购物车操作\r\n\t\t\taddcart: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar stock = that.guigelist[ks].stock;\t\t\r\n\t\t\t\tvar glass_record_id = 0;\r\n\t\t\t\tif(that.showglass){\r\n\t\t\t\t\tglass_record_id = that.grid;\r\n\t\t\t\t}\r\n\t\t\t\tlet prodataArr = Object.values(that.guigelist).filter(item => item.num > 0);\r\n\t\t\t\tif(!prodataArr.length) return app.error(\"数量不能为0\");\r\n\t\t\t\t// 混批起售数量判断\r\n\t\t\t\tlet gwcnum = prodataArr.reduce((acc,curr) => acc + curr.num,0)\r\n\t\t\t\tif(this.jietiDiscountType == 0){\r\n\t\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1 && (gwcnum != 0)) {\r\n\t\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1 && (gwcnum != 0)) {\r\n\t\t\t\t\t\t\tif(this.product.limit_start > 1) return app.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(this.product.limit_start > 0){\r\n\t\t\t\t\tlet prodnum = prodataArr.filter(item => item.num < this.product.limit_start)\r\n\t\t\t\t\tif(prodnum.length) return app.error('该商品每种规格' + this.product.limit_start + '件起售');\r\n\t\t\t\t}\r\n\t\t\t\tlet prodataIdArr = [];\r\n\t\t\t\tfor (var i = 0; i < prodataArr.length; i++) {\r\n\t\t\t\t  prodataIdArr.push(proid + ',' + prodataArr[i].id + ',' + prodataArr[i].num);\r\n\t\t\t\t}\r\n\t\t\t\tif(this.needaddcart){\r\n\t\t\t\t\tapp.post(this.controller+'/addcartmore', {prodata: prodataIdArr.join('-'),num: num,glass_record_id:glass_record_id}, function (res) {\r\n\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\tapp.success('添加成功');\r\n\t\t\t\t\t\t\tthat.$emit('addcart',{proid: proid,ggid: ggid,num: num,jlprice:that.jlprice,jltitle:that.jltitle});\r\n\t\t\t\t\t\t\tthat.$emit('buydialogChange');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.post(this.controller+'/addcartmore', {prodata: prodataIdArr.join('-'),num: num,glass_record_id:glass_record_id}, function (res) {\r\n\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\tapp.success('添加成功');\r\n\t\t\t\t\t\t\tthat.$emit('addcart');\r\n\t\t\t\t\t\t\tthat.$emit('buydialogChange');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t//加\r\n\t\tgwcplus: function (e,index,itemk) {\r\n\t\t\tlet  gwcnum = e.num + 1;\r\n\t\t\tvar ggselected = this.ggselected;\r\n\t\t\tif(this.guigedata.length < 2){\r\n\t\t\t\tggselected[0] = e.k;\r\n\t\t\t}else{\r\n\t\t\t\tggselected[itemk] = e.k;\r\n\t\t\t}\r\n\t\t\tlet ks = ggselected.join(',');\r\n\t\t\tthis.ggselected = ggselected;\r\n\t\t\tthis.ks = ks;\r\n\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\tapp.error('库存不足');\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\t\t\tthis.guigelist[ks].num = gwcnum;\r\n\t\t\tif(this.guigedata.length < 2){\r\n\t\t\t\tlet guigeArr = this.guigedata[0].items;\r\n\t\t\t\tthis.guigedata[0].items = [];\r\n\t\t\t\tguigeArr[index].num = gwcnum;\r\n\t\t\t\tthis.guigedata[0].items = guigeArr;\r\n\t\t\t}else{\r\n\t\t\t\tlet guigeArr = this.guigedatalast[0].items;\r\n\t\t\t\tthis.guigedatalast[0].items = [];\r\n\t\t\t\tguigeArr[index].num = gwcnum;\r\n\t\t\t\tthis.guigedatalast[0].items = guigeArr;\r\n\t\t\t}\r\n\t\t\tthis.ProductQuantity();\r\n\t\t},\r\n\t\t//减\r\n\t\tgwcminus: function (e,index,itemk) {\r\n\t\t\tif(!e.num) return;\r\n\t\t\tlet  gwcnum = e.num - 1;\r\n\t\t\tvar ggselected = this.ggselected;\r\n\t\t\tif(this.guigedata.length < 2){\r\n\t\t\t\tggselected[0] = e.k;\r\n\t\t\t}else{\r\n\t\t\t\tggselected[itemk] = e.k;\r\n\t\t\t}\r\n\t\t\tlet ks = ggselected.join(',');\r\n\t\t\tthis.ggselected = ggselected;\r\n\t\t\tthis.ks = ks;\r\n\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\t// 起售件数判断\r\n\t\t\tif(this.jietiDiscountType == 1){\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1 && (gwcnum != 0)) {\r\n\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1 && (gwcnum != 0)) {\r\n\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.guigelist[ks].num = gwcnum;\r\n\t\t\tif(this.guigedata.length < 2){\r\n\t\t\t\tlet guigeArr = this.guigedata[0].items;\r\n\t\t\t\tthis.guigedata[0].items = [];\r\n\t\t\t\tguigeArr[index].num = gwcnum;\r\n\t\t\t\tthis.guigedata[0].items = guigeArr;\r\n\t\t\t}else{\r\n\t\t\t\tlet guigeArr = this.guigedatalast[0].items;\r\n\t\t\t\tthis.guigedatalast[0].items = [];\r\n\t\t\t\tguigeArr[index].num = gwcnum;\r\n\t\t\t\tthis.guigedatalast[0].items = guigeArr;\r\n\t\t\t}\r\n\t\t\tthis.ProductQuantity();\r\n\t\t},\r\n\t\t//输入\r\n\t\tgwcinput: function (e,item,index,itemk) {\r\n\t\t\tvar ggselected = this.ggselected;\r\n\t\t\tif(this.guigedata.length < 2){\r\n\t\t\t\tggselected[0] = item.k;\r\n\t\t\t}else{\r\n\t\t\t\tggselected[itemk] = item.k;\r\n\t\t\t}\r\n\t\t\tlet ks = ggselected.join(',');\r\n\t\t\tthis.ggselected = ggselected;\r\n\t\t\tthis.ks = ks;\r\n\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\tif(this.guigelist[ks].stock > 0){this.guigelist[ks].num = this.guigelist[ks].stock;}else{this.guigelist[ks].num = 0;}\r\n\t\t\t\tlet guigeArr = this.guigedata[1].items;\r\n\t\t\t\tthis.guigedata[0].items = [];\r\n\t\t\t\tguigeArr[index].num = 0;\r\n\t\t\t\tthis.guigedata[0].items = guigeArr;\r\n\t\t\t\treturn this.guigelist[ks].stock > 0 ? this.guigelist[ks].stock : 0;\r\n\t\t\t}\r\n\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\tif(this.jietiDiscountType == 1){\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tgwcnum = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tgwcnum = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\tgwcnum = this.product.perlimitdan;\r\n\t\t\t}\r\n\t\t\tthis.guigelist[ks].num = gwcnum;\r\n\t\t\tif(this.guigedata.length < 2){\r\n\t\t\t\tlet guigeArr = this.guigedata[0].items;\r\n\t\t\t\tthis.guigedata[0].items = [];\r\n\t\t\t\tguigeArr[index].num = gwcnum;\r\n\t\t\t\tthis.guigedata[0].items = guigeArr;\r\n\t\t\t}else{\r\n\t\t\t\tlet guigeArr = this.guigedatalast[0].items;\r\n\t\t\t\tthis.guigedatalast[0].items = [];\r\n\t\t\t\tguigeArr[index].num = gwcnum;\r\n\t\t\t\tthis.guigedatalast[0].items = guigeArr;\r\n\t\t\t}\r\n\t\t\tthis.ProductQuantity();\r\n\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped>\r\n.pricerange-view{width: 100%;justify-content: space-between;overflow: scroll;}\r\n.pricerange-view .price-range{justify-content: center;padding: 20rpx 0rpx;;display: inline-block;width: 145rpx;margin-right:15rpx;}\r\n.pricerange-view .price-range .price-text{font-size: 28rpx;color: #333;width: 100%;justify-content: center;}\r\n.pricerange-view .price-range .range-text{font-size: 24rpx;color: #888;width: 100%;text-align: center;}\r\n\r\n.buydialog .title .choosename{height: 40rpx;line-height:40rpx;}\r\n.buydialog .title .pifa-tag{height: 44rpx;line-height: 44rpx;border-radius: 14rpx;font-size: 20rpx;width:60rpx;text-align: center;}\r\n.buydialog .title .stock{height: 34rpx;line-height:34rpx;}\r\n\r\n\r\n\r\n.buydialog .buynum{width: 100%; position: relative;margin: 2% 0rpx;justify-content: space-between}\r\n.buydialog .buynum .buynumleft-view{display: flex;align-items: center;justify-content: flex-start;width: 200rpx;}\r\n.buydialog .buynum .buynumleft-view .image-view{width: 100rpx;height: 100rpx;border-radius: 8rpx;overflow: hidden;}\r\n.buydialog .buynum .buynumleft-view .image-view image{width: 100rpx;height: 100rpx;}\r\n.buydialog .buynum .buynumleft-view .ggname-text{font-size: 24rpx;color: #888888;margin-left: 10rpx;}\r\n.buydialog .buynum .ggprice-prounits{align-items: center;font-size: 28rpx;padding: 3rpx 0rpx;}\r\n.buydialog .buynum .stock-addnum{align-items: center;}\r\n.buydialog .buynum .stock-addnum .stock-text-class{font-size: 26rpx;color: #888888;margin-top: 5rpx;}\r\n\r\n.buydialog .addnumtype6{border: 1px rgba(0,0,0,0.1) solid;}\r\n.buydialog .addnumtype6 .img{width:34rpx;height:34rpx;}\r\n\r\n.buydialog .op{margin-top:20rpx;}\r\n\r\n.member_module{border: 1rpx solid #fd4a46;}\r\n.member_lable{background: #fd4a46;}\r\n.member_value{color: #fd4a46;}\r\n.bottom-but{width: 100%;box-shadow: 0rpx -1rpx 1rpx 0rpx rgba(0,0,0,0.1);display: flex;flex-direction: column;align-items: center;}\r\n.bottom-but .total-view{width: 90%;justify-content: space-between;font-size: 26rpx;color:#333;margin: 20rpx auto 0rpx;}\r\n.bottom-but .total-view .price{ font-size: 30rpx;color: #FC4343;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-pifa2.vue?vue&type=style&index=0&id=39dc7604&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-pifa2.vue?vue&type=style&index=0&id=39dc7604&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839384932\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}