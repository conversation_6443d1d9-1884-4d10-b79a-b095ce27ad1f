{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-shortvideo/dp-shortvideo.vue?8cca", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-shortvideo/dp-shortvideo.vue?6e2f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-shortvideo/dp-shortvideo.vue?0dfd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-shortvideo/dp-shortvideo.vue?e0bb", "uni-app:///components/dp-shortvideo/dp-shortvideo.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-shortvideo/dp-shortvideo.vue?51dc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-shortvideo/dp-shortvideo.vue?7e4f"], "names": ["data", "pre_url", "props", "params"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA20B,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoB/1B;EACAA;IACA;MACAC;IACA;EACA;EACAC;IACAC;IACAH;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAAwrC,CAAgB,wmCAAG,EAAC,C;;;;;;;;;;;ACA5sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-shortvideo/dp-shortvideo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-shortvideo.vue?vue&type=template&id=67fd2686&\"\nvar renderjs\nimport script from \"./dp-shortvideo.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-shortvideo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-shortvideo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-shortvideo/dp-shortvideo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-shortvideo.vue?vue&type=template&id=67fd2686&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-shortvideo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-shortvideo.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-shortvideo\" :style=\"{\r\n\tcolor:params.color,\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',\r\n\tpadding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'\r\n}\">\r\n\t<view class=\"dp-shortvideo-content\">\r\n\t\t<view v-for=\"(item, index) in data\" :key=\"index\" class=\"item\" :style=\"{marginRight:index%2==0?'2%':'0'}\">\r\n\t\t\t<image class=\"ff\" mode=\"widthFix\" :src=\"item.coverimg\" @tap=\"goto\"  :data-url=\"'/activity/shortvideo/detail?id=' + item.videoId +'&cid='+params.category\"></image>\r\n\t\t\t<view class=\"f2\">\r\n\t\t\t\t<view class=\"t1\" v-if=\"params.showlogo==1\"><image class=\"touxiang\" :src=\"item.logo\"/></view>\r\n\t\t\t\t<view class=\"t2\" v-if=\"params.showviewnum==1\"><image class=\"tubiao\" :src=\"pre_url+'/static/img/shortvideo_playnum.png'\"/>{{item.view_num}}</view>\r\n\t\t\t\t<view class=\"t3\" v-if=\"params.showzannum==1\"><image class=\"tubiao\" :src=\"pre_url+'/static/img/shortvideo_likenum.png'\"/>{{item.zan_num}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tpre_url:getApp().globalData.pre_url\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-shortvideo{height: auto;position: relative; }\r\n.dp-shortvideo-content{position:relative;display:flex;flex-wrap:wrap}\r\n.dp-shortvideo-content .item{width:49%;height:500rpx;background:#fff;overflow:hidden;border-radius:8rpx;margin-bottom:20rpx;position:relative}\r\n.dp-shortvideo-content .item .ff{width:100%;height:100%;display:block;}\r\n.dp-shortvideo-content .item .f2{position: absolute;bottom:20rpx;left:20rpx;display:flex;align-items:center;color:#fff;font-size:22rpx}\r\n.dp-shortvideo-content .item .f2 .t1{display:flex;align-items:center;text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);}\r\n.dp-shortvideo-content .item .f2 .t2{display:flex;align-items:center;margin-left:30rpx;text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);}\r\n.dp-shortvideo-content .item .f2 .t3{display:flex;align-items:center;margin-left:30rpx;text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);}\r\n.dp-shortvideo-content .item .f2 .tubiao{display:block;height:28rpx;width:28rpx;margin-right:10rpx}\r\n.dp-shortvideo-content .item .f2 .touxiang{display:block;width:40rpx;height:40rpx;border-radius:50%;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-shortvideo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-shortvideo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839375311\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}