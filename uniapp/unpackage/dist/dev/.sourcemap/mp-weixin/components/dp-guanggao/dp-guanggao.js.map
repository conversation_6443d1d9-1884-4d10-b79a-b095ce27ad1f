{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-guanggao/dp-guanggao.vue?33f9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-guanggao/dp-guanggao.vue?7b24", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-guanggao/dp-guanggao.vue?8388", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-guanggao/dp-guanggao.vue?d860", "uni-app:///components/dp-guanggao/dp-guanggao.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-guanggao/dp-guanggao.vue?0081", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-guanggao/dp-guanggao.vue?189b"], "names": ["data", "guang<PERSON><PERSON><PERSON>", "windowHeight", "isend", "cpbtn", "hasLaunchedad", "pre_url", "props", "guang<PERSON><PERSON>l", "guanggaopic", "guanggaotype", "default", "param", "ggcover", "ggskip", "skiptype", "cishu", "mounted", "uni", "methods", "guanggaoClick", "playend"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0B71B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;IACAC;MAAAC;IAAA;IACAC;MAAAC;MAAAC;MAAAC;MAAAC;IAAA;EAEA;EACAC;IAEA;IACA;IACA;MACA;IACA;;IAGA;IACA;MACA;MACA;QAEA;QACA;;QAEA;QACAC;MACA;IAEA;MAEA;MACAA;MACAA;IACA;EAEA;EAGAC;IAEAC;MACA;IACA;IAGAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-guanggao/dp-guanggao.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-guanggao.vue?vue&type=template&id=7215d41a&\"\nvar renderjs\nimport script from \"./dp-guanggao.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-guanggao.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-guanggao.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-guanggao/dp-guanggao.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-guanggao.vue?vue&type=template&id=7215d41a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-guanggao.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-guanggao.vue?vue&type=script&lang=js&\"", "<template>\r\n<view  v-if=\"guanggaopic && guanggaostatus=='1' && hasLaunchedad\">\r\n\t<view class=\"advert flex-xy-center\" :class=\"param.ggcover=='1'?'advert_cover':''\" @touchmove.stop.prevent=\" \">\r\n\t\t<view class=\"advert_module\">\r\n\t\t\t<image class=\"advert_close_img\" @tap=\"guanggaoClick\" :src=\"pre_url+'/static/img/close2.png'\" v-if=\"guanggaotype=='1' && param.ggcover=='1' && param.ggskip=='1' && param.skiptype!=2\" alt=\"\"/>\r\n\t\t\t<image :src=\"guanggaopic\" @tap=\"goto\" :data-url=\"guanggaourl\" :mode=\"param.ggcover=='1'?'acceptFill':'widthFix'\" class=\"advert_poster\" alt=\"\"/>\r\n\t\t\t<view @tap=\"guanggaoClick\" class=\"advert_close advert_close_bottom flex-xy-center\" v-if=\"guanggaotype=='1' && param.ggcover!='1' && param.ggskip=='1'\">\r\n<!--\t\t\t<view @tap=\"guanggaoClick\" class=\"advert_close advert_close_bottom flex-xy-center\" v-if=\"param.ggskip=='1'\">-->\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" alt=\"\"/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"advert_video\" :class=\"param.ggcover=='1'?'advert_cover':''\" v-if=\"guanggaotype=='2'\">\r\n\t\t<text v-if=\"guanggaotype=='2' && param.ggcover=='1' && param.ggskip=='1' && param.skiptype!=2\" class=\"advert_close_txt\" @tap=\"guanggaoClick\">跳过</text>\r\n\t\t<video style=\"max-width: 100%; height: 850rpx;\" class=\"dp-guanggao-video\" :style=\"{height:windowHeight+'px',width:'100%'}\" :src=\"guanggaopic\" :show-mute-btn=\"true\" :play-btn-position=\"'center'\"\r\n\t\t:object-fit=\"param.ggcover==1?'cover':''\" :controls=\"false\" :autoplay=\"true\" :loop=\"false\" :show-center-play-btn=\"cpbtn\" @ended=\"playend\"></video>\r\n\t\t<view @tap=\"guanggaoClick\" class=\"advert_close advert_close_top flex-xy-center\" v-if=\"isend\">\r\n\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" alt=\"\"/>\r\n\t\t</view>\r\n\t</view>\r\n\t<view @tap=\"guanggaoClick\" class=\"dp-bottom-close-btn\" v-if=\"(param.showgg=='2' || (param.showgg=='1' && param.ggcover=='1')) && param.ggskip=='1' &&  param.skiptype==2\">\r\n\t\t<view class=\"dp-ggskip-btn\" >跳过</view>\r\n\t</view>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tguanggaostatus:'1',\r\n\t\t\t\twindowHeight:560,\r\n\t\t\t\tisend:false,\r\n\t\t\t\tcpbtn:false,\r\n        hasLaunchedad:false,\r\n\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tguanggaourl:\"\",\r\n\t\t\tguanggaopic:\"\",\r\n\t\t\tguanggaotype:{default:'1'},\r\n\t\t\tparam:{ggcover:0,ggskip:0,skiptype:1,cishu:0}\r\n\r\n\t\t},\r\n\t\tmounted:function(){\r\n\r\n\t\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\t\t\tthis.windowHeight = sysinfo.windowHeight;\r\n\t\t\tif(app.globalData.platform=='h5' || app.globalData.platform=='mp'){\r\n\t\t\t\tthis.cpbtn = true\r\n\t\t\t}\r\n\r\n\r\n      //如果开启了首次启动显示\r\n      if(this.param.cishu == 0){\r\n        const isFirstLaunch = uni.getStorageSync('hasLaunched');\r\n        if (!isFirstLaunch) {\r\n\r\n          // 是首次启动，显示广告\r\n          this.hasLaunchedad = true;\r\n\r\n          // 设置已启动标识\r\n          uni.setStorageSync('hasLaunched', true);\r\n        }\r\n\r\n      } else {\r\n\r\n        this.hasLaunchedad = true;\r\n        uni.setStorageSync('hasLaunched', false);\r\n        uni.setStorageSync('guanggacishu', 0);\r\n      }\r\n\r\n\t\t},\r\n\r\n\r\n\t\tmethods:{\r\n\r\n\t\t\tguanggaoClick(){\r\n        this.guanggaostatus='0'\r\n\t\t\t},\r\n\r\n\r\n\t\t\tplayend:function(e){\r\n\t\t\t\tthis.guanggaostatus='0';\r\n\t\t\t\tthis.isend = true;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.advert{\r\n\t\tposition: fixed;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 2000000;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t}\r\n\t.advert_module{\r\n\t\tposition: relative;\r\n\t\twidth: 80%;\r\n\t}\r\n\t.advert_cover .advert_module{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.advert_poster{\r\n\t\tposition: relative;\r\n\t\twidth: 85%;\r\n\t\tdisplay: block;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\t.advert.advert_cover{z-index: 2000000;}\r\n\t.advert_cover .advert_poster{\r\n\t\theight: 100%;\r\n\t}\r\n\t.advert_cover .advert_module .advert_poster{\r\n\t\twidth: 100%;height: 100%;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.advert_cover .advert_close_txt{\r\n\t\tposition: absolute;\r\n\t\ttop: 6rpx;\r\n\t\tright: 6rpx;\r\n\t\twidth: 100rpx;\r\n\t\theight: 46rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t\tcolor: #ffffff;\r\n\t\tz-index: 2000050;\r\n\t\tborder-radius: 30rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.advert_cover .advert_module .advert_close_img{\r\n\t\tposition: absolute;\r\n\t\ttop: 6rpx;\r\n\t\tright: 6rpx;\r\n\t\twidth: 46rpx;\r\n\t\theight: 46rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tz-index: 2000050;\r\n\t}\r\n\t.advert_close_bottom{\r\n\t\tposition: relative;\r\n\t\tborder: 1px solid #fff;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t.advert_close_top{\r\n\t\tposition: fixed;\r\n\t\ttop: 14rpx;\r\n\t\tright: 14rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t}\r\n\t.advert_close{\r\n\t\theight: 60rpx;\r\n\t\twidth: 60rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tborder-radius: 100rpx;\r\n\t}\r\n\t.advert_close image{\r\n\t\theight: 50rpx;\r\n\t\twidth: 50rpx;\r\n\t}\r\n\t\r\n\t.advert_video{\r\n\t\tposition: fixed;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 2000000;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t}\r\n\t\r\n\t.dp-bottom-close-btn{display: flex;align-items: center;justify-content: center;position: fixed;bottom: 90rpx;width: 100%;\tz-index: 2000070;}\r\n\t.dp-ggskip-btn{width: 120rpx;color: #fff;background: rgba(0, 0, 0, 0.7);border-radius: 40rpx;text-align: center; padding: 8rpx 20rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-guanggao.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-guanggao.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839386270\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}