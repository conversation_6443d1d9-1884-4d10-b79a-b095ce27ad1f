{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-item/dp-product-item.vue?2099", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-item/dp-product-item.vue?a2d4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-item/dp-product-item.vue?c14c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-item/dp-product-item.vue?2cee", "uni-app:///components/dp-product-item/dp-product-item.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-item/dp-product-item.vue?8c0b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-product-item/dp-product-item.vue?dabf"], "names": ["data", "pre_url", "buydialogShow", "proid", "showLinkStatus", "lx_bname", "lx_name", "lx_bid", "lx_tel", "btntype", "productType", "ggNum", "props", "showstyle", "default", "menuindex", "saleimg", "showname", "namecolor", "showprice", "showcost", "showstock", "showsales", "showcart", "cartimg", "idfield", "probgcolor", "showcommission", "showbname", "showbdistance", "params", "type", "methods", "buydialogChange", "console", "addcart", "showLinkChange", "that", "toDetail", "url", "app"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzSA;AAAA;AAAA;AAAA;AAA60B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoLj2B;AAAA,gBACA;EACAA;IACA;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;MAAAJ;IAAA;IACAK;MAAAL;IAAA;IACAM;MAAAN;IAAA;IACAO;MAAAP;IAAA;IACAQ;MAAAR;IAAA;IACAS;MAAAT;IAAA;IACAU;MAAAV;IAAA;IACAd;IACAyB;MAAAX;IAAA;IACAY;MAAAZ;IAAA;IACAa;MACAb;IACA;IACAc;MACAd;IACA;IACAe;MACAf;IACA;IACAgB;MACAC;MACAjB;QACA;MACA;IACA;EACA;EACAkB;IACAC;MAAA;MACA;QACA;QACA;QACA;UACA;YACA;YACA;cACA;gBACA;cACA;gBACA;cACA;YACA;UACA;QACA;MACA;MACA;MACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAC;MACAA;MACAA;MACAA;MACAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACAC;MACA;MACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACtRA;AAAA;AAAA;AAAA;AAA0rC,CAAgB,0mCAAG,EAAC,C;;;;;;;;;;;ACA9sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-product-item/dp-product-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dp-product-item.vue?vue&type=template&id=40f320aa&\"\nvar renderjs\nimport script from \"./dp-product-item.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-product-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-product-item.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-product-item/dp-product-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-item.vue?vue&type=template&id=40f320aa&\"", "var components\ntry {\n  components = {\n    buydialogPifa: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa/buydialog-pifa\" */ \"@/components/buydialog-pifa/buydialog-pifa.vue\"\n      )\n    },\n    buydialogPifa2: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa2/buydialog-pifa2\" */ \"@/components/buydialog-pifa2/buydialog-pifa2.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m21 =\n    _vm.params.style == \"2\" && _vm.params.nowbuy == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m22 =\n    _vm.params.style == \"2\" && _vm.params.nowbuy == 1 ? _vm.t(\"color1\") : null\n  var l1 = _vm.__map(_vm.data, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.showstyle == \"1\" && item.sellpoint ? _vm.t(\"color2\") : null\n    var m1 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"color2rgb\")\n        : null\n    var m2 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"color2\")\n        : null\n    var m3 =\n      _vm.showcommission == 1 && item.commission_price > 0\n        ? _vm.t(\"佣金\")\n        : null\n    var m4 =\n      _vm.showprice != \"0\" &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true) ||\n        item.usd_sellprice) &&\n      !item.price_color\n        ? _vm.t(\"color1\")\n        : null\n    var m5 =\n      _vm.showprice != \"0\" &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true) ||\n        item.usd_sellprice) &&\n      !item.usd_sellprice\n        ? !_vm.isNull(item.service_fee) &&\n          item.service_fee_switch &&\n          item.service_fee > 0\n        : null\n    var m6 =\n      _vm.showprice != \"0\" &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true) ||\n        item.usd_sellprice) &&\n      !item.usd_sellprice &&\n      m5\n        ? _vm.t(\"服务费\")\n        : null\n    var m7 =\n      _vm.showprice != \"0\" &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true) ||\n        item.usd_sellprice) &&\n      !item.usd_sellprice &&\n      _vm.showstyle != 3 &&\n      item.product_type == 2 &&\n      item.unit_price &&\n      item.unit_price > 0\n        ? _vm.t(\"color1\")\n        : null\n    var m8 =\n      _vm.showprice != \"0\" &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true) ||\n        item.usd_sellprice) &&\n      ((_vm.showprice == \"3\" && item.nextmemberlevel_name) ||\n        _vm.showprice == \"4\")\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m9 =\n      _vm.showprice != \"0\" &&\n      (item.price_type != 1 || item.sell_price > 0) &&\n      (!item.show_sellprice ||\n        (item.show_sellprice && item.show_sellprice == true) ||\n        item.usd_sellprice) &&\n      ((_vm.showprice == \"3\" && item.nextmemberlevel_name) ||\n        _vm.showprice == \"4\")\n        ? _vm.t(\"color1\")\n        : null\n    var m10 =\n      item.xunjia_text &&\n      item.price_type == 1 &&\n      item.sell_price <= 0 &&\n      _vm.showstyle != 1\n        ? _vm.t(\"color1\")\n        : null\n    var m11 =\n      item.xunjia_text &&\n      item.price_type == 1 &&\n      item.sell_price <= 0 &&\n      _vm.showstyle == 1\n        ? _vm.t(\"color1\")\n        : null\n    var m12 =\n      item.xunjia_text &&\n      item.price_type == 1 &&\n      item.sell_price <= 0 &&\n      item.xunjia_text &&\n      item.price_type == 1\n        ? _vm.t(\"color1\")\n        : null\n    var g0 = item.priceshows && item.priceshows.length > 0\n    var m13 =\n      _vm.showprice == \"3\" && item.nextmemberlevel_price\n        ? _vm.t(\"color2\")\n        : null\n    var m14 =\n      _vm.showprice == \"3\" &&\n      item.nextmemberlevel_price &&\n      item.nextmemberlevel_name\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m15 =\n      _vm.showprice == \"3\" &&\n      item.nextmemberlevel_price &&\n      item.nextmemberlevel_name\n        ? _vm.t(\"color1\")\n        : null\n    var m16 =\n      _vm.showprice == \"4\" && item.scoredk_price >= 0 ? _vm.t(\"color2\") : null\n    var m17 =\n      _vm.showprice == \"4\" && item.scoredk_price >= 0\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m18 =\n      _vm.showprice == \"4\" && item.scoredk_price >= 0 ? _vm.t(\"color1\") : null\n    var g1 = _vm.showstyle == \"1\" && item.fwlist && item.fwlist.length > 0\n    var l0 = g1\n      ? _vm.__map(item.fwlist, function (fw, fwidx) {\n          var $orig = _vm.__get_orig(fw)\n          var m19 = _vm.t(\"color2rgb\")\n          var m20 = _vm.t(\"color2\")\n          return {\n            $orig: $orig,\n            m19: m19,\n            m20: m20,\n          }\n        })\n      : null\n    var m23 =\n      _vm.showcart == 1 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      _vm.params.style == \"2\" &&\n      _vm.params.nowbuy == 1\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m24 =\n      _vm.showcart == 1 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      _vm.params.style == \"2\" &&\n      _vm.params.nowbuy == 1\n        ? _vm.t(\"color1\")\n        : null\n    var m25 =\n      _vm.showcart == 1 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      !(_vm.params.style == \"2\" && _vm.params.nowbuy == 1)\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m26 =\n      _vm.showcart == 1 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      !(_vm.params.style == \"2\" && _vm.params.nowbuy == 1)\n        ? _vm.t(\"color1\")\n        : null\n    var m27 =\n      _vm.showcart == 2 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      _vm.params.style == \"2\" &&\n      _vm.params.nowbuy == 1\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m28 =\n      _vm.showcart == 2 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      _vm.params.style == \"2\" &&\n      _vm.params.nowbuy == 1\n        ? _vm.t(\"color1\")\n        : null\n    var m29 =\n      _vm.showcart == 2 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      !(_vm.params.style == \"2\" && _vm.params.nowbuy == 1)\n        ? _vm.t(\"color1rgb\")\n        : null\n    var m30 =\n      _vm.showcart == 2 &&\n      !item.price_type &&\n      item.hide_cart != true &&\n      !(_vm.params.style == \"2\" && _vm.params.nowbuy == 1)\n        ? _vm.t(\"color1\")\n        : null\n    var m31 = item.hongbaoEdu > 0 ? _vm.t(\"color2\") : null\n    var m32 = item.hongbaoEdu > 0 ? _vm.t(\"color2rgb\") : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n      m6: m6,\n      m7: m7,\n      m8: m8,\n      m9: m9,\n      m10: m10,\n      m11: m11,\n      m12: m12,\n      g0: g0,\n      m13: m13,\n      m14: m14,\n      m15: m15,\n      m16: m16,\n      m17: m17,\n      m18: m18,\n      g1: g1,\n      l0: l0,\n      m23: m23,\n      m24: m24,\n      m25: m25,\n      m26: m26,\n      m27: m27,\n      m28: m28,\n      m29: m29,\n      m30: m30,\n      m31: m31,\n      m32: m32,\n    }\n  })\n  var m33 = _vm.showLinkStatus && _vm.lx_tel ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m21: m21,\n        m22: m22,\n        l1: l1,\n        m33: m33,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-item.vue?vue&type=script&lang=js&\"", "<template>\r\n<view style=\"width:100%\">\r\n\t<view class=\"dp-product-normal-item\">\r\n\t\t<view class=\"item\" v-for=\"(item,index) in data\" :style=\"'background:'+probgcolor+';'+(showstyle==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (showstyle==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%'))\" :key=\"item.id\" @click=\"toDetail(index)\" >\r\n\t\t\t<view class=\"product-pic\" >\r\n\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t<image class=\"saleimg\" :src=\"saleimg\" v-if=\"saleimg!=''\" mode=\"widthFix\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product-info\">\r\n\t\t\t\t<view class=\"p1\" v-if=\"showname == 1\">{{item.name}}</view>\r\n\t\t\t\t<view class=\"p5\" v-if=\"showstyle=='1' && item.sellpoint\" :style=\"{color:t('color2')}\"><text>{{item.sellpoint}}</text></view>\r\n\t\t\t\t<!-- 是否显示商家 距离 佣金 S-->\r\n\t\t\t\t<view class=\"binfo flex-bt\" v-if=\"(showbname=='1' || showbdistance=='1') && item.binfo\">\r\n\t\t\t\t\t\t<view class=\"flex-y-center b1\">\r\n\t\t\t\t\t\t\t<block v-if=\"showbname=='1'\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.binfo.logo\" class=\"t1\">\r\n\t\t\t\t\t\t\t\t<text class=\"t2\">{{item.binfo.name}}</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"b2 t2\" v-if=\"showbdistance=='1' && item.binfo.distance\">{{item.binfo.distance}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"couponitem\" v-if=\"showcommission == 1 && item.commission_price>0\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t\" :style=\"{background:'rgba('+t('color2rgb')+',0.1)',color:t('color2')}\">\r\n\t\t\t\t\t\t\t<text>{{t('佣金')}}{{item.commission_price}}{{item.commission_desc}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 是否显示商家 距离 佣金 E-->\r\n                \r\n        <view v-if=\"showstyle==2\">\r\n            <view class=\"field_buy\" v-if=\"params.brand == 1 && item.brand\">\r\n                <span style=\"width: 80rpx\">品牌：</span>\r\n                <span>{{item.brand}}</span>\r\n            </view>\r\n            <view  class=\"field_buy\" v-if=\"params.barcode == 1 && item.barcode\">\r\n                <span style=\"width: 80rpx\">货号：</span>\r\n                <span>{{item.barcode}}</span>\r\n            </view>\r\n            <view  class=\"field_buy\" v-if=\"params.guige == 1 && item.ggname\">\r\n                <span style=\"width: 80rpx\"> 规格：</span>\r\n                <span>{{item.ggname}}</span>\r\n            </view>\r\n            <view  class=\"field_buy\" v-if=\"params.unit == 1 && item.unit\">\r\n                <span style=\"width: 80rpx\"> 单位：</span>\r\n                <span>{{item.unit}}</span>\r\n            </view>\r\n            <view  class=\"field_buy\" v-if=\"params.ggstock == 1\">\r\n                <span style=\"width: 80rpx\"> 库存：</span>\r\n                <span>{{item.ggstock}}</span>\r\n            </view>\r\n            <view  class=\"field_buy\" v-if=\"params.valid_time == 1 && item.valid_time\">\r\n                <span style=\"width: 80rpx\"> 有效期：</span>\r\n                <span>{{item.valid_time}}</span>\r\n            </view>\r\n            <view  class=\"field_buy\" v-if=\"params.remark == 1 && item.remark\">\r\n                <span style=\"width: 80rpx\"> 备注：</span>\r\n                <span>{{item.remark}}</span>\r\n            </view>\r\n        </view>\r\n        <view v-if=\"(showstyle=='2' || showstyle=='3') && item.price_type != 1 && item.show_cost == '1'\" :style=\"{color:item.cost_color?item.cost_color:'#999',fontSize:'36rpx'}\"><text style=\"font-size: 24rpx;\">{{item.cost_tag}}</text>{{item.cost_price}}</view>      \r\n\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t<view class=\"p2-1\" :class=\"params.style=='1'?'flex-bt flex-y-center':''\" v-if=\"showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)\">\r\n\t\t\t\t\t\t<view v-if=\"showstyle=='1' && item.price_type != 1 && item.show_cost=='1'\" :style=\"{color:item.cost_color?item.cost_color:'#999',fontSize:'36rpx'}\"><text style=\"font-size: 24rpx;\">{{item.cost_tag}}</text>{{item.cost_price}}</view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\" v-if=\"(!item.show_sellprice || (item.show_sellprice && item.show_sellprice==true)) || item.usd_sellprice\">\r\n\t\t\t\t\t\t\t<view class=\"t1\" :style=\"{color:item.price_color?item.price_color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{item.usd_sellprice}}\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size: 28rpx;\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text><text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\r\n                  <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">{{item.price_tag?item.price_tag:'￥'}}</text>{{item.sell_price}}\r\n                  <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\r\n                  <text v-if=\"!isNull(item.service_fee) && item.service_fee_switch && item.service_fee > 0\" style=\"font-size: 28rpx;\">+{{item.service_fee}}{{t('服务费')}}</text><text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"showstyle!=3 && item.product_type==2 && item.unit_price && item.unit_price>0\" class=\"t1-m\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t(约{{item.unit_price}}元/斤)\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<text :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1'),fontSize:'12rpx'}\" v-if=\"(showprice == '3' && item.nextmemberlevel_name) || showprice == '4'\" class=\"tag_price_name\"> 零售价 </text>\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"(!item.show_sellprice || (item.show_sellprice && item.show_sellprice==true)) && item.market_price*1 > item.sell_price*1 && showprice == '1'\">￥{{item.market_price}}</text>\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<text class=\"t3\" v-if=\"item.juli\" style=\"color:#888;\">{{item.juli}}</text> \r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"p2-1\" v-if=\"item.xunjia_text && item.price_type == 1 && item.sell_price <= 0\" style=\"height: 50rpx;line-height: 44rpx;\">\r\n\t\t\t\t\t\t<text v-if=\"showstyle!=1\" class=\"t1\" :style=\"{color:t('color1'),fontSize:'30rpx'}\">询价</text>\r\n\t\t\t\t\t\t\t<text v-if=\"showstyle==1\" class=\"t1\" :style=\"{color:t('color1')}\">询价</text>\r\n\t\t\t\t\t\t\t<block v-if=\"item.xunjia_text && item.price_type == 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"lianxi\" :style=\"{background:t('color1')}\" @tap.stop=\"showLinkChange\" :data-lx_name=\"item.lx_name\" :data-lx_bid=\"item.lx_bid\" :data-lx_bname=\"item.lx_bname\" :data-lx_tel=\"item.lx_tel\" data-btntype=\"2\">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n        <!-- 商品处显示会员价 -->\r\n        <view v-if=\"item.price_show && item.price_show == 1\" style=\"line-height: 46rpx;\">\r\n          <text style=\"font-size:26rpx\">￥{{item.sell_putongprice}}</text>\r\n        </view>\r\n        <view v-if=\"item.priceshows && item.priceshows.length>0\">\r\n          <view v-for=\"(item2,index2) in item.priceshows\" style=\"line-height: 46rpx;\">\r\n            <text style=\"font-size:26rpx\">￥{{item2.sell_price}}</text>\r\n            <text style=\"margin-left: 15rpx;font-size: 22rpx;font-weight: 400;\">{{item2.price_show_text}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n\t\t\t\t<view class=\"p2\" v-if=\"showprice == '3'\">\r\n\t\t\t\t\t<view class=\"p2-1\"  style=\"height: 30rpx;line-height: 30rpx;\">\r\n\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.nextmemberlevel_price\" style=\"height: 30rpx;line-height: 30rpx;text-align:center;\">\r\n\t\t\t\t\t\t<text :style=\"{color:t('color2'),fontSize:'30rpx'}\"><text style=\"font-size:22rpx\">￥</text>{{item.nextmemberlevel_price}} </text>\r\n\t\t\t\t\t\t<text :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1'),fontSize:'12rpx'}\" v-if=\"item.nextmemberlevel_name\"  class=\"tag_price_name\"> {{item.nextmemberlevel_name}} </text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"p2\" v-if=\"showprice == '4'\">\r\n\t\t\t\t\t<view class=\"p2-1\" style=\"height: 30rpx;line-height: 30rpx;\">\r\n\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.scoredk_price >=0\" style=\"height: 30rpx;line-height: 30rpx;text-align:center;\">\r\n\t\t\t\t\t\t<text :style=\"{color:t('color2'),fontSize:'30rpx'}\"><text style=\"font-size:22rpx\">￥</text>{{item.scoredk_price}} </text> <text :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1'),fontSize:'12rpx'}\"  class=\"tag_price_name\"> 出厂价 </text>\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"p6\" v-if=\"showstyle=='1' && item.fwlist && item.fwlist.length>0\">\r\n\t\t\t\t\t<view class=\"p6-m\" :style=\"'background:rgba('+t('color2rgb')+',0.15);color:'+t('color2')+';'\" v-for=\"(fw,fwidx) in item.fwlist\" :key=\"fwidx\">\r\n\t\t\t\t\t\t{{fw}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"p1\" v-if=\"item.merchant_name\" style=\"color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal;\"><text>{{item.merchant_name}}</text></view>\r\n\t\t\t\t<view class=\"p1\" v-if=\"item.main_business\" style=\"color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;\"><text>{{item.main_business}}</text></view>\r\n\t\t\t\t<text class=\"p3\" v-if=\"item.product_type == 3\">手工费: ￥{{item.hand_fee?item.hand_fee:0}}</text>\r\n\t\t\t\t<view class=\"p3\" v-if=\"showstock=='1'\">库存{{item.stock}}</view>\r\n\t\t\t\t<view class=\"p3\" v-if=\"showsales=='1'\">已售{{item.sales}}件</view>\r\n\t\t\t\t<view v-if=\"(showsales !='1' ||  item.sales<=0) && item.main_business\" style=\"height: 44rpx;\"></view>\r\n        <view v-if=\"params.style=='2' && params.nowbuy == 1\" @click.stop=\"buydialogChange\" data-btntype=\"2\" :data-proid=\"item[idfield]\" class=\"nowbuy\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" >\r\n            立即购买\r\n        </view>\r\n\t\t\t\t<view class=\"p4\" :style=\"params.style=='2' && params.nowbuy == 1?'bottom:24rpx;background:rgba('+t('color1rgb')+',0.1);color:'+t('color1'):'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')\" v-if=\"showcart==1 && !item.price_type && item.hide_cart!=true\" @click.stop=\"buydialogChange\" data-btntype=\"1\" :data-proid=\"item[idfield]\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t<view class=\"p4\" :style=\"params.style=='2' && params.nowbuy == 1?'bottom:24rpx;background:rgba('+t('color1rgb')+',0.1);color:'+t('color1'):'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')\" v-if=\"showcart==2 && !item.price_type && item.hide_cart!=true\" @click.stop=\"buydialogChange\" data-btntype=\"1\" :data-proid=\"item[idfield]\"><image :src=\"cartimg\" class=\"img\"/></text></view>\r\n      </view>\r\n\t\t\t<view class=\"bg-desc\" v-if=\"item.hongbaoEdu > 0\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">可获额度 +{{item.hongbaoEdu}}</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<block v-if=\"productType == 4\">\r\n\t\t<block v-if=\"ggNum == 2\">\r\n\t\t\t<buydialog-pifa v-if=\"buydialogShow\" :proid=\"proid\" :btntype=\"btntype\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" />\r\n\t\t</block>\r\n\t\t<block v-else>\r\n\t\t\t<buydialog-pifa2 v-if=\"buydialogShow\" :proid=\"proid\" :btntype=\"btntype\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" />\r\n\t\t</block>\r\n\t</block>\r\n\t<block v-else>\r\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" :btntype=\"btntype\" @addcart=\"addcart\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\"></buydialog>\r\n\t</block>\r\n    <view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\r\n    \t<view class=\"main\">\r\n    \t\t<view class=\"close\" @tap=\"showLinkChange\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n    \t\t<view class=\"content\">\r\n    \t\t\t<view class=\"title\">{{lx_name}}</view>\r\n    \t\t\t<view class=\"row\" v-if=\"lx_bid > 0\">\r\n    \t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">店铺名称</view>\r\n    \t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;display: flex;\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+lx_bid\">\r\n    \t\t\t\t  <view style=\"width: 100%;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;\">{{lx_bname}}</view>\r\n    \t\t\t\t  <view style=\"flex: 1;\"></view>\r\n    \t\t\t\t  <image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/>\r\n    \t\t\t\t</view>\r\n    \t\t\t</view>\r\n    \t\t\t<view class=\"row\" v-if=\"lx_tel\">\r\n    \t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">联系电话</view>\r\n    \t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;\" @tap=\"goto\" :data-url=\"'tel::'+lx_tel\" :style=\"{color:t('color1')}\">{{lx_tel}}<image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"lx_tel\"></image></view>\r\n    \t\t\t</view>\r\n    \t\t</view>\r\n    \t</view>\r\n    </view>\r\n</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tbuydialogShow:false,\r\n\t\t\t\tproid:0,\r\n                \r\n        showLinkStatus:false,\r\n        lx_bname:'',\r\n        lx_name:'',\r\n        lx_bid:'',\r\n        lx_tel:'',\r\n        btntype:1,\r\n\t\t\t\tproductType:'',\r\n\t\t\t\tggNum:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tshowstyle:{default:2},\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tsaleimg:{default:''},\r\n\t\t\tshowname:{default:1},\r\n\t\t\tnamecolor:{default:'#333'},\r\n\t\t\tshowprice:{default:'1'},\r\n\t\t\tshowcost:{default:'0'},\r\n\t\t\tshowstock:{default:'0'},\r\n\t\t\tshowsales:{default:'1'},\r\n\t\t\tshowcart:{default:'1'},\r\n\t\t\tcartimg:{default:'/static/imgsrc/cart.svg'},\r\n\t\t\tdata:{},\r\n\t\t\tidfield:{default:'id'},\r\n\t\t\tprobgcolor:{default:'#fff'},\r\n\t\t\tshowcommission: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowbname: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n\t\t\tshowbdistance: {\r\n\t\t\t\tdefault: '0'\r\n\t\t\t},\r\n      params:{\r\n\t\t\t\ttype:Object,\r\n\t\t\t\tdefault() {\r\n\t\t\t\t\treturn {};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tbuydialogChange: function (e) {\r\n\t\t\t\tif(!this.buydialogShow){\r\n\t\t\t\t\tthis.proid = e.currentTarget.dataset.proid;\r\n          this.btntype = e.currentTarget.dataset.btntype;\r\n\t\t\t\t\tthis.data.forEach(item => {\r\n\t\t\t\t\t\tif(item[this.idfield] == this.proid){\r\n\t\t\t\t\t\t\tthis.productType = item.product_type;\r\n\t\t\t\t\t\t\tif(item.product_type == 4){\r\n\t\t\t\t\t\t\t\tif(item.gg_num){\r\n\t\t\t\t\t\t\t\t\tthis.ggNum = item.gg_num;\r\n\t\t\t\t\t\t\t\t}else if(item.guigedata){\r\n\t\t\t\t\t\t\t\t\tthis.ggNum = Object.keys(JSON.parse(item.guigedata)).length;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t\tconsole.log(this.buydialogShow);\r\n\t\t\t},\r\n\t\t\taddcart:function(){\r\n\t\t\t\tthis.$emit('addcart');\r\n\t\t\t},\r\n            showLinkChange: function (e) {\r\n                var that = this;\r\n            \tthat.showLinkStatus = !that.showLinkStatus;\r\n                that.lx_name = e.currentTarget.dataset.lx_name;\r\n                that.lx_bid = e.currentTarget.dataset.lx_bid;\r\n                that.lx_bname = e.currentTarget.dataset.lx_bname;\r\n                that.lx_tel = e.currentTarget.dataset.lx_tel;\r\n            },\r\n            toDetail:function(key){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar item = that.data[key];\r\n\t\t\t\tvar id = item[that.idfield];\r\n\t\t\t\tvar url = '/pages/shop/product?id='+id;//默认链接\r\n\t\t\t\t//来自商品柜\r\n\t\t\t\tif(item.device_id){\r\n\t\t\t\t\tvar dgid = item.id;\r\n\t\t\t\t\tvar deviceno = item.device_no;\r\n\t\t\t\t\tvar lane = item.goods_lane;\r\n\t\t\t\t\tvar prodata  = id+','+item.ggid+','+item.stock;\r\n\t\t\t\t\tvar devicedata = deviceno+','+lane;\r\n\t\t\t\t\turl = url+'&dgprodata='+prodata+'&devicedata='+devicedata;\r\n\t\t\t\t}\r\n\t\t\t\tapp.goto(url);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n.dp-product-normal-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.dp-product-normal-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden;}\r\n.dp-product-normal-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\r\n.dp-product-normal-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.dp-product-normal-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}\r\n.dp-product-normal-item .product-info {padding:20rpx 20rpx;position: relative;}\r\n.dp-product-normal-item .product-info .p2 .t1-m {font-size: 32rpx;padding-left: 8rpx;}\r\n.dp-product-normal-item .product-info .p5 {font-size:24rpx;font-weight: bold;margin: 8rpx 0;}\r\n.dp-product-normal-item .product-info .p6{font-size:24rpx;display: flex;flex-wrap: wrap;margin-top: 6rpx;}\r\n.dp-product-normal-item .product-info .p6-m{text-align: center;padding:6rpx 10rpx;border-radius: 6rpx;margin: 6rpx;}\r\n.dp-product-normal-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.dp-product-normal-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\r\n.dp-product-normal-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\r\n.dp-product-normal-item .product-info .p2-1 .t1{font-size:36rpx;}\r\n.dp-product-normal-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.dp-product-normal-item .product-info .p2-1 .t3 {margin-left:10rpx;font-size:22rpx;color: #999;}\r\n.dp-product-normal-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\r\n.dp-product-normal-item .product-info .p3{color:#999999;font-size:20rpx;margin-top:10rpx}\r\n.dp-product-normal-item .product-info .p4{width:52rpx;height:52rpx;border-radius:50%;position:absolute;display:relative;bottom:10rpx;right:20rpx;text-align:center;}\r\n.dp-product-normal-item .product-info .p4 .icon_gouwuche{font-size:30rpx;height:52rpx;line-height:52rpx}\r\n.dp-product-normal-item .product-info .p4 .img{width:100%;height:100%}\r\n.bg-desc {color: #fff; padding: 10rpx 20rpx;}\r\n\r\n.dp-product-normal-item .product-info .binfo {\r\n\t\tpadding-bottom:6rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.dp-product-normal-item .product-info .binfo .t1 {\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.dp-product-normal-item .product-info .binfo .t2 {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: normal;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\t.dp-product-normal-item .product-info .binfo .b2{flex-shrink: 0;}\r\n\t.dp-product-normal-item .product-info .binfo .b1{max-width: 75%;}\r\n\t.dp-product-normal-item .couponitem {\r\n\t\twidth: 100%;\r\n\t\t/* padding: 0 20rpx 20rpx 20rpx; */\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.dp-product-normal-item .couponitem .f1 {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.dp-product-normal-item .couponitem .f1 .t {\r\n\t\tmargin-right: 10rpx;\r\n\t\tborder-radius: 3px;\r\n\t\tfont-size: 22rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tpadding-right: 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\r\n.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}\r\n.field_buy{line-height: 40rpx;border-bottom: 0;padding: 4rpx 0;word-break: break-all;}\r\n.nowbuy{width:160rpx;line-height:60rpx;text-align: center;border-radius: 4rpx;margin-top: 10rpx;}\r\n\r\n.tag_price_name{height:25rpx;line-height:25rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 8rpx;font-size:20rpx;text-overflow: ellipsis;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-item.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-product-item.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839377566\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}