{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/bonuspoolgold/goldwithdraw.vue?4078", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/bonuspoolgold/goldwithdraw.vue?1059", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/bonuspoolgold/goldwithdraw.vue?d6ff", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/bonuspoolgold/goldwithdraw.vue?1125", "uni-app:///adminExt/bonuspoolgold/goldwithdraw.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/bonuspoolgold/goldwithdraw.vue?bf8a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/bonuspoolgold/goldwithdraw.vue?f529"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "userinfo", "sysset", "paytype", "show", "pre_url", "onLoad", "onShow", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "moneyinput", "changeradio", "formSubmit", "console", "money", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwC91B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAE;UACAC;QACA;QACAH;QACAA;QACAA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MACA;QACAH;MACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MACAL;IACA;IACAM;MACA;MACAC;MACA;MACA;MACA;MACA;QACAN;QACA;MACA;MACA;QACAA;QACA;MACA;MACAA;MACAA;QAAAO;QAAAhB;MAAA;QACAS;QACA;UACAA;YACA;UACA;UACA;QACA;UACAA;UACAD;YACAS;cACAR;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAurC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "adminExt/bonuspoolgold/goldwithdraw.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './adminExt/bonuspoolgold/goldwithdraw.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goldwithdraw.vue?vue&type=template&id=cdf8d4c6&\"\nvar renderjs\nimport script from \"./goldwithdraw.vue?vue&type=script&lang=js&\"\nexport * from \"./goldwithdraw.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goldwithdraw.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"adminExt/bonuspoolgold/goldwithdraw.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goldwithdraw.vue?vue&type=template&id=cdf8d4c6&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"金币\") : null\n  var m2 = _vm.isload ? _vm.t(\"金币\") : null\n  var m3 = _vm.isload ? _vm.t(\"金币\") : null\n  var m4 = _vm.isload && _vm.paytype == \"money\" ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goldwithdraw.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goldwithdraw.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t<form @submit=\"formSubmit\">\r\n\t\t<view class=\"mymoney\" :style=\"{background:t('color1')}\">\r\n\t\t\t<view class=\"f1\">可兑换{{t('金币')}}</view>\r\n\t\t\t<view class=\"f2\"><text style=\"font-size:26rpx\">￥</text>{{userinfo.gold}}</view>\r\n\t\t\t<view class=\"f1\">当前{{t('金币')}}价值：{{sysset.gold_price}}</view>\r\n\t\t\t<view class=\"f3\" @tap=\"goto\" data-url=\"goldlog\"><text>{{t('金币')}}记录</text><text class=\"iconfont iconjiantou\" style=\"font-size:20rpx\"></text></view>\r\n\t\t</view>\r\n\t\t<view class=\"content2\">\r\n\t\t\t<view class=\"item2\"><view class=\"f1\">兑换数量</view></view>\r\n\t\t\t<view class=\"item3\"><view class=\"f1\">￥</view><view class=\"f2\"><input class=\"input\" type=\"digit\" name=\"money\" value=\"\" placeholder=\"请输入兑换数量\" placeholder-style=\"color:#999;font-size:40rpx\" @input=\"moneyinput\"/></view></view>\r\n\t\t\t<view class=\"item4\" v-if=\"sysset.cash_fee>0 \">\r\n\t\t\t\t<text v-if=\"sysset.cash_fee>0\">兑换手续费{{sysset.cash_fee}}% </text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"withdrawtype\">\r\n\t\t\t<view class=\"f1\">选择兑换方式：</view>\r\n\t\t\t<view class=\"f2\">\r\n\t\t\t\t<view class=\"item\" @tap.stop=\"changeradio\" data-paytype=\"money\">\r\n\t\t\t\t\t<view class=\"t1\">余额</view>\r\n\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='money' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<button class=\"btn\" form-type=\"submit\" :style=\"{background:t('color1')}\">立即兑换</button>\r\n\t</form>\r\n  <view class=\"withdraw_desc\" v-if=\"sysset.withdraw_desc\">\r\n      <view class=\"title\">说明</view>\r\n      <textarea :value=\"sysset.withdraw_desc\"></textarea>\r\n  </view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\topt:{},\r\n\t\tloading:false,\r\n\t\tisload: false,\r\n\t\tmenuindex:-1,\r\n\t\tuserinfo: [],\r\n\t\tsysset: false,\r\n\t\tpaytype: 'money',\r\n\t\tshow: 0,\r\n\t\tpre_url: app.globalData.pre_url,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n  \tthis.opt = app.getopts(opt);\r\n  },\r\n  onShow: function () {\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminFinance/goldwithdraw', {}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: that.t('金币') + '兑换'\r\n\t\t\t\t});\r\n\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tthat.tmplids = res.tmplids;\r\n\t\t\t\tvar sysset = res.sysset;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    moneyinput: function (e) {\r\n      var usermoney = parseFloat(this.userinfo.gold);\r\n      var money = parseFloat(e.detail.value);\r\n      if (money < 0) {\r\n        app.error('必须大于0');\r\n      } else if (money > usermoney) {\r\n        app.error('可兑换' + this.t('金币') + '不足');\r\n      }\r\n    },\r\n    changeradio: function (e) {\r\n      var that = this;\r\n      var paytype = e.currentTarget.dataset.paytype;\r\n      that.paytype = paytype;\r\n    },\r\n    formSubmit: function (e) {\r\n      var that = this;\r\n\t\t\tconsole.log(e.detail.value)\r\n      var usermoney = parseFloat(this.userinfo.gold);\r\n      var money = parseFloat(e.detail.value.money);\r\n      var paytype = this.paytype;\r\n      if (isNaN(money) || money <= 0) {\r\n        app.error('兑换金额必须大于0');\r\n        return;\r\n      }\r\n      if (money > usermoney) {\r\n        app.error('余额不足');\r\n        return;\r\n      }\r\n\t\tapp.showLoading('提交中');\r\n      app.post('ApiAdminFinance/goldwithdraw', {money: money,paytype: paytype}, function (res) {\r\n\t\tapp.showLoading(false);\r\n        if (res.status == 0) {\r\n\t\t\tapp.alert(res.msg, function () {\r\n\t\t\t\tif(res.url) app.goto(res.url);\r\n\t\t\t});\r\n\t\t\treturn;\r\n        } else {\r\n\t\t\tapp.success(res.msg);\r\n\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t  app.goto('/admin/finance/index');\r\n\t\t\t\t}, 1000);\r\n\t\t\t});\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{display:flex;flex-direction:column}\r\n.mymoney{width:94%;margin:20rpx 3%;border-radius: 10rpx 56rpx 10rpx 10rpx;position:relative;display:flex;flex-direction:column;padding:70rpx 0}\r\n.mymoney .f1{margin:0 0 0 60rpx;color:rgba(255,255,255,0.8);font-size:24rpx;}\r\n.mymoney .f2{margin:20rpx 0 0 60rpx;color:#fff;font-size:64rpx;font-weight:bold}\r\n.mymoney .f3{height:56rpx;padding:0 10rpx 0 20rpx;border-radius: 28rpx 0px 0px 28rpx;background:rgba(255,255,255,0.2);font-size:20rpx;font-weight:bold;color:#fff;display:flex;align-items:center;position:absolute;top:94rpx;right:0}\r\n\r\n.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}\r\n.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}\r\n.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}\r\n.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}\r\n\r\n.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}\r\n.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}\r\n\r\n.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}\r\n.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}\r\n.content2 .item3 .f2{display:flex;align-items:center;font-size:60rpx;color:#333333;font-weight:bold}\r\n.content2 .item3 .f2 .input{font-size:60rpx;height:100rpx;line-height:100rpx;}\r\n.content2 .item4{display:flex;width:94%;margin:0 3%;border-top:1px solid #F0F0F0;height:100rpx;line-height:100rpx;color:#8C8C8C;font-size:28rpx}\r\n\r\n.withdrawtype{width:94%;margin:20rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}\r\n.withdrawtype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}\r\n\r\n\r\n.withdrawtype .f2{padding:0 30rpx}\r\n.withdrawtype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}\r\n.withdrawtype .f2 .item:last-child{border-bottom:0}\r\n.withdrawtype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#333}\r\n.withdrawtype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}\r\n\r\n.withdrawtype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\r\n.withdrawtype .f2 .item .radio .radio-img{width:100%;height:100%}\r\n\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n\r\n.withdraw_desc{padding: 30rpx;}\r\n.withdraw_desc .title{font-size: 30rpx;color: #5E5E5E;font-weight: bold;padding: 10rpx 0;}\r\n.withdraw_desc textarea{width: 100%; line-height: 46rpx;font-size: 24rpx;color: #222222;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goldwithdraw.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goldwithdraw.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839432279\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}