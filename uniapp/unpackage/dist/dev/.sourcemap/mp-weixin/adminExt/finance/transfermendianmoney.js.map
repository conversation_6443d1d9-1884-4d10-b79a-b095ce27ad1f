{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/finance/transfermendianmoney.vue?5bcc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/finance/transfermendianmoney.vue?6b5b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/finance/transfermendianmoney.vue?7d6c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/finance/transfermendianmoney.vue?4605", "uni-app:///adminExt/finance/transfermendianmoney.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/finance/transfermendianmoney.vue?096d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/finance/transfermendianmoney.vue?9896"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "userinfo", "moneyList", "mymoney", "moneySelected", "paypwd", "paycheck", "mid", "mobile", "tourl", "member_info", "member_info2", "pre_url", "money_transfer_type", "onLoad", "uni", "title", "onPullDownRefresh", "methods", "switchMember", "switchMember2", "memberInput", "change<PERSON><PERSON><PERSON>", "that", "app", "changeQuery2", "tel", "getdata", "selectMoney", "mobileinput", "moneyinput", "changeradio", "getpwd", "formSubmit", "money", "clearTimeout", "url", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,6BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AACwE;AACL;AACa;;;AAGhF;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,kzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoDt2B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IAEA;IACA;IACAC;MACAC;IACA;IACA;IACA;EACA;;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACAC;QAAAjB;MAAA;QACAgB;QACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACAF;MACAC;QAAAE;MAAA;QACAH;QACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACAG;MACA;MACAJ;MACAC;QAAAjB;MAAA;QACAgB;QACA;UACAC;UAAA;QACA;QACA;UACAD;UACAA;QACA;QACA;UACAA;QACA;QACA;QACAA;QACAA;MACA;IACA;IACAK;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;MACA;MACAR;IACA;IACAS;MACA;MACA;MACAT;IACA;IACAU;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACAT;QACA;MACA;MAEA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QAAA;MACA;QACAA;QAAA;MACA;MAEAA;QACAA;QACAA;UAAAU;UAAA1B;UAAAD;UAAAF;QAAA;UACAmB;UACA;YACAA;YACA;cACA;gBACAW;gBACApB;kBACAqB;gBACA;cACA;YACA;YACA;UACA;YACAZ;YACAD;cACAc;gBACAb;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3PA;AAAA;AAAA;AAAA;AAA+rC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACAntC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "adminExt/finance/transfermendianmoney.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './adminExt/finance/transfermendianmoney.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./transfermendianmoney.vue?vue&type=template&id=1d944b77&\"\nvar renderjs\nimport script from \"./transfermendianmoney.vue?vue&type=script&lang=js&\"\nexport * from \"./transfermendianmoney.vue?vue&type=script&lang=js&\"\nimport style0 from \"./transfermendianmoney.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"adminExt/finance/transfermendianmoney.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfermendianmoney.vue?vue&type=template&id=1d944b77&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.member_info.id ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && !_vm.member_info.id ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"余额\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfermendianmoney.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfermendianmoney.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t<form @submit=\"formSubmit\" autocomplete=\"off\">\r\n\t\t\r\n\t\t<view class=\"content2\">\r\n\t\t\t<block>\r\n\t\t\t<view class=\"item2\"><view class=\"f1\">对方ID</view></view>\r\n\t\t\t<view class=\"item3\">\r\n\t\t\t\t<view class=\"member-info\" v-if=\"member_info.id\">\r\n\t\t\t\t\t<view class=\"info-view flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"head-img\" :src=\"member_info.headimg\" v-if='member_info.headimg'></image>\r\n\t\t\t\t\t\t<image class=\"head-img\" :src=\"pre_url+'/static/img/wxtx.png'\" v-else></image>\r\n\t\t\t\t\t\t<view class=\"member-text-view\">\r\n\t\t\t\t\t\t\t<view class=\"member-nickname\">{{member_info.nickname}}</view>\r\n\t\t\t\t\t\t\t<view class=\"member-id\">ID：{{member_info.id}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"query-button\" :style=\"{color:t('color1')}\" @click=\"switchMember\">切换</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"member-info\" v-else>\r\n\t\t\t\t\t<input class=\"input\" type=\"number\" name=\"mid\" :value=\"mid\" placeholder=\"请输入对方ID\" placeholder-style=\"color:#999;font-size:36rpx\" @input=\"memberInput\"></input>\r\n\t\t\t\t\t<view class=\"query-button\" :style=\"{color:t('color1')}\" @click=\"changeQuery(mid)\">查询</view<>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item4\" style=\"height: 1rpx;\"></view>\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t<view class=\"item2\"><view class=\"f1\">转账金额</view></view>\r\n\t\t\t<view class=\"item3\"><view class=\"f2\"><input class=\"input\" type=\"number\" name=\"money\" value=\"\" placeholder=\"请输入转账金额\" placeholder-style=\"color:#999;font-size:36rpx\" @input=\"moneyinput\"></input></view>\r\n\t\t\t</view>\r\n      <view class=\"item2\" v-if=\"paycheck\"><view class=\"f1\">支付密码</view></view>\r\n      <view class=\"item3\" v-if=\"paycheck\">\r\n\t\t  <view class=\"f2\">\r\n\t\t\t  <input class=\"input\" type=\"password\" name=\"paypwd\" value=\"\" placeholder=\"请输入支付密码\" placeholder-style=\"color:#999;font-size:36rpx\" @input=\"getpwd\"></input>\r\n\t\t</view>\r\n      </view>\r\n\t\t\t<view class=\"item4\">\r\n\t\t\t\t<text style=\"margin-right:10rpx\" :class=\"mid>0?'redtxt':''\">您的当前{{t('余额')}}：{{mymoney}}，转账后不可退回 </text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<button class=\"btn\" :style=\"{background:t('color1')}\" form-type=\"submit\">转账</button>\r\n\t\t<view class='text-center' @tap=\"goto\" data-url='/admin/finance/index' style=\"margin-top: 40rpx; line-height: 60rpx;\"><text>返回财务首页</text></view>\r\n\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tuserinfo: {},\r\n\t\t\tmoneyList:[],\r\n\t\t\tmymoney: 0,\r\n\t\t\tmoneySelected: '',\r\n\t\t\tpaypwd: '',\r\n\t\t\tpaycheck:false,\r\n\t\t\tmid:'',\r\n\t\t\tmobile:'',\r\n\t\t\ttourl:'/pages/my/usercenter',\r\n\t\t\tmember_info:{},\r\n\t\t\tmember_info2:{},\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tmoney_transfer_type:[]\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.mid = this.opt.mid || '';\r\n\t\tif(this.opt.tourl) this.tourl = decodeURIComponent(this.opt.tourl);\r\n\r\n\t\tvar that = this;\r\n\t\t// app.checkLogin();\r\n\t\tuni.setNavigationBarTitle({\r\n\t\t\ttitle: '转账'\r\n\t\t});\r\n\t\tthis.getdata();\r\n\t\t// this.getpaycheck();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tswitchMember(){\r\n\t\t\tthis.member_info = {};\r\n\t\t\tthis.mid = '';\r\n\t\t},\r\n\t\tswitchMember2(){\r\n\t\t\tthis.member_info2 = {};\r\n\t\t\tthis.mobile = '';\r\n\t\t},\r\n\t\tmemberInput(event){\r\n\t\t\tthis.mid = event.detail.value;\r\n\t\t},\r\n\t\tchangeQuery(mid){\r\n\t\t\tlet that = this;\r\n\t\t\tif(!mid) return app.error('请输入会员ID');\r\n\t\t\tthat.loading = true\r\n\t\t\tapp.get('ApiMy/getMemberBase',{mid:that.mid},function (res) {\r\n\t\t\t\tthat.loading = false\r\n\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\tthat.member_info = res.data;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.error('未查询到此会员！');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tchangeQuery2(mobile){\r\n\t\t\tlet that = this;\r\n\t\t\tif(!mobile) return app.error('请输入手机号');\r\n\t\t\tthat.loading = true\r\n\t\t\tapp.get('ApiMy/getMemberBase',{tel:that.mobile},function (res) {\r\n\t\t\t\tthat.loading = false\r\n\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\tthat.member_info2 = res.data;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.error('未查询到此会员！');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true\r\n\t\t\tapp.get('ApiAdminFinance/transferMendianMoney', {mid:that.mid}, function (res) {\r\n\t\t\t\tthat.loading = false\r\n\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t}\r\n\t\t\t\tif(res.status == 1) {\r\n\t\t\t\t\tthat.mymoney = res.mymoney;\r\n\t\t\t\t\tthat.moneyList = res.moneyList;\r\n\t\t\t\t}\r\n\t\t\t\tif(res.paycheck==1){\r\n\t\t\t\t\tthat.paycheck = true\r\n\t\t\t\t}\r\n\t\t\t\tif(res.money_transfer_type && res.money_transfer_type.length == 0) app.alert('未设置可用的转账方式，请联系客服');\r\n\t\t\t\tthat.money_transfer_type = res.money_transfer_type;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\tselectMoney: function (e) {\r\n\t\t  var money = e.currentTarget.dataset.money;\r\n\t\t  this.moneySelected = money;\r\n\t\t},\r\n\t\tmobileinput: function (e) {\r\n\t\t  var value = e.detail.value;\r\n\t\t\tthis.mobile = value;\r\n\t\t},\r\n\t\t\r\n    moneyinput: function (e) {\r\n      var money = parseFloat(e.detail.value);\r\n    },\r\n    changeradio: function (e) {\r\n      var that = this;\r\n      var paytype = e.currentTarget.dataset.paytype;\r\n      that.paytype = paytype;\r\n    },\r\n    getpwd: function (e) {\r\n      var that = this;\r\n      var paypwd = e.detail.value;\r\n      that.paypwd = paypwd;\r\n    },\r\n    formSubmit: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar money = parseFloat(e.detail.value.money);\r\n\t\t\tif(that.mid>0){\r\n\t\t\t\tvar mid = that.mid;\r\n\t\t\t}else{\r\n\t\t\t\tvar mid = typeof(mid) != 'undefined' ? parseInt(e.detail.value.mid) : e.detail.value.mid;\r\n\t\t\t}\r\n\t\t\tif(that.mobile != ''){\r\n\t\t\t\tvar mobile = that.mobile;\r\n\t\t\t}else{\r\n\t\t\t\tvar mobile = e.detail.value.mobile;\r\n\t\t\t}\r\n\t\t\tvar paypwd = e.detail.value.paypwd;\r\n\t\t\t// if(inArray('tel',that.money_transfer_type) && inArray('tel',that.money_transfer_type))\r\n\t\t\tif (typeof(mobile) != 'undefined' && typeof(mid) != 'undefined' && mobile == '' && (mid == '' || mid == 0 || isNaN(mid))) {\r\n\t\t\t\tapp.error(\"请输入手机号码或接收人ID\");\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (typeof(mobile) != 'undefined' && typeof(mid) == 'undefined' && !app.isPhone(mobile)) {\r\n\t\t\t\tapp.error(\"手机号码有误，请重填\");\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (typeof(mobile) != 'undefined' && mobile != '' && !app.isPhone(mobile)) {\r\n\t\t\t\tapp.error(\"手机号码有误，请重填\");\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (typeof(mid) != 'undefined' && typeof(mobile) == 'undefined' && (mid == '' || mid == 0 || isNaN(mid))) {\r\n\t\t\t\tapp.error(\"请输入接收人ID\");\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif(typeof(mid) != 'undefined' && mid == app.globalData.mid) {\r\n\t\t\t\tapp.error(\"不能转账给自己\");\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (isNaN(money) || money <= 0) {\r\n\t\t\t\tapp.error('转账金额必须大于0');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (this.paycheck && paypwd=='') {\r\n\t\t\t\tapp.error(\"请输入支付密码\");\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\tif (money < 0) {\r\n\t\t\t\tapp.error('转账金额必须大于0');return;\r\n\t\t\t} else if (money > that.mymoney) {\r\n\t\t\t\tapp.error(this.t('余额') + '不足');return;\r\n\t\t\t}\r\n\r\n\t\t\tapp.confirm('确定要转账吗？', function(){\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t\tapp.post('ApiAdminFinance/transferMendianMoney', {money: money,mobile: mobile,mid:mid,paypwd:paypwd}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\tif(data.set_paypwd==1){\r\n\t\t\t\t\t\t\t\tlet timer = setTimeout(function () {\r\n\t\t\t\t\t\t\t\tclearTimeout(timer)\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl:'/pagesExt/my/paypwd'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t} \r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}else {\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\t\tapp.goto(data.url);\r\n\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}, '提交中');\r\n\t\t\t})\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{display:flex;flex-direction:column}\r\n.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}\r\n.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}\r\n.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}\r\n.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}\r\n\r\n.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}\r\n.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}\r\n\r\n.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}\r\n.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}\r\n.content2 .item3 .f2{display:flex;align-items:center;font-size:36rpx;color:#333333;font-weight:bold;flex: 1;}\r\n.content2 .item3 .f2 .input{font-size:36rpx;height:100rpx;line-height:100rpx;width: 100%;}\r\n.content2 .item3 .member-info{display:flex;align-items:center;flex: 1;}\r\n.content2 .item3 .member-info .input{font-size:36rpx;height:100rpx;line-height:100rpx;width: 100%;color:#333333;font-weight:bold;}\r\n.content2 .item3 .member-info .query-button{white-space: nowrap;font-size: 28rpx;border-radius: 8rpx;padding: 5rpx 8rpx;}\r\n.content2 .item3 .member-info .info-view{flex: 1;}\r\n.content2 .item3 .member-info .info-view .head-img{width: 90rpx;height: 90rpx;border-radius: 8rpx;overflow: hidden;}\r\n.content2 .item3 .member-info .info-view .member-text-view{height: 90rpx;padding-left: 20rpx;display: flex;flex-direction: column;align-items: flex-start;justify-content: flex-start;}\r\n.content2 .item3 .member-info .info-view .member-text-view .member-nickname{font-size: 28rpx;color: #333;font-weight: bold;}\r\n.content2 .item3 .member-info .info-view .member-text-view .member-id{font-size: 24rpx;color: #999999;margin-top: 10rpx;}\r\n.content2 .item3 .member-info-oneline .info-view .member-text-view {flex-direction: row; align-items: center;}\r\n\r\n.content2 .item4{display:flex;width:94%;margin:0 3%;border-top:1px solid #F0F0F0;height:100rpx;line-height:100rpx;color:#8C8C8C;font-size:28rpx}\r\n.content2 .redtxt{color: #FC4343;}\r\n.text-center {text-align: center;}\r\n\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n\r\n.giveset{width:100%;padding:20rpx 20rpx 20rpx 20rpx;display:flex;flex-wrap:wrap;justify-content:center}\r\n.giveset .item{margin:10rpx;padding:15rpx 0;width:25%;height:100rpx;background:#FDF6F6;border-radius:10rpx;display:flex;flex-direction:row;align-items:center;justify-content:center}\r\n.giveset .item .t1{color:#545454;font-size:32rpx;}\r\n.giveset .item .t2{color:#8C8C8C;font-size:20rpx;margin-top:6rpx}\r\n.giveset .item.active .t1{color:#fff;font-size:32rpx}\r\n.giveset .item.active .t2{color:#fff;font-size:20rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfermendianmoney.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfermendianmoney.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839432257\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}