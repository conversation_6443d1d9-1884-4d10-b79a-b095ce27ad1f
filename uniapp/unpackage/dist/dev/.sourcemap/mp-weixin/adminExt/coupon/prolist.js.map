{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/prolist.vue?2a95", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/prolist.vue?093b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/prolist.vue?872d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/prolist.vue?9a45", "uni-app:///adminExt/coupon/prolist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/prolist.vue?92f8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/prolist.vue?b879"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pagenum", "nomore", "nodata", "order", "field", "clist", "curIndex", "curIndex2", "datalist", "app", "onLoad", "onPullDownRefresh", "methods", "searchConfirm", "that", "searchproduct", "getprolist", "keyword", "gid", "cid", "cpid", "is_coupon", "addHistory", "historylist", "newhistorylist", "searchChange", "searchbtn", "deleteSearchHistory", "historyClick", "goSearch", "couponAddChange", "uni", "id", "name", "pic", "give_num", "delta", "getdata", "bid", "isget", "getdatalist", "scrolltolower", "changeCTab", "changeOrder", "switchRightTab", "buydialogChange", "toDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiHz1B;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,sDACA,iDACA,yDACA,0DACA,2DAEA,sDACA,yDACA,4DACA,yDACAC;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAA;IACA;IACAC;MACA;MACAD;MACAA;MACAA;MACAA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAF;MACAA;MACAA;MACAA;MACAL;QAAAT;QAAAiB;QAAAb;QAAAD;QAAAe;QAAAC;QAAAC;QAAAC;MAAA;QACAP;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACAf;MACAK;IACA;IACAW;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAZ;MACA;QACA;UACAA;UACAL;QACA;UACAK;UACAL;QACA;MACA;IACA;IACAkB;MACA;MACAb;MACAL;IACA;IACAmB;MACA;MACA;MACA;MACAd;MACAA;IACA;IACA;IACA;IACAe;MACA;IACA;IACAC;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAJ;QACAK;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAvB;MACAA;MACAL;QAAAU;QAAAmB;MAAA;QACA;QACAxB;QACA;UACA;YACA;cACAA;cACAA;YACA;YACA;YACA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACAyB;gBACA;cACA;YACA;YACA;UACA;QACA;QACAzB;QACAA;MACA;IACA;IACA0B;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA1B;MACAA;MACAA;MACAL;QAAAT;QAAAI;QAAAD;QAAAgB;QAAAmB;QAAAlB;QAAAC;MAAA;QACAP;QAEAiB;QAEA;QACA;UACA;YACAjB;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MACA;IAEA;IAEA2B;MAEA;QAEA;QACA;MAEA;IAEA;IAEA;;IAEAC;MAEA;MAEA;MACA;MAEA;MACA;MAEA;MAEA;MAEA;MAEA;MAEA;IAEA;IAEA;;IAEAC;MAEA;MAEA;MACA;MAEA;MACA;MACA;MAEA;IAEA;IAEA;;IAEAC;MAEA;MAEA;MAEA;MAEA;MACA;MACA;MACA;MAEA;MACA;MACA;MAEA;IAEA;IAEAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACArC;MACA;QACA;QACAA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACtaA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "adminExt/coupon/prolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './adminExt/coupon/prolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prolist.vue?vue&type=template&id=a35616be&\"\nvar renderjs\nimport script from \"./prolist.vue?vue&type=script&lang=js&\"\nexport * from \"./prolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"adminExt/coupon/prolist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=template&id=a35616be&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.pageSwitch\n    ? !_vm.history_list || _vm.history_list.length == 0\n    : null\n  var g1 = !_vm.pageSwitch ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l0 =\n    !_vm.pageSwitch && g1\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.t(\"color1\")\n          var m1 = _vm.t(\"color1\")\n          var m2 = _vm.t(\"color1rgb\")\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var m3 = _vm.pageSwitch ? _vm.t(\"color1\") : null\n  var l1 = _vm.pageSwitch\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m4 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m4: m4,\n        }\n      })\n    : null\n  var g2 = _vm.pageSwitch\n    ? _vm.curIndex > -1 && _vm.clist[_vm.curIndex].child.length > 0\n    : null\n  var m5 = _vm.pageSwitch && g2 && _vm.curIndex2 == -1 ? _vm.t(\"color1\") : null\n  var m6 =\n    _vm.pageSwitch && g2 && _vm.curIndex2 == -1 ? _vm.t(\"color1rgb\") : null\n  var l2 =\n    _vm.pageSwitch && g2\n      ? _vm.__map(_vm.clist[_vm.curIndex].child, function (item, idx2) {\n          var $orig = _vm.__get_orig(item)\n          var m7 = _vm.curIndex2 == idx2 ? _vm.t(\"color1\") : null\n          var m8 = _vm.curIndex2 == idx2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m7: m7,\n            m8: m8,\n          }\n        })\n      : null\n  var l3 = _vm.pageSwitch\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m9 = _vm.t(\"color1\")\n        var m10 = _vm.t(\"color1\")\n        var m11 = _vm.t(\"color1rgb\")\n        return {\n          $orig: $orig,\n          m9: m9,\n          m10: m10,\n          m11: m11,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m3: m3,\n        l1: l1,\n        g2: g2,\n        m5: m5,\n        m6: m6,\n        l2: l2,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<!-- 搜索 -->\r\n\t<block v-if=\"!pageSwitch\">\r\n\t\t<view class=\"search-container-search\" :style=\"history_show?'height:100%;':''\">\r\n\t\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\r\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的商品\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-btn\" @tap=\"searchbtn\">\r\n\t\t\t\t\t<text>搜索</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"search-history\" v-show=\"history_show\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<text class=\"search-history-title\">最近搜索</text>\r\n\t\t\t\t\t<view class=\"delete-search-history\" @tap=\"deleteSearchHistory\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/del.png'\" style=\"width:36rpx;height:36rpx\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-history-list\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in history_list\" :key=\"index\" class=\"search-history-item\" :data-value=\"item\" @tap=\"historyClick\">{{item}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"!history_list || history_list.length==0\" class=\"flex-y-center\"><image :src=\"pre_url+'/static/img/tanhao.png'\" style=\"width:36rpx;height:36rpx;margin-right:10rpx\"/>暂无记录\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"product-container\">\r\n\t\t\t<block v-if=\"datalist && datalist.length>0\">\r\n\t\t\t\t\t<view class=\"product-itemlist\">\r\n\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"toDetail\" :data-type=\"item.type?item.type:0\" :data-id=\"item.id\" >\r\n\t\t\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\" :style=\"{color:t('color1')}\">{{item.sell_price}}<text style=\"font-size:24rpx;padding-left:2px\">元/{{item.danwei}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"addbut\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"couponAddChange(item)\">添加</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\r\n\t\t</view>\r\n\t</block>\r\n\t<!-- 列表 -->\r\n\t<block v-if=\"pageSwitch\">\r\n\t\t<view @tap.stop=\"goSearch\"class=\"search-container\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\r\n\t\t\t\t<view class=\"search-text\">搜索感兴趣的商品</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"content-container\">\r\n\t\t\t<view class=\"nav_left\">\r\n\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == -1 ? 'active' : '')\" @tap=\"switchRightTab\" data-index=\"-1\" data-id=\"0\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>全部</view>\r\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == index ? 'active' : '')\" @tap=\"switchRightTab\" :data-index=\"index\" :data-id=\"item.id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav_right\">\r\n\t\t\t\t<view class=\"nav_right-content\">\r\n\t\t\t\t\t<view class=\"classify-ul\" v-if=\"curIndex>-1 && clist[curIndex].child.length>0\">\r\n\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\r\n\t\t\t\t\t\t <view class=\"classify-li\" :style=\"curIndex2==-1?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeCTab\" :data-id=\"clist[curIndex].id\" data-index=\"-1\">全部</view>\r\n\t\t\t\t\t\t <block v-for=\"(item, idx2) in clist[curIndex].child\" :key=\"idx2\">\r\n\t\t\t\t\t\t <view class=\"classify-li\" :style=\"curIndex2==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeCTab\" :data-id=\"item.id\" :data-index=\"idx2\">{{item.name}}</view>\r\n\t\t\t\t\t\t </block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\r\n\t\t\t\t\t\t<view class=\"product-itemlist\">\r\n\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"toDetail\" :data-type=\"item.type?item.type:0\" :data-id=\"item.id\" >\r\n\t\t\t\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t1\" :style=\"{color:t('color1')}\">{{item.sell_price}}<text style=\"font-size:24rpx;padding-left:2px\">元/{{item.danwei}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"addbut\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"couponAddChange(item)\">添加</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\r\n\t\t\t\t\t\t<nodata text=\"暂无相关商品\" v-if=\"nodata\"></nodata>\r\n\t\t\t\t\t\t<view style=\"width:100%;height:100rpx\"></view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\"></buydialog>\r\n\t</block>\r\n\t<loading v-if=\"loading\" loadstyle=\"left:62.5%\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpagenum: 1,\r\n\t\t\tnomore: false,\r\n\t\t\tnodata: false,\r\n\t\t\torder: '',\r\n\t\t\tfield: '',\r\n\t\t\tclist: [],\r\n\t\t\tcurIndex: -1,\r\n\t\t\tcurIndex2: -1,\r\n\t\t\tdatalist: [],\r\n\t\t\tnodata: false,\r\n\t\t\tcurCid: 0,\r\n\t\t\tproid:0,\r\n\t\t\tbuydialogShow: false,\r\n\t\t\tpageSwitch:true,\r\n\t\t\t// \r\n\t\t\thistory_show: true,\r\n\t\t\tkeyword: '',\r\n\t\t\thistory_list: [],\r\n\t\t\tproductlisttype: 'item2',\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t};\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tmethods: {\r\n\t\tsearchConfirm: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var keyword = e.detail.value;\r\n\t\t  that.keyword = keyword\r\n\t\t  that.searchproduct();\r\n\t\t},\r\n\t\tsearchproduct: function () {\r\n\t\t  var that = this;\r\n\t\t  that.pagenum = 1;\r\n\t\t  that.datalist = [];\r\n\t\t  that.addHistory();\r\n\t\t  that.getprolist();\r\n\t\t},\r\n\t\tgetprolist: function () {\r\n\t\t  var that = this;\r\n\t\t  var pagenum = that.pagenum;\r\n\t\t  var keyword = that.keyword;\r\n\t\t  var order = that.order;\r\n\t\t  var field = that.field;\r\n\t\t  var gid = that.gid;\r\n\t\t  var cid = that.cid;\r\n\t\t  var cpid = that.cpid;\r\n\t\t  that.history_show = false;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t  that.nomore = false;\r\n\t\t  app.post('ApiYuyue/getprolist',{pagenum: pagenum,keyword: keyword,field: field,order: order,gid: gid,cid: cid,cpid:cpid,is_coupon:1}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t    var data = res.data;\r\n\t\t    if (pagenum == 1) {\r\n\t\t      that.datalist = data;\r\n\t\t      if (data.length == 0) {\r\n\t\t        that.nodata = true;\r\n\t\t      }\r\n\t\t    }else{\r\n\t\t      if (data.length == 0) {\r\n\t\t        that.nomore = true;\r\n\t\t      } else {\r\n\t\t        var datalist = that.datalist;\r\n\t\t        var newdata = datalist.concat(data);\r\n\t\t        that.datalist = newdata;\r\n\t\t      }\r\n\t\t    }\r\n\t\t  });\r\n\t\t},\r\n\t\taddHistory: function () {\r\n\t\t  var that = this;\r\n\t\t  var keyword = that.keyword;\r\n\t\t  if (app.isNull(keyword)) return;\r\n\t\t  var historylist = app.getCache('search_history_list');\r\n\t\t  if (app.isNull(historylist)) historylist = [];\r\n\t\t  historylist.unshift(keyword);\r\n\t\t  var newhistorylist = [];\r\n\t\t  for (var i in historylist) {\r\n\t\t    if (historylist[i] != keyword || i == 0) {\r\n\t\t      newhistorylist.push(historylist[i]);\r\n\t\t    }\r\n\t\t  }\r\n\t\t  if (newhistorylist.length > 5) newhistorylist.splice(5, 1);\r\n\t\t  app.setCache('search_history_list', newhistorylist);\r\n\t\t  that.history_list = newhistorylist\r\n\t\t},\r\n\t\tsearchChange: function (e) {\r\n\t\t  this.keyword = e.detail.value;\r\n\t\t  if (e.detail.value == '') {\r\n\t\t    this.history_show = true;\r\n\t\t    this.datalist = [];\r\n\t\t  }\r\n\t\t},\r\n\t\tsearchbtn: function () {\r\n\t\t  var that = this;\r\n\t\t  if (that.history_show) {\r\n\t\t    var keyword = that.keyword;\r\n\t\t    that.searchproduct();\r\n\t\t  } else {\r\n\t\t    if (that.productlisttype == 'itemlist') {\r\n\t\t      that.productlisttype = 'item2';\r\n\t\t      app.setCache('productlisttype', 'item2');\r\n\t\t    } else {\r\n\t\t      that.productlisttype = 'itemlist';\r\n\t\t      app.setCache('productlisttype', 'itemlist');\r\n\t\t    }\r\n\t\t  }\r\n\t\t},\r\n\t\tdeleteSearchHistory: function () {\r\n\t\t  var that = this;\r\n\t\t  that.history_list = null;\r\n\t\t  app.removeCache(\"search_history_list\");\r\n\t\t},\r\n\t\thistoryClick: function (e){\r\n\t\t  var that = this;\r\n\t\t  var keyword = e.currentTarget.dataset.value;\r\n\t\t  if (keyword.length == 0) return;\r\n\t\t  that.keyword = keyword;\r\n\t\t  that.searchproduct();\r\n\t\t},\r\n\t\t// \r\n\t\t// 搜索商品\r\n\t\tgoSearch(){\r\n\t\t\tthis.pageSwitch = !this.pageSwitch;\r\n\t\t},\r\n\t\tcouponAddChange(item){\r\n\t\t\tuni.$emit('shopDataEmitS',{id:item.id,name:item.name,pic:item.pic,give_num:1});\r\n\t\t\tuni.navigateBack({\r\n\t\t\t\tdelta: 1\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar nowcid = that.opt.cid;\r\n\t\t\tif (!nowcid) nowcid = '';\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tthat.pagenum = 1;\r\n\t\t\tthat.datalist = [];\r\n\t\t\tapp.get('ApiYuyue/classify', {cid:nowcid,bid:bid}, function (res) {\r\n\t\t\t  var clist = res.data;\r\n\t\t\t  that.clist = clist;\r\n\t\t\t  if (nowcid) {\r\n\t\t\t    for (var i = 0; i < clist.length; i++) {\r\n\t\t\t      if (clist[i]['id'] == nowcid) {\r\n\t\t\t        that.curIndex = i;\r\n\t\t\t        that.curCid = nowcid;\r\n\t\t\t      }\r\n\t\t\t      var downcdata = clist[i]['child'];\r\n\t\t\t      var isget = 0;\r\n\t\t\t      for (var j = 0; j < downcdata; j++) {\r\n\t\t\t        if (downcdata[j]['id'] == nowcid) {\r\n\t\t\t          that.curIndex = i;\r\n\t\t\t          that.curIndex2 = j;\r\n\t\t\t          that.curCid = nowcid;\r\n\t\t\t          isget = 1;\r\n\t\t\t          break;\r\n\t\t\t        }\r\n\t\t\t      }\r\n\t\t\t      if (isget) break;\r\n\t\t\t    }\r\n\t\t\t  }\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tthat.getdatalist();\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar cid = that.curCid;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tvar cpid = that.opt.cpid ? that.opt.cpid : '';\r\n\t\t\tvar order = that.order;\r\n    \r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tapp.post('ApiYuyue/getprolist', {pagenum: pagenum,field: field,order: order,cid: cid,bid:bid,cpid:cpid,is_coupon:1}, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\tif(pagenum == 1){\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\tthat.datalist = newdata;\r\n\t\t\t});\r\n \r\n\t\t},\r\n   \r\n\t\tscrolltolower: function () {\r\n     \r\n\t\t\tif (!this.nomore) {\r\n   \r\n\t\t\t\tthis.pagenum = this.pagenum + 1;    \r\n\t\t\t\tthis.getdatalist(true);\r\n \r\n\t\t\t}\r\n \r\n\t\t},\r\n    \r\n\t\t//改变子分类\r\n    \r\n\t\tchangeCTab: function (e) {\r\n    \r\n\t\t\tvar that = this;\r\n\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\r\n\t\t\tthis.curIndex2 = index;\r\n\t\t\tthis.nodata = false;\r\n\r\n\t\t\tthis.curCid = id;\r\n\r\n\t\t\tthis.pagenum = 1;\r\n\r\n\t\t\tthis.datalist = [];\r\n\r\n\t\t\tthis.nomore = false;\r\n\r\n\t\t\tthis.getdatalist();\r\n\r\n\t\t},\r\n    \r\n\t\t//改变排序规则\r\n\r\n\t\tchangeOrder: function (e) {\r\n    \r\n\t\t\tvar t = e.currentTarget.dataset;\r\n  \r\n\t\t\tthis.field = t.field; \r\n\t\t\tthis.order = t.order;\r\n \r\n\t\t\tthis.pagenum = 1;\r\n\t\t\tthis.datalist = []; \r\n\t\t\tthis.nomore = false;\r\n\t\t\t\r\n\t\t\tthis.getdatalist();\r\n  \r\n\t\t},\r\n   \r\n\t\t//事件处理函数\r\n \r\n\t\tswitchRightTab: function (e) {\r\n  \r\n\t\t\tvar that = this;\r\n    \r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n   \r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n  \r\n\t\t\tthis.curIndex = index;\r\n\t\t\tthis.curIndex2 = -1;\r\n\t\t\tthis.nodata = false;\r\n\t\t\tthis.curCid = id\r\n;\r\n\t\t\tthis.pagenum = 1; \r\n\t\t\tthis.datalist = [];\r\n\t\t\tthis.nomore = false;\r\n  \r\n\t\t\tthis.getdatalist();\r\n \r\n\t\t}\r\n,\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\ttoDetail(e){\r\n\t\t\treturn;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar type = e.currentTarget.dataset.type;\r\n\t\t\tif(type == 0){\r\n\t\t\t\tapp.goto('/activity/yuyue/product?id='+id);\r\n\t\t\t}else{\r\n\t\t\t\tvar prodata = id;\r\n\t\t\t\tapp.goto('/activity/yuyue/buy?prodata=' + prodata);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\npage {height:100%;}\r\n.container{width: 100%;height:100%;max-width:640px;background-color: #fff;color: #939393;display: flex;flex-direction:column}\r\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\r\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\r\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\r\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\r\n\r\n.content-container{flex:1;height:100%;display:flex;overflow: hidden;}\r\n\r\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\r\n.nav_left .nav_left_items{line-height:50rpx;color:#999999;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\r\n.nav_left .nav_left_items.active{background: #fff;color:#222222;font-size:28rpx;font-weight:bold}\r\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\r\n.nav_left .nav_left_items.active .before{display:block}\r\n\r\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}\r\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%}\r\n.nav-pai{ width: 100%;display:flex;align-items:center;justify-content:center;}\r\n.nav-paili{flex:1; text-align:center;color:#323232; font-size:28rpx;font-weight:bold;position: relative;height:80rpx;line-height:80rpx;}\r\n.nav-paili .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.nav-paili .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n\r\n.classify-ul{width:100%;height:100rpx;padding:0 10rpx;}\r\n.classify-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:22rpx;color:#6C737F;font-size:20rpx;text-align: center;height:44rpx; line-height:44rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\r\n\r\n.classify-box{padding: 0 0 20rpx 0;width: 100%;height:calc(100% - 60rpx);overflow-y: scroll; border-top:1px solid #F5F6F8;}\r\n.classify-box .nav_right_items{ width:100%;border-bottom:1px #f4f4f4 solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\r\n\r\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\r\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\r\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\r\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:30rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:60rpx}\r\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\r\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;}\r\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\r\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}\r\n.product-itemlist .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\r\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}\r\n.addbut{width:88rpx;height:60rpx;border-radius:30rpx;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;font-size: 24rpx;\r\nline-height:60rpx;color: #fff;}\r\n/*  */\r\n.search-container-search {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}\r\n.topsearch{width:100%;padding:16rpx 20rpx;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n.topsearch .search-btn{display:flex;align-items:center;color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}\r\n.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}\r\n.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}\r\n\r\n.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}\r\n.search-history {padding: 24rpx 34rpx;}\r\n.search-history .search-history-title {color: #666;}\r\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\r\n.search-history-list {padding: 24rpx 0 0 0;}\r\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\r\n\r\n.filter-scroll-view{margin-top:var(--window-top)}\r\n.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}\r\n.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}\r\n.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}\r\n.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}\r\n.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}\r\n.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}\r\n.search-filter button .icon{margin-top:6rpx;height:54rpx;}\r\n.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between}\r\n.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}\r\n.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}\r\n\r\n.product-container {width: 100%;margin-top: 120rpx;font-size:26rpx;padding:0 24rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839432348\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}