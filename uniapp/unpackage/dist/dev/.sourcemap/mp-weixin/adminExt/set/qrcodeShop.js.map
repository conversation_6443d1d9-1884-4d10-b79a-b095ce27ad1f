{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/set/qrcodeShop.vue?fd00", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/set/qrcodeShop.vue?9ffa", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/set/qrcodeShop.vue?f835", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/set/qrcodeShop.vue?fcf9", "uni-app:///adminExt/set/qrcodeShop.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/set/qrcodeShop.vue?76ff", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/set/qrcodeShop.vue?b6ba"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "onLoad", "methods", "getdata", "that", "app", "savpic", "console", "savpic2", "uni", "url", "success", "filePath", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8B51B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAH;IACA;EACA;EACAI;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;QACAA;MACA;IACA;IACAE;MAEA;MACAC;MACA;QACAF;QAAA;MACA;MACA;IACA;IACAG;MACA;MACAH;MACAI;QACAC;QACAC;UACA;YACAF;cACAG;cACAD;gBACAN;cACA;cACAQ;gBACAN;gBACAF;gBACAA;cACA;YACA;UACA;QACA;QACAQ;UACAN;UACAF;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "adminExt/set/qrcodeShop.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './adminExt/set/qrcodeShop.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./qrcodeShop.vue?vue&type=template&id=0fe140b0&\"\nvar renderjs\nimport script from \"./qrcodeShop.vue?vue&type=script&lang=js&\"\nexport * from \"./qrcodeShop.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qrcodeShop.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"adminExt/set/qrcodeShop.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcodeShop.vue?vue&type=template&id=0fe140b0&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.data && _vm.data.url_h5 ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.data && _vm.data.qrcode_h5 ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.data && _vm.data.qrcode_wx ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload && _vm.data && _vm.data.qrcode_alipay ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcodeShop.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcodeShop.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"row\" v-if=\"data && data.url_h5\">\r\n\t\t\t<view class=\"title\">链接</view>\r\n\t\t\t<view>{{data.url_h5}}</view>\r\n\t\t\t<view class=\"btn\" @tap=\"goto\" :data-url=\"'copy::'+data.url_h5\" :style=\"{background:t('color1')}\">复制</view>\r\n\t\t</view>\r\n\t\t<view class=\"row\" v-if=\"data && data.qrcode_h5\">\r\n\t\t\t<view class=\"title\">二维码</view>\r\n\t\t\t<image :src=\"data.qrcode_h5\" style=\"width:60%\" @tap=\"previewImage\" :data-url=\"poster\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"btn\" @tap=\"savpic\" :data-pic=\"data.qrcode_h5\" :style=\"{background:t('color1')}\">下载</view>\r\n\t\t</view>\r\n\t\t<view class=\"row\" v-if=\"data && data.qrcode_wx\">\r\n\t\t\t<view class=\"title\">微信小程序码</view>\r\n\t\t\t<image :src=\"data.qrcode_wx\" style=\"width:60%\" @tap=\"previewImage\" :data-url=\"poster\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"btn\" @tap=\"savpic\" :data-pic=\"data.qrcode_wx\" :style=\"{background:t('color1')}\">下载</view>\r\n\t\t</view>\r\n\t\t<view class=\"row\" v-if=\"data && data.qrcode_alipay\">\r\n\t\t\t<view class=\"title\">支付宝小程序码</view>\r\n\t\t\t<image :src=\"data.qrcode_alipay\" style=\"width:60%\" @tap=\"previewImage\" :data-url=\"poster\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"btn\" @tap=\"savpic\" :data-pic=\"data.qrcode_alipay\" :style=\"{background:t('color1')}\">下载</view>\r\n\t\t</view>\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n      data: {},\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminIndex/qrcodeShop', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.data = res;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\r\n\t\tsavpic:function(e){\r\n\t\r\n\t\t\tvar pics = e.currentTarget.dataset.pic;\r\n\t\t\tconsole.log(pics);\r\n\t\t\tif(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){\r\n\t\t\t\tapp.error('请长按图片保存');return;\r\n\t\t\t}\r\n\t\t\tthis.savpic2(pics);\r\n\t\t},\r\n\t\tsavpic2:function(pic){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading('图片保存中');\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: pic,\r\n\t\t\t\tsuccess (res) {\r\n\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\tsuccess:function () {\r\n\t\t\t\t\t\t\t\tapp.success('已保存到相册');\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail:function(failres){\r\n\t\t\t\t\t\t\t\tconsole.log(failres);\r\n\t\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\t\tapp.error('保存失败');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail:function(failres){\r\n\t\t\t\t\tconsole.log(failres);\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.error('下载失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\n\t}\n};\r\n</script>\r\n<style>\n .container {width: 100%;padding:30rpx; text-align:center;}\r\n .row {margin-top:30rpx;padding:20rpx; border-radius:20rpx;word-break: break-all;background-color:#fff;}\r\n .title{margin:20rpx 0;font-size:32rpx;font-weight: bold;}\r\n .btn {margin: 16rpx auto 0;width:270rpx;height:70rpx;line-height:70rpx; text-align:center;color:#fff; border-radius:12rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcodeShop.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcodeShop.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839432243\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}