{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/businessFenxiao/mendianlist.vue?8716", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/businessFenxiao/mendianlist.vue?e2fc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/businessFenxiao/mendianlist.vue?8f44", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/businessFenxiao/mendianlist.vue?f349", "uni-app:///pagesA/businessFenxiao/mendianlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/businessFenxiao/mendianlist.vue?94c4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/businessFenxiao/mendianlist.vue?1b04"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "datalist", "pagenum", "type", "member", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsC71B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACAC;MACAA;MACAA;MACA;MACAC;QAAAT;QAAAC;MAAA;QACAO;QACA;QACA;UACAA;UACA;YACAA;UACA;UACAA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/businessFenxiao/mendianlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/businessFenxiao/mendianlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mendianlist.vue?vue&type=template&id=1855983e&\"\nvar renderjs\nimport script from \"./mendianlist.vue?vue&type=script&lang=js&\"\nexport * from \"./mendianlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mendianlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/businessFenxiao/mendianlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mendianlist.vue?vue&type=template&id=1855983e&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mendianlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mendianlist.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t<view v-if=\"isload\">\r\n\t\t<view class=\"bg-view\"></view>\r\n\t\t<view class=\"content-view flex-col\">\r\n\t\t\t<view class=\"info-view\">\r\n\t\t\t\t<view class=\"image-view\">\r\n\t\t\t\t\t<image :src=\"member.headimg\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-view-text flex-col\">\r\n\t\t\t\t\t<view class=\"title-text\">{{member.nickname}}</view>\r\n\t\t\t\t\t<view class=\"inventory-text\">{{member.tel}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"but-class\">签章</view>\r\n\t\t\t\t<view class=\"job-class\">项目经理</view> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-view-data flex-col\">\r\n\t\t\t\t<view class=\"title-text\">门店列表</view>\r\n\t\t\t\t<view class=\"options-view flex-bt\" v-for=\"(item,index) in datalist\" :key=\"index\" @click=\"goto\" :data-url=\"'/pagesA/businessFenxiao/mendian?bid='+item.id\">\r\n\t\t\t\t\t<view class=\"image-view\">\r\n\t\t\t\t\t\t<image :src=\"item.logo\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-view-data flex-col\">\r\n\t\t\t\t\t\t<view class=\"title-text\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"inventory-text\">{{item.stage}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t</view>\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t</view>\n</template>\n\n<script>\r\n\tvar app = getApp();\r\n\texport default{\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\topt:{},\r\n\t\t\t\tloading:false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex:-1,\r\n\t\t\t\t\r\n\t\t\t\tnodata:false,\r\n\t\t\t\tnomore:false,\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\ttype:0,\r\n\t\t\t\tmember:{}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.type = this.opt.type || 0;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonReachBottom: function () {\r\n\t\t\tif (!this.nomore && !this.nodata) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getdata(true);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata: function (loadmore) {\r\n\t\t\t\tif(!loadmore){\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tvar pagenum = that.pagenum;\r\n\t\t\t\tapp.post('ApiBusinessFenxiao/mendianlists', {pagenum: that.pagenum,type:that.type}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.member = res.member;\r\n\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\n</script>\n\n<style>\r\n\t.bg-view{width: 150%;background: #eb8c2b;height: 500rpx;border-radius: 50%;position: relative;top:-260rpx;left: 50%;transform: translateX(-50%);z-index: 1;}\r\n\t.content-view{width: 90%;height: auto;position: absolute;top:30rpx;z-index:2;left: 50%;transform: translateX(-50%);}\r\n\t.info-view{background: #fff;height:200rpx;width: 100%;margin:20rpx auto;padding:30rpx 40rpx;align-items: center;display: flex;align-items: center;justify-content: flex-start;border-radius: 16rpx;\r\n\tposition: relative;}\r\n\t.info-view .image-view{width: 128rpx;height: 128rpx;background: #F5F7F9;border-radius: 50%;}\r\n\t.info-view .image-view image{width: 100%;height: 100%;}\r\n\t.info-view .info-view-text{padding: 0rpx 20rpx;}\r\n\t.info-view .info-view-text .title-text{font-size: 30rpx;color: #3A4463;font-weight: bold;}\r\n\t.info-view .info-view-text .inventory-text{color: rgba(58, 68, 99, 0.5);font-size: 24rpx;padding-top: 20rpx;}\r\n\t.info-view .but-class{width: 110rpx;height: 50rpx;line-height: 50rpx;color: #fff;border-radius:8rpx;text-align: center;font-size: 24rpx;background-color: #eb8c2b;\r\n\tposition: absolute;right: 40rpx;bottom: 50rpx;}\r\n\t.job-class{position: absolute;right: 50rpx;top: 20rpx;font-size: 24rpx;font-weight: bold;color: #333;}\r\n\t.content-view-data{width: 100%;height: auto;}\r\n\t.content-view-data .title-text{font-size: 24rpx;font-weight: bold;color: #333;padding: 15rpx 20rpx;}\r\n\t.options-view{background: #fff;width: 100%;margin-bottom:5rpx;padding: 15rpx 20rpx;align-items: center;display: flex;align-items: center;justify-content: space-between;\r\n\tmargin-top: 15rpx;border-radius: 16rpx;box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(0,0,0,.1);}\r\n\t.options-view .image-view{width: 128rpx;height: 128rpx;background: #F5F7F9;border-radius: 12rpx;}\r\n\t.options-view .image-view image{width: 100%;height: 100%;}\r\n\t.info-view-data{width: 75%;}\r\n\t.info-view-data .title-text{font-size: 30rpx;font-family:500;color: #3A4463;padding: 10rpx 0rpx;}\r\n\t.info-view-data .inventory-text{color: rgba(58, 68, 99, 0.5);font-size: 24rpx;padding: 10rpx 0rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mendianlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mendianlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839414456\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}