{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/agent/memberPriceRate.vue?c111", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/agent/memberPriceRate.vue?aeab", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/agent/memberPriceRate.vue?1198", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/agent/memberPriceRate.vue?63f0", "uni-app:///pagesA/agent/memberPriceRate.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/agent/memberPriceRate.vue?d812", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/agent/memberPriceRate.vue?b73d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "tlid", "pre_url", "rate", "color1", "cateArr", "clist", "cindex", "onLoad", "uni", "title", "that", "onPullDownRefresh", "getdata", "app", "console", "cateChange", "postdata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA60B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0Bj2B;AAAA;EAEAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IACA;IACA;IAEAC;IACAA;IACAA;EACA;EACAC,iDAEA;AAAA,6EACA,8BAEA,oEACA;EACAC;IACA;IACAF;IACAG;MACA;QACA;QAEA;UACAH;QACA;QACA;QACA;QACA;UACA,2CACAA;UACAN;QACA;QACAU;QACAJ;QACAA;QAEAA;MACA;QACAG;QACA;MACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAH;IACAA;MAAA;IAAA;MACAA;MACA;QACAA;MACA;QACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAA0rC,CAAgB,0mCAAG,EAAC,C;;;;;;;;;;;ACA9sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/agent/memberPriceRate.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/agent/memberPriceRate.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./memberPriceRate.vue?vue&type=template&id=ccf91a7e&\"\nvar renderjs\nimport script from \"./memberPriceRate.vue?vue&type=script&lang=js&\"\nexport * from \"./memberPriceRate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./memberPriceRate.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/agent/memberPriceRate.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./memberPriceRate.vue?vue&type=template&id=ccf91a7e&\"", "var components\ntry {\n  components = {\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./memberPriceRate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./memberPriceRate.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <block v-if=\"isload\">\r\n      <view class=\"head\" :style=\"'background: '+color1\"></view>\r\n      <view style=\"width: 700rpx;margin: 0 auto;background-color: #fff;;padding: 20rpx;margin-top: -60rpx;border-radius: 12rpx;\">\r\n        <view class=\"content_css flex\" >\r\n          <view class=\"left\">\r\n            等级价格倍率：\r\n          </view>\r\n\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t<picker class=\"picker\" @change=\"cateChange\" :value=\"cindex\" :range=\"cateArr\">\r\n\t\t\t\t\t\t\t<view v-if=\"cindex\">{{cateArr[cindex]}}</view>\r\n\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n        </view>\r\n        \r\n      </view>\r\n      <button class=\"btn\" @tap=\"postdata\" :style=\"'background: '+color1\">提交</button>\r\n      \r\n    </block>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n        opt:{},\r\n        loading:false,\r\n        isload: false,\r\n        menuindex:-1,\r\n        tlid:0,\r\n        pre_url:app.globalData.pre_url,\r\n\r\n        rate:'',\r\n        color1:'',\r\n\t\t\t\tcateArr: [],\r\n\t\t\t\tclist:[],\r\n\t\t\t\tcindex: 0,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tuni.setNavigationBarTitle({\r\n\t\t\ttitle: '等级价格倍率'\r\n\t\t});\r\n    var that = this;\r\n\t\tvar opt = app.getopts(opt);\r\n\r\n    that.opt = opt;\r\n    that.color1 = app.t('color1');\r\n    that.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\r\n\t},\r\n  onPullDownRefresh: function () {\r\n\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n      app.get('ApiAgent/memberPriceRate', {}, function (res) {\r\n      \tif (res.status == 1) {\r\n          var data = res;\r\n          \r\n          if(!that.color1 && res._initdata){\r\n            that.color1 = res._initdata.color1;\r\n          }\r\n          var clist = res.levellist;\r\n          var cateArr = [];\r\n          for (var i in clist) {\r\n\t\t\t\t\t\tif(clist[i].id == res.levelid_price_rate)\r\n\t\t\t\t\t\t\tthat.cindex = i;\r\n          \tcateArr.push(clist[i].name+'('+clist[i].price_rate+')');\r\n          }\r\n\t\t\t\t\tconsole.log('cateArr',cateArr);\r\n\t\t\t\t\tthat.cateArr = cateArr;\n\t\t\t\t\tthat.clist = res.levellist\r\n\r\n          that.loaded();\r\n      \t}else{\r\n      \t\tapp.alert(res.msg);\r\n      \t\treturn;\r\n      \t}\r\n      });\r\n\t\t},\r\n\t\tcateChange: function (e) {\r\n\t\t  this.cindex = e.detail.value;\r\n\t\t},\r\n    postdata:function(e){\r\n    \tvar that = this\r\n      // var rate = that.rate;\r\n      // if(!rate || rate < 0){\r\n      //   app.alert('请输入正确的数值');\r\n      //   return;\r\n      // }\r\n\t\t\t\r\n\t\t\tvar levelid = that.clist[that.cindex].id;\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiAgent/memberPriceRate', {'levelid':levelid}, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t});\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage{background:#f1f1f1;width: 100%;height: 100%;}\r\n.head{width: 100%;height: 160rpx;}\r\n.content_css{overflow: hidden;line-height: 80rpx;border-bottom: 2rpx solid #E7E7E7;}\r\n.left{width: 220rpx;}\r\n.right{width: 480rpx;line-height:80rpx;height:80rpx}\r\n.scan{background-color:#fff;height: 54rpx;width: 54rpx;float: right;padding: 2rpx;overflow: hidden;margin-top: 10rpx;margin-right: 20rpx;}\r\n.btn{width: 400rpx;color: #fff;line-height: 100rpx;height: 100rpx;text-align: center;margin:0 auto;margin-top: 60rpx;border-radius: 100rpx;}\r\n.content_css2{border: 0;border-top: 2rpx solid #E7E7E7;}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./memberPriceRate.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./memberPriceRate.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839422704\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}