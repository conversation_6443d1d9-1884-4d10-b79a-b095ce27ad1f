{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderdetail.vue?66b7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderdetail.vue?a1dc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderdetail.vue?33fe", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderdetail.vue?a5ed", "uni-app:///pagesA/cashier/orderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderdetail.vue?c904", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderdetail.vue?0d34"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "prodata", "djs", "iscommentdp", "detail", "payorder", "prolist", "shopset", "storeinfo", "lefttime", "codtxt", "pay_transfer_info", "invoice", "selectExpressShow", "express_content", "fromfenxiao", "hexiao_code_member", "showprice_dollar", "hexiao_qr", "selecthxnumDialogShow", "hxogid", "hxnum", "hxnumlist", "storelist", "storeshowall", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "set_hexiao_code_member", "<PERSON><PERSON><PERSON>", "orderid", "setTimeout", "getdjs", "todel", "toclose", "orderCollect", "businessType", "extraData", "merchant_id", "merchant_trade_no", "transaction_id", "success", "console", "fail", "complete", "showhxqr", "closeHxqr", "showhxqr2", "uni", "itemList", "gethxqr", "hxnumRadioChange", "hideSelecthxnumDialog", "openLocation", "latitude", "longitude", "name", "address", "scale", "openMendian", "logistics", "express_oglist", "hideSelectExpressDialog", "doStoreShowAll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwM71B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA,oCACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;QAEA;UACAC;YACA;YACA;YACAD;YACAA;YACA;YACA;cACA;gBACA;gBACAR;cACA;YACA;YACAA;cACA;YACA;YACA;cACA;gBACAA;cACA;YACA;YACAQ;UACA;QACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;MAEAL;MACAC;QAAAK;QAAArB;MAAA;QACAe;QACA;UACAC;UAAA;QACA;QACAA;QACAD;QACAO;UACAP;QACA;MACA;IACA;IACAQ;MACA;MACA;MAEA;QACAR;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAS;MACA;MACA;MACAR;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAN;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACAT;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;MACAV;QACAA;QACA;UACAA;YAAAK;UAAA;YACAL;YACA;cAAAA;cAAA;YAAA,OACA;cACA;gBACA1C;kBACAqD;kBACAC;oBACAC;oBACAC;oBACAC;kBACA;kBACAC;oBACA;oBACAC;oBACAA;oBACAjB;sBAAAK;oBAAA;sBACAL;sBACAA;sBACAM;wBACAP;sBACA;oBACA;kBACA;kBACAmB;oBACA;oBACAD;oBACAA;kBACA;kBACAE;oBACA;kBAAA;gBAEA;cACA;gBACA;gBACAnB;gBACAiB;cACA;YACA;UACA;QACA;UACAjB;YAAAK;UAAA;YACAL;YACAA;YACAM;cACAP;YACA;UACA;QACA;MAEA;IACA;IACAqB;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAtB;QAAA;MACA;MACA;MACA;QACAV;MACA;MACA;QACAS;QACAA;QACAA;MACA;QACAwB;UACAC;UACAR;YACA;cACAjB;cACAA;YACA;UACA;QACA;MACA;IACA;IACA0B;MACA;MACA;MACA;MACA;QACAzB;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAA;MACAA;QAAAZ;QAAAC;MAAA;QACAW;QACA;UACAA;QACA;UACAD;UACAA;QACA;MACA;IACA;IACA2B;MACA;MACA;MACA;MACApB;QACAP;QACAA;MACA;IACA;IACA4B;MACA;IACA;IACAC;MACA;MACA;MACA;MACAL;QACAM;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACAlC;IACA;IACAmC;MACA;MACA;MACA;MACA;MACA;MACAlB;MACA;QACAjB;MACA;QACAlB;QACA;UACA;YACA;YACAmC;YACA;YACA;cACA;gBACAmB;cACA;YACA;YACAtD;UACA;QACA;QACA;QACA;MACA;IACA;IACAuD;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7hBA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/cashier/orderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/cashier/orderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetail.vue?vue&type=template&id=67fecf4f&\"\nvar renderjs\nimport script from \"./orderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/cashier/orderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=template&id=67fecf4f&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m2 = _vm.isload && _vm.detail.scoredk_money > 0 ? _vm.t(\"积分\") : null\n  var m3 = _vm.isload && _vm.detail.dec_money > 0 ? _vm.t(\"余额\") : null\n  var m4 =\n    _vm.isload && _vm.detail.refund_money > 0\n      ? _vm.dateFormat(_vm.detail.refund_time)\n      : null\n  var m5 = _vm.isload && _vm.detail.hexiao_code_member ? _vm.t(\"color1\") : null\n  var l0 =\n    _vm.isload && _vm.selecthxnumDialogShow\n      ? _vm.__map(_vm.hxnumlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.hxnum == item ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m6: m6,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url('+(pre_url + '/static/img/ordertop.png')+');background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">结算中</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\n\t\t\t\t<view class=\"t1\">已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">挂单中</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==10\">\n\t\t\t\t<view class=\"t1\">已退款</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"product\">\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"box\">\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\">\n\t\t\t\t\t\t<image :src=\"item.propic\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t<text class=\"t1\">{{item.proname}}</text>\n\t\t\t\t\t\t<view class=\"t2 flex flex-y-center flex-bt\">\n\t\t\t\t\t\t\t<text>{{item.ggname}}</text>\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && item.iscomment==0 && shopset.comment==1\" @tap.stop=\"goto\" :data-url=\"'comment?ogid=' + item.id\">去评价</view>\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && item.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?ogid=' + item.id\">查看评价</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"t3\" ><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\">×{{item.num}}</text></view>\n\t\t\t\t\t\t<!-- <view class=\"t4 flex flex-x-bottom\">\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && item.iscomment==0 && shopset.comment==1\" @tap.stop=\"goto\" :data-url=\"'comment?ogid=' + item.id\">去评价</view>\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && item.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?ogid=' + item.id\">查看评价</view>\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t<block v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1 && item.hexiao_code\">\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"showhxqr2\" :data-id=\"item.id\" :data-num=\"item.num\" :data-hxnum=\"item.hexiao_num\" :data-hexiao_code=\"item.hexiao_code\" style=\"position:absolute;top:20rpx;right:0rpx;\">核销码</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.paytype\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status ==2\">\n\t\t\t\t<text class=\"t1\">挂单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.hangup_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">商品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.pre_totalprice}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\n            <view class=\"item\" v-if=\"detail.dec_money > 0\">\n            \t<text class=\"t1\">{{t('余额')}}抵扣</text>\n            \t<text class=\"t2 red\">-¥{{detail.dec_money}}</text>\n            </view>\n\t\t\t<view class=\"item\" v-if=\"detail.moling_money > 0\">\n\t\t\t\t<text class=\"t1\">抹零</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.moling_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\"><text v-if=\"showprice_dollar && detail.usd_totalprice>0\">${{detail.usd_totalprice}}</text>  ¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" >\n\t\t\t\t<text class=\"t1\">收银员</text>\n\t\t\t\t<text class=\"t2 \">{{detail.admin_user}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">结算中</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">已完成</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">挂单中</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==10\">已退款</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\" v-if=\"detail.refund_money>0\">\n\t\t\t\t<text class=\"t1\">已退款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_money>0\">\n\t\t\t\t<text class=\"t1\">退款时间</text>\n\t\t\t\t<text class=\"t2 red\">{{dateFormat(detail.refund_time)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">已退款</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:calc(160rpx + env(safe-area-inset-bottom));\"></view>\n\n\t\t<view class=\"bottom notabbarbot\" v-if=\"fromfenxiao==0\">\n\t\t\t<block v-if=\"detail.payaftertourl && detail.payafterbtntext\">\n\t\t\t\t<view style=\"position:relative\">\n\t\t\t\t\t<block v-if=\"detail.payafter_username\">\n\t\t\t\t\t\t<view class=\"btn2\">{{detail.payafterbtntext}}</view>\n\t\t\t\t\t\t<!-- #ifdef H5 -->\n\t\t\t\t\t\t<wx-open-launch-weapp :username=\"detail.payafter_username\" :path=\"detail.payafter_path\" style=\"position:absolute;top:0;left:0;right:0;bottom:0;z-index:8\">\n\t\t\t\t\t\t\t<script type=\"text/wxtag-template\">\n\t\t\t\t\t\t\t\t<div style=\"width:100%;height:40px;\"></div>\n\t\t\t\t\t\t\t</script>\n\t\t\t\t\t\t</wx-open-launch-weapp>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"detail.payaftertourl\">{{detail.payafterbtntext}}</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\n\t\t</view>\n\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\n\t\t\t<view class=\"hxqrbox\">\n\t\t\t\t<image :src=\"hexiao_qr\" @tap=\"previewImage\" :data-url=\"hexiao_qr\" class=\"img\"/>\n\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\n\t\t\t\t<view v-if=\"detail.hexiao_code_member\">\n\t\t\t\t\t<input type=\"number\" placeholder=\"请输入核销密码\" @input=\"set_hexiao_code_member\" style=\"border: 1px #eee solid;padding: 10rpx;margin:20rpx 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t<button @tap=\"hexiao\" class=\"btn\" :style=\"{background:t('color1')}\">确定</button>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t\n\t\t<uni-popup id=\"dialogSelectExpress\" ref=\"dialogSelectExpress\" type=\"dialog\">\n\t\t\t<view style=\"background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx\" v-if=\"express_content\">\n\t\t\t\t<view class=\"sendexpress\" v-for=\"(item, index) in express_content\" :key=\"index\" style=\"border-bottom: 1px solid #f5f5f5;padding:20rpx 0;\">\n\t\t\t\t\t<view class=\"sendexpress-item\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" style=\"display: flex;\">\n\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#121212\">{{item.express_com}} - {{item.express_no}}</view>\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.express_oglist\" style=\"margin-top:20rpx\">\n\t\t\t\t\t\t<view class=\"oginfo-item\" v-for=\"(item2, index2) in item.express_oglist\" :key=\"index2\" style=\"display: flex;align-items:center;margin-bottom:10rpx\">\n\t\t\t\t\t\t\t<image :src=\"item2.pic\" style=\"width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0\"/>\n\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#555\">{{item2.name}}({{item2.ggname}})</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<view v-if=\"selecthxnumDialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideSelecthxnumDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择核销数量</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideSelecthxnumDialog\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in hxnumlist\" :key=\"index\" @tap=\"hxnumRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item}}</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"hxnum==item ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      prodata: '',\n      djs: '',\n      iscommentdp: \"\",\n      detail: \"\",\n\t\t\tpayorder:{},\n      prolist: \"\",\n      shopset: \"\",\n      storeinfo: \"\",\n      lefttime: \"\",\n      codtxt: \"\",\n\t\t\tpay_transfer_info:{},\n\t\t\tinvoice:0,\n\t\t\tselectExpressShow:false,\n\t\t\texpress_content:'',\n\t\t\tfromfenxiao:0,\n\t\t\thexiao_code_member:'',\n\t\t\tshowprice_dollar:false,\n\t\t\thexiao_qr:'',\n\t\t\tselecthxnumDialogShow:false,\n\t\t\thxogid:'',\n\t\t\thxnum:'',\n\t\t\thxnumlist:[],\n\t\t\tstorelist:[],\n\t\t\tstoreshowall:false,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tif (this.opt && this.opt.fromfenxiao && this.opt.fromfenxiao == '1'){\n\t\t  this.fromfenxiao = 1;\n    }\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiOrder/getCashierOrderDetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.iscommentdp = res.iscommentdp,\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.prolist = res.prolist;\n\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\tthat.lefttime = res.lefttime;\n\t\t\t\tthat.codtxt = res.codtxt;\n\t\t\t\tthat.pay_transfer_info =  res.pay_transfer_info;\n\t\t\t\tthat.payorder = res.payorder;\n\t\t\t\tthat.invoice = res.invoice;\n\t\t\t\tthat.storelist = res.storelist || [];\n\t\t\t\tthat.showprice_dollar = res.showprice_dollar\n\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\n\t\t\t\tif (that.detail.mdid == -1 && that.storelist) {\n\t\t\t\t\tapp.getLocation(function(res) {\n\t\t\t\t\t\tvar latitude = res.latitude;\n\t\t\t\t\t\tvar longitude = res.longitude;\n\t\t\t\t\t\tthat.latitude = latitude;\n\t\t\t\t\t\tthat.longitude = longitude;\n\t\t\t\t\t\tvar storelist = that.storelist;\n\t\t\t\t\t\tfor (var x in storelist) {\n\t\t\t\t\t\t\tif (latitude && longitude && storelist[x].latitude && storelist[x].longitude) {\n\t\t\t\t\t\t\t\tvar juli = that.getDistance(latitude, longitude,storelist[x].latitude, storelist[x].longitude);\n\t\t\t\t\t\t\t\tstorelist[x].juli = juli;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstorelist.sort(function(a, b) {\n\t\t\t\t\t\t\treturn a[\"juli\"] - b[\"juli\"];\n\t\t\t\t\t\t});\n\t\t\t\t\t\tfor (var x in storelist) {\n\t\t\t\t\t\t\tif (storelist[x].juli) {\n\t\t\t\t\t\t\t\tstorelist[x].juli = '距离'+storelist[x].juli + '千米';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.storelist = storelist;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tset_hexiao_code_member:function(e){\n\t\t\tthis.hexiao_code_member = e.detail.value;\n\t\t},\n\t\thexiao: function () {\n\t\t\tlet that = this;\n\t\t\t\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiOrder/hexiao', {orderid: that.opt.id,hexiao_code_member:that.hexiao_code_member}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif(res.status != 1){\n\t\t\t\t\tapp.error(res.msg);return;\n\t\t\t\t}\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tthat.closeHxqr();\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t  that.getdata();\n\t\t\t\t}, 1000);\n\t\t\t});\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n    todel: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiOrder/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n      });\n    },\n    toclose: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiOrder/closeOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n    orderCollect: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要收货吗?', function () {\n\t\t\t\tapp.showLoading('收货中');\n\t\t\t\tif(app.globalData.platform == 'wx' && that.detail.wxpaylog && that.detail.wxpaylog.is_upload_shipping_info == 1){\n\t\t\t\t\tapp.post('ApiOrder/orderCollectBefore', {orderid: orderid}, function (data) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tif(data.status != 1){app.error(data.msg);return;}\n\t\t\t\t\t\telse{\n\t\t\t\t\t\t\tif (wx.openBusinessView) {\n\t\t\t\t\t\t\t  wx.openBusinessView({\n\t\t\t\t\t\t\t    businessType: 'weappOrderConfirm',\n\t\t\t\t\t\t\t    extraData: {\n\t\t\t\t\t\t\t      merchant_id: that.detail.wxpaylog.mch_id,\n\t\t\t\t\t\t\t      merchant_trade_no: that.detail.wxpaylog.ordernum,\n\t\t\t\t\t\t\t      transaction_id: that.detail.wxpaylog.transaction_id\n\t\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t    success(res) {\n\t\t\t\t\t\t\t      //dosomething\n\t\t\t\t\t\t\t\t\t\tconsole.log('openBusinessView success')\n\t\t\t\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\t\t\t\tapp.post('ApiOrder/orderCollect', {orderid: orderid}, function (data2) {\n\t\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t\t\tapp.success(data2.msg);\n\t\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t    fail(err) {\n\t\t\t\t\t\t\t      //dosomething\n\t\t\t\t\t\t\t\t\t\tconsole.log('openBusinessView fail')\n\t\t\t\t\t\t\t\t\t\tconsole.log(err)\n\t\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t    complete() {\n\t\t\t\t\t\t\t      //dosomething\n\t\t\t\t\t\t\t    }\n\t\t\t\t\t\t\t  });\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t  //引导用户升级微信版本\n\t\t\t\t\t\t\t\tapp.error('请升级微信版本');\n\t\t\t\t\t\t\t\tconsole.log('openBusinessView error')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t\tapp.post('ApiOrder/orderCollect', {orderid: orderid}, function (data) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n\t\t\t\t}\n        \n      });\n    },\n\t\tshowhxqr:function(e){\n\t\t\tthis.hexiao_qr = e.currentTarget.dataset.hexiao_qr\n\t\t\tthis.$refs.dialogHxqr.open();\n\t\t},\n\t\tcloseHxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.close();\n\t\t},\n\t\tshowhxqr2:function(e){\n      var that = this;\n\t\t\tvar leftnum = e.currentTarget.dataset.num - e.currentTarget.dataset.hxnum;\n\t\t\tthis.hxogid = e.currentTarget.dataset.id;\n\t\t\tif(leftnum <= 0){\n\t\t\t\tapp.alert('没有剩余核销数量了');return;\n\t\t\t}\n\t\t\tvar hxnumlist = [];\n\t\t\tfor(var i=0;i<leftnum;i++){\n\t\t\t\thxnumlist.push((i+1)+'');\n\t\t\t}\n      if (hxnumlist.length > 6) {\n\t\t\t\tthat.hxnumlist = hxnumlist;\n        that.selecthxnumDialogShow = true;\n        that.hxnum = '';\n      } else {\n        uni.showActionSheet({\n          itemList: hxnumlist,\n          success: function (res) {\n\t\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\t\tthat.hxnum = hxnumlist[res.tapIndex];\n\t\t\t\t\t\t\tthat.gethxqr();\n\t\t\t\t\t\t}\n          }\n        });\n      }\n\t\t},\n\t\tgethxqr(){\n      var that = this;\n\t\t\tvar hxnum = this.hxnum;\n\t\t\tvar hxogid = this.hxogid;\n\t\t\tif(!hxogid){\n\t\t\t\tapp.alert('请选择要核销的商品');return;\n\t\t\t}\n\t\t\tif(!hxnum){\n\t\t\t\tapp.alert('请选择核销数量');return;\n\t\t\t}\n\t\t\tapp.showLoading();\n\t\t\tapp.post('ApiOrder/getproducthxqr', {hxogid: hxogid,hxnum:hxnum}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif(data.status == 0){\n\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t}else{\n\t\t\t\t\tthat.hexiao_qr = data.hexiao_qr\n\t\t\t\t\tthat.$refs.dialogHxqr.open();\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    hxnumRadioChange: function (e) {\n      var that = this;\n      var index = e.currentTarget.dataset.index;\n\t\t\tthis.hxnum = this.hxnumlist[index];\n\t\t\tsetTimeout(function(){\n\t\t\t\tthat.selecthxnumDialogShow = false;\n\t\t\t\tthat.gethxqr();\n\t\t\t},200)\n    },\n\t\thideSelecthxnumDialog:function(){\n\t\t\tthis.selecthxnumDialogShow = false;\n\t\t},\n\t\topenLocation:function(e){\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\n\t\t\tvar address = e.currentTarget.dataset.address;\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t address:address,\n\t\t\t scale: 13\n\t\t\t})\n\t\t},\n\t\topenMendian: function(e) {\n\t\t\tvar storeinfo = e.currentTarget.dataset.storeinfo;\n\t\t\tapp.goto('/pages/shop/mendian?id=' + storeinfo.id);\n\t\t},\n\t\tlogistics:function(e){\n\t\t\tvar express_com = e.currentTarget.dataset.express_com\n\t\t\tvar express_no = e.currentTarget.dataset.express_no\n\t\t\tvar express_content = e.currentTarget.dataset.express_content\n\t\t\tvar express_type = e.currentTarget.dataset.express_type\n\t\t\tvar prolist = this.prolist;\n\t\t\tconsole.log(express_content)\n\t\t\tif(!express_content){\n\t\t\t\tapp.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);\n\t\t\t}else{\n\t\t\t\texpress_content = JSON.parse(express_content);\n\t\t\t\tfor(var i in express_content){\n\t\t\t\t\tif(express_content[i].express_ogids){\n\t\t\t\t\t\tvar express_ogids = (express_content[i].express_ogids).split(',');\n\t\t\t\t\t\tconsole.log(express_ogids);\n\t\t\t\t\t\tvar express_oglist = [];\n\t\t\t\t\t\tfor(var j in prolist){\n\t\t\t\t\t\t\tif(app.inArray(prolist[j].id+'',express_ogids)){\n\t\t\t\t\t\t\t\texpress_oglist.push(prolist[j]);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\texpress_content[i].express_oglist = express_oglist;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.express_content = express_content;\n\t\t\t\tthis.$refs.dialogSelectExpress.open();\n\t\t\t}\n\t\t},\n\t\thideSelectExpressDialog:function(){\n\t\t\tthis.$refs.dialogSelectExpress.close();\n\t\t},\n\t\tdoStoreShowAll:function(){\n\t\t\tthis.storeshowall = true;\n\t\t},\n  }\n};\n</script>\n<style>\n\t.text-min { font-size: 24rpx; color: #999;}\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.product .box{width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content{display:flex;position:relative;}\n.product .box:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n\n.product .content .detail .t1{font-size:26rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{color: #999;font-size: 26rpx;margin-top: 10rpx;}\n.product .content .detail .t3{display:flex;color: #ff4246;margin-top: 10rpx;}\n.product .content .detail .t4{margin-top: 10rpx;}\n\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .t3{ margin-top: 3rpx;}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%;height:calc(92rpx + env(safe-area-inset-bottom));background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;padding: 0 20rpx;}\n\n.btn { border-radius: 10rpx;color: #fff;}\n.btn1{height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;flex-shrink: 0;margin: 0 0 0 15rpx;padding: 0 15rpx;}\n.btn2{height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;padding: 0 15rpx;flex-shrink: 0;margin: 0 0 0 15rpx;}\n.btn3{font-size:24rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;padding: 0 15rpx;flex-shrink: 0;margin: 0 0 0 15rpx;}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.hxqrbox .img{width:400rpx;height:400rpx}\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n\n.glassitem{background:#f5f5f5;display: flex;align-items: center;padding: 10rpx 0;font-size: 24rpx;}\n.glassitem .gcontent{flex:1;padding: 0 20rpx;}\n.glassheader{line-height: 50rpx;font-size: 26rpx;font-weight: 600;}\n.glassrow{line-height: 40rpx;font-size: 26rpx;}\n.glassrow .glasscol{min-width: 25%;text-align: center;}\n.glassitem .bt{border-top:1px solid #e3e3e3}\n\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.pstime-item .radio .radio-img{width:100%;height:100%}\n.pdl10{padding-left: 10rpx;}\n\n.radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;padding:20rpx 20rpx;border-bottom:1px dotted #f1f1f1}\n.radio-item:last-child {border: 0}\n.radio-item .f1 {color: #333;font-size:30rpx;flex: 1}\n.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}\n.refundtips{background: #fff9ed; color: #ff5c5c;}\n.refundtips textarea{font-size: 24rpx;line-height: 40rpx;width: 100%;height: auto; word-wrap : break-word;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839417785\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}