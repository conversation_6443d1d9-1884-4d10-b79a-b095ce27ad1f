{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderlist.vue?4585", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderlist.vue?f4cc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderlist.vue?0783", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderlist.vue?e74f", "uni-app:///pagesA/cashier/orderlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderlist.vue?49a5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/cashier/orderlist.vue?d472"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "codtxt", "canrefund", "express_content", "selectExpressShow", "hexiao_qr", "keyword", "showprice_dollar", "app", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "getdata", "that", "changetab", "uni", "scrollTop", "duration", "toclose", "orderid", "setTimeout", "todel", "orderCollect", "businessType", "extraData", "merchant_id", "merchant_trade_no", "transaction_id", "success", "console", "fail", "complete", "logistics", "express_oglist", "hideSelectExpressDialog", "showhxqr", "closeHxqr", "searchConfirm", "showhxqr2", "hxnumlist", "itemList", "gethxqr", "hxogid", "hxnum", "hxnumRadioChange", "hideSelecthxnumDialog"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClGA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsI31B;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,oDACA,kEACA,sDACA,kDACA,sDACA,oDACAC;EAEA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAT;QAAAZ;QAAAE;QAAAQ;MAAA;QACAW;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAC;MACA;QACAV;QAAA;MACA;MACA;MACAW;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAd;QACAA;QACAA;UAAAe;QAAA;UACAf;UACAA;UACAgB;YACAP;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;MACAjB;QACAA;QACAA;UAAAe;QAAA;UACAf;UACAA;UACAgB;YACAP;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACA;MACA;MACAlB;QACAA;QACA;UACAA;YAAAe;UAAA;YACAf;YACA;cAAAA;cAAA;YAAA,OACA;cACA;gBACAtB;kBACAyC;kBACAC;oBACAC;oBACAC;oBACAC;kBACA;kBACAC;oBACA;oBACAC;oBACAA;oBACAzB;sBAAAe;oBAAA;sBACAf;sBACAA;sBACAgB;wBACAP;sBACA;oBACA;kBACA;kBACAiB;oBACA;oBACAD;oBACAA;kBACA;kBACAE;oBACA;kBAAA;gBAEA;cACA;gBACA;gBACA3B;gBACAyB;cACA;YACA;UACA;QACA;UACAzB;YAAAe;UAAA;YACAf;YACAA;YACAgB;cACAP;YACA;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAH;MACA;QACAzB;MACA;QACAL;QACA;UACA;YACA;YACA8B;YACA;YACA;cACA;gBACAI;cACA;YACA;YACAlC;UACA;QACA;QACA;QACA8B;QACA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAlC;QAAA;MACA;MACA;MACA;QACAmC;MACA;MACA;QACA1B;QACAA;QACAA;MACA;QACAE;UACAyB;UACAZ;YACA;cACAf;cACAA;YACA;UACA;QACA;MACA;IACA;IACA4B;MACA;MACA;MACA;MACA;QACArC;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAA;MACAA;QAAAsC;QAAAC;MAAA;QACAvC;QACA;UACAA;QACA;UACAS;UACAA;QACA;MACA;IACA;IACA+B;MACA;MACA;MACA;MACAxB;QACAP;QACAA;MACA;IACA;IACAgC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvaA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/cashier/orderlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/cashier/orderlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderlist.vue?vue&type=template&id=5bc47a9c&\"\nvar renderjs\nimport script from \"./orderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./orderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/cashier/orderlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=template&id=5bc47a9c&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = [1, 2, 3].includes(item.status) && item.invoice\n        var m0 =\n          item.bid > 0 && item.status == 3 && item.iscommentdp == 0\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          m0: m0,\n        }\n      })\n    : null\n  var l1 =\n    _vm.isload && _vm.selecthxnumDialogShow\n      ? _vm.__map(_vm.hxnumlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.hxnum == item ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<dd-tab :itemdata=\"['全部','已完成','已退款']\" :itemst=\"['all','1','10']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\n\t\t<view style=\"width:100%;height:100rpx\"></view>\n\t\t<!-- #ifndef H5 || APP-PLUS -->\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\n\t\t\t</view>\n\t\t</view>\n\t\t<!--  #endif -->\n\t\t<view class=\"order-content\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'detail?id=' + item.id\">\n\t\t\t\t\t<view class=\"head\">\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.bid!=0\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.bid\"><image :src=\"pre_url+'/static/img/ico-shop.png'\"></image> {{item.binfo.name}}</view>\n\t\t\t\t\t\t<view class=\"f1\" v-else><image :src=\"item.binfo.logo\" class=\"logo-row\"></image> {{item.binfo.name}}</view>\n\t\t\t\t\t\t<view class=\"flex1\">\n\t\t\t\t\t\t\t\t<text style=\"color:orangered; margin-left: 10rpx;\" v-if=\"item.yuding_type && item.yuding_type =='1'\">[预定订单]</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <text v-if=\"item.status==0\" class=\"st0\">结算中</text> -->\n\t\t\t\t\t\t<text v-if=\"item.status==1\" class=\"st3\">已完成</text>\n\t\t\t\t\t\t<!-- <text v-if=\"item.status==2\" class=\"st2\">挂单中</text> -->\n\t\t\t\t\t\t<text v-if=\"item.status==10\" class=\"st4\">已退款</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<block v-for=\"(item2, idx) in item.prolist\" :key=\"idx\">\n\t\t\t\t\t\t<view class=\"content\" :style=\"idx+1==item.procount?'border-bottom:none':''\">\n\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/pages/shop/product?id=' + item2.proid\">\n\t\t\t\t\t\t\t\t<image :src=\"item2.propic\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item2.proname}}</text>\n\t\t\t\t\t\t\t\t<text class=\"t2\">{{item2.ggname}}</text>\n\t\t\t\t\t\t\t\t<block>\n\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"item2.product_type && item2.product_type==2\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">{{item2.real_sell_price}}元/斤</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.real_total_weight}}斤</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-else>\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\"><text v-if=\"showprice_dollar && item2.usd_sellprice\">${{item2.usd_sellprice}} </text>￥{{item2.sell_price}}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.num}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-if=\"(item.status==1 || item.status==2) && item.freight_type==1 && item2.hexiao_code\">\n\t\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"showhxqr2\" :data-id=\"item2.id\" :data-num=\"item2.num\" :data-hxnum=\"item2.hexiao_num\" :data-hexiao_code=\"item2.hexiao_code\" style=\"position:absolute;top:40rpx;right:0rpx;\">核销码</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t\t<view>共计{{item.procount}}件商品 实付:￥{{item.totalprice}}  <text v-if=\"item.balance_price > 0 && item.balance_pay_status == 0\"  style=\"display: block; float: right;\">尾款：￥{{item.balance_price}}</text></view>\n\t\t\t\t\t\t<!-- <text v-if=\"item.refund_status==1\" style=\"color:red;padding-left:6rpx\">退款中￥{{item.refund_money}}</text> -->\n\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red;padding-left:6rpx\">已退款￥{{item.refund_money}}</text>\n\t\t\t\t\t\t<!-- <text v-if=\"item.refund_status==3\" style=\"color:red;padding-left:6rpx\">退款申请已驳回</text> -->\n\t\t\t\t\t\t\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"op\">\n\t\t\t\t\t\t<block v-if=\"([1,2,3]).includes(item.status) && item.invoice\">\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'invoice?type=shop&orderid=' + item.id\">发票</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/pagesA/cashier/orderdetail?id=' + item.id\" class=\"btn2\">详情</view>\n\n\t\t\t\t\t\t<block v-if=\"(item.status==1 || item.status==2) && item.freight_type==1 && item.hexiao_qr\">\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"showhxqr\" :data-hexiao_qr=\"item.hexiao_qr\">核销码</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view v-if=\"item.refundCount\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundlist?orderid='+ item.id\">查看退款</view>\n\t\t\t\t\t\t<block v-if=\"item.status==3 || item.status==4\">\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"todel\" :data-id=\"item.id\">删除订单</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.bid>0 && item.status==3\">\n\t\t\t\t\t\t\t<view v-if=\"item.iscommentdp==0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/order/commentdp?orderid=' + item.id\">评价店铺</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t<nodata v-if=\"nodata\"></nodata>\n\n\t\t\n\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\n\t\t\t<view class=\"hxqrbox\">\n\t\t\t\t<image :src=\"hexiao_qr\" @tap=\"previewImage\" :data-url=\"hexiao_qr\" class=\"img\"/>\n\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\n\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<uni-popup id=\"dialogSelectExpress\" ref=\"dialogSelectExpress\" type=\"dialog\">\n\t\t\t<view style=\"background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx\" v-if=\"express_content\">\n\t\t\t\t<view class=\"sendexpress\" v-for=\"(item, index) in express_content\" :key=\"index\" style=\"border-bottom: 1px solid #f5f5f5;padding:20rpx 0;\">\n\t\t\t\t\t<view class=\"sendexpress-item\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" style=\"display: flex;\">\n\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#121212\">{{item.express_com}} - {{item.express_no}}</view>\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.express_oglist\" style=\"margin-top:20rpx\">\n\t\t\t\t\t\t<view class=\"oginfo-item\" v-for=\"(item2, index2) in item.express_oglist\" :key=\"index2\" style=\"display: flex;align-items:center;margin-bottom:10rpx\">\n\t\t\t\t\t\t\t<image :src=\"item2.pic\" style=\"width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0\"/>\n\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#555\">{{item2.name}}({{item2.ggname}})</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t<view v-if=\"selecthxnumDialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideSelecthxnumDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择核销数量</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideSelecthxnumDialog\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in hxnumlist\" :key=\"index\" @tap=\"hxnumRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item}}</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"hxnum==item ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n\t\t\tnodata:false,\n      codtxt: \"\",\n\t\t\tcanrefund:1,\n\t\t\texpress_content:'',\n\t\t\tselectExpressShow:false,\n\t\t\thexiao_qr:'',\n\t\t\tkeyword:'',\n\t\t\tshowprice_dollar:false,\n\t\t\thexiao_qr:'',\n\t\t\tselecthxnumDialogShow:false,\n\t\t\thxogid:'',\n\t\t\thxnum:'',\n\t\t\thxnumlist:[],\n\t\t\tpre_url: app.globalData.pre_url,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tif(this.opt && this.opt.st){\n\t\t\tthis.st = this.opt.st;\n\t\t}\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n\tonNavigationBarSearchInputConfirmed:function(e){\n\t\tthis.searchConfirm({detail:{value:e.text}});\n\t},\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\tthat.loading = true;\n      app.post('ApiOrder/getCashierOrder', {st: st,pagenum: pagenum,keyword:that.keyword}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.codtxt = res.codtxt;\n\t\t\t\t\tthat.canrefund = res.canrefund;\n\t\t\t\t\tthat.showprice_dollar = res.showprice_dollar\n\t\t\t\t\tthat.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    changetab: function (st) {\n\t\t\tif(st == 5){\n\t\t\t\tapp.goto('refundlist');return;\n\t\t\t}\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    toclose: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiOrder/closeOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n    todel: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiOrder/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n    orderCollect: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar orderinfo = that.datalist[index];\n      app.confirm('确定要收货吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tif(app.globalData.platform == 'wx' && orderinfo.wxpaylog && orderinfo.wxpaylog.is_upload_shipping_info == 1){\n\t\t\t\t\tapp.post('ApiOrder/orderCollectBefore', {orderid: orderid}, function (data) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tif(data.status != 1){app.error(data.msg);return;}\n\t\t\t\t\t\telse{\n\t\t\t\t\t\t\tif (wx.openBusinessView) {\n\t\t\t\t\t\t\t  wx.openBusinessView({\n\t\t\t\t\t\t\t    businessType: 'weappOrderConfirm',\n\t\t\t\t\t\t\t    extraData: {\n\t\t\t\t\t\t\t      merchant_id: orderinfo.wxpaylog.mch_id,\n\t\t\t\t\t\t\t      merchant_trade_no: orderinfo.wxpaylog.ordernum,\n\t\t\t\t\t\t\t      transaction_id: orderinfo.wxpaylog.transaction_id\n\t\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t    success(res) {\n\t\t\t\t\t\t\t      //dosomething\n\t\t\t\t\t\t\t\t\t\tconsole.log('openBusinessView success')\n\t\t\t\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\t\t\t\tapp.post('ApiOrder/orderCollect', {orderid: orderid}, function (data2) {\n\t\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t\t\tapp.success(data2.msg);\n\t\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t    fail(err) {\n\t\t\t\t\t\t\t      //dosomething\n\t\t\t\t\t\t\t\t\t\tconsole.log('openBusinessView fail')\n\t\t\t\t\t\t\t\t\t\tconsole.log(err)\n\t\t\t\t\t\t\t    },\n\t\t\t\t\t\t\t    complete() {\n\t\t\t\t\t\t\t      //dosomething\n\t\t\t\t\t\t\t    }\n\t\t\t\t\t\t\t  });\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t  //引导用户升级微信版本\n\t\t\t\t\t\t\t\tapp.error('请升级微信版本');\n\t\t\t\t\t\t\t\tconsole.log('openBusinessView error')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t\tapp.post('ApiOrder/orderCollect', {orderid: orderid}, function (data) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n\t\t\t\t}\n      });\n    },\n\t\tlogistics:function(e){\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar orderinfo = this.datalist[index];\n\t\t\tvar express_com = orderinfo.express_com\n\t\t\tvar express_no = orderinfo.express_no\n\t\t\tvar express_content = orderinfo.express_content\n\t\t\tvar express_type = orderinfo.express_type\n\t\t\tvar prolist = orderinfo.prolist\n\t\t\tconsole.log(express_content)\n\t\t\tif(!express_content){\n\t\t\t\tapp.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);\n\t\t\t}else{\n\t\t\t\texpress_content = JSON.parse(express_content);\n\t\t\t\tfor(var i in express_content){\n\t\t\t\t\tif(express_content[i].express_ogids){\n\t\t\t\t\t\tvar express_ogids = (express_content[i].express_ogids).split(',');\n\t\t\t\t\t\tconsole.log(express_ogids);\n\t\t\t\t\t\tvar express_oglist = [];\n\t\t\t\t\t\tfor(var j in prolist){\n\t\t\t\t\t\t\tif(app.inArray(prolist[j].id+'',express_ogids)){\n\t\t\t\t\t\t\t\texpress_oglist.push(prolist[j]);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\texpress_content[i].express_oglist = express_oglist;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.express_content = express_content;\n\t\t\t\tconsole.log(express_content);\n\t\t\t\tthis.$refs.dialogSelectExpress.open();\n\t\t\t}\n\t\t},\n\t\thideSelectExpressDialog:function(){\n\t\t\tthis.$refs.dialogSelectExpress.close();\n\t\t},\n\t\tshowhxqr:function(e){\n\t\t\tthis.hexiao_qr = e.currentTarget.dataset.hexiao_qr\n\t\t\tthis.$refs.dialogHxqr.open();\n\t\t},\n\t\tcloseHxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.close();\n\t\t},\n\t\tsearchConfirm:function(e){\n\t\t\tthis.keyword = e.detail.value;\n      this.getdata(false);\n\t\t},\n\t\tshowhxqr2:function(e){\n      var that = this;\n\t\t\tvar leftnum = e.currentTarget.dataset.num - e.currentTarget.dataset.hxnum;\n\t\t\tthis.hxogid = e.currentTarget.dataset.id;\n\t\t\tif(leftnum <= 0){\n\t\t\t\tapp.alert('没有剩余核销数量了');return;\n\t\t\t}\n\t\t\tvar hxnumlist = [];\n\t\t\tfor(var i=0;i<leftnum;i++){\n\t\t\t\thxnumlist.push((i+1)+'');\n\t\t\t}\n      if (hxnumlist.length > 6) {\n\t\t\t\tthat.hxnumlist = hxnumlist;\n        that.selecthxnumDialogShow = true;\n        that.hxnum = '';\n      } else {\n        uni.showActionSheet({\n          itemList: hxnumlist,\n          success: function (res) {\n\t\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\t\tthat.hxnum = hxnumlist[res.tapIndex];\n\t\t\t\t\t\t\tthat.gethxqr();\n\t\t\t\t\t\t}\n          }\n        });\n      }\n\t\t},\n\t\tgethxqr(){\n      var that = this;\n\t\t\tvar hxnum = this.hxnum;\n\t\t\tvar hxogid = this.hxogid;\n\t\t\tif(!hxogid){\n\t\t\t\tapp.alert('请选择要核销的商品');return;\n\t\t\t}\n\t\t\tif(!hxnum){\n\t\t\t\tapp.alert('请选择核销数量');return;\n\t\t\t}\n\t\t\tapp.showLoading();\n\t\t\tapp.post('ApiOrder/getproducthxqr', {hxogid: hxogid,hxnum:hxnum}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif(data.status == 0){\n\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t}else{\n\t\t\t\t\tthat.hexiao_qr = data.hexiao_qr\n\t\t\t\t\tthat.$refs.dialogHxqr.open();\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    hxnumRadioChange: function (e) {\n      var that = this;\n      var index = e.currentTarget.dataset.index;\n\t\t\tthis.hxnum = this.hxnumlist[index];\n\t\t\tsetTimeout(function(){\n\t\t\t\tthat.selecthxnumDialogShow = false;\n\t\t\t\tthat.gethxqr();\n\t\t\t},200)\n    },\n\t\thideSelecthxnumDialog:function(){\n\t\t\tthis.selecthxnumDialogShow = false;\n\t\t},\n  }\n};\n</script>\n<style>\n.container{ width:100%;}\n.topsearch{width:94%;margin:10rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.order-content{display:flex;flex-direction:column}\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\n.order-box .head .f1{display:flex;align-items:center;color:#333}\n.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\n.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\n.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\n\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative;align-items: center;}\n.order-box .content:last-child{ border-bottom: 0; }\n.order-box .content image{ width: 140rpx; height: 140rpx;}\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.order-box .content .detail .t2{height: 44rpx;line-height: 44rpx;color: #999;overflow: hidden;font-size: 24rpx;}\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.order-box .content .detail .x1{ flex:1}\n.order-box .content .detail .x2{ width:110rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n\n.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n\n.btn1{margin-left:20rpx; margin-top: 10rpx;max-width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;padding: 0 20rpx;}\n.btn2{margin-left:20rpx; margin-top: 10rpx;max-width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;padding: 0 20rpx;}\n\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.hxqrbox .img{width:400rpx;height:400rpx}\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n.tgr{font-size: 24rpx;}\n\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.pstime-item .radio .radio-img{width:100%;height:100%}\n.pdl10{padding-left: 10rpx;}\n\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839418923\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}