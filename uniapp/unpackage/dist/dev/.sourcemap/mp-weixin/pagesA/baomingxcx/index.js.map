{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/baomingxcx/index.vue?5277", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/baomingxcx/index.vue?260c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/baomingxcx/index.vue?c659", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/baomingxcx/index.vue?dd4d", "uni-app:///pagesA/baomingxcx/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/baomingxcx/index.vue?898f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesA/baomingxcx/index.vue?7ff1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "un", "tel", "realname", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sex", "birthday", "fujian01", "<PERSON><PERSON><PERSON><PERSON>", "index1", "index2", "index3", "index4", "index5", "index6", "index7", "index8", "onLoad", "console", "uni", "url", "method", "header", "success", "that", "methods", "getdata", "app", "id", "subform", "formdata", "orderid", "setTimeout", "sexChage", "BindPickerChange", "saveposter", "filePath", "fail", "<PERSON><PERSON><PERSON>", "posterDialogClose", "addjiating", "name", "guanxi", "danwei", "jiatinglist", "jiatinglistInput", "removejiating", "addxuexi", "jddate", "bydate", "xueli", "school", "zhuanye", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xuexilist", "xuexilistInput", "removexuexi", "addwork", "workdatestart", "workdateend", "city", "bumen", "<PERSON><PERSON><PERSON>", "worklist", "worklistInput", "removework", "uploadimg", "pics", "removeimg", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "bindDateChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjMA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmev1B;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,+CACA,mDACA,kDACA,oDACA,wDACA,wDACA,sDACA,qDACA,yDACA,sDACA,uDACA,wDACA,kDACA,kDACA,mDACA,mDACA,0DACA,wDACA,uDACA,yDACA,iDACA,oDACA;EAEA;EAEAC;IACA;IACA;IACA;IACA;IACAC;IACAC;MACAC;MACAzB;MACA0B;MACAC;QAAA;MAAA;MACAC;QACAL;QACAM;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAF;MACA;MACAG;QAAAC;MAAA;QACAJ;QACA;UACA;YACAA;UACA;UACAA;UACA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;UACA;UACAA;QACA;UACAG;QACA;MAEA;IACA;IAEAE;MACA;MACA;MACA;MAEAC;MACAA;MACAA;MAGAA;MACAA;MACAA;MACA;;MAEA;QACAH;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MAGA;QACAA;QAAA;MACA;MAEA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MAEA;MACA;MACA;QACAA;QAAA;MACA;MACAA;MACA;MACAA;QAAAC;QAAA7B;QAAAgC;MAAA;QACAJ;QACA;UACAA;UACAH;YACAQ;cACAR;YACA;UACA;QACA;UACAG;QACA;MACA;IACA;IACAM;MACA;IACA;IACAC;MACA;MACA;MACA;MACAV;IACA;IACAW;MACA;MACAR;MACAR;QACAC;QACAG;UACA;YACAJ;cACAiB;cACAb;gBACAI;cACA;cACAU;gBACAV;gBACAA;cACA;YACA;UACA;QACA;QACAU;UACAV;UACAA;QACA;MACA;IACA;IACAW;MACA;MACAd;MACA;MACAA;MACA;QACAA;QACA;QACA;QACAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA;QACA;QACAN;QACAM;MACA;IAEA;IACAe;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QAAAC;QAAAC;QAAAC;QAAA1C;MAAA;MACA2C;MACA;IACA;IACAC;MACA;MACA;MACA;MACAD;MACA;IACA;IACAE;MACA;MACA;MACA;MACAF;MACA;IACA;IAEAG;MACA;MACA;MACA;MACA;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAD;MACA;IACA;IAIAE;MACA;MACA;MACA;MACAF;MACA;IACA;IAEAG;MACA;MACA;MACA;MACA;QAAAC;QAAAC;QAAAhB;QAAAiB;QAAAC;QAAAC;MAAA;MACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAD;MACA;MACA7C;IACA;IACA+C;MACA;MACA;MACA;MACAF;MACA;IACA;IAGAG;MACA;MACA;MACA;MACA;MACAvC;QACA;UACAwC;QACA;QACAjD;QACAM;QACAN;MACA;IACA;IACAkD;MACA;MACA;MACA;MACA;MACAD;MACA3C;IAEA;IACA6C;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAtD;MACA;MACA;QACA;QACA;QACAM;QACAN;QACA;QACAoC;QACA;MACA;QACA;QACA;QACA9B;QACA;QACAuC;QACA;MACA;QACAvC;MACA;MACAA;IACA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;ACz5BA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/baomingxcx/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/baomingxcx/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=a4a16cc2&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/baomingxcx/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=a4a16cc2&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    _vm.order &&\n    _vm.order.status == 1 &&\n    _vm.order.paystatus == 1 &&\n    _vm.order.poster\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    _vm.order &&\n    _vm.order.status == 1 &&\n    _vm.order.paystatus == 1 &&\n    _vm.order.poster\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m2 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    _vm.order &&\n    _vm.order.status == 1 &&\n    _vm.order.paystatus == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    _vm.order &&\n    _vm.order.status == 1 &&\n    _vm.order.paystatus == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m4 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    _vm.order &&\n    _vm.order.status == 1 &&\n    !(_vm.order.paystatus == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    _vm.order &&\n    _vm.order.status == 1 &&\n    !(_vm.order.paystatus == 1)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g0 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.zhengjian.length\n      : null\n  var g1 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.zhengjian.join(\",\")\n      : null\n  var l0 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.__map(_vm.info.benrenshenfen, function (item1, idx1) {\n          var $orig = _vm.__get_orig(item1)\n          var m6 = _vm.inArray(item1, _vm.order.benrenshenfen)\n          return {\n            $orig: $orig,\n            m6: m6,\n          }\n        })\n      : null\n  var g2 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.shenfenzheng.length\n      : null\n  var g3 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1) &&\n    g2 > 0\n      ? _vm.shenfenzheng.join(\",\")\n      : null\n  var g4 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.biyezheng.length\n      : null\n  var g5 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1) &&\n    g4 > 0\n      ? _vm.biyezheng.join(\",\")\n      : null\n  var m7 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m8 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload &&\n    !(_vm.order && _vm.order.status == 0) &&\n    !(_vm.order && _vm.order.status == 1)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m10 = _vm.isload && _vm.showdengji ? _vm.t(\"color1\") : null\n  var m11 = _vm.isload && _vm.showdengji ? _vm.t(\"color1rgb\") : null\n  var m12 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1\") : null\n  var m13 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" >\r\n\t<block v-if=\"isload\">\t\r\n\t\t\r\n\t\t<view v-if=\"order && order.status==0\">\r\n\t\t\t<view class=\"step1\" >\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/imgsrc/baomingxcx/shenhezhong.png'\" /></view>\r\n\t\t\t\t\t\t<view class=\"f2\">您的资料正在审核中</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view v-else-if=\"order && order.status==1\">\r\n\t\t\t<view class=\"step1\" >\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/imgsrc/baomingxcx/tongguo.png'\"  style=\"width: 150rpx; height: 150rpx;\"/></view>\r\n\t\t\t\t\t\t<view class=\"f2\" style=\"margin-top: 20rpx;\">您的资料已审核通过</view>\r\n\t\t\t\t\t\t<block v-if=\"order.paystatus==1\">\r\n\t\t\t\t\t\t\t<view class=\"f2\" style=\"margin-top: 20rpx;\" v-if=\"order.paystatus==1\">已支付</view>\r\n\t\t\t\t\t\t\t<view v-if=\"order.poster\" class=\"paybtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"   @tap=\"dengji\" data-feild='zhunkaozheng'  :data-poster=\"order.poster\" >下载准考证</view>\r\n\t\t\t\t\t\t\t<view v-else class=\"tips\" style=\"text-align: center;\">等待准考证生成</view>\r\n\t\t\t\t\t\t\t<view class=\"paybtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" @tap=\"dengji\"  data-field=\"dengji\"  :data-poster=\"order.dengjipic\" >查看登记表</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<view class=\"f2\" style=\"margin-top: 20rpx;\">需支付金额：<text style=\"color: red;\">￥{{info.price}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"paybtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\"  @tap.stop=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + order.payorderid\">立即支付</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\t\r\n\t\t<view v-else>\r\n\t\t\t\t\t\t<view class=\"tips\" v-if=\"order.status==2\">审核未通过：{{order.checkreason}}</view>\r\n\t\t\t\t\t\t<form @submit=\"subform\">\r\n\t\t\t\t\t\t\t<view class=\"step1\" >\r\n\t\t\t\t\t\t\t\t\t<view class=\"title\">基本信息</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-box\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">姓名<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input name=\"realname\" :value=\"order.realname\" placeholder=\"请输入真实姓名\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">身份证号<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input name=\"icode\" :value=\"order.icode\" placeholder=\"请输入身份证号\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">性别<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text data-sex='1' @tap=\"sexChage\" :class=\"'radio1 ' +(sex==1?'checked':'')\" > 男 </text> \r\n\t\t\t\t\t\t\t\t\t\t\t\t<text data-sex='2'  @tap=\"sexChage\" :class=\"'radio1 ' +(sex==2?'checked':'')\"> 女</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">出生日期<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" value=\"\" start=\"1900-01-01\" data-field=\"birthday\" @change=\"bindDateChange\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"birthday\">{{birthday}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-else>请选择出生日期</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">民族<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display: none;\"  name=\"minzu\" :value=\"index1\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index1\"  data-id=\"1\"  @change=\"BindPickerChange\"  :value='index1'  :range=\"info.minzu\"  >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.minzu[index1]\">{{info.minzu[index1]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择民族</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">婚姻状况<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\"  style=\"display: none;\"  name=\"hunyin\" :value=\"index2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index2\"  @change=\"BindPickerChange\" :value='index2' :range=\"info.hunyin\"  >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.hunyin[index2]\">{{info.hunyin[index2]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">政治面貌<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\"  style=\"display: none;\"  name=\"zhengzhimianmao\" :value=\"index3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index3\"  @change=\"BindPickerChange\" :value='index3'  :range=\"info.zhengzhimianmao\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.zhengzhimianmao[index3]\">{{info.zhengzhimianmao[index3]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">籍贯<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input name=\"jiguan\" :value=\"order.jiguan\" placeholder=\"请输入籍贯\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">户籍地<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input name=\"hujidi\" :value=\"order.hujidi\" placeholder=\"请输入户籍地\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">生源地</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input name=\"sydi\" :value=\"order.sydi\" placeholder=\"请输入生源地\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t\t\t<view class=\"title\">证件照片 <text style=\"color: #999;font-size: 24rpx\"> (请上传一寸照片) </text><text style=\"color:red\">*</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in zhengjian\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"zhengjian\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\" ></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"uploadbtn\" v-if=\"zhengjian.length<1\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"zhengjian\"></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"shili\"><image :src=\"pre_url+'/static/imgsrc/baomingxcx/shili.png'\" /></view>\r\n\t\t\t\t\t\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"zhengjian\" :value=\"zhengjian.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"pictips\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">要求近期彩色正面免冠1寸证件照片;格式支持：JPG、JPEG,最大支持50KB</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">证件照片参考模板</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"title\">联系方式</view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">联系电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input name=\"tel\" :value=\"order.tel\" placeholder=\"请输入联系电话\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">紧急电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input name=\"jjtel\" :value=\"order.jjtel\" placeholder=\"请输入紧急电话\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">通讯地址<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input name=\"txaddress\" :value=\"order.txaddress\" placeholder=\"请输入通讯地址\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"title\">教育信息</view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">学历<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display: none;\"   name=\"xueli\" :value=\"index4\">\r\n\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index4\"  @change=\"BindPickerChange\" :value='index4'  :range=\"info.xueli\"  >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.xueli[index4]\">{{info.xueli[index4]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">学位<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display: none;\"   name=\"xuewei\" :value=\"index5\">\r\n\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index5\"  @change=\"BindPickerChange\" :value='index5'  :range=\"info.xuewei\"  >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.xuewei[index5]\">{{info.xuewei[index5]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">毕业院校<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input name=\"biyeschool\" :value=\"order.biyeschool\" placeholder=\"请输入毕业院校\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">毕业时间<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" value=\"\" start=\"1900-01-01\"  @change=\"bindDateChange\" data-field=\"biyedate\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"biyedate\">{{biyedate}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">所属专业<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input name=\"zhuanye\" :value=\"order.zhuanye\" placeholder=\"请输入所属专业\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">教育形式<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display: none;\"   name=\"jyxingshi\" :value=\"index6\">\r\n\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index6\"  @change=\"BindPickerChange\" :value='index6'  :range=\"info.jyxingshi\"  >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.jyxingshi[index6]\">{{info.jyxingshi[index6]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">外语水平</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input name=\"en_level\" :value=\"order.en_level\" placeholder=\"请输入外语水平\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">计算机水平</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input name=\"pc_level\" :value=\"order.pc_level\" placeholder=\"请输入计算机水平\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"title\">健康信息</view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">身高<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input name=\"shengao\" :value=\"order.shengao\" placeholder=\"请输入身高\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">体重<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input name=\"weight\" :value=\"order.weight\" placeholder=\"请输入体重\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">辨色力</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display: none;\"   name=\"bianseli\" :value=\"index7\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index7\"  @change=\"BindPickerChange\" :value='index7'  :range=\"info.bianseli\"  >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.bianseli[index7]\">{{info.bianseli[index7]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"title\">其他信息</view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">本人身份<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t<checkbox-group name=\"benrenshenfen\" class=\"checkbox-group\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in info.benrenshenfen\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :checked=\"inArray(item1,order.benrenshenfen)?true:false\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">是否满足加分条件</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display: none;\"   name=\"addtiaojian\" :value=\"index8\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" data-field=\"index8\"  @change=\"BindPickerChange\" :value='index8'  :range=\"info.addtiaojian\"  >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-if=\"info.addtiaojian[index8]\">{{info.addtiaojian[index8]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view  v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-item  border\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\" style=\"height: 80rpx;\">奖惩情况</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f3\" style=\"border: 1rpx solid #f5f5f5; border-radius: 10rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<textarea name=\"jiangchengqk\" class='textarea'  :value=\"order.jiangchengqk\"  placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"title\" style=\"display: flex; justify-content: space-between;\">\r\n\t\t\t\t\t\t\t\t\t<view><text style=\"color:red\"> *</text><text>家庭情况</text></view>\r\n\t\t\t\t\t\t\t\t\t<text style=\"color: #999; font-size: 24rpx;\" @tap=\"addjiating\">添加家庭情况</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\" v-if=\"jiatinglist\" v-for=\"(item,index) in jiatinglist\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">姓名<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input  @input=\"jiatinglistInput\" :data-index=\"index\" data-field=\"name\"  :value=\"item.name\" placeholder=\"请填写姓名\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">关系<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input @input=\"jiatinglistInput\" :data-index=\"index\" data-field=\"guanxi\"  :value=\"item.guanxi\" placeholder=\"请填写关系\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">单位<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input  @input=\"jiatinglistInput\" :data-index=\"index\" data-field=\"danwei\"  :value=\"item.danwei\" placeholder=\"请填写单位\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">紧急联系电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input  @input=\"jiatinglistInput\" :data-index=\"index\" data-field=\"tel\" :value=\"item.tel\" placeholder=\"紧急联系电话\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"del\" :data-index=\"index\"  @tap=\"removejiating\">删除</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"title\" style=\"display: flex; justify-content: space-between;\">\r\n\t\t\t\t\t\t\t\t\t<view><text style=\"color:red\"> *</text><text>学习经历</text></view>\r\n\t\t\t\t\t\t\t\t\t<text style=\"color: #999; font-size: 24rpx;\" @tap=\"addxuexi\">添加学习经历</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\" v-if=\"xuexilist\" v-for=\"(item,index) in xuexilist\">\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">起止日期<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :value=\"jddate[index]\" start=\"1900-01-01\"  :data-index='index' @change=\"bindDateChange\" :data-field=\"'jddate'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.jddate && !jddate[index]\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.jddate\">{{item.jddate}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-else-if=\"jddate[index]\">\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"jddate[index]\">{{jddate[index]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else  style=\"color: #999;\">请选择开始日期</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t - \r\n\t\t\t\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :value=\"bydate[index]\"  start=\"1900-01-01\"  :data-index=\"index\" @change=\"bindDateChange\" data-field=\"bydate\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.bydate && !bydate[index]\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.bydate\">{{item.bydate}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-else-if=\"bydate[index]\">\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"bydate[index]\">{{bydate[index]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else style=\"color: #999;\">请选择毕业日期</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">学历/学位<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input  @input=\"xuexilistInput\" :data-index=\"index\" data-field=\"xueli\"  :value=\"item.xueli\" placeholder=\"请填写学历/学位\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">毕业院校及系别<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input @input=\"xuexilistInput\" :data-index=\"index\" data-field=\"school\" :value=\"item.school\" placeholder=\"请填写毕业院校\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">所学专业<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input  @input=\"xuexilistInput\" :data-index=\"index\" data-field=\"zhuanye\"  :value=\"item.zhuanye\" placeholder=\"请填写专业\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-item flex border\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">学习形式<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<input  @input=\"xuexilistInput\" :data-index=\"index\" data-field=\"xuexixingshi\"  :value=\"item.xuexixingshi\" placeholder=\"请填写学习形式\" > </input>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"del\" :data-index=\"index\"  @tap=\"removexuexi\">删除</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"title\">报名附件</view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-box\">\r\n\t\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center\" style=\"flex-wrap:wrap;padding:20rpx 0;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox\" v-if=\"shenfenzheng.length>0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in shenfenzheng\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"shenfenzheng\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img2\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"shenfenzheng\" :value=\"shenfenzheng.join(',')\" maxlength=\"-1\"/>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<block >\r\n\t\t\t\t\t\t\t\t\t\t\t<view  class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 30rpx',backgroundSize:'50rpx 50rpx',backgroundColor:'#F3F3F3'}\"  @tap=\"uploadimg\" data-field=\"shenfenzheng\" style=\"margin-right:20rpx;\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view  style=\"color:#999\"><text style=\"color:red\"> *</text>身份证正反面必传</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center\" style=\"flex-wrap:wrap;padding:20rpx 0;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox\" v-if=\"biyezheng.length>0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in biyezheng\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"biyezheng\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img2\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"biyezheng\" :value=\"biyezheng.join(',')\" maxlength=\"-1\"/>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<block >\r\n\t\t\t\t\t\t\t\t\t\t\t<view  class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 30rpx',backgroundSize:'50rpx 50rpx',backgroundColor:'#F3F3F3'}\"  @tap=\"uploadimg\" data-field=\"biyezheng\" style=\"margin-right:20rpx;\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view  style=\"color:#999\"><text style=\"color:red\"> *</text>毕业证必传</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t</view>\t\t\r\n\t\t\t\t\t\t\t</view>\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"step\" >\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"agree flex\"> \r\n\t\t\t\t\t\t\t\t\t<checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox class=\"checkbox\" value=\"1\" :checked=\"isagree\"/>我已阅读并同意</label></checkbox-group>\r\n\t\t\t\t\t\t\t\t\t<text :style=\"{color:t('color1')}\"  @tap=\"showxieyiFun\">《承诺书》</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t\t\t<button class=\"savebtn2\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">立即提交</button>\r\n\t\t\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"posterDialog\" v-if=\"showdengji\">\r\n\t\t\t\t\t<view class=\"main\">\r\n\t\t\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"dengjipic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"dengjipic\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view  @tap=\"saveposter(dengjipic)\"  :data-poster=\"dengjipic\" class=\"upload\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" > {{field=='dengji'?'下载登记表':'下载准考证'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\r\n\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\r\n\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t<parse :content=\"info.content\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi\">已阅读并同意</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<view style=\"display:none\">{{test}}</view>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:false,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{un:'',tel:'',realname:''},\r\n\t\t\tshowxieyi:false,\r\n\t\t\tzhengjian:[],\r\n\t\t\tsex:1,\r\n\t\t\tbirthday:'',\r\n\t\t\tfujian01:'',\r\n\t\t\thukoubo:[],\r\n\t\t\tindex1:0,\r\n\t\t\tindex2:0,\r\n\t\t\tindex3:0,\r\n\t\t\tindex4:0,\r\n\t\t\tindex5:0,\r\n\t\t\tindex6:0,\r\n\t\t\tindex7:0,\r\n\t\t\tindex8:0,\r\n\t\t\tinfo:[],\r\n\t\t\tpicker:[],\r\n\t\t\tfield:'',\r\n\t\t\tisagree:false,\r\n\t\t\tbiyedate:'',\r\n\t\t\tjiatinglist:[],\r\n\t\t\txuexilist:[],\r\n\t\t\tworklist:[],\r\n\t\t\tshenfenzheng:[],\r\n\t\t\tbiyezheng:[],\r\n\t\t\ttuiwuzheng:[],\r\n\t\t\txuejibaogao:[],\r\n\t\t\tother:[],\r\n\t\t\torder:[],\r\n\t\t\tjddate:{},\r\n\t\t\tbydate:{},\r\n\t\t\tworkdatestart:{},\r\n\t\t\tworkdateend:{},\r\n\t\t\tshowdengji:false,\r\n\t\t\tdengjipic:'',\r\n\t\t\ttest:'',\r\n\t\t\ttmplids: [],\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t\tvar that = this;\r\n\t\tvar url = app.globalData.pre_url+'/static/area.json?time=v1';\r\n\t\tconsole.log(url);\r\n\t\tuni.request({\r\n\t\t\turl: url,\r\n\t\t\tdata: {},\r\n\t\t\tmethod: 'GET',\r\n\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\tsuccess: function(res2) {\r\n\t\t\t\tconsole.log(res2.data);\r\n\t\t\t\tthat.items = res2.data\r\n\t\t\t}\r\n\t\t});\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tvar id = this.opt.id\r\n\t\t\tapp.get('ApiBaomingxcx/getsysset',{ id:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status==1){\r\n\t\t\t\t\tif(res.data){\r\n\t\t\t\t\t\tthat.info = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.tmplids = res.tmplids;\r\n\t\t\t\t\tif(res.order){\r\n\t\t\t\t\t\t\tthat.order = res.order\r\n\t\t\t\t\t\t\tthat.birthday = res.order.birthday\r\n\t\t\t\t\t\t\tthat.index1 = res.order.minzu\r\n\t\t\t\t\t\t\tthat.index2 = res.order.hunyin\r\n\t\t\t\t\t\t\tthat.index3 = res.order.zhengzhimianmao\r\n\t\t\t\t\t\t\tthat.index4 = res.order.zhengzhimianmao\r\n\t\t\t\t\t\t\tthat.index5 = res.order.zhengzhimianmao\r\n\t\t\t\t\t\t\tthat.index6 = res.order.zhengzhimianmao\r\n\t\t\t\t\t\t\tthat.index7 = res.order.zhengzhimianmao\r\n\t\t\t\t\t\t\tthat.index8 = res.order.zhengzhimianmao\r\n\t\t\t\t\t\t\tthat.zhengjian = res.order.zhengjian\r\n\t\t\t\t\t\t\tthat.biyedate = res.order.biyedate\r\n\t\t\t\t\t\t\tthat.hukoubo = res.order.hukoubo\r\n\t\t\t\t\t\t\tthat.shenfenzheng = res.order.shenfenzheng\r\n\t\t\t\t\t\t\tthat.biyezheng = res.order.biyezheng\r\n\t\t\t\t\t\t\tthat.tuiwuzheng = res.order.tuiwuzheng\r\n\t\t\t\t\t\t\tthat.xuejibaogao = res.order.xuejibaogao\r\n\t\t\t\t\t\t\tthat.other = res.order.other\r\n\t\t\t\t\t\t\tthat.jiatinglist =  res.order.jiatinglist\r\n\t\t\t\t\t\t\tthat.xuexilist =  res.order.xuexilist\r\n\t\t\t\t\t\t\tthat.worklist =  res.order.worklist\r\n\t\t\t\t\t\t\tthat.sex =  res.order.sex\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert('参数缺失');\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n    subform: function (e) {\r\n      var that = this;\r\n\t\t\t//var tags = JSON.stringify(that.tags);\r\n      var formdata = e.detail.value;\r\n\r\n\t\t\tformdata.sex  = that.sex\r\n\t\t\tformdata.birthday  = that.birthday\r\n\t\t\tformdata.biyedate = that.biyedate\r\n\t\r\n\r\n\t\t\tformdata.jiatinglist = that.jiatinglist\r\n\t\t\tformdata.xuexilist = that.xuexilist\r\n\t\t\tformdata.worklist = that.worklist\r\n\t\t\t//console.log(formdata);\r\n\t\t\t\r\n\t\t\tif(formdata.realname==''){\r\n\t\t\t\tapp.error('请填写姓名');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.icode==''){\r\n\t\t\t\tapp.error('请填写身份证号');return;\t\r\n\t\t\t}\r\n\t\t\tif (!/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(formdata.icode)) {\r\n\t\t\t\tapp.alert('身份证号格式错误');return;\r\n\t\t\t}\r\n\t\t\tif(formdata.sex==''){\r\n\t\t\t\tapp.error('请选择性别');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.birthday==''){\r\n\t\t\t\tapp.error('请填写生日');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.minzu==''){\r\n\t\t\t\tapp.error('请选择民族');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.hunyin==''){\r\n\t\t\t\tapp.error('请选择婚姻');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.zhengzhimianmao==''){\r\n\t\t\t\tapp.error('请选择政治面貌');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.jiguan==''){\r\n\t\t\t\tapp.error('请填写籍贯');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.hujidi==''){\r\n\t\t\t\tapp.error('请填写户籍地');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.zhengjian==''){\r\n\t\t\t\tapp.error('请上传证件照');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.tel==''){\r\n\t\t\t\tapp.error('请填写联系方式');return;\t\r\n\t\t\t}\r\n\t\t\tif (!app.isPhone(formdata.tel)) {\r\n\t\t\t\tapp.alert('联系方式格式错误');return;\r\n\t\t\t}\r\n\t\t\tif(formdata.jjtel==''){\r\n\t\t\t\tapp.error('请填写紧急联系方式');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.txaddress==''){\r\n\t\t\t\tapp.error('请填写通讯地址');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.xueli==''){\r\n\t\t\t\tapp.error('请选择学历');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.xuewei==''){\r\n\t\t\t\tapp.error('请选择学位');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.biyedate==''){\r\n\t\t\t\tapp.error('请选择毕业时间');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.biyeschool==''){\r\n\t\t\t\tapp.error('请填写毕业院校');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.zhuanye==''){\r\n\t\t\t\tapp.error('请填写专业');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.jyxingshi==''){\r\n\t\t\t\tapp.error('请选择教育形式');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.shengao==''){\r\n\t\t\t\tapp.error('请填写身高');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.weight==''){\r\n\t\t\t\tapp.error('请填写体重');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.benrenshenfen==''){\r\n\t\t\t\tapp.error('请选择本人身份');return;\t\r\n\t\t\t}\r\n\r\n\t\t\r\n\t\t\tif(formdata.shenfenzheng=='' || formdata.shenfenzheng==undefined){\r\n\t\t\t\tapp.error('请上传身份证');return;\t\r\n\t\t\t}                                             \r\n\t\t\t\r\n\t\t\tif(formdata.biyezheng==''  || formdata.biyezheng==undefined){\r\n\t\t\t\tapp.error('请上传毕业证');return;\t\r\n\t\t\t}\r\n\t\t\tif(formdata.jiatinglist=='' ){\r\n\t\t\t\tapp.error('请将填写家庭情况');return;\r\n\t\t\t}\r\n\t\t\tif(formdata.xuexilist=='' ){\r\n\t\t\t\tapp.error('请将填学习经历');return;\r\n\t\t\t}\r\n\t\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tvar orderid = that.order.id ? that.order.id : '';\r\n\t\t\tif(!that.isagree){\r\n\t\t\t\tapp.error('请先阅读并同意服务协议');return;\t\r\n\t\t\t}\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\t//console.log(formdata);return;\r\n      app.post('ApiBaomingxcx/formsubmit', {id:id,info:formdata,orderid:orderid}, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (res.status == 1) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t})\r\n        } else {\r\n          app.error(res.msg);\r\n        }\r\n      });\r\n    },\r\n\t\tsexChage:function(e){\r\n\t\t\t\tthis.sex = e.currentTarget.dataset.sex;\r\n\t\t},\r\n\t\tBindPickerChange:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index = e.detail.value;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tthat[field] = index\r\n\t\t},\r\n\t\tsaveposter:function(pic){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading('图片保存中');\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: pic,\r\n\t\t\t\tsuccess (res) {\r\n\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\tsuccess:function () {\r\n\t\t\t\t\t\t\t\tapp.success('保存成功');\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail:function(){\r\n\t\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\t\tapp.error('保存失败');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail:function(){\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.error('下载失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tdengji: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.showdengji = true;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tthat.field = field;\r\n\t\t\tif(field=='dengji'){\r\n\t\t\t\tthat.sharetypevisible = false;\r\n\t\t\t\t//app.showLoading('生成中');\r\n\t\t\t\tvar poster = e.currentTarget.dataset.poster;\r\n\t\t\t\tthat.dengjipic = poster;\r\n\t\t\t\t/*app.post('ApiBaomingxcx/poster', { id: that.order.id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.dengjipic = data.poster;\r\n\t\t\t\t\t}\r\n\t\t\t\t});*/\r\n\t\t\t}else{\r\n\t\t\t\t\tvar poster = e.currentTarget.dataset.poster;\r\n\t\t\t\t\tconsole.log(poster);\r\n\t\t\t\t\tthat.dengjipic = poster;\r\n\t\t\t}\r\n\t\r\n\t\t},\r\n\t\tposterDialogClose: function () {\r\n\t\t\tthis.showdengji = false;\r\n\t\t},\r\n\t\taddjiating:function(e){\r\n\t\t\tvar that=this;\r\n\t\t\tvar jiatinglist = that.jiatinglist;\r\n\t\t\tvar length= jiatinglist.length\r\n\t\t\tvar val = { name:'',guanxi:'',danwei:'',tel:''}\r\n\t\t\tjiatinglist.push(val);\r\n\t\t\tthis.jiatinglist = jiatinglist;\r\n\t\t},\r\n\t\tjiatinglistInput:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar jiatinglist = this.jiatinglist;\r\n\t\t\tjiatinglist[index][field] = e.detail.value;\r\n\t\t\tthis.jiatinglist = jiatinglist;\r\n\t\t},\r\n\t\tremovejiating:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar jiatinglist = that.jiatinglist;\r\n\t\t\tjiatinglist.splice(index,1);\r\n\t\t\tthis.jiatinglist = jiatinglist;\r\n\t\t},\r\n\r\n\t\taddxuexi:function(e){\r\n\t\t\tvar that=this;\r\n\t\t\tvar xuexilist = that.xuexilist;\r\n\t\t\tvar length= xuexilist.length\r\n\t\t\tvar val = { jddate:'',bydate:'',xueli:'',school:'',zhuanye:'',xuexixingshi:''}\r\n\t\t\txuexilist.push(val);\r\n\t\t\tthis.xuexilist = xuexilist;\r\n\t\t},\r\n\t\txuexilistInput:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar xuexilist = this.xuexilist;\r\n\t\t\txuexilist[index][field] = e.detail.value;\r\n\t\t\tthis.xuexilist = xuexilist;\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\tremovexuexi:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar xuexilist = that.xuexilist;\r\n\t\t\txuexilist.splice(index,1);\r\n\t\t\tthis.xuexilist = xuexilist;\r\n\t\t},\r\n\t\t\r\n\t\taddwork:function(e){\r\n\t\t\tvar that=this;\r\n\t\t\tvar worklist = that.worklist;\r\n\t\t\tvar length= worklist.length\r\n\t\t\tvar val = { workdatestart:'',workdateend:'', danwei:'',city:'',bumen:'',zhiwu:''}\r\n\t\t\tworklist.push(val);\r\n\t\t\tthis.worklist = worklist;\r\n\t\t},\r\n\t\tworklistInput:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar worklist = this.worklist;\r\n\t\t\tworklist[index][field] = e.detail.value;\r\n\t\t\tthis.worklist = worklist;\r\n\t\t\tconsole.log(worklist);\r\n\t\t},\r\n\t\tremovework:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar worklist = that.worklist;\r\n\t\t\tworklist.splice(index,1);\r\n\t\t\tthis.worklist = worklist;\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(field);\r\n\t\t\t\tthat[field] = pics;\r\n\t\t\t\tconsole.log(that[field])\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field];\r\n\t\t\tpics.splice(index,1);\r\n\t\t\tthat[field] = pics;\r\n\t\t\r\n\t\t},\r\n\t\tisagreeChange: function (e) {\r\n\t\t  var val = e.detail.value;\r\n\t\t  if (val.length > 0) {\r\n\t\t    this.isagree = true;\r\n\t\t  } else {\r\n\t\t    this.isagree = false;\r\n\t\t  }\r\n\t\t},\r\n\t\tshowxieyiFun: function () {\r\n\t\t  this.showxieyi = true;\r\n\t\t},\r\n\t\thidexieyi: function () {\r\n\t\t  this.showxieyi = false;\r\n\t\t\t\tthis.isagree = true;\r\n\t\t\t\tif(this.wxloginclick){\r\n\t\t\t\t\tthis.weixinlogin();\r\n\t\t\t\t}\r\n\t\t},\r\n\t\tbindDateChange: function(e) {\r\n\t\t\tvar that=this\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\nconsole.log(field);\r\n\t\t\t//this.birthday = e.detail.value\r\n\t\t\tif(field=='jddate' || field=='bydate'){\r\n\t\t\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\t\t\t//that.jdindex = index;\r\n\t\t\t\t\tthat[field][index] = e.detail.value\r\n\t\t\t\t\t\t\t\t\tconsole.log(that[field][index]);\r\n\t\t\t\t\tvar xuexilist = that.xuexilist;\r\n\t\t\t\t\txuexilist[index][field] = e.detail.value;\r\n\t\t\t\t\tthis.xuexilist = xuexilist;\r\n\t\t\t}else if(field=='workdatestart' || field=='workdateend'){\r\n\t\t\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\t\t\t//that.jdindex = index;\r\n\t\t\t\t\tthat[field][index] = e.detail.value\r\n\t\t\t\t\tvar worklist = that.worklist;\r\n\t\t\t\t\tworklist[index][field] = e.detail.value;\r\n\t\t\t\t\tthis.worklist = worklist;\r\n\t\t\t}else{\r\n\t\t\t\t\tthat[field] =  e.detail.value\r\n\t\t\t}\r\n\t\t\tthat.test = Math.random();\r\n\t\t},\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n<style>\r\n\t\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n\r\n.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx;  z-index: 1000;}\r\n.form-item{ justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item .jdtype{ line-height: 50rpx;}\r\n.form-item .f1{color:#333;flex-shrink:0; font-size: 30rpx; display: flex; align-items: center;}\r\n.form-item .f2{display:flex;align-items:center;  margin: 10rpx 0;justify-content: flex-end; width: 80%; }\r\n.form-item .f2 input{ height: 80rpx;}\r\n.form-item .f2 .picker{ height: 80rpx; line-height: 80rpx;}\r\n.form-item .f2 .checkbox-group{ margin:20rpx}\r\n.form-item .tbox { display: flex; padding: 30rpx 0;}\r\n.form-item .tbox .pname{color:#7C7F8E;align-items:center; font-size: 30rpx; margin-right: 20rpx;}\r\n.form-item .tbox .subname{color:#222222; margin-right: 15rpx; align-items:center; font-size: 24rpx;  height: 50rpx; background:#fff ; border: 1rpx solid #E5E5E5;border-radius: 24rpx; padding: 0rpx 20rpx;}\r\n.form-item .tbox .subname.on{ color:#FF3A69; border: 1rpx solid #FFA1A1; background: ;font-size: 24rpx; background: #FFE9E9; }\r\n\t\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right; line-height: 80rpx;}\r\n.form-item textarea{ width:100%;height:150rpx;padding:20rpx;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .tishi{  line-height: 30rpx; color:#949798 ; margin-bottom: 30rpx;font-size: 26rpx;}\r\n.form-item .tishi.t2{  background: #F4F4F4; padding:0rpx 20rpx; border-radius: 10rpx;}\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.subitem{ display: flex; align-items: center; margin-right: 20rpx;}\r\n\r\n\r\n.step1{}\r\n.step1 .title{ font-size:32rpx ;color:#333333; padding:20rpx  30rpx; font-weight: bold; border-bottom: 1rpx solid #EEEEEE;}\r\n.step2 .title{ font-size:32rpx ;color:#333333; padding: 20rpx 0; border-bottom: 1rpx solid #EEEEEE;}\r\n.step2 .border{ border-bottom: 1rpx solid #EEEEEE;}\r\n.form-item .radio1{ font-size: 20rpx; display: flex; padding: 0rpx 20rpx;  background: #EEEEEE; color:#778899; border-radius: 24rpx; \r\nline-height: 50rpx;height: 50rpx; width: 120rpx; text-align: center; align-items: center; justify-content: center; margin-left: 20rpx;}\r\n.form-item .radio1.checked{ background:#FF3A69;color: #fff; }\r\n\r\n.form-item2{ line-height: 100rpx;justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item2 input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right; }\r\n.form-item2 .f1{ margin-top: 10rpx;}\r\n.form-item2 .f2 label{ margin-top: 11rpx; margin-left: 10rpx;}\r\n\r\n.step .title{ font-size:32rpx ;color:#333333; padding: 20rpx 0; border-bottom: 1rpx solid #EEEEEE;}\r\n.step .border{ border-bottom: 1rpx solid #EEEEEE;}\r\n.step .t3{  margin: 30rpx 0 ; color: #C66121; font-size: 28rpx; width: 60%; }\r\n.step .agree{ margin: 30rpx ;  color: #999; font-size: 28rpx; line-height: 50rpx;}\r\n.step .agree .t4{  color: #FF3A69; }\r\n\r\n\r\n.savebtn{ width: 90%; height:80rpx; line-height: 80rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\n.savebtn2{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\n\r\n.autoitem{padding:10rpx 0;flex:1;display:flex;justify-content: flex-end;}\r\n.autoitem image{ width:50rpx;height:50rpx}\r\nswitch{transform:scale(.7);}\r\n.shili{ margin-left: 20rpx; width: 200rpx; height: 200rpx;}\r\n.shili image{ width: 100%; height: 100%;}\r\n\r\n.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative; display: flex;}\r\n.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-10rpx;top:-26rpx;color:#999;font-size:32rpx;background:#999;z-index:9;border-radius:50%}\r\n.dp-form-imgbox-close .image{width:100%;height:100%}\r\n.dp-form-imgbox-img{display: block;width:100rpx;height:100rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.dp-form-imgbox-img>.image{max-width:100%;}\r\n.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n.layui-imgbox{ margin-right: 10rpx; position: relative;}\r\n\r\n\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n\r\n.layui-imgbox-img2{display: block;width:100rpx;height:100rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img2>image{max-width:100%;}\r\n\r\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\r\n.radio .radio-img{width:100%;height:100%;display:block}\r\n\r\n.freightitem{width:100%;height:60rpx;display:flex;align-items:center;margin-left:40rpx}\r\n.freightitem .f1{color:#666;flex:1}\r\n\r\n\r\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\r\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\r\n\r\n.apply_box{ margin-top: 20rpx; display: flex; justify-content:space-between}\r\n.apply_box image{}\r\n\r\n.dp-form-uploadbtn{position:relative;height:100rpx;width:100rpx}\r\n\t\r\n.step1 .content{ width: 100%; min-height: 750rpx; color: #868686; font-size: 30rpx; position: fixed; top: 20%; }\r\n.step1 .content .f1{ display: flex; flex-wrap: wrap; align-items: center; justify-content: center;}\r\n.step1 .content .f2{ display: flex; flex-wrap: wrap; align-items: center; justify-content: center;}\r\n\r\n.step1 .content image{ width:400rpx; height:400rpx}\r\n.step1 .content .paybtn{ width: 80%; height:80rpx; line-height: 80rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 10%; margin-top:60rpx; border: none; } \r\n\r\n.tips{ color: red; padding:20rpx}\r\n.upload{ text-align: center; height: 80rpx; width: 50%; line-height: 80rpx; color: #fff; border-radius: 50rpx; margin: 20rpx auto;}\r\n.del{ text-align: right; height: 80rpx; line-height: 80rpx; color: red;}\r\n\r\n.pictips{ display: flex; font-size:20rpx ; color: #999; padding-bottom:20rpx} \r\n.pictips .t1{display: flex; width: 65%; margin-right:20rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839414357\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}