{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/index.vue?e0e5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/index.vue?10c8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/index.vue?fdf4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/index.vue?124b", "uni-app:///admin/restaurant/product/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/index.vue?fbc8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/index.vue?72fa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "st", "datalist", "pagenum", "nomore", "count0", "count1", "countall", "sclist", "keyword", "nodata", "bottomButShow", "bid", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "changetab", "that", "getdata", "app", "todel", "id", "setst", "searchConfirm", "couponAddChange", "uni", "name", "pic", "give_num", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiDt2B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAD;MACAA;MACAA;MACAE;QAAAf;QAAAN;QAAAF;QAAAW;MAAA;QACAU;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACAD;QACAA;UAAAE;QAAA;UACA;YACAF;YACAF;UACA;YACAE;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACA;MACAH;QACAA;UAAAvB;UAAAyB;QAAA;UACA;YACAF;YACAF;UACA;YACAE;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;IACA;IACAC;MACAC;QAAAJ;QAAAK;QAAAC;QAAAC;MAAA;MACAH;QACAI;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzKA;AAAA;AAAA;AAAA;AAAqsC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACAztC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/restaurant/product/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/restaurant/product/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1926c42e&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/restaurant/product/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1926c42e&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m2 = !_vm.bottomButShow ? _vm.t(\"color1\") : null\n  var l0 = _vm.__map(_vm.datalist, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 =\n      _vm.bottomButShow && (!item.status || item.status == 0)\n        ? _vm.t(\"color1\")\n        : null\n    var m1 =\n      _vm.bottomButShow && !(!item.status || item.status == 0)\n        ? _vm.t(\"color2\")\n        : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m2: m2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<dd-tab :itemdata=\"['全部('+countall+')','已上架('+count1+')','未上架('+count0+')']\" :itemst=\"['all','1','0']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\n\t<view style=\"width:100%;height:100rpx\"></view>\n\t<!-- #ifndef H5 || APP-PLUS -->\n\t<view class=\"topsearch flex-y-center\">\n\t\t<view class=\"f1 flex-y-center\">\n\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\n\t\t</view>\n\t</view>\n\t<!--  #endif -->\n\t<view class=\"order-content\" id=\"datalist\">\n\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t<view class=\"order-box\">\n\t\t\t<view class=\"content\" style=\"border-bottom:none\">\n\t\t\t\t<view>\n\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t<view class=\"t2\">剩余：{{item.stock}}<text style=\"color:#a88;padding-left:20rpx\">已售：{{item.sales}}</text></view>\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">￥{{item.sell_price}}</text><text class=\"x2\">￥{{item.market_price}}</text></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"op\">\n\t\t\t\t<text style=\"color:red\" class=\"flex1\" v-if=\"!item.status || item.status==0\">未上架</text>\n\t\t\t\t<text style=\"color:green\" class=\"flex1\" v-else>已上架</text>\n\t\t\t\t<text style=\"color:orange\" class=\"flex1\" v-if=\"!item.ischecked\">待审核</text>\n\t\t\t\t<block v-if=\"bottomButShow\">\n\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"setst\" data-st=\"1\" :data-id=\"item.id\" v-if=\"!item.status || item.status==0\">上架</view>\n\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color2')}\" @tap=\"setst\" data-st=\"0\" :data-id=\"item.id\" v-else>下架</view>\n\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'edit?id='+item.id\" class=\"btn2\">编辑</view>\n\t\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"item.id\">删除</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @click=\"couponAddChange(item)\">添加</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t</view>\n\n\t<nomore v-if=\"nomore\"></nomore>\n\t<nodata v-if=\"nodata\"></nodata>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n      count0: 0,\n      count1: 0,\n      countall: 0,\n      sclist: \"\",\n\t\t\tkeyword: '',\n      nodata: false,\n\t\t\tbottomButShow:true,\n\t\t\tbid:0,\n      pre_url:app.globalData.pre_url\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.bid = this.opt.bid ? this.opt.bid : '';\n\t\tthis.getdata();\n\t\tif(opt.coupon){\n\t\t\tthis.bottomButShow = false;\n\t\t}\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n\tonNavigationBarSearchInputConfirmed:function(e){\n\t\tthis.searchConfirm({detail:{value:e.text}});\n\t},\n  methods: {\n    changetab: function (st) {\n      var that = this;\n      that.st = st;\n      that.getdata();\n    },\n    getdata: function (loadmore) {\n     if(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\tthat.loading = true;\n      app.post('ApiAdminRestaurantProduct/index', {keyword:that.keyword,pagenum: pagenum,st: that.st,bid:that.bid}, function (res) {\n        that.loading = false;\n        var data = res.datalist;\n        if (pagenum == 1){\n\t\t\t\t\tthat.countall = res.countall;\n\t\t\t\t\tthat.count0 = res.count0;\n\t\t\t\t\tthat.count1 = res.count1;\n\t\t\t\t\tthat.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    todel: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该菜品吗?', function () {\n        app.post('ApiAdminRestaurantProduct/del', {id: id}, function (res) {\n          if (res.status == 1) {\n            app.success(res.msg);\n            that.getdata();\n          } else {\n            app.error(res.msg);\n          }\n        });\n      });\n    },\n    setst: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      app.confirm('确定要' + (st == 0 ? '下架' : '上架') + '吗?', function () {\n        app.post('ApiAdminRestaurantProduct/setst', {st: st,id: id}, function (res) {\n          if (res.status == 1) {\n            app.success(res.msg);\n            that.getdata();\n          } else {\n            app.error(res.msg);\n          }\n        });\n      });\n    },\n\t\tsearchConfirm:function(e){\n\t\t\tthis.keyword = e.detail.value;\n      this.getdata(false);\n\t\t},\n\t\tcouponAddChange(item){\n\t\t\tuni.$emit('shopDataEmit',{id:item.id,name:item.name,pic:item.pic,give_num:1});\n\t\t\tuni.navigateBack({\n\t\t\t\tdelta: 1\n\t\t\t});\n\t\t}\n  }\n};\n</script>\n<style>\n.container{ width:100%;}\n.topsearch{width:94%;margin:10rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.order-content{display:flex;flex-direction:column}\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\n\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;position:relative}\n.order-box .content:last-child{ border-bottom: 0; }\n.order-box .content image{ width: 140rpx; height: 140rpx;}\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.order-box .content .detail .t1{font-size:26rpx;min-height:50rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.order-box .content .detail .t2{height:36rpx;line-height:36rpx;color: #999;overflow: hidden;font-size: 24rpx;}\n.order-box .content .detail .t3{display:flex;height: 36rpx;line-height: 36rpx;color: #ff4246;}\n.order-box .content .detail .x1{ font-size:30rpx;margin-right:5px}\n.order-box .content .detail .x2{ font-size:24rpx;text-decoration:line-through;color:#999}\n\n.order-box .bottom{ width:100%; padding:10rpx 0px; border-top: 1px #e5e5e5 solid; color: #555;}\n.order-box .op{ display:flex;align-items:center;width:100%; padding:10rpx 0px; border-top: 1px #e5e5e5 solid; color: #555;}\n.btn1{margin-left:20rpx;width:120rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:120rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n</style>", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839433199\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}