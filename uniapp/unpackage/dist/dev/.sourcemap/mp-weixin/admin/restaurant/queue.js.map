{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/queue.vue?6c17", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/queue.vue?6a49", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/queue.vue?d614", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/queue.vue?d3e3", "uni-app:///admin/restaurant/queue.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/queue.vue?5d02", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/queue.vue?4f6a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "business", "clist", "myqueue", "myjust<PERSON>ue", "lastQueue", "bid", "socketOpen", "token", "is_show_quhao", "onLoad", "onShow", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "app", "that", "uni", "url", "console", "socketMsgQueue", "type", "aid", "intervel", "sendSocketMessage", "receiveMessage", "<PERSON><PERSON><PERSON>", "cid", "call_id", "repeat", "id", "guo<PERSON>", "queue_id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwCv1B;AACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IAEA;EACA;EACAC,2BAEA;EACAC;IACA;EACA;EACAC;IACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACA;UACAA;YACAA;UACA;QACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACA;UACA;UACAC;UACAA;YACAC;UACA;UACAD;YACAE;YACAH;YACA;cACAA;YACA;YACAI;UACA;UACAJ;YAAAK;YAAAf;YAAAZ;cAAA4B;cAAAlB;YAAA;UAAA;UACAmB;YACAP;cAAAK;cAAAf;YAAA;UACA;UAEAW;YACA;cACA;cACAD;YACA;UACA;QACA;MACA;IACA;IACAQ;MACA;QACAL;QACAF;UACAvB;QACA;MACA;QACAyB;QACAC;MACA;IACA;IACAK;MACAN;MACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACA;MACAX;MACAA;QAAAY;MAAA;QACAZ;QACA;UACAA;YACAC;UACA;QACA;QACA;UACAD;YACA;UAAA,CACA;UACAC;YAAAK;YAAAf;YAAAZ;cAAAkC;cAAAN;cAAAlB;YAAA;UAAA;QACA;MACA;IACA;IACAyB;MACA;MACA;MACA;MACAd;MACAA;QAAAe;MAAA;QACAf;QACA;UACAA;YACAC;UACA;QACA;QACA;UACAD;YACAC;UACA;UACAA;YAAAK;YAAAf;YAAAZ;cAAAkC;cAAAN;cAAAlB;YAAA;UAAA;QACA;QAAA;MACA;IACA;IACA2B;MACA;MACA;MACAhB;QACAA;QACAA;UAAAe;QAAA;UACAf;UACAA;YACA;UAAA,CACA;UACAC;YAAAK;YAAAf;YAAAZ;cAAA4B;cAAAlB;cAAA4B;YAAA;UAAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/restaurant/queue.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/restaurant/queue.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./queue.vue?vue&type=template&id=0d47a3e6&\"\nvar renderjs\nimport script from \"./queue.vue?vue&type=script&lang=js&\"\nexport * from \"./queue.vue?vue&type=script&lang=js&\"\nimport style0 from \"./queue.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/restaurant/queue.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./queue.vue?vue&type=template&id=0d47a3e6&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./queue.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./queue.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"topbannerbg\" :style=\"business.pic?'background:url('+business.pic+') 100%':''\"></view>\n\t\t<view class=\"topbannerbg2\"></view>\n\t\t<view class=\"topbanner\">\n\t\t\t<view class=\"left\"><image class=\"img\" :src=\"business.logo\"/></view>\n\t\t\t<view class=\"right\">\n\t\t\t\t<view class=\"f1\">{{business.name}}</view>\n\t\t\t\t<view class=\"f2\">{{business.desc}}</view>\n\t\t\t</view>\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"myqueue\" v-if=\"1\">\r\n\t\t\t<view class=\"title\"><text class=\"t1\">当前叫号</text><!-- <text class=\"t2\" @tap=\"cancel\">取消排队</text> --></view>\r\n\t\t\t<view class=\"head\">\r\n\t\t\t\t<view class=\"f1\" v-if=\"lastQueue\">{{lastQueue.queue_no}}</view>\r\n\t\t\t\t<view class=\"f2\" v-else>暂无</view>\r\n\t\t\t\t<view class=\"flex\" v-if=\"lastQueue\"><button class=\"btn-mini\" @tap=\"guohao\" :data-id=\"lastQueue.id\">过号</button> <button class=\"btn-mini\" @tap=\"repeat\" :data-id=\"lastQueue.id\" :data-queue_no=\"lastQueue.queue_no\">重复</button></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"item\" v-for=\"item in clist\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{item.seat_min}}-{{item.seat_max}}人</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">等待<text style=\"color:#FC5729;padding:0 6rpx;font-size:28rpx\">{{item.waitnum}}</text>桌</view>\r\n\t\t\t\t\t<view class=\"f3\"><button class=\"btn-mini\" @tap=\"jiaohao\" :data-cid=\"item.id\" v-if=\"item.waitnum\">下一桌</button><button class=\"btn-mini\" disabled=\"true\" v-else>下一桌</button></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\n\t\t\n\t\t<view class=\"btn\" v-if=\"is_show_quhao\" @tap=\"goto\" :data-url=\"'/restaurant/admin/quhao?bid='+bid\">取号排队</view>\n\t\t<!-- <view class=\"log\" v-if=\"!myqueue\" @tap=\"goto\" :data-url=\"'record?bid='+bid\">我的排队记录</view> -->\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar intervel;\nvar socketMsgQueue = [];\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tbusiness:{},\n      clist: [],\n\t\t\tmyqueue:'',\n\t\t\tmyjustqueue:'',\n\t\t\tlastQueue:{},\n\t\t\tbid:'',\n\t\t\tsocketOpen:false,\n\t\t\ttoken:'',\r\n\t\t\tis_show_quhao:''\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.bid = this.opt.bid || 0;\n\t\t\n\t\tthis.getdata();\n  },\r\n\tonShow:function() {\n\t\t\r\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(intervel);\n    if(this.socketOpen) uni.closeSocket();\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tapp.get('ApiAdminRestaurantQueue/index', {}, function (res) {\r\n\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\n\t\t\t\tthat.business = res.business\n\t\t\t\tthat.clist = res.clist;\n\t\t\t\tthat.lastQueue = res.lastQueue;\n\t\t\t\tthat.myqueue = res.myqueue;\n\t\t\t\tthat.myjustqueue = res.myjustqueue;\n\t\t\t\tthat.token = res.token\r\n\t\t\t\tthat.is_show_quhao = res.is_show_quhao;\n\t\t\t\tthat.loaded();\n\t\t\t\n\t\t\t\tif(!that.socketOpen){\n\t\t\t\t\tvar pre_url = app.globalData.pre_url;\n\t\t\t\t\tvar wssurl = pre_url.replace('https://', \"wss://\") + '/wss';\n\t\t\t\t\tuni.closeSocket();\n\t\t\t\t\tuni.connectSocket({\n\t\t\t\t\t\turl: wssurl\n\t\t\t\t\t});\n\t\t\t\t\tuni.onSocketOpen(function (res) {\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\tthat.socketOpen = true;\n\t\t\t\t\t\tfor (var i = 0; i < socketMsgQueue.length; i++) {\n\t\t\t\t\t\t\tthat.sendSocketMessage(socketMsgQueue[i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tsocketMsgQueue = [];\n\t\t\t\t\t});\n\t\t\t\t\tthat.sendSocketMessage({type: 'restaurant_queue',token: that.token,data:{ aid:app.globalData.aid,bid:that.bid }});\n\t\t\t\t\tintervel = setInterval(function () {\n\t\t\t\t\t\tthat.sendSocketMessage({type: 'connect',token: that.token});\n\t\t\t\t\t}, 25000);\n\t\t\t\t\t\n\t\t\t\t\tuni.onSocketMessage(function (res) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\t\t\tthat.receiveMessage(data);\n\t\t\t\t\t\t} catch (e) {}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tsendSocketMessage:function(msg) {\n\t\t\tif (this.socketOpen) {\n\t\t\t\tconsole.log(msg)\n\t\t\t\tuni.sendSocketMessage({\n\t\t\t\t\tdata: JSON.stringify(msg)\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconsole.log('111')\n\t\t\t\tsocketMsgQueue.push(msg);\n\t\t\t}\n\t\t},\n    receiveMessage: function (data) {\n\t\t\tconsole.log(data);\n\t\t\tif(data.type == 'restaurant_queue_add' || data.type == 'restaurant_queue_cancel' || data.type == 'restaurant_queue_callno'){\n\t\t\t\tthis.getdata();\n\t\t\t}\n    },\r\n\t\tjiaohao:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar cid = e.currentTarget.dataset.cid;\r\n\t\t\tvar queue_no = e.currentTarget.dataset.queue_no;\r\n\t\t\tapp.showLoading();\r\n\t\t\tapp.get('ApiAdminRestaurantQueue/jiaohao', {cid:cid}, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif(res.status == 1) {\r\n\t\t\t\t\tapp.success(res.msg,function(){\r\n\t\t\t\t\t\t//that.getdata();\r\n\t\t\t\t\t});\n\t\t\t\t\tthat.sendSocketMessage({ type:'restaurant_queue_callno',token:that.token,data:{ call_id:res.queue.id,aid:app.globalData.aid,bid:that.bid } })\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\trepeat:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar queue_no = e.currentTarget.dataset.queue_no;\r\n\t\t\tapp.showLoading();\r\n\t\t\tapp.get('ApiAdminRestaurantQueue/jiaohao', {id:id}, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif(res.status == 1) {\r\n\t\t\t\t\tapp.success(res.msg,function(){\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t});\n\t\t\t\t\tthat.sendSocketMessage({ type:'restaurant_queue_callno',token:that.token,data:{ call_id:res.queue.id,aid:app.globalData.aid,bid:that.bid } })\r\n\t\t\t\t};\r\n\t\t\t})\r\n\t\t},\n\t\tguohao:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tapp.confirm('确定要过号吗?',function(){\n\t\t\t\tapp.showLoading();\n\t\t\t\tapp.get('ApiAdminRestaurantQueue/guohao', {id:id}, function (res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.alert(res.msg,function(){\n\t\t\t\t\t\t//that.getdata();\n\t\t\t\t\t});\n\t\t\t\t\tthat.sendSocketMessage({type: 'restaurant_queue_cancel',token: that.token,data:{ aid:app.globalData.aid,bid:that.bid,queue_id:id }});\n\t\t\t\t})\n\t\t\t})\n\t\t}\n  }\n};\n</script>\n<style>\n.container{height:100%;overflow:hidden;position: relative;}\n\n.topbannerbg{width:100%;height:280rpx;background:#fff;}\n.topbannerbg2{position:absolute;z-index:7;width:100%;height:280rpx;background:rgba(0,0,0,0.7);top:0}\n.topbanner{position:absolute;z-index:8;width:100%;display:flex;padding:60rpx;top:0;align-items:center}\n.topbanner .left{width:100rpx;height:100rpx;flex-shrink:0;margin-right:20rpx;}\n.topbanner .left .img{width:100%;height:100%;border-radius:50%}\n.topbanner .right{display:flex;flex-direction:column;padding:20rpx 0}\n.topbanner .right .f1{font-size:32rpx;font-weight:bold;color:#fff}\n.topbanner .right .f2{font-size:22rpx;color:#fff;opacity:0.7;margin-top:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;line-height:30rpx;}\n.topbanner .right .f3{width:100%;display:flex;padding-right:20rpx;margin-top:10rpx}\n.topbanner .right .f3 .t2{display:flex;align-items:center;font-size:24rpx;color:rgba(255,255,255,0.9)}\n.topbanner .right .f3 .img{width:32rpx;height:32rpx;margin-left:10rpx}\n\n.notice{display:flex;width:94%;margin:0 3%;height:120rpx;background: #fff;position:absolute;z-index:11;padding:0 50rpx;border-radius:10rpx;margin-top:-70rpx;}\n.notice .content{display:flex;width:100%;align-items:center}\n.notice .content .f1{width:40rpx;height:40rpx;margin-right:20rpx}\n.notice .content .f2{flex:1;color:#FC5729;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n\n .content{display:flex;flex-direction:column;margin:20rpx 0}\n .content .item{background:#FFF6F3;border-radius:10rpx;display:flex;align-items:center;width:100%;height:130rpx;margin-bottom:16rpx;padding:0 20rpx}\n .content .item .f1{display:flex;flex-direction:column;width:50%;padding-left:20rpx}\n .content .item .f1 .t1{color:#222222; font-size: 32rpx;}\n .content .item .f1 .t2{color:#999999;}\n .content .item .f2{display:flex;width:30%;align-items:center;}\n .content .item .f3{display:flex;width:20%;align-items:center;font-size:24rpx;justify-content:flex-end}\n\r\n.myqueue{display:flex;flex-direction:column;width:94%;margin:0 3%;margin-top:20rpx;background: #fff;padding:20rpx;border-radius:10rpx;}\n.myqueue .title{display:flex;align-items:center;justify-content:space-between}\n.myqueue .title .t1{color:#161616;font-weight:bold}\n.myqueue .title .t2{color:#FC5729;font-size:24rpx}\n.myqueue .head{display:flex;flex-direction:column;align-items:center;width:100%;margin:40rpx 0; position: relative;}\n.myqueue .head .f1{font-size:64rpx;font-weight:bold;color:#564B4B}\n.myqueue .head .f2{font-size:24rpx;color:#A29E9E;margin-top:10rpx}\n.myqueue .bottom{display:flex;align-items:center;width:100%;color:#564B4B;justify-content:space-between}\n\n.btn{width:94%;margin:0 3%;margin-top:40rpx;height:90rpx;line-height:90rpx;text-align:center;background: linear-gradient(90deg, #FF7D15 0%, #FC5729 100%);color:#fff;font-size:32rpx;font-weight:bold;border-radius:10rpx}\n.log{width:100%;text-align:center;margin-top:30rpx;margin-bottom:40rpx;color:#888}\r\n.btn-mini {width: 200rpx;height: 60rpx;line-height: 60rpx;background: #E34242;border-radius: 8rpx; color: #fff;}\r\n/* .head .btn-mini { position: absolute; right: 20rpx;width: 120rpx; bottom: 40rpx;} */\r\n.head .btn-mini {margin: 10rpx 30rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./queue.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./queue.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839433020\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}