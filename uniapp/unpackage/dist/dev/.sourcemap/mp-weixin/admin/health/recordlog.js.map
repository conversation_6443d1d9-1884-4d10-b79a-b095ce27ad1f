{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/recordlog.vue?df5e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/recordlog.vue?f081", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/recordlog.vue?d1b3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/recordlog.vue?b091", "uni-app:///admin/health/recordlog.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/recordlog.vue?9f2f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/recordlog.vue?8905"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "title", "questionlist", "question_index", "question", "rid", "custom", "themeColor", "fontSize", "onLoad", "onUnload", "methods", "getdata", "that", "app", "go<PERSON>rev", "gonext", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyC31B;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC,+BACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAT;MAAA;QACAQ;QACA;UACAA;UACA;YACAA;YACAA;UACA;UACAA;UACA;YACAA;YACAA;UACA;YACAA;UACA;UACAA;QACA;UACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACAF;MACA;MACAA;IACA;IACAG;MACA;MACA;MACA;MACA;QACAH;MACA;MACAA;IACA;IACAI;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/health/recordlog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/health/recordlog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./recordlog.vue?vue&type=template&id=7d387b2e&\"\nvar renderjs\nimport script from \"./recordlog.vue?vue&type=script&lang=js&\"\nexport * from \"./recordlog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recordlog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/health/recordlog.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recordlog.vue?vue&type=template&id=7d387b2e&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.questionlist.length : null\n  var g1 = _vm.isload && _vm.custom.PSQI ? _vm.questionlist.length : null\n  var g2 = _vm.isload && _vm.custom.PSQI ? _vm.questionlist.length : null\n  var m0 =\n    _vm.isload && !_vm.custom.PSQI && _vm.question_index > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m1 =\n    _vm.isload && !_vm.custom.PSQI && _vm.question_index > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g3 = _vm.isload && !_vm.custom.PSQI ? _vm.questionlist.length : null\n  var g4 = _vm.isload && !_vm.custom.PSQI ? _vm.questionlist.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m0: m0,\n        m1: m1,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recordlog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recordlog.vue?vue&type=script&lang=js&\"", "<template>\r\n<view :style=\"{fontSize:fontSize}\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"progress\">当前：<text>{{question_index+1}}/{{questionlist.length}}</text></view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"question\">\r\n\t\t\t\t<view class=\"title\" >\r\n\t\t\t\t\t<rich-text :nodes=\"question.name\"></rich-text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"option_group\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in question.optionlist\">\r\n\t\t\t\t\t\t<view class=\"option flex\" :style=\"item.checked==1?('background:'+themeColor+';color:#ffffff'):''\" @tap=\"selectOption\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t{{item.option}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"bottom\" v-if=\"custom.PSQI\">\r\n\t\t\t<block>\r\n\t\t\t<view class=\"btn btn1\" @tap=\"goprev\" v-if=\"question_index>0\">上一题</view>\r\n\t\t\t<view class=\"btn btn0\" v-else></view>\r\n\t\t\t</block>\r\n\t\t\t<view v-if=\"question_index<questionlist.length-1\" class=\"btn btn2\" @tap=\"gonext\">下一题</view>\r\n\t\t\t<view v-if=\"question_index==questionlist.length-1\" class=\"btn btn2\" @tap=\"goback\">返 回</view>\r\n\t\t</view>\r\n\t\t<view class=\"bottom\" v-else>\r\n\t\t\t<block>\r\n\t\t\t<view class=\"btn btn1\" @tap=\"goprev\" v-if=\"question_index>0\" :style=\"'background:rgba('+t('color1rgb')+',0.16);color:'+t('color1')\">上一题</view>\r\n\t\t\t<view class=\"btn btn0\" v-else></view>\r\n\t\t\t</block>\r\n\t\t\t<view v-if=\"question_index<questionlist.length-1\" class=\"btn\" :style=\"{background:themeColor}\" @tap=\"gonext\">下一题</view>\r\n\t\t\t<view v-if=\"question_index==questionlist.length-1\" class=\"btn\" :style=\"{background:themeColor}\" @tap=\"goback\">返 回</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\ttitle: \"\",\r\n\t\t\tquestionlist: [],\r\n\t\t\tquestion_index:-1,\r\n\t\t\tquestion:{},\r\n\t\t\trid:0,\r\n\t\t\tcustom:{},\r\n\t\t\tthemeColor:'',\r\n\t\t\tfontSize:'28rpx'\r\n\t\t};\r\n\t},\r\n\t  onLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.rid = this.opt.rid || 0;\r\n\t\t\tthis.getdata();\r\n\t  },\r\n\tonUnload: function () {\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiAdminHealth/questionRecordLog', {rid:that.opt.rid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t  that.questionlist = res.datalist\r\n\t\t\t\t\tif(that.questionlist.length>0){\r\n\t\t\t\t\t\tthat.question_index = 0;\r\n\t\t\t\t\t\tthat.question = that.questionlist[0]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.custom = res.custom\r\n\t\t\t\t\tif(that.custom.PSQI){\r\n\t\t\t\t\t\tthat.themeColor = '#229989'\r\n\t\t\t\t\t\tthat.fontSize = '36rpx'\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.themeColor = that.t('color1');\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tgoprev:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tif(that.question_index>0){\r\n\t\t\t\tthat.question_index--;\r\n\t\t\t}\r\n\t\t\tthat.question = that.questionlist[that.question_index]\r\n\t\t},\r\n\t\tgonext:function(){\r\n\t\t\tvar that = this;\r\n\t\t\t//当前是不是选择了\r\n\t\t\tvar optionlist = that.question.optionlist\r\n\t\t\tif(that.question_index<that.questionlist.length-1){\r\n\t\t\t\tthat.question_index++;\r\n\t\t\t}\r\n\t\t\tthat.question = that.questionlist[that.question_index]\r\n\t\t},\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\n.container{position: absolute;top: 80rpx;width: 100%;padding: 0 30rpx;}\r\n.progress{position: fixed;top: 0;width: 100%;left: 0;background: #f6f6f6;display: flex;align-items: center;height: 80rpx;padding: 0 5%;}\r\n.question{background: #fff;padding: 30rpx;border-radius: 16rpx;margin-bottom: 140rpx;}\r\n.question .title{ width: 100%; /*color: #222222; font-size: 34rpx;*/ font-weight: bold; } \r\n.option_group{margin-top: 30rpx;max-height: 1260rpx;overflow-y: scroll;}\r\n.option_group .option{background:#f6f6f6; border-radius: 10rpx;margin-bottom: 20rpx;line-height: 120%;padding:14rpx 20rpx;}\r\n.option_group .option .t1{ margin-left: 40rpx;}\r\n.option_group .option.on{ background: #61CD78; color:#fff;}\r\n.bottom{width: 100%;background:#f6f6f6;padding:20rpx;display: flex;justify-content: center;align-items: center;position: fixed;bottom: 0;}\n.bottom .btn{border-radius: 10rpx;background: #37A0D3;color: #FFFFFF;padding: 16rpx 40rpx;text-align: center;width: 43%;margin: 10rpx;}\r\n.btn.btn1{background: #09d1c8;}\r\n.btn.btn2{background: #ffc03b}\r\n.btn.btn0{opacity: 0;}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recordlog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recordlog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839432848\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}