{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/index.vue?2297", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/index.vue?478a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/index.vue?09c8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/index.vue?0732", "uni-app:///admin/member/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/index.vue?f386", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/index.vue?8ce1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pre_url", "datalist", "pagenum", "nomore", "nodata", "count", "keyword", "auth_data", "dkopen", "member_tel", "member_pwd", "is_add_member", "couponShow", "couponRes", "sendingQuantity", "numberSenders", "sendtype", "giftGiver", "numPush", "onLoad", "name", "id", "onPullDownRefresh", "onReachBottom", "methods", "pushAllChange", "that", "uni", "title", "pagenumA", "app", "<PERSON><PERSON><PERSON><PERSON>", "cpid", "persendnum", "ids", "setTimeout", "delta", "pushAll", "SendCoupons", "closePopop", "pushCoupons", "SelectMembers", "getdata", "searchChange", "searchConfirm", "loginUser", "mid", "str"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Hv1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;MACA;MACA;QAAAC;QAAAC;MAAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;kBAAAC;gBAAA;gBACAC;gBACAC;kBAAAxB;kBAAAJ;gBAAA;kBACA;oBACA;oBACA6B;sBAAA;oBAAA;oBACA;sBACAf;sBACAgB;sBACAC;sBACAC;oBACA;oBACAJ;sBACA;wBACAJ;wBACAA;wBACAC;0BAAAC;wBAAA;sBACA;wBACAE;wBACAK;0BACAR;4BACAS;0BACA;wBACA;sBACA;oBACA;kBACA;oBACAV;oBACAC;oBACAG;oBACAK;sBACAR;wBACAS;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAZ;MACA;QACAA;QACA;UACAV;UACAgB;UACAC;UACAC;QACA;QACAJ;UACAJ;UACA;YACAI;YACAJ;YACAS;cACAR;gBACAS;cACA;YACA;UACA;YACAN;YACAK;cACAR;gBACAS;cACA;YACA;UACA;QACA;MACA;IACA;IACAG;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACAX;IACA;IACAY;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAhB;MACAA;MACAA;MACAI;QAAAxB;QAAAJ;MAAA;QACAwB;QACAA;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAC;YACAC;UACA;UACAF;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAiB;MACA;IACA;IACAC;MACA;MACA;MACAlB;MACAA;IACA;IACAmB;MACA;MACAf;QAAAgB;QAAAC;MAAA;QACAjB;QACA;UACAA;UAEAK;YACAL;UACA;QAEA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5UA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/member/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/member/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3294ef6b&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/member/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3294ef6b&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var m0 = _vm.isload && g0 ? _vm.t(\"会员\") : null\n  var m1 =\n    _vm.isload && !_vm.dkopen && _vm.auth_data.member ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload ? _vm.t(\"优惠券\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入昵称/姓名/手机号搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"content\" v-if=\"datalist && datalist.length>0\">\r\n\t\t\t<view class=\"label\">\r\n\t\t\t\t<text class=\"t1\">{{t('会员')}}列表（共{{count}}人）</text>\r\n\t\t\t\t<view class=\"btn\" @tap=\"goto\" data-url=\"/admin/order/addmember?type=1\"  v-if=\"is_add_member\">添加会员</view>\r\n\t\t\t\t<view class=\"btn\" v-if=\"!couponShow\" @click=\"pushAll\">全部推送</view>\r\n\t\t\t</view>\r\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"goto\" :data-url=\"'detail?mid=' + item.id\">\r\n\t\t\t\t\t\t<image :src=\"item.headimg\"></image>\r\n\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t<view v-if=\"dkopen && item.realname\">\r\n\t\t\t\t\t\t\t\t{{item.realname}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"x1 flex-y-center\">\r\n\t\t\t\t\t\t\t\t{{item.nickname}}\r\n\t\t\t\t\t\t\t\t<image style=\"margin-left:10rpx;width:40rpx;height:40rpx\" :src=\"pre_url+'/static/img/nan2.png'\" v-if=\"item.sex==1\"></image>\r\n\t\t\t\t\t\t\t\t<image style=\"margin-left:10rpx;width:40rpx;height:40rpx\" :src=\"pre_url+'/static/img/nv2.png'\" v-if=\"item.sex==2\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-if=\"!dkopen\">\r\n\t\t\t\t\t\t\t\t<text class=\"x2\">最后访问：{{item.last_visittime}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"x2\">加入时间：{{item.createtime}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"x2\">{{item.province ? '' : item.province}}{{item.city ? '' : item.city}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"x2\" v-if=\"item.remark\" style=\"color:#a66;font-size:22rpx\">{{item.remark}}</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if='item.tel && dkopen'>\r\n\t\t\t\t\t\t\t\t<text class=\"x2\">手机号：{{item.tel}}</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if='couponShow'>\r\n\t\t\t\t\t\t<view class=\"f2\" v-if=\"!dkopen\">\r\n\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"goto\" :data-url=\"'detail?mid=' + item.id\">详情</view>\r\n\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"goto\" :data-url=\"'/admin/member/history?id='+item.id\">足迹</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.can_login\" class=\"btn\" @tap=\"loginUser(item.id,item.tel)\">登录</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\" v-else>\r\n\t\t\t\t\t\t\t<view class=\"btn\" @click=\"SelectMembers(item)\">选择</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<view class=\"btn\" @click=\"pushCoupons(item)\">推送</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t<view class=\"tabbar\" v-if=\"!dkopen\">\r\n\t\t\t<view class=\"tabbar-bot\"></view>\r\n\t\t\t<view class=\"tabbar-bar\" style=\"background-color:#ffffff;\">\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../member/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.member\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/member2.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text active\">{{t('会员')}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../kefu/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.zixun\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/zixun.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text\">咨询</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../finance/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.finance\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/finance.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text\">财务</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../index/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/my.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text\">我的</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 推送优惠券 -->\r\n\t\t<uni-popup id=\"popup\" ref=\"popup\" type=\"center\">\r\n\t\t\t<view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text class=\"uni-dialog-title-text\" :class=\"['uni-popup__'+dialogType]\">推送{{t('优惠券')}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<view class=\"uni-dialog-content-options\">\r\n\t\t\t\t\t\t<view class=\"uni-dialog-content-text\">{{t('优惠券')}}名称：</view>\r\n\t\t\t\t\t\t<view style=\"word-break: break-all;\">{{couponRes.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-content-options\">\r\n\t\t\t\t\t\t<view class=\"uni-dialog-content-text\">每人发送数量：</view>\r\n\t\t\t\t\t\t<input class=\"uni-dialog-input\" v-model=\"sendingQuantity\" type=\"text\" :placeholder=\"placeholder\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-content-options\">\r\n\t\t\t\t\t\t<view class=\"uni-dialog-content-text\">共计发送人数：</view>\r\n\t\t\t\t\t\t<view>{{numberSenders}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"closePopop\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"SendCoupons\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">发送</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</block>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n      count: 0,\r\n      keyword: '',\r\n      auth_data: {},\r\n\t\t\tdkopen:false,\r\n\t\t\tmember_tel:'',\r\n\t\t\tmember_pwd:'',\r\n\t\t\tis_add_member:1,\r\n\t\t\tcouponShow:true,\r\n\t\t\tcouponRes:{},\r\n\t\t\tsendingQuantity:1,\r\n\t\t\tnumberSenders:1,\r\n\t\t\tsendtype:0,\r\n\t\t\tgiftGiver:[],\r\n\t\t\tnumPush:0,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t\tif(opt.type) this.dkopen = true;\r\n\t\t// 判断是餐饮优惠券||优惠券 ,推送功能\r\n\t\tif(opt.coupon || opt.restaurantCoupon){\r\n\t\t\tthis.couponShow = false;\r\n\t\t\tthis.couponRes = {name:opt.name,id:opt.id};\r\n\t\t}\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  methods: {\r\n\t\t// 优惠券全部推送\r\n\t\tasync pushAllChange(){\r\n\t\t\tlet that = this;\r\n\t\t\tuni.showLoading({title:'正在推送...'});\r\n\t\t\tlet pagenumA = that.pagenum++;\r\n\t\t\tapp.post('ApiAdminMember/index', {keyword: '',pagenum: pagenumA}, function (res) {\r\n\t\t\t\tif(res.datalist.length){\r\n\t\t\t\t\tlet IdArr = [];\r\n\t\t\t\t\tIdArr = res.datalist.map(item => item.id);\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\tsendtype:0,\r\n\t\t\t\t\t\tcpid:that.couponRes.id,\r\n\t\t\t\t\t\tpersendnum:that.sendingQuantity,\r\n\t\t\t\t\t\tids:IdArr\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.post('ApiAdminCoupon/send',params,function(res){\r\n\t\t\t\t\t\tif(res.status){\r\n\t\t\t\t\t\t\tthat.pushAllChange();\r\n\t\t\t\t\t\t\tthat.numPush = that.numPush + res.sucnum;\r\n\t\t\t\t\t\t\tuni.showLoading({title:`已推送${that.numPush}人`});\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\tdelta:1\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},300)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\t\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.$refs.popup.close();\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tapp.success('推送完成');\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\tdelta:1\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},300)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tpushAll(){\r\n\t\t\tthis.sendtype = 1;\r\n\t\t\tthis.numberSenders = this.count;\r\n\t\t\tthis.giftGiver = [];\r\n\t\t\tthis.$refs.popup.open();\r\n\t\t},\r\n\t\tSendCoupons(){\r\n\t\t\tlet that = this;\r\n\t\t\tif(that.sendtype){\r\n\t\t\t\t// 全部推送\r\n\t\t\t\tthat.pushAllChange();\r\n\t\t\t}else{\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tsendtype:that.sendtype,\r\n\t\t\t\t\tcpid:that.couponRes.id,\r\n\t\t\t\t\tpersendnum:that.sendingQuantity,\r\n\t\t\t\t\tids:that.giftGiver\r\n\t\t\t\t}\r\n\t\t\t\tapp.post('ApiAdminCoupon/send',params,function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status){\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tthat.$refs.popup.close();\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta:1\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},300)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta:1\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},300)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tclosePopop(){\r\n\t\t\tthis.$refs.popup.close();\r\n\t\t},\r\n\t\t// 推送优惠券\r\n\t\tpushCoupons(item){\r\n\t\t\tthis.giftGiver = [];\r\n\t\t\tthis.giftGiver.push(item.id);\r\n\t\t\tthis.sendtype = 0;\r\n\t\t\tthis.numberSenders = 1;\r\n\t\t\tthis.sendingQuantity = 1;\r\n\t\t\tthis.$refs.popup.open();\r\n\t\t},\r\n\t\tSelectMembers(item){\r\n\t\t\tapp.goto('/admin/order/dkorder?mid='+item.id)\r\n\t\t},\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n      var keyword = that.keyword;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.loading = true;\r\n      app.post('ApiAdminMember/index', {keyword: keyword,pagenum: pagenum}, function (res) {\r\n        that.loading = false;\r\n\t\t\t\tthat.is_add_member = res.is_add_member ? res.is_add_member:0;\r\n        var data = res.datalist;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\tthat.count = res.count;\r\n\t\t\t\t\tthat.auth_data = res.auth_data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: that.t('会员') + '列表'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    searchChange: function (e) {\r\n      this.keyword = e.detail.value;\r\n    },\r\n    searchConfirm: function (e) {\r\n      var that = this;\r\n      var keyword = e.detail.value;\r\n      that.keyword = keyword;\r\n      that.getdata();\r\n    },\r\n\tloginUser:function (id,tel){\r\n\t\tvar str = id+tel\r\n\t\tapp.post(\"ApiAdminMember/adminLoginUser\", {mid:id,str:str}, function (res) {\r\n\t\t\tapp.showLoading(false);\r\n\t\t    if (res.status == 1) {\r\n\t\t    app.success(res.msg);\r\n\t\t\t\t\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tapp.goto('/pages/my/usercenter','redirect');\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t\r\n\t\t  } else {\r\n\t\t    app.error(res.msg);\r\n\t\t  }\r\n\t\t});\r\n\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n@import \"../common.css\";\r\n.topsearch{width:94%;margin:16rpx 3%;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\r\n.content{width: 94%;margin:0 3%;background: #fff;border-radius:16rpx}\r\n.content .label{display:flex;width: 100%;padding:24rpx 16rpx;color: #333;}\r\n.content .label .t1{flex:1}\r\n.content .label .t2{ width:300rpx;text-align:right}\r\n.content .label .btn{ border-radius:8rpx; padding:3rpx 12rpx;margin-left: 10px;border: 1px #999 solid; text-align:center; font-size:28rpx;color:#333;}\r\n\r\n.content .item{width: 100%;padding: 32rpx;border-top: 1px #e5e5e5 solid;min-height: 112rpx;display:flex;align-items:center;}\r\n.content .item image{width:90rpx;height:90rpx;}\r\n.content .item .f1{display:flex;flex:1}\r\n.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}\r\n.content .item .f1 .t2 .x1{color: #222;font-size:30rpx;}\r\n.content .item .f1 .t2 .x2{color: #999;font-size:24rpx}\r\n\r\n.content .item .f2{display:flex;flex-direction:column;width:auto;text-align:right;border-left:1px solid #e5e5e5}\r\n.content .item .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}\r\n.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}\r\n.content .item .btn{ border-radius:8rpx; padding:3rpx 12rpx;margin-left: 10px;border: 1px #999 solid; text-align:center; font-size:28rpx;color:#333;}\r\n.content .item .btn:nth-child(n+2) {margin-top: 10rpx;}\r\n.popup__options{display: flex;align-items: center;justify-content: flex-start;padding-bottom: 15rpx;}\r\n.popup__options .popup__options_text{width: 120rpx;text-align: right;}\r\n.popup__but{font-size: 14px;color: #007aff;display: table;margin: 30rpx auto 30rpx;}\r\n\r\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\r\n.uni-dialog-title {/* #ifndef APP-NVUE */display: flex;\t/* #endif */flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\r\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\r\n.uni-dialog-content {display: flex;flex-direction: column;justify-content: center;padding: 5px 15px 15px 15px;}\r\n.uni-dialog-content-options{display: flex;align-items: center;justify-content: flex-start;padding: 10rpx 0rpx;}\r\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;text-align: right;width: 228rpx;white-space: nowrap;}\r\n.uni-dialog-button-group {/* #ifndef APP-NVUE */display: flex;/* #endif */flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\r\n.uni-dialog-button {/* #ifndef APP-NVUE */display: flex;/* #endif */flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;\t/* #ifdef H5 */\tcursor: pointer;/* #endif */}\r\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\r\n.uni-dialog-button-text {font-size: 14px;}\r\n.uni-button-color {color: #007aff;}\r\n.uni-dialog-input {\tflex: 1;font-size: 14px;border: 1px #d1d1d1 solid;border-radius:5rpx;margin-right: 20rpx;padding-left: 10rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839447597\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}