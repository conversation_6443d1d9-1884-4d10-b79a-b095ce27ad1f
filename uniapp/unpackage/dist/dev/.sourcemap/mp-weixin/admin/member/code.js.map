{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/code.vue?4cd2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/code.vue?117a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/code.vue?4719", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/code.vue?f500", "uni-app:///admin/member/code.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/code.vue?a50b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/code.vue?d9cc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "platform", "pre_url", "code", "onLoad", "onPullDownRefresh", "methods", "getdata", "uni", "title", "that", "subconfirm", "app", "cardno", "<PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON>", "needResult", "scanType", "success", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuBt1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;MACA;MACAC;IAEA;IACAC;MACA;MACA;MACA;MACA;QACAC;QACA;MACA;MACAF;MACAE;QAAAT;QAAAU;MAAA;QACAH;QACA;UACAE;UACA;QACA;QACAA;QACAF;MACA;IACA;IACAI;MACA;MACA;QACAF;QAAA;MACA;QACA;QACAG;UAAA;UACAA;YACAC;YAAA;YACAC;YAAA;YACAC;cACA;cACA;gBACA;cACA;gBACA;gBACA;cACA;cACA;gBACA;gBACAf;cACA;cACAO;YACA;UACA;QACA;MACA;QACAF;UACAU;YACAC;YACA;YACA;cACA;YACA;cACA;cACA;YACA;YACA;cACA;cACAhB;YACA;YACAgB;YACAT;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzHA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/member/code.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/member/code.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./code.vue?vue&type=template&id=0ea69e38&\"\nvar renderjs\nimport script from \"./code.vue?vue&type=script&lang=js&\"\nexport * from \"./code.vue?vue&type=script&lang=js&\"\nimport style0 from \"./code.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/member/code.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=template&id=0ea69e38&\"", "var components\ntry {\n  components = {\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\n\t\t<form report-submit=\"true\" @submit=\"subconfirm\" style=\"width:100%\">\r\n\t\t\t<view class=\"title\">会员消费</view>\r\n\t\t\t\r\n\t\t\t<view class=\"inputdiv\">\r\n\t\t\t\t<input id=\"code\" type=\"text\" name=\"code\" :value=\"code\" placeholder-style=\"color:#666;\" placeholder=\"请输入会员码\"/>\r\n\t\t\t\t<view class=\"scanicon\" @tap=\"saoyisao\" v-if=\"platform!='h5'\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/scan-icon2.png'\"></image>\r\n\t\t\t\t</view>\t\r\n\t\t\t</view>\r\n\t\t\r\n\t\t\t<button class=\"btn\" form-type=\"submit\">确定</button>\r\n\t\t</form>\n\n\t</block>\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n      opt:{},\r\n      loading:false,\r\n      isload: false,\r\n      menuindex:-1,\r\n      platform:app.globalData.platform,\r\n      pre_url:app.globalData.pre_url,\r\n\t\t\tcode:''\r\n      \n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '会员消费'\n\t\t\t\t});\n\t\t\tthat.loaded();\n\t\t\t\n\t\t},\r\n\t\tsubconfirm: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var code = e.detail.value.code;\r\n\t\t\tvar cardno = '';\r\n\t\t\tif(code == ''){\r\n\t\t\t\tapp.error('请输入会员码');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthat.loading = true;\r\n\t\t  app.post('ApiAdminMember/searchCode', {code: code,cardno:cardno}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t    if (res.status == 0) {\r\n\t\t      app.error(res.msg);\r\n\t\t      return;\r\n\t\t    }\r\n\t\t    app.goto('../member/codebuy?mid='+res.mid);\r\n\t\t\t\tthat.loaded();\r\n\t\t  });\r\n\t\t},\n    saoyisao: function (d) {\r\n      var that = this;\r\n    \tif(app.globalData.platform == 'h5'){\r\n    \t\tapp.alert('请使用微信扫一扫功能扫码');return;\r\n    \t}else if(app.globalData.platform == 'mp'){\r\n    \t\tvar jweixin = require('jweixin-module');\r\n    \t\tjweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用\r\n    \t\t\tjweixin.scanQRCode({\r\n    \t\t\t\tneedResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\r\n    \t\t\t\tscanType: [\"qrCode\",\"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\r\n    \t\t\t\tsuccess: function (res) {\r\n    \t\t\t\t\tvar content = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\r\n    \t\t\t\t\tif(content.indexOf('?') === -1){\r\n    \t\t\t\t\t\tvar code = content;\r\n    \t\t\t\t\t}else{\r\n    \t\t\t\t\t\tvar contentArr = content.split('=');\r\n    \t\t\t\t\t\tvar code = contentArr.pop();\r\n    \t\t\t\t\t}\r\n    \t\t\t\t\tif(code.indexOf(',') !== -1){\r\n    \t\t\t\t\t\tvar contentArr = code.split(',');\r\n    \t\t\t\t\t\tcode = contentArr.pop();\r\n    \t\t\t\t\t}\r\n    \t\t\t\t\tthat.code = code;\r\n    \t\t\t\t}\r\n    \t\t\t});\r\n    \t\t});\r\n    \t}else{\r\n    \t\tuni.scanCode({\r\n    \t\t\tsuccess: function (res) {\r\n    \t\t\t\tconsole.log(res);\r\n    \t\t\t\tvar content = res.result;\r\n    \t\t\t\tif(content.indexOf('?') === -1){\r\n    \t\t\t\t\tvar code = content;\r\n    \t\t\t\t}else{\r\n    \t\t\t\t\tvar contentArr = content.split('=');\r\n    \t\t\t\t\tvar code = contentArr.pop();\r\n    \t\t\t\t}\r\n    \t\t\t\tif(code.indexOf(',') !== -1){\r\n    \t\t\t\t\tvar contentArr = code.split(',');\r\n    \t\t\t\t\tcode = contentArr.pop();\r\n    \t\t\t\t}\r\n\t\t\t\t\t\tconsole.log(code);\r\n    \t\t\t\tthat.code = code;\r\n    \t\t\t}\r\n    \t\t});\r\n    \t}\r\n    },\n  }\n};\r\n</script>\r\n<style>\r\n.container{display:flex;flex-direction:column;}\r\n.container .title{display:flex;justify-content:center;width:100%;color:#555;font-size:40rpx;text-align:center;height:100rpx;line-height:100rpx;margin-top:60rpx}\r\n.container .inputdiv{display:flex;width:90%;margin:0 auto;margin-top:40rpx;margin-bottom:40rpx;position:relative}\r\n.container .inputdiv input{background:#fff;width:100%;height:120rpx;line-height:120rpx;padding:0 40rpx;font-size:40rpx;border:1px solid #f5f5f5;border-radius:20rpx}\r\n.container .btn{ height: 88rpx;line-height: 88rpx;background: #FC4343;width:90%;margin:0 auto;border-radius:8rpx;margin-top:60rpx;color: #fff;font-size: 36rpx;}\r\n.container .f0{width:100%;margin-top:40rpx;height:60rpx;line-height:60rpx;color:#FC4343;font-size:30rpx;display:flex;align-items:center;justify-content:center}\r\n.container .scanicon{width:80rpx;height:80rpx;position:absolute;top:20rpx;right:20rpx;z-index:9}\n.container .scanicon image{width:100%;height:100%}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839442219\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}