{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shopRefundOrderDetail.vue?c4a0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shopRefundOrderDetail.vue?a645", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shopRefundOrderDetail.vue?b816", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shopRefundOrderDetail.vue?4aad", "uni-app:///admin/order/shopRefundOrderDetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shopRefundOrderDetail.vue?1db0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shopRefundOrderDetail.vue?9a94"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "order", "detail", "prolist", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "setremark", "setremarkconfirm", "type", "orderid", "content", "setTimeout", "refundnopassShow", "refundnopass", "remark", "release", "refundpass", "returnpass"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,8BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AACyE;AACL;AACa;;;AAGjF;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2FAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAAm1B,CAAgB,mzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0Nv2B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;MACA;MACAH;QAAAI;QAAAC;QAAAC;MAAA;QACAN;QACAO;UACAR;QACA;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;MACA;MACA;MACAT;MACAA;QAAAI;QAAAC;QAAAK;QAAAC;MAAA;QACAX;QACAA;QACAO;UACAR;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;MACAZ;QACAA;QACAA;UAAAI;UAAAC;UAAAM;QAAA;UACAX;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;MACAb;QACAA;QACAA;UAAAI;UAAAC;UAAAM;QAAA;UACAX;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7TA;AAAA;AAAA;AAAA;AAAgsC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACAptC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/shopRefundOrderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/shopRefundOrderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shopRefundOrderDetail.vue?vue&type=template&id=0d7913f2&\"\nvar renderjs\nimport script from \"./shopRefundOrderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./shopRefundOrderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shopRefundOrderDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/shopRefundOrderDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopRefundOrderDetail.vue?vue&type=template&id=0d7913f2&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var g0 = _vm.isload\n    ? _vm.detail.refund_pics && _vm.detail.refund_pics.length > 0\n    : null\n  var m1 = _vm.isload && _vm.order.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload && _vm.order.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && _vm.order.scoredk_money > 0 ? _vm.t(\"积分\") : null\n  var m4 = _vm.isload && _vm.order.dec_money > 0 ? _vm.t(\"余额\") : null\n  var m5 =\n    _vm.isload && _vm.order.combine_money && _vm.order.combine_money > 0\n      ? _vm.t(\"余额\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopRefundOrderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopRefundOrderDetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop_refund.jpg);background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==0\">\n\t\t\t\t<view class=\"t1\">已取消</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==1\">\n\t\t\t\t<view class=\"t1\">待审核</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==2\">\n\t\t\t\t<view class=\"t1\">审核通过，已退款</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==3\">\n\t\t\t\t<view class=\"t1\">驳回</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==4\">\n\t\t\t\t<view class=\"t1\">审核通过，待退货</view>\n\t\t\t\t<view class=\"t2\">联系买家进行退货</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- <view class=\"address\">\n\t\t\t<view class=\"img\">\n\t\t\t\t<image src=\"/static/img/address3.png\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"info\">\n\t\t\t\t<text class=\"t1\">{{detail.linkman}} {{detail.tel}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\">地址：{{detail.area}}{{detail.address}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openLocation\" :data-address=\"storeinfo.address\" :data-latitude=\"storeinfo.latitude\" :data-longitude=\"storeinfo.longitude\">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text>\n\t\t\t</view>\n\t\t</view> -->\n\t\t<view class=\"product\">\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"content\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\">\n\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\">×{{item.refund_num}}</text></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">类型</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_type_label}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">退货单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.refund_ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">申请退款金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">本单已退款金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.refundMoneyTotal}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">申请时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 grey\" v-if=\"detail.refund_status==0\">已取消</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">待审核</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==4\">审核通过，待退货</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款原因</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">图片</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.refund_pics && detail.refund_pics.length > 0\"><block v-for=\"item in detail.refund_pics\"><image :src=\"item\" class=\"imageMin\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item\"/></block></text>\n\t\t\t\t<text class=\"t2\" v-else>无</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\n\t\t\t\t<text class=\"t1\">审核备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{order.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{order.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.status>0 && order.paytypeid!='4' && order.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{order.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.status>0 && order.paytime\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{order.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.status>1 && order.send_time\">\n\t\t\t\t<text class=\"t1\">发货时间</text>\n\t\t\t\t<text class=\"t2\">{{order.send_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.status==3 && order.collect_time\">\n\t\t\t\t<text class=\"t1\">收货时间</text>\n\t\t\t\t<text class=\"t2\">{{order.collect_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n      <view class=\"item\" v-if=\"order.issource && order.source && order.source == 'supply_zhenxin'\">\n      \t<text class=\"t1\">商品来源</text>\n      \t<text class=\"t2\">甄新汇选</text>\n      </view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">商品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{order.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{order.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.manjian_money > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{order.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">配送方式</text>\n\t\t\t\t<text class=\"t2\">{{order.freight_text}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.freight_type==1 && order.freight_price > 0\">\n\t\t\t\t<text class=\"t1\">服务费</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{order.freight_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.freight_time\">\n\t\t\t\t<text class=\"t1\">{{order.freight_type!=1?'配送':'提货'}}时间</text>\n\t\t\t\t<text class=\"t2\">{{order.freight_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"order.coupon_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{order.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"order.scoredk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{order.scoredk_money}}</text>\n\t\t\t</view>\n      <view class=\"item\" v-if=\"order.dec_money > 0\">\n        <text class=\"t1\">{{t('余额')}}抵扣</text>\n        <text class=\"t2 red\">-¥{{order.dec_money}}</text>\n      </view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{order.totalprice}}</text>\n\t\t\t</view>\n      <view class=\"item\" v-if=\"order.combine_money && order.combine_money > 0\">\n      \t<text class=\"t1\">{{t('余额')}}已付</text>\n      \t<text class=\"t2 red\">-¥{{order.combine_money}}</text>\n      </view>\n      <view class=\"item\" v-if=\"order.paytypeid == 2 && order.combine_wxpay && order.combine_wxpay > 0\">\n      \t<text class=\"t1\">微信已付</text>\n      \t<text class=\"t2 red\">-¥{{order.combine_wxpay}}</text>\n      </view>\n      <view class=\"item\" v-if=\"(order.paytypeid == 3 || (order.paytypeid>=302 && order.paytypeid<=330)) && order.combine_alipay && order.combine_alipay > 0\">\n      \t<text class=\"t1\">支付宝已付</text>\n      \t<text class=\"t2 red\">-¥{{order.combine_alipay}}</text>\n      </view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"order.status==0\">未付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"order.status==1\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"order.status==2\">已发货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"order.status==3\">已收货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"order.status==4\">已关闭</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:calc(160rpx + env(safe-area-inset-bottom));\"></view>\n\n\t\t<view class=\"bottom notabbarbot\">\n\t\t\t<block v-if=\"detail.refund_status==1 && detail.cancheck\">\n\t\t\t\t<view class=\"btn2\" @tap=\"refundnopassShow\" :data-id=\"detail.id\">驳回</view>\n\t\t\t\t<view v-if=\"detail.refund_type == 'refund'\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\">退款通过</view>\n\t\t\t\t<view v-else-if=\"detail.refund_type == 'return'\" class=\"btn2\" @tap=\"returnpass\" :data-id=\"detail.id\">退货退款通过</view>\n\t\t\t</block>\n\t\t\t<view v-if=\"detail.refund_type == 'return' && detail.refund_status==4 && detail.cancheck\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\" :data-title=\"'确定要收到退货并退款吗？'\">收货并退款</view>\n\t\t\t<!-- <view class=\"btn2\" @tap=\"setremark\" :data-id=\"detail.id\">设置备注</view> -->\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetremark\" ref=\"dialogSetremark\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"确定要驳回申请吗？\" :value=\"detail.remark\" placeholder=\"请输入备注\" @confirm=\"refundnopass\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\n\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      order: {},\n      detail: \"\",\n      prolist: \"\"\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminOrder/shopRefundOrderDetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.order = res.order;\n\t\t\t\tthat.prolist = res.prolist;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tsetremark:function(){\n\t\t\tthis.$refs.dialogSetremark.open();\n\t\t},\n\t\tsetremarkconfirm: function (done, remark) {\n\t\t\tthis.$refs.dialogSetremark.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminOrder/setremark', { type:'shop',orderid: that.detail.id,content:remark }, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n    },\n\t\trefundnopassShow: function (e) {\n\t\t\tthis.$refs.dialogSetremark.open();\n\t\t},\n\t\trefundnopass: function (done, remark) {\n\t\t\tthis.$refs.dialogSetremark.close();\n\t\t\tvar that = this;\n\t\t\tvar orderid = that.detail.id;\n\t\t\tapp.showLoading();\n\t\t\tapp.post('ApiAdminOrder/refundnopass', { type:'shop',orderid: orderid,remark:remark, release:'2106'}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success(data.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\trefundpass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id;\n\t\t\tvar title = e.currentTarget.dataset.title;\n\t\t\tapp.confirm(title ? title : '确定要审核通过并退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundpass', { type:'shop',orderid: orderid, release:'2106' }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\treturnpass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定同意买家退货退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/returnpass', { type:'shop',orderid: orderid, release:'2106' }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n  }\n};\n</script>\n<style>\n.imageMin {width:130rpx;height:auto; margin-right: 6rpx;}\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%; height:calc(92rpx + env(safe-area-inset-bottom));padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;padding:0 30rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.picker{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopRefundOrderDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopRefundOrderDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839439921\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}