{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?55fc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?ed3c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?dcab", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?e191", "uni-app:///admin/order/cycleorderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?0707", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?7f9a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?93c6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleorderdetail.vue?b591"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "opt", "pre_url", "detail", "djs", "shopset", "storeinfo", "onLoad", "methods", "getdata", "app", "id", "that", "interval", "toPlanDetail", "toclose", "orderid", "setTimeout", "todel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACa;AACwB;;;AAGpG;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA80B,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqLl2B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;QAAAC;MAAA;QACA;UACAD;UACAA;YACAA;UACA;UACA;QACA;UACAE;UACAA;UACAA;UACA;YACAC;cACAD;cACAA;YACA;UACA;UACAA;UACAF;QACA;MAGA;IACA;IACAI;MACA;MACA;MACA;QACA;MACA;QACAJ;MACA;IACA;IACAK;MACA;MACA;MACAL;QACAA;QACAA;UAAAM;QAAA;UACAN;UACAA;UACAO;YACAL;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;MACAR;QACAA;QACAA;UAAAM;QAAA;UACAN;UACAA;UACAO;YACAP;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxQA;AAAA;AAAA;AAAA;AAA2rC,CAAgB,2mCAAG,EAAC,C;;;;;;;;;;;ACA/sC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAmtC,CAAgB,moCAAG,EAAC,C;;;;;;;;;;;ACAvuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/cycleorderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/cycleorderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cycleorderdetail.vue?vue&type=template&id=2094ad30&scoped=true&\"\nvar renderjs\nimport script from \"./cycleorderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./cycleorderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cycleorderdetail.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./cycleorderdetail.vue?vue&type=style&index=1&id=2094ad30&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2094ad30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/cycleorderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleorderdetail.vue?vue&type=template&id=2094ad30&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.detail.formdata.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleorderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleorderdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"isload\">\r\n\t\t\r\n\t\t<view class=\"banner\" v-if=\"detail.refund_status ==1 || detail.refund_status ==1\">\r\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_banner.png'\" class=\"banner_img\" alt=\"\" />\r\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_icon.png'\" class=\"banner_icon\" alt=\"\" />\r\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.refund_status==1\">\r\n\t\t\t\t<view class=\"banner_title\">退款中</view>\r\n\t\t\t\t<view class=\"banner_text\" >\r\n\t\t\t\t\t等待平台审核中\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.refund_status==2\">\r\n\t\t\t\t<view class=\"banner_title\">退款成功</view>\r\n\t\t\t\t<view class=\"banner_text\" >\r\n\t\t\t\t\t审核通过，已退款\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"banner\" v-else>\r\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_banner.png'\" class=\"banner_img\" alt=\"\" />\r\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_icon.png'\" class=\"banner_icon\" alt=\"\" />\r\n\t\t\t<view class=\"banner_data\" v-if=\"detail.status==0\">\r\n\t\t\t\t<view class=\"banner_title\">\r\n\t\t\t\t\t待支付\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"banner_text\" v-if=\"detail.status == 0\">\r\n\t\t\t\t\t等待用户支付中\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==1\">\r\n\t\t\t\t<view class=\"banner_title\" >\r\n\t\t\t\t\t<text v-if=\"detail.freight_type ==1\">待取货</text>\r\n\t\t\t\t\t<text v-else>待发货</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"banner_text\" >\r\n\t\t\t\t\t<view class=\"t1\">{{detail.paytype=='货到付款' ? '已选择'+codtxt : '已成功付款'}}\r\n\t\t\t\t\t\t<text v-if=\"detail.freight_type!=1\">，我们会尽快为您配送</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==2\">\r\n\t\t\t\t<view class=\"banner_title\"  v-if=\"detail.freight_type ==1\">已取货</view>\r\n\t\t\t\t<view class=\"banner_title\"  v-else>已发货</view>\r\n\t\t\t\t<view class=\"banner_text\" v-if=\"detail.status == 0\">\r\n\t\t\t\t\t具体发货信息请查看每期订单\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==3\">\r\n\t\t\t\t<view class=\"banner_title\">已完成</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==4\">\r\n\t\t\t\t<view class=\"banner_title\">已关闭</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"body\">\r\n\t\t\t<view class=\"address\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<text class=\"t1\" user-select=\"true\" selectable=\"true\">{{detail.linkman}} {{detail.tel}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">地址：{{detail.area}}{{detail.address}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openMendian\" :data-storeinfo=\"storeinfo\" user-select=\"true\" selectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}} </text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"body_title\">\r\n\t\t\t\t服务信息\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"body_module\">\r\n\t\t\t\t<img :src=\"detail.propic\"\r\n\t\t\t\t\tclass=\"body_img\" alt=\"\" />\r\n\t\t\t\t<view class=\"body_data\">\r\n\t\t\t\t\t<view class=\"body_name\">\r\n\t\t\t\t\t\t{{detail.proname}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"body_text\">\r\n\t\t\t\t\t\t{{detail.ggname}} | {{detail.pspl}}<text v-if=\"detail.every_day\">,{{detail.every_day}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"body_price flex flex-bt flex-y-bottom\">\r\n\t\t\t\t\t\t<text>￥{{detail.sell_price}}</text>\r\n\t\t\t\t\t\t<!-- <text class=\"body_num\">x{{detail.num}}</text> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>下单人</text>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\r\n\t\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>会员ID</text>\r\n\t\t\t\t<text>{{detail.mid}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>订单号</text>\r\n\t\t\t\t<text>{{detail.ordernum}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>下单时间</text>\r\n\t\t\t\t<text>{{detail.createtime}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>配送期数</text>\r\n\t\t\t\t<text>共 {{detail.qsnum}} 期</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>每期数量</text>\r\n\t\t\t\t<text>共 {{detail.num}} 件</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>配送计划</text>\r\n\t\t\t\t<view class=\"body_time\" @tap=\"toPlanDetail\" :data-url=\"'cycleplanlist?id=' + detail.id\">\r\n\t\t\t\t\t<view>{{detail.start_date}}起<text style=\"margin-left: 10rpx;\" v-if=\"detail.every_day\">{{detail.every_day}}</text><text style=\"margin-left: 10rpx;\" v-else>{{detail.pspl}}</text></view>\r\n\t\t\t\t\t<img :src=\"pre_url+'/static/img/week/week_detail.png'\" alt=\"\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>配送方式</text>\r\n\t\t\t\t<text>{{detail.freight_text}}</text>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"body_list\">\r\n\t\t\t\t<text>备注信息</text>\r\n\t\t\t\t<text v-if=\"detail.remark\">{{detail.remark}}</text>\r\n\t\t\t\t<text v-else>暂无</text>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>商品总金额</text>\r\n\t\t\t\t<text class=\"body_color\">￥{{detail.product_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\"  v-if=\"detail.leveldk_money > 0\">\r\n\t\t\t\t<text>会员折扣</text>\r\n\t\t\t\t<text class=\"body_color\">-￥{{detail.leveldk_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\" v-if=\"detail.coupon_money > 0\">\r\n\t\t\t\t<text>优惠券抵扣</text>\r\n\t\t\t\t<text class=\"body_color\">-￥{{detail.coupon_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"body_list\">\r\n\t\t\t\t<text>实付金额</text>\r\n\t\t\t\t<text class=\"body_color\">￥{{detail.totalprice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"body_list\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t<text class=\"t1\">退款状态</text>\r\n\t\t\t\t<text class=\"t2 body_color\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 body_color\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 body_color\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\r\n\t\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\r\n\t\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- <view class=\"opt\">\r\n\t\t\t<view class=\"opt_module\">\r\n\t\t\t\t<view class=\"opt_btn\" @tap=\"toclose\" :data-id=\"detail.id\" v-if=\"detail.status == 0\">关闭订单</view>\r\n\t\t\t\t<view class=\"opt_btn\" style=\"    background: #FF9900;color: #fff;border: none;\" v-if=\"detail.status == 0\" @tap=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + detail.payorderid\">去支付</view>\r\n\t\t\t\t<view class=\"opt_btn\" v-if=\"detail.status == 4\" @tap=\"todel\" :data-id=\"detail.id\" >删除订单</view>\r\n\t\t\t\t<view class=\"opt_btn\" v-if=\"detail.status==3 && detail.iscomment==0 && shopset.comment==1\" @tap.stop=\"goto\" :data-url=\"'comment?orderid=' + detail.id\">去评价</view>\r\n\t\t\t\t<view class=\"opt_btn\" v-if=\"detail.status==3 && detail.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?orderid=' + detail.id\">查看评价</view>\r\n\t\t\t\t<view style=\"width: 100%;\" class=\"flex flex-x-bottom\" v-if=\"detail.status !=3 && detail.status !=0  && (detail.refund_status==3 ||detail.refund_status==0 )\">\r\n\t\t\t\t\t<view class=\"opt_btn\"  @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + detail.id\">申请退款</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisload: false,\r\n\t\t\t\topt:{},\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tdetail:{},\r\n\t\t\t\tdjs: '',\r\n\t\t\t\tshopset:{},\r\n\t\t\t\tstoreinfo:[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t\tapp.get('ApiAdminOrder/cycleorderdetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\t\tif(res.status == 0){\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tapp.alert(res.msg, function() {\r\n\t\t\t\t\t\t\tapp.goback()\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn \r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\t\tthat.storeinfo = res.storeinfo;\r\n\t\t\t\t\t\tif (res.lefttime > 0) {\r\n\t\t\t\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\r\n\t\t\t\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoPlanDetail(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t var url = e.currentTarget.dataset.url;\r\n\t\t\t\tif(that.detail.status==0){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.goto(url);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoclose: function (e) {\r\n\t\t\t  var that = this;\r\n\t\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t\t  app.confirm('确定要取消该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t    app.post('ApiCycle/closeOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t      app.success(data.msg);\r\n\t\t\t      setTimeout(function () {\r\n\t\t\t        that.getdata();\r\n\t\t\t      }, 1000);\r\n\t\t\t    });\r\n\t\t\t  });\r\n\t\t\t},\r\n\t\t\ttodel: function (e) {\r\n\t\t\t  var that = this;\r\n\t\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t\t  app.confirm('确定要删除该订单吗?', function () {\r\n\t\t\t\t\t\tapp.showLoading('删除中');\r\n\t\t\t    app.post('ApiCycle/delOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t      app.success(data.msg);\r\n\t\t\t      setTimeout(function () {\r\n\t\t\t        app.goback(true);\r\n\t\t\t      }, 1000);\r\n\t\t\t    });\r\n\t\t\t  });\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground: #F6F6F6;\r\n\t}\r\n</style>\r\n<style scoped>\r\n\t.banner {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.banner_img {\r\n\t\twidth: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.banner_icon {\r\n\t\tposition: absolute;\r\n\t\tright: 70rpx;\r\n\t\ttop: 30rpx;\r\n\t\theight: 124rpx;\r\n\t\twidth: 124rpx;\r\n\t}\r\n\r\n\t.banner_data {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 60rpx 60rpx 0 60rpx;\r\n\t}\r\n\r\n\t.banner_title {\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #FFFFFF;\r\n\t}\r\n\r\n\t.banner_text {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: rgba(255, 255, 255, 0.6);\r\n\t\tmargin-top: 28rpx;\r\n\t}\r\n\r\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\r\n.address .img{width:40rpx}\r\n.address image{width:40rpx; height:40rpx;}\r\n.address .info{flex:1;display:flex;flex-direction:column;}\r\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\r\n.address .info .t2{font-size:24rpx;color:#999} \r\n\r\n\t.body {\r\n\t\tposition: relative;\r\n\t\twidth: 690rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 30rpx 30rpx 0 30rpx;\r\n\t\tmargin: -235rpx auto 0 auto;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.body_title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #111111;\r\n\t}\r\n\r\n\t.body_module {\r\n\t\tpadding: 45rpx 0 20rpx 0;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.body_data {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.body_img {\r\n\t\twidth: 172rpx;\r\n\t\theight: 172rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-right: 30rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.body_name {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #323232;\r\n\t}\r\n\r\n\t.body_text {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #999999;\r\n\t\tmargin-top: 15rpx;\r\n\t}\r\n\r\n\t.body_price {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-family: Arial;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #FD4A46;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.body_tag {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #FD4A46;\r\n\t}\r\n\r\n\t.body_num {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #222222;\r\n\t}\r\n\r\n\t.body_list {\r\n\t\tposition: relative;\r\n\t\twidth: 630rpx;\r\n\t\theight: 88rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #222222;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #f7f7f7;\r\n\t}\r\n\r\n\t.body_list:last-child {\r\n\t\tborder-bottom: 0;\r\n\t}\r\n\r\n\t.body_time {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.body_time img {\r\n\t\theight: 35rpx;\r\n\t\twidth: 35rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.body_color {\r\n\t\tcolor: #FF5347;\r\n\t}\r\n\r\n\t.opt {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 105rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.opt_module {\r\n\t\tposition: fixed;\r\n\t\theight: 105rpx;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tbottom: 0;\r\n\t\tpadding: 0 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tbox-shadow: 0px 0px 18px 0px rgba(132, 132, 132, 0.3200);\r\n\t}\r\n\r\n\t.opt_btn {\r\n\t\twidth: 160rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tbackground: #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t\tborder: 1px solid #cdcdcd\r\n\t}\r\n\r\n/* \t.opt_btn:last-child {\r\n\t\tcolor: #fff;\r\n\t\tbackground: #FD4A46;\r\n\t} */\r\n\t.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n\t.hxqrbox .img{width:400rpx;height:400rpx}\r\n\t.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n\t.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\t.orderinfo{width:100%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 0;background: #FFF;}\r\n\t.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n\t.orderinfo .item:last-child{ border-bottom: 0;}\r\n\t.orderinfo .item .t1{width:200rpx;flex-shrink:0;font-size: 26rpx}\r\n\t.orderinfo .item .t2{flex:1;text-align:right}\r\n\t.orderinfo .item .t3{ margin-top: 3rpx;}\r\n\t.orderinfo .item .red{color:red}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleorderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleorderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839442166\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleorderdetail.vue?vue&type=style&index=1&id=2094ad30&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleorderdetail.vue?vue&type=style&index=1&id=2094ad30&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839442024\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}