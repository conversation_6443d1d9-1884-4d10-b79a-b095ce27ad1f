{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporder.vue?00ad", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporder.vue?c155", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporder.vue?7a9f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporder.vue?e499", "uni-app:///admin/order/shoporder.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporder.vue?9eba", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporder.vue?d237"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "codtxt", "keyword", "mid", "wifiprintdata", "morewifiprintShow", "wifiprintAuth", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "getdata", "that", "app", "changetab", "uni", "scrollTop", "duration", "searchConfirm", "wifiprintChexbox", "wif<PERSON><PERSON>t", "ids"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwF31B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAd;QAAAD;QAAAN;QAAAE;MAAA;QACAkB;QACAA;QACA;QACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACApB;MACA;MACAY;MACAC;QAAAQ;MAAA;QACAT;QACA;UACAC;UACA;QACA;UACAA;UACA;QACA;MAEA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACpNA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/shoporder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/shoporder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shoporder.vue?vue&type=template&id=4710048e&\"\nvar renderjs\nimport script from \"./shoporder.vue?vue&type=script&lang=js&\"\nexport * from \"./shoporder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shoporder.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/shoporder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporder.vue?vue&type=template&id=4710048e&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.wifiprintAuth ? _vm.inArray(item.id, [1, 2]) : null\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporder.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<dd-tab :itemdata=\"['全部','待付款','待发货','待收货','已完成']\" :itemst=\"['all','0','1','2','3']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\r\n\t\t<view style=\"width:100%;height:100rpx\"></view>\r\n\t\t<!-- #ifndef H5 || APP-PLUS -->\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"morewifiprintShow\" @click=\"wifiprint\" data-type=\"2\" class=\"btn2\">批量打印</view>\r\n\t\t</view>\r\n\t\t<!--  #endif -->\r\n\t\t<view class=\"order-content\">\r\n\t\t\t<checkbox-group class=\"radio-group\" name=\"gettj\" @change=\"wifiprintChexbox\">\r\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view class=\"order-box\" @tap.stop=\"goto\" :data-url=\"'shoporderdetail?id=' + item.id\">\r\n\t\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t\t<view>订单号：{{item.ordernum}}</view>\r\n\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t<text v-if=\"item.status==0\" class=\"st0\">待付款</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type!=1\" class=\"st1\">待发货</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type==1\" class=\"st1\">待提货</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==2\" class=\"st2\">待收货</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st3\">已完成</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">已关闭</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<block v-for=\"(item2, idx) in item.prolist\" :key=\"idx\">\r\n\t\t\t\t\t\t<view class=\"content\" :style=\"idx+1==item.procount?'border-bottom:none':''\">\r\n\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/pages/shop/product?id=' + item2.proid\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item2.pic\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item2.name}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\">{{item2.ggname}}</text>\r\n\t\t\t\t\t\t\t\t<block>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"item2.product_type && item2.product_type==2\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">{{item2.real_sell_price}}元/斤</text><text class=\"x2\">×{{item2.real_total_weight}}斤</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-else><text class=\"x1 flex1\">￥{{item2.sell_price}}</text><text class=\"x2\">×{{item2.num}}</text></view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<view class=\"t2 tgr\" v-if=\"item2.has_glassrecord\">\r\n\t\t\t\t\t\t\t\t{{item2.glassrecord.name}} \r\n\t\t\t\t\t\t\t\t{{item2.glassrecord.nickname?item2.glassrecord.nickname:''}} \r\n\t\t\t\t\t\t\t\t{{item2.glassrecord.check_time?item2.glassrecord.check_time:''}}\r\n\t\t\t\t\t\t\t\t{{item2.glassrecord.typetxt}}\r\n<!--\t\t\t\t\t\t\t\t<block>\r\n\t\t\t\t\t\t\t\t\t<text class=\"pdl10\" v-if=\"item2.glassrecord.double_ipd==0\">{{item2.glassrecord.ipd?'PD'+item2.glassrecord.ipd:''}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"pdl10\" v-else> PD R{{item2.glassrecord.ipd_right}} L{{item2.glassrecord.ipd_left}}</text>\r\n\t\t\t\t\t\t\t\t</block>-->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n          <view v-if=\"item.crk_givenum && item.crk_givenum>0\" style=\"color:#f60;line-height:70rpx\">+随机赠送{{item.crk_givenum}}件</view>\r\n          <view class=\"bottom\" v-if=\"item.isdygroupbuy && item.isdygroupbuy==1\">\r\n          \t<text style=\"color:red\">抖音团购券</text>\r\n          </view>\r\n\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t<text>共计{{item.procount}}件商品 实付:￥{{item.totalprice}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red\"> 退款中￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red\"> 已退款￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red\"> 退款申请已驳回</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bottom flex-y-center\" v-if=\"item.mid>0\">\r\n\t\t\t\t\t\t<image :src=\"item.member.headimg\" style=\"width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx\"/><text style=\"font-weight:bold;color:#333;margin-right:8rpx\">{{item.member.nickname}}</text>(ID:{{item.mid}})\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\"  v-if=\"wifiprintAuth\">\r\n\t\t\t\t\t\t<view @click.stop=\"\">\r\n\t\t\t\t\t\t\t<checkbox :value=\"''+item.id\" :checked=\"inArray(item.id,[1,2])?true:false\"></checkbox>\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view :data-id=\"item.id\"  @click.stop=\"wifiprint\" data-type=\"1\" class=\"btn2\">打印小票</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t</checkbox-group>\r\n\t\t</view>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n      st: 'all',\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n      codtxt: \"\",\r\n\t\t\tkeyword:\"\",\r\n\t\t\tmid:'',\r\n\t\t\twifiprintdata:[],\r\n\t\t\tmorewifiprintShow:false,\r\n\t\t\twifiprintAuth:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt && this.opt.st){\r\n\t\t\tthis.st = this.opt.st;\r\n\t\t}\r\n\t\tif(this.opt && this.opt.mid){\r\n\t\t\tthis.mid = this.opt.mid;\r\n\t\t}\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n\tonNavigationBarSearchInputConfirmed:function(e){\r\n\t\tthis.searchConfirm({detail:{value:e.text}});\r\n\t},\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n\t\t\tvar mid = that.mid\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.loading = true;\r\n      app.post('ApiAdminOrder/shoporder', {mid:mid,keyword:that.keyword,st: st,pagenum: pagenum}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.wifiprintAuth = res.wifiprintAuth;\r\n        var data = res.datalist;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    changetab: function (st) {\r\n      this.st = st;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n    },\r\n\tsearchConfirm:function(e){\r\n\t\tthis.keyword = e.detail.value;\r\n\t\tthis.getdata(false);\r\n\t},\r\n\twifiprintChexbox:function(e){\r\n\t\tvar value =  e.detail.value;\r\n\t\tthis.wifiprintdata = value;\r\n\t\tif(this.wifiprintdata.length > 0){\r\n\t\t\tthis.morewifiprintShow = true;\r\n\t\t}else{\r\n\t\t\tthis.morewifiprintShow = false;\r\n\t\t}\r\n\t},\r\n\twifiprint(e){\r\n\t\tvar that = this;\r\n\t\tvar wifiprintdata = that.wifiprintdata;\r\n\t\tvar type = e.currentTarget.dataset.type;\r\n\t\tif(type ==1){\r\n\t\t\tvar wifiprintdata = [];\r\n\t\t\twifiprintdata.push(e.currentTarget.dataset.id);\r\n\t\t}\r\n\t\t that.loading = true;\r\n\t\t app.post('ApiAdminOrder/wifiprint', {ids:wifiprintdata}, function (res) {\r\n\t\t\t  that.loading = false;\r\n\t\t\t if(res.status==1){\r\n\t\t\t\tapp.success('打印成功');\r\n\t\t\t\treturn;\r\n\t\t\t }else{\r\n\t\t\t\t app.error(res.msg);\r\n\t\t\t\t return;\r\n\t\t\t }\r\n\t\t\t\r\n\t\t })\r\n\t},\r\n\t\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{ width:100%}\r\n.topsearch{width:94%;margin:10rpx 3%;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\r\n.order-content{display:flex;flex-direction:column}\r\n.order-box{ width: 94%;margin:10rpx 3%;padding:16rpx 3%; background: #fff;border-radius:8px}\r\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\r\n.order-box .head .f1{display:flex;align-items:center;color:#333}\r\n.order-box .head .f1 image{width:34rpx;height:34rpx;margin-right:4px}\r\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\r\n.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\r\n.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\r\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\r\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\r\n\r\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative;align-items: center;}\r\n.order-box .content:last-child{ border-bottom: 0; }\r\n.order-box .content image{ width: 140rpx; height: 140rpx;}\r\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.order-box .content .detail .x1{ flex:1}\r\n.order-box .content .detail .x2{ width:110rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n\r\n.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n.order-box .op{ display:flex;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n\r\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\r\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n.tgr{font-size: 24rpx;}\r\n.pdl10{padding-left: 10rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporder.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporder.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839439906\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}