{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/yuyueorderdetail.vue?515f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/yuyueorderdetail.vue?c3e4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/yuyueorderdetail.vue?4969", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/yuyueorderdetail.vue?7b9a", "uni-app:///admin/order/yuyueorderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/yuyueorderdetail.vue?2ed4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/yuyueorderdetail.vue?220c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "prodata", "djs", "iscommentdp", "detail", "payorder", "prolist", "storeinfo", "lefttime", "codtxt", "pay_transfer_info", "invoice", "selectExpressShow", "express_content", "p<PERSON><PERSON><PERSON><PERSON>", "peisonguser2", "index2", "showlist", "text", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "getdjs", "setremark", "setremarkconfirm", "type", "orderid", "content", "setTimeout", "todel", "refundnopass", "refundpass", "orderCollect", "peisong", "worker_id", "dialogPeisongClose", "peisong<PERSON>hange", "confirmPeisong", "showhxqr", "closeHxqr", "openLocation", "uni", "latitude", "longitude", "name", "scale", "logistics", "hideSelectExpressDialog", "ispay", "closeOrder"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACa;;;AAG5E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAA80B,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwTl2B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA,oCACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MAEA;QACAJ;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACA;MACAL;QAAAM;QAAAC;QAAAC;MAAA;QACAR;QACAS;UACAV;QACA;MACA;IACA;IACAW;MACA;MACA;MACAV;QACAA;QACAA;UAAAO;QAAA;UACAP;UACAA;UACAS;YACAT;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;MACAX;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACAZ;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;MACAb;QACAA;QACAA;UAAAO;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAe;MACA;MACAf;MACAC;QAAAM;QAAAC;MAAA;QACAR;QACA;QACA;QACA;QACA;QAEA;QACA;UACAV;QACA;QACAU;QACAA;QACA;UACAA;QACA;UACA;YACA;UACA;YACA;UACA;UACA;YACA;UACA;YACA;UACA;UACAC;YACAA;YACAA;cAAAM;cAAAC;cAAAQ;YAAA;cACAf;cACAA;cACAS;gBACAV;cACA;YACA;UACA;QACA;MACA;IACA;IACAiB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAlB;QAAAM;QAAAC;QAAAQ;MAAA;QACAf;QACAD;QACAU;UACAV;QACA;MACA;IACA;IACAoB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEAC;MACA;MACA;MACA3B;IACA;IACA4B;MACA;IACA;IACAC;MACA;MACA;MACA7B;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACA+B;MACA;MACA;MACA9B;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClkBA;AAAA;AAAA;AAAA;AAA2rC,CAAgB,2mCAAG,EAAC,C;;;;;;;;;;;ACA/sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/yuyueorderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/yuyueorderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./yuyueorderdetail.vue?vue&type=template&id=5b76cb41&\"\nvar renderjs\nimport script from \"./yuyueorderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./yuyueorderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./yuyueorderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/yuyueorderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yuyueorderdetail.vue?vue&type=template&id=5b76cb41&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.detail.formdata.length : null\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && _vm.detail.scoredk_money > 0 ? _vm.t(\"积分\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yuyueorderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yuyueorderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop flex\" :style=\"'background:url('+pre_url+'/static/img/orderbg.png);background-size:100%'\">\n\t\t\t<view class=\"f1 \" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\t\t\t\t\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1 && detail.refund_status==0\">\n\t\t\t\t<block v-if=\"!detail.worker_id\">\n\t\t\t\t\t<view class=\"t2\">等待派单</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<view class=\"t2\">订单已接单</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">订单服务中</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\n\t\t\t\t<view class=\"t1\">订单已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t\t<view class=\"orderx\"><image :src=\"pre_url+'/static/img/orderx.png'\"></view>\n\t\t\t\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo orderinfotop\">\n\t\t\t<view class=\"title\">订单信息</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">预约时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.yy_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in detail.formdata\" :key=\"index\">\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\n\t\t\t\t<view class=\"t2\" v-else-if=\"item[2]=='upload_video'\"><video :src=\"item[1]\" style=\"width: 100%;\"/></video></view>\r\n\t\t\t\t<view class=\"t2\" v-else-if=\"item[2]=='upload_pics'\">\r\n\t\t\t\t\t<block v-for=\"vv in item[1]\" :key='kk'>\r\n\t\t\t\t\t\t<image :src=\"vv\" style=\"width:200rpx;height:auto;margin-right: 10rpx;\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"vv\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- <view class=\"btitle flex-y-center\" v-if=\"detail.bid>0\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + detail.bid\">\n\t\t\t<image :src=\"detail.binfo.logo\" style=\"width:36rpx;height:36rpx;\"></image>\n\t\t\t<view class=\"flex1\" decode=\"true\" space=\"true\" style=\"padding-left:16rpx\">{{detail.binfo.name}}</view>\n\t\t</view> -->\n\t\t<view class=\"product\">\n\t\t\t<view class=\"title\">服务信息</view>\n\t\t\t<view class=\"content\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'product?id=' + prolist.proid\">\n\t\t\t\t\t<image :src=\"prolist.propic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{prolist.proname}}</text>\n\t\t\t\t\t<text class=\"t2\">{{prolist.ggname}}</text>\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{prolist.product_price}}</text><text class=\"\" style=\"color: #939393;\">×{{prolist.num}}</text></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\n\t\t\t<view class=\"item flex-col\">\n\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\n\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{detail.freight_content}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"orderinfo\">\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">应付金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.manjian_money > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\n\t\t\t\t<text class=\"t1\">服务费</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\n\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\n\t\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\t\t\t<block v-if=\"detail.showfeedetail && (detail.status==1 || detail.status==2 || detail.status==3)\">\n\t\t\t\t<view class=\"item\" v-if=\"detail.worker_id\">\n\t\t\t\t\t<text class=\"t1\">服务提成</text>\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.ticheng}}</text>\n\t\t\t\t</view>\n\t\t\t\t<block v-if=\"detail.bid>0\">\n\t\t\t\t\t<view class=\"item\" v-if=\"detail.plateform_fee>0\">\n\t\t\t\t\t\t<text class=\"t1\">平台抽成</text>\n\t\t\t\t\t\t<text class=\"t2 red\">¥{{detail.plateform_fee}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\"v-if=\"detail.js_totalprice>0 && detail.totalprice!=detail.js_totalprice\">\n\t\t\t\t\t\t<text class=\"t1\">预估结算</text>\n\t\t\t\t\t\t<text class=\"t2 red\">¥{{detail.js_totalprice}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</block>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">服务中</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已完成</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\n\t\t\t\t<text class=\"\" v-if=\"detail.refundCount\" style=\"margin-left: 8rpx;\">有退款({{detail.refundCount}})</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refundingMoneyTotal>0\">\n\t\t\t\t<text class=\"t1\">退款中</text>\n\t\t\t\t<text class=\"t2 red\" @tap=\"goto\" :data-url=\"'refundlist?orderid='+ detail.id\">¥{{detail.refundingMoneyTotal}}</text>\n\t\t\t\t<text class=\"t3 iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refundedMoneyTotal>0\">\n\t\t\t\t<text class=\"t1\">已退款</text>\n\t\t\t\t<text class=\"t2 red\" @tap=\"goto\" :data-url=\"'refundlist?orderid='+ detail.id\">¥{{detail.refundedMoneyTotal}}</text>\n\t\t\t\t<text class=\"t3 iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\n\t\t\t\t<text class=\"t1\">尾款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.balance_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\n\t\t\t\t<text class=\"t1\">尾款状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==1\">已支付</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==0\">未支付</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.paytypeid\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\n\t\t\t\t<text class=\"t1\">派单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">完成时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.addmoney>0\">\n\t\t\t\t<text class=\"t1\">补差价</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.addmoney}}</text>\n\t\t\t</view>\n      \n      <view class=\"item\">\n      \t<text class=\"t1\">服务方式</text>\n      \t<text class=\"t2\">\n          <block v-if=\"detail.fwtype==2\">\n             {{text['上门服务']}}\n          </block>\n          <block v-if=\"detail.fwtype==3\">\n            到商家服务\n          </block>\n          <block v-if=\"detail.fwtype==1\">\n             {{text['到店服务']}}\n          </block>\n        </text>\n      </view>\n      <block v-if=\"detail.fwbid && detail.fwbinfo\">\n        <view class=\"item\">\n        \t<text class=\"t1\">商家名称</text>\n        \t<text class=\"t2\">{{detail.fwbinfo.name}}</text>\n        </view>\n        <view class=\"item\" v-if=\"detail.fwbinfo.address\">\n        \t<view class=\"t1\">商家地址</view>\n        \t<view v-if=\"!detail.fwbinfo.latitude || !detail.fwbinfo.longitude\" class=\"t2\">\n            {{detail.fwbinfo.address}}\n          </view>\n          <view v-else @tap=\"openLocation\" :data-latitude=\"detail.fwbinfo.latitude\" :data-longitude=\"detail.fwbinfo.longitude\" class=\"t2\">\n            {{detail.fwbinfo.address}}\n          </view>\n        </view>\n      </block>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"title\">顾客信息</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">姓名</text>\n\t\t\t\t<text class=\"t2\">{{detail.linkman}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">手机号</text>\n\t\t\t\t<text class=\"t2\" @tap=\"goto\" :data-url=\"'tel:'+detail.tel\">{{detail.tel}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.fwtype==2\">\n\t\t\t\t<text class=\"t1\">上门地址</text>\n\t\t\t\t<text class=\"t2\" @tap=\"copy\" :data-text=\"detail.area2+detail.address\">{{detail.area2}}{{detail.address}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t\t\n\t\t\n\t\t<view style=\"width:100%;height:120rpx\"></view>\n\n\t\t<view class=\"bottom\">\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundnopass\" :data-id=\"detail.id\">退款驳回</view>\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\">退款通过</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"closeOrder\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\t<view v-if=\"detail.status==0 && detail.bid==0\" class=\"btn2\" @tap=\"ispay\" :data-id=\"detail.id\">改为已支付</view>\n\t\t\t<view v-if=\"detail.status==1 && detail.can_paidan && !detail.worker_id && showlist\" class=\"btn2\" @tap=\"goto\" :data-url=\"'/admin/yuyue/selectworker?id='+detail.id\" >派单</view>\n\t\t\t<view v-if=\"detail.status==1 && detail.worker_id && showlist\" class=\"btn2\" @tap=\"goto\" :data-url=\"'/admin/yuyue/selectworker?id='+detail.id+'&type=update'\" >改派</view>\n\t\t\t<view v-else-if=\"detail.status==1 && !detail.worker_id && !showlist\" class=\"btn2\" @tap=\"peisong\" :data-id=\"detail.id\">派单</view>\n\n\t\t\t<block v-if=\"(detail.status==2 || detail.status==3 || detail.worker_id>0) &&   detail.status!=4\">\n\t\t\t\t<view class=\"btn2\" @tap=\"logistics\" :data-express_com=\"detail.express_com\" :data-express_no=\"detail.worker_orderid\"  >查进度</view>\n\t\t\t</block>\n\t\t\t<view class=\"btn2\" @tap=\"setremark\" :data-id=\"detail.id\">设置备注</view>\n\t\t\t<block v-if=\"detail.status==3 || detail.status==4\">\n\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"detail.id\">删除订单</view>\n\t\t\t</block>\n\t\t\n\t\t</view>\n\t\t\n\t\t\n\t\t<uni-popup id=\"dialogSetremark\" ref=\"dialogSetremark\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"设置备注\" :value=\"detail.remark\" placeholder=\"请输入备注\" @confirm=\"setremarkconfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\n\t\t<uni-popup id=\"dialogPeisong\" ref=\"dialogPeisong\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请选择配送员</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<picker @change=\"peisongChange\" :value=\"index2\" :range=\"peisonguser2\" style=\"font-size:24rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t<view class=\"picker\">{{peisonguser2[index2]}}</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogPeisongClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmPeisong\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      prodata: '',\n      djs: '',\n      iscommentdp: \"\",\n      detail: \"\",\n\t\t\tpayorder:{},\n      prolist: \"\",\n      storeinfo: \"\",\n      lefttime: \"\",\n      codtxt: \"\",\n\t\t\tpay_transfer_info:{},\n\t\t\tinvoice:0,\n\t\t\tselectExpressShow:false,\n\t\t\texpress_content:'',\n\t\t\tpeisonguser:[],\n\t\t\tpeisonguser2:[],\n\t\t\tindex2:0,\n\t\t\tshowlist:false,\r\n\t\t\ttext:{}\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminOrder/yuyueorderdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.iscommentdp = res.iscommentdp,\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.prolist = res.prolist;\n\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\tthat.lefttime = res.lefttime;\n\t\t\t\tthat.codtxt = res.codtxt;\n\t\t\t\tthat.pay_transfer_info =  res.pay_transfer_info;\n\t\t\t\tthat.payorder = res.payorder;\n\t\t\t\tthat.invoice = res.invoice;\n\t\t\t\tthat.showlist = res.showlist\r\n\t\t\t\tthat.text = res.text\n\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n\tsetremark:function(){\n\t\tthis.$refs.dialogSetremark.open();\n\t},\n\tsetremarkconfirm: function (done, remark) {\n\t\tthis.$refs.dialogSetremark.close();\n\t\tvar that = this\n\t\tapp.post('ApiAdminOrder/setremark', { type:'yuyue',orderid: that.detail.id,content:remark }, function (res) {\n\t\t\tapp.success(res.msg);\n\t\t\tsetTimeout(function () {\n\t\t\t\tthat.getdata();\n\t\t\t}, 1000)\n\t\t})\n\t},\n    todel: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiYuyue/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n      });\n    },\n\t  refundnopass: function (e) {\n\t\tvar that = this;\n\t\tvar orderid = e.currentTarget.dataset.id\n\t\tapp.confirm('确定要驳回退款申请吗?', function () {\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiAdminOrder/refundnopass', { type:'yuyue',orderid: orderid }, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success(data.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t});\n\t  },\n\t  refundpass: function (e) {\n\t\tvar that = this;\n\t\tvar orderid = e.currentTarget.dataset.id\n\t\tapp.confirm('确定要审核通过并退款吗?', function () {\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiAdminOrder/refundpass', { type:'yuyue',orderid: orderid }, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success(data.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t});\n\t  },\n    orderCollect: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定已完成服务吗?', function () {\n\t\t\t\tapp.showLoading('确认中');\n        app.post('ApiYuyue/orderCollect', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n\tpeisong:function(){\n\t\tvar that = this;\n\t\tthat.loading = true;\n\t\tapp.post('ApiAdminOrder/getyuyuepsuser',{type:'yuyue_order',orderid:that.detail.id},function(res){\n\t\t\tthat.loading = false;\n\t\t\tvar peisonguser = res.peisonguser\n\t\t\tvar paidantype = res.paidantype\n\t\t\tvar psfee = res.psfee\n\t\t\tvar ticheng = res.ticheng\n\t\n\t\t\tvar peisonguser2 = [];\n\t\t\tfor(var i in peisonguser){\n\t\t\t\tpeisonguser2.push(peisonguser[i].title);\n\t\t\t}\n\t\t\tthat.peisonguser = res.peisonguser;\n\t\t\tthat.peisonguser2 = peisonguser2;\n\t\t\tif(paidantype==1){\n\t\t\t\tthat.$refs.dialogPeisong.open();\n\t\t\t}else{\n\t\t\t\tif(that.detail.bid == 0){\n\t\t\t\t\tvar tips='选择服务人员抢单，订单将发布到抢单大厅由服务人员单，服务人员提成￥'+ticheng+'，确定要服务人员抢单吗？';\n\t\t\t\t}else{\n\t\t\t\t\tvar tips='选择服务人员抢单，订单将发布到抢单大厅由服务人员单，需扣除服务费￥'+psfee+'，确定要服务人员抢单吗？';\n\t\t\t\t}\n\t\t\t\tif(paidantype == 2){\n\t\t\t\t\tvar psid = '-1';\n\t\t\t\t}else{\n\t\t\t\t\tvar psid = '0';\n\t\t\t\t}\n\t\t\t\tapp.confirm(tips,function(){\n\t\t\t\t\tapp.showLoading('提交中...');\n\t\t\t\t\tapp.post('ApiAdminOrder/yuyuepeisong', { type:'yuyue_order',orderid: that.detail.id,worker_id:psid}, function (res) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t})\n\t},\n\tdialogPeisongClose:function(){\n\t\tthis.$refs.dialogPeisong.close();\n\t},\n\tpeisongChange:function(e){\n\t\tthis.index2 = e.detail.value;\n\t},\n\tconfirmPeisong:function(){\n\t\tvar that = this\n\t\tvar psid = this.peisonguser[this.index2].id\n\t\tapp.post('ApiAdminOrder/yuyuepeisong', { type:'yuyue_order',orderid: that.detail.id,worker_id:psid}, function (res) {\n\t\t\tapp.success(res.msg);\n\t\t\tthat.$refs.dialogPeisong.close();\n\t\t\tsetTimeout(function () {\n\t\t\t\tthat.getdata();\n\t\t\t}, 1000)\n\t\t})\n\t},\n\t\tshowhxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.open();\n\t\t},\n\t\tcloseHxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.close();\n\t\t},\n\t\topenLocation:function(e){\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\n\t\t\tvar address = e.currentTarget.dataset.address;\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t scale: 13\n\t\t\t})\n\t\t},\n\t\t\n\t\tlogistics:function(e){\n\t\t\tvar express_com = e.currentTarget.dataset.express_com\n\t\t\tvar express_no = e.currentTarget.dataset.express_no\n\t\t\tapp.goto('/activity/yuyue/logistics?express_no=' + express_no);\n\t\t},\n\t\thideSelectExpressDialog:function(){\n\t\t\tthis.$refs.dialogSelectExpress.close();\n\t\t},\n\t\tispay:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要改为已支付吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/ispay', { type:'yuyue',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tcloseOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/closeOrder', { type:'yuyue',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function (){\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n  }\n};\n</script>\n<style>\n\t.text-min { font-size: 24rpx; color: #999;}\n.ordertop{width:100%;height:452rpx;padding:50rpx 0 0 70rpx; justify-content: space-between;}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:40rpx;height:60rpx;line-height:60rpx;}\n.ordertop .f1 .t2{font-size:26rpx; margin-top: 20rpx;}\n\n.container .orderinfotop{ position: relative; margin-top: -200rpx;}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .title,.product .title{ font-weight: bold; font-size: 30rpx; line-height: 60rpx; margin-bottom: 15rpx;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .t3{ margin-top: 3rpx;}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;z-index: 1;}\n\n.btn1{margin-left:20rpx;min-width:160rpx;padding: 0 20rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.hxqrbox .img{width:400rpx;height:400rpx}\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n\n.orderx image{ width:124rpx ; height: 124rpx; margin-right: 60rpx;}\n\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yuyueorderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yuyueorderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839440103\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}