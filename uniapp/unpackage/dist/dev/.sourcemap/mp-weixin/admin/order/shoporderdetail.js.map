{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporderdetail.vue?3503", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporderdetail.vue?4cef", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporderdetail.vue?cf86", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporderdetail.vue?d477", "uni-app:///admin/order/shoporderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporderdetail.vue?f029", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/shoporderdetail.vue?858e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "expressdata", "express_index", "express_no", "prodata", "djs", "detail", "prolist", "shopset", "storeinfo", "lefttime", "codtxt", "p<PERSON><PERSON><PERSON><PERSON>", "peisonguser2", "index2", "express_pic", "express_fhname", "express_fhaddress", "express_shname", "express_shaddress", "express_remark", "returnProlist", "refundTotalprice", "refundNum", "refundReason", "myt_weight", "myt_remark", "mytindex", "myt_shop_id", "selecthxnumDialogShow", "hxogid", "hxnum", "hxnumlist", "hexiao_code", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "<PERSON><PERSON><PERSON><PERSON>", "app", "j<PERSON><PERSON>", "needResult", "scanType", "success", "serialNumber", "that", "fail", "uni", "getdata", "id", "interval", "getdjs", "setremark", "setremarkconfirm", "type", "orderid", "content", "setTimeout", "changePrice", "changePriceConfirm", "val", "fahuo", "dialogExpressClose", "dialogExpress10Close", "expresschange", "setexpressno", "<PERSON><PERSON><PERSON><PERSON>", "express_com", "setexpress_pic", "setexpress_fhname", "setexpress_fhaddress", "setexpress_shname", "setexpress_shaddress", "setexpress_remark", "confirmfahuo10", "pic", "fhname", "fhaddress", "shname", "shaddress", "remark", "ispay", "<PERSON><PERSON><PERSON>", "showhxqr2", "console", "hxnumRadioChange", "op", "co", "hideSelecthxnumDialog", "delOrder", "closeOrder", "refundnopass", "refundpass", "retundInput", "max", "ogid", "total", "refundMoneyReason", "refundMoney", "refundinit", "dialogExpress12Close", "gotoRefundMoney", "reason", "money", "peisong", "psid", "dialogPeisongClose", "peisong<PERSON>hange", "confirmPeisong", "peisongWx", "uploadimg", "pics", "removeimg", "peisongMyt", "goMyt", "confirmfahuo11", "dialogExpress11Close", "mytWeight", "mytRemark", "mytshopChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAA60B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACohBj2B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACA;QACAC;QAAA;MACA;QACA;QACAC;UAAA;UACAA;YACAC;YAAA;YACAC;YAAA;YACAC;cACA;cACA;cACAC;cACAC;YACA;YACAC;cACAP;YACA;UACA;QACA;MACA;QACAQ;UACAJ;YACAE;UACA;UACAC;YACAP;UACA;QACA;MACA;IACA;IACAS;MACA;MACAH;MACAN;QAAAU;MAAA;QACAJ;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAK;YACAL;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAM;MACA;MACA;MAEA;QACAN;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAO;MACA;IACA;IACAC;MACA;MACA;MACAd;QAAAe;QAAAC;QAAAC;MAAA;QACAjB;QACAkB;UACAZ;QACA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;MACA;MACApB;QAAAe;QAAAC;QAAAK;MAAA;QACArB;QACAkB;UACAZ;QACA;MACA;IACA;IACAgB;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA3B;QAAAe;QAAAC;QAAArD;QAAAiE;MAAA;QACA;UACA5B;UACA;QACA;QACAA;QACAkB;UACAZ;QACA;MACA;IACA;IACAuB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAnC;QAAAe;QAAAC;QAAAoB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;QACAzC;QACAkB;UACAZ;QACA;MACA;IACA;IACAoC;MACA;MACA;MACA1C;QACAA;QACAA;UAAAe;UAAAC;QAAA;UACAhB;UACAA;UACAkB;YACAZ;UACA;QACA;MACA;IACA;IACAqC;MACA;MACA;MACA3C;QACAA;QACAA;UAAAe;UAAAC;QAAA;UACAhB;UACAA;UACAkB;YACAZ;UACA;QACA;MACA;IACA;IACAsC;MACA;MACA;MACA;MACA;QACA5C;QAAA;MACA;MACAM;MACA;MACA;QACAd;MACA;MACAqD;MAEAvC;MACAA;MACAA;IAEA;IACAwC;MACA;MACA;MACA;MACA9C;QACAM;QACAN;UAAA+C;UAAAhC;UAAAiC;UAAAzD;QAAA;UACAS;UACA;YACAA;YAAA;UACA;UACA;YACAA;YACAM;UACA;YACAN;cACAA;YACA;UACA;QAEA;MACA;IACA;IACAiD;MACA;IACA;IACAC;MACA;MACA;MACAlD;MACAA;QACAA;UAAAe;UAAAC;QAAA;UACAhB;UACAA;UACAkB;YACAlB;UACA;QACA;MACA;IACA;IACAmD;MACA;MACA;MACAnD;QACAA;QACAA;UAAAe;UAAAC;QAAA;UACAhB;UACAA;UACAkB;YACAZ;UACA;QACA;MACA;IACA;IACA8C;MACA;MACA;MACApD;QACAA;QACAA;UAAAe;UAAAC;QAAA;UACAhB;UACAA;UACAkB;YACAZ;UACA;QACA;MACA;IACA;IACA+C;MACA;MACA;MACArD;QACAA;QACAA;UAAAe;UAAAC;QAAA;UACAhB;UACAA;UACAkB;YACAZ;UACA;QACA;MACA;IACA;IACAgD;MACA;MACA;MACA;QAAAC;QAAAC;MACA;MACA;MACA;MAEA;QACA;MACA;MAEA;QACA;UACAzE;QACA;QACA;UACA0E;QACA;UACAA;QACA;MACA;MACAA;MACAA;MACAnD;IACA;IACAoD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAtD;MACAN;QAAAgB;MAAA;QACAV;QACA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;YACA;YACA;UACA;QACA;QACAA;MACA;IACA;IACAuD;MACA;MACA;MACA;IACA;IACAC;MACA;MACAjB;MACA7C;QACAM;QACAN;QACAA;UACAgB;UACAjC;UACAgF;UACAC;QACA;UACA;YACAhE;YACA;UACA;UACAA;UACAA;UACAkB;YACAZ;UACA;QACA;MACA;IACA;IACA2D;MACA;MACA3D;MACAN;QAAAe;QAAAC;MAAA;QACAV;QACA;QACA;QACA;QACA;QAEA;QACA;UACAjC;QACA;QACAiC;QACAA;QACA;UACAA;QACA;UACA;YACA;UACA;YACA;UACA;UACA;YACA;UACA;YACA;UACA;UACAN;YACAA;cAAAe;cAAAC;cAAAkD;YAAA;cACA;gBACAlE;gBACAkB;kBACAZ;gBACA;cACA;gBACAN;cACA;YACA;UACA;QACA;MACA;IACA;IACAmE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACArE;QAAAe;QAAAC;QAAAkD;MAAA;QACAlE;QACAM;QACAY;UACAZ;QACA;MACA;IACA;IACAgE;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACAtE;QACAM;QACAN;UAAAe;UAAAC;QAAA;UACAV;UACAN;UACAkB;YACAZ;UACA;QACA;MACA;IACA;IACAiE;MACA;MACA;MACA;MACA;MACA;MACA;MACAvE;QACA;UACAwE;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAnE;MACA;IACA;IACAoE;MACA;MACA;MACA;QACA;MACA;QACApE;MACA;IACA;IACAqE;MACA;MACA;MACA;MACA3E;QACAM;QACAA;QACA;UACAS;UACAC;UACA/B;UACAC;UACAE;QACA;QACAY;UACAM;UACA;YACAN;YACAkB;cACAZ;YACA;UACA;YACAN;UACA;QAEA;MACA;IACA;IACA4E;MACA;MACAtE;IACA;IACAuE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA1E;MACA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9lCA;AAAA;AAAA;AAAA;AAA0rC,CAAgB,0mCAAG,EAAC,C;;;;;;;;;;;ACA9sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/shoporderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/shoporderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shoporderdetail.vue?vue&type=template&id=56d4e8ca&\"\nvar renderjs\nimport script from \"./shoporderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./shoporderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shoporderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/shoporderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=template&id=56d4e8ca&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.detail.mid > 0 ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && _vm.detail.scoredk_money > 0 ? _vm.t(\"积分\") : null\n  var m4 = _vm.isload && _vm.detail.dec_money > 0 ? _vm.t(\"余额\") : null\n  var m5 =\n    _vm.isload && _vm.detail.silvermoneydec && _vm.detail.silvermoneydec > 0\n      ? _vm.t(\"银值\")\n      : null\n  var m6 =\n    _vm.isload && _vm.detail.goldmoneydec && _vm.detail.goldmoneydec > 0\n      ? _vm.t(\"金值\")\n      : null\n  var m7 =\n    _vm.isload && _vm.detail.shopscoredk_money > 0 ? _vm.t(\"产品积分\") : null\n  var m8 =\n    _vm.isload && _vm.detail.combine_money && _vm.detail.combine_money > 0\n      ? _vm.t(\"余额\")\n      : null\n  var g0 = _vm.isload ? _vm.detail.formdata.length : null\n  var l0 =\n    _vm.isload && _vm.selecthxnumDialogShow\n      ? _vm.__map(_vm.hxnumlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m9 = _vm.hxnum == item ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m9: m9,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url('+(shopset.order_detail_toppic?shopset.order_detail_toppic: pre_url + '/static/img/ordertop.png')+');background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\n\t\t\t\t<view class=\"t1\">{{detail.paytypeid==4 ? '已选择'+detail.paytype : '已成功付款'}}</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=1\">请尽快发货</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type==1\">待提货</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">订单已发货</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=3\">发货信息：{{detail.express_com}} {{detail.express_no}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\n\t\t\t\t<view class=\"t1\">订单已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"address\">\n\t\t\t<view class=\"img\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"info\">\n\t\t\t\t<view class=\"t1\"><text user-select=\"true\" selectable=\"true\">{{detail.linkman}}</text> <text v-if=\"detail.tel\" @tap=\"goto\" :data-url=\"'tel:'+detail.tel\" style=\"margin-left: 20rpx;\" user-select=\"true\" selectable=\"true\">{{detail.tel}}</text></view>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">地址：{{detail.area}}{{detail.address}}</text>\n        <text class=\"t2\" v-if=\"detail.product_thali\">学生姓名：{{detail.product_thali_student_name}}</text>\n        <text class=\"t2\" v-if=\"detail.product_thali\">学校信息：{{detail.product_thali_school}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openLocation\" :data-address=\"storeinfo.address\" :data-latitude=\"storeinfo.latitude\" :data-longitude=\"storeinfo.longitude\" user-select=\"true\" selectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text>\n        <view class=\"t2\" v-if=\"detail.worknum\">工号：{{detail.worknum}}</view>\n      </view>\n\t\t</view>\n\t\t<view class=\"product\">\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"box\">\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\">\n\t\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\n\t\t\t\t\t\t<view class=\"t3\" v-if=\"item.product_type && item.product_type==2\">\n\t\t\t\t\t\t\t<text class=\"x1 flex1\">{{item.real_sell_price}}元/斤</text>\n\t\t\t\t\t\t\t<text class=\"x2\">×{{item.real_total_weight}}斤</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"t3\" v-else><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\">×{{item.num}}</text></view>\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"shopset.show_shd_remark == 1 && item.shd_remark\" style=\"height: auto;\">备注：{{item.shd_remark}}</text>\n\t\t\t\t\t\t<block v-if=\"(detail.status==1 || detail.status==2) && detail.is_quanyi==1 && item.hexiao_code\">\n\t\t\t\t\t\t\t<view class=\"btn3\" @tap.stop=\"showhxqr2\" :data-id=\"item.id\" :data-num=\"item.hexiao_num_total\" :data-hxnum=\"item.hexiao_num_used\" :data-hexiao_code=\"item.hexiao_code\" style=\"position:absolute;top:46rpx;right:0rpx;\">权益核销</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- glassinfo -->\n\t\t\t\t<view class=\"glassitem\" v-if=\"item.glassrecord\">\n\t\t\t\t\t<view class=\"gcontent\">\n\t\t\t\t\t\t<view class=\"glassheader\">\n\t\t\t\t\t\t\t{{item.glassrecord.name}}\n\t\t\t\t\t\t\t{{item.glassrecord.nickname?item.glassrecord.nickname:''}}\n\t\t\t\t\t\t\t{{item.glassrecord.check_time?item.glassrecord.check_time:''}}\n\t\t\t\t\t\t\t{{item.glassrecord.typetxt}}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<text class=\"pdl10\" v-if=\"item.glassrecord.double_ipd==0\">{{item.glassrecord.ipd?'PD'+item.glassrecord.ipd:''}}</text>\n\t\t\t\t\t\t\t<text class=\"pdl10\" v-else>PD R{{item.glassrecord.ipd_right}} L{{item.glassrecord.ipd_left}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"glassrow bt\">\n\t\t\t\t\t\t\t<view class=\"grow\">\n\t\t\t\t\t\t\tR {{item.glassrecord.degress_right}}/{{item.glassrecord.ats_right?item.glassrecord.ats_right:'0.00'}}*{{item.glassrecord.ats_zright?item.glassrecord.ats_zright:'0'}} <text class=\"pdl10\" v-if=\"item.glassrecord.type==3\">ADD+{{item.glassrecord.add_right?item.glassrecord.add_right:0}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"grow\">\n\t\t\t\t\t\t\tL {{item.glassrecord.degress_left}}/{{item.glassrecord.ats_left?item.glassrecord.ats_left:'0.00'}}*{{item.glassrecord.ats_zleft?item.glassrecord.ats_zleft:'0'}} <text class=\"pdl10\" v-if=\"item.glassrecord.type==3\">ADD+{{item.glassrecord.add_left?item.glassrecord.add_left:0}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"glassrow\" v-if=\"item.glassrecord.remark\">{{item.glassrecord.remark}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t</view>\n\t\t\t\t<!-- glassinfo -->\n\t\t\t</view>\n      <view v-if=\"detail.crk_givenum && detail.crk_givenum>0\" style=\"color:#f60;line-height:70rpx\">+随机赠送{{detail.crk_givenum}}件</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\n\t\t\t<view class=\"item flex-col\">\n\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\n\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{detail.freight_content}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"detail.mid>0\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"detail.remark\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.remark}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\n\t\t\t\t<text class=\"t1\">发货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">收货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n      <view class=\"item\" v-if=\"detail.issource && detail.source && detail.source == 'supply_zhenxin'\">\n      \t<text class=\"t1\">商品来源</text>\n      \t<text class=\"t2\">甄新汇选</text>\n      </view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">商品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.manjian_money > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.invoice_money > 0\">\n\t\t\t\t<text class=\"t1\">发票费用</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.invoice_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">配送方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_text}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\n\t\t\t\t<text class=\"t1\">服务费</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\n\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.dec_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('余额')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.dec_money}}</text>\n\t\t\t</view>\n      <view class=\"item\" v-if=\"detail.silvermoneydec && detail.silvermoneydec > 0\">\n      \t<text class=\"t1\">{{t('银值')}}抵扣</text>\n      \t<text class=\"t2 red\">-¥{{detail.silvermoneydec}}</text>\n      </view>\n      <view class=\"item\" v-if=\"detail.goldmoneydec && detail.goldmoneydec > 0\">\n      \t<text class=\"t1\">{{t('金值')}}抵扣</text>\n      \t<text class=\"t2 red\">-¥{{detail.goldmoneydec}}</text>\n      </view>\n\t\t\t<view class=\"item\" v-if=\"detail.discount_rand_money > 0\">\n\t\t\t\t<text class=\"t1\">随机立减</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.discount_rand_money}}</text>\n\t\t\t</view>\n      <view class=\"item\" v-if=\"detail.dedamount_dkmoney && detail.dedamount_dkmoney > 0\">\n      \t<text class=\"t1\">抵扣金抵扣</text>\n      \t<text class=\"t2 red\">-¥{{detail.dedamount_dkmoney}}</text>\n      </view>\n      <view class=\"item\" v-if=\"detail.shopscoredk_money > 0\">\n      \t<text class=\"t1\">{{t('产品积分')}}抵扣</text>\n      \t<text class=\"t2 red\">-¥{{detail.shopscoredk_money}}</text>\n      </view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n      <view class=\"item\" v-if=\"detail.combine_money && detail.combine_money > 0\">\n        <text class=\"t1\">{{t('余额')}}已付</text>\n        <text class=\"t2 red\">-¥{{detail.combine_money}}</text>\n      </view>\n      <view class=\"item\" v-if=\"detail.paytypeid == 2 && detail.status<=3 && detail.combine_wxpay && detail.combine_wxpay > 0\">\n        <text class=\"t1\">微信已付</text>\n        <text class=\"t2 red\">-¥{{detail.combine_wxpay}}</text>\n      </view>\n      <view class=\"item\" v-if=\"(detail.paytypeid == 3 || (detail.paytypeid>=302 && detail.paytypeid<=330)) && detail.combine_alipay && detail.combine_alipay > 0\">\n        <text class=\"t1\">支付宝已付</text>\n        <text class=\"t2 red\">-¥{{detail.combine_alipay}}</text>\n      </view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">已发货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已收货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款原因</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason||'暂无'}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\n\t\t\t\t<text class=\"t1\">审核备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item flex-col\" v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1 && detail.is_quanyi==0 && !detail.hidefahuo\">\n\t\t\t\t<text class=\"t1\">核销码</text>\n\t\t\t\t<view class=\"flex-x-center\">\n\t\t\t\t\t<image :src=\"detail.hexiao_qr\" style=\"width:400rpx;height:400rpx\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\n\t\t\t\t<text class=\"t1\">尾款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.balance_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\n\t\t\t\t<text class=\"t1\">尾款状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==1\">已支付</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==0\">未支付</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"detail.checkmemid\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">所选会员</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.checkmember.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.checkmember.nickname}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"detail.isdygroupbuy==1\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">抖音团购券信息</text>\n\t\t    <text class=\"t2\">{{detail.dyorderids}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\n\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:calc(160rpx + env(safe-area-inset-bottom));\"></view>\n\n\t\t<view class=\"bottom notabbarbot\">\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundnopass\" :data-id=\"detail.id\">退款驳回</view>\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\">退款通过</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"closeOrder\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\t<view v-if=\"detail.status==0 && detail.bid==0 && !detail.isdygroupbuy\" class=\"btn2\" @tap=\"ispay\" :data-id=\"detail.id\">改为已支付</view>\n\t\t\t<block v-if=\"detail.status==1 && !detail.hidefahuo && shopset.send_show\">\n\t\t\t\t<view v-if=\"detail.product_type==2\" class=\"btn2\" @tap=\"goto\" :data-url=\"'weightOrderFahuo?id='+detail.id\">发货</view>\n\t\t\t\t<view v-if=\"detail.product_type!=2 && detail.can_fahuo==1\" class=\"btn2\" @tap=\"fahuo\" :data-id=\"detail.id\">发货</view>\n\t\t\t</block>\n\t\t\t\n\t\t\t<view v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1 && detail.is_quanyi==0 && shopset.hexiao_show\" class=\"btn2\" @tap=\"hexiao\" :data-id=\"detail.id\">核销</view>\n\t\t\t<block v-if=\"detail.status==1 && detail.canpeisong && shopset.send_show\">\n\t\t\t\t<view class=\"btn2\" v-if=\"detail.express_wx_status\" @tap=\"peisongWx\"  :data-id=\"detail.id\">即时配送</view>\n\t\t\t\t<view class=\"btn2\" v-else-if=\"detail.myt_status\"   @tap=\"peisongMyt\" :data-id=\"detail.id\">麦芽田配送</view>\n\t\t\t\t<view class=\"btn2\" v-else @tap=\"peisong\" :data-id=\"detail.id\">配送</view>\n\t\t\t</block>\n\t\t\t<block v-if=\"(detail.status==2 || detail.status==3) && detail.express_com\">\n\t\t\t\t<view v-if=\"detail.express_type =='express_wx'\" class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no+'&type=express_wx'\">订单跟踪</view>\n\t\t\t\t<view v-else class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no\">\n\t\t\t\t\t\t<text v-if=\"detail.psid == -2\">订单跟踪</text>\n\t\t\t\t\t\t<text v-else>查物流</text>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t<view v-if=\"detail.status==1\" class=\"btn2\" @tap=\"refundinit\" :data-id=\"detail.id\">退款</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"changePrice\" :data-id=\"detail.id\">改价</view>\n\t\t\t<view v-if=\"detail.status==2 && detail.freight_type==10\" class=\"btn2\" @tap=\"fahuo\" :data-id=\"detail.id\">修改物流</view>\n\t\t\t<view v-if=\"detail.status==4\" class=\"btn2\" @tap=\"delOrder\" :data-id=\"detail.id\">删除</view>\n\t\t\t<view class=\"btn2\" @tap=\"setremark\" :data-id=\"detail.id\">设置备注</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetremark\" ref=\"dialogSetremark\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"设置备注\" :value=\"detail.remark\" placeholder=\"请输入备注\" @confirm=\"setremarkconfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\t\n\t\t<uni-popup id=\"dialogChangePrice\" ref=\"dialogChangePrice\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"修改价格\" :value=\"detail.totalprice\" placeholder=\"请输入价格\" @confirm=\"changePriceConfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\t\n\t\t<uni-popup id=\"dialogExpress\" ref=\"dialogExpress\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">发货</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx\">\n\t\t\t\t\t\t\t<text style=\"font-size:28rpx;color:#000\">快递公司：</text>\n\t\t\t\t\t\t\t<picker @change=\"expresschange\" :value=\"express_index\" :range=\"expressdata\" style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">{{expressdata[express_index]}}</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view> \n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">快递单号：</view>\n\t\t\t\t\t\t\t<view class=\"danhao-input-view\">\n\t\t\t\t\t\t\t\t<input type=\"text\" v-model=\"express_no\" placeholder=\"请输入快递单号\" @input=\"setexpressno\" style=\"border:none;outline:none;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/saoyisao.png`\" @click=\"saoyisao\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpressClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<uni-popup id=\"dialogPeisong\" ref=\"dialogPeisong\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请选择配送员</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<picker @change=\"peisongChange\" :value=\"index2\" :range=\"peisonguser2\" style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t<view class=\"picker\">{{peisonguser2[index2]}}</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogPeisongClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmPeisong\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<uni-popup id=\"dialogExpress10\" ref=\"dialogExpress10\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">发货信息</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"form-item flex\" style=\"border-bottom:0;\">\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"margin-right:20rpx\">物流单照片</view>\n\t\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t\t<view class=\"layui-imgbox\" v-if=\"express_pic\">\n\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"0\" data-field=\"express_pic\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"express_pic\" @tap=\"previewImage\" :data-url=\"express_pic\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"express_pic\" data-pernum=\"1\" v-else></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"express_pic\" :value=\"express_pic\" maxlength=\"-1\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">发货人：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货人信息\" @input=\"setexpress_fhname\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">发货地址：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货地址\" @input=\"setexpress_fhaddress\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">收货人：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货人信息\" @input=\"setexpress_shname\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">收货地址：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货地址\" @input=\"setexpress_shaddress\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">备注：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入备注\" @input=\"setexpress_remark\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpress10Close\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo10\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n        <uni-popup id=\"dialogExpress11\" ref=\"dialogExpress11\" type=\"dialog\">\n        \t<view class=\"uni-popup-dialog\">\n        \t\t<view class=\"uni-dialog-title\">\n        \t\t\t<text class=\"uni-dialog-title-text\">配送设置</text>\n        \t\t</view>\n        \t\t<view class=\"uni-dialog-content\">\n        \t\t\t<view>\n\t\t\t\t\t\t\t\t<view v-if=\"detail.myt_shop\" class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">门店：</view>\n\t\t\t\t\t\t\t\t\t\t<picker @change=\"mytshopChange\" :value=\"mytindex\" :range=\"detail.myt_shoplist\"  range-key='name' style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;line-height: 52rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"picker\">{{detail.myt_shoplist[mytindex]['name']}}</view>\n\t\t\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t\t</view>\n        \t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n        \t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">重量：</view>\n        \t\t\t\t\t<input type=\"text\" placeholder=\"请输入重量(选填)\" @input=\"mytWeight\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n        \t\t\t\t</view>\n        \t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n        \t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">备注：</view>\n        \t\t\t\t\t<input type=\"text\" placeholder=\"请输入备注(选填)\" @input=\"mytRemark\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n        \t\t\t\t</view>\n        \t\t\t</view>\n        \t\t</view>\n        \t\t<view class=\"uni-dialog-button-group\">\n        \t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpress11Close\">\n        \t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n        \t\t\t</view>\n        \t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo11\">\n        \t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n        \t\t\t</view>\n        \t\t</view>\n        \t</view>\n        </uni-popup>\n\t\t\t\t<uni-popup id=\"dialogExpress12\" ref=\"dialogExpress12\" type=\"dialog\" :mask-click=\"false\">\n\t\t\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t\t\t<text class=\"uni-dialog-title-text\">退款</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<view class=\"product\" style=\"width: 100%;margin:0;padding:0\">\n\t\t\t\t\t\t\t\t\t<scroll-view class=\"popup-content\" scroll-y=\"true\" style=\"max-height: 600rpx;overflow: hidden;\">\n\t\t\t\t\t\t\t\t\t<view v-for=\"(item, idx) in returnProlist\" :key=\"idx\" class=\"box\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" style=\"width: 110rpx;height: 110rpx\"></image>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item.sell_price}}×{{item.num}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"color: #888;font-size: 24rpx;display: flex;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text>退货数量</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"number\" :value=\"item.canRefundNum\" :data-max=\"item.canRefundNum\" :data-ogid=\"item.id\" @input=\"retundInput\" class=\"retundNum\" style=\"border: 1px #eee solid;width: 80rpx;margin-left: 10rpx;text-align: center;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;height: 80rpx;\">\n\t\t\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">退款原因：</view>\n\t\t\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入退款原因\" @input=\"refundMoneyReason\" adjust-position=\"false\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;height: 80rpx\">\n\t\t\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">退款金额：</view>\n\t\t\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入退款金额\" @input=\"refundMoney\" adjust-position=\"false\" :value=\"refundTotalprice\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpress12Close\">\n\t\t\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"gotoRefundMoney()\">\n\t\t\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</uni-popup>\n\t\t\t\t<view v-if=\"selecthxnumDialogShow\" class=\"popup__container\">\n\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideSelecthxnumDialog\"></view>\n\t\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">请选择核销数量</text>\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideSelecthxnumDialog\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in hxnumlist\" :key=\"index\" @tap=\"hxnumRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t\t\t<view class=\"flex1\">{{item}}</view>\n\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"hxnum==item ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n        opt:{},\n        loading:false,\n        isload: false,\n        menuindex:-1,\n\n        pre_url:app.globalData.pre_url,\n        expressdata:[],\n        express_index:0,\n        express_no:'',\n        prodata: '',\n        djs: '',\n        detail: \"\",\n        prolist: \"\",\n        shopset: {},\n        storeinfo: \"\",\n        lefttime: \"\",\n        codtxt: \"\",\n        peisonguser:[],\n        peisonguser2:[],\n        index2:0,\n        express_pic:'',\n        express_fhname:'',\n        express_fhaddress:'',\n        express_shname:'',\n        express_shaddress:'',\n        express_remark:'',\n        \n\t\t\t\treturnProlist:[], //退款商品\n\t\t\t\trefundTotalprice:0, //退款金额\n\t\t\t\trefundNum:[],\n\t\t\t\trefundReason:'', //退款原因\n        myt_weight:'',\n        myt_remark:'',\n        mytindex:0,\n        myt_shop_id:0,\n\t\tselecthxnumDialogShow:false,\n\t\thxogid:'',\n\t\thxnum:'',\n\t\thxnumlist:[],\n\t\thexiao_code:''\n\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tsaoyisao: function (d) {\n\t\t  var that = this;\n\t\t\tif(app.globalData.platform == 'h5'){\n\t\t\t\tapp.alert('请使用微信扫一扫功能扫码');return;\n\t\t\t}else if(app.globalData.platform == 'mp'){\n\t\t\t\tvar jweixin = require('jweixin-module');\n\t\t\t\tjweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用\n\t\t\t\t\tjweixin.scanQRCode({\n\t\t\t\t\t\tneedResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\n\t\t\t\t\t\tscanType: [\"qrCode\",\"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\n\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\tlet serialNumber = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\n\t\t\t\t\t\t\tlet serial = serialNumber.split(\",\");\n\t\t\t\t\t\t\tserialNumber = serial[serial.length-1];\n\t\t\t\t\t\t\tthat.express_no = serialNumber;\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail:function(err){\n\t\t\t\t\t\t\tapp.error(err.errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}else{\n\t\t\t\tuni.scanCode({\n\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\tthat.express_no = res.result;\n\t\t\t\t\t},\n\t\t\t\t\tfail:function(err){\n\t\t\t\t\t\tapp.error(err.errMsg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminOrder/shoporderdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.expressdata = res.expressdata;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.prolist = res.prolist;\n\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\tthat.lefttime = res.lefttime;\n\t\t\t\tthat.codtxt = res.codtxt;\n\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n\t\tsetremark:function(){\n\t\t\tthis.$refs.dialogSetremark.open();\n\t\t},\n\t\tsetremarkconfirm: function (done, remark) {\n\t\t\tthis.$refs.dialogSetremark.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminOrder/setremark', { type:'shop',orderid: that.detail.id,content:remark }, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n    },\n\t\tchangePrice:function(){\n\t\t\tthis.$refs.dialogChangePrice.open();\n\t\t},\n\t\tchangePriceConfirm: function (done, val) {\n\t\t\tthis.$refs.dialogChangePrice.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminOrder/changePrice', { type:'shop',orderid: that.detail.id,val:val }, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n    },\n\t\tfahuo:function(){\n\t\t\tif(this.detail.freight_type==10){\n\t\t\t\tthis.$refs.dialogExpress10.open();\n\t\t\t}else{\n\t\t\t\tthis.$refs.dialogExpress.open();\n\t\t\t}\n\t\t},\n\t\tdialogExpressClose:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t},\n\t\tdialogExpress10Close:function(){\n\t\t\tthis.$refs.dialogExpress10.close();\n\t\t},\n\t\texpresschange:function(e){\n\t\t\tthis.express_index = e.detail.value;\n\t\t},\n\t\tsetexpressno:function(e){\n\t\t\tthis.express_no = e.detail.value;\n\t\t},\n\t\tconfirmfahuo:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t\tvar that = this\n\t\t\tvar express_com = this.expressdata[this.express_index]\n\t\t\tapp.post('ApiAdminOrder/sendExpress', { type:'shop',orderid: that.detail.id,express_no:that.express_no,express_com:express_com}, function (res) {\n\t\t\t\tif(res.status == 0){\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tsetexpress_pic:function(e){\n\t\t\tthis.express_pic = e.detail.value;\n\t\t},\n\t\tsetexpress_fhname:function(e){\n\t\t\tthis.express_fhname = e.detail.value;\n\t\t},\n\t\tsetexpress_fhaddress:function(e){\n\t\t\tthis.express_fhaddress = e.detail.value;\n\t\t},\n\t\tsetexpress_shname:function(e){\n\t\t\tthis.express_shname = e.detail.value;\n\t\t},\n\t\tsetexpress_shaddress:function(e){\n\t\t\tthis.express_shaddress = e.detail.value;\n\t\t},\n\t\tsetexpress_remark:function(e){\n\t\t\tthis.express_remark = e.detail.value;\n\t\t},\n\t\tconfirmfahuo10:function(){\n\t\t\tthis.$refs.dialogExpress10.close();\n\t\t\tvar that = this\n\t\t\tvar express_com = this.expressdata[this.express_index]\n\t\t\tapp.post('ApiAdminOrder/sendExpress', { type:'shop',orderid: that.detail.id,pic:that.express_pic,fhname:that.express_fhname,fhaddress:that.express_fhaddress,shname:that.express_shname,shaddress:that.express_shaddress,remark:that.express_remark}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tispay:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要改为已支付吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/ispay', { type:'shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\thexiao:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要核销并改为已完成状态吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/hexiao', { type:'shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tshowhxqr2:function(e){\n\t\t\tvar that = this;\n\t\t\tvar leftnum = e.currentTarget.dataset.num - e.currentTarget.dataset.hxnum;\n\t\t\tthis.hxogid = e.currentTarget.dataset.id;\n\t\t\tif(leftnum <= 0){\n\t\t\t\tapp.alert('没有剩余核销数量了');return;\n\t\t\t}\n\t\t\tthat.hexiao_code = e.currentTarget.dataset.hexiao_code;\n\t\t\tvar hxnumlist = [];\n\t\t\tfor(var i=0;i<leftnum;i++){\n\t\t\t\thxnumlist.push((i+1)+'');\n\t\t\t}\n\t\t\tconsole.log(hxnumlist);\n\t\t\t\n\t\t\t\tthat.hxnumlist = hxnumlist;\n\t\t\t\tthat.selecthxnumDialogShow = true;\n\t\t\t\tthat.hxnum = '';\n\t\t\t\n\t\t},\n\t\thxnumRadioChange: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tthis.hxnum = this.hxnumlist[index];\n\t\t\tapp.confirm('确定要核销'+this.hxnum+'次吗?', function () {\n\t\t\t\tthat.selecthxnumDialogShow = false;\n\t\t\t\tapp.post('ApiAdminHexiao/hexiao',{op:'confirm',type:'shopproduct',co:that.hexiao_code,hxnum:that.hxnum}, function (res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(res.status == 0){\n\t\t\t\t\t\tapp.alert(res.msg);return;\n\t\t\t\t\t}\n\t\t\t\t\tif(that.hexiao_type == 1){\n\t\t\t\t\t\t\tapp.success(tip+'成功');\n\t\t\t\t\t\t\tthat.hexiao_status = true;\n\t\t\t\t\t}else{\n\t\t\t\t\t\t\tapp.alert(res.msg,function(){\n\t\t\t\t\t\t\t\tapp.goto('/admin/index/index','reLaunch');\t\n\t\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t})\n\t\t\t})\n\t\t},\n\t\thideSelecthxnumDialog:function(){\n\t\t\tthis.selecthxnumDialogShow = false;\n\t\t},\n\t\tdelOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.showLoading('删除中');\n\t\t\tapp.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.post('ApiAdminOrder/delOrder', { type:'shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto('shoporder');\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\tcloseOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/closeOrder', { type:'shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function (){\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\trefundnopass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要驳回退款申请吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundnopass', { type:'shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\trefundpass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要审核通过并退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundpass', { type:'shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tretundInput:function(e){\n\t\t\tvar that = this;\n\t\t\tvar valnum = e.detail.value;\n\t\t\tvar {max,ogid} = e.currentTarget.dataset;\n\t\t\tvar prolist = that.returnProlist;\n\t\t\tvar refundNum = that.refundNum;\n\t\t\tvar total = 0;\n\t\t\t\n\t\t\tif(valnum > max){\n\t\t\t\treturn app.error('请输入正确的数量');\n\t\t\t}\n\t\t\t\n\t\t\tfor(var i in refundNum){\n\t\t\t\tif(refundNum[i].ogid == ogid){\n\t\t\t\t\trefundNum[i].num = valnum;\n\t\t\t\t}\n\t\t\t\tif(refundNum[i].num == prolist[i].num){\n\t\t\t\t\ttotal += parseFloat(prolist[i].real_totalprice)\n\t\t\t\t}else{\n\t\t\t\t\ttotal += refundNum[i].num * parseFloat(prolist[i].real_totalprice) / prolist[i].num \n\t\t\t\t}\n\t\t\t}\n\t\t\ttotal = parseFloat(total);\n\t\t\ttotal = total.toFixed(2);\n\t\t\tthat.refundTotalprice = total; \n\t\t},\n\t\trefundMoneyReason:function(e){\n\t\t\tthis.refundReason = e.detail.value;\n\t\t},\n\t\trefundMoney:function(e){\n\t\t\tthis.refundTotalprice = e.detail.value;\n\t\t},\n\t\trefundinit:function(e){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiAdminOrder/refundinit',{orderid:that.detail.id},function(data){\n\t\t\t\tthat.loading = false;\n\t\t\t\tlet prolist = data.prolist;\n\t\t\t\tthat.returnProlist = data.prolist;\n\t\t\t\tthat.refundTotalprice = data.detail.returnTotalprice;\n\t\t\t\tthat.refundNum = [];\n\t\t\t\tthat.refundReason = '';\n\t\t\t\tfor(var i in prolist){\n\t\t\t\t\tthat.refundNum.push({\n\t\t\t\t\t\t'ogid':prolist[i].id,\n\t\t\t\t\t\t'num':prolist[i].canRefundNum\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tthat.$refs.dialogExpress12.open();\n\t\t\t})\n\t\t},\n\t\tdialogExpress12Close:function(){\n\t\t\tthis.returnProlist = [];\n\t\t\tthis.refundReason = '';\n\t\t\tthis.$refs.dialogExpress12.close();\n\t\t},\n\t\tgotoRefundMoney:function(){\n\t\t\tvar that = this;\n\t\t\tconsole.log(that.refundNum,11111);\n\t\t\tapp.confirm('确定要退款吗?', function () {\n\t\t\t\tthat.$refs.dialogExpress12.close();\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refund',{\n\t\t\t\t\torderid:that.detail.id,\n\t\t\t\t\trefundNum:that.refundNum,\n\t\t\t\t\treason:that.refundReason,\n\t\t\t\t\tmoney:that.refundTotalprice\n\t\t\t\t},function(res){\n\t\t\t\t\tif(res.status == 0){\n\t\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tpeisong:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiAdminOrder/getpeisonguser',{type:'shop_order',orderid:that.detail.id},function(res){\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar peisonguser = res.peisonguser\n\t\t\t\tvar paidantype = res.paidantype\n\t\t\t\tvar psfee = res.psfee\n\t\t\t\tvar ticheng = res.ticheng\n\n\t\t\t\tvar peisonguser2 = [];\n\t\t\t\tfor(var i in peisonguser){\n\t\t\t\t\tpeisonguser2.push(peisonguser[i].title);\n\t\t\t\t}\n\t\t\t\tthat.peisonguser = res.peisonguser;\n\t\t\t\tthat.peisonguser2 = peisonguser2;\n\t\t\t\tif(paidantype==1){\n\t\t\t\t\tthat.$refs.dialogPeisong.open();\n\t\t\t\t}else{\n\t\t\t\t\tif(that.detail.bid == 0){\n\t\t\t\t\t\tvar tips='选择配送员配送，订单将发布到抢单大厅由配送员抢单，配送员提成￥'+ticheng+'，确定要配送员配送吗？';\n\t\t\t\t\t}else{\n\t\t\t\t\t\tvar tips='选择配送员配送，订单将发布到抢单大厅由配送员抢单，需扣除配送费￥'+psfee+'，确定要配送员配送吗？';\n\t\t\t\t\t}\n\t\t\t\t\tif(paidantype == 2){\n\t\t\t\t\t\tvar psid = '-1';\n\t\t\t\t\t}else{\n\t\t\t\t\t\tvar psid = '0';\n\t\t\t\t\t}\n\t\t\t\t\tapp.confirm(tips,function(){\n\t\t\t\t\t\tapp.post('ApiAdminOrder/peisong', { type:'shop_order',orderid: that.detail.id,psid:psid}, function (res) {\n                            if(res.status ==1){\n                                app.success(res.msg);\n                                setTimeout(function () {\n                                \tthat.getdata();\n                                }, 1000)\n                            }else{\n                                app.error(res.msg);\n                            }\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tdialogPeisongClose:function(){\n\t\t\tthis.$refs.dialogPeisong.close();\n\t\t},\n\t\tpeisongChange:function(e){\n\t\t\tthis.index2 = e.detail.value;\n\t\t},\n\t\tconfirmPeisong:function(){\n\t\t\tvar that = this\n\t\t\tvar psid = this.peisonguser[this.index2].id\n\t\t\tapp.post('ApiAdminOrder/peisong', { type:'shop_order',orderid: that.detail.id,psid:psid}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tthat.$refs.dialogPeisong.close();\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tpeisongWx:function(){\n\t\t\tvar that = this;\n\t\t\tvar psfee = that.detail.freight_price;\n\t\t\tif(that.detail.bid == 0){\n\t\t\t\tvar tips='选择即时配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？';\n\t\t\t}else{\n\t\t\t\tvar tips='选择即时配送，订单将派单到第三方配送平台，需扣除配送费￥'+psfee+'，确定要派单吗？';\n\t\t\t}\n\t\t\tapp.confirm(tips,function(){\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.post('ApiAdminOrder/peisongWx', { type:'shop_order',orderid: that.detail.id}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t})\n\t\t},\n\t\tuploadimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\n\t\t\tif(!pernum) pernum = 1;\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tif(!pics) pics = [];\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tfor(var i=0;i<urls.length;i++){\n\t\t\t\t\tpics.push(urls[i]);\n\t\t\t\t}\n\t\t\t\tif(field == 'express_pic') that.express_pic = pics[0];\n\t\t\t},pernum);\n\t\t},\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index= e.currentTarget.dataset.index\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tif(field == 'express_pic'){\n\t\t\t\tthat.express_pic = '';\n\t\t\t}\n\t\t},\n        peisongMyt:function(e){\n            var that = this;\n            var detail = that.detail;\n            if(detail.myt_set){\n                this.$refs.dialogExpress11.open();\n            }else{\n                that.goMyt();\n            }\n        },\n        goMyt:function(){\n            var that = this;\n            var detail = that.detail;\n            var tips='选择麦芽田配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？';\n            app.confirm(tips,function(){\n                that.$refs.dialogExpress11.close();\n                that.loading = true;\n                var data = {\n                    type:'shop_order',\n                    orderid: detail.id,\n                    myt_weight:that.myt_weight,\n                    myt_remark:that.myt_remark,\n                    myt_shop_id:that.myt_shop_id\n                }\n                app.post('ApiAdminOrder/peisong', data, function (res) {\n                    that.loading = false;\n                    if(res.status == 1){\n                        app.success(res.msg);\n                        setTimeout(function () {\n                            that.getdata();\n                        }, 1000)\n                    }else{\n                        app.alert(res.msg)\n                    }\n                    \n                })\n            })\n        },\n        confirmfahuo11:function(){\n        \tvar that = this\n        \tthat.goMyt();\n        },\n        dialogExpress11Close:function(){\n        \tthis.$refs.dialogExpress11.close();\n        },\n        mytWeight:function(e){\n        \tthis.myt_weight = e.detail.value;\n        },\n        mytRemark:function(e){\n        \tthis.myt_remark = e.detail.value;\n        },\n        mytshopChange:function(e){\n            var that = this;\n            var detail   = that.detail;\n            var mytindex = e.detail.value;\n            that.mytindex = mytindex;\n            //that.myt_name  = detail.myt_shoplist[mytindex]['name'];\n            that.myt_shop_id    = detail.myt_shoplist[mytindex]['id'];\n        },\n  }\n};\n</script>\n<style>\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%;height:calc(92rpx + env(safe-area-inset-bottom));padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.picker{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 5px 15px 5px;}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\n.danhao-input-view{border: 1px #eee solid;display: flex;align-items: center;flex: 1;}\n.danhao-input-view image{width: 60rpx;height: 60rpx;}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n.glassitem{background:#f5f5f5;display: flex;align-items: center;padding: 10rpx 0;font-size: 24rpx;}\n.glassitem .gcontent{flex:1;padding: 0 20rpx;}\n.glassheader{line-height: 50rpx;font-size: 26rpx;font-weight: 600;}\n.glassrow{line-height: 40rpx;font-size: 26rpx;}\n.glassrow .glasscol{min-width: 25%;text-align: center;}\n.glassitem .bt{border-top:1px solid #e3e3e3}\n.pdl10{padding-left: 10rpx;}\n.item .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.pstime-item .radio .radio-img{width:100%;height:100%}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839439946\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}