{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?fa84", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?64cd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?e248", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?1d6e", "uni-app:///admin/scoreproduct/edit.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?1362", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?30c0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "pagecontent", "aglevellist", "levellist", "clist", "cateArr", "freighttypeArr", "freightindex", "freightList", "freightdata", "freightIds", "gui<PERSON><PERSON>", "pic", "pics", "cids", "cnames", "clistshow", "ggname", "ggindex", "ggindex2", "oldgglist", "gglist", "catche_detailtxt", "product_showset", "commission_canset", "bid", "paramList", "paramdata", "resparamdata", "editorFormdata", "test", "scoreshop_guige", "onLoad", "methods", "getdata", "that", "app", "id", "thisval", "console", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "setfield", "subform", "formdata", "setTimeout", "detailAddtxt", "dialogDetailtxtClose", "catcheDetailtxt", "dialogDetailtxtConfirm", "detailAddpic", "detailAddvideo", "uni", "sourceType", "success", "url", "filePath", "name", "fail", "detailMoveup", "detailMovedown", "detailMovedel", "changeFrieght", "newfreightIds", "ischecked", "freighttypeChange", "bindStatusChange", "bindguigesetChange", "gglistInput", "getgglist", "newlen", "h", "k", "title", "tarr", "sarr", "ks", "titles", "market_price", "cost_price", "sell_price", "weight", "stock", "givescore", "lvprice_data", "addgggroupname", "delgggroupname", "itemList", "newguigedata", "setgggroupname", "items", "addggname", "delggname", "newitems", "index2", "setggname", "cidsChange", "getcnames", "changeClistDialog", "uploadimg", "removeimg", "uploadimg2", "removeimg2"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,iNAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,iQAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnSA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Vt1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;QACAA;QACA;QACAA;QAEAA;QACAA;QACA;QACA;QACA;QACA;UACA;UACA;YACA;cACAG;YACA;cACAA;YACA;UACA;UAEA;YACA;cACA;gBACAA;cACA;YACA;UACA;UACAT;UACAF;QACA;QACAY;QACAA;QACAJ;QACAA;QACA,cACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAK;MACA;MACA;MACA;MACA;MACA;MACAX;MACAU;MACA;MACA;MAEA;MACA;IACA;IACAE;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAC;MACA;MACA;QACAP;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;MACA;QACAO;MACA;QACAA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;MACAP;QAAAC;QAAArC;QAAAW;QAAAU;QAAApB;MAAA;QACA;UACAmC;QACA;UACAA;UACAQ;YACAR;UACA;QACA;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAR;MACA;IACA;IACAS;MACA;MACAT;MACA;MACA;MACAtC;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MACA;MACA;IACA;IACAgD;MACA;MACAb;QACA;QACA;QACA;UACA;UACAvB;YAAA;YAAA;YAAA;YAAA;UAAA;QACA;QACA;QACAZ;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;cAAA;YAAA;YAAA;cAAA;YAAA;UAAA;UAAA;UAAA;UAAA;QAAA;QACAkC;MACA;IACA;IACAe;MACA;MACAC;QACAC;QACAC;UACA;UACAjB;UACAe;YACAG;YACAC;YACAC;YACAH;cACAjB;cACA;cACA;gBACAD;gBACA;gBACA;gBACAlC;kBAAA;kBAAA;kBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;sBAAA;oBAAA;oBAAA;sBAAA;oBAAA;kBAAA;kBAAA;kBAAA;kBAAA;gBAAA;gBACAkC;cACA;gBACAC;cACA;YACA;YACAqB;cACArB;cACAA;YACA;UACA;QACA;QACAqB;UACAlB;QACA;MACA;IACA;;IACAmB;MACA;MACA;MACA,eACAzD;IACA;IACA0D;MACA;MACA;MACA,oCACA1D;IACA;IACA2D;MACA;MACA;MACA3D;IACA;IACA4D;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA3B;MACA;MACA;QACA;UAAA;UAAA;UAAA;YAAA;YAAA;UAAA;QAAA;QACA;UACA;UAAA;QACA;MACA;MACA;IACA;IACA4B;MACA;MACA;MACA;MACA9C;MACA;MACAkB;IACA;IACA6B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UAAA;QAAA;QAAA;QACAC;QACAC;QACA;UACAA;YAAAC;YAAAC;UAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;QACA;QACA;UACA;YACAC;UACA;QAAA;QACAC;MACA;MACAnC;MACAA;MAEA;QACA;QACA;QACA;UACAoC;UACAC;QACA;QACAD;QACAC;QACA;QACA;QACA;UACA;QACA;UACA;YAAAD;YAAAnB;YAAAqB;YAAAC;YAAAC;YAAAC;YAAAC;YAAArE;YAAAsE;YAAAC;UAAA;QACA;QACA9D;MACA;MACA;MACAkB;IACA;IACA6C;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAlC;QACAmC;QACAjC;UACA;YACA;cAAA;cACAlB;cACAA;cACAA;cAAA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACAoD;gBACA;cACA;cACApD;cACAI;cACAJ;YACA;UACA;QACA;MACA;IACA;IACAqD;MACA;MACA;MACA;QAAA;QACAtE;QACAP;UAAA4D;UAAAC;UAAAiB;QAAA;QACA;MACA;QAAA;QACA9E;QACA;MACA;MACA;MACA;IACA;IACA+E;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAxC;QACAmC;QACAjC;UACA;YACA;cAAA;cACAlB;cACAA;cACAA;cACAA;cAAA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACA;kBACA;kBACA;oBACA;sBACAyD;wBAAArB;wBAAAC;sBAAA;sBACAqB;oBACA;kBACA;kBACAlF;gBACA;gBACA4E;cACA;cACApD;cACAI;cACAJ;YACA;UACA;QACA;MACA;IACA;IACA2D;MACA;MACA;MACA;MACA;QAAA;QACA;QACA3E;QACAsE;UAAAlB;UAAAC;QAAA;QACA7D;QACA;MACA;QAAA;QACAA;QACA;MACA;MACA;MACA;IACA;IACAoF;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAzD;MACA;MACA;QACAxB;MACA;MACA;IACA;IACAkF;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA9D;QACA;UACAvB;QACA;QACA;QACA;MACA;IACA;IACAsF;MACA;MACA;MACA;MACA;QACA;QACAtF;QACAsB;MACA;QACA;QACAtB;QACAsB;MACA;IACA;IACAiE;MACA;MACA;MACAhE;QACAD;MACA;IACA;IACAkE;MACA;MACA;MACAlE;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACz2BA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/scoreproduct/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/scoreproduct/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=99dcda44&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/scoreproduct/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=99dcda44&\"", "var components\ntry {\n  components = {\n    dpNotice: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-notice/dp-notice\" */ \"@/components/dp-notice/dp-notice.vue\"\n      )\n    },\n    dpBanner: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-banner/dp-banner\" */ \"@/components/dp-banner/dp-banner.vue\"\n      )\n    },\n    dpSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-search/dp-search\" */ \"@/components/dp-search/dp-search.vue\"\n      )\n    },\n    dpText: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-text/dp-text\" */ \"@/components/dp-text/dp-text.vue\"\n      )\n    },\n    dpTitle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-title/dp-title\" */ \"@/components/dp-title/dp-title.vue\"\n      )\n    },\n    dpDhlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-dhlist/dp-dhlist\" */ \"@/components/dp-dhlist/dp-dhlist.vue\"\n      )\n    },\n    dpLine: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-line/dp-line\" */ \"@/components/dp-line/dp-line.vue\"\n      )\n    },\n    dpBlank: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-blank/dp-blank\" */ \"@/components/dp-blank/dp-blank.vue\"\n      )\n    },\n    dpMenu: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-menu/dp-menu\" */ \"@/components/dp-menu/dp-menu.vue\"\n      )\n    },\n    dpMap: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-map/dp-map\" */ \"@/components/dp-map/dp-map.vue\"\n      )\n    },\n    dpCube: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cube/dp-cube\" */ \"@/components/dp-cube/dp-cube.vue\"\n      )\n    },\n    dpPicture: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-picture/dp-picture\" */ \"@/components/dp-picture/dp-picture.vue\"\n      )\n    },\n    dpPictures: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-pictures/dp-pictures\" */ \"@/components/dp-pictures/dp-pictures.vue\"\n      )\n    },\n    dpVideo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-video/dp-video\" */ \"@/components/dp-video/dp-video.vue\"\n      )\n    },\n    dpShop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-shop/dp-shop\" */ \"@/components/dp-shop/dp-shop.vue\"\n      )\n    },\n    dpProduct: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product/dp-product\" */ \"@/components/dp-product/dp-product.vue\"\n      )\n    },\n    dpCollage: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-collage/dp-collage\" */ \"@/components/dp-collage/dp-collage.vue\"\n      )\n    },\n    dpKanjia: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kanjia/dp-kanjia\" */ \"@/components/dp-kanjia/dp-kanjia.vue\"\n      )\n    },\n    dpSeckill: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-seckill/dp-seckill\" */ \"@/components/dp-seckill/dp-seckill.vue\"\n      )\n    },\n    dpScoreshop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-scoreshop/dp-scoreshop\" */ \"@/components/dp-scoreshop/dp-scoreshop.vue\"\n      )\n    },\n    dpCoupon: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-coupon/dp-coupon\" */ \"@/components/dp-coupon/dp-coupon.vue\"\n      )\n    },\n    dpArticle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-article/dp-article\" */ \"@/components/dp-article/dp-article.vue\"\n      )\n    },\n    dpBusiness: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-business/dp-business\" */ \"@/components/dp-business/dp-business.vue\"\n      )\n    },\n    dpLiveroom: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-liveroom/dp-liveroom\" */ \"@/components/dp-liveroom/dp-liveroom.vue\"\n      )\n    },\n    dpButton: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-button/dp-button\" */ \"@/components/dp-button/dp-button.vue\"\n      )\n    },\n    dpHotspot: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-hotspot/dp-hotspot\" */ \"@/components/dp-hotspot/dp-hotspot.vue\"\n      )\n    },\n    dpCover: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cover/dp-cover\" */ \"@/components/dp-cover/dp-cover.vue\"\n      )\n    },\n    dpRichtext: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-richtext/dp-richtext\" */ \"@/components/dp-richtext/dp-richtext.vue\"\n      )\n    },\n    dpForm: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-form/dp-form\" */ \"@/components/dp-form/dp-form.vue\"\n      )\n    },\n    dpUserinfo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-userinfo/dp-userinfo\" */ \"@/components/dp-userinfo/dp-userinfo.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.cids.length : null\n  var g1 = _vm.isload ? _vm.pic.length : null\n  var g2 = _vm.isload ? _vm.pic.join(\",\") : null\n  var g3 = _vm.isload ? _vm.pics.length : null\n  var g4 = _vm.isload ? _vm.pics.join(\",\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.gglist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.t(\"积分\")\n        var m1 = _vm.t(\"积分\")\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var l1 =\n    _vm.isload && _vm.freightindex == 1\n      ? _vm.__map(_vm.freightList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.inArray(item.id, _vm.freightIds)\n          var m3 = m2 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n          }\n        })\n      : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l4 =\n    _vm.isload && _vm.clistshow\n      ? _vm.__map(_vm.clist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.inArray(item.id, _vm.cids)\n          var m7 = m6 ? _vm.t(\"color1\") : null\n          var g5 = item.child.length\n          var l3 = _vm.__map(item.child, function (item2, index2) {\n            var $orig = _vm.__get_orig(item2)\n            var m8 = _vm.inArray(item2.id, _vm.cids)\n            var m9 = m8 ? _vm.t(\"color1\") : null\n            var g6 = item2.child.length\n            var l2 = _vm.__map(item2.child, function (item3, index3) {\n              var $orig = _vm.__get_orig(item3)\n              var m10 = _vm.inArray(item3.id, _vm.cids)\n              var m11 = m10 ? _vm.t(\"color1\") : null\n              return {\n                $orig: $orig,\n                m10: m10,\n                m11: m11,\n              }\n            })\n            return {\n              $orig: $orig,\n              m8: m8,\n              m9: m9,\n              g6: g6,\n              l2: l2,\n            }\n          })\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            g5: g5,\n            l3: l3,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        l0: l0,\n        l1: l1,\n        m4: m4,\n        m5: m5,\n        l4: l4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">商品名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"name\" :value=\"info.name\" placeholder=\"请填写商品名称\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">商品分类<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"changeClistDialog\"><text v-if=\"cids.length>0\">{{cnames}}</text><text v-else style=\"color:#888\">请选择</text><image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\">商品主图<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" data-pernum=\"1\" v-if=\"pic.length==0\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<view class=\"f1\">商品图片</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"flex-wrap:wrap\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" data-pernum=\"9\" v-if=\"pics.length<5\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\" v-show=\"scoreshop_guige\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text>多规格</text>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" name=\"guigeset\" @change=\"bindguigesetChange\">\r\n\t\t\t\t\t\t\t<label><radio value=\"0\" :checked=\"!info || info.guigeset==0?true:false\"></radio> 关闭</label>\r\n\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.guigeset==1?true:false\"></radio> 开启</label> \r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"form-item flex-col\" v-show=\"info.guigeset==1\">\r\n\t\t\t\t\t<view class=\"f1\">设置规格</view>\r\n\t\t\t\t\t<view class=\"flex-col\">\r\n\t\t\t\t\t\t<view class=\"ggtitle\">\r\n\t\t\t\t\t\t\t<view class=\"t1\">规格分组</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\">规格名称</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"ggcontent\" v-for=\"(gg,index) in guigedata\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"t1\" @tap=\"delgggroupname\" :data-index=\"index\" :data-title=\"gg.title\">{{gg.title}}<image class=\"edit\" :src=\"pre_url+'/static/img/edit2.png'\"/></view>\r\n\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t<view class=\"ggname\" v-for=\"(ggitem,index2) in gg.items\" :key=\"index2\" @tap=\"delggname\" :data-index=\"index\" :data-index2=\"index2\" :data-title=\"ggitem.title\" :data-k=\"ggitem.k\">{{ggitem.title}}<image class=\"close\" :src=\"pre_url+'/static/img/ico-del.png'\"/></view>\r\n\t\t\t\t\t\t\t\t<view class=\"ggnameadd\" @tap=\"addggname\" :data-index=\"index\">+</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"ggcontent\">\r\n\t\t\t\t\t\t\t<view class=\"ggadd\" @tap=\"addgggroupname\">添加分组</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<!-- 规格列表 -->\r\n\t\t\t<view class=\"form-box\" v-for=\"(item,index) in gglist\" :key=\"index\">\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\" v-show=\"info.guigeset==1\">\r\n\t\t\t\t\t<view class=\"f1\">规格</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"font-weight:bold\">{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">市场价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"market_price\" :name=\"'market_price['+index+']'\" :value=\"item.market_price\" placeholder=\"请填写市场价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">成本价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"cost_price\" :name=\"'cost_price['+index+']'\" :value=\"item.cost_price\" placeholder=\"请填写成本价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">所需金额（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"money_price\" :name=\"'money_price['+index+']'\" :value=\"item.money_price\" placeholder=\"请填写所需金额\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">所需{{t('积分')}}（个）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"score_price\" :name=\"'score_price['+index+']'\" :value=\"item.score_price\" :placeholder=\"'请填写所需'+t('积分')\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">重量（克）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"weight\" :name=\"'weight['+index+']'\" :value=\"item.weight\" placeholder=\"请填写重量\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">库存</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"stock\" :name=\"'stock['+index+']'\" :value=\"item.stock\" placeholder=\"请填写库存\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-show=\"info.guigeset==1\">\r\n\t\t\t\t\t<view class=\"f1\">规格图片</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"flex-wrap:wrap;margin-top:20rpx;margin-bottom:20rpx\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox\" v-if=\"item.pic!=''\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg2\" :data-index=\"index\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item.pic\" @tap=\"previewImage\" :data-url=\"item.pic\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" v-else :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg2\" :data-index=\"index\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">配送模板</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<picker @change=\"freighttypeChange\" :value=\"freightindex\" :range=\"freighttypeArr\">\r\n\t\t\t\t\t\t\t<view class=\"picker\">{{freighttypeArr[freightindex]}}</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\" v-if=\"freightindex==1\">\r\n\t\t\t\t\t<view class=\"f1\">选择模板</view>\r\n\t\t\t\t\t<view class=\"f2 flex-col\">\r\n\t\t\t\t\t\t<view class=\"freightitem\" style=\"width:100%\" v-for=\"(item,index) in freightList\" :key=\"index\" @tap=\"changeFrieght\" :data-index=\"index\" :data-id=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item.id,freightIds) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\" v-if=\"freightindex==2\">\r\n\t\t\t\t\t<view class=\"f1\">发货信息</view>\r\n\t\t\t\t\t<view class=\"f2 flex-col\"><textarea name=\"freightdata\" placeholder=\"请输入发货信息\" placeholder-style=\"color:#888;font-size:28rpx\" style=\"height:160rpx;min-height:160rpx;font-size:28rpx\" maxlength=\"-1\" :value=\"info.freightdata\"></textarea></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\" v-if=\"freightindex==3\" style=\"color:#999\">请在电脑端后台上传卡密信息</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">销量</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"sales\" :value=\"info.sales\" placeholder=\"请填写销量\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"oldsales\" :value=\"info.id?info.sales:'0'\">\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">每人限购</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"buymax\" :value=\"info.buymax\" placeholder=\"0表示不限购\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">序号</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"sort\" :value=\"info.sort\" placeholder=\"用于排序,越大越靠前\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view>状态<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" name=\"status\" @change=\"bindStatusChange\">\r\n\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.status==1?true:false\"></radio> 上架</label> \r\n\t\t\t\t\t\t\t<label><radio value=\"0\" :checked=\"!info || info.status==0?true:false\"></radio> 下架</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<text>商品详情</text>\r\n\t\t\t\t\t<view class=\"detailop\"><view class=\"btn\" @tap=\"detailAddtxt\">+文本</view><view class=\"btn\" @tap=\"detailAddpic\">+图片</view><view class=\"btn\" @tap=\"detailAddvideo\">+视频</view></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<block v-for=\"(setData, index) in pagecontent\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"detaildp\">\r\n\t\t\t\t\t\t\t<view class=\"op\"><view class=\"flex1\"></view><view class=\"btn\" @tap=\"detailMoveup\" :data-index=\"index\">上移</view><view class=\"btn\" @tap=\"detailMovedown\" :data-index=\"index\">下移</view><view class=\"btn\" @tap=\"detailMovedel\" :data-index=\"index\">删除</view></view>\r\n\t\t\t\t\t\t\t<view class=\"detailbox\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='notice'\">\r\n\t\t\t\t\t\t\t\t\t<dp-notice :params=\"setData.params\" :data=\"setData.data\"></dp-notice>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='banner'\">\r\n\t\t\t\t\t\t\t\t\t<dp-banner :params=\"setData.params\" :data=\"setData.data\"></dp-banner> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='search'\">\r\n\t\t\t\t\t\t\t\t\t<dp-search :params=\"setData.params\" :data=\"setData.data\"></dp-search>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='text'\">\r\n\t\t\t\t\t\t\t\t\t<dp-text :params=\"setData.params\" :data=\"setData.data\"></dp-text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='title'\">\r\n\t\t\t\t\t\t\t\t\t<dp-title :params=\"setData.params\" :data=\"setData.data\"></dp-title>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='dhlist'\">\r\n\t\t\t\t\t\t\t\t\t<dp-dhlist :params=\"setData.params\" :data=\"setData.data\"></dp-dhlist>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='line'\">\r\n\t\t\t\t\t\t\t\t\t<dp-line :params=\"setData.params\" :data=\"setData.data\"></dp-line>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='blank'\">\r\n\t\t\t\t\t\t\t\t\t<dp-blank :params=\"setData.params\" :data=\"setData.data\"></dp-blank>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='menu'\">\r\n\t\t\t\t\t\t\t\t\t<dp-menu :params=\"setData.params\" :data=\"setData.data\"></dp-menu> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='map'\">\r\n\t\t\t\t\t\t\t\t\t<dp-map :params=\"setData.params\" :data=\"setData.data\"></dp-map> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cube'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cube :params=\"setData.params\" :data=\"setData.data\"></dp-cube> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='picture'\">\r\n\t\t\t\t\t\t\t\t\t<dp-picture :params=\"setData.params\" :data=\"setData.data\"></dp-picture> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='pictures'\"> \r\n\t\t\t\t\t\t\t\t\t<dp-pictures :params=\"setData.params\" :data=\"setData.data\"></dp-pictures> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='video'\">\r\n\t\t\t\t\t\t\t\t\t<dp-video :params=\"setData.params\" :data=\"setData.data\"></dp-video> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='shop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-shop :params=\"setData.params\" :data=\"setData.data\" :shopinfo=\"setData.shopinfo\"></dp-shop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='product'\">\r\n\t\t\t\t\t\t\t\t\t<dp-product :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-product> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='collage'\">\r\n\t\t\t\t\t\t\t\t\t<dp-collage :params=\"setData.params\" :data=\"setData.data\"></dp-collage> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='kanjia'\">\r\n\t\t\t\t\t\t\t\t\t<dp-kanjia :params=\"setData.params\" :data=\"setData.data\"></dp-kanjia> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='seckill'\">\r\n\t\t\t\t\t\t\t\t\t<dp-seckill :params=\"setData.params\" :data=\"setData.data\"></dp-seckill> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='scoreshop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-scoreshop :params=\"setData.params\" :data=\"setData.data\"></dp-scoreshop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='coupon'\">\r\n\t\t\t\t\t\t\t\t\t<dp-coupon :params=\"setData.params\" :data=\"setData.data\"></dp-coupon> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='article'\">\r\n\t\t\t\t\t\t\t\t\t<dp-article :params=\"setData.params\" :data=\"setData.data\"></dp-article> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='business'\">\r\n\t\t\t\t\t\t\t\t\t<dp-business :params=\"setData.params\" :data=\"setData.data\"></dp-business> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='liveroom'\">\r\n\t\t\t\t\t\t\t\t\t<dp-liveroom :params=\"setData.params\" :data=\"setData.data\"></dp-liveroom> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='button'\">\r\n\t\t\t\t\t\t\t\t\t<dp-button :params=\"setData.params\" :data=\"setData.data\"></dp-button> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='hotspot'\">\r\n\t\t\t\t\t\t\t\t\t<dp-hotspot :params=\"setData.params\" :data=\"setData.data\"></dp-hotspot> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cover'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cover :params=\"setData.params\" :data=\"setData.data\"></dp-cover> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='richtext'\">\r\n\t\t\t\t\t\t\t\t\t<dp-richtext :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-richtext> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='form'\">\r\n\t\t\t\t\t\t\t\t\t<dp-form :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-form> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='userinfo'\">\r\n\t\t\t\t\t\t\t\t\t<dp-userinfo :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-userinfo> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">提交</button>\r\n\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t</form>\r\n\r\n\t\t\r\n\t\t<view class=\"popup__container\" v-if=\"clistshow\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"changeClistDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择商品分类</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"changeClistDialog\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"item.id\">\r\n\t\t\t\t\t\t<view class=\"clist-item\" @tap=\"cidsChange\" :data-id=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item.id,cids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.child\" :key=\"item2.id\">\r\n\t\t\t\t\t\t\t<view class=\"clist-item\" style=\"padding-left:80rpx\" @tap=\"cidsChange\" :data-id=\"item2.id\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\" v-if=\"item.child.length-1==index2\">└ {{item2.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\" v-else>├ {{item2.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item2.id,cids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-for=\"(item3, index3) in item2.child\" :key=\"item3.id\">\r\n\t\t\t\t\t\t\t<view class=\"clist-item\" style=\"padding-left:160rpx\" @tap=\"cidsChange\" :data-id=\"item3.id\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\" v-if=\"item2.child.length-1==index3\">└ {{item3.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\" v-else>├ {{item3.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item3.id,cids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<uni-popup id=\"dialogInput\" ref=\"dialogInput\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"输入规格名称\" :value=\"ggname\" placeholder=\"请输入规格名称\" @confirm=\"setggname\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t<uni-popup id=\"dialogInput2\" ref=\"dialogInput2\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"输入规格分组\" :value=\"ggname\" placeholder=\"请输入规格分组\" @confirm=\"setgggroupname\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t<uni-popup id=\"dialogDetailtxt\" ref=\"dialogDetailtxt\" type=\"dialog\">\r\n\t\t\t<view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请输入文本内容</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<textarea value=\"\" placeholder=\"请输入文本内容\" @input=\"catcheDetailtxt\"></textarea>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"dialogDetailtxtConfirm\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-popup-dialog__close\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t<span class=\"uni-popup-dialog__close-icon \"></span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</block>\r\n\t<view style=\"display:none\">{{test}}</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:false,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{},\r\n\t\t\tpagecontent:[],\r\n\t\t\taglevellist:[],\r\n\t\t\tlevellist:[],\r\n\t\t\tclist:[],\r\n\t\t\tcateArr:[],\r\n\t\t\tfreighttypeArr:['全部模板','指定模板','自动发货','在线卡密'],\r\n\t\t\tfreightindex:0,\r\n\t\t\tfreightList:[],\r\n\t\t\tfreightdata:[],\r\n\t\t\tfreightIds:[],\r\n\t\t\tguigedata:[],\r\n\t\t\tpic:[],\r\n\t\t\tpics:[],\r\n\t\t\tcids:[],\r\n\t\t\tcnames:'',\r\n\t\t\tclistshow:false,\r\n\t\t\tggname:'',\r\n\t\t\tggindex:0,\r\n\t\t\tggindex2:0,\r\n\t\t\toldgglist:[],\r\n\t\t\tgglist:[],\r\n\t\t\tcatche_detailtxt:'',\r\n\t\t\tproduct_showset:1,\r\n\t\t\tcommission_canset:1,\r\n\t\t\tbid:0,\r\n\t\t\tparamList:[],\r\n\t\t\tparamdata:[],\r\n\t\t\tresparamdata:{},\r\n\t\t\teditorFormdata:[],\r\n\t\t\ttest:'',\r\n\t\t\tscoreshop_guige:false,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminScoreshopProduct/edit',{id:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.info = res.info;\r\n\t\t\t\tthat.scoreshop_guige = res.scoreshop_guige;\r\n\t\t\t\tthat.product_showset = res.product_showset\r\n\t\t\t\tthat.commission_canset = res.commission_canset\r\n\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\tthat.aglevellist = res.aglevellist;\r\n\t\t\t\tthat.levellist = res.levellist;\r\n\t\t\t\tthat.oldgglist = res.newgglist;\r\n\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\tthat.cateArr = res.cateArr;\r\n\t\t\t\tthat.freightList = res.freightList;\r\n\t\t\t\tthat.freightdata = res.freightdata;\r\n\t\t\t\tif(res.info.freighttype == 1) that.freightindex = 0;\r\n\t\t\t\tif(res.info.freighttype == 0){\r\n\t\t\t\t\tthat.freightindex = 1;\r\n\t\t\t\t\tif(res.info.freightdata){\r\n\t\t\t\t\t\tthat.freightIds = res.info.freightdata.split(',');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(res.info.freighttype == 3) that.freightindex = 2;\r\n\t\t\t\tif(res.info.freighttype == 4) that.freightindex = 3;\r\n\t\t\t\tthat.pic = res.pic;\r\n\t\t\t\tthat.pics = res.pics;\r\n\t\t\t\tif(res.info.cid) that.cids = [res.info.cid];\r\n\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\t\r\n\t\t\t\tthat.paramList = res.paramList;\r\n\t\t\t\tthat.resparamdata = res.paramdata;\r\n\t\t\t\tvar paramList = res.paramList;\r\n\t\t\t\tvar editorFormdata = [];\r\n\t\t\t\tvar paramdata = {};\r\n\t\t\t\tfor(var i in paramList){\r\n\t\t\t\t\tvar thisval = res.paramdata[paramList[i].name];\r\n\t\t\t\t\tif(!thisval){\r\n\t\t\t\t\t\tif(paramList[i].type ==2){\r\n\t\t\t\t\t\t\tthisval = [];\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthisval = '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (paramList[i].type == '1') {\r\n\t\t\t\t\t\tfor(var j in paramList[i].params){\r\n\t\t\t\t\t\t\tif(paramList[i].params[j] == thisval){\r\n\t\t\t\t\t\t\t\tthisval = j;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\teditorFormdata.push(thisval);\r\n\t\t\t\t\tparamdata['form'+i] = thisval;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\tconsole.log(paramdata)\r\n\t\t\t\tthat.editorFormdata = editorFormdata;\r\n\t\t\t\tthat.paramdata = paramdata;\r\n\t\t\t\tif(res.bset)\r\n\t\t\t\tthat.bset = res.bset;\r\n\t\t\t\tthat.bid = res.bid;\r\n\t\t\t\tthat.getcnames();\r\n\t\t\t\tthat.getgglist();\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\teditorBindPickerChange:function(e){\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\tvar val = e.detail.value;\r\n\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\teditorFormdata[idx] = val;\r\n\t\t\tconsole.log(editorFormdata)\r\n\t\t\tthis.editorFormdata = editorFormdata;\r\n\t\t\tthis.test = Math.random();\r\n\r\n\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\tthis.paramdata[field] = val;\r\n\t\t},\r\n\t\tsetfield:function(e){\r\n\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\tvar value = e.detail.value;\r\n\t\t\tthis.paramdata[field] = value;\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var formdata = e.detail.value;\r\n      formdata.cid = (that.cids).join(',');\r\n\t\t\tvar guigedata = that.guigedata;\r\n\t\t\tif(guigedata.length == 0){\r\n\t\t\t\tapp.alert('至少需要添加一个规格');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tfor(var i in guigedata){\r\n\t\t\t\tif(guigedata[i].items.length==0){\r\n\t\t\t\t\tapp.alert('规格分组['+guigedata[i].title+']至少需要添加一个规格');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tvar freightindex = this.freightindex;\r\n\t\t\tif(freightindex == 0){\r\n\t\t\t\tformdata.freighttype = 1;\r\n\t\t\t}else if(freightindex == 1){\r\n\t\t\t\tformdata.freighttype = 0;\r\n\t\t\t\tformdata.freightdata = this.freightIds.join(',');\r\n\t\t\t}else if(freightindex == 2){\r\n\t\t\t\tformdata.freighttype = 3;\r\n\t\t\t}else if(freightindex == 3){\r\n\t\t\t\tformdata.freighttype = 4;\r\n\t\t\t}\r\n      var id = that.opt.id ? that.opt.id : '';\r\n      app.post('ApiAdminScoreshopProduct/save', {id:id,info:formdata,guigedata:guigedata,gglist:that.gglist,pagecontent:that.pagecontent}, function (res) {\r\n        if (res.status == 0) {\r\n          app.error(res.msg);\r\n        } else {\r\n          app.success(res.msg);\r\n          setTimeout(function () {\r\n            app.goto('index', 'redirect');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\t\tdetailAddtxt:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.open();\r\n\t\t},\r\n\t\tdialogDetailtxtClose:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tcatcheDetailtxt:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.catche_detailtxt = e.detail.value;\r\n\t\t},\r\n\t\tdialogDetailtxtConfirm:function(e){\r\n\t\t\tvar detailtxt = this.catche_detailtxt;\r\n\t\t\tconsole.log(detailtxt)\r\n\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"text\",\"params\":{\"content\":detailtxt,\"showcontent\":detailtxt,\"bgcolor\":\"#ffffff\",\"fontsize\":\"14\",\"lineheight\":\"20\",\"letter_spacing\":\"0\",\"bgpic\":\"\",\"align\":\"left\",\"color\":\"#000\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"5\",\"padding_y\":\"5\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\tthis.pagecontent = pagecontent;\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tdetailAddpic:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\tvar pics = [];\r\n\t\t\t\tfor(var i in urls){\r\n\t\t\t\t\tvar picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\tpics.push({\"id\":picid,\"imgurl\":urls[i],\"hrefurl\":\"\",\"option\":\"0\"})\r\n\t\t\t\t}\r\n\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"picture\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":pics,\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t},9);\r\n\t\t},\r\n\t\tdetailAddvideo:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseVideo({\r\n        sourceType: ['album', 'camera'],\r\n        success: function (res) {\r\n          var tempFilePath = res.tempFilePath;\r\n          app.showLoading('上传中');\r\n          uni.uploadFile({\r\n            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\r\n            filePath: tempFilePath,\r\n            name: 'file',\r\n            success: function (res) {\r\n              app.showLoading(false);\r\n              var data = JSON.parse(res.data);\r\n              if (data.status == 1) {\r\n                that.video = data.url;\r\n\t\t\t\t\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\t\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"video\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"src\":data.url,\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\t\t\t\t\tthat.pagecontent = pagecontent;\r\n              } else {\r\n                app.alert(data.msg);\r\n              }\r\n            },\r\n            fail: function (res) {\r\n              app.showLoading(false);\r\n              app.alert(res.errMsg);\r\n            }\r\n          });\r\n        },\r\n        fail: function (res) {\r\n          console.log(res); //alert(res.errMsg);\r\n        }\r\n      });\r\n\t\t},\r\n\t\tdetailMoveup:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index > 0)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index-1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedown:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index < pagecontent.length-1)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index+1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedel:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.splice(index,1);\r\n\t\t},\r\n\t\tchangeFrieght:function(e){\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar freightIds = this.freightIds;\r\n\t\t\tvar newfreightIds = [];\r\n\t\t\tvar ischecked = false;\r\n\t\t\tfor(var i in freightIds){\r\n\t\t\t\tif(freightIds[i] != id){\r\n\t\t\t\t\tnewfreightIds.push(freightIds[i]);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tischecked = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(!ischecked) newfreightIds.push(id);\r\n\t\t\tthis.freightIds = newfreightIds;\r\n\t\t},\r\n\t\tfreighttypeChange:function(e){\r\n\t\t\tthis.freightindex = e.detail.value;\r\n\t\t},\r\n\t\tbindStatusChange:function(e){\r\n\t\t\tthis.info.status = e.detail.value;\r\n\t\t},\r\n\t\tbindguigesetChange:function(e){\r\n\t\t\tconsole.log(e.detail.value)\r\n\t\t\tthis.info.guigeset = e.detail.value;\r\n\t\t\tif(this.info.guigeset == 0){\r\n\t\t\t\tthis.guigedata = [{\"k\":0,\"title\":\"规格\",\"items\":[{\"k\":0,\"title\":\"默认规格\"}]}];\r\n\t\t\t\tfor(var k in this.gglist){\r\n\t\t\t\t\tthis.gglist = [this.gglist[k]];break;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.test = Math.random();\r\n\t\t},\r\n\t\tgglistInput:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar gglist = this.gglist;\r\n\t\t\tgglist[index][field] = e.detail.value;\r\n\t\t\tthis.gglist = gglist;\r\n\t\t\tconsole.log(gglist)\r\n\t\t},\r\n\t\tgetgglist:function(){\r\n\t\t\tvar oldgglist = this.oldgglist;\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar gglist = [];\r\n\t\t\tvar len = guigedata.length;\r\n\t\t\tvar newlen = 1; \r\n\t\t\tvar h = new Array(len);\r\n\t\t\tfor(var i=0;i<len;i++){\r\n\t\t\t\tvar itemlen = guigedata[i].items.length;\r\n\t\t\t\tif(itemlen<=0) { return; };\r\n\t\t\t\tnewlen*=itemlen;\r\n\t\t\t\th[i] = new Array(itemlen);\r\n\t\t\t\tfor(var j=0;j<itemlen;j++){\r\n\t\t\t\t\th[i][j] = { k:guigedata[i].items[j].k,title:guigedata[i].items[j].title};\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t \r\n\t\t\t//排列组合算法\r\n\t\t\tvar arr = h;  //原二维数组\r\n\t\t\tvar sarr = [[]];  //排列组合后的数组\r\n\t\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\t\tvar tarr = [];\r\n\t\t\t\tfor (var j = 0; j < sarr.length; j++)\r\n\t\t\t\t\tfor (var k = 0; k < arr[i].length; k++){\r\n\t\t\t\t\t\ttarr.push(sarr[j].concat(arr[i][k]));\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsarr = tarr;\r\n\t\t\t}\r\n\t\t\tconsole.log(sarr);\r\n\t\t\tconsole.log(' ------ ');\r\n\t\t\r\n\t\t\tfor(var i=0;i<sarr.length;i++){\r\n\t\t\t\tvar ks = [];\r\n\t\t\t\tvar titles = [];\r\n\t\t\t\tfor(var j=0;j<sarr[i].length;j++){\r\n\t\t\t\t\tks.push( sarr[i][j].k);\r\n\t\t\t\t\ttitles.push( sarr[i][j].title);\r\n\t\t\t\t}\r\n\t\t\t\tks =ks.join(',');\r\n\t\t\t\ttitles =titles.join(',');\r\n\t\t\t\t//console.log(ks);\r\n\t\t\t\t//console.log(titles);\r\n\t\t\t\tif(typeof(oldgglist[ks])!='undefined'){\r\n\t\t\t\t\tvar val = oldgglist[ks];\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvar val = { ks:ks,name:titles,market_price:'',cost_price:'',sell_price:'',weight:'100',stock:'1000',pic:'',givescore:'0',lvprice_data:null};\r\n\t\t\t\t}\r\n\t\t\t\tgglist.push(val);\r\n\t\t\t}\r\n\t\t\tthis.gglist = gglist;\r\n\t\t\tconsole.log(gglist);\r\n\t\t},\r\n\t\taddgggroupname:function(e){\r\n\t\t\tthis.ggname = '';\r\n\t\t\tthis.ggindex = -1;\r\n\t\t\tthis.$refs.dialogInput2.open();\r\n\t\t},\r\n\t\tdelgggroupname:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: [ '修改','删除'],\r\n        success: function (res) {\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\tthat.$refs.dialogInput2.open();return;\r\n\t\t\t\t\t\t}else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\tfor(var i in guigedata){\r\n\t\t\t\t\t\t\t\tif(i != ggindex){\r\n\t\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\tconsole.log(newguigedata);\r\n\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetgggroupname:function(done,val){\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar ggindex = this.ggindex;\r\n\t\t\tif(ggindex == -1){ //新增规格分组\r\n\t\t\t\tggindex = guigedata.length;\r\n\t\t\t\tguigedata.push({k:ggindex,title:val,items:[]});\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}else{ //修改规格分组名称\r\n\t\t\t\tguigedata[ggindex].title = val;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}\r\n\t\t\tthis.$refs.dialogInput2.close();\r\n\t\t\tthis.getgglist();\r\n\t\t},\r\n\t\taddggname:function(e){\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tthis.ggname = '';\r\n\t\t\tthis.ggindex = ggindex;\r\n\t\t\tthis.ggindex2 = -1;\r\n\t\t\tthis.$refs.dialogInput.open();\r\n\t\t},\r\n\t\tdelggname:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tvar ggindex2 = e.currentTarget.dataset.index2;\r\n\t\t\tvar k = e.currentTarget.dataset.k;\r\n\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: [ '修改','删除'],\r\n        success: function (res) {\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\tthat.ggindex2 = ggindex2;\r\n\t\t\t\t\t\t\tthat.$refs.dialogInput.open();return;\r\n\t\t\t\t\t\t}else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\tfor(var i in guigedata){\r\n\t\t\t\t\t\t\t\tif(i == ggindex){\r\n\t\t\t\t\t\t\t\t\tvar newitems = [];\r\n\t\t\t\t\t\t\t\t\tvar index2 = 0;\r\n\t\t\t\t\t\t\t\t\tfor(var j in guigedata[i].items){\r\n\t\t\t\t\t\t\t\t\t\tif(j!=ggindex2){\r\n\t\t\t\t\t\t\t\t\t\t\tnewitems.push({k:index2,title:guigedata[i].items[j].title});\r\n\t\t\t\t\t\t\t\t\t\t\tindex2++;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tguigedata[i].items = newitems;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\tconsole.log(newguigedata)\r\n\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetggname:function(done,val){\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar ggindex = this.ggindex;\r\n\t\t\tvar ggindex2 = this.ggindex2;\r\n\t\t\tif(ggindex2 == -1){ //新增规格名称\r\n\t\t\t\tvar items = guigedata[ggindex].items;\r\n\t\t\t\tggindex2 = items.length;\r\n\t\t\t\titems.push({k:ggindex2,title:val});\r\n\t\t\t\tguigedata[ggindex].items = items;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}else{ //修改规格名称\r\n\t\t\t\tguigedata[ggindex].items[ggindex2].title = val;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}\r\n\t\t\tthis.$refs.dialogInput.close();\r\n\t\t\tthis.getgglist();\r\n\t\t},\r\n\t\tcidsChange:function(e){\r\n\t\t\tvar clist = this.clist;\r\n\t\t\tvar cids = this.cids;\r\n\t\t\tvar cid = e.currentTarget.dataset.id;\r\n\t\t\tthis.cids = [cid];\r\n\t\t\tthis.getcnames();\r\n\t\t},\r\n\t\tgetcnames:function(){\r\n\t\t\tvar cateArr = this.cateArr;\r\n\t\t\tvar cids = this.cids;\r\n\t\t\tconsole.log(cids)\r\n\t\t\tvar cnames = [];\r\n\t\t\tfor(var i in cids){\r\n\t\t\t\tcnames.push(cateArr[cids[i]]);\r\n\t\t\t}\r\n\t\t\tthis.cnames = cnames.join(',');\r\n\t\t},\r\n\t\tchangeClistDialog:function(){\r\n\t\t\tthis.clistshow = !this.clistshow\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\r\n\t\t\tif(!pernum) pernum = 1;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tif(field == 'pic') that.pic = pics;\r\n\t\t\t\tif(field == 'pics') that.pics = pics;\r\n\t\t\t},pernum);\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tif(field == 'pic'){\r\n\t\t\t\tvar pics = that.pic\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pic = pics;\r\n\t\t\t}else if(field == 'pics'){\r\n\t\t\t\tvar pics = that.pics\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pics = pics;\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadimg2:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tthat.gglist[index].pic = urls[0];\r\n\t\t\t},1);\r\n\t\t},\r\n\t\tremoveimg2:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tthat.gglist[index].pic = '';\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item .f1{color:#222;width:200rpx;flex-shrink:0}\r\n.form-item .f2{display:flex;align-items:center}\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.form-item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\n\r\n.ggtitle{height:60rpx;line-height:60rpx;color:#111;font-weight:bold;font-size:26rpx;display:flex;border-bottom:1px solid #f4f4f4}\r\n.ggtitle .t1{width:200rpx;}\r\n.ggcontent{line-height:60rpx;margin-top:10rpx;color:#111;font-size:26rpx;display:flex}\r\n.ggcontent .t1{width:200rpx;display:flex;align-items:center;flex-shrink:0}\r\n.ggcontent .t1 .edit{width:40rpx;height:40rpx}\r\n.ggcontent .t2{display:flex;flex-wrap:wrap;align-items:center}\r\n.ggcontent .ggname{background:#f55;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-bottom:10rpx;font-size:24rpx;position:relative}\r\n.ggcontent .ggname .close{position:absolute;top:-14rpx;right:-14rpx;background:#fff;height:28rpx;width:28rpx;border-radius:14rpx}\r\n.ggcontent .ggnameadd{background:#ccc;font-size:36rpx;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-left:10rpx;position:relative}\r\n.ggcontent .ggadd{font-size:26rpx;color:#558}\r\n\r\n.ggbox{line-height:50rpx;}\r\n\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n\r\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.radio .radio-img{width:100%;height:100%;display:block}\r\n\r\n.freightitem{width:100%;height:60rpx;display:flex;align-items:center;margin-left:40rpx}\r\n.freightitem .f1{color:#666;flex:1}\r\n\r\n.detailop{display:flex;line-height:60rpx}\r\n.detailop .btn{border:1px solid #ccc;margin-right:10rpx;padding:0 16rpx;color:#222;border-radius:10rpx}\r\n.detaildp{position:relative;line-height:50rpx}\r\n.detaildp .op{width:100%;display:flex;justify-content:flex-end;font-size:24rpx;height:60rpx;line-height:60rpx;margin-top:10rpx}\r\n.detaildp .op .btn{background:rgba(0,0,0,0.4);margin-right:10rpx;padding:0 10rpx;color:#fff}\r\n.detaildp .detailbox{border:2px dashed #00a0e9}\r\n\r\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\r\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\r\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\r\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\r\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\r\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\r\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\r\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\r\n.uni-dialog-button-text {font-size: 14px;}\r\n.uni-button-color {color: #007aff;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839433335\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}