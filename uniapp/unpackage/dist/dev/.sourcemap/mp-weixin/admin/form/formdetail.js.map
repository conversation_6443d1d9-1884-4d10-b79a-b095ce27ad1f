{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/form/formdetail.vue?2986", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/form/formdetail.vue?c60f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/form/formdetail.vue?7a36", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/form/formdetail.vue?6072", "uni-app:///admin/form/formdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/form/formdetail.vue?b5c1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/form/formdetail.vue?bd3e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "detail", "formcontent", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "setst", "st", "setTimeout", "setst2", "setst2confirm", "reason", "del", "download", "uni", "url", "success", "filePath", "showMenu", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4F51B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;;MAGAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACAF;QACAA;UAAAC;UAAAE;QAAA;UACAH;UACAI;YACAL;UACA;QACA;MACA;IACA;IACAM;MACA;IACA;IACAC;MACA;MACA;MACAN;QAAAC;QAAAE;QAAAI;MAAA;QACAP;QACAI;UACAL;QACA;MACA;IACA;IACAS;MACA;MACA;MACAR;QACAA;UAAAC;QAAA;UACAD;UACAI;YACAJ;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MAMAC;QACAC;QACAC;UACA;UACA;YACAF;cACAG;cACAC;cACAF;gBACAG;cACA;YACA;UACA;QACA;MACA;IAEA;EACA;;;;;;;;;;;;;;;AC5LA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/form/formdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/form/formdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./formdetail.vue?vue&type=template&id=2e634ce2&\"\nvar renderjs\nimport script from \"./formdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./formdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./formdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/form/formdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=template&id=2e634ce2&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">提交人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">标题</text>\n\t\t\t\t<text class=\"t2\">{{detail.title}}</text>\n\t\t\t</view>\n\t\t\t<view v-for=\"(item, index) in formcontent\" :key=\"index\" class=\"item\">\n\t\t\t\t<text class=\"t1\">{{item.val1}}</text>\n\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload'\"><image :src=\"detail['form'+index]\" style=\"width:50px\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"detail['form'+index]\"></image></view>\r\n        <!-- #ifdef !H5 && !MP-WEIXIN -->\r\n        <view class=\"t2\"v-else-if=\"item.key=='upload_file'\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n        \t\t{{detail['form'+index]}}\r\n        </view>\r\n        <!-- #endif -->\r\n        <!-- #ifdef H5 || MP-WEIXIN -->\r\n        <view class=\"t2\"v-else-if=\"item.key=='upload_file'\"  @tap=\"download\" :data-file=\"detail['form'+index]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n        \t\t点击下载查看\r\n        </view>\r\n        <!-- #endif -->\r\n        <view class=\"t2\" v-else-if=\"item.key=='upload_video'\"><video :src=\"detail['form'+index]\"  style=\"width:80%;height:300rpx;margin-top:20rpx\"></video></view>\r\n        <view class=\"t2\"v-else-if=\"item.key=='upload_pics'\">\r\n          <block v-for=\"(item2,index2) in detail['form'+index]\" :key=\"index2\">\r\n            <image :src=\"item2\" style=\"width:50px;margin-right: 10rpx;\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item2\"></image>\r\n          </block>\r\n        </view>\r\n        <text class=\"t2\" v-else>{{detail['form'+index]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">提交时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">审核状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && (!detail.payorderid||detail.paystatus==1)\" style=\"color:#88e\">待确认</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && detail.payorderid && detail.paystatus==0\" style=\"color:red\">待支付</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\" style=\"color:green\">已确认</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\" style=\"color:red\">已驳回</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==2\">\n\t\t\t\t<text class=\"t1\">驳回原因</text>\n\t\t\t\t<text class=\"t2\" style=\"color:red\">{{detail.reason}}</text>\n\t\t\t</view>\n\t\t\t<block v-if=\"form.payset==1\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款金额</text>\n\t\t\t\t<text class=\"t2\" style=\"font-size:32rpx;color:#e94745\">￥{{detail.money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==0\" style=\"color:green\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==1\" style=\"color:red\">已退款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==0\" style=\"color:red\">未付款</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.paystatus>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">付款时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<view style=\"width:100%;height:160rpx\"></view>\n\t\t<view class=\"bottom notabbarbot\">\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"setst\" :data-st=\"1\" :data-id=\"detail.id\">确认</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"setst2\" :data-st=\"2\" :data-id=\"detail.id\">驳回</view>\n\t\t\t<view class=\"btn2\" @tap=\"del\" :data-id=\"detail.id\">删除</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetst2\" ref=\"dialogSetst2\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"驳回原因\" :value=\"detail.reason\" placeholder=\"请输入驳回原因\" @confirm=\"setst2confirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tdetail:{},\n\t\t\tformcontent:[],\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminForm/formdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.form = res.form;\n\t\t\t\tthat.formcontent = res.formcontent;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tsetst:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tvar st = e.currentTarget.dataset.st;\n\t\t\tapp.confirm('确定要'+(st==2?'驳回':'确认')+'吗?',function(){\n\t\t\t\tapp.post('ApiAdminForm/formsetst', {id:id,st:st}, function (res) {\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t},1000);\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tsetst2:function(e){\n\t\t\tthis.$refs.dialogSetst2.open();\n\t\t},\n\t\tsetst2confirm:function(done,value){\n\t\t\tthis.$refs.dialogSetst2.close();\n      var that = this;\n      app.post('ApiAdminForm/formsetst', {id: that.opt.id,st:2,reason:value}, function (data) {\n        app.success(data.msg);\n        setTimeout(function () {\n          that.getdata();\n        }, 1000);\n      });\n\t\t},\n\t\tdel:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tapp.confirm('确定要删除吗?',function(){\n\t\t\t\tapp.post('ApiAdminForm/formdel', {id:id}, function (res) {\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto('/admin/index/index');\n\t\t\t\t\t},1000);\n\t\t\t\t})\n\t\t\t});\n\t\t},\r\n    download:function(e){\r\n        var that = this;\r\n        var file = e.currentTarget.dataset.file;\r\n        // #ifdef H5\r\n            window.location.href= file;\r\n        // #endif\r\n        \r\n        // #ifdef MP-WEIXIN\r\n        uni.downloadFile({\r\n        \turl: file, \r\n        \tsuccess: (res) => {\r\n                var filePath = res.tempFilePath;\r\n        \t\tif (res.statusCode === 200) {\r\n        \t\t\tuni.openDocument({\r\n                      filePath: filePath,\r\n                      showMenu: true,\r\n                      success: function (res) {\r\n                        console.log('打开文档成功');\r\n                      }\r\n                    });\r\n        \t\t}\r\n        \t}\r\n        });\r\n        // #endif\r\n    },\n  }\n};\r\n</script>\r\n<style>\r\n\r\n.orderinfo{ width:100%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%;height:92rpx;padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839437881\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}