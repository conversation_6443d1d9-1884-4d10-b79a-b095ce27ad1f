{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/myupload.vue?6669", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/myupload.vue?8416", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/myupload.vue?5528", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/myupload.vue?ca3f", "uni-app:///admin/shortvideo/myupload.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/myupload.vue?5185", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/myupload.vue?275c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "st", "datalist", "pagenum", "nomore", "count0", "count1", "countall", "sclist", "keyword", "nodata", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "changetab", "that", "getdata", "app", "todel", "id", "setst"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqC11B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;QACAT;MACA;QACAQ;MACA;MACAA;MACAA;MACAA;MACAE;QAAAV;QAAAN;QAAAF;MAAA;QACAgB;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACAD;QACAA;UAAAE;QAAA;UACA;YACAF;YACAF;UACA;YACAE;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACA;MACAH;QACAA;UAAAlB;UAAAoB;QAAA;UACA;YACAF;YACAF;UACA;YACAE;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/shortvideo/myupload.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/shortvideo/myupload.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myupload.vue?vue&type=template&id=23e45a5f&\"\nvar renderjs\nimport script from \"./myupload.vue?vue&type=script&lang=js&\"\nexport * from \"./myupload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myupload.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/shortvideo/myupload.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myupload.vue?vue&type=template&id=23e45a5f&\"", "var components\ntry {\n  components = {\n    ddSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-search/dd-search\" */ \"@/components/dd-search/dd-search.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1rgb\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myupload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myupload.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<dd-search :isfixed=\"true\" @getdata=\"getdata\"></dd-search>\n\n\t<view class=\"container\" id=\"datalist\">\n\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t<view class=\"order-box\">\n\t\t\t<view class=\"content\" style=\"border-bottom:none\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'/activity/shortvideo/detail?id=' + item.id\">\n\t\t\t\t\t<image :src=\"item.coverimg\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t<view class=\"t2\">播放量 {{item.view_num}}<text style=\"color:#a88;padding-left:20rpx\">点赞数 {{item.zan_num}}</text></view>\n\t\t\t\t\t<text class=\"t3\" v-if=\"item.status==2\">驳回原因：{{item.reason}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"op\">\n\t\t\t\t<text style=\"color:orange\" class=\"flex1\" v-if=\"item.status==0\">待审核</text>\n\t\t\t\t<text style=\"color:red\" class=\"flex1\" v-if=\"item.status==2\">未通过</text>\n\t\t\t\t<text style=\"color:green\" class=\"flex1\" v-if=\"item.status==1\">已通过</text>\n\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"item.id\">删除</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t</view>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<nodata v-if=\"nodata\"></nodata>\n\t\n\t<view style=\"height:140rpx\"></view>\n\t<view class=\"btn-add\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot3'\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" @tap=\"goto\" data-url=\"uploadvideo\"><image :src=\"pre_url+'/static/img/add.png'\" style=\"width:28rpx;height:28rpx;margin-right:6rpx\"/>发布短视频</view>\n\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n      count0: 0,\n      count1: 0,\n      countall: 0,\n      sclist: \"\",\n\t\t\tkeyword: '',\n      nodata: false,\n      pre_url:app.globalData.pre_url,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    changetab: function (st) {\n      var that = this;\n      that.st = st;\n      that.getdata();\n    },\n    getdata: function (loadmore,keyword) {\n     if(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n\t\t\tif(typeof keyword =='undefined') {\n\t\t\t\tkeyword = that.keyword;\n\t\t\t} else {\n\t\t\t\tthat.keyword = keyword;\n\t\t\t}\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\tthat.loading = true;\n      app.post('ApiAdminShortvideo/myupload', {keyword:keyword,pagenum: pagenum,st: that.st}, function (res) {\n        that.loading = false;\n        var data = res.datalist;\n        if (pagenum == 1){\n\t\t\t\t\tthat.countall = res.countall;\n\t\t\t\t\tthat.count0 = res.count0;\n\t\t\t\t\tthat.count1 = res.count1;\n\t\t\t\t\tthat.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    todel: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该短视频吗?', function () {\n        app.post('ApiAdminShortvideo/myuploaddel', {id: id}, function (res) {\n          if (res.status == 1) {\n            app.success(res.msg);\n            that.getdata();\n          } else {\n            app.error(res.msg);\n          }\n        });\n      });\n    },\n    setst: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      app.confirm('确定要' + (st == 0 ? '下架' : '上架') + '吗?', function () {\n        app.post('ApiAdminShortvideo/setst', {st: st,id: id}, function (res) {\n          if (res.status == 1) {\n            app.success(res.msg);\n            that.getdata();\n          } else {\n            app.error(res.msg);\n          }\n        });\n      });\n    }\n  }\n};\n</script>\n<style>\n.container{ width:100%;margin-top:106rpx}\n.order-box{ width: 94%;margin:0 3%;padding:3px 3%; background: #fff; margin-bottom:12rpx;border-radius:16rpx}\n\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;position:relative}\n.order-box .content:last-child{ border-bottom: 0; }\n.order-box .content image{ width: 140rpx; height: 140rpx;}\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.order-box .content .detail .t1{font-size:26rpx;min-height:50rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.order-box .content .detail .t2{height:36rpx;line-height:36rpx;color: #999;overflow: hidden;font-size: 24rpx;}\n.order-box .content .detail .t3{display:flex;height: 36rpx;line-height: 36rpx;color: #ff4246;font-size: 24rpx;}\n.order-box .content .detail .x1{ font-size:30rpx;margin-right:5px}\n.order-box .content .detail .x2{ font-size:24rpx;text-decoration:line-through;color:#999}\n\n.order-box .bottom{ width:100%; padding:10rpx 0px; border-top: 1px #e5e5e5 solid; color: #555;}\n.order-box .op{ display:flex;align-items:center;width:100%; padding:10rpx 0px; border-top: 1px #e5e5e5 solid; color: #555;}\n.btn1{margin-left:20rpx;width:120rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:120rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:0;margin-bottom:20rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myupload.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myupload.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839440111\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}