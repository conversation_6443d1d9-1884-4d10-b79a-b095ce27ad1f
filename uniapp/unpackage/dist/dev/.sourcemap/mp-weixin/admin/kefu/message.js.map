{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/kefu/message.vue?4415", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/kefu/message.vue?5b7d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/kefu/message.vue?e909", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/kefu/message.vue?f949", "uni-app:///admin/kefu/message.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/kefu/message.vue?fe2b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/kefu/message.vue?60ce"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "isload", "loading", "token", "uid", "nowtime", "pagenum", "datalist", "message", "trimMessage", "faceshow", "nomore", "keyword", "pre_url", "onLoad", "onUnload", "clearInterval", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "getdata", "that", "app", "mid", "uni", "title", "interval0", "updateMessageTime", "console", "prevtime", "getdatalist", "sendMessage", "icon", "duration", "aid", "umid", "msgtype", "content", "platform", "type", "sendimg", "receiveMessage", "setTimeout", "toggleFaceBox", "scrollToBottom", "scrollTop", "onInputFocus", "onPageScroll", "messageChange", "transformMsgHtml", "selectface", "getTime", "todaystart", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkDz1B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAG;UACAC;QACA;QACAJ;QACAA;QAEAK;UACAL;QACA;MACA;IACA;IACAM;MACA;MACA;MACAC;MACA;QACA;QACA;QACA;UACAC;QACA;QACA;UACAvB;QACA;UACAA;QACA;MACA;MACAsB;MACA;IACA;IACAE;MACA;MACA;MACAT;MACAC;QAAAC;QAAAlB;QAAAM;MAAA;QACAU;QACA;QACA;UACA;YACAf;UACA;UACAe;UACAA;UACA;YACAA;UACA,QAEA;UACAA;QACA;UACAA;QACA;MACA;IACA;IACAU;MACA;MACA;MACA;QACAP;UACAC;UACAO;UACAC;QACA;MACA;QACA;UACA;YACAC;YACAX;YACAY;YACAhC;YACAiC;YACAC;YACAC;YACA1B;UACA;UACAU;YACAiB;YACArC;YACAJ;UACA;UACAuB;UACAA;UACAA;QACA;MACA;IACA;IACAmB;MACA;MACAlB;QACA;UACA;UACA;YACAY;YACAX;YACAY;YACAhC;YACAiC;YACAC;YACAC;YACA1B;UACA;UACAU;YACAiB;YACArC;YACAJ;UACA;QACA;MACA;IACA;IACA2C;MACA;MACA;QACA;QACAlC;QACAc;QACAqB;QACA;QACApB;UAAAC;QAAA;QACA;MACA;QACA;MACA;IACA;IACAoB;MACA;IACA;IACAC;MACA;MACAF;QACAlB;UACAqB;UACAZ;QACA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACAZ;MACA;MACA;QACAA;MACA;MACA;IACA;IACAa;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAC;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzRA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/kefu/message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/kefu/message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./message.vue?vue&type=template&id=2b651fef&\"\nvar renderjs\nimport script from \"./message.vue?vue&type=script&lang=js&\"\nexport * from \"./message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./message.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/kefu/message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=2b651fef&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    wxface: function () {\n      return import(\n        /* webpackChunkName: \"components/wxface/wxface\" */ \"@/components/wxface/wxface.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"message-list\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view class=\"message-time\" v-if=\"item.formatTime\">{{item.formatTime}}</view>\n\t\t\t\t<view class=\"message-item\" v-if=\"item.isreply==0\">\n\t\t\t\t\t<image class=\"message-avatar\" mode=\"aspectFill\" :src=\"item.headimg\"></image>\n\t\t\t\t\t<view class=\"message-text-left\">\n\t\t\t\t\t\t<view class=\"arrow-box arrow-left\">\n\t\t\t\t\t\t\t<image class=\"arrow-icon\" :src=\"pre_url+'/static/img/arrow-white.png'\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"message-text\">\n\t\t\t\t\t\t\t<parse :content=\"item.content\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"message-item\" style=\"justify-content:flex-end\" v-else>\n\t\t\t\t\t<view class=\"message-text-right\">\n\t\t\t\t\t\t<view class=\"arrow-box arrow-right\">\n\t\t\t\t\t\t\t<image class=\"arrow-icon\" :src=\"pre_url+'/static/img/arrow-green.png'\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"message-text\">\n\t\t\t\t\t\t\t<parse :content=\"item.content\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<image class=\"message-avatar\" mode=\"aspectFill\" :src=\"item.uheadimg\"></image>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<view class=\"input-box notabbarbot\" id=\"input-box\">\n\t\t\t<view class=\"input-form\">\n\t\t\t\t<image @tap=\"sendimg\" class=\"pic-icon\" :src=\"pre_url+'/static/img/msg-pic.png'\"></image>\n\t\t\t\t<input @confirm=\"sendMessage\" @focus=\"onInputFocus\" @input=\"messageChange\" class=\"input\" :confirmHold=\"true\" confirmType=\"send\" cursorSpacing=\"20\" type=\"text\" :value=\"message\" maxlength=\"-1\"/>\n\t\t\t\t<image @tap=\"toggleFaceBox\" class=\"face-icon\" :src=\"pre_url+'/static/img/face-icon.png'\"></image>\n\t\t\t\t<button class=\"send-button\" v-if=\"!trimMessage\">\n\t\t\t\t\t发送\n\t\t\t\t</button>\n\t\t\t\t<button @tap=\"sendMessage\" class=\"send-button-active\" v-if=\"trimMessage\">\n\t\t\t\t\t发送\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t\t<wxface v-if=\"faceshow\" @selectface=\"selectface\"></wxface>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n<script>\nvar app = getApp();\nvar interval0;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n      isload: false,\n      loading: false,\n\t\t\t\n\t\t\ttoken:'',\n\t\t\tuid:0,\n\t\t\tnowtime:'',\n      pagenum: 1,\n      datalist: [],\n      message: \"\",\n      trimMessage: \"\",\n      faceshow: false,\n      nomore: false,\n\t\t\tkeyword:'',\n      pre_url:app.globalData.pre_url\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.mid = this.opt.mid;\n\t\tthis.getdata();\n  },\n  onUnload: function () {\n    clearInterval(interval0);\n  },\n\tonNavigationBarSearchInputConfirmed:function(e){\n\t\tthis.searchConfirm({detail:{value:e.text}});\n\t},\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminKefu/message',{mid:that.mid},function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.token = res.token;\n\t\t\t\tthat.uid = res.uid;\n\t\t\t\tthat.nowtime = res.nowtime;\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: res.tomember.nickname\n\t\t\t\t});\n\t\t\t\tthat.getdatalist();\n\t\t\t\tthat.loaded();\n\n\t\t\t\tinterval0 = setInterval(function () {\n\t\t\t\t\tthat.nowtime++;\n\t\t\t\t}, 1000);\n\t\t\t});\n\t\t},\n    updateMessageTime: function () {\n      var that = this;\n      var datalist = this.datalist;\n\t\t\tconsole.log(datalist)\n\t\t\tfor(var i in datalist){\n        var thistime = parseInt(datalist[i].createtime);\n        var prevtime = 0;\n        if (i > 0) {\n          prevtime = parseInt(datalist[i - 1].createtime);\n        }\n        if (thistime - prevtime > 600) {\n          datalist[i].formatTime = that.getTime(thistime);\n        } else {\n          datalist[i].formatTime = '';\n        }\n      }\n\t\t\tconsole.log(datalist)\n      this.datalist = datalist;\n    },\n    getdatalist: function () {\n      var that = this;\n      if (that.loading) return;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiAdminKefu/getmessagelist',{mid:that.mid,pagenum: that.pagenum,keyword:that.keyword}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar datalist = res.data;\n\t\t\t\tif (datalist.length > 0) {\n\t\t\t\t\tfor (var i in datalist) {\n\t\t\t\t\t\tdatalist[i].content = that.transformMsgHtml(datalist[i].msgtype, datalist[i].content);\n\t\t\t\t\t}\n\t\t\t\t\tthat.datalist = datalist.concat(that.datalist);\n\t\t\t\t\tthat.updateMessageTime();\n\t\t\t\t\tif (that.pagenum == 1) {\n\t\t\t\t\t\tthat.scrollToBottom();\n\t\t\t\t\t} else {\n\n\t\t\t\t\t}\n\t\t\t\t\tthat.pagenum = that.pagenum + 1;\n\t\t\t\t}else{\n\t\t\t\t\tthat.nomore = true;\n\t\t\t\t}\n\t\t\t});\n    },\n    sendMessage: function (e) {\n      var that = this;\n      var message = this.message;\n      if (message.length > 2000) {\n        uni.showToast({\n          title: \"单条消息不能超过2000字\",\n          icon: \"none\",\n          duration: 1000\n        });\n      } else {\n        if (message.replace(/^\\s*|\\s*$/g, \"\")) {\n          var msgdata = {\n            aid: app.globalData.aid,\n            mid: that.mid,\n\t\t\t\t\t\tumid:app.globalData.mid,\n            uid: that.uid,\n            msgtype: 'text',\n            content: message,\n\t\t\t\t\t\tplatform:app.globalData.platform,\n\t\t\t\t\t\tpre_url:app.globalData.pre_url\n          };\n          app.sendSocketMessage({\n            type: 'tokehu',\n            token: that.token,\n            data: msgdata\n          });\n          that.message = \"\";\n          that.trimMessage = \"\";\n          that.faceshow = false\n        }\n      }\n    },\n    sendimg: function () {\n      var that = this;\n      app.chooseImage(function (data) {\n        for (var i = 0; i < data.length; i++) {\n          var message = data[i];\n          var msgdata = {\n            aid: app.globalData.aid,\n            mid: that.mid,\n\t\t\t\t\t\tumid:app.globalData.mid,\n            uid: that.uid,\n            msgtype: 'image',\n            content: message,\n\t\t\t\t\t\tplatform:app.globalData.platform,\n\t\t\t\t\t\tpre_url:app.globalData.pre_url\n          };\n          app.sendSocketMessage({\n            type: 'tokehu',\n            token: that.token,\n            data: msgdata\n          });\n        }\n      }, 3);\n    },\n    receiveMessage: function (data) {\n\t\t\tvar that = this;\n\t\t\tif((data.type == 'tokefu' && that.mid == data.data.mid) || (data.type=='tokehu' && that.mid == data.data.mid)){\n\t\t\t\tvar message = data.data\n\t\t\t\tmessage.content = that.transformMsgHtml(message.msgtype, message.content);\n\t\t\t\tthat.datalist = that.datalist.concat([message]);\n\t\t\t\tsetTimeout(that.updateMessageTime, 100);\n\t\t\t\tthis.scrollToBottom();\n\t\t\t\tapp.post('ApiKefu/isread',{mid:that.mid});\n\t\t\t\treturn true;\n\t\t\t}else{\n\t\t\t\treturn false;\n\t\t\t}\n    },\n    toggleFaceBox: function () {\n      this.faceshow = !this.faceshow\n    },\n    scrollToBottom: function () {\n      var that = this;\n      setTimeout(function () {\n\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\tscrollTop: 10000,\n\t\t\t\t\tduration:0\n\t\t\t\t});\n      },300);\n    },\n    onInputFocus: function (e) {\n      this.faceshow = false\n    },\n\t\tonPageScroll: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar scrollY = e.scrollTop;     \n\t\t\tif (scrollY == 0 && !that.nomore) {\n\t\t\t\tthis.getdatalist();\n\t\t\t}\n\t\t},\n    messageChange: function (e) {\n      this.message = e.detail.value;\n      this.trimMessage = e.detail.value.trim();\n    },\n    transformMsgHtml: function (msgtype, content) {\n      if (msgtype == 'miniprogrampage') {\n        var contentdata = JSON.parse(content);\n        content = '<div style=\"font-size:16px;font-weight:bold;height:25px;line-height:25px\">' + contentdata.Title + '</div><img src=\"' + contentdata.ThumbUrl + '\" style=\"width:400rpx\"/>';\n      }\n      if (msgtype == 'image') {\n        content = '<img src=\"' + content + '\" style=\"width:400rpx\"/>';\n      }\n      return content;\n    },\n    selectface: function (face) {\n      this.message = \"\" + this.message + face;\n\t\t\tthis.trimMessage = this.message.trim();\n    },\n    getTime: function (createtime) {\n      var t = this.nowtime - createtime;\n      if (t > 0) {\n        var todaystart = new Date(this.dateFormat(this.nowtime, \"Y-m-d 00:00:00\")).getTime();\n        todaystart = todaystart / 1000;\n        var lastdaystart = todaystart - 86400;\n        if (t <= 180) {\n          return '刚刚';\n        }\n        if (createtime > todaystart) {\n          return this.dateFormat(createtime, \"H:i\");\n        }\n        if (createtime > lastdaystart) {\n          return '昨天' + this.dateFormat(createtime, \"H:i\");\n        }\n        return this.dateFormat(createtime, 'Y年m月d日 H:i:s');\n      }\n    },\n\t\tsearchConfirm:function(e){\n\t\t\tthis.keyword = e.detail.value;\n\t\t\tthis.pagenum = 1;\n\t\t\tthis.datalist = [];\n      this.getdatalist();\n\t\t}\n  }\n};\n</script>\n<style>\n.container{display: block;min-height: 100%;\tbox-sizing: border-box;\tbackground: #f4f4f4;color: #222;}\n.message-list {\tpadding-left: 25rpx;\tpadding-right: 25rpx;\tpadding-bottom: 20rpx;padding-bottom:100rpx;}\n.message-item {display:flex;padding:20rpx 0}\n.message-time {\twidth:100%;padding-top: 20rpx;\tpadding-bottom: 10rpx;\ttext-align: center;display: inline-block;\tcolor: #999;\tfont-size: 24rpx;}\n.message-avatar {\twidth: 90rpx;\theight: 90rpx;\tborder-radius: 50%;}\n.message-text {\tmax-width: 525rpx;\tmin-height: 64rpx;\tline-height: 50rpx;\tfont-size: 30rpx;\tpadding: 20rpx 30rpx;word-break:break-all}\n.message-text-left {\tposition: relative;\tbackground-color: #fff;\tmargin-left: 20rpx;\tborder-radius: 12rpx;\tborder: 1rpx solid #dddddd;}\n.message-text-right {\tposition: relative;\tbackground-color: #9AE966;\tmargin-right: 20rpx;\tborder-radius: 12rpx;\tborder: 1rpx solid #6DBF58;}\n.arrow-box {\tposition: absolute;\twidth: 16rpx;\theight: 24rpx;\ttop: 35rpx;}\n.arrow-left {left: -14rpx;}\n.arrow-right {right: -14rpx;}\n.arrow-icon {\tdisplay: block;\twidth: 100%;\theight: 100%;}\n\n.input-box {\tposition: fixed;\tz-index: 100;\tbottom: 0;\twidth: 96%;\tmin-height: 100rpx;\tpadding: 15rpx 2%;\tbackground-color: #fff;box-sizing:content-box}\n.input-form {\twidth: 100%;\theight: 100%;\tdisplay: flex;\tflex-direction: row;\talign-items: center;}\n.input {\tflex: 1;\theight: 66rpx;\tborder: 1rpx solid #ddd;\tpadding: 5rpx 10rpx;\tbackground-color: #fff;\tfont-size: 30rpx;\tborder-radius: 12rpx;}\n.pic-icon {\twidth: 54rpx;\theight: 54rpx;\tmargin-right: 18rpx;}\n.face-icon {\twidth: 60rpx;\theight: 60rpx;\tmargin-left: 18rpx;}\n.faces-box {\twidth: 100%;\theight: 500rpx;}\n.single-face {\twidth: 48rpx;\theight: 48rpx;\tmargin: 10rpx;}\n.send-button {\twidth: 100rpx;\theight: 62rpx;\tmargin-left: 18rpx;\tborder-radius: 8rpx;\ttext-align: center;\tline-height: 62rpx;\tfont-size: 28rpx;\tbackground-color: #dcdcdc;\tborder: 1rpx solid #ccc;\tcolor: #999;}\n.send-button-active {\twidth: 100rpx;\theight: 62rpx;\tmargin-left: 20rpx;\tborder-radius: 8rpx;\ttext-align: center;\tline-height: 62rpx;\tfont-size: 28rpx;\tbackground-color: #2396FC;\tborder: 1rpx solid #1F88E5;\tcolor: #fff;}\n.send-icon {\twidth: 56rpx;\theight: 56rpx;}\n\n.anit{width: 100%;height: 70rpx;background:#555;position: absolute;color:#fff;font-size: 30rpx;line-height: 70rpx;top: -70rpx;text-align: left;padding:0 20rpx;overflow:hidden}\n.show{top: 0rpx;animation: show 0.2s;animation-timing-function:ease;}\n@keyframes show{from {top:-70rpx;}to {top:0rpx;}}\n.hide{top: -70rpx;animation: hide 0.2s;animation-timing-function:ease;}\n@keyframes hide{from {top:0rpx;}to {top:-70rpx;}}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839442622\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}