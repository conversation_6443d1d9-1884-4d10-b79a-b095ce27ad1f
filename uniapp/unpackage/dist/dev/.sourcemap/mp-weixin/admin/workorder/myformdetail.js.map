{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/myformdetail.vue?cc15", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/myformdetail.vue?b206", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/myformdetail.vue?e814", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/myformdetail.vue?1f7e", "uni-app:///admin/workorder/myformdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/myformdetail.vue?24ef", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/myformdetail.vue?e413"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "detail", "formcontent", "showstatus", "ishowjindu", "jdlist", "pre_url", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "getliucheng", "setst", "close", "del", "setTimeout", "jindu", "<PERSON>jd"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4H91B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACAF;QACA;QACAD;MAEA;IACA;IACAI;MACA;MACA;MACAJ;MACAA;IACA;IACAK;MACA;MACAL;IACA;IAGAM;MACA;MACA;MACAL;QACAA;UAAAC;QAAA;UACAD;UACAM;YACAN;UACA;QACA;MACA;IACA;IACAO;MACA;MACAR;MACA;MACA;MACAC;QAAAC;MAAA;QACA;UACA;UACAF;QACA;MAEA;IACA;IACAS;MACA;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnNA;AAAA;AAAA;AAAA;AAAurC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/workorder/myformdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/workorder/myformdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myformdetail.vue?vue&type=template&id=f4159afe&\"\nvar renderjs\nimport script from \"./myformdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./myformdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myformdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/workorder/myformdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myformdetail.vue?vue&type=template&id=f4159afe&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var g0 = _vm.ishowjindu ? _vm.jdlist.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myformdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myformdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">提交人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">标题</text>\n\t\t\t\t<text class=\"t2\">{{detail.title}}</text>\n\t\t\t</view>\n\t\t\t<view v-for=\"(item, index) in formcontent\" :key=\"index\" class=\"item\">\n\t\t\t\t<text class=\"t1\">{{item.val1}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"item.key!='upload'\">{{detail['form'+index]}}</text>\n\t\t\t\t<view class=\"t2\" style=\"display: flex; justify-content: flex-end;\" v-else>\n\t\t\t\t\t<view v-for=\"(sub, indx) in detail['form'+index]\" :key=\"indx\">\n\t\t\t\t\t\t<image :src=\"sub\" style=\"width:50px; margin-left: 10rpx;\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"sub\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">提交时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">审核状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && (!detail.payorderid||detail.paystatus==1)\" style=\"color:#88e\">待处理</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && detail.payorderid && detail.paystatus==0\" style=\"color:red\">待支付</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\" style=\"color:green\">处理中</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\" style=\"color:green\">已完成</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==-1\" style=\"color:red\">已驳回</text>\n\t\t\t\t\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==-1\">\n\t\t\t\t<text class=\"t1\">驳回原因</text>\n\t\t\t\t<text class=\"t2\" style=\"color:red\">{{detail.reason}}</text>\n\t\t\t</view>\n\t\t\t<block v-if=\"form.payset==1\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款金额</text>\n\t\t\t\t<text class=\"t2\" style=\"font-size:32rpx;color:#e94745\">￥{{detail.money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==0\" style=\"color:green\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==1\" style=\"color:red\">已退款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==0\" style=\"color:red\">未付款</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.paystatus>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">付款时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<view style=\"width:100%;height:160rpx\"></view>\n\t\t<view class=\"bottom notabbarbot\">\n\t\t\t<view v-if=\"detail.status>0\" class=\"btn2\" @tap=\"jindu\"  :data-id=\"detail.id\">查看进度</view>\n\t\t\t<view class=\"btn2\" @tap=\"del\" :data-id=\"detail.id\">删除</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetst2\" ref=\"dialogSetst2\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"驳回原因\" :value=\"detail.reason\" placeholder=\"请输入驳回原因\" @confirm=\"setst2confirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t</block>\n\t\n\t<view class=\"modal\" v-if=\"ishowjindu\">\n\t\t<view class=\"modal_jindu\">\n\t\t\t\t<view class=\"close\" @tap=\"closejd\"><image :src=\"pre_url+'/static/img/close.png'\" /></view>\n\t\t\t\t<block v-if=\"jdlist.length>0\">\n\t\t\n\t\t\t\t\t<view class=\"item \" v-for=\"(item,index) in jdlist\" :key=\"index\" style=\"display: flex;\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"'/static/img/jindu' + (index==0?'2':'1') + '.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t<text class=\"t2\"> 时间：{{item.time}}</text>\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.desc}}({{item.remark}}) </text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t\t<view style=\"font-size:14px;color:#f05555;padding:10px;\">等待处理</view>\n\t\t\t\t</block>\n\t\t</view>\n\t</view>\n\t\n\t\n\t<view class=\"modal\" v-if=\"showstatus\">\n\t\t<view class=\"modal_jindu\">\n\t\t\t<form   @submit=\"formsubmit\">\n\t\t\t\t\t<view class=\"close\" @tap=\"close\"><image :src=\"pre_url+'/static/img/close.png'\" /></view>\n\t\t\t\t\t<view class=\"title\">选择流程</view>\n\t\t\t\t\t<view class=\"uni-list\">\n\t\t\t\t\t\t\t<radio-group name=\"liucheng\">\n\t\t\t\t\t\t\t\t\t<label class=\"uni-list-cell uni-list-cell-pd\" v-for=\"(item, index) in lclist\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<radio :value=\"''+item.id\" style=\"transform:scale(0.7)\"/>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t\t<view class=\"beizhu flex\">\n\t\t\t\t\t\t\t\t\t<label>备注:</label><textarea placeholder=\"输入内容\" name=\"content\" maxlength=\"-1\"> </textarea>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<button class=\"btn\" form-type=\"submit\">提交</button>\n\t\t\t</form>\n\t\t</view>\n\t</view>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tdetail:{},\n\t\t\tformcontent:[],\n\t\t\tshowstatus:0,\n\t\t\tishowjindu:false,\n\t\t\tjdlist:[],\n\t\t\tpre_url:app.globalData.pre_url,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n\t\tthis.getliucheng();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminWorkorder/myformdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.form = res.form;\n\t\t\t\tthat.formcontent = res.formcontent;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tgetliucheng:function(e){\n\t\t\tvar that=this\n\t\t\tapp.post('ApiAdminWorkorder/getliucheng', {}, function (res) {\n\t\t\t\t\tvar lclist = res.datalist;\n\t\t\t\t\tthat.lclist = lclist;\n\t\t\t\t\t\n\t\t\t});\n\t\t},\n\t\tsetst:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tthat.id=id\n\t\t\tthat.showstatus=true;\n\t\t},\n\t\tclose:function(e){\n\t\t\tvar that=this\n\t\t\tthat.showstatus=false\n\t\t},\n\t\t\n\n\t\tdel:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tapp.confirm('确定要删除吗?',function(){\n\t\t\t\tapp.post('ApiAdminWorkorder/formdel', {id:id}, function (res) {\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto('/admin/index/index');\n\t\t\t\t\t},1000);\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tjindu:function(e){\n\t\t\tvar that=this\n\t\t\tthat.ishowjindu=true\n\t\t\tvar id = e.currentTarget.dataset.id\n\t\t\t//读取进度表\n\t\t\tapp.post('ApiWorkorder/selectjindu', { id: id }, function (res) {\n\t\t\t\t\tif(res.status==1){\n\t\t\t\t\t\tvar data = res.data\n\t\t\t\t\t\tthat.jdlist =data\n\t\t\t\t\t}\n\t\t\t\t\n\t\t\t})\n\t\t},\n\t\tclosejd:function(e){\n\t\t\tvar that=this\n\t\t\tthat.ishowjindu=false\n\t\t}\n  }\n};\n</script>\n<style>\n\n.orderinfo{ width:100%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%;height:92rpx;padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.modal{ position: fixed; background:rgba(0,0,0,0.3); width: 100%; height: 100%; top:0; z-index: 100;}\n.modal .modal_jindu{ background: #fff; position: absolute; top: 20%; align-items: center; margin: auto; width: 90%; left: 30rpx; border-radius: 10rpx; padding: 40rpx;}\n.modal_jindu .close image { width: 20rpx; height: 20rpx; position: absolute; top:10rpx; right: 20rpx;}\n.modal_jindu .title{ font-size: 32rpx; font-weight: bold;}\n.uni-list{ margin-top: 30rpx;}\n.uni-list-cell{ display: flex; height: 80rpx;}\n.beizhu label{ width: 100rpx;}\n.modal_jindu .btn{  background: #1658c6; border-radius: 3px;line-height: 24px; border: none; padding: 0 10px;color: #fff;font-size: 20px; text-align: center; width: 300px;  display: flex; height: 40px; justify-content: center;align-items: center;}\n\n\n.modal_jindu .item .f1{ width:60rpx;position:relative}\n/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/\n.modal_jindu .item .f1 image{width: 30rpx; height: 100%; position: absolute; left: -16rpx; top: 0rpx;}\n.modal_jindu .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0}\n.modal_jindu .item .f2 .t1{font-size: 30rpx;}\n.modal_jindu .item .f2 .t1{font-size: 26rpx;}\n\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myformdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myformdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839438403\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}