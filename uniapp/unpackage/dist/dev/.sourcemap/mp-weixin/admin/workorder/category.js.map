{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/category.vue?f6b0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/category.vue?a101", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/category.vue?c2e6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/category.vue?d24e", "uni-app:///admin/workorder/category.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/category.vue?704a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/category.vue?524e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pagenum", "nomore", "nodata", "order", "field", "clist", "curIndex", "curIndex2", "datalist", "curCid", "proid", "buydialogShow", "bid", "isshow", "pre_url", "iscate", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "cid", "isget", "getdatalist", "wherefield", "console", "uni", "scrolltolower", "changeCTab", "changeOrder", "switchRightTab", "buydialogChange", "showLinkChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2D11B;AAAA,eACA;EAEAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAV;MAAA;QACAQ;QACA;QACAA;QAEA;UACA;YACA;cACAA;cACAA;YACA;YACA;YACA;YACA;cACA;gBACAA;gBACAA;gBACAG;gBACA;cACA;YACA;YACA;UACA;QACA;QACAH;QACAA;MACA;IACA;IAEAI;MACA;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;MAEA;MACAJ;MACAA;MACAA;MACA;MACAK;MACAA;MACAA;MACAA;MACA;QACAA;MACA;QACAA;MACA;MACAC;MACAL;QACAD;QAEAO;QAEA;QACA;UACA;YACAP;UACA;YACAA;UACA;QACA;QACAA;QACA;QACA;QACAA;MACA;IAEA;IAEAQ;MAEA;QAEA;QACA;MAEA;IAEA;IAEA;;IAEAC;MAEA;MAEA;MACA;MACAH;MACA;MACA;IACA;IAEA;;IAEAI;MAEA;MAEA;MACA;MAEA;MACA;MACA;MAEA;IAEA;IAEA;;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IAEA;IAEAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACAb;MACAA;MACAA;MACAA;MACAA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC9PA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/workorder/category.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/workorder/category.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./category.vue?vue&type=template&id=50d9343e&\"\nvar renderjs\nimport script from \"./category.vue?vue&type=script&lang=js&\"\nexport * from \"./category.vue?vue&type=script&lang=js&\"\nimport style0 from \"./category.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/workorder/category.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category.vue?vue&type=template&id=50d9343e&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.curIndex == index ? _vm.t(\"color1\") : null\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"content-container\">\r\n\t\t\t<view class=\"nav_left\">\r\n\t\t\t\r\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == index ? 'active' : '')\" :style=\"{color:curIndex == index?t('color1'):'#333'}\" @tap=\"switchRightTab\" :data-index=\"index\" :data-id=\"item.id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav_right\">\r\n\t\t\t\t<view class=\"nav_right-content\">\r\n\r\n\t\t\t\t\t<scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\r\n\t\t\t\t\t\t<view class=\"product-itemlist\">\r\n\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,idx2) in datalist\" :key=\"item.id\"  v-if=\"iscate==1\" >\r\n\t\t\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"changeCTab\" :data-id=\"item.id\" :data-index=\"idx2\" ><image :src=\"pre_url+'/static/img/workorder/'+(curIndex2==idx2?'down':'up')+'.png?v1'\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"p1\" @click=\"goto\" :data-url=\"'record?cid='+item.id\" >\r\n\t\t\t\t\t\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"count\" v-if=\"item.count>0\">{{item.count}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"list\" v-if=\"curIndex2==idx2\" v-for=\"(subitem,subindex) in item.list\" @tap=\"goto\" :data-url=\"'record?cateid='+subitem.id\">\r\n\t\t\t\t\t\t\t\t\t<label> {{subitem.name}}</label><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-else class=\"item\" >\r\n\t\t\t\t\t\t\t\t<view class=\"product-info\" >\r\n\t\t\t\t\t\t\t\t\t<view class=\"list\" style=\"width: 100%;position: relative;\" v-for=\"(subitem,subindex) in datalist\" @tap=\"goto\" :data-url=\"'record?cateid='+subitem.id\" >\r\n\t\t\t\t\t\t\t\t\t\t<label> {{subitem.name}}</label><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"count\" style=\"position: absolute;top:10rpx; right: 30rpx\" v-if=\"subitem.count>0\">{{subitem.count}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\r\n\t\t\t\t\t\t<nodata text=\"暂无相关商品\" v-if=\"nodata\"></nodata>\r\n\t\t\t\t\t\t<view style=\"width:100%;height:100rpx\"></view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> \r\n\r\n  \r\n\t</block>\r\n\t<loading v-if=\"loading\" loadstyle=\"left:62.5%\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpagenum: 1,\r\n\t\t\tnomore: false,\r\n\t\t\tnodata: false,\r\n\t\t\torder: '',\r\n\t\t\tfield: '',\r\n\t\t\tclist: [],\r\n\t\t\tcurIndex:0,\r\n\t\t\tcurIndex2: 0,\r\n\t\t\tdatalist: [],\r\n\t\t\tcurCid: 0,\r\n\t\t\tproid:0,\r\n\t\t\tbuydialogShow: false,\r\n\t\t\tbid:'',  \r\n\t\t\tisshow:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tiscate:0\r\n\t\t};\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.bid = this.opt.bid ? this.opt.bid  : '';\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar nowcid = that.opt.cid;\r\n\t\t\tif (!nowcid) nowcid = '';\r\n\t\t\tthat.pagenum = 1;\r\n\t\t\tthat.datalist = [];\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminWorkorder/getclassify', {cid:nowcid,bid:that.bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t  var clist = res.data;\r\n\t\t\t  that.clist = clist;\r\n\r\n\t\t\t  if (nowcid) {\r\n\t\t\t    for (var i = 0; i < clist.length; i++) {\r\n\t\t\t      if (clist[i]['id'] == nowcid) {\r\n\t\t\t        that.curIndex = i;\r\n\t\t\t        that.curCid = nowcid;\r\n\t\t\t      }\r\n\t\t\t      var downcdata = clist[i]['child'];\r\n\t\t\t      var isget = 0;\r\n\t\t\t      for (var j = 0; j < downcdata.length; j++) {\r\n\t\t\t        if (downcdata[j]['id'] == nowcid) {\r\n\t\t\t          that.curIndex = i;\r\n\t\t\t          that.curCid = nowcid;\r\n\t\t\t          isget = 1;\r\n\t\t\t          break;\r\n\t\t\t        }\r\n\t\t\t      }\r\n\t\t\t      if (isget) break;\r\n\t\t\t    }\r\n\t\t\t  }\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tthat.getdatalist();\r\n\t\t\t});\r\n\t\t},\r\n \r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar cid = that.curCid?that.curCid:that.clist[that.curIndex].id;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tvar order = that.order;\r\n    \r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tvar wherefield = {};\r\n\t\t\twherefield.pagenum = pagenum;\r\n\t\t\twherefield.field = field;\r\n\t\t\twherefield.order = order;\r\n\t\t\twherefield.bid = bid;\r\n\t\t\tif(bid > 0){\r\n\t\t\t\twherefield.cid2 = cid;\r\n\t\t\t}else{\r\n\t\t\t\twherefield.cid = cid;\r\n\t\t\t}\r\n\t\t\tconsole.log(wherefield);\r\n\t\t\tapp.post('ApiAdminWorkorder/getsubcate',wherefield, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\tif(pagenum == 1){\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.iscate = res.iscate\r\n\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\tthat.datalist = newdata;\r\n\t\t\t});\r\n \r\n\t\t},\r\n\r\n\t\tscrolltolower: function () {\r\n\r\n\t\t\tif (!this.nomore) {\r\n\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getdatalist(true);\r\n\r\n\t\t\t}\r\n\r\n\t\t},\r\n\r\n\t\t//改变子分类\r\n    \r\n\t\tchangeCTab: function (e) {\r\n    \r\n\t\t\tvar that = this;\r\n\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\t\t\tconsole.log(index)\r\n\t\t\tthis.curIndex2 = index;\r\n\t\t\tthis.curCid = id;\r\n\t\t},\r\n    \r\n\t\t//改变排序规则\r\n\r\n\t\tchangeOrder: function (e) {\r\n    \r\n\t\t\tvar t = e.currentTarget.dataset;\r\n  \r\n\t\t\tthis.field = t.field; \r\n\t\t\tthis.order = t.order;\r\n \r\n\t\t\tthis.pagenum = 1;\r\n\t\t\tthis.datalist = []; \r\n\t\t\tthis.nomore = false;\r\n\t\t\t\r\n\t\t\tthis.getdatalist();\r\n  \r\n\t\t},\r\n   \r\n\t\t//事件处理函数\r\n \r\n\t\tswitchRightTab: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\t\t\tthis.curIndex = index;\r\n\t\t\tthis.curIndex2 = -1;\r\n\t\t\tthis.nodata = false;\r\n\t\t\tthis.curCid = id;\r\n\t\t\tthis.pagenum = 1; \r\n\t\t\tthis.datalist = [];\r\n\t\t\tthis.nomore = false;\r\n  \r\n\t\t\tthis.getdatalist();\r\n \r\n\t\t}\r\n,\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n        showLinkChange: function (e) {\r\n            var that = this;\r\n        \tthat.showLinkStatus = !that.showLinkStatus;\r\n            that.lx_name = e.currentTarget.dataset.lx_name;\r\n            that.lx_bid = e.currentTarget.dataset.lx_bid;\r\n            that.lx_bname = e.currentTarget.dataset.lx_bname;\r\n            that.lx_tel = e.currentTarget.dataset.lx_tel;\r\n        },\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\npage {height:100%;}\r\n.container{width: 100%;height:100%;max-width:640px;background-color: #fff;color: #939393;display: flex;flex-direction:column}\r\n.content-container{flex:1;height:100%;display:flex;overflow: hidden;}\r\n\r\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\r\n.nav_left .nav_left_items{line-height:50rpx;color:#333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 20rpx;}\r\n.nav_left .nav_left_items.active{background: #fff;color:#333;font-size:28rpx;font-weight:bold}\r\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\r\n.nav_left .nav_left_items.active .before{display:block}\r\n\r\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}\r\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%}\r\n.nav-pai{ width: 100%;display:flex;align-items:center;justify-content:center;}\r\n.nav-paili{flex:1; text-align:center;color:#323232; font-size:28rpx;font-weight:bold;position: relative;height:80rpx;line-height:80rpx;}\r\n.nav-paili .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.nav-paili .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n\r\n.classify-ul{width:100%;height:100rpx;padding:0 10rpx;}\r\n.classify-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:22rpx;color:#6C737F;font-size:20rpx;text-align: center;height:44rpx; line-height:44rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\r\n\r\n.classify-box{padding: 0 0 20rpx 0;width: 100%;height:calc(100% - 60rpx);overflow-y: scroll; border-top:1px solid #F5F6F8;}\r\n.classify-box .nav_right_items{ width:100%;border-bottom:1px #f4f4f4 solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\r\n\r\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\r\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\r\n.product-itemlist .product-info {width: 100%;padding:0 10rpx 5rpx 20rpx;position: relative; display: flex; }\r\n\r\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx; line-height: 72rpx; margin-bottom:10rpx;}\r\n.product-itemlist .product-info .img{ display: flex; align-items: center;}\r\n.product-itemlist .product-info .img image{ width: 38rpx; height: 38rpx;}\r\n.product-itemlist .product-info .count{ display: inline-block; background-color: #FC5648;color: #fff; border-radius:50%; width:36rpx;height: 36rpx; line-height:36rpx; text-align:center;margin-left: 20rpx; }\r\n\r\n.product-itemlist .list{ height: 60rpx; padding-left: 30rpx;display: flex;justify-content: space-between;align-items: center;}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839438486\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}