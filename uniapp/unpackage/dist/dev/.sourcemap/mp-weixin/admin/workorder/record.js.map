{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/record.vue?ff2e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/record.vue?b6b3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/record.vue?a079", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/record.vue?760c", "uni-app:///admin/workorder/record.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/record.vue?94f7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/record.vue?36ba"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "format", "opt", "loading", "isload", "menuindex", "statuss", "st", "datalist", "pagenum", "nomore", "lclist", "current", "ishowjindu", "jdlist", "content_pic", "cid", "begindate", "enddate", "keyword", "nodata", "cateArr", "cindex", "pre_url", "computed", "startDate", "endDate", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getDate", "year", "month", "day", "searchInput", "search", "that", "bindDateChange", "bindDateChange2", "changetab", "uni", "scrollTop", "duration", "getdata", "app", "formid", "getliucheng", "radioChange", "console", "jindu", "id", "<PERSON>jd", "picker<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+Gx1B;AAAA,eAEA;EAEAC;IACA;MACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAC;MACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAP;MACA;MACAQ;QAAA1B;QAAAF;QAAAC;QAAAX;QAAAE;QAAAO;QAAA8B;MAAA;QACAT;QACA;QACAA;QACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAU;MACA;MACAF;QACA;QACAR;MAEA;IACA;IACAW;MACA;MACAC;IACA;IACAC;MACA;MACAb;MACA;MACAA;MACA;MACAQ;QAAAM;MAAA;QACA;UACA;UACAd;QACA;MAEA;IACA;IACAe;MACA;MACAf;IACA;IACAgB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClRA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/workorder/record.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/workorder/record.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./record.vue?vue&type=template&id=4d17cc11&\"\nvar renderjs\nimport script from \"./record.vue?vue&type=script&lang=js&\"\nexport * from \"./record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./record.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/workorder/record.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=template&id=4d17cc11&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload && _vm.ishowjindu ? _vm.jdlist.length : null\n  var l1 =\n    _vm.isload && _vm.ishowjindu && g0 > 0\n      ? _vm.__map(_vm.jdlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 = item.content_pic.length\n          var l0 = _vm.__map(item.hflist, function (hf, hfindex) {\n            var $orig = _vm.__get_orig(hf)\n            var g2 = hf.hfcontent_pic.length\n            return {\n              $orig: $orig,\n              g2: g2,\n            }\n          })\n          return {\n            $orig: $orig,\n            g1: g1,\n            l0: l0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<dd-tab :itemdata=\"['全部','待处理','处理中 ','已完成','待支付']\" :itemst=\"['all','0','1','2','10']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\r\n\t\t<view style=\"width:100%;height:90rpx\"></view>\r\n\t\t\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\"  @input=\"searchInput\"  placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"margin: 0 20rpx;\">\r\n\t\t\t\t\r\n\t\t\t\t<picker @change=\"pickerChange\" :value=\"cindex\" :range=\"cateArr\" range-key=\"name\" style=\"height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE\">\r\n\t\t\t\t\t<view class=\"picker\">{{cindex==-1? '请选择工单' : cateArr[cindex].name}}</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"searchbox\">\r\n\t\t\r\n\t\t\t<!-- 日期选择 -->\r\n\t\t\t<view class=\"date\">\r\n\t\t\t\t<view class=\"begindate\">\r\n\t\t\t\t\t<picker mode=\"date\" :value=\"begindate\" :start=\"startDate\" :end=\"endDate\" @change=\"bindDateChange\" >\r\n\t\t\t\t\t\t<view class=\"uni-input\">{{begindate?begindate:'选择开始日期'}}</view>\r\n\t\t\t\t\t</picker>\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t-\r\n\t\t\t\t<view class=\"enddate\">\r\n\t\t\t\t\t<picker mode=\"date\" :value=\"enddate\" :start=\"startDate\" :end=\"endDate\" @change=\"bindDateChange2\">\r\n\t\t\t\t\t\t<view class=\"uni-input\"> {{enddate?enddate:'选择结束日期'}}</view>\r\n\t\t\t\t\t</picker>\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"searchbtn\" :style=\"{background:t('color1')}\" @tap=\"search\">搜索</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"content\" id=\"datalist\">\r\n\t\t\t<view class=\"item\" v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view class=\"f1\"  @tap.stop=\"goto\" :data-url=\"'formdetail?id=' + item.id\">\r\n\t\t\t\t\t\t<view class=\"itembox\">\r\n\t\t\t\t\t\t\t<view style=\"justify-content: space-between;\">\r\n\t\t\t\t\t\t\t\t<view class=\"t1\" >用户昵称：{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"t1\" >工单类型：{{item.cname}}</view>\t\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" >工单名称：{{item.title}}</text>\r\n\t\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==0 && (!item.payorderid||item.paystatus==1)\" style=\"color:#88e\" @tap.stop=\"goto\"  :data-url=\"'jindu?id='+item.id+'&cid='+item.cid\" :data-status=\"item.status\"  >{{item.clname}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==0 && item.payorderid && item.paystatus==0\" style=\"color:red\">待支付</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==1\" style=\"color:green\" @tap.stop=\"goto\"  :data-url=\"'jindu?id='+item.id+'&cid='+item.cid\" :data-status=\"item.status\" >{{item.clname}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==2\" style=\"color:green\">已完成</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==-1\" style=\"color:red\">已驳回</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t<view class=\"flex\" style=\"justify-content: space-between; margin-top: 20rpx;\">\r\n\t\t\t\t\t\t\t<view  @tap.stop=\"goto\" :data-url=\"'formdetail?id=' + item.id\">\r\n\t\t\t\t\t\t\t\t<text class=\"t2\">提交时间：{{item.createtime}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.paynum\" user-select=\"true\" selectable=\"true\">{{item.paynum}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"jindu\" @tap.stop=\"goto\" :data-url=\"'jindu?id='+item.id+'&cid='+item.cid\" :data-status=\"item.status\">查看进度</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\t</view>\r\n\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"modal\" v-if=\"ishowjindu\">\r\n\t\t\t<view class=\"modal_jindu\">\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closejd\"><image :src=\"pre_url+'/static/img/close.png'\" /></view>\r\n\t\t\t\t\t<block v-if=\"jdlist.length>0\">\r\n\t\t\t\r\n\t\t\t\t\t\t<view class=\"item \" v-for=\"(item,index) in jdlist\" :key=\"index\" style=\"display: flex;\">\r\n\t\t\t\t\t\t\t<view class=\"f1\"><image :src=\"'/static/img/jindu' + (index==0?'2':'1') + '.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t\t<text class=\"t2\"> 时间：{{item.time}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.desc}}({{item.remark}}) </text>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.content_pic.length>0\" v-for=\"(pic, ind) in item.content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"pic\" @tap=\"previewImage\" :data-url=\"pic\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-for=\"(hf,hfindex) in item.hflist\" :key=\"hfindex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"hf.hfremark\" >用户回复：{{hf.hfremark}} </view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t4\" v-if=\"hf.hftime\" >回复时间：{{hf.hftime}} </view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"hf.hfcontent_pic.length>0\" v-for=\"(pic2, ind2) in hf.hfcontent_pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"pic2\" @tap=\"previewImage\" :data-url=\"pic2\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else-if=\"statuss==-1\">\r\n\t\t\t\t\t\t\t<view style=\"font-size:14px;color:#f05555;padding:10px;\">工单已驳回</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<view style=\"font-size:14px;color:#f05555;padding:10px;\">等待处理</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\r\n\t</block>\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\t\r\n  data() {\r\n\t\tconst currentDate = this.getDate({\r\n\t\t\t\tformat: true\r\n\t\t})\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tstatuss:0,\r\n      st: 'all',\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n\t\t\tlclist:[],\r\n\t\t\tcurrent:0,\r\n\t\t\tishowjindu:false,\r\n\t\t\tjdlist:[],\r\n\t\t\tcontent_pic:[],\r\n\t\t\tcid:0,\r\n\t\t\tbegindate:'',\r\n\t\t\tenddate:'',\r\n\t\t\tkeyword:'',\r\n\t\t\tnodata: false,\r\n\t\t\tcateArr:[],\r\n\t\t\tcindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n    };\r\n  },\r\n\tcomputed: {\r\n\t\t\tstartDate() {\r\n\t\t\t\t\treturn this.getDate('start');\r\n\t\t\t},\r\n\t\t\tendDate() {\r\n\t\t\t\t\treturn this.getDate('end');\r\n\t\t\t}\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.st = this.opt.st || 'all';\r\n\t\tthis.cid = this.opt.cid\r\n\t\tthis.cateid = this.opt.cateid\r\n\t\tthis.getdata();\r\n\t\tthis.getliucheng();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  methods: {\r\n\t\tgetDate(type) {\r\n\t\t\t\tconst date = new Date();\r\n\t\t\t\tlet year = date.getFullYear();\r\n\t\t\t\tlet month = date.getMonth() + 1;\r\n\t\t\t\tlet day = date.getDate();\r\n\t\t\t\tif (type === 'start') {\r\n\t\t\t\t\t\tyear = year - 60;\r\n\t\t\t\t} else if (type === 'end') {\r\n\t\t\t\t\t\tyear = year;\r\n\t\t\t\t}\r\n\t\t\t\tmonth = month > 9 ? month : '0' + month;\r\n\t\t\t\tday = day > 9 ? day : '0' + day;\r\n\t\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t},\r\n\t\tsearchInput:function(e){\r\n\t\t\tthis.keyword = e.detail.value\r\n\t\t},\r\n\t\tsearch:function(e){\r\n\t\t\t  var that = this;\r\n\t\t\t\tthat.getdata();\r\n\t\t},\r\n\t\tbindDateChange: function(e) {\r\n\t\t\t\tthis.begindate = e.detail.value\r\n\t\t},\r\n\t\tbindDateChange2: function(e) {\r\n\t\t\t\tthis.enddate = e.detail.value\r\n\t\t},\r\n    changetab: function (st) {\r\n      this.st = st;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n    },\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n\t\t\tthis.nodata = false;\r\n\t\t\tthis.nomore = false;\r\n\t\t\tthis.loading = true;\r\n\t\t\tif(this.cindex!=-1){\r\n\t\t\t\tthat.cateid = that.cateArr[this.cindex].id;\r\n\t\t\t}\r\n      app.post('ApiAdminWorkorder/formlog', {keyword:that.keyword, begindate:that.begindate,enddate:that.enddate, st: st,pagenum: pagenum,cid:that.cid,formid:that.cateid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.data;\r\n\t\t\t\tthat.cateArr = res.catelist\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\t\tgetliucheng:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tapp.post('ApiAdminWorkorder/getliucheng', {}, function (res) {\r\n\t\t\t\t\tvar lclist = res.datalist;\r\n\t\t\t\t\tthat.lclist = lclist;\r\n\t\t\t\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\tradioChange:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tconsole.log(e)\r\n\t\t},\r\n\t\tjindu:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tthat.ishowjindu=true\r\n\t\t\tvar id = e.currentTarget.dataset.id\r\n\t\t\tthat.statuss = e.currentTarget.dataset.status\r\n\t\t\t//读取进度表\r\n\t\t\tapp.post('ApiWorkorder/selectjindu', { id: id }, function (res) {\r\n\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\tvar data = res.data\r\n\t\t\t\t\t\tthat.jdlist =data\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t})\r\n\t\t},\r\n\t\tclosejd:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tthat.ishowjindu=false\r\n\t\t},\r\n\t\tpickerChange: function (e) {\r\n\t\t  this.cindex = e.detail.value;\r\n\t\t},\r\n  }\r\n}\r\n</script>\r\n<style>\r\n\r\n\t.content{ width:100%;margin:0;}\r\n\t.content .item{ width:94%;margin:20rpx 3%;;background:#fff;border-radius:16rpx;padding:30rpx 30rpx;display:flex;align-items:center;}\r\n\t.content .item:last-child{border:0}\r\n\t.content .item .f1{width:100%;display:flex;flex-direction:column}\r\n\t.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis; height:50rpx}\r\n\t.content .item .f1 .t2{color:#666666;margin-top:10rpx}\r\n\t.content .item .f1 .t3{color:#666666}\r\n\t.content .item .f2{width:20%;font-size:32rpx;text-align:right}\r\n\t.content .item .f2 .t1{color:#03bc01}\r\n\t.content .item .f2 .t2{color:#000000}\r\n\t.content .item .f3{ flex:1;font-size:30rpx;text-align:right}\r\n\t.content .item .f3 .t1{color:#03bc01}\r\n\t.content .item .f3 .t2{color:#000000}\r\n\t.content .item .f1 .itembox{ display: flex; justify-content: space-between;}\r\n\t\r\n\t\r\n\t.jindu{ border: 1rpx solid #ccc; font-size: 24rpx; padding: 5rpx 10rpx; border-radius: 10rpx; color: #555;}\r\n\t\r\n\t\r\n\t.topsearch{width:94%;margin:10rpx 3%;}\r\n\t.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n\t.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n\t.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\t\r\n\t.searchbox{ width: 100%;background: #FFFFFF;padding:20rpx 26rpx;z-index: 9999;display: flex;justify-content: space-between;align-items: center; }\r\n\t.searchbox .picker{display: flex;justify-content: space-between;align-items: center;border:1rpx solid #e0e0e0;border-radius: 6rpx;padding:0 12rpx;height: 64rpx;line-height: 70rpx; }\r\n\t.searchbox .picker .picker-txt{text-overflow: ellipsis;white-space: nowrap;overflow: hidden;height: 70rpx; width: 300rpx;}\r\n\t.searchbox .down{width: 20rpx;height: 20rpx;margin-left: 10rpx;flex-shrink: 0;}\r\n\t.pickerD{width: 50%;margin-left: 20rpx;}\r\n\t.searchbox .cates{ width: 30%;overflow: hidden;}\r\n\t.date{ display: flex;align-items: center;}\r\n\t.date .begindate{ }\r\n\t.date .begindate .uni-input{ color: grey;}\r\n\t.date .enddate { margin:0 10rpx;}\r\n\t.date .enddate .uni-input{  color: grey;}\r\n\t.searchbtn{ display: flex; width: 120rpx;height: 50rpx;color: #fff;line-height: 50rpx; border-radius:50rpx;align-items: center;justify-content: center;}\r\n\t\r\n\t.modal{ position: fixed; background:rgba(0,0,0,0.3); width: 100%; height: 100%; top:0; z-index: 100;}\r\n\t.modal .modal_jindu{ background: #fff; position: absolute; top: 20%; align-items: center; margin: auto; width: 90%; left: 30rpx; border-radius: 10rpx; padding: 40rpx;overflow-y:auto; display: flex; flex-wrap: wrap; max-height: 600rpx;}\r\n\t.modal_jindu .close image { width: 30rpx; height: 30rpx;position: fixed; top:21%;right: 60rpx;}\r\n\t.modal_jindu .title{ font-size: 32rpx; font-weight: bold;}\r\n\t.uni-list{ margin-top: 30rpx;}\r\n\t.uni-list-cell{ display: flex; height: 80rpx;}\r\n\t.beizhu label{ width: 100rpx;}\r\n\t.modal_jindu .btn{  background: #1658c6; border-radius: 3px;line-height: 24px; border: none; padding: 0 10px;color: #fff;font-size: 20px; text-align: center; width: 300px;  display: flex; height: 40px; justify-content: center;align-items: center;}\r\n\t.beizhu textarea{  height: 100rpx;}\r\n\t\r\n\t.modal_jindu .item{ }\r\n\t.modal_jindu .item .f1{ position:relative}\r\n\t/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/\r\n\t.modal_jindu .item .f1 image{width: 30rpx; height: 100rpx; position: absolute; left: -16rpx; top: 0rpx;}\r\n\t.modal_jindu .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0; margin-left: 30rpx;}\r\n\t.modal_jindu .item .f2 .t1{font-size: 30rpx; width: 100%;word-break:break-all; }\r\n\t.modal_jindu .item .f2 .t1{font-size: 26rpx;}\r\n\t.modal_jindu .item .f2 .t3{font-size: 24rpx; color:#008000; margin-top: 10rpx;}\r\n\t.modal_jindu .item .f2 .t4{font-size: 24rpx;  color:#008000;}\r\n\t\r\n\t\r\n\t.layui-imgbox-img{display: block;width:150rpx;height:150rpx;padding:2px;background-color: #f6f6f6;overflow:hidden ;margin-top: 20rpx;}\r\n\t.layui-imgbox-img>image{max-width:100%;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839438450\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}