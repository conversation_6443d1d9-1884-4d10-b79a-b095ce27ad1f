{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/index.vue?edee", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/index.vue?1310", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/index.vue?3be2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/index.vue?2d68", "uni-app:///admin/index/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/index.vue?f703", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/index.vue?dd1d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "statusBarHeight", "pre_url", "set", "uinfo", "count0", "count1", "count2", "count3", "count4", "seckill<PERSON>ount", "collageCount", "kanjia<PERSON>ount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scoreshopCount", "maidanCount", "productCount", "yuyueorderCount", "cycleCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formlogCount", "luckycollageCount", "auth_data", "showshoporder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showcollageorder", "showkanjiaorder", "showseckillorder", "showscoreshoporder", "showluckycollageorder", "showtuangouorder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showmaidanlog", "showformlog", "showworkorder", "workorderCount", "showrecharge", "showrestaurantproduct", "showrestauranttable", "wxtmplset", "searchmember", "showCycleorder", "scoreshop_product", "scoreproductCount", "custom", "isload", "today_order_count", "today_money", "restaurant_shop_count", "restaurant_takeaway_count", "restaurant_booking_count", "restaurant_queue", "restaurant_deposit", "restaurant_product_count", "restaurant_table_count", "tabIndex", "shopDataShow", "other_show", "showworkadd", "showbusinessqr", "show_categroy_business", "show_auth", "add_product", "hotel", "showmdmoney", "onLoad", "onPullDownRefresh", "methods", "explanation", "uni", "title", "content", "showCancel", "determineTabIndex", "tabChange", "getdata", "that", "app", "res", "<PERSON><PERSON><PERSON><PERSON>", "success", "console", "url", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjHA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4jBv1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;UACA;QACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;QACAA;QACAE;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;QACAF;QAAA;MACA,6CAkCA;QAEAR;UACAW;YACAC;YACA;cACAJ;YACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;gBACAK;cACA;cACAL;YACA;UACA;UACAM;YACAN;UACA;QACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACp0BA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1f4f9c77&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1f4f9c77&\"", "var components\ntry {\n  components = {\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.auth_data.finance\n      ? _vm.inArray(\"show_hx_num\", _vm.show_auth)\n      : null\n  var m1 =\n    _vm.isload && _vm.custom.mendian_upgrade && _vm.uinfo.bid == 0\n      ? _vm.t(\"门店\")\n      : null\n  var m2 =\n    _vm.isload && _vm.custom.mendian_upgrade && _vm.uinfo.bid == 0\n      ? _vm.t(\"门店\")\n      : null\n  var m3 =\n    _vm.isload && _vm.custom.mendian_upgrade && _vm.uinfo.bid == 0\n      ? _vm.t(\"门店\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.shopDataShow &&\n    _vm.auth_data.order &&\n    _vm.tabIndex == 1 &&\n    _vm.showscoreshoporder\n      ? _vm.t(\"积分\")\n      : null\n  var m5 =\n    _vm.isload && false && _vm.auth_data.order && _vm.showscoreshoporder\n      ? _vm.t(\"积分\")\n      : null\n  var m6 = _vm.isload ? _vm.inArray(\"Coupon\", _vm.show_auth) : null\n  var m7 = _vm.isload && m6 ? _vm.t(\"优惠券\") : null\n  var m8 = _vm.isload ? _vm.inArray(\"RestaurantCoupon\", _vm.show_auth) : null\n  var m9 = _vm.isload ? _vm.inArray(\"HuodongBaomingOrder\", _vm.show_auth) : null\n  var m10 = _vm.isload\n    ? _vm.inArray(\"member_code_buy\", _vm.auth_data.hexiao_auth_data)\n    : null\n  var m11 = _vm.isload ? _vm.inArray(\"queue_free\", _vm.show_auth) : null\n  var m12 = _vm.isload\n    ? _vm.inArray(\"wxadminQueueFreeSet\", _vm.show_auth)\n    : null\n  var m13 = _vm.isload ? _vm.inArray(\"ShopOrderlr\", _vm.show_auth) : null\n  var m14 = _vm.isload ? _vm.inArray(\"ShopStock\", _vm.show_auth) : null\n  var m15 = _vm.isload\n    ? _vm.inArray(\"show_business\", _vm.show_auth) && _vm.uinfo.bid == 0\n    : null\n  var m16 = _vm.isload ? _vm.inArray(\"invoicebaoxiao\", _vm.show_auth) : null\n  var m17 = _vm.isload\n    ? _vm.inArray(\"qrcode_shop\", _vm.auth_data.wxauth_data)\n    : null\n  var m18 = _vm.isload && _vm.auth_data.member ? _vm.t(\"会员\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"view-width\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<!-- background:'url('+pre_url+'/static/img/admin/headbgimg.png)' -->\r\n\t\t<view class=\"head-class\" :style=\"{background:'url('+set.bgimg+')',backgroundSize:'cover',backgroundRepeat:'no-repeat'}\">\r\n\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t<view :style=\"{height:(44+statusBarHeight)+'px'}\"></view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<view class=\"head-view flex-bt flex-y-center\">\r\n\t\t\t\t<view class=\"avat-view\">\r\n\t\t\t\t\t<view class=\"user-info flex-row\" @tap=\"goto\" :data-url=\"uinfo.bid==0 ? '/pages/index/index' : '/pagesExt/business/index?id='+uinfo.bid\" >\r\n\t\t\t\t\t\t <text class=\"nickname\">{{set.name}}</text><text class=\"nickname\" v-if=\"uinfo.bid > 0\" style=\"font-weight: normal;\">(ID:{{uinfo.bid}})</text>\r\n\t\t\t\t\t</view>\r\n          <image class=\"imgback\" :src=\"`${pre_url}/static/img/location/right-black.png`\" ></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"option-img-view\">\r\n\t\t\t\t\t<view  style=\"margin-right: 28rpx;\" class=\"setup-view\" @tap=\"saoyisao\"  v-if=\"auth_data.hexiao_auth_data\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/saoyisao.png`\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"setup-view\" @tap=\"goto\" data-url=\"setpage\">\r\n\t\t\t\t\t\t<image class=\"setup-img\":src=\"`${pre_url}/static/img/admin/setup.png`\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"today-data flex-bt flex-y-center\"  v-if=\"auth_data.finance\">\r\n\t\t\t\t<view class=\"option-view flex-col flex-x-center\">\r\n\t\t\t\t\t<view class=\"title-text flex flex-y-center flex-x-center\" @click=\"explanation\">今日收款<image class=\"title-icon\" :src=\"`${pre_url}/static/img/admin/jieshiicon.png`\"></image></view>\r\n\t\t\t\t\t<view class=\"flex-y-center flex-x-center\">\r\n\t\t\t\t\t\t<text class=\"unit-money\">￥</text><text class=\"num-text\">{{today_money}}</text>\r\n\t\t\t\t\t</view>\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"option-view flex-col flex-x-center\">\r\n\t\t\t\t\t<text class=\"title-text\">今日订单</text>\r\n\t\t\t\t\t<view class=\"flex-y-center flex-x-center\">\r\n\t\t\t\t\t\t<text class=\"num-text\">{{today_order_count}}</text>\r\n\t\t\t\t\t</view>\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"option-view flex-col flex-x-center\" v-if=\"inArray('show_hx_num',show_auth)\">\r\n\t\t\t\t\t<text class=\"title-text\">核销次数</text>\r\n\t\t\t\t\t<view class=\"flex-y-center flex-x-center\">\r\n\t\t\t\t\t\t<text class=\"num-text\">{{uinfo.hexiao_num}}</text>\r\n\t\t\t\t\t</view>\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"option-view flex-col flex-x-center\">\r\n\t\t\t\t\t<text class=\"title-text\">今日访客数</text>\r\n\t\t\t\t\t<view class=\"flex-y-center flex-x-center\">\r\n\t\t\t\t\t\t<text class=\"num-text\">5555</text>\r\n\t\t\t\t\t</view>\t\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\r\n\t\t\t<block v-if=\"auth_data.order\">\r\n\t\t\t<view class=\"mall-orders flex-col width\" v-if=\"showshoporder\">\r\n\t\t\t\t<view class=\"order-title flex-bt\">\r\n\t\t\t\t\t<view class=\"title-text flex-y-center\"><image class=\"left-img\" :src=\"`${pre_url}/static/img/admin/titletips.png`\"></image>商城订单</view>\r\n\t\t\t\t\t<view class=\"all-text flex-y-center\" @tap=\"goto\" data-url=\"../order/shoporder\">全部订单<image class=\"right-img\" :src=\"`${pre_url}/static/img/admin/jiantou.png`\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"order-list flex-bt\">\r\n\t\t\t\t\t<view class=\"option-order flex-col\" @tap=\"goto\" data-url=\"../order/shoporder?st=0\">\r\n\t\t\t\t\t\t<text class=\"num-text\">{{count0}}</text>\r\n\t\t\t\t\t\t<text class=\"title-text\">待付款</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"option-order flex-col\" @tap=\"goto\" data-url=\"../order/shoporder?st=1\">\r\n\t\t\t\t\t\t<text class=\"num-text\">{{count1}}</text>\r\n\t\t\t\t\t\t<text class=\"title-text\">待发货</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"option-order flex-col\" @tap=\"goto\" data-url=\"../order/shoporder?st=2\">\r\n\t\t\t\t\t\t<text class=\"num-text\">{{count2}}</text>\r\n\t\t\t\t\t\t<text class=\"title-text\">待收货</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"option-order flex-col\" @tap=\"goto\" data-url=\"../order/shopRefundOrder\">\r\n\t\t\t\t\t\t<text class=\"num-text\">{{count4}}</text>\r\n\t\t\t\t\t\t<text class=\"title-text\">退款/售后</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"option-order flex-col\" @tap=\"goto\" data-url=\"../order/shoporder?st=3\">\r\n\t\t\t\t\t\t<text class=\"num-text\">{{count3}}</text>\r\n\t\t\t\t\t\t<text class=\"title-text\">已完成</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<view class=\"meun-view flex-aw\">\r\n\t\t\t<view class=\"meun-options flex-col flex-x-center\" @tap=\"goto\" data-url=\"../hexiao/record\"  v-if=\"auth_data.hexiao_auth_data\">\r\n\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/menu1.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t<text class=\"menu-text\">核销记录</text>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"auth_data.product\">\t\t\t\r\n\t\t\t\t<view class=\"meun-options flex-col flex-x-center\" @tap=\"goto\" data-url=\"../product/index\">\r\n\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/menu2.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t<text class=\"menu-text\">商品管理</text>\r\n\t\t\t\t</view>\t\t\t\t\r\n\t\t\t</block>\r\n\t\t\t<view class=\"meun-options flex-col flex-x-center\" @tap=\"goto\" data-url=\"../index/setnotice\" v-if=\"uinfo.shownotice\">\r\n\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/menu3.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t<text class=\"menu-text\">消息通知</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"meun-options flex-col flex-x-center\" @tap=\"goto\" data-url=\"login\">\r\n\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/menu4.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t<text class=\"menu-text\">切换账号</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"meun-options flex-col flex-x-center\" @tap=\"goto\" data-url=\"setpwd\">\r\n\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/menu5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t<text class=\"menu-text\">修改密码</text>\r\n\t\t\t</view>\r\n\t\t</view>\t\t\r\n\t\t</view>\r\n\r\n\t\t<block v-if=\"auth_data.restaurant_product || auth_data.restaurant_table || auth_data.restaurant_tableWaiter\">\r\n\t\t<view class=\"menu-manage flex-col\">\r\n\t\t\t<view class=\"menu-title\">菜品管理</view>\r\n\t\t\t<view class=\"menu-list width\">\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../restaurant/product/edit\" v-if=\"auth_data.restaurant_product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm1.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">添加菜品</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../restaurant/product/index\" v-if=\"auth_data.restaurant_product\" >\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm2.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">菜品列表</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../restaurant/category/index\" v-if=\"auth_data.restaurant_product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm6.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">菜品分类</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../restaurant/category/edit\" v-if=\"auth_data.restaurant_product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm4.png`\"class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">添加分类</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../restaurant/table\" v-if=\"auth_data.restaurant_table\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">餐桌编辑</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../restaurant/tableCategory\" v-if=\"auth_data.restaurant_table\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm6.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">餐桌分类</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../restaurant/tableWaiter\" v-if=\"auth_data.restaurant_tableWaiter\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm3.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">点餐清台</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t</block>\r\n\r\n\t\t<block v-if=\"custom.mendian_upgrade && uinfo.bid == 0\">\r\n\t\t\t<view class=\"menu-manage flex-col\">\r\n\t\t\t\t<view class=\"menu-title\">{{t('门店')}}管理</view>\r\n\t\t\t\t<view class=\"menu-list width\">\r\n\t\t\t\t\t<block>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/adminExt/mendian/list\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dismendian.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">{{t('门店')}}列表</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/adminExt/mendian/withdrawlog\" >\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm8.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">{{t('门店')}}佣金提现</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t\r\n\t\t<block v-if=\"auth_data.restaurant_takeaway || auth_data.restaurant_shop || auth_data.restaurant_booking || auth_data.restaurant_deposit || auth_data.restaurant_queue\">\r\n\t\t\t<view class=\"menu-manage flex-col\">\r\n\t\t\t\t<view class=\"menu-title\">外卖管理</view>\r\n\t\t\t\t<view class=\"menu-list width\">\r\n\t\t\t\t\t<block>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../restaurant/takeawayorder\" v-if=\"auth_data.restaurant_takeaway\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm7.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">外卖订单</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../restaurant/shoporder\" v-if=\"auth_data.restaurant_shop\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm8.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">点餐订单</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../restaurant/bookingorder\" v-if=\"auth_data.restaurant_booking\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm9.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">预定订单</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../restaurant/booking\" v-if=\"auth_data.restaurant_booking\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm1.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">添加预定</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../restaurant/queue\" v-if=\"auth_data.restaurant_queue\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm3.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">排队叫号</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../restaurant/queueCategory\"  v-if=\"auth_data.restaurant_queue\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm4.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">排队管理</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../restaurant/depositorder\" v-if=\"auth_data.restaurant_deposit\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm2.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">寄存订单</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n    <block v-if=\"showmdmoney == 1 && (auth_data.mendian_mdmoneylog || auth_data.mendian_mdwithdraw || auth_data.mendian_mdwithdrawlog)\">\r\n    \t<view class=\"menu-manage flex-col\">\r\n    \t\t<view class=\"menu-title\">门店余额</view>\r\n    \t\t<view class=\"menu-list width\">\r\n    \t\t\t<block>\r\n    \t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../finance/mdmoneylog\" v-if=\"auth_data.mendian_mdmoneylog\">\r\n    \t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/financenbg7.png`\" class=\"menu-img\"></image>\r\n    \t\t\t\t\t<text class=\"menu-text\">余额明细</text>\r\n    \t\t\t\t</view>\r\n    \t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../finance/mdwithdraw\" v-if=\"auth_data.mendian_mdwithdraw\">\r\n    \t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/financenbg8.png`\" class=\"menu-img\"></image>\r\n    \t\t\t\t\t<text class=\"menu-text\">余额提现</text>\r\n    \t\t\t\t</view>\r\n    \t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../finance/mdwithdrawlog\" v-if=\"auth_data.mendian_mdwithdrawlog\">\r\n    \t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/financenbg8.png`\" class=\"menu-img\"></image>\r\n    \t\t\t\t\t<text class=\"menu-text\">提现记录</text>\r\n    \t\t\t\t</view>\r\n    \t\t\t</block>\r\n    \t\t</view>\r\n    \t</view>\r\n    </block>\r\n\r\n\t\t<view class=\"menu-manage\" v-if=\"shopDataShow\">\r\n\t\t\t<view class=\"divider-div\"></view>\r\n\t\t\t<view class=\"tab-div flex-y-center\">\r\n\t\t\t\t<view class=\"tab-options flex-col\" @click=\"tabChange(1)\" v-if=\"auth_data.order && (showcollageorder || showkanjiaorder || showseckillorder || showscoreshoporder || showluckycollageorder || showyuyueorder || showCycleorder)\">\r\n\t\t\t\t\t<view :class=\"{'tab-options-active':tabIndex == 1}\">商城订单</view>\r\n\t\t\t\t\t<view class=\"color-bar\" v-if=\"tabIndex == 1\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tab-options flex-col\" @click=\"tabChange(2)\" v-if=\"auth_data.restaurant_takeaway || auth_data.restaurant_shop || auth_data.restaurant_booking || auth_data.restaurant_queue || auth_data.restaurant_deposit\">\r\n\t\t\t\t\t<view :class=\"{'tab-options-active':tabIndex == 2}\">餐饮订单</view>\r\n\t\t\t\t\t<view class=\"color-bar\" v-if=\"tabIndex == 2\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tab-options flex-col\" @click=\"tabChange(0)\" v-if=\"auth_data.restaurant_product || auth_data.restaurant_tableWaiter\">\r\n\t\t\t\t\t<view :class=\"{'tab-options-active':tabIndex == 0}\">餐饮数据</view>\r\n\t\t\t\t\t<view class=\"color-bar\" v-if=\"tabIndex == 0\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t<view class=\"tab-options flex-col\" @click=\"tabChange(4)\" v-if=\"auth_data.hotel_order\">\r\n\t\t\t\t\t<view :class=\"{'tab-options-active':tabIndex == 4}\">{{hotel.text['酒店']}}订单</view>\r\n\t\t\t\t\t<view class=\"color-bar\" v-if=\"tabIndex == 4\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<!-- 餐饮数据 -->\r\n\t\t\t<block v-if=\"auth_data.restaurant_product || auth_data.restaurant_tableWaiter\">\r\n\t\t\t\t<view class=\"data-div flex-col\" v-if=\"tabIndex == 0\">\r\n\t\t\t\t\t<view class=\"data-div-list\">\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../restaurant/product/index\" v-if=\"auth_data.restaurant_product\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">菜品</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{restaurant_product_count}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"auth_data.restaurant_product\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../restaurant/tableWaiter\" v-if=\"auth_data.restaurant_tableWaiter\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">餐桌</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{restaurant_table_count}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\t\t\r\n\r\n\t\t\t<!-- 商城订单 -->\r\n\t\t\t<block v-if=\"auth_data.order\">\r\n\t\t\t\t<view class=\"data-div flex-col\" v-if=\"tabIndex == 1\">\r\n\t\t\t\t\t<view class=\"data-div-list\">\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/collageorder\" v-if=\"showcollageorder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">拼团订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{collageCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"showcollageorder\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/kanjiaorder\" v-if=\"showkanjiaorder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">砍价订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{kanjiaCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"showkanjiaorder\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/seckillorder\" v-if=\"showseckillorder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">秒杀订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{seckillCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"showseckillorder\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/tuangouorder\" v-if=\"showtuangouorder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">团购订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{tuangouCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"showtuangouorder\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/scoreshoporder\" v-if=\"showscoreshoporder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">{{t('积分')}}商城订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{scoreshopCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"showscoreshoporder\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/luckycollageorder\" v-if=\"showluckycollageorder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">幸运拼团订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{luckycollageCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"showluckycollageorder\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/yuyueorder\" v-if=\"showyuyueorder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">预约订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{yuyueorderCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"showyuyueorder\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../order/cycleorder\" v-if=\"showCycleorder\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">周期购订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{cycleCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<!-- 外卖订单 -->\r\n\t\t\t<block v-if=\"auth_data.restaurant_takeaway || auth_data.restaurant_shop || auth_data.restaurant_booking || auth_data.restaurant_queue || auth_data.restaurant_deposit\">\r\n\t\t\t\t<view class=\"data-div flex-col\" v-if=\"tabIndex == 2\">\r\n\t\t\t\t\t<view class=\"data-div-list\">\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../restaurant/takeawayorder\" v-if=\"auth_data.restaurant_takeaway\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">外卖订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{restaurant_takeaway_count}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"auth_data.restaurant_takeaway\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../restaurant/shoporder\" v-if=\"auth_data.restaurant_shop\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">点餐订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{restaurant_shop_count}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"auth_data.restaurant_shop\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../restaurant/bookingorder\" v-if=\"auth_data.restaurant_booking\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">预定订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{restaurant_booking_count}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"auth_data.restaurant_booking\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../restaurant/queue\" v-if=\"auth_data.restaurant_queue\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">排队叫号</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{restaurant_queue}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border-bar-div\" v-if=\"auth_data.restaurant_queue\"></view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"../restaurant/depositorder\" v-if=\"auth_data.restaurant_deposit\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">寄存订单</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{restaurant_deposit}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<!-- 酒店订单 -->\r\n\t\t\t<block v-if=\"auth_data.hotel_order\">\r\n\t\t\t\t<view class=\"data-div flex-col\" v-if=\"tabIndex == 4\">\r\n\t\t\t\t\t<view class=\"data-div-list hotelorder\">\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"/adminExt/hotel/orderlist?st=all\" v-if=\"auth_data.hotel_order\" >\r\n\t\t\t\t\t\t\t<view class=\"title-text\">全部</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{hotel.hotelCount}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"/adminExt/hotel/orderlist?st=1\" v-if=\"auth_data.hotel_order\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">待确认</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{hotel.hotelOrderCount1}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"/adminExt/hotel/orderlist?st=2\" v-if=\"auth_data.hotel_order\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">待入住</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{hotel.hotelOrderCount2}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"/adminExt/hotel/orderlist?st=3\" v-if=\"auth_data.hotel_order\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">已到店</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{hotel.hotelOrderCount3}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data-div-options\" @tap=\"goto\" data-url=\"/adminExt/hotel/orderlist?st=4\" v-if=\"auth_data.hotel_order\">\r\n\t\t\t\t\t\t\t<view class=\"title-text\">已离店</view>\r\n\t\t\t\t\t\t\t<view class=\"num-text\">{{hotel.hotelOrderCount4}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"menu-manage flex-col\" v-if=\"false\">\r\n\t\t\t<view class=\"menu-title\">商家数据</view>\r\n\t\t\t<view class=\"mer-list\">\r\n\t\t\t\t<view class=\"merchant-view cysj-text\" :style=\"{background:'url('+pre_url+'/static/img/admin/merbg1.png)',backgroundSize:'cover',backgroundRepeat:'no-repeat'}\">\r\n\t\t\t\t\t<view class=\"mer-title\">餐饮数据</view>\r\n\t\t\t\t\t<block>\r\n\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../restaurant/product/index\" v-if=\"auth_data.restaurant_product\">菜品列表:<text>{{restaurant_product_count}}</text></view>\t\r\n\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../restaurant/tableWaiter\" v-if=\"auth_data.restaurant_tableWaiter\">餐桌管理:<text>{{restaurant_table_count}}</text></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"merchant-view\" :style=\"{background:'url('+pre_url+'/static/img/admin/merbg2.png)',backgroundSize:'cover',backgroundRepeat:'no-repeat'}\">\r\n\t\t\t\t\t<view class=\"mer-title\">商城订单</view>\r\n\t\t\t\t\t<scroll-view class=\"scroll-Y scdd-text\" scroll-y=\"true\">\r\n\t\t\t\t\t\t<block v-if=\"auth_data.order\">\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/collageorder\" v-if=\"showcollageorder\">拼团订单:<text>{{collageCount}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/kanjiaorder\" v-if=\"showkanjiaorder\">砍价订单:<text>{{kanjiaCount}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/seckillorder\" v-if=\"showseckillorder\">秒杀订单:<text>{{seckillCount}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/tuangouorder\" v-if=\"showtuangouorder\">团购订单:<text>{{tuangouCount}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/scoreshoporder\" v-if=\"showscoreshoporder\">{{t('积分')}}商城订单:<text>{{scoreshopCount}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/luckycollageorder\" v-if=\"showluckycollageorder\">幸运拼团订单:<text>{{luckycollageCount}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/yuyueorder\" v-if=\"showyuyueorder\">预约订单:<text>{{yuyueorderCount}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/cycleorder\" v-if=\"showCycleorder\">周期购订单:<text>{{cycleCount}}</text></view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"mer-options\" @tap=\"goto\" data-url=\"../order/maidanlog\" v-if=\"showmaidanlog\">买单记录:<text>{{maidanCount}}</text></view> -->\r\n\t\t\t\t\t\t\t<!-- <view class=\"mer-options\" @tap=\"goto\" data-url=\"../form/formlog\" v-if=\"showformlog\">表单提交记录:<text>{{formlogCount}}</text></view> -->\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<!-- <view class=\"mer-options\" @tap=\"goto\" data-url=\"../workorder/record\" v-if=\"showworkorder\">工单记录:<text>{{workorderCount}}</text></view> -->\r\n\t\t\t\t\t\t<block v-if=\"scoreshop_product\">\r\n\t\t\t\t\t\t\t<!-- <view class=\"mer-options\" @tap=\"goto\" data-url=\"../scoreproduct/index\" v-if=\"showworkorder\">兑换商品列表:<text>{{scoreproductCount}}</text></view> -->\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"merchant-view\" :style=\"{background:'url('+pre_url+'/static/img/admin/merbg3.png)',backgroundSize:'cover',backgroundRepeat:'no-repeat'}\">\r\n\t\t\t\t\t<view class=\"mer-title\">外卖订单</view>\r\n\t\t\t\t\t<scroll-view class=\"scroll-Y wmdd-text\" scroll-y=\"true\">\r\n\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../restaurant/takeawayorder\" v-if=\"auth_data.restaurant_takeaway\">外卖订单:<text>{{restaurant_takeaway_count}}</text></view>\r\n\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../restaurant/shoporder\" v-if=\"auth_data.restaurant_shop\">点餐订单:<text>{{restaurant_shop_count}}</text></view>\r\n\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../restaurant/bookingorder\" v-if=\"auth_data.restaurant_booking\">预定订单:<text>{{restaurant_booking_count}}</text></view>\r\n\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../restaurant/queue\" v-if=\"auth_data.restaurant_queue\">排队叫号:<text>{{restaurant_queue}}</text></view>\r\n\t\t\t\t\t\t<view class=\"mer-options\" @tap=\"goto\" data-url=\"../restaurant/depositorder\" v-if=\"auth_data.restaurant_deposit\">寄存订单:<text>{{restaurant_deposit}}</text></view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 新增页面放在adminExt分包 -->\r\n\t\t<view class=\"menu-manage flex-col\">\r\n\t\t\t<!-- <view class=\"menu-title\">其他</view> -->\r\n\t\t\t<view class=\"menu-list width\">\r\n\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/adminExt/coupon/index\" v-if=\"inArray('Coupon',show_auth)\">\r\n\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t<text class=\"menu-text\">{{t('优惠券')}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/adminExt/coupon/restaurantList\" v-if=\"inArray('RestaurantCoupon',show_auth)\">\r\n\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t<text class=\"menu-text\">餐饮优惠券</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"auth_data.order\">\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../order/yuekeorder\" v-if=\"showyuekeorder\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">约课记录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../form/formlog\" v-if=\"showformlog\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm8.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">表单提交记录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"/adminExt/order/maidanindex\" v-if=\"showmaidanlog\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm9.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">买单统计</text>\r\n\t\t\t\t\t</view>\r\n\t\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"/adminExt/huodongbaoming/order\"  v-if=\"inArray('HuodongBaomingOrder',show_auth)\">\r\n\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm9.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t<text class=\"menu-text\">活动报名订单</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"/admin/health/record\" v-if=\"custom.showHealth\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">量表填写记录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../workorder/category\" v-if=\"showworkorder\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">工单</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\" @tap=\"goto\" data-url=\"../scoreproduct/index\"  v-if=\"scoreshop_product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">兑换商品列表</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../member/code\" v-if=\"inArray('member_code_buy',auth_data.hexiao_auth_data)\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm6.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">会员消费</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"businessqr\" v-if=\"showbusinessqr\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">推广码</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" :data-url=\"'/pagesA/workorder/category?bid='+uinfo.bid\" v-if=\"showworkadd\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">工单提交</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../product/edit\" v-if=\"auth_data.product && add_product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm7.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">添加商品</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../scoreproduct/edit\" v-if=\"scoreshop_product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">添加兑换商品</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"auth_data.order\">\r\n\t\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/activity/searchmember/searchmember\" v-if=\"searchmember\">\r\n\t\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t\t<text class=\"menu-text\">一键查看</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../yingxiao/queueFree\" v-if=\"inArray('queue_free',show_auth)\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">排队记录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/adminExt/queuefree/queueFreeSet\" v-if=\"inArray('wxadminQueueFreeSet',show_auth)\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">排队设置</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<block v-if=\"show_categroy_business\">\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../product/category2/index\" v-if=\"auth_data.product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm2.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">商品分类</text>\r\n\t\t\t\t\t</view><view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../product/category2/edit?id=\" v-if=\"auth_data.product\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm1.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">添加商品分类</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../order/dkorder\" v-if=\"inArray('ShopOrderlr',show_auth)\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">代客下单</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/adminExt/shop/shopstock\"  v-if=\"inArray('ShopStock',show_auth)\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">库存录入</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"../business/index\" v-if=\"inArray('show_business',show_auth) && uinfo.bid==0\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/wm5.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">商家列表</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/pagesB/admin/pickupdevice\" v-if=\"auth_data.device_addstock\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm6.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">商品柜设备</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/pagesC/invoicebaoxiao/adminrecordlist\" v-if=\"inArray('invoicebaoxiao',show_auth)\">\r\n\t\t\t\t\t\t<image :src=\"`${pre_url}/static/img/admin/dishm8.png`\" class=\"menu-img\"></image>\r\n\t\t\t\t\t\t<text class=\"menu-text\">发票报销记录</text>\r\n\t\t\t\t\t</view>\r\n          <view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/pagesC/qrcodevar/index\" v-if=\"auth_data.qrcode_variable_maidan\">\r\n          \t<image :src=\"`${pre_url}/static/img/admin/menu1.png`\" class=\"menu-img\"></image>\r\n          \t<text class=\"menu-text\">绑定收款码</text>\r\n          </view>          \r\n          <view class=\"meun-list-options flex-col flex-x-center flex-y-center\"  @tap=\"goto\" data-url=\"/adminExt/set/qrcodeShop\" v-if=\"inArray('qrcode_shop',auth_data.wxauth_data)\">\r\n          \t<image :src=\"`${pre_url}/static/img/admin/menu1.png`\" class=\"menu-img\"></image>\r\n          \t<text class=\"menu-text\">店铺二维码</text>\r\n          </view>\r\n\t\t\t\t\t<!-- 新增页面放在adminExt分包 -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"tabbar\">\r\n\t\t\t<view class=\"tabbar-bot\"></view>\r\n\t\t\t<view class=\"tabbar-bar\" style=\"background-color:#ffffff\">\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../member/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.member\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/member.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text\">{{t('会员')}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../kefu/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.zixun\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/zixun.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text\">咨询</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../finance/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.finance\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/finance.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text\">财务</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"../index/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n\t\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/my2.png?v=1'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tabbar-text active\">我的</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t</block>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\topt:{},\r\n\t\t\t\tloading:false,\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\tset:{},\r\n\t\t\t\tuinfo:{},\r\n\t\t\t\tcount0: 0,\r\n\t\t\t\tcount1: 0,\r\n\t\t\t\tcount2: 0,\r\n\t\t\t\tcount3: 0,\r\n\t\t\t\tcount4: 0,\r\n\t\t\t\tseckillCount: 0,\r\n\t\t\t\tcollageCount: 0,\r\n\t\t\t\tkanjiaCount: 0,\r\n\t\t\t\ttuangouCount:0,\r\n\t\t\t\tscoreshopCount: 0,\r\n\t\t\t\tmaidanCount: 0,\r\n\t\t\t\tproductCount: 0,\r\n\t\t\t\tyuyueorderCount: 0,\r\n\t\t\t\tcycleCount: 0,\r\n\t\t\t\thexiaoCount: 0,\r\n\t\t\t\tformlogCount:0,\r\n\t\t\t\tluckycollageCount:0,\r\n\t\t\t\tauth_data: {},\r\n\t\t\t\tshowshoporder:false,\r\n\t\t\t\tshowyuyueorder:false,\r\n\t\t\t\tshowcollageorder:false,\r\n\t\t\t\tshowkanjiaorder:false,\r\n\t\t\t\tshowseckillorder:false,\r\n\t\t\t\tshowscoreshoporder:false,\r\n\t\t\t\tshowluckycollageorder:false,\r\n\t\t\t\tshowtuangouorder:false,\r\n\t\t\t\tshowyuekeorder:false,\r\n\t\t\t\tshowmaidanlog:false,\r\n\t\t\t\tshowformlog:false,\r\n\t\t\t\tshowworkorder:false,\r\n\t\t\t\tworkorderCount:0,\r\n\t\t\t\tshowrecharge:false,\r\n\t\t\t\tshowrestaurantproduct:false,\r\n\t\t\t\tshowrestauranttable:false,\r\n\t\t\t\twxtmplset:{},\r\n\t\t\t\tsearchmember:false,\r\n\t\t\t\tshowCycleorder:false,\r\n\t\t\t\tscoreshop_product:false,\r\n\t\t\t\tscoreproductCount:0,\r\n\t\t\t\tcustom:{},//定制显示控制放一起\r\n\t\t\t\tisload:false,\r\n\t\t\t\ttoday_order_count:0,//进入订单数\r\n\t\t\t\ttoday_money:0,\r\n\t\t\t\trestaurant_shop_count:0,\r\n\t\t\t\trestaurant_takeaway_count:0,\r\n\t\t\t\trestaurant_booking_count:0,\r\n\t\t\t\trestaurant_queue:0,\r\n\t\t\t\trestaurant_deposit:0,\r\n\t\t\t\trestaurant_product_count:0,\r\n\t\t\t\trestaurant_table_count:0,\r\n\t\t\t\ttabIndex:1,\r\n\t\t\t\tshopDataShow:true,\r\n\t\t\t\tother_show:true,\r\n\t\t\t\tshowworkadd:false,\r\n\t\t\t\tshowbusinessqr:false,\r\n\t\t\t\tshow_categroy_business:false,\r\n\t\t\t\tshow_auth:[],\r\n\t\t\t\tadd_product:1,\r\n\t\t\t\thotel:0,\r\n        showmdmoney:0\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt){\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t\tlet sysinfo = uni.getSystemInfoSync();\r\n\t\t\tthis.statusBarHeight = sysinfo.statusBarHeight;\r\n\t\t},\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\t// 今日收款解释说明\r\n\t\t\texplanation(){\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '解释说明',\r\n\t\t\t\t\tcontent: `数据不含${app.t('余额')}支付`,\r\n\t\t\t\t\tshowCancel:false\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdetermineTabIndex(){\r\n\t\t\t\tif((this.auth_data.order && (this.showcollageorder || this.showkanjiaorder || this.showseckillorder || this.showscoreshoporder || this.showluckycollageorder || this.showyuyueorder || this.showCycleorder))){\r\n\t\t\t\t\tthis.tabIndex = 1;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(this.auth_data.restaurant_takeaway || this.auth_data.restaurant_shop || this.auth_data.restaurant_booking || this.auth_data.restaurant_queue || this.auth_data.restaurant_deposit){\r\n\t\t\t\t\t\tthis.tabIndex = 2;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif(this.auth_data.restaurant_product || this.auth_data.restaurant_tableWaiter){\r\n\t\t\t\t\t\t\tthis.tabIndex = 0;\r\n\t\t\t\t\t\t}else if(this.auth_data.hotel_order){\r\n\t\t\t\t\t\t\tthis.tabIndex = 4;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthis.shopDataShow = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// // 其他\r\n\t\t\t\t// if((this.auth_data.order && (this.showyuekeorder || this.showformlog || this.showmaidanlog || this.searchmember)) || this.custom.showHealth || this.showworkorder || this.scoreshop_product || inArray('member_code_buy',this.auth_data.hexiao_auth_data) || this.showbusinessqr || this.showworkadd || this.auth_data.product){\r\n\t\t\t\t// \tthis.other_show = true;\r\n\t\t\t\t// }else{\r\n\t\t\t\t// \tthis.other_show = false;\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\t// \r\n\t\t\ttabChange(type){\r\n\t\t\t\tthis.tabIndex = type\r\n\t\t\t},\r\n\t\t\t// 页面信息\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiAdminIndex/index', {}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.set = res.set;\r\n\t\t\t\t\tthat.show_auth = res.show_auth\r\n\t\t\t\t\tres.show_auth.filter(item => {\r\n\t\t\t\t\t\tif(item == 'ScoreshopProduct') that.scoreshop_product = true;\r\n\t\t\t\t\t\tif(item == 'ScoreshopOrder') that.showscoreshoporder = true;\r\n\t\t\t\t\t\tif(item == 'KanjiaOrder') that.showkanjiaorder = true;\r\n\t\t\t\t\t\tif(item == 'CollageOrder') that.showcollageorder = true;\r\n\t\t\t\t\t\tif(item == 'SeckillOrder') that.showseckillorder = true;\r\n\t\t\t\t\t\tif(item == 'TuangouOrder') that.showtuangouorder = true;\r\n\t\t\t\t\t\tif(item == 'LuckyCollageOrder') that.showluckycollageorder = true;\r\n\t\t\t\t\t\tif(item == 'YuyueOrder') that.showyuyueorder = true;\r\n\t\t\t\t\t\tif(item == 'CycleOrder') that.showCycleorder = true;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// that.wxtmplset = res.wxtmplset;\r\n\t\t\t\t\tthat.uinfo = res.uinfo;\r\n\t\t\t\t\tthat.count0 = res.count0;\r\n\t\t\t\t\tthat.count1 = res.count1;\r\n\t\t\t\t\tthat.count2 = res.count2;\r\n\t\t\t\t\tthat.count3 = res.count3;\r\n\t\t\t\t\tthat.count4 = res.count4;\r\n\t\t\t\t\tthat.seckillCount = res.seckillCount;\r\n\t\t\t\t\tthat.collageCount = res.collageCount;\r\n\t\t\t\t\tthat.cycleCount = res.cycleCount;\r\n\t\t\t\t\tthat.luckycollageCount = res.luckycollageCount;\r\n\t\t\t\t\tthat.kanjiaCount = res.kanjiaCount;\r\n\t\t\t\t\tthat.tuangouCount = res.tuangouCount;\r\n\t\t\t\t\tthat.scoreshopCount = res.scoreshopCount;\r\n\t\t\t\t\tthat.yuyueCount = res.yuyueCount;\r\n\t\t\t\t\tthat.maidanCount = res.maidanCount;\r\n\t\t\t\t\tthat.productCount = res.productCount;\r\n\t\t\t\t\tthat.hexiaoCount = res.hexiaoCount;\r\n\t\t\t\t\tthat.formlogCount = res.formlogCount;\r\n\t\t\t\t\tthat.auth_data = res.auth_data;\r\n\t\t\t\t\tthat.yuyueorderCount = res.yuyueorderCount;\r\n\t\t\t\t\tthat.workordercount = res.workordercount;\r\n\t\t\t\t\tthat.showshoporder = res.showshoporder;\r\n\t\t\t\t\tthat.hotelCount = res.hotelCount\r\n\t\t\t\t\t// that.showcollageorder = res.showcollageorder;\r\n\t\t\t\t\t// that.showCycleorder = res.showCycleorder;\r\n\t\t\t\t\t// that.showkanjiaorder = res.showkanjiaorder;\r\n\t\t\t\t\t// that.showseckillorder = res.showseckillorder;\r\n\t\t\t\t\t// that.showtuangouorder = res.showtuangouorder;\r\n\t\t\t\t\t// that.showscoreshoporder = res.showscoreshoporder;\r\n\t\t\t\t\t// that.showluckycollageorder = res.showluckycollageorder;\r\n\t\t\t\t\tthat.showmaidanlog = res.showmaidanlog;\r\n\t\t\t\t\tthat.showformlog = res.showformlog;\r\n\t\t\t\t\tthat.showworkorder = res.showworkorder;\r\n\t\t\t\t\t// that.showyuyueorder = res.showyuyueorder;\r\n\t\t\t\t\tthat.showrecharge = res.showrecharge;\r\n\t\t\t\t\tthat.showrestaurantproduct = res.showrestaurantproduct;\r\n\t\t\t\t\tthat.showrestauranttable = res.showrestauranttable;\r\n\t\t\t\t\tthat.searchmember = res.searchmember;\r\n\t\t\t\t\tthat.showyuekeorder = res.showyuekeorder;\r\n\t\t\t\t\t// that.scoreshop_product = res.scoreshop_product || false;\r\n\t\t\t\t\tthat.scoreproductCount = res.scoreproductCount || 0;\r\n\t\t\t\t\tthat.custom = res.custom;\r\n\t\t\t\t\tthat.today_order_count = res.today_order_count;\r\n\t\t\t\t\tthat.today_money = res.today_money;\r\n\t\t\t\t\tthat.restaurant_takeaway_count = res.restaurant_takeaway_count;\r\n\t\t\t\t\tthat.restaurant_shop_count = res.restaurant_shop_count;\r\n\t\t\t\t\tthat.restaurant_booking_count = res.restaurant_booking_count;\r\n\t\t\t\t\tthat.restaurant_queue = res.restaurant_queue;\r\n\t\t\t\t\tthat.restaurant_deposit = res.restaurant_deposit;\r\n\t\t\t\t\tthat.restaurant_product_count = res.restaurant_product_count;\r\n\t\t\t\t\tthat.restaurant_table_count = res.restaurant_table_count;\r\n\t\t\t\t\tthat.showworkadd = res.showworkadd;\r\n\t\t\t\t\tthat.showbusinessqr = res.showbusinessqr;\r\n\t\t\t\t\tthat.show_categroy_business = res.show_categroy_business;\r\n\t\t\t\t\tthat.add_product = res.add_product;\r\n\t\t\t\t\tthat.hotel = res.hotel;\r\n          that.showmdmoney = res.showmdmoney || 0;\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\tthat.determineTabIndex()\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsaoyisao: function (d) {\r\n\t\t\t  var that = this;\r\n\t\t\t\tif(app.globalData.platform == 'h5'){\r\n\t\t\t\t\tapp.alert('请使用微信扫一扫功能扫码核销');return;\r\n\t\t\t\t}else if(app.globalData.platform == 'mp'){\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tvar jweixin = require('jweixin-module');\r\n\t\t\t\t\tjweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用\r\n\t\t\t\t\t\tjweixin.scanQRCode({\r\n\t\t\t\t\t\t\tneedResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\r\n\t\t\t\t\t\t\tscanType: [\"qrCode\",\"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\r\n\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\tvar content = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\r\n\t\t\t\t\t\t\t\tvar params = content.split('?')[1];\r\n\t\t\t\t\t\t\t\tvar url = '/admin/hexiao/hexiao?'+params;\r\n\t\t\t\t\t\t\t\t//扫出餐码\r\n\t\t\t\t\t\t\t\tvar outurl = new Buffer(content, 'base64').toString('utf8')\r\n\t\t\t\t\t\t\t\tvar outparam = app.getparams('?'+outurl);\r\n\t\t\t\t\t\t\t\tif(outparam['type'] =='outfood'){\r\n\t\t\t\t\t\t\t\t\turl = '/restaurant/admin/outfood?'+outurl;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tapp.goto(url);\r\n\t\t\t\t\t\t\t\t//if(content.length == 18 && (/^\\d+$/.test(content))){ //是十八位数字 付款码\r\n\t\t\t\t\t\t\t\t//\tlocation.href = \"{:url('shoukuan')}/aid/{$aid}/auth_code/\"+content\r\n\t\t\t\t\t\t\t\t//}else{\r\n\t\t\t\t\t\t\t\t//\tlocation.href = content;\r\n\t\t\t\t\t\t\t\t//}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail:function(err){\r\n\t\t\t\t\t\t\t\tif(err.errMsg == 'scanQRCode:the permission value is offline verifying' || err.errMsg == 'scanQRCode:permission denied' || err.errMsg == 'permission denied'){\r\n\t\t\t\t\t\t\t\t\tapp.error('请先绑定公众号');\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tapp.error(err.errMsg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\t\t\t\t\t\r\n\t\t\t\t}else{\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tuni.scanCode({\r\n\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\tif(res.path){\r\n\t\t\t\t\t\t\t\t\tapp.goto('/'+res.path);\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tvar content = res.result;\r\n\t\t\t\t\t\t\t\tvar params = content.split('?')[1];\r\n\t\t\t\t\t\t\t\tvar url = '/admin/hexiao/hexiao?'+params;\r\n\t\t\t\t\t\t\t\t//扫出餐码\r\n\t\t\t\t\t\t\t\tvar outurl = new Buffer(content, 'base64').toString('utf8')\r\n\t\t\t\t\t\t\t\tvar outparam = app.getparams('?'+outurl);\r\n\t\t\t\t\t\t\t\tif(outparam['type'] =='outfood'){\r\n\t\t\t\t\t\t\t\t\turl = '/restaurant/admin/outfood?'+outurl;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tapp.goto(url);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail:function(err){\r\n\t\t\t\t\t\t\tapp.error(err.errMsg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t@import \"../common.css\";\r\n\tpage{background:#fff}\r\n\t.width{width: 95%;margin: 0 auto;}\r\n\t.view-width{width: 100%;height: auto;padding-bottom:60rpx}\r\n\t.head-class{}\r\n\t.head-view{\r\n\t\t/* #ifndef H5*/\r\n\t\tpadding: 10rpx 40rpx 15rpx 40rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef H5 */\r\n\t\tpadding: 30rpx 40rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\t.head-view .avat-view{display:flex;align-items: center;justify-content: flex-start;}\r\n\t.head-view .avat-view .avat-img-view {width: 80rpx;height:80rpx;border-radius: 50%;overflow: hidden;border:2rpx #fff solid;}\r\n\t.head-view .avat-view .avat-img-view image{width: 100%;height: 100%;}\r\n\t.head-view .avat-view .user-info{display: flex;align-items: flex-start;flex-direction: column;}\r\n\t.head-view .avat-view .user-info .nickname{font-size: 32rpx;font-weight: bold;}\r\n\t.head-view .avat-view .user-info .un-text{font-size: 24rpx;color: rgba(34, 34, 34, 0.7);}\r\n  .head-view .avat-view .imgback{width: 40rpx;height: 40rpx;margin-top: 4rpx;}\r\n\t.head-view .option-img-view{width: 160rpx;display: flex;align-items: center;justify-content: flex-end;}\r\n\t.head-view .recharge{background: #fff; width: 100rpx;color: #FB6534; text-align: center; font-size: 24rpx; padding: 5rpx; border-radius: 10rpx;margin-left: 20rpx;}\r\n\t.head-view .setup-view{position:relative;width: 64rpx;height:64rpx;}\r\n\t.head-view .setup-view image{width: 64rpx;height: 64rpx;}\r\n\t.head-view .setup-view .setup-img{\r\n\t\t/* #ifdef H5 */\r\n\t\t/* width: 48rpx;height: 48rpx;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%); */\r\n\t\t/* #endif */\r\n\t\t}\r\n\t.today-data{padding: 20rpx 65rpx 40rpx;justify-content:space-around }\r\n\t.option-view{width: 50%;}\r\n\t.option-view text{text-align: center;}\r\n\t.option-view .title-text{font-size: 24rpx;color: #5f6064;padding-bottom: 15rpx;}\r\n\t.option-view .title-text .title-icon{width: 30rpx;height: 30rpx;margin-left: 10rpx;}\r\n\t.option-view .num-text{font-size: 28rpx;font-weight: 700}\r\n\t.option-view .unit-money{font-size: 24rpx;font-weight: 700;}\r\n\t.mall-orders{border-radius:12rpx;overflow: hidden;}\r\n\t.order-title{padding: 32rpx 40rpx;align-items: center;background: linear-gradient(to right, #c4dfff , #d7e8ff);}\r\n\t.order-title .title-text{font-size: 26rpx;font-weight: 500;color: #222;}\r\n\t.order-title .all-text{font-size: 24rpx;color: #5f6064;}\r\n\t.order-title .title-text .left-img{width: 6rpx;height: 24rpx;margin-right: 12rpx;}\r\n\t.order-title .all-text .right-img{width: 10rpx;height: 20rpx;margin-left: 20rpx;}\r\n\t.order-list{justify-content: space-around;padding:40rpx 0rpx;background: #D5E8FF;}\r\n\t.order-list .option-order{align-items: center;}\r\n\t.order-list .option-order .num-text{font-size: 28rpx;font-weight: bold;padding-bottom:10rpx;}\r\n\t.order-list .option-order .title-text{font-size: 24rpx;color: #5f6064;}\r\n\t.meun-view{padding:40rpx;}\r\n\t.meun-view .meun-options .menu-img{width: 88rpx;height:88rpx;}\r\n\t.meun-view .meun-options .menu-text{font-size: 24rpx;color: #242424;margin-top:12rpx;}\r\n\t.menu-manage{margin-bottom:50rpx}\r\n\t.menu-manage .menu-title{font-size: 30rpx;color: #242424;font-weight:bold;padding: 10rpx 40rpx;}\r\n\t.menu-manage .menu-list{display: flex;align-items: center;flex-wrap: wrap;justify-items: flex-start;}\r\n\t.menu-manage .menu-list .meun-list-options{width: 16%;margin:4% 2%;}\r\n\t.menu-manage .menu-list .meun-list-options .menu-img{width:60rpx;height:60rpx;}\r\n\t.menu-manage .menu-list .meun-list-options .menu-text{font-size: 24rpx;color: #242424;margin-top: 20rpx;white-space: nowrap;}\r\n\t.menu-manage .divider-div{width: 100%;height:20rpx;background: #F2F3F4;}\r\n\t.menu-manage .tab-div{padding-top: 20rpx;}\r\n\t.menu-manage .tab-div .tab-options {height:100rpx;font-size:26rpx;color: #666666;justify-content:flex-start;align-items:center;padding:20rpx 40rpx 0rpx}\r\n\t.menu-manage .tab-div .tab-options-active{color:#3d7af7}\r\n\t.menu-manage .tab-div .tab-options .color-bar{width:48rpx;height:3px;background: #3d7af7;margin-top: 20rpx;}\r\n\t.menu-manage .data-div{padding: 0rpx 30rpx;align-items:center;justify-content:space-between;}\r\n\t.data-div-list{display: flex;flex-direction: row;width: 100%;align-items: center;justify-content: flex-start;flex-wrap:wrap;}\r\n\t.data-div-list .data-div-options{display: flex;flex-direction: column;align-items: center;justify-content: space-around;width: 22%;padding: 30rpx 0rpx;}\r\n\t.data-div-list .data-div-options .title-text{font-size: 24rpx;color: #5f6064;}\r\n\t.data-div-list .data-div-options .num-text{font-size: 28rpx;font-weight: bold;color: #222222;margin-top: 20rpx;}\r\n\t.data-div-list .border-bar-div{height: 40rpx;width: 3rpx;background:#e5e5e5;margin: 0rpx 12rpx;}\r\n\t.data-div-list .border-bar-div:nth-child(8n){height: 40rpx;width: 0rpx;background:red;margin: 0rpx 0rpx;}\r\n\r\n\t.data-div-list.hotelorder .data-div-options{ width: 20%;}\r\n\t\t\r\n\t\r\n\t.mer-list{padding: 20rpx 40rpx;display: flex;align-items: center;justify-content: space-between;}\r\n\t.mer-list .merchant-view {display: flex;flex-direction: column;align-items: flex-start;\r\n\twidth: 31%;height: 380rpx;border-radius:16rpx;padding: 0rpx 18rpx;background-repeat: no-repeat; background-size: cover;}\r\n\t.mer-list .merchant-view .mer-title{font-size: 24rpx;color: #242424;padding: 26rpx 0rpx;font-weight: 500;}\r\n\t.mer-list .merchant-view .mer-options{font-size: 20rpx;color: #7B7B7B;padding-bottom:20rpx;white-space: nowrap;\t/* #ifdef H5*/\ttransform: scale(0.8);\t/* #endif*/}\r\n\t.mer-list .merchant-view .mer-options text{padding: 0rpx 10rpx;font-size: 20rpx;text-align: left;}\r\n\t.cysj-text .mer-options text{color: #3F71E5;font-weight: 500;}\r\n\t.scdd-text .mer-options text{color: #FF9000;font-weight: 500;}\r\n\t.wmdd-text .mer-options text{color: #02B56A;font-weight: 500;}\r\n\t.scroll-Y{height: 280rpx;}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839448643\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}