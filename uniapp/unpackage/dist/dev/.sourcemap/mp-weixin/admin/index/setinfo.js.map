{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setinfo.vue?7ada", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setinfo.vue?4771", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setinfo.vue?254a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setinfo.vue?8c0b", "uni-app:///admin/index/setinfo.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setinfo.vue?9563", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setinfo.vue?e624"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "pic", "pics", "info", "latitude", "longitude", "address", "start_hours", "end_hours", "start_hours2", "end_hours2", "start_hours3", "end_hours3", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "cateChange", "locationSelect", "success", "bindStartHoursChange", "bindEndHoursChange", "bindStartHours2Change", "bindEndHours2Change", "bindStartHours3Change", "bindEndHours3Change", "subform", "setTimeout", "isagreeChange", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Hz1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAC;UACAC;QACA;QACA;QACA;QACA;UACArB;QACA;QACA;QACA;UACAG;QACA;UACAA;QACA;QACAe;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;MACAH;QACAI;UACAN;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IAEAO;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACAZ;QACA;MACA;MACA;QACA;QACA;MAAA;MAEA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACAf;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MAEAe;MACAA;QAAAf;MAAA;QACAe;QACA;UACAA;UACAa;YACAb;UACA;QACA;UACAA;QACA;MACA;IACA;IACAc;MACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAlB;QACA;UACAhB;QACA;QACA;QACA;QACA;MACA;IACA;IACAmC;MACA;MACA;MACA;MACA;QACA;QACAnC;QACAe;MACA;QACA;QACAf;QACAe;MACA;QACA;QACAf;QACAe;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9UA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/index/setinfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/index/setinfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setinfo.vue?vue&type=template&id=13e31516&\"\nvar renderjs\nimport script from \"./setinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./setinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setinfo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/index/setinfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setinfo.vue?vue&type=template&id=13e31516&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.pic.length : null\n  var g1 = _vm.isload ? _vm.pic.join(\",\") : null\n  var g2 = _vm.isload ? _vm.pics.length : null\n  var g3 = _vm.isload ? _vm.pics.join(\",\") : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setinfo.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"subform\">\n\t\t\t<view class=\"apply_box\">\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>商家名称<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.name\" :disabled=\"true\" placeholder=\"请输入商家名称\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>商家描述<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"desc\" :value=\"info.desc\" placeholder=\"请输入商家描述\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>店铺坐标<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" disabled placeholder=\"请选择坐标\" name=\"zuobiao\" :value=\"latitude ? latitude+','+longitude:''\" @tap=\"locationSelect\"></input></view>\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"latitude\" :value=\"latitude\"></input>\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"longitude\" :value=\"longitude\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>店铺地址<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"address\" :value=\"address\" placeholder=\"请输入商家详细地址\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>客服电话<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写客服电话\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<!-- <view class=\"apply_item\" style=\"line-height:50rpx;display: none;\"><textarea name=\"content\" placeholder=\"请输入商家简介\" :value=\"info.content\"></textarea></view> -->\n\t\t\t</view>\n\t\t\t<view class=\"apply_box\">\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><view>商家主图<text style=\"color:red\"> *</text></view></view>\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n\t\t\t\t\t<view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" v-if=\"pic.length==0\"></view>\n\t\t\t\t</view>\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"logo\" :value=\"pic.join(',')\" maxlength=\"-1\"></input>\n\t\t\t</view>\n\t\t\t<view class=\"apply_box\">\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><view>商家照片(3-5张)<text style=\"color:red\"> *</text></view></view>\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<5\"></view>\n\t\t\t\t</view>\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\n\t\t\t</view>\n\t\t\t<view class=\"apply_box\">\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><view>营业时间<text style=\"color:red\"> *</text></view></view>\n\t\t\t\t\t<view class=\"flex-col\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t<picker mode=\"time\" :value=\"start_hours\" @change=\"bindStartHoursChange\">\n\t\t\t\t\t\t\t <view class=\"picker\">{{start_hours}}</view>\n\t\t\t\t\t\t </picker>\n\t\t\t\t\t\t <view style=\"padding:0 30rpx;color:#222;\">到</view>\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_hours\" @change=\"bindEndHoursChange\">\n\t\t\t\t\t\t\t <view class=\"picker\">{{end_hours}}</view>\n\t\t\t\t\t\t </picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t<picker mode=\"time\" :value=\"start_hours2\" @change=\"bindStartHours2Change\">\n\t\t\t\t\t\t\t <view class=\"picker\">{{start_hours2}}</view>\n\t\t\t\t\t\t </picker>\n\t\t\t\t\t\t <view style=\"padding:0 30rpx;color:#222;\">到</view>\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_hours2\" @change=\"bindEndHours2Change\">\n\t\t\t\t\t\t\t <view class=\"picker\">{{end_hours2}}</view>\n\t\t\t\t\t\t </picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t<picker mode=\"time\" :value=\"start_hours3\" @change=\"bindStartHours3Change\">\n\t\t\t\t\t\t\t <view class=\"picker\">{{start_hours3}}</view>\n\t\t\t\t\t\t </picker>\n\t\t\t\t\t\t <view style=\"padding:0 30rpx;color:#222;\">到</view>\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_hours3\" @change=\"bindEndHours3Change\">\n\t\t\t\t\t\t\t <view class=\"picker\">{{end_hours3}}</view>\n\t\t\t\t\t\t </picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>打烊后接单<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t<radio-group class=\"radio-group\" name=\"end_buy_status\">\n\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.end_buy_status==1?true:false\"></radio> 开启</label> \n\t\t\t\t\t\t\t<label><radio value=\"0\" :checked=\"info.end_buy_status==0?true:false\"></radio> 关闭</label>\n\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>店铺状态<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t<radio-group class=\"radio-group\" name=\"is_open\">\n\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.is_open==1?true:false\"></radio> 营业</label> \n\t\t\t\t\t\t\t<label><radio value=\"0\" :checked=\"info.is_open==0?true:false\"></radio> 休息</label>\n\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\r\n        <block v-if=\"info.canset_paymoney_givepercent\">\n          <view class=\"apply_item\">\n            <view>让利比例(%)</view>\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"paymoney_givepercent\" :value=\"info.paymoney_givepercent\" placeholder=\"请填写让利比例\"></input></view>\n          </view>\r\n          \r\n          <view v-if=\"info.paymoney_givepercent2>=0\" class=\"apply_item\" style=\"color: red;\">\r\n            <view>待审核让利比例(%)</view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" :disabled=\"true\" :value=\"info.paymoney_givepercent2\" ></input></view>\r\n          </view>\r\n        </block>\n\t\t\t</view>\n\t\t\t<view style=\"padding:30rpx 0\"><button form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">确 定</button></view>\n\t\t</form>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      datalist: [],\n      pagenum: 1,\n      cateArr: [],\n      cindex: 0,\n\t\t\tpic:[],\n\t\t\tpics:[],\n      info: {},\n      latitude: '',\n      longitude: '',\n      address:'',\n\t\t\tstart_hours:'00:00',\n\t\t\tend_hours:'00:00',\n\t\t\tstart_hours2:'00:00',\n\t\t\tend_hours2:'00:00',\n\t\t\tstart_hours3:'00:00',\n\t\t\tend_hours3:'00:00',\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminIndex/setinfo', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 2) {\n\t\t\t\t\tapp.alert(res.msg, function () {\n\t\t\t\t\t\tapp.goto('/admin/index/index', 'redirect');\n\t\t\t\t\t})\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: res.title\n\t\t\t\t});\n\t\t\t\tvar clist = res.clist;\n\t\t\t\tvar cateArr = [];\n\t\t\t\tfor (var i in clist) {\n\t\t\t\t\tcateArr.push(clist[i].name);\n\t\t\t\t}\n\t\t\t\tvar pics = res.info ? res.info.pics : '';\n\t\t\t\tif (pics) {\n\t\t\t\t\tpics = pics.split(',');\n\t\t\t\t} else {\n\t\t\t\t\tpics = [];\n\t\t\t\t}\n\t\t\t\tthat.pics = pics;\n\t\t\t\tthat.info = res.info;\n        that.address = res.info.address;\n        that.latitude = res.info.latitude;\n        that.longitude = res.info.longitude;\n\t\t\t\tthat.cateArr = cateArr;\n\t\t\t\tthat.pic = res.info.logo ? [res.info.logo] : [];\n\t\t\t\tthat.start_hours = res.info.start_hours ? res.info.start_hours : '00:00';\n\t\t\t\tthat.end_hours = res.info.end_hours ? res.info.end_hours : '00:00';\n\t\t\t\tthat.start_hours2 = res.info.start_hours2 ? res.info.start_hours2 : '00:00';\n\t\t\t\tthat.end_hours2 = res.info.end_hours2 ? res.info.end_hours2 : '00:00';\n\t\t\t\tthat.start_hours3 = res.info.start_hours3 ? res.info.start_hours3 : '00:00';\n\t\t\t\tthat.end_hours3 = res.info.end_hours3 ? res.info.end_hours3 : '00:00';\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    cateChange: function (e) {\n      this.cindex = e.detail.value;\n    },\n    locationSelect: function () {\n      var that = this;\n      uni.chooseLocation({\n        success: function (res) {\n          that.info.address = res.name;\n\t\t\t\t\tthat.info.latitude = res.latitude;\n          that.info.longitude = res.longitude;\n          that.address = res.name;\n          that.latitude = res.latitude;\n          that.longitude = res.longitude;\n        }\n      });\n    },\n\t\t\n\t\tbindStartHoursChange:function(e){\n\t\t\tthis.start_hours = e.target.value\n\t\t},\n\t\tbindEndHoursChange:function(e){\n\t\t\tthis.end_hours = e.target.value\n\t\t},\n\t\t\n\t\tbindStartHours2Change:function(e){\n\t\t\tthis.start_hours2 = e.target.value\n\t\t},\n\t\tbindEndHours2Change:function(e){\n\t\t\tthis.end_hours2 = e.target.value\n\t\t},\n\t\t\n\t\tbindStartHours3Change:function(e){\n\t\t\tthis.start_hours3 = e.target.value\n\t\t},\n\t\tbindEndHours3Change:function(e){\n\t\t\tthis.end_hours3 = e.target.value\n\t\t},\n    subform: function (e) {\n      var that = this;\n      var info = e.detail.value;\n      if (info.tel == '') {\n        app.error('请填写客服电话');\n        return false;\n      }\n      if (info.zuobiao == '') {\n        //app.error('请选择店铺坐标');\n        //return false;\n      }\n      if (info.address == '') {\n        app.error('请填写店铺地址');\n        return false;\n      }\n      if (info.pic == '') {\n        app.error('请上传商家主图');\n        return false;\n      }\n      if (info.pics == '') {\n        app.error('请上传商家照片');\n        return false;\n      }\n      info.address = that.address;\n      info.latitude = that.latitude;\n      info.longitude = that.longitude;\n\t\t\tinfo.start_hours = that.start_hours ? that.start_hours : '00:00';\n\t\t\tinfo.end_hours = that.end_hours ? that.end_hours : '00:00';\n\t\t\tinfo.start_hours2 = that.start_hours2 ? that.start_hours2 : '00:00';\n\t\t\tinfo.end_hours2 = that.end_hours2 ? that.end_hours2 : '00:00';\n\t\t\tinfo.start_hours3 = that.start_hours3 ? that.start_hours3 : '00:00';\n\t\t\tinfo.end_hours3 = that.end_hours3 ? that.end_hours3 : '00:00';\n\n\t\t\tapp.showLoading('提交中');\n      app.post(\"ApiAdminIndex/setinfo\", {info: info}, function (res) {\n\t\t\t\tapp.showLoading(false);\n        if (res.status == 1) {\n          app.success(res.msg);\n          setTimeout(function () {\n            app.goto('index');\n          }, 1000);\n        } else {\n          app.error(res.msg);\n        }\n      });\n    },\n    isagreeChange: function (e) {\n      console.log(e.detail.value);\n      var val = e.detail.value;\n      if (val.length > 0) {\n        this.isagree = true;\n      } else {\n        this.isagree = false;\n      }\n    },\n    showxieyiFun: function () {\n      this.showxieyi = true;\n    },\n    hidexieyi: function () {\n      this.showxieyi = false;\n\t\t\tthis.isagree = true;\n    },\n\t\tuploadimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tif(!pics) pics = [];\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tfor(var i=0;i<urls.length;i++){\n\t\t\t\t\tpics.push(urls[i]);\n\t\t\t\t}\n\t\t\t\tif(field == 'pic') that.pic = pics;\n\t\t\t\tif(field == 'pics') that.pics = pics;\n\t\t\t\tif(field == 'zhengming') that.zhengming = pics;\n\t\t\t},1)\n\t\t},\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index= e.currentTarget.dataset.index\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tif(field == 'pic'){\n\t\t\t\tvar pics = that.pic\n\t\t\t\tpics.splice(index,1);\n\t\t\t\tthat.pic = pics;\n\t\t\t}else if(field == 'pics'){\n\t\t\t\tvar pics = that.pics\n\t\t\t\tpics.splice(index,1);\n\t\t\t\tthat.pics = pics;\n\t\t\t}else if(field == 'zhengming'){\n\t\t\t\tvar pics = that.zhengming\n\t\t\t\tpics.splice(index,1);\n\t\t\t\tthat.zhengming = pics;\n\t\t\t}\n\t\t},\n  }\n}\n</script>\n<style>\nradio{transform: scale(0.6);}\ncheckbox{transform: scale(0.6);}\n.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\n.apply_title { background: #fff}\n.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\n.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\n\n.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\n.apply_box .apply_item:last-child{ border:none}\n.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\n.apply_item input::placeholder{ color:#999999}\n.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\n.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\n.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\n.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setinfo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setinfo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839442801\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}