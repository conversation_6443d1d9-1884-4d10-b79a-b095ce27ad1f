{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setpage.vue?02d9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setpage.vue?83dc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setpage.vue?633c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setpage.vue?4c98", "uni-app:///admin/index/setpage.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setpage.vue?5e2b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/index/setpage.vue?ec0d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "set", "uinfo", "count0", "count1", "count2", "count3", "count4", "seckill<PERSON>ount", "collageCount", "kanjia<PERSON>ount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scoreshopCount", "maidanCount", "productCount", "cycleCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formlogCount", "auth_data", "showshoporder", "showbusinessqr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showcollageorder", "showkanjiaorder", "showseckillorder", "showscoreshoporder", "showluckycollageorder", "showtuangouorder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showmaidanlog", "showformlog", "showworkorder", "showworkadd", "workorderCount", "showrecharge", "wxtmplset", "searchmember", "showCycleorder", "scoreshop_product", "scoreproductCount", "custom", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "switchchange", "console", "field", "value", "switchOpen", "addsubnum"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyHz1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAE;MACAC;MACA;MACA;MACAF;QACAG;QACAC;MACA;IACA;IACAC;MACA;MACA;MACAL;QACAG;QACAC;MACA;IACA;IACAE;MACA;MACAP;MACAG;MACAH;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjQA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/index/setpage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/index/setpage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setpage.vue?vue&type=template&id=1ead9ad6&\"\nvar renderjs\nimport script from \"./setpage.vue?vue&type=script&lang=js&\"\nexport * from \"./setpage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setpage.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/index/setpage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setpage.vue?vue&type=template&id=1ead9ad6&\"", "var components\ntry {\n  components = {\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setpage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setpage.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t<view class=\"contentdata\">\n\t\t<view class=\"list\">\n\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"head-view\">\n\t\t\t\t\t\t<view class=\"avat-view\"  @tap=\"goto\" :data-url=\"uinfo.bid==0 ? '/pages/index/index' : '/pagesExt/business/index?id='+uinfo.bid\">\n\t\t\t\t\t\t\t<view class=\"avat-img-view\"><image :src=\"set.logo\"></image></view>\n\t\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t\t <text class=\"un-text\">{{uinfo.un}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex flex-y-center\">\n\t\t\t\t\t\t\t<view v-if=\"showrecharge\" class=\"recharge\" @tap=\"goto\" data-url=\"recharge\">充值</view>\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"f4\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"list\" v-if=\"false\">\n\t\t\t<view v-if=\"uinfo.tmpl_orderconfirm_show==1\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f2\">订单提交通知</view>\n\t\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"uinfo.tmpl_orderconfirm==1?true:false\" @change=\"switchchange\" data-type=\"tmpl_orderconfirm\"></switch></view>\n\t\t\t\t</view>\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-bottom:10rpx\" @tap=\"addsubnum\" :data-tmplid=\"wxtmplset.tmpl_orderconfirm\">剩余可接收次数：<text style=\"color:#FC5648;font-weight:bold;font-size:30rpx\">{{uinfo.tmpl_orderconfirmNum}}</text>，每点击此处一次可增加一次机会</view>\n\t\t\t\t<!--  #endif -->\n\t\t\t</view>\n\t\t\t<view v-if=\"uinfo.tmpl_orderpay_show==1\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f2\">订单支付通知</view>\n\t\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"uinfo.tmpl_orderpay==1?true:false\" @change=\"switchchange\" data-type=\"tmpl_orderpay\"></switch></view>\n\t\t\t\t</view>\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-bottom:10rpx\" @tap=\"addsubnum\" :data-tmplid=\"wxtmplset.tmpl_orderconfirm\">剩余可接收次数：<text style=\"color:#FC5648;font-weight:bold;font-size:30rpx\">{{uinfo.tmpl_orderconfirmNum}}</text>，每点击此处一次可增加一次机会</view>\n\t\t\t\t<!--  #endif -->\n\t\t\t</view>\n\t\t\t<view v-if=\"uinfo.tmpl_ordershouhuo_show==1\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f2\">订单收货通知</view>\n\t\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"uinfo.tmpl_ordershouhuo==1?true:false\" @change=\"switchchange\" data-type=\"tmpl_ordershouhuo\"></switch></view>\n\t\t\t\t</view>\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-bottom:10rpx\" @tap=\"addsubnum\" :data-tmplid=\"wxtmplset.tmpl_ordershouhuo\">剩余可接收次数：<text style=\"color:#FC5648;font-weight:bold;font-size:30rpx\">{{uinfo.tmpl_ordershouhuoNum}}</text>，每点击此处一次可增加一次机会</view>\n\t\t\t\t<!--  #endif -->\n\t\t\t</view>\n\t\t\t<view v-if=\"uinfo.tmpl_ordertui_show==1\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f2\">退款申请通知</view>\n\t\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"uinfo.tmpl_ordertui==1?true:false\" @change=\"switchchange\" data-type=\"tmpl_ordertui\"></switch></view>\n\t\t\t\t</view>\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-bottom:10rpx\" @tap=\"addsubnum\" :data-tmplid=\"wxtmplset.tmpl_ordertui\">剩余可接收次数：<text style=\"color:#FC5648;font-weight:bold;font-size:30rpx\">{{uinfo.tmpl_ordertuiNum}}</text>，每点击此处一次可增加一次机会</view>\n\t\t\t\t<!--  #endif -->\n\t\t\t</view>\n\t\t\t<view v-if=\"uinfo.tmpl_withdraw_show==1\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f2\">提现申请通知</view>\n\t\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"uinfo.tmpl_withdraw==1?true:false\" @change=\"switchchange\" data-type=\"tmpl_withdraw\"></switch></view>\n\t\t\t\t</view>\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-bottom:10rpx\" @tap=\"addsubnum\" :data-tmplid=\"wxtmplset.tmpl_withdraw\">剩余可接收次数：<text style=\"color:#FC5648;font-weight:bold;font-size:30rpx\">{{uinfo.tmpl_withdrawNum}}</text>，每点击此处一次可增加一次机会</view>\n\t\t\t\t<!--  #endif -->\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"uinfo.tmpl_formsub_show==1\">\n\t\t\t\t<view class=\"f2\">表单提交通知</view>\n\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"uinfo.tmpl_formsub==1?true:false\" @change=\"switchchange\" data-type=\"tmpl_formsub\"></switch></view>\n\t\t\t</view>\n\t\t\t<view v-if=\"uinfo.tmpl_kehuzixun_show==1\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f2\">用户咨询通知</view>\n\t\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"uinfo.tmpl_kehuzixun==1?true:false\" @change=\"switchchange\" data-type=\"tmpl_kehuzixun\"></switch></view>\n\t\t\t\t</view>\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-bottom:30rpx\" @tap=\"addsubnum\" :data-tmplid=\"wxtmplset.tmpl_kehuzixun\">剩余可接收次数：<text style=\"color:#FC5648;font-weight:bold;font-size:30rpx\">{{uinfo.tmpl_kehuzixunNum}}</text>，每点击此处一次可增加一次机会</view>\n\t\t\t\t<!--  #endif -->\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"list\">\n\t\t\t<view class=\"item\" v-if=\"uinfo.bid>0\">\n\t\t\t\t<view class=\"f2\">店铺休息</view>\n\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"set.is_open==0?true:false\" @change=\"switchOpen\" data-type=\"is_open\"></switch></view>\n\t\t\t</view>\n\t\t\t<view v-if=\"uinfo.bid>0\" style=\"color:#999;font-size:24rpx;margin-bottom:30rpx\" >休息时不接单</view>\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"setinfo\" v-if=\"uinfo.bid>0\">\n\t\t\t\t<view class=\"f2\">店铺设置</view>\n\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"f4\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"setpwd\">\n\t\t\t\t<view class=\"f2\">修改密码</view>\n\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"f4\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"../index/login\">\n\t\t\t\t<view class=\"f2\">切换账号</view>\n\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"f4\"></image>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t</block>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\t<!-- \n\t一键查看\n\t约课记录\n\t添加商品\n\t量表填写记录\n\t兑换商品列表\n\t添加兑换商品\n\t店铺设置\n\t店铺休息\n\t工单提交\n\t推广码\n\t推广码\n\t -->\n<script>\nvar app = getApp();\nexport default {\n  data() {\n\t\treturn {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\t\n\t\t\tset:{},\n\t\t\tuinfo:{},\n      count0: 0,\n      count1: 0,\n      count2: 0,\n      count3: 0,\n      count4: 0,\n      seckillCount: 0,\n      collageCount: 0,\n      kanjiaCount: 0,\n\t\t\ttuangouCount:0,\n      scoreshopCount: 0,\n      maidanCount: 0,\n      productCount: 0,\n\t\t\tcycleCount: 0,\n      hexiaoCount: 0,\n\t\t\tformlogCount:0,\n      auth_data: {},\n\t\t\tshowshoporder:false,\n\t\t\tshowbusinessqr:false,\n\t\t\tshowyuyueorder:false,\n\t\t\tshowcollageorder:false,\n\t\t\tshowkanjiaorder:false,\n\t\t\tshowseckillorder:false,\n\t\t\tshowscoreshoporder:false,\n\t\t\tshowluckycollageorder:false,\n\t\t\tshowtuangouorder:false,\n\t\t\tshowyuekeorder:false,\n\t\t\tshowmaidanlog:false,\n\t\t\tshowformlog:false,\n\t\t\tshowworkorder:false,\n\t\t\tshowworkadd:false,\n\t\t\tworkorderCount:0,\n\t\t\tshowrecharge:false,\n\t\t\twxtmplset:{},\n\t\t\tsearchmember:false,\n\t\t\tshowCycleorder:false,\n\t\t\tscoreshop_product:false,\n\t\t\tscoreproductCount:0,\n\t\t\tcustom:{},//定制显示控制放一起\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminIndex/index', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.set = res.set;\n\t\t\t\tthat.wxtmplset = res.wxtmplset;\n\t\t\t\tthat.uinfo = res.uinfo;\n\t\t\t\tthat.count0 = res.count0;\n\t\t\t\tthat.count1 = res.count1;\n\t\t\t\tthat.count2 = res.count2;\n\t\t\t\tthat.count3 = res.count3;\n\t\t\t\tthat.count4 = res.count4;\n\t\t\t\tthat.seckillCount = res.seckillCount;\n\t\t\t\tthat.collageCount = res.collageCount;\n\t\t\t\tthat.cycleCount = res.cycleCount;\n\t\t\t\tthat.luckycollageCount = res.luckycollageCount;\n\t\t\t\tthat.kanjiaCount = res.kanjiaCount;\n\t\t\t\tthat.tuangouCount = res.tuangouCount;\n\t\t\t\tthat.scoreshopCount = res.scoreshopCount;\n\t\t\t\tthat.yuyueCount = res.yuyueCount;\n\t\t\t\tthat.maidanCount = res.maidanCount;\n\t\t\t\tthat.productCount = res.productCount;\n\t\t\t\tthat.hexiaoCount = res.hexiaoCount;\n\t\t\t\tthat.formlogCount = res.formlogCount;\n\t\t\t\tthat.auth_data = res.auth_data;\n\t\t\t\tthat.showbusinessqr = res.showbusinessqr;\n\t\t\t\tthat.workordercount = res.workordercount;\n\t\t\t\tthat.showshoporder = res.showshoporder;\n\t\t\t\tthat.showcollageorder = res.showcollageorder;\n\t\t\t\tthat.showCycleorder = res.showCycleorder;\n\t\t\t\tthat.showkanjiaorder = res.showkanjiaorder;\n\t\t\t\tthat.showseckillorder = res.showseckillorder;\n\t\t\t\tthat.showtuangouorder = res.showtuangouorder;\n\t\t\t\tthat.showscoreshoporder = res.showscoreshoporder;\n\t\t\t\tthat.showluckycollageorder = res.showluckycollageorder;\n\t\t\t\tthat.showmaidanlog = res.showmaidanlog;\n\t\t\t\tthat.showformlog = res.showformlog;\n\t\t\t\tthat.showworkorder = res.showworkorder;\n\t\t\t\tthat.showworkadd = res.showworkadd;\n\t\t\t\tthat.showyuyueorder = res.showyuyueorder;\n\t\t\t\tthat.showrecharge = res.showrecharge;\n\t\t\t\tthat.searchmember = res.searchmember;\n\t\t\t\tthat.showyuekeorder = res.showyuekeorder;\n\t\t\t\tthat.scoreshop_product = res.scoreshop_product || false;\n\t\t\t\tthat.scoreproductCount = res.scoreproductCount || 0;\n\t\t\t\tthat.custom = res.custom\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    switchchange: function (e) {\n      console.log(e);\n      var field = e.currentTarget.dataset.type;\n      var value = e.detail.value ? 1 : 0;\n      app.post('ApiAdminIndex/setusertmpl', {\n        field: field,\n        value: value\n      }, function (data) {});\n    },\n\t\tswitchOpen: function (e) {\n      var field = e.currentTarget.dataset.type;\n      var value = e.detail.value ? 0 : 1;\n      app.post('ApiAdminIndex/setField', {\n        field: field,\n        value: value\n      }, function (data) {});\n    },\n\t\taddsubnum:function(e){\t\n      var that = this;\n\t\t\tthat.tmplids = [e.currentTarget.dataset.tmplid];\n\t\t\tconsole.log(that.tmplids);\n\t\t\tthat.subscribeMessage(function () {\n\t\t\t\tthat.getdata();\n\t\t\t});\n    }\n  }\n};\n</script>\n<style>\n@import \"../common.css\";\n.head-view{display:flex;align-items: center;justify-content: space-between;width: 100%;}\n.head-view .avat-view{display:flex;align-items: center;justify-content: flex-start;}\n.head-view .avat-view .avat-img-view {width: 80rpx;height:80rpx;border-radius: 50%;overflow: hidden;}\n.head-view .avat-view .avat-img-view image{width: 100%;height: 100%;}\n.head-view .avat-view .user-info{margin-left: 20rpx;}\n.head-view .avat-view .user-info .un-text{font-size: 28rpx;color: rgba(34, 34, 34, 0.7);}\n.head-view .recharge{width: 100rpx;color: #FB6534; text-align: center; font-size: 24rpx;}\n.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;position:relative;margin-bottom:20rpx}\n.money{ display:flex;width:100%;align-items:center;padding:10rpx 20rpx;background:#fff;}\n.money .f1{flex:auto}\n.money .f1 .t2{color:#ff3300}\n.money .f2{ background:#fff;color:#ff3300;border:1px solid #ff3300;height:50rpx;line-height:50rpx;padding:0 14rpx;font-size: 28rpx;}\n\n.score{ display:flex;width:100%;align-items:center;padding:10rpx 20rpx;background:#fff;border-top:1px dotted #eee}\n.score .f1 .t2{color:#ff3300}\n\n.agent{width:100%;background:#fff;padding:0px 20rpx;margin-top:20rpx}\n.agent .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:1px solid #eee}\n.agent .head .f1{flex:auto;}\n.agent .head .f2{ display:flex;align-items:center;color:#999;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}\n.agent .head .f2 image{ width:30rpx;height:30rpx;}\n.agent .head .t3{ width: 40rpx; height: 40rpx;}\n.agent .content{ display:flex;width:100%;padding:10rpx 0;align-items:center;font-size:24rpx}\n.agent .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\n.agent .content .item image{ width:50rpx;height:50rpx}\n.agent .content .item .t3{ padding-top:3px}\n.agent .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;}\n\n.list{ width: 100%;background: #fff;margin-top:20rpx;padding:0 20rpx;font-size:30rpx;border-radius:16rpx}\n.list .item{ height:100rpx;display:flex;align-items:center;border-bottom:0px solid #eee}\n.list .item:last-child{border-bottom:0;}\n.list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center;}\n.list .f1 image{ width:40rpx;height:40rpx;}\n.list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}\n.list .f2{color:#222}\n.list .f3{ color: #FC5648;text-align:right;flex:1;}\n.list .f4{ width: 24rpx; height: 24rpx;}\n\nswitch{transform:scale(.7);}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setpage.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setpage.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839448680\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}