{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/category4.vue?07d2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/category4.vue?5af6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/category4.vue?67c5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/category4.vue?9adb", "uni-app:///pages/shop/category4.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/category4.vue?07b7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/category4.vue?61f0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "currentActiveIndex", "animation", "clist", "bid", "pre_url", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "clickRootItem", "gotoCatproductPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8C31B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAJ;MACAK;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAP;MAAA;QACAM;QACAA;QACAA;MACA;IACA;IACAE;MACA;MACA;IACA;IACAC;MACA;MACA;QACAF;MACA;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/shop/category4.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/shop/category4.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./category4.vue?vue&type=template&id=2c9e1d54&\"\nvar renderjs\nimport script from \"./category4.vue?vue&type=script&lang=js&\"\nexport * from \"./category4.vue?vue&type=script&lang=js&\"\nimport style0 from \"./category4.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/shop/category4.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category4.vue?vue&type=template&id=2c9e1d54&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.data, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g0 = _vm.isload ? _vm.data[_vm.currentActiveIndex].child.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category4.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category4.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"view-show\">\n\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/pages/shop/search?bid='+bid\" class=\"search-container\">\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t\t<view class=\"search-text\">搜索感兴趣的商品</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"content\" style=\"height:calc(100% - 94rpx);overflow:hidden;display:flex\">\n\t\t\t\t<scroll-view class=\"nav_left\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t<block v-for=\"(item, index) in data\" :key=\"index\" >\n\t\t\t\t\t<view class=\"nav_left_items\" :class=\"index===currentActiveIndex?'active':''\" @tap=\"clickRootItem\" :data-root-item-id=\"item.id\" :data-root-item-index=\"index\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"nav_right\">\n\t\t\t\t\t<view class=\"nav_right-content\">\n\t\t\t\t\t\t<scroll-view class=\"detail-list\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t\t\t<view v-if=\"data[currentActiveIndex].banner\">\n\t\t\t\t\t\t\t\t<image :src=\"data[currentActiveIndex].banner\" style=\"width:100%;height:auto;margin-bottom:40rpx\" mode=\"widthFix\" @tap=\"gotoCatproductPage\" :data-id=\"data[currentActiveIndex].id\"/>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"head\" v-else>\n\t\t\t\t\t\t\t\t<view class=\"txt\">{{data[currentActiveIndex].name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"show-all\" @tap=\"gotoCatproductPage\" :data-id=\"data[currentActiveIndex].id\">查看全部<text class=\"iconfont iconjiantou\"></text></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t\t<view v-for=\"(item, itemIndex) in data[currentActiveIndex].child\" :key=\"itemIndex\" @tap.stop=\"gotoCatproductPage\" class=\"detail-item\" :data-id=\"item.id\" form-type=\"submit\" :style=\"(itemIndex+1)%3===0?'margin-right: 0':''\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.pic\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<nodata v-if=\"(data[currentActiveIndex].child).length == 0\"></nodata>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\" loadstyle=\"left:62.5%\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n      data: [],\n      currentActiveIndex: 0,\n      animation: true,\n      clist: \"\",\n\t\t\tbid:'',\n\t\t\tpre_url:app.globalData.pre_url,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.bid = this.opt.bid ? this.opt.bid : '';\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiShop/category2', {bid:that.bid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.data = res.data;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    clickRootItem: function (t) {\n      var e = t.currentTarget.dataset;\n      this.currentActiveIndex = e.rootItemIndex;\n    },\n    gotoCatproductPage: function (t) {\n      var e = t.currentTarget.dataset;\n\t\t\tif(this.bid){\n\t\t\t\tapp.goto('/pages/shop/prolist?bid='+this.bid+'&cid2=' + e.id);\n\t\t\t}else{\n\t\t\t\tapp.goto('/pages/shop/prolist?cid=' + e.id);\n\t\t\t}\n    }\n  }\n};\n</script>\n<style>\npage {position: relative;width: 100%;height: 100%;}\nbutton {border: 0 solid!important;}\n.container{height:100%}\n.view-show{background-color: white;line-height: 1;width: 100%;height: 100%;}\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\n\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\n.nav_left .nav_left_items{line-height:50rpx;color:#666666;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\n.nav_left .nav_left_items.active{background: #fff;color:#222222;font-size:28rpx;font-weight:bold}\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\n.nav_left .nav_left_items.active .before{display:block}\n\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}\n.nav_right-content{background: #ffffff;padding:20rpx;height:100%;position:relative}\n.detail-list {height:100%;overflow:scroll}\n.detail-list .head {height: 82rpx;width: 100%;display: flex;align-items:center;justify-content:space-between;margin-bottom:40rpx}\n.detail-list .head .txt {color:#222222;font-weight:bold;font-size:28rpx;}\n.detail-list .head .show-all {font-size: 22rpx;color:#949494;display:flex;align-items:center}\n.detail-list .detail {width:100%;display:flex;flex-wrap:wrap}\n.detail-list .detail .detail-item {width:160rpx;height: 160rpx;margin-bottom:70rpx;}\n.detail-list .detail .detail-item .img {width: 112rpx;height: 112rpx;margin-left:24rpx}\n.detail-list .detail .detail-item .txt {color:#333;font-size: 28rpx;margin-top:20rpx;text-align: center;white-space: nowrap;word-break: break-all;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;}\n\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category4.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./category4.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839377744\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}