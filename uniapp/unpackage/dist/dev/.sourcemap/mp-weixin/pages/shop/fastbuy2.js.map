{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy2.vue?1877", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy2.vue?ef37", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy2.vue?a10b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy2.vue?a1ad", "uni-app:///pages/shop/fastbuy2.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy2.vue?0977", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy2.vue?4a1b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "buydialogShow", "harr", "business", "datalist", "menuList", "cartList", "numtotal", "proid", "totalprice", "currentActiveIndex", "animation", "scrollToViewId", "commentlist", "comment_nodata", "comment_nomore", "paylist", "bid", "scrollState", "custom", "mendianid", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "inpurCart", "that", "app", "ggid", "input_num", "getdata", "mendian_id", "uni", "title", "console", "changetab", "scrollTop", "duration", "getCommentList", "pagenum", "id", "clickRootItem", "setTimeout", "addcart", "num", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "currentTarget", "dataset", "clearShopCartFn", "content", "success", "gopay", "prodata", "gotoCatproductPage", "scroll", "countH", "buydialogChange", "handleClickMask", "openLocation", "latitude", "longitude", "name", "scale", "createpayorder"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwO11B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;MACA;QACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAApB;QAAAqB;QAAAC;MAAA;QACAH;QACA;UACAA;QACA;UACAA;UACAC;QACA;MACA;IACA;IACAG;MACA;MACAJ;MACAC;QAAAX;QAAAe;MAAA;QACAL;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAM;UACAC;QACA;QAEA;UACAP;QACA;;QAEA;QACA;QACA;QACA;QACAQ;QACA;UACA;UACAA;UACAjC;QACA;QACAyB;QACA;UACAA;UACA;YACA;UACA;QACA;QACAA;MACA;IACA;IACAS;MACA;MACA;MACA;MACAH;QACAI;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAZ;MACAA;MACAA;MACA;QACAC;UAAAY;UAAAhC;QAAA;UACAmB;UACAM;UACA;UACA;YACAN;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;YACA;UACA;QACA;MACA;QACAC;UAAAa;UAAAzC;UAAAwC;QAAA;UACAb;UACAM;UACA;UACA;YACAN;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;IACAe;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAjB;MACAC;QAAApB;QAAAqB;QAAAgB;MAAA;QACAlB;QACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAkB;MACAC;MACA;QAAAC;UAAAC;QAAA;MAAA;IACA;IACAC;MACA;MACAjB;QACAC;QACAiB;QACAC;UACA;YACAxB;cAAAX;YAAA;cACAU;YACA;UACA;YACAQ;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;QACAzB;QACA;MACA;MACA;MACA;QACA0B;MACA;MACA1B;IACA;IACA2B;MACA;MACA3B;IACA;IACA4B;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA3B;QACA4B;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACArC;MACAA;QAAAa;MAAA;QACA;UACAb;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9gBA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/shop/fastbuy2.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/shop/fastbuy2.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fastbuy2.vue?vue&type=template&id=f88c28d0&\"\nvar renderjs\nimport script from \"./fastbuy2.vue?vue&type=script&lang=js&\"\nexport * from \"./fastbuy2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fastbuy2.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/shop/fastbuy2.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy2.vue?vue&type=template&id=f88c28d0&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.paylist && _vm.paylist.length > 0 : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.menuList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var m1 = _vm.isload && !g0 ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && !g0 ? _vm.t(\"color1\") : null\n  var g1 = _vm.isload && !g0 ? !_vm.paylist || _vm.paylist.length == 0 : null\n  var m3 = _vm.isload && !g0 && g1 ? _vm.t(\"color1\") : null\n  var l1 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = index === _vm.currentActiveIndex ? _vm.t(\"color1\") : null\n          var m5 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var l4 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.datalist, function (detail, index) {\n          var $orig = _vm.__get_orig(detail)\n          var l3 = _vm.__map(detail.prolist, function (item, indexs) {\n            var $orig = _vm.__get_orig(item)\n            var m6 =\n              _vm.custom.product_show_sellpoint && item.sellpoint\n                ? _vm.t(\"color2\")\n                : null\n            var m7 =\n              item.price_type != 1 || item.sell_price > 0\n                ? _vm.t(\"color1\")\n                : null\n            var m8 =\n              item.price_type != 1 || item.sell_price > 0\n                ? !_vm.isNull(item.service_fee) &&\n                  item.service_fee_switch &&\n                  item.service_fee > 0\n                : null\n            var m9 =\n              (item.price_type != 1 || item.sell_price > 0) && m8\n                ? _vm.t(\"color1\")\n                : null\n            var m10 =\n              (item.price_type != 1 || item.sell_price > 0) && m8\n                ? _vm.t(\"服务费\")\n                : null\n            var m11 =\n              (item.price_type != 1 || item.sell_price > 0) &&\n              _vm.custom.product_weight &&\n              item.product_type == 2 &&\n              item.unit_price\n                ? _vm.t(\"color1\")\n                : null\n            var g2 = item.priceshows && item.priceshows.length > 0\n            var g3 =\n              _vm.custom.product_show_fwlist &&\n              item.fwlist &&\n              item.fwlist.length > 0\n            var l2 = g3\n              ? _vm.__map(item.fwlist, function (fw, fwidx) {\n                  var $orig = _vm.__get_orig(fw)\n                  var m12 = _vm.t(\"color2rgb\")\n                  var m13 = _vm.t(\"color2\")\n                  return {\n                    $orig: $orig,\n                    m12: m12,\n                    m13: m13,\n                  }\n                })\n              : null\n            var m14 =\n              !item.price_type && _vm.numtotal[item.id] > 0\n                ? Number(_vm.numtotal[item.id])\n                : null\n            var m15 =\n              !item.price_type && _vm.numtotal[item.id] > 0\n                ? Number(_vm.numtotal[item.id])\n                : null\n            var m16 =\n              !item.price_type && _vm.numtotal[item.id] > 0 && !(m15 >= 100)\n                ? Number(_vm.numtotal[item.id])\n                : null\n            return {\n              $orig: $orig,\n              m6: m6,\n              m7: m7,\n              m8: m8,\n              m9: m9,\n              m10: m10,\n              m11: m11,\n              g2: g2,\n              g3: g3,\n              l2: l2,\n              m14: m14,\n              m15: m15,\n              m16: m16,\n            }\n          })\n          return {\n            $orig: $orig,\n            l3: l3,\n          }\n        })\n      : null\n  var g4 = _vm.isload && _vm.st == 2 ? _vm.commentlist.length : null\n  var m17 = _vm.isload && _vm.st == 2 && g4 > 0 ? _vm.t(\"color1\") : null\n  var l5 =\n    _vm.isload && _vm.st == 3\n      ? _vm.__map(_vm.paylist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m18 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m18: m18,\n          }\n        })\n      : null\n  var m19 = _vm.isload ? _vm.t(\"color1\") : null\n  var m20 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m21 = _vm.isload && _vm.cartList.total > 0 ? _vm.t(\"color1\") : null\n  var m22 = _vm.isload ? _vm.t(\"color1\") : null\n  var m23 = _vm.isload ? _vm.t(\"color1\") : null\n  var m24 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l6 = _vm.isload\n    ? _vm.__map(_vm.cartList.list, function (cart, index) {\n        var $orig = _vm.__get_orig(cart)\n        var m25 = Number(cart.num)\n        return {\n          $orig: $orig,\n          m25: m25,\n        }\n      })\n    : null\n  var g5 = _vm.isload ? _vm.cartList.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        g1: g1,\n        m3: m3,\n        l1: l1,\n        l4: l4,\n        g4: g4,\n        m17: m17,\n        l5: l5,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        l6: l6,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy2.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"view-show\">\n\t\t\t<view class=\"topbannerbg\" :style=\"business.pic?'background:url('+business.pic+') 100%':''\"></view>\n\t\t\t<view class=\"topbannerbg2\"></view>\n\t\t\t<view class=\"topbanner\">\n\t\t\t\t<view class=\"left\"><image class=\"img\" :src=\"business.logo\"/></view>\n\t\t\t\t<view class=\"right\">\n\t\t\t\t\t<view class=\"f1\">{{business.name}}</view>\n\t\t\t\t\t<view class=\"f2\">{{business.desc}}</view>\n\t\t\t\t\t<view class=\"f2\" style=\"opacity:0.9\" v-if=\"business.address\" @tap=\"openLocation\" :data-latitude=\"business.latitude\" :data-longitude=\"business.longitude\" :data-company=\"business.name\" :data-address=\"business.address\"><text class=\"iconfont icondingwei\"></text>{{business.address}}</view>\n\t\t\t\t\t<!-- <view class=\"f3\"><view class=\"flex1\"></view><view class=\"t2\">收藏<image class=\"img\" src=\"/static/img/like1.png\"/></view></view> -->\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"navtab\">\n\t\t\t\t<block v-if=\"paylist && paylist.length > 0\">\n\t\t\t\t<block v-for=\"(item, index) in menuList\" :key=\"index\">\n\t\t\t\t\t<view :class=\"'item ' + (st == item.st ? 'on' : '')\" @tap=\"changetab\" :data-st=\"item.st\">{{item.alias ? item.alias : item.name}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t</block>\n\t\t\t\t\t<!-- <view class=\"item\" :class=\"st==0?'on':''\" @tap=\"changetab\" data-st=\"0\">商品<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t\t<view class=\"item\" :class=\"st==1?'on':''\" @tap=\"changetab\" data-st=\"1\">商家信息<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t\t<view class=\"item\" :class=\"st==3?'on':''\" @tap=\"changetab\" data-st=\"3\" v-if=\"paylist && paylist.length > 0\">会员支付<view class=\"after\" :style=\"{background:t('color1')}\"></view></view> -->\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<view class=\"item\" :class=\"st==0?'on':''\" @tap=\"changetab\" data-st=\"0\">商品<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t\t<view class=\"item\" :class=\"st==1?'on':''\" @tap=\"changetab\" data-st=\"1\">商家信息<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t\t<view class=\"item\" :class=\"st==2?'on':''\" @tap=\"changetab\" data-st=\"2\" v-if=\"!paylist || paylist.length == 0\">评价<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t<view v-if=\"st==0\" class=\"content\" style=\"overflow:hidden;display:flex;margin-top:86rpx\" :style=\"{height:'calc(100% - '+(menuindex>-1?550:450)+'rpx)'}\">\n\t\t\t\t<scroll-view class=\"nav_left\" :scrollWithAnimation=\"animation\" scroll-y=\"true\">\n\t\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\" >\n\t\t\t\t\t<view class=\"nav_left_items\" :class=\"index===currentActiveIndex?'active':''\" :style=\"{color:index===currentActiveIndex?t('color1'):'#333'}\" @tap=\"clickRootItem\" :data-root-item-id=\"item.id\" :data-root-item-index=\"index\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"nav_right\">\n\t\t\t\t\t<view class=\"nav_right-content\">\n\t\t\t\t\t\t<scroll-view @scroll=\"scroll\" class=\"detail-list\" :scrollIntoView=\"scrollToViewId\" :scrollWithAnimation=\"animation\" scroll-y=\"true\">\n\t\t\t\t\t\t\t<view v-for=\"(detail, index) in datalist\" :key=\"index\" class=\"classification-detail-item\">\n\t\t\t\t\t\t\t\t<view class=\"head\" :data-id=\"detail.id\" :id=\"'detail-' + detail.id\">\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{detail.name}}</view>\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"show-all\" @tap=\"gotoCatproductPage\">查看全部<text class=\"iconfont iconjiantou\"></text></view> -->\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"product-itemlist\">\n\t\t\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,indexs) in detail.prolist\" :key=\"indexs\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-pic\" @click=\"goto\" :data-url=\"'product?id='+item.id\">\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p5\" :style=\"{color:t('color2')}\" v-if=\"custom.product_show_sellpoint && item.sellpoint\"><text>{{item.sellpoint}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p2\" v-if=\"item.price_type != 1 || item.sell_price > 0\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:20rpx;padding-right:1px\">￥</text>{{item.sell_price}}\n                          <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- 服务费 -->\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"!isNull(item.service_fee) && item.service_fee_switch && item.service_fee > 0\" class=\"t1-m\" :style=\"{color:t('color1')}\">+{{item.service_fee}}{{t('服务费')}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- 称重商品单价 -->\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"custom.product_weight && item.product_type==2 && item.unit_price\" class=\"t1-m\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t(约{{item.unit_price}}元/斤)\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n                      <!-- 商品处显示会员价 -->\n                      <view v-if=\"item.price_show && item.price_show == 1\" style=\"line-height: 46rpx;\">\n                        <text style=\"font-size:26rpx\">￥{{item.sell_putongprice}}</text>\n                      </view>\n                      <view v-if=\"item.priceshows && item.priceshows.length>0\">\n                        <view v-for=\"(item2,index2) in item.priceshows\" style=\"line-height: 46rpx;\">\n                          <text style=\"font-size:26rpx\">￥{{item2.sell_price}}</text>\n                          <text style=\"margin-left: 15rpx;font-size: 22rpx;font-weight: 400;\">{{item2.price_show_text}}</text>\n                        </view>\n                      </view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p6\" v-if=\"custom.product_show_fwlist && item.fwlist && item.fwlist.length>0\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p6-m\" :style=\"'background:rgba('+t('color2rgb')+',0.15);color:'+t('color2')+';'\" v-for=\"(fw,fwidx) in item.fwlist\" :key=\"fwidx\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{fw}}\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" @click.stop=\"buydialogChange\" :data-proid=\"item.id\"><text class=\"iconfont icon_gouwuche\"></text></view> -->\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"addnum\" v-if=\"!item.price_type\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"countbut-view\" style=\"padding-right: 10rpx;\" v-if=\"numtotal[item.id]>0\" @tap.stop=\"addcart\" data-num=\"-1\" :data-proid=\"item.id\" :data-stock=\"item.stock\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"minus\">-</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"numtotal[item.id]>0\" class=\"input-view\" :style=\"{width: Number(numtotal[item.id]) >= 100 ? '75rpx':'60rpx'}\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<input class=\"input-class\" type=\"number\" v-model=\"numtotal[item.id]\" @blur='inpurCart($event,item.id,item.gglist[0].id)' :style=\"{width: Number(numtotal[item.id]) >= 100 ? '75rpx': (Number(numtotal[item.id]) >= 10 ? '45rpx':'30rpx')}\" />\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"countbut-view\" style=\"padding-left: 10rpx;\" v-if=\"item.ggcount>1\" @tap.stop=\"buydialogChange\" :data-proid=\"item.id\" :data-stock=\"item.stock\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"plus\">+</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"countbut-view\" style=\"padding-left: 10rpx;\" v-else @tap.stop=\"addcart\" data-num=\"1\" :data-proid=\"item.id\" :data-ggid=\"item.gglist[0].id\" :data-stock=\"item.stock\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"plus\">+</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"st==1\" class=\"content1\" style=\"margin-top:86rpx;padding-top:20rpx\">\n\t\t\t\t<view class=\"item flex-col\">\n\t\t\t\t\t<text class=\"t1\">联系电话</text>\n\t\t\t\t\t<text class=\"t2\">{{business.tel}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-col\">\n\t\t\t\t\t<text class=\"t1\">商家地址</text>\n\t\t\t\t\t<text class=\"t2\">{{business.address}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-col\">\n\t\t\t\t\t<text class=\"t1\">商家简介</text>\n\t\t\t\t\t<view class=\"t2\"><parse :content=\"business.content\"></parse></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-col\" v-if=\"bid!=0\">\n\t\t\t\t\t<text class=\"t1\">营业时间</text>\n\t\t\t\t\t<text class=\"t2\">{{business.start_hours}} 至 {{business.end_hours}}</text>\n\t\t\t\t</view>\n        <view style=\"width: 100%;height: 140rpx;clear: both;\"></view>\n\t\t\t</view>\n\t\t\t<view v-if=\"st==2\" class=\"content2\" style=\"margin-top:86rpx;padding-top:20rpx\">\n\t\t\t\t<view class=\"comment\" style=\"height:calc(100vh - 460rpx);overflow:scroll\">\n\t\t\t\t\t<block v-if=\"commentlist.length>0\">\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"title\">\n\t\t\t\t\t\t\t<view class=\"f1\">评价({{business.comment_num}})</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\">好评率 <text :style=\"{color:t('color1')}\">{{business.comment_haopercent}}%</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-for=\"(item, index) in commentlist\" :key=\"index\" class=\"item\">\n\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t<image class=\"t1\" :src=\"item.headimg\"/>\n\t\t\t\t\t\t\t\t<view class=\"t2\">{{item.nickname}}</view>\n\t\t\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"pre_url+'/static/img/star' + (item.score>item2?'2native':'') + '.png'\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view style=\"color:#777;font-size:22rpx;\">{{item.createtime}}</view>\n\t\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.content}}</text>\n\t\t\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.content_pic!=''\">\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in item.content_pic\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"item.content_pic\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"item.reply_content\">\n\t\t\t\t\t\t\t\t<view class=\"arrow\"></view>\n\t\t\t\t\t\t\t\t<view class=\"t1\">商家回复：{{item.reply_content}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"width:100%;height:120rpx\"></view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<nodata v-show=\"comment_nodata\"></nodata>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"st==3\" class=\"content2\" style=\"margin-top:86rpx;padding-top:20rpx\">\n\t\t\t\t<view class=\"paylist\" style=\"height:calc(100vh - 460rpx);overflow:scroll\">\n\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in paylist\" :key=\"index\" @click=\"createpayorder\" :data-id=\"item.id\">\n\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t<view class=\"t1\"><text>{{item.name}}</text></view>\n\t\t\t\t\t\t\t<view class=\"t2\" :style=\"{color:t('color1')}\">￥{{item.market_price}}<text style=\"padding:0 4rpx\">/</text><text style=\"font-size:24rpx\">会员价￥</text>{{item.sell_price}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"arrowright\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart\" :menuindex=\"menuindex\" btntype=\"1\" :needaddcart=\"false\"></buydialog>\n\t\t<view style=\"height:auto;position:relative\">\n\t\t\t<view style=\"width:100%;height:100rpx\"></view>\n\t\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t<view class=\"cart_ico\" :style=\"{background:'linear-gradient(0deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"handleClickMask\"><image class=\"img\" :src=\"pre_url+'/static/img/cart.png'\" /><view class=\"cartnum\" :style=\"{background:t('color1')}\" v-if=\"cartList.total>0\">{{cartList.total}}</view></view>\n\t\t\t\t<view class=\"text1\">合计</view>\n\t\t\t\t<view class=\"text2\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx\">￥</text>{{cartList.totalprice}}</view>\n\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t<view class=\"op\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"gopay\">去结算</view>\n\t\t\t</view>\n\t\t</view>\n\t<uni-popup ref=\"popup\" type=\"bottom\" :animation='false'>\n\t\t<view class=\"popup-content-fastbuy\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t<view class=\"popup__title\" style=\"border-bottom:1px solid #EFEFEF\">\n\t\t\t\t<text class=\"popup__title-text\" style=\"color:#323232;font-weight:bold;font-size:32rpx\">购物车</text>\n\t\t\t\t<view class=\"popup__close flex-y-center\" @tap.stop=\"clearShopCartFn\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/>清空</view>\n\t\t\t</view>\n\t\t\t<view class=\"popup__content\" style=\"padding:0\">\n\t\t\t\t<scroll-view scroll-y class=\"prolist\">\n\t\t\t\t\t<block v-for=\"(cart, index) in cartList.list\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"proitem\">\n\t\t\t\t\t\t\t<image :src=\"cart.guige.pic?cart.guige.pic:cart.product.pic\" class=\"pic flex0\"></image>\n\t\t\t\t\t\t\t<view class=\"con\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{cart.product.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"cart.guige.name!='默认规格'\">{{cart.guige.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"f3\" style=\"color:#ff5555;margin-top:10rpx;font-size:28rpx\">￥{{cart.guige.sell_price}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"addnum\">\n\t\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"addcart\" data-num=\"-1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\"/></view>\n\t\t\t\t\t\t\t\t<view class=\"input-view\" :style=\"{width: Number(cart.num) >= 100 ? '75rpx':'60rpx'}\">\n\t\t\t\t\t\t\t\t\t<input class=\"input-class\" type=\"number\" v-model=\"cart.num\" @blur='inpurCart($event,cart.proid,cart.ggid)' />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"addcart\" data-num=\"1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"!cartList.list.length\">\n\t\t\t\t\t\t<text class=\"nopro\">暂时没有商品喔~</text>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tst:0,\n\t\t\tbuydialogShow:false,\n\t\t\tharr:[],\n\t\t\tbusiness:{},\n      datalist: [],\n\t\t\tmenuList:[],\n\t\t\tcartList:{},\n\t\t\tnumtotal:[],\n\t\t\tproid:'',\n\t\t\ttotalprice:'0.00',\n      currentActiveIndex: 0,\n      animation: true,\n      scrollToViewId: \"\",\n\t\t\tcommentlist:[],\n\t\t\tcomment_nodata:false,\n\t\t\tcomment_nomore:false,\n\t\t\tpaylist:[],\n\t\t\tbid:0,\n\t\t\tscrollState:true,\n\t\t\tcustom:{},\n\t\t\tmendianid:0,\n\t\t\tpre_url: app.globalData.pre_url,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\t//读全局缓存的地区信息\n\t\tvar locationCache =  app.getLocationCache();\n\t\tif(locationCache){\n\t\t\tif(locationCache.latitude){\n\t\t\t\tthis.latitude = locationCache.latitude\n\t\t\t\tthis.longitude = locationCache.longitude\n\t\t\t}\n\t\t\tif(locationCache.area){\n\t\t\t\tthis.area = locationCache.area\n\t\t\t}\n\t\t\tif(locationCache.mendian_id){\n\t\t\t\tthis.mendianid = locationCache.mendian_id\n\t\t\t}\n\t\t}\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonReachBottom: function () {\n\t\tif (this.st == 2) {\n\t\t\tif (!this.comment_nodata && !this.comment_nomore) {\n\t\t\t\tthis.pagenum = this.pagenum + 1;\n\t\t\t\tthis.getCommentList(true);\n\t\t\t}\n\t\t}\n\t},\n  methods: {\n\t\tinpurCart(e,proId,ggId){\n\t\t\tvar that = this;\n\t\t\tvar num = e.detail.value || 0;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiShop/addcart', {proid: proId,ggid: ggId,input_num: num}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t} else {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiShop/fastbuy2', {bid:that.opt.bid,mendian_id:that.mendianid}, function (res) {\n\t\t\t\tthat.cartList = {};\n\t\t\t\tthat.numtotal = [];\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.business = res.business\n\t\t\t\tthat.datalist = res.data;\n\t\t\t\tthat.cartList = res.cartList;\n\t\t\t\tthat.menuList = res.menuList;\n\t\t\t\tthat.numtotal = res.numtotal;\n\t\t\t\tthat.paylist = res.paylist;\n\t\t\t\tthat.bid = res.bid;\n\t\t\t\tthat.custom = res.custom;\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: that.business.name\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif(that.menuList.length > 0) {\n\t\t\t\t\tthat.st = that.menuList[0].st;\n\t\t\t\t}\n\n\t\t\t\t//计算每个高度\n\t\t\t\tvar harr = [];\n\t\t\t\tvar clientwidth = uni.getSystemInfoSync().windowWidth;\n\t\t\t\tvar datalist = res.data;\n\t\t\t\tconsole.log(datalist.length)\n\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\n\t\t\t\t\tvar child = datalist[i].prolist;\n\t\t\t\t\tconsole.log(child)\n\t\t\t\t\tharr.push(Math.ceil(child.length) * 200 / 750 * clientwidth);\n\t\t\t\t}\n\t\t\t\tthat.harr = harr;\n\t\t\t\tif(that.opt.cid){\n\t\t\t\t\tthat.scrollToViewId = 'detail-' + that.opt.cid;\n\t\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\n\t\t\t\t\t\tif(datalist[i].id == that.opt.cid) that.currentActiveIndex = i;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tchangetab:function(e){\n\t\t\tthis.st = e.currentTarget.dataset.st;\n\t\t\tthis.pagenum = 1;\n\t\t\tthis.commentlist = [];\n\t\t\tuni.pageScrollTo({\n\t\t\t\tscrollTop: 0,\n\t\t\t\tduration: 0\n\t\t\t});\n\t\t\tthis.getCommentList();\n\t\t},\n\t\tgetCommentList: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.commentlist = [];\n\t\t\t}\n\t\t\tvar that = this;\n\t\t\tvar pagenum = that.pagenum;\n\t\t\tvar st = that.st;\n\t\t\tthat.loading = true;\n\t\t\tthat.comment_nodata = false;\n\t\t\tthat.comment_nomore = false;\n\t\t\tif(that.bid == 0){\n\t\t\t\tapp.post('ApiShop/commentlist', {pagenum: pagenum,proid:0}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\tvar data = res.data;\n\t\t\t\t\tif (pagenum == 1) {\n\t\t\t\t\t\tthat.commentlist = data;\n\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\tthat.comment_nodata = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\tthat.comment_nomore = true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tvar commentlist = that.commentlist;\n\t\t\t\t\t\t\tvar newdata = commentlist.concat(data);\n\t\t\t\t\t\t\tthat.commentlist = newdata;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}else{\n\t\t\t\tapp.post('ApiBusiness/getdatalist', {id: that.business.id,st: st,pagenum: pagenum}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\tvar data = res.data;\n\t\t\t\t\tif (pagenum == 1) {\n\t\t\t\t\t\tthat.commentlist = data;\n\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\tthat.comment_nodata = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\tthat.comment_nomore = true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tvar commentlist = that.commentlist;\n\t\t\t\t\t\t\tvar newdata = commentlist.concat(data);\n\t\t\t\t\t\t\tthat.commentlist = newdata;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n    clickRootItem: function (t) {\n\t\t\tthis.scrollState=false;\n      var e = t.currentTarget.dataset;\n      this.scrollToViewId = 'detail-' + e.rootItemId;\n      this.currentActiveIndex = e.rootItemIndex;\n\t\t\tsetTimeout(()=>{\n\t\t\t\tthis.scrollState=true;\n\t\t\t},500)\n    },\n\t\taddcart:function(e){\n\t\t\tvar that = this;\n\t\t\tvar ks = that.ks;\n\t\t\tvar num = e.currentTarget.dataset.num;\n\t\t\tvar proid = e.currentTarget.dataset.proid;\n\t\t\tvar ggid = e.currentTarget.dataset.ggid;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiShop/addcart', {proid: proid,ggid: ggid,num: num}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    //加入购物车弹窗后\n    afteraddcart: function (e) {\n\t\t\te.hasoption = false;\n      this.addcart({currentTarget:{dataset:e}});\n    },\n    clearShopCartFn: function () {\n\t\t\tvar that = this;\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '确定清空此购物车？',\n\t\t\t\tsuccess: function (ress) {\n\t\t\t\t\tif (ress.confirm) {\n\t\t\t\t\t\tapp.post(\"ApiShop/cartclear\", {bid:that.opt.bid}, function (res) {\n\t\t\t\t\t\t  that.getdata();\n\t\t\t\t\t\t});\n\t\t\t\t\t} else if (ress.cancel) {\n\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n    },\n    gopay: function () {\n      var cartList = this.cartList.list;\n      if (cartList.length == 0) {\n        app.alert('请先添加商品到购物车');\n        return;\n      }\n      var prodata = [];\n      for (var i = 0; i < cartList.length; i++) {\n        prodata.push(cartList[i].proid + ',' + cartList[i].ggid + ',' + cartList[i].num);\n      }\n      app.goto('/pagesB/shop/buy?frompage=fastbuy&prodata=' + prodata.join('-'));\n    },\n    gotoCatproductPage: function (t) {\n      var e = t.currentTarget.dataset;\n      app.goto('prolist?cid=' + e.id);\n    },\n    scroll: function (e) {\n\t\t\tif(this.scrollState){\n\t\t\t\tvar scrollTop = e.detail.scrollTop;\n\t\t\t\tvar harr = this.harr;\n\t\t\t\tvar countH = 0;\n\t\t\t\tfor (var i = 0; i < harr.length; i++) {\n\t\t\t\t\tif (scrollTop >= countH && scrollTop < countH + harr[i]) {\n\t\t\t\t\t\tthis.currentActiveIndex = i;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcountH += harr[i];\n\t\t\t\t}\n\t\t\t}\n    },\n\t\tbuydialogChange: function (e) {\n\t\t\tif(!this.buydialogShow){\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\n\t\t\t}\n\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t},\n\t\thandleClickMask:function(){\n\t\t\tthis.$refs.popup.open();\n\t\t},\n\t\topenLocation:function(e){\n\t\t\t//console.log(e)\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\n\t\t\tvar address = e.currentTarget.dataset.address\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t scale: 13\n\t\t })\t\t\n\t\t},\n\t\tcreatepayorder:function(e){\n\t\t\tvar id = e.currentTarget.dataset.id\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiPlugBusinessqr/createpayorder',{id:id},function(res){\n\t\t\t\tif(res.status == 0) {\n\t\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t} else {\n\t\t\t\t\tapp.goto('/pagesExt/pay/pay?id=' + res.payorderid);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n  }\n};\n</script>\n<style>\npage {position: relative;width: 100%;height: 100%;}\n.container{height:100%;position: relative;}\n\n.topbannerbg{width:100%;height:264rpx;background:#fff;}\n.topbannerbg2{position:absolute;z-index:7;width:100%;height:264rpx;background:rgba(0,0,0,0.7);top:0}\n.topbanner{position:absolute;z-index:8;width:100%;display:flex;padding:40rpx 20rpx;top:0}\n.topbanner .left{width:160rpx;height:160rpx;flex-shrink:0;margin-right:20rpx}\n.topbanner .left .img{width:100%;height:100%;border-radius:50%}\n.topbanner .right{display:flex;flex-direction:column;padding:20rpx 0}\n.topbanner .right .f1{font-size:36rpx;font-weight:bold;color:#fff}\n.topbanner .right .f2{font-size:22rpx;color:#fff;opacity:0.7;margin-top:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;line-height:30rpx;}\n.topbanner .right .f3{width:100%;display:flex;padding-right:20rpx;margin-top:10rpx}\n.topbanner .right .f3 .t2{display:flex;align-items:center;font-size:24rpx;color:rgba(255,255,255,0.9)}\n.topbanner .right .f3 .img{width:32rpx;height:32rpx;margin-left:10rpx}\n\n.navtab{display:flex;width:100%;height:110rpx;background: #fff;position:absolute;z-index:9;padding:0 50rpx;border-radius:24rpx 24rpx 0 0;margin-top:-24rpx;}\n.navtab .item{flex:1;font-size:32rpx; text-align:center; color:#222222; height: 110rpx; line-height: 110rpx;overflow: hidden;position:relative}\n.navtab .item .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:20rpx;height:4px;border-radius:2px;width:40rpx}\n.navtab .on{font-size:36rpx;font-weight:bold}\n.navtab .on .after{display:block}\n\n.content1 .item{display:flex;flex-direction:column;width:100%;padding:0 40rpx;margin-top:40rpx}\n.content1 .item:last-child{ border-bottom: 0;}\n.content1 .item .t1{width:200rpx;color:#2B2B2B;font-weight:bold;font-size:30rpx;height:60rpx;line-height:60rpx}\n.content1 .item .t2{color:#2B2B2B;font-size:24rpx;line-height:30rpx}\n\n.content2 .comment{padding:0 10rpx;overflow:scroll}\n\n.content2 .comment .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex;margin:0 3%}\n.content2 .comment .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\n.content2 .comment .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\n\n.content2 .comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\n.content2 .comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\n.content2 .comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\n.content2 .comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\n.content2 .comment .item .f1 .t3{text-align:right;}\n.content2 .comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\n.content2 .comment .item .score{ font-size: 24rpx;color:#f99716;}\n.content2 .comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\n.content2 .comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\n.content2 .comment .item .f2 .t1{color:#333;font-size:28rpx;}\n.content2 .comment .item .f2 .t2{display:flex;width:100%}\n.content2 .comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\n.content2 .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\n.content2 .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\n.content2 .comment .item .f3{width:100%;padding:10rpx 0;position:relative}\n.content2 .comment .item .f3 .arrow{width: 16rpx;height: 16rpx;background:#eee;transform: rotate(45deg);position:absolute;top:0rpx;left:36rpx}\n.content2 .comment .item .f3 .t1{width:100%;border-radius:10rpx;padding:10rpx;font-size:22rpx;color:#888;background:#eee}\n\n\n.view-show{background-color: white;line-height: 1;width: 100%;height: 100%;}\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\n\n.nav_left{width: 25%;height:100%;background:#F6F6F6;overflow-y:scroll;}\n.nav_left .nav_left_items{line-height:50rpx;color:#333333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\n.nav_left .nav_left_items.active{background: #fff;color:#333333;font-size:28rpx;font-weight:bold}\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-22rpx;left:0rpx;height:44rpx;border-radius:4rpx;width:6rpx}\n.nav_left .nav_left_items.active .before{display:block}\n\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #fff;box-sizing: border-box;padding:0 0 0 0}\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%;position:relative}\n.detail-list {height:100%;overflow:scroll}\n.classification-detail-item {width: 100%;overflow: visible;background:#fff}\n.classification-detail-item .head {height: 82rpx;width: 100%;display: flex;align-items:center;justify-content:space-between;}\n.classification-detail-item .head .txt {color:#222222;font-weight:bold;font-size:28rpx;}\n.classification-detail-item .head .show-all {font-size: 26rpx;color:#949494;display:flex;align-items:center}\n\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;font-weight:bold;}\n.product-itemlist .product-info .p2 .t1-m {font-size: 26rpx;font-weight: bold;padding-left: 6rpx;}\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}\n.product-itemlist .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}\n.product-itemlist .product-info .p5{font-size:24rpx;font-weight: bold;margin: 6rpx 0;}\n.product-itemlist .product-info .p6{font-size:24rpx;display: flex;flex-wrap: wrap;margin-top: 6rpx;}\n.product-itemlist .product-info .p6-m{text-align: center;padding:6rpx 10rpx;border-radius: 6rpx;margin: 6rpx;}\n.product-itemlist .addnum {position: absolute;right:10rpx;bottom:20rpx;font-size: 32rpx;color: #666;width: auto;display:flex;align-items:center}\n.product-itemlist .addnum .countbut-view{padding: 5rpx;}\n.product-itemlist .addnum .plus {width:44rpx;height:44rpx;background:#FD4A46;color:#FFFFFF;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:32rpx;}\n.product-itemlist .addnum .minus {width:44rpx;height:44rpx;background:#FFFFFF;color:#FD4A46;border:1px solid #FD4A46;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:32rpx}\n.product-itemlist .addnum .img{width:24rpx;height:24rpx}\n.product-itemlist .addnum .input-view {margin: 0 2rpx;}\n.product-itemlist .addnum .input-class{color:#2B2B2B;font-size:32rpx;text-align: center;width: 100%;height: 32rpx;margin: 0 auto;border-radius: 5rpx;}\n\n.prolist {max-height: 620rpx;min-height: 320rpx;overflow: hidden;padding:0rpx 20rpx;font-size: 28rpx;border-bottom: 1px solid #e6e6e6;}\n.prolist .nopro {text-align: center;font-size: 26rpx;display: block;margin: 80rpx auto;}\n.prolist .proitem{position: relative;padding:10rpx 0;display:flex;border-bottom:1px solid #eee}\n.prolist .proitem .pic{width: 120rpx;height: 120rpx;margin-right: 20rpx;}\n.prolist .proitem .con{padding-right:180rpx;padding-top:10rpx}\n.prolist .proitem .con .f1{color:#323232;font-size:26rpx;line-height:32rpx;margin-bottom: 10rpx;margin-top: -6rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\n.prolist .proitem .con .f2{font-size: 24rpx;line-height:28rpx;color: #999;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;}\n.prolist .proitem .addnum {position: absolute;right: 20rpx;bottom:50rpx;font-size: 30rpx;color: #666;width: auto;display:flex;align-items:center}\n.prolist .proitem .addnum .plus,.minus {width:52rpx;height:40rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center;}\n.prolist .proitem .addnum .img{width:24rpx;height:24rpx}\n.prolist .tips {font-size: 22rpx;color: #666;text-align: center;line-height: 56rpx;background: #f5f5f5;}\n.prolist .proitem .addnum .input-view {margin: 0 10rpx;font-weight:bold;}\n.prolist .proitem .addnum .input-class{color:#2B2B2B;font-size:24rpx;text-align: center;width: 100%;}\n\n.paylist{padding:20rpx 30rpx;background:#f5f5f5}\n.paylist .item{width:100%;display: inline-block;position: relative;margin-bottom: 20rpx;background: #fff;display:flex;padding:0;border-radius:10rpx;border-bottom:1px solid #F8F8F8;overflow:hidden}\n.paylist .item .f1{width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\n.paylist .item .f1 .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.paylist .item .f2{width: 70%;padding:30rpx 20rpx 30rpx 40rpx;position: relative;}\n.paylist .item .f2 .t1 {color:#323232;font-weight:bold;font-size:40rpx;line-height:50rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:100rpx}\n.paylist .item .f2 .t2{font-size:36rpx;line-height:46rpx;}\n.paylist .item .arrowright{position:absolute;top:90rpx;right:20rpx;width:40rpx;height:40rpx}\n\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\n.footer .cart_ico{width:64rpx;height:64rpx;border-radius: 10rpx;display:flex;align-items:center;justify-content:center;position:relative}\n.footer .cart_ico .img{width:36rpx;height:36rpx;}\n.footer .cart_ico .cartnum{position:absolute;top:-17rpx;right:-17rpx;width:34rpx;height:34rpx;border:1px solid #fff;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden;font-size:20rpx;font-weight:bold;color:#fff}\n.footer .text1 {height: 100rpx;line-height: 100rpx;color:#555555;font-weight:bold;font-size: 30rpx;margin-left:40rpx;margin-right:10rpx}\n.footer .text2 {font-size: 32rpx;font-weight:bold}\n.footer .op{width: 200rpx;height: 72rpx;line-height:72rpx;border-radius: 36rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center}\n::-webkit-scrollbar{width: 0;height: 0;color: transparent;}\n.popup-content-fastbuy{background: #fff;border-radius:20rpx 20rpx 0 0;}\n.uni-popup__wrapper-box{background: #fff !important;border-radius:20rpx 20rpx 0 0;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy2.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy2.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839377727\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}