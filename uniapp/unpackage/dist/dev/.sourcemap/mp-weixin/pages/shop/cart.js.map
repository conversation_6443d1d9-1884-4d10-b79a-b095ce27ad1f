{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/cart.vue?3e39", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/cart.vue?199a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/cart.vue?b080", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/cart.vue?cefe", "uni-app:///pages/shop/cart.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/cart.vue?22d0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/cart.vue?75a5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "indexurl", "cartlist", "tjdatalist", "totalprice", "selectedcount", "allchecked", "xixie", "xixie_cartlist", "showprice_dollar", "usdrate", "usdtotalprice", "show_service_fee", "allservice_fee", "onLoad", "onShow", "uni", "onPullDownRefresh", "methods", "getdata", "that", "app", "bid", "latitude", "longitude", "area", "mendian_id", "checkLocationCache", "locationCache", "calculate", "ids", "totalservicefee", "changeradio", "changeradio2", "isallchecked", "changeradioAll", "cartdelete", "id", "type", "setTimeout", "cartdeleteb", "toOrder", "tmpprostr", "prodata", "jl", "j<PERSON><PERSON>", "tourl", "gwcplus", "num", "gwcminus", "gwcinput", "addcart"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsGt1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EAEAC;IACA;EACA;EACAC;IACA;MACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;MACAA;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;QACAN;QACAA;QACAA;QACA;UACAA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAO;MACA;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACA;QACA;QACA;QACA;UACAH;QACA;UACAA;QACA;UACAA;QACA;QACAG;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACAC;YACA;YACA1B;YACAC;YACA;cACA0B;YACA;UACA;QACA;MACA;MACAX;MACAA;MACA;QACAA;MACA;MAEAA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA,gBAEA;QACA;QACA;QACA;UACA9B;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAkB;MACA;MACAA;IACA;IAEAa;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACA/B;QACA;UACAA;QACA;QACA;QACA;UACA;YACAgC;UACA;QACA;QACA;UACAhC;QACA;UACAA;QACA;QACAkB;MACA,QAEA;MAEAA;IACA;IACAe;MACA;MACA;MACA;MACA;QACAjC;QACA;UACAA;QACA;MACA;MACAkB;MAEA;MACA;QACA;QACA;UACAZ;UACA;YACAA;UACA;QACA;QACAY;MACA;MAEAA;MACAA;IACA;IACAgB;MACA;MACA;MACA;MACAf;QACAA;UAAAgB;UAAAC;QAAA;UACAjB;UACA;YACAkB;cACAnB;YACA;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MAEAnB;QACAA;UAAAC;UAAAgB;QAAA;UACAjB;UACAkB;YACAnB;UACA;QACA;MACA;IACA;IACAqB;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;YACA;YACA;cACAC;YACA;YACAC;UACA;QACA;MACA;MACA;MACA;QACA;UACA;YACA;YACA;YACA;YACA;cACAC;YACA;YACAC;UACA;QACA;MACA;MACA;QACAxB;QACA;MACA;MACA;MACA;QACAyB;MACA;MACAzB;IACA;IACA;IACA0B;MACA;MACA;MACA;MACA;MAEA;MACA;QACA;QACA;UACA1B;UACA;QACA;QACA;QACAnB;QACA;MACA;QACA;QACA;UACAmB;UACA;QACA;QACA;QACAb;QACA;MACA;MAEA;MACA;MACAa;QAAAgB;QAAAW;QAAAV;MAAA;QACA;UACA;QAAA,CACA;UACAjB;UACAnB;QACA;UACAmB;UACA;YACAnB;UACA;YACAM;UACA;QACA;MACA;IACA;IACA;IACAyC;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;UACA5B;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;QACAnB;QACA;QACA;MACA;QACA;QACAM;QACA;QACA;MACA;MAEA;MACAa;QAAAgB;QAAAW;QAAAV;MAAA;QACA;UACA;QAAA,CACA;UACAjB;UACA;YACAnB;UACA;YACAM;UACA;QACA;MACA;IACA;IACA;IACA0C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA7B;QACA;MACA;MACA;QACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;QACAnB;QACA;QACA;MACA;QACA;QACA;UACAmB;UACA;QACA;QACA;QACAb;QACA;QACA;MACA;MACA;MACAa;QAAAgB;QAAAW;QAAAV;MAAA;QACA;UACA;QAAA,CACA;UACAjB;UACAnB;QACA;UACAmB;QACA;MACA;IACA;IACA8B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5gBA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/shop/cart.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/shop/cart.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cart.vue?vue&type=template&id=f2908c20&\"\nvar renderjs\nimport script from \"./cart.vue?vue&type=script&lang=js&\"\nexport * from \"./cart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cart.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/shop/cart.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=template&id=f2908c20&\"", "var components\ntry {\n  components = {\n    dpProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-item/dp-product-item\" */ \"@/components/dp-product-item/dp-product-item.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.cartlist.length : null\n  var l1 =\n    _vm.isload && g0 > 0\n      ? _vm.__map(_vm.cartlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = item.checked ? _vm.t(\"color1\") : null\n          var g1 = item.prolist.length\n          var m2 = _vm.t(\"color1\")\n          var l0 = _vm.__map(item.prolist, function (item2, index2) {\n            var $orig = _vm.__get_orig(item2)\n            var m1 = item2.checked ? _vm.t(\"color1\") : null\n            var m3 =\n              !_vm.isNull(item2.product.service_fee_switch) &&\n              !_vm.isNull(item2.guige.service_fee) &&\n              item2.product.service_fee_switch == 1 &&\n              item2.guige.service_fee > 0\n            var m4 = m3 ? _vm.t(\"服务费\") : null\n            return {\n              $orig: $orig,\n              m1: m1,\n              m3: m3,\n              m4: m4,\n            }\n          })\n          return {\n            $orig: $orig,\n            m0: m0,\n            g1: g1,\n            m2: m2,\n            l0: l0,\n          }\n        })\n      : null\n  var g2 = _vm.isload ? !_vm.xixie_cartlist && _vm.cartlist.length <= 0 : null\n  var m5 = _vm.isload && g2 ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && g2 ? _vm.t(\"color1rgb\") : null\n  var g3 = _vm.tjdatalist.length\n  var g4 = _vm.cartlist.length > 0 || (_vm.xixie && _vm.xixie_cartlist)\n  var m7 = g4 && _vm.allchecked ? _vm.t(\"color1\") : null\n  var m8 =\n    g4 && !_vm.showprice_dollar && _vm.allservice_fee > 0\n      ? _vm.t(\"服务费\")\n      : null\n  var m9 = g4 ? _vm.t(\"color1\") : null\n  var m10 = g4 ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n        g2: g2,\n        m5: m5,\n        m6: m6,\n        g3: g3,\n        g4: g4,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<block v-if=\"cartlist.length>0\">\n\t\t\t<view class=\"cartmain\">\n\t\t\t\t<block v-for=\"(item, index) in cartlist\" :key=\"item.bid\">\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<view class=\"btitle\">\n\t\t\t\t\t\t\t<view @tap.stop=\"changeradio\" :data-index=\"index\" class=\"radio\" :style=\"item.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t\t<view class=\"btitle-name\" @tap=\"goto\" :data-url=\"item.bid==0?indexurl:'/pagesExt/business/index?id=' + item.business.id\">{{item.business.name}}</view>\n\t\t\t\t\t\t\t<view class=\"flex1\"> </view>\n\t\t\t\t\t\t\t<view class=\"btitle-del\" @tap=\"cartdeleteb\" :data-bid=\"item.bid\"><image class=\"img\" :src=\"pre_url+'/static/img/del.png'\"/><text style=\"margin-left:10rpx\">删除</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"content\" v-for=\"(item2,index2) in item.prolist\" @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item2.product.id\" :key=\"index2\">\n\t\t\t\t\t\t\t<view @tap.stop=\"changeradio2\" :data-index=\"index\" :data-index2=\"index2\" class=\"radio\" :style=\"item2.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t\t<view class=\"proinfo\" :style=\"(item.prolist).length == index2+1 ? 'border:0' : ''\">\n\t\t\t\t\t\t\t\t<image :src=\"item2.guige.pic?item2.guige.pic:item2.product.pic\" class=\"img\"/>\n\t\t\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"title\"><text>{{item2.product.name}}</text></view>\n\t\t\t\t\t\t\t\t\t<view class=\"desc\"><text>{{item2.guige.name}}</text></view>\n                  <view class=\"desc\" v-if=\"item2.product.product_type==3\"><text>手工费:￥{{item2.guige.hand_fee}}</text></view>\n\t\t\t\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"!isNull(item2.product.service_fee_switch) && !isNull(item2.guige.service_fee) && item2.product.service_fee_switch == 1 && item2.guige.service_fee > 0\" style=\"line-height: initial;font-size: 26rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"display: block;\"><text style=\"font-size:24rpx\">￥</text>{{item2.guige.sell_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"display: block;\">+{{item2.guige.service_fee}}{{t('服务费')}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{item2.guige.sell_price}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<view class=\"addnum\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"minus\" @tap.stop=\"gwcminus\" :data-index=\"index\" :data-index2=\"index2\" :data-cartid=\"item2.id\" :data-num=\"item2.num\" :data-limit_start=\"item2.product.limit_start\" :data-limit_start_guige=\"item2.guige.limit_start\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" /></view>\n\t\t\t\t\t\t\t\t\t\t<input class=\"input\" @tap.stop=\"\" type=\"number\" :value=\"item2.num\" @blur=\"gwcinput\" :data-max=\"item2.guige.store_nums\" :data-index=\"index\" :data-index2=\"index2\" :data-cartid=\"item2.id\" :data-num=\"item2.num\" :data-limit_start=\"item2.product.limit_start\" :data-limit_start_guige=\"item2.guige.limit_start\"></input>\n\t\t\t\t\t\t\t\t\t\t<view class=\"plus\" @tap.stop=\"gwcplus\" :data-index=\"index\" :data-index2=\"index2\" :data-max=\"item2.guige.store_nums\" :data-num=\"item2.num\" :data-cartid=\"item2.id\" :data-limit_start=\"item2.product.limit_start\" :data-limit_start_guige=\"item2.guige.limit_start\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"prodel\" @tap.stop=\"cartdelete\" :data-cartid=\"item2.id\"><image class=\"prodel-img\" :src=\"pre_url+'/static/img/del.png'\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</block>\n        \n\t\t<block v-if=\"!xixie_cartlist && cartlist.length<=0\">\n\t\t\t<view class=\"data-empty\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/cartnull.png'\" class=\"data-empty-img\" style=\"width:120rpx;height:120rpx\"/>\n\t\t\t\t<view class=\"data-empty-text\" style=\"margin-top:20rpx;font-size:24rpx\">购物车空空如也~</view>\n\t\t\t\t<view style=\"width:400rpx;border:0;height:80rpx;line-height:80rpx;margin:40rpx auto;border-radius:6rpx;color:#fff\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"indexurl\" data-opentype=\"reLaunch\">去选购</view>\n\t\t\t</view>\n\t\t</block>\n\t</block>\n\t<view v-if=\"tjdatalist.length > 0\">\n\t\t<view class=\"xihuan\">\n\t\t\t<view class=\"xihuan-line\"></view>\n\t\t\t<view class=\"xihuan-text\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/xihuan.png'\" class=\"img\"/>\n\t\t\t\t<text class=\"txt\">为您推荐</text>\n\t\t\t</view>\n\t\t\t<view class=\"xihuan-line\"></view>\n\t\t</view>\n\t\t<view class=\"prolist\">\n\t\t\t<dp-product-item :data=\"tjdatalist\" @addcart=\"addcart\" :menuindex=\"menuindex\"></dp-product-item>\n\t\t</view>\n\t</view>\n\n\t<loading v-if=\"loading\"></loading>\n\t<block v-if=\"cartlist.length>0 ||(xixie && xixie_cartlist)\">\n\t<view style=\"height:auto;position:relative\">\n\t\t<view style=\"width:100%;height:110rpx\"></view>\n\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t<view @tap.stop=\"changeradioAll\" class=\"radio\" :style=\"allchecked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t<view @tap.stop=\"changeradioAll\" class=\"text0\">全选（{{selectedcount}}）</view>\n\t\t\t<view class=\"flex1\"></view>\n\t\t\t<view class=\"text1\">合计：</view>\n\t\t\t<block  v-if=\"showprice_dollar\">\n\t\t\t\t<view class=\"text2\" style=\"font-size: 32rpx;\"><text style=\"font-size:20rpx\">$</text>{{usdtotalprice}}</view>\n\t\t\t\t<view class=\"text3\" ><text style=\"font-size:18rpx\">￥</text>{{totalprice}}</view>\n\t\t\t</block>\n\t\t\t<block v-else-if=\"allservice_fee > 0\">\n\t\t\t\t<view class=\"text2\">\n\t\t\t\t\t<view class=\"text3\"><text style=\"font-size:18rpx\">￥</text>{{totalprice}}</view>\n\t\t\t\t\t<view class=\"text3\"><text style=\"font-size:18rpx\">+</text>{{allservice_fee}}{{t('服务费')}}</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t<block v-else>\n\t\t\t\t<view class=\"text2\"><text style=\"font-size:20rpx\">￥</text>{{totalprice}}</view>\n\t\t\t</block>\n\t\t\t\t\t\n\t\t\t<view class=\"op\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"toOrder\">去结算</view>\n\t\t</view>\n\t</view>\n\t</block>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n    <wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tindexurl:app.globalData.indexurl,\n\t\t\tcartlist:[],\n\t\t\ttjdatalist:[],\n      totalprice: '0.00',\n      selectedcount: 0,\n\t\t\tallchecked:true,\n\t\t\txixie:false,\n\t\t\txixie_cartlist:'',\n\t\t\tshowprice_dollar:false,\n\t\t\tusdrate:0,\n\t\t\tusdtotalprice:0,\n\t\t\tshow_service_fee:false,\n\t\t\tallservice_fee:0 //总服务费\n    };\n  },\n  \n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n  },\n\tonShow:function(){\n    if(app.globalData.platform=='wx' && app.globalData.hide_home_button==1){\n      uni.hideHomeButton();\n    }\n\t\tthis.getdata();\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n    getdata: function (){\n\t\t\tvar that = this;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n\t\t\tif(bid){\n\t\t\t\tthat.indexurl = '/pagesExt/business/index?id='+bid;\n\t\t\t}\n\t\t\t//如果设置过地域限制【定位模式下】\n\t\t\tvar locationCache =  that.checkLocationCache();\n\t\t\tthat.loading = true;\n        app.get('ApiShop/cart', {bid:bid,latitude:locationCache.latitude,longitude:locationCache.longitude,area:locationCache.area,mendian_id:locationCache.mendian_id}, function (res) {\n\t\t\t\t\t\tthat.loading = false;\n            that.cartlist = res.cartlist;\n            that.tjdatalist = res.tjdatalist;\n            if(res.xixie ){\n                that.xixie          = res.xixie;\n                that.xixie_cartlist = res.xixie_cartlist;\n            }\n\t\t\t\t\t\tthat.showprice_dollar = res.price_dollar\n\t\t\t\t\t\tthat.usdrate = res.usdrate\n            that.calculate();\n\t\t\t\t\t\tthat.loaded();\n      });\n    },\n\t\tcheckLocationCache:function(){\n\t\t\tvar that  = this\n\t\t\tvar locationCache = app.getLocationCache();\n\t\t\tif(!locationCache.latitude){\n\t\t\t\tlocationCache.latitude = ''\n\t\t\t}\n\t\t\tif(!locationCache.longitude){\n\t\t\t\tlocationCache.longitude = ''\n\t\t\t}\n\t\t\tif(!locationCache.mendian_id){\n\t\t\t\tlocationCache.mendian_id = 0\n\t\t\t}\n\t\t\tif(!locationCache.area){\n\t\t\t\tlocationCache.area = ''\n\t\t\t}\n\t\t\tif(locationCache && locationCache.area){\n\t\t\t\tvar area = '';\n\t\t\t\tvar areaArr = locationCache.area.split(',');\n\t\t\t\tvar showlevel = locationCache.showlevel?locationCache.showlevel:2\n\t\t\t\tif(showlevel==1 && areaArr.length>0){\n\t\t\t\t\tarea = areaArr[0]\n\t\t\t\t}else if(showlevel==2 && areaArr.length>1){\n\t\t\t\t\tarea = areaArr[0] + ','+areaArr[1]\n\t\t\t\t}else if(showlevel==3 && areaArr.length>2){\n\t\t\t\t\tarea = areaArr[0] + ','+areaArr[1] + ','+areaArr[2]\n\t\t\t\t}\n\t\t\t\tlocationCache.area = area;\n\t\t\t}\n\t\t\treturn locationCache;\n\t\t},\n    calculate: function () {\n      var that = this;\n      var cartlist = that.cartlist;\n      var ids = [];\n      var totalprice = 0.00;\n\t  var totalservicefee = 0.00;\n      var selectedcount = 0;\n\t\t\tfor(var i in cartlist){\n\t\t\t\t\tfor(var j in cartlist[i].prolist){\n\t\t\t\t\t\t\tif(cartlist[i].prolist[j].checked){\n\t\t\t\t\t\t\t\t\tids.push(cartlist[i].prolist[j].id);\n\t\t\t\t\t\t\t\t\tvar thispro = cartlist[i].prolist[j];\n\t\t\t\t\t\t\t\t\ttotalprice += thispro.guige.sell_price * thispro.num;\n\t\t\t\t\t\t\t\t\tselectedcount += thispro.num;\n\t\t\t\t\t\t\t\t\tif(!app.isNull(thispro.product.service_fee_switch) && thispro.product.service_fee_switch == 1){\n\t\t\t\t\t\t\t\t\t\ttotalservicefee += thispro.guige.service_fee * thispro.num;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t}\n      that.totalprice = totalprice.toFixed(2);\n\t\t\tthat.allservice_fee = totalservicefee.toFixed(2);\n\t\t\tif(that.showprice_dollar && that.usdrate){\n\t\t\t\t\tthat.usdtotalprice = (totalprice/that.usdrate).toFixed(2);\n\t\t\t}\n\t\t\t\n      that.selectedcount = selectedcount;\n    },\n    changeradio: function (e) {\n        var that = this;\n        var xixie = that.xixie;\n        var index = e.currentTarget.dataset.index;\n        var type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\n        if(type == 2){\n           \n        }else{\n            var cartlist = that.cartlist;\n            var checked = cartlist[index].checked;\n            if(checked){\n                cartlist[index].checked = false;\n            }else{\n                cartlist[index].checked = true;\n            }\n            for(var i in cartlist[index].prolist){\n                cartlist[index].prolist[i].checked = cartlist[index].checked;\n            }\n            that.cartlist = cartlist;\n        }\n        that.calculate();\n    },\n\t\t\n    changeradio2: function (e) {\n        var that = this;\n        var type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\n        var index = e.currentTarget.dataset.index;\n        var index2 = e.currentTarget.dataset.index2;\n        if(!type){\n            var cartlist = that.cartlist;\n            var checked = cartlist[index].prolist[index2].checked;\n            if(checked){\n            \tcartlist[index].prolist[index2].checked = false;\n            }else{\n            \tcartlist[index].prolist[index2].checked = true;\n            }\n            var isallchecked = true;\n            for(var i in cartlist[index].prolist){\n            \tif(cartlist[index].prolist[i].checked == false){\n            \t\tisallchecked = false;\n            \t}\n            }\n            if(isallchecked){\n            \tcartlist[index].checked = true;\n            }else{\n            \tcartlist[index].checked = false;\n            }\n            that.cartlist = cartlist;\n        }else{\n            \n        }\n\t\t\t\n        that.calculate();\n    },\n\t\tchangeradioAll:function(){\n            var that = this;\n\t\t\tvar cartlist = that.cartlist;\n\t\t\tvar allchecked = that.allchecked\n\t\t\tfor(var i in cartlist){\n\t\t\t\tcartlist[i].checked = allchecked ? false : true;\n\t\t\t\tfor(var j in cartlist[i].prolist){\n\t\t\t\t\tcartlist[i].prolist[j].checked = allchecked ? false : true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthat.cartlist = cartlist;\n            \n            var xixie = that.xixie;\n            if(xixie){\n                var xixie_cartlist = that.xixie_cartlist;\n                for(var i in xixie_cartlist){\n                \txixie_cartlist[i].checked = allchecked ? false : true;\n                \tfor(var j in xixie_cartlist[i].prolist){\n                \t\txixie_cartlist[i].prolist[j].checked = allchecked ? false : true;\n                \t}\n                }\n                that.xixie_cartlist = xixie_cartlist;\n            }\n            \n\t\t\tthat.allchecked = allchecked ? false : true;\n            that.calculate();\n\t\t},\n        cartdelete: function (e) {\n            var that = this;\n            var id = e.currentTarget.dataset.cartid;\n            var type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\n            app.confirm('确定要从购物车移除吗?', function () {\n                app.post('ApiShop/cartdelete', {id: id,type:type}, function (data) {\n                    app.success(data.msg);\n                    if(data.status == 1){\n                        setTimeout(function () {\n                          that.getdata();\n                        }, 1000);\n                    }\n                });\n            });\n        },\n\t\tcartdeleteb:function(e){\n            var that = this;\n            var bid   = e.currentTarget.dataset.bid;\n            var type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\n\n            app.confirm('确定要从购物车移除吗?', function () {\n                app.post('ApiShop/cartdelete', {bid: bid,type:type}, function (data) {\n                    app.success('操作成功');\n                    setTimeout(function () {\n                        that.getdata();\n                    }, 1000);\n                });\n            });\n        },\n    toOrder: function () {\n        var that = this;\n        var cartlist = that.cartlist;\n        var ids = [];\n        var prodata = [];\n        for(var i in cartlist){\n            for(var j in cartlist[i].prolist){\n                if(cartlist[i].prolist[j].checked){\n\t\t\t\t\tvar thispro = cartlist[i].prolist[j];\n\t\t\t\t\tvar tmpprostr = thispro.product.id + ',' + thispro.guige.id + ',' + thispro.num;\n\t\t\t\t\tif(thispro.glassrecord){\n\t\t\t\t\t\ttmpprostr += ',' + thispro.glassrecord.id\n\t\t\t\t\t}\t\n\t\t\t\t\tprodata.push(tmpprostr);\n                }\n            }\n        }\n        var jldata = [];\n\t\tfor(var i in cartlist){\n\t\t    for(var j in cartlist[i].prolist){\n\t\t        if(cartlist[i].prolist[j].checked){\n\t\t\t\t\tvar thispro = cartlist[i].prolist[j];\t\n\t\t\t\t\tvar jldatastr = thispro.jldata;\n\t\t\t\t\tvar jl = [];\n\t\t\t\t\tif(jldatastr){\n\t\t\t\t\t\tjl = JSON.parse(jldatastr)\n\t\t\t\t\t}\n\t\t\t\t\tjldata.push(jl);\n\t\t        }\n\t\t    }\n\t\t}\n        if (prodata == undefined || prodata.length == 0) {\n            app.error('请先选择产品');\n            return;\n        }\n\t\tvar tourl = '/pagesB/shop/buy?prodata=' + prodata.join('-');\n\t\tif(jldata.length >0){\n\t\t\ttourl +='&jldata='+JSON.stringify(jldata);\n\t\t}\n        app.goto(tourl);\n    },\n    //加\n    gwcplus: function (e) {\n        var type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\n        var index  = parseInt(e.currentTarget.dataset.index);\n        var index2 = parseInt(e.currentTarget.dataset.index2);\n        var cartid = e.currentTarget.dataset.cartid;\n        \n        var num = parseInt(e.currentTarget.dataset.num);\n        if(!type){\n            var maxnum = parseInt(e.currentTarget.dataset.max);\n            if (num >= maxnum) {\n                app.error('库存不足');\n                return;\n            }\n            var cartlist = this.cartlist;\n            cartlist[index].prolist[index2].num++;\n            this.cartlist = cartlist\n        }else{\n            var buymax = parseInt(e.currentTarget.dataset.buymax);\n            if (buymax>0 && num > buymax) {\n                app.alert('每人限购'+buymax);\n                return;\n            }\n            var xixie_cartlist = this.xixie_cartlist;\n            xixie_cartlist[index].prolist[index2].num++;\n            this.xixie_cartlist = xixie_cartlist\n        }\n        \n        this.calculate();\n        var that = this;\n        app.post('ApiShop/cartChangenum', {id: cartid,num: num + 1,type:type}, function (data){\n            if (data.status == 1) {\n                 //that.getdata();\n            }else if(data.status==2){\n\t\t\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\t\t\tcartlist[index].prolist[index2].num = data.num;\n\t\t\t\t\t\t} else {\n                app.error(data.msg);\n                if(!type){\n                    cartlist[index].prolist[index2].num--;\n                }else{\n                    xixie_cartlist[index].prolist[index2].num--;\n                }\n            }\n        });\n    },\n    //减\n    gwcminus: function (e) {\n        var type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\n        var index = parseInt(e.currentTarget.dataset.index);\n        var index2 = parseInt(e.currentTarget.dataset.index2);\n        var cartid = e.currentTarget.dataset.cartid;\n        \n        var num = parseInt(e.currentTarget.dataset.num);\n        if (num == 1) return;\n        if(!type){\n            var maxnum = parseInt(e.currentTarget.dataset.max);\n            var limit_start = parseInt(e.currentTarget.dataset.limit_start);\n            var limit_start_guige = parseInt(e.currentTarget.dataset.limit_start_guige);\n            if(limit_start_guige > 0 && num <= limit_start_guige){\n            \tapp.error('该商品规格'+limit_start_guige+'件起售');\n            \treturn;\n            }\n            if(limit_start > 0 && num <= limit_start){\n            \tapp.error('该商品'+limit_start+'件起售');\n            \treturn;\n            }\n            var cartlist = this.cartlist;\n            cartlist[index].prolist[index2].num--;\n            this.cartlist = cartlist\n            this.calculate();\n        }else{\n            var xixie_cartlist = this.xixie_cartlist;\n            xixie_cartlist[index].prolist[index2].num--;\n            this.xixie_cartlist = xixie_cartlist\n            this.calculate();\n        }\n\n        var that = this;\n        app.post('ApiShop/cartChangenum', {id: cartid,num: num - 1,type:type}, function (data) {\n        if (data.status == 1) {\n            //that.getdata();\n        } else {\n            app.error(data.msg);\n            if(!type){\n                cartlist[index].prolist[index2].num++;\n            }else{\n                xixie_cartlist[index].prolist[index2].num++;\n            }\n        }\n      });\n    },\n    //输入\n    gwcinput: function (e) {\n        var type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\n        var index = parseInt(e.currentTarget.dataset.index);\n        var index2 = parseInt(e.currentTarget.dataset.index2);\n        var maxnum = parseInt(e.currentTarget.dataset.max);\n        var cartid = e.currentTarget.dataset.cartid;\n        var num = e.currentTarget.dataset.num;\n        var newnum = parseInt(e.detail.value);\n        if (num == newnum) return;\n        if (newnum < 1) {\n            app.error('最小数量为1');\n            return;\n        }\n        if(!type){\n            var limit_start = parseInt(e.currentTarget.dataset.limit_start);\n            var limit_start_guige = parseInt(e.currentTarget.dataset.limit_start_guige);\n            if(limit_start_guige > 0 && newnum < limit_start_guige){\n                app.error('该商品规格'+limit_start_guige+'件起售');\n                return;\n            }\n            if(limit_start > 0 && newnum < limit_start){\n                app.error('该商品'+limit_start+'件起售');\n                return;\n            }\n            if (newnum > maxnum) {\n                app.error('库存不足');\n                return;\n            }\n            var cartlist = this.cartlist;\n            cartlist[index].prolist[index2].num = newnum;\n            this.cartlist = cartlist\n            this.calculate();\n        }else{\n            var buymax = parseInt(e.currentTarget.dataset.buymax);\n            if (buymax>0 && num > buymax) {\n                app.alert('每人限购'+buymax);\n                return;\n            }\n            var xixie_cartlist = this.xixie_cartlist;\n            xixie_cartlist[index].prolist[index2].num = newnum;\n            this.xixie_cartlist = xixie_cartlist\n            this.calculate();\n        }\n        var that = this;\n        app.post('ApiShop/cartChangenum', {id: cartid,num: newnum,type:type}, function (data) {\n            if (data.status == 1) {\n                //that.getdata();\n            }else if(data.status==2){\n\t\t\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\t\t\tcartlist[index].prolist[index2].num = data.num;\n\t\t\t\t\t\t} else {\n                app.error(data.msg);\n            }\n        });\n    },\n    addcart:function(){\n        this.getdata();\n    }\n  }\n};\n</script>\n<style>\n.container{height:100%}\n.cartmain .item {width: 94%;margin:20rpx 3%;background: #fff;border-radius:20rpx;padding:30rpx 3%;}\n.cartmain .item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.cartmain .item .radio .radio-img{width:100%;height:100%}\n\n.cartmain .item .btitle{width:100%;display:flex;align-items:center;margin-bottom:30rpx}\n.cartmain .item .btitle-name{color:#222222;font-weight:bold;font-size:28rpx;}\n.cartmain .item .btitle-del{display:flex;align-items:center;color:#999999;font-size:24rpx;}\n.cartmain .item .btitle-del .img{width:24rpx;height:24rpx}\n\n.cartmain .item .content {width:100%;position: relative;display:flex;align-items:center;}\n.cartmain .item .content .proinfo{flex:1;display:flex;padding:20rpx 0;border-bottom:1px solid #f2f2f2}\n.cartmain .item .content .proinfo .img {width: 176rpx;height: 176rpx;}\n.cartmain .item .content .detail {flex:1;margin-left:20rpx;height: 176rpx;position: relative;}\n.cartmain .item .content .detail .title {color: #222222;font-weight:bold;font-size:28rpx;line-height:34rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:68rpx}\n.cartmain .item .content .detail .desc {margin-top:0rpx;height: auto;color: #999;overflow: hidden;font-size: 20rpx;}\n.cartmain .item .content .detail .desc text{width: 350rpx;display: block;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\n.cartmain .item .content .prodel{width:24rpx;height:24rpx;position:absolute;top:90rpx;right:0}\n.cartmain .item .content .prodel-img{width:100%;height:100%}\n.cartmain .item .content .price{margin-top:10rpx;height:60rpx;line-height:60rpx;font-size:32rpx;font-weight:bold;display:flex;align-items:center}\n.cartmain .item .content .addnum {position: absolute;right: 0;bottom:0rpx;font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\n.cartmain .item .content .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.cartmain .item .content .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.cartmain .item .content .addnum .img{width:24rpx;height:24rpx}\n.cartmain .item .content .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\n.cartmain .item .content .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx;margin: 0 15rpx;}\n\n.cartmain .item .bottom{width: 94%;margin: 0 3%;border-top: 1px #e5e5e5 solid;padding: 10rpx 0px;overflow: hidden;color: #ccc;display:flex;align-items:center;justify-content:flex-end}\n.cartmain .item .bottom .f1{display:flex;align-items:center;color:#333}\n.cartmain .item .bottom .f1 image{width:40rpx;height:40rpx;border-radius:4px;margin-right:4px}\n.cartmain .item .bottom .op {border: 1px #ff4246 solid;border-radius: 10rpx;color: #ff4246;padding: 0 10rpx;height: 46rpx;line-height: 46rpx;margin-left: 10rpx;}\n\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\n.footer .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\n.footer .radio .radio-img{width:100%;height:100%}\n.footer .text0{color:#666666;font-size:24rpx;}\n.footer .text1 {height: 110rpx;line-height: 110rpx;color:#444;font-weight:bold;font-size:24rpx;}\n.footer .text2 {color: #F64D00;font-size: 36rpx;font-weight:bold}\n.footer .text3 {color: #F64D00;font-size: 28rpx;font-weight:bold}\n.footer .op{width: 216rpx;height: 80rpx;line-height:80rpx;border-radius: 6rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center;margin-left:30rpx}\n\n.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:12rpx 160rpx}\n.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}\n.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\n.xihuan-text .txt{color:#111;font-size:30rpx}\n.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}\n\n.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}\n\n.data-empty {width: 100%; text-align: center; padding-top:100rpx;padding-bottom:100rpx}\n.data-empty-img{ width: 300rpx; height: 300rpx; display: inline-block; }\n.data-empty-text{ display: block; text-align: center; color: #999999; font-size:32rpx; width: 100%; margin-top: 30rpx; } \n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cart.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839378764\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}