{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/product.vue?19b0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/product.vue?5dc3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/product.vue?bdc5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/product.vue?9c32", "uni-app:///pages/shop/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/product.vue?9508", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/product.vue?e6b2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "textset", "onLoad", "onShow", "uni", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "imageUrl", "query", "onUnload", "clearInterval", "methods", "glassesPopupClose", "classChange", "scrollToupper", "slideClass", "showLinkChange", "getdata", "that", "app", "id", "longitude", "latitude", "mendian_id", "devicedata", "adUnitId", "rewardedVideoAd", "console", "desc", "setTimeout", "view0", "size", "rect", "scrollOffset", "view1", "view2", "loadImg", "swiper<PERSON><PERSON>e", "getCurrentSwiperHeight", "payvideo", "parsevideo", "buydialogChange", "addfavorite", "proid", "type", "addfavorite2", "url", "shareClick", "handleClickMask", "showPoster", "shareScheme", "schemeDialogClose", "posterDialogClose", "showfuwudetail", "hidefuwudetail", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "changetoptab", "scroll", "sharemp", "shareapp", "<PERSON><PERSON><PERSON><PERSON>", "phoneNumber", "fail", "<PERSON><PERSON><PERSON><PERSON>", "name", "scale", "change<PERSON><PERSON>ge", "addcart2", "ggid", "num", "tobuy", "showsubqrcode", "closesubqrcode", "addcart", "showgg1Dialog", "closegg1Dialog", "showgg2Dialog", "closegg2Dialog", "autoaddlogin", "wxlogin", "success", "code", "pid", "alilogin", "copy1", "getApp", "purchaseorder", "<PERSON><PERSON><PERSON><PERSON>", "toLocation", "showGuigeType", "reset", "changeradio", "changeradioAll", "Object", "item", "gwcplus", "gwcminus", "gwcnum", "gwcinput", "guigeTypebuy", "prodatagg", "totalNum", "thisprodata", "prodata", "glass_record_id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxmCA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8+Bz1B;AACA;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,6DACA,uDACA,sDACA,uDACA,kDACA,4DACA,8DACA,wDACA,oDACA,oDACA,wDACA,yDACA,uDACA,uDACA,qDACA,wDACA,oDACA,mDACA,mDACA,kDACA,qDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,2DACA,4DACA,mEACA,sDACA,0DACA,0DACA,2DACA,0DACA,sDACA,4DACA,4DACA,4DACA,4DACA,sDACA,0DACA,yDACA,6DACA,gEACA,iEAEA,yDACA,qDACA,uDACA,oDACA,uDACA,2DAEA,wDACA,uDACA,oDACA,mEACA,sDACA,sDACA,uDACA,8DACA,0DACA,sDACA,sDACA,qDACA,sDACA,gDACA,2DACA,mDACA,2DACA;EAEA;EACAC;IACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACA;MACAD;MACAG;MACAC;IACA;EACA;EACAC;IACAC;EACA;EAEAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACA;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;QACAN;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAD;QACA;QACA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACA;YACAA;UACA;QACA;QACAjB;UACAG;QACA;QACA;UACAc;UACA;YACAA;YACAA;UACA;QACA;QAEAA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACA;UACAC;QACA;QAEA;UACA;YACAD;YACAC;YACA;cACAA;gBAAAM;cAAA;YACA;YACA;YACAC;cAAAP;cAAAO;YAAA;cAAAP;YAAA;YACAO;cACAP;cACA;cACAQ;cACAD;cACAA;cACAR;gBAAAd;gBAAAC;gBAAAuB;cAAA;YACA;YACAF;cACAP;cACA;gBACA;gBACAD;kBAAAd;kBAAAC;kBAAAuB;gBAAA;cACA;gBACA;gBACA;cAAA;cAEAF;cACAA;YACA;UACA;QACA;UACAR;YAAAd;YAAAC;YAAAuB;UAAA;QACA;;QAEA;QACA;UAKAV;UAGAA;QAEA;;QAEA;;QAEA;UACAC;YACAD;YACAA;YACAA;UACA;YACAS;UACA;QACA;QAEAE;UACA;UACAC;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;YACA,SACAf;UACA;UACA;UACAgB;YACAH;YAAA;YACAC;YAAA;YACAC;UACA;YACA,SACAf;UACA;UACA;UACAiB;YACAJ;YAAA;YACAC;YAAA;YACAC;UACA;YACA,SACAf;UACA;QACA;MACA;IACA;IACAkB;MACA;IACA;IACAC;MAAA;MACA;MACAnB;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAoB;MAAA;MAEA;MACA9B;MACA;MACAA;QACA;QACA;UACA;QACA;MACA;IAUA;IACA+B;MACA;MACAtC;IACA;IACAuC;MACA;MACAvC;IACA;IACAwC;MACA;QACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAvB;QAAAwB;QAAAC;MAAA;QACA;UACA1B;QACA;QACAC;MACA;IACA;IACA;IACA0B;MACA;MACA;QACA;QACA1B;UAAAwB;UAAAC;QAAA;UACA;YACA1B;UACA;UACAC;QACA;MACA;QACA;QACA;UACA;YAAA2B;UAAA;YAAAA;UAAA;QACA;QACA;UACA;YAAAA;UAAA;YAAAA;UAAA;QACA;QACA;UACAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA3B;QACA;UACA;YACAA;YACA;UACA;UACAA;QACA;MACA;IACA;IACA4B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA/B;MACAA;MACAC;MACAA;QAAAwB;MAAA;QACAxB;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IAEAgC;MACA;MACA/B;MACAA;QAAAwB;MAAA;QACAxB;QACA;UACAA;QACA;UACAD;UACAA;QACA;MACA;IACA;IAEAiC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAzD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;;IACA0D;MACA;MACA;MACA;MACA;MACAhC;IACA;IACAiC;MACA;MACA;MACA;MACA;QACA1C;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA2C;MACA1C;MACA;IACA;IACA2C,+BA6CA;IACAC;MACA;MACA9D;QACA+D;QACAC,uBACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;MACAjE;QACAqB;QACAD;QACA8C;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACApD;QACA;MACA;MACA;MACA;QACAC;QACA;MACA;MACAA;QAAAwB;QAAA4B;QAAAC;MAAA;QACAtD;QACA;UACAC;QACA;UACAA;QACA;MACA;IACA;IACAsD;MACA;MACA;MACA;MACA;MACA;MACA;QACAvD;QACA;MACA;MACA;MACA;QACAC;QACA;MACA;MACA;MACAA;IACA;IACAuD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAjD;MACA;IACA;IACAkD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA9D;QACA;UACA;UACA;QAAA,CACA;UACAA;QACA;QACA;MACA;IACA;IACA+D;MACA;MACA/F;QAAAgG;UACAxD;UACA;UACA;UACAR;YAAAiE;YAAAC;UAAA;YACA;cACA;cACA;YAAA,CACA;cACAlE;YACA;YACA;UACA;QACA;MAAA;IACA;IACAmE,+BA4CA;IACAC;MACA;MACA;MACAtF;QACAT;QACA2F;UACAK;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IACAC,mCAWA;IACAC;MACA;MACA1E;MACAC;QAAAC;QAAAyE;MAAA;QACA3E;QACA;UACAC;UACA;QACA;QAEAD;QACAA;QAEAA;QACAA;QACA,0CACAA,oDAEAA;QACAA;UAAA;QAAA;QACAA;QACAA;MACA;IACA;IACA4E;MACA;MACA;MACA;MACA;MACA;QACA;QACA5E;QACAA;MACA;QACAA;MACA;MACAA;IACA;IACA6E;MACA;MACA;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA/E;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;MACA;MACA;IACA;IACAgF;MACA;MACA;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAD;MACA;MACA;QACAA;QACA;MACA;QACA;MACA;MACA;MACA;IACA;IACAE;MAAA;MACA;MACA;MACA;MACApF;QACA8E;UACA;YACAO;UACA;QACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA;MACA;MACA;QACA;MACA;MACAA;MACA;MACA;QACAtF;QACA;MACA;MACA;MACAA;QAAAuF;QAAAC;MAAA;QACA;UACAxF;QACA;UACAA;QACA;QACAD;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC33DA;AAAA;AAAA;AAAA;AAA0sC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACA9tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/shop/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/shop/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=575548cf&scoped=true&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&id=575548cf&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"575548cf\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/shop/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=575548cf&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    buydialogShow: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-show/buydialog-show\" */ \"@/components/buydialog-show/buydialog-show.vue\"\n      )\n    },\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    dpProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-item/dp-product-item\" */ \"@/components/dp-product-item/dp-product-item.vue\"\n      )\n    },\n    buydialogPifa: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa/buydialog-pifa\" */ \"@/components/buydialog-pifa/buydialog-pifa.vue\"\n      )\n    },\n    buydialogPifa2: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa2/buydialog-pifa2\" */ \"@/components/buydialog-pifa2/buydialog-pifa2.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    buydialogPurchase: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-purchase/buydialog-purchase\" */ \"@/components/buydialog-purchase/buydialog-purchase.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.bboglist.length : null\n  var m2 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var g1 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.tjdatalist.length\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    g1 > 0 &&\n    _vm.toptabbar_index == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1 && g1 > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g2 =\n    _vm.isload && _vm.shopset.show_header_pic !== 0 && _vm.isplay == 0\n      ? _vm.product.pics.length\n      : null\n  var g3 = _vm.isload\n    ? _vm.showtoptabbar == 1 && _vm.couponlist.length > 0\n    : null\n  var l0 =\n    _vm.isload && g3\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m10 = _vm.t(\"color1rgb\")\n          var m11 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m10: m10,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    _vm.custom.show_price &&\n    (_vm.shopset.hide_cost == 0 || _vm.shopset.hide_sellprice == 0) &&\n    _vm.shopset.hide_cost == 0 &&\n    !_vm.shopset.cost_color\n      ? _vm.t(\"color1\")\n      : null\n  var m13 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    _vm.custom.show_price &&\n    (_vm.shopset.hide_cost == 0 || _vm.shopset.hide_sellprice == 0) &&\n    _vm.shopset.hide_cost == 0 &&\n    !_vm.shopset.cost_color\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    _vm.custom.show_price &&\n    (_vm.shopset.hide_cost == 0 || _vm.shopset.hide_sellprice == 0) &&\n    _vm.shopset.hide_sellprice == 0 &&\n    _vm.sell_price &&\n    !_vm.shopset.sellprice_color\n      ? _vm.t(\"color1\")\n      : null\n  var m15 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    _vm.custom.show_price &&\n    (_vm.shopset.hide_cost == 0 || _vm.shopset.hide_sellprice == 0) &&\n    _vm.shopset.hide_sellprice == 0 &&\n    !_vm.sell_price &&\n    !_vm.shopset.sellprice_color\n      ? _vm.t(\"color1\")\n      : null\n  var m16 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    !_vm.custom.show_price\n      ? _vm.t(\"color1\")\n      : null\n  var m17 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    !_vm.custom.show_price &&\n    !(_vm.showprice_dollar && _vm.product.usdmin_price != 0) &&\n    !(_vm.custom.product_guige_showtype && _vm.shopset.show_guigetype == 2)\n      ? !_vm.isNull(_vm.product.min_service_fee) &&\n        _vm.product.service_fee_switch &&\n        _vm.product.service_fee > 0\n      : null\n  var m18 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    !_vm.custom.show_price &&\n    !(_vm.showprice_dollar && _vm.product.usdmin_price != 0) &&\n    !(_vm.custom.product_guige_showtype && _vm.shopset.show_guigetype == 2) &&\n    m17\n      ? !_vm.isNull(_vm.product.max_service_fee) &&\n        _vm.product.max_service_fee != _vm.product.min_service_fee\n      : null\n  var m19 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    !_vm.custom.show_price &&\n    !(_vm.showprice_dollar && _vm.product.usdmin_price != 0) &&\n    !(_vm.custom.product_guige_showtype && _vm.shopset.show_guigetype == 2) &&\n    m17\n      ? _vm.t(\"服务费\")\n      : null\n  var m20 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    !_vm.custom.show_price &&\n    _vm.show_money_price\n      ? _vm.t(\"color1\")\n      : null\n  var m21 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    !_vm.custom.show_price &&\n    _vm.show_money_price\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m22 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type) &&\n    !_vm.custom.show_price &&\n    _vm.show_money_price\n      ? _vm.t(\"余额\")\n      : null\n  var g4 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"0\" || !_vm.sysset.price_show_type)\n      ? _vm.product.priceshows && _vm.product.priceshows.length > 0\n      : null\n  var m23 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.ictips\n      ? _vm.t(\"color1\")\n      : null\n  var m24 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"1\" &&\n    _vm.product.lvprice == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m25 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"1\" &&\n    _vm.product.lvprice == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m26 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"1\" &&\n    _vm.product.lvprice == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m27 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m28 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"1\" &&\n    _vm.show_money_price\n      ? _vm.t(\"color1\")\n      : null\n  var m29 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"1\" &&\n    _vm.show_money_price\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m30 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"1\" &&\n    _vm.show_money_price\n      ? _vm.t(\"余额\")\n      : null\n  var m31 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"0\"\n      ? _vm.t(\"color1\")\n      : null\n  var m32 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.show_money_price\n      ? _vm.t(\"color1\")\n      : null\n  var m33 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.show_money_price\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m34 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.show_money_price\n      ? _vm.t(\"余额\")\n      : null\n  var m35 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.sysset.price_show_type == \"2\" &&\n    _vm.product.lvprice == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m36 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.sysset.price_show_type == \"2\" &&\n    _vm.product.lvprice == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m37 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    (_vm.sysset.price_show_type == \"2\" || _vm.sysset.price_show_type == \"1\") &&\n    _vm.product.is_vip == \"0\" &&\n    _vm.sysset.price_show_type == \"2\" &&\n    _vm.product.lvprice == 1\n      ? _vm.t(\"color1\")\n      : null\n  var g5 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0)\n      ? _vm.product.labels && _vm.product.labels.length > 0\n      : null\n  var m38 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    !(_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.xunjia_text\n      ? _vm.t(\"color1\")\n      : null\n  var g6 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_product_name)) &&\n    !(_vm.product.price_type != 1 || _vm.product.min_price > 0)\n      ? _vm.product.labels && _vm.product.labels.length > 0\n      : null\n  var m39 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status == 0 &&\n    _vm.custom.product_commission_desc &&\n    _vm.shopset.commission_desc &&\n    _vm.shopset.showcommission == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m40 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status == 0 &&\n    _vm.custom.product_commission_desc &&\n    _vm.shopset.commission_desc &&\n    _vm.shopset.showcommission == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m41 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status == 0 &&\n    !(\n      _vm.custom.product_commission_desc &&\n      _vm.shopset.commission_desc &&\n      _vm.shopset.showcommission == 1\n    ) &&\n    _vm.shopset.showcommission == 1 &&\n    (_vm.product.commission > 0 || _vm.product.commissionScore > 0) &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m42 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status == 0 &&\n    !(\n      _vm.custom.product_commission_desc &&\n      _vm.shopset.commission_desc &&\n      _vm.shopset.showcommission == 1\n    ) &&\n    _vm.shopset.showcommission == 1 &&\n    (_vm.product.commission > 0 || _vm.product.commissionScore > 0) &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m43 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status == 0 &&\n    !(\n      _vm.custom.product_commission_desc &&\n      _vm.shopset.commission_desc &&\n      _vm.shopset.showcommission == 1\n    ) &&\n    _vm.shopset.showcommission == 1 &&\n    (_vm.product.commission > 0 || _vm.product.commissionScore > 0) &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m44 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status &&\n    (_vm.product.commission_total1 > 0 || _vm.product.commission_total2 > 0)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m45 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status &&\n    (_vm.product.commission_total1 > 0 || _vm.product.commission_total2 > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m46 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status &&\n    (_vm.product.commission_total1 > 0 || _vm.product.commission_total2 > 0) &&\n    _vm.product.commission_total1 > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m47 =\n    _vm.isload &&\n    _vm.custom.commission_max_times_status &&\n    (_vm.product.commission_total1 > 0 || _vm.product.commission_total2 > 0) &&\n    _vm.product.commission_total2 > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m48 =\n    _vm.isload && _vm.custom.active_coin && _vm.product.give_active_coin > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m49 =\n    _vm.isload && _vm.custom.active_coin && _vm.product.give_active_coin > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m50 =\n    _vm.isload && _vm.custom.active_coin && _vm.product.give_active_coin > 0\n      ? _vm.t(\"激活币\")\n      : null\n  var m51 =\n    _vm.isload &&\n    _vm.custom.member_goldmoney_silvermoney &&\n    (_vm.product.givegoldmoney > 0 || _vm.product.givesilvermoney > 0)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m52 =\n    _vm.isload &&\n    _vm.custom.member_goldmoney_silvermoney &&\n    (_vm.product.givegoldmoney > 0 || _vm.product.givesilvermoney > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m53 =\n    _vm.isload &&\n    _vm.custom.member_goldmoney_silvermoney &&\n    (_vm.product.givegoldmoney > 0 || _vm.product.givesilvermoney > 0) &&\n    _vm.product.givesilvermoney > 0\n      ? _vm.t(\"银值\")\n      : null\n  var m54 =\n    _vm.isload &&\n    _vm.custom.member_goldmoney_silvermoney &&\n    (_vm.product.givegoldmoney > 0 || _vm.product.givesilvermoney > 0) &&\n    _vm.product.givegoldmoney > 0\n      ? _vm.t(\"金值\")\n      : null\n  var m55 =\n    _vm.isload && _vm.product.buyselect_commission > 0 ? _vm.t(\"佣金\") : null\n  var m56 =\n    _vm.isload && _vm.product.product_type == 5 ? _vm.t(\"color1rgb\") : null\n  var m57 = _vm.isload && _vm.product.product_type == 5 ? _vm.t(\"color1\") : null\n  var m58 = _vm.isload && _vm.product.product_type == 5 ? _vm.t(\"优惠券\") : null\n  var l1 =\n    _vm.isload && _vm.product.product_type == 5\n      ? _vm.__map(_vm.product.fenqi_data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m59 = item.fenqi_give_num ? _vm.t(\"优惠券\") : null\n          var m60 = !item.fenqi_give_num ? _vm.t(\"优惠券\") : null\n          return {\n            $orig: $orig,\n            m59: m59,\n            m60: m60,\n          }\n        })\n      : null\n  var m61 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m62 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m63 =\n    _vm.isload && _vm.product.give_commission_max > 0 ? _vm.t(\"佣金上限\") : null\n  var m64 =\n    _vm.isload && _vm.product.give_commission_max > 0 ? _vm.t(\"佣金上限\") : null\n  var g7 = _vm.isload\n    ? _vm.cuxiaolist.length > 0 ||\n      _vm.couponlist.length > 0 ||\n      _vm.fuwulist.length > 0 ||\n      _vm.product.discount_tips != \"\"\n    : null\n  var g8 = _vm.isload && g7 ? _vm.fuwulist.length : null\n  var g9 = _vm.isload && g7 ? _vm.cuxiaolist.length : null\n  var l2 =\n    _vm.isload && g7 && g9 > 0\n      ? _vm.__map(_vm.cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m65 = _vm.t(\"color1rgb\")\n          var m66 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m65: m65,\n            m66: m66,\n          }\n        })\n      : null\n  var g10 =\n    _vm.isload && g7\n      ? _vm.couponlist.length > 0 && _vm.showtoptabbar == 0\n      : null\n  var l3 =\n    _vm.isload && g7 && g10\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m67 = _vm.t(\"color1rgb\")\n          var m68 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m67: m67,\n            m68: m68,\n          }\n        })\n      : null\n  var m69 =\n    _vm.isload &&\n    _vm.commentposition == 0 &&\n    _vm.shopset.comment == 1 &&\n    _vm.commentcount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g11 =\n    _vm.isload &&\n    _vm.commentposition == 0 &&\n    _vm.shopset.comment == 1 &&\n    _vm.commentcount > 0\n      ? _vm.commentlist.length\n      : null\n  var m70 =\n    _vm.isload && _vm.shopset.showjd == 1 && _vm.business\n      ? _vm.t(\"color1\")\n      : null\n  var m71 =\n    _vm.isload && _vm.shopset.showjd == 1 && _vm.business\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g12 = _vm.isload\n    ? _vm.showNearbyMendian &&\n      _vm.mendianids.length > 0 &&\n      _vm.latitude &&\n      _vm.longitude\n    : null\n  var m72 = _vm.isload && g12 ? _vm.t(\"门店\") : null\n  var g13 = _vm.isload && g12 ? _vm.mendianids.length : null\n  var g14 = _vm.isload && g12 && g13 > 1 ? _vm.mendianids.length : null\n  var g15 = _vm.isload && g12 ? _vm.mendianids.length : null\n  var m73 = _vm.isload && g12 ? _vm.t(\"门店\") : null\n  var m74 =\n    _vm.isload && g12 && _vm.mendian.distance ? _vm.t(\"color1rgb\") : null\n  var m75 = _vm.isload && g12 && _vm.mendian.distance ? _vm.t(\"color1\") : null\n  var m76 = _vm.isload ? _vm.isEmpty(_vm.product.paramdata) : null\n  var m77 = _vm.isload && _vm.commentposition == 1 ? _vm.t(\"color1\") : null\n  var g16 =\n    _vm.isload && _vm.commentposition == 1 ? _vm.commentlist.length : null\n  var g17 = _vm.isload ? _vm.tjdatalist.length : null\n  var l4 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    _vm.shopdetail_menudataList\n      ? _vm.__map(_vm.shopdetail_menudataList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m78 =\n            item.isShow == 1 && item.menuType == 2 && _vm.cartnum > 0\n              ? _vm.t(\"color1rgb\")\n              : null\n          return {\n            $orig: $orig,\n            m78: m78,\n          }\n        })\n      : null\n  var m79 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !_vm.shopdetail_menudataList &&\n    _vm.custom.shop_gwc_name &&\n    _vm.shopset.gwc_showst == 1 &&\n    _vm.cartnum > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m80 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !_vm.shopdetail_menudataList &&\n    !_vm.custom.shop_gwc_name &&\n    _vm.cartnum > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m81 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    _vm.showjiesheng == 1\n      ? _vm.t(\"color2\")\n      : null\n  var m82 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    _vm.showjiesheng == 1 &&\n    !_vm.product.buybtn_link_url\n      ? _vm.t(\"color1\")\n      : null\n  var m83 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    _vm.showjiesheng == 1 &&\n    !!_vm.product.buybtn_link_url\n      ? _vm.t(\"color1\")\n      : null\n  var m84 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    _vm.is_member_auto_addlogin == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m85 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    _vm.product.price_type == 1 &&\n    !_vm.custom.product_xunjia_btn\n      ? _vm.t(\"color1\")\n      : null\n  var m86 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    _vm.product.price_type == 1 &&\n    _vm.custom.product_xunjia_btn &&\n    _vm.product.show_xunjia_btn == \"1\" &&\n    _vm.product.xunjia_btn_url &&\n    !_vm.product.xunjia_btn_bgcolor\n      ? _vm.t(\"color1\")\n      : null\n  var m87 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    _vm.product.price_type == 1 &&\n    _vm.custom.product_xunjia_btn &&\n    _vm.product.show_xunjia_btn == \"1\" &&\n    !_vm.product.xunjia_btn_url &&\n    !_vm.product.xunjia_btn_bgcolor\n      ? _vm.t(\"color1\")\n      : null\n  var m88 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    _vm.custom.product_guige_showtype == 1 &&\n    _vm.shopset.show_guigetype == 2 &&\n    _vm.product.freighttype != 3 &&\n    _vm.product.freighttype != 4 &&\n    _vm.product.product_type != 9\n      ? _vm.t(\"color2\")\n      : null\n  var m89 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      _vm.custom.product_guige_showtype == 1 && _vm.shopset.show_guigetype == 2\n    ) &&\n    !_vm.product.addcart_link_url &&\n    _vm.dgprodata == \"\" &&\n    _vm.product.freighttype != 3 &&\n    _vm.product.freighttype != 4 &&\n    _vm.product.product_type != 9\n      ? _vm.t(\"color2\")\n      : null\n  var m90 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      _vm.custom.product_guige_showtype == 1 && _vm.shopset.show_guigetype == 2\n    ) &&\n    !!_vm.product.addcart_link_url &&\n    _vm.product.freighttype != 3 &&\n    _vm.product.freighttype != 4 &&\n    _vm.product.product_type != 9\n      ? _vm.t(\"color2\")\n      : null\n  var m91 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    _vm.product.shop_yuding &&\n    _vm.product.stock <= 0 &&\n    _vm.product.yuding_stock > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m92 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      _vm.product.shop_yuding &&\n      _vm.product.stock <= 0 &&\n      _vm.product.yuding_stock > 0\n    ) &&\n    _vm.custom.product_guige_showtype == 1 &&\n    _vm.shopset.show_guigetype == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m93 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      _vm.product.shop_yuding &&\n      _vm.product.stock <= 0 &&\n      _vm.product.yuding_stock > 0\n    ) &&\n    !(\n      _vm.custom.product_guige_showtype == 1 && _vm.shopset.show_guigetype == 2\n    ) &&\n    !_vm.product.buybtn_link_url &&\n    _vm.dgprodata != \"\" &&\n    _vm.devicedata != \"\"\n      ? _vm.t(\"color1\")\n      : null\n  var m94 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      _vm.product.shop_yuding &&\n      _vm.product.stock <= 0 &&\n      _vm.product.yuding_stock > 0\n    ) &&\n    !(\n      _vm.custom.product_guige_showtype == 1 && _vm.shopset.show_guigetype == 2\n    ) &&\n    !_vm.product.buybtn_link_url &&\n    !(_vm.dgprodata != \"\" && _vm.devicedata != \"\")\n      ? _vm.t(\"color1\")\n      : null\n  var m95 =\n    _vm.isload &&\n    (!_vm.custom.product_detail_special ||\n      (_vm.custom.product_detail_special && _vm.shopset.show_option_group)) &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !_vm.showfuwudialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.is_member_auto_addlogin == 1) &&\n    !(_vm.product.price_type == 1) &&\n    !(\n      _vm.product.shop_yuding &&\n      _vm.product.stock <= 0 &&\n      _vm.product.yuding_stock > 0\n    ) &&\n    !(\n      _vm.custom.product_guige_showtype == 1 && _vm.shopset.show_guigetype == 2\n    ) &&\n    !!_vm.product.buybtn_link_url\n      ? _vm.t(\"color1\")\n      : null\n  var g18 =\n    _vm.isload &&\n    (_vm.product.product_type == 4 || _vm.product.product_type == 6)\n      ? JSON.parse(_vm.product.guigedata).length == 2 &&\n        _vm.product.product_type != 6\n      : null\n  var m96 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m97 = _vm.isload && _vm.showScheme ? _vm.t(\"color1\") : null\n  var m98 = _vm.isload && _vm.showScheme ? _vm.t(\"color1rgb\") : null\n  var m99 =\n    _vm.isload && _vm.showLinkStatus && _vm.business.tel\n      ? _vm.t(\"color1\")\n      : null\n  var g19 =\n    _vm.product.guige_show_type == 1 &&\n    JSON.parse(_vm.product.guigedata).length == 2\n  var m100 = g19 && _vm.show_guige_type == 1 ? _vm.t(\"color1\") : null\n  var m101 = g19 && _vm.show_guige_type == 1 ? _vm.t(\"color2\") : null\n  var m102 = g19 && _vm.show_guige_type == 1 ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        g1: g1,\n        m8: m8,\n        m9: m9,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        g4: g4,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        g5: g5,\n        m38: m38,\n        g6: g6,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        m47: m47,\n        m48: m48,\n        m49: m49,\n        m50: m50,\n        m51: m51,\n        m52: m52,\n        m53: m53,\n        m54: m54,\n        m55: m55,\n        m56: m56,\n        m57: m57,\n        m58: m58,\n        l1: l1,\n        m61: m61,\n        m62: m62,\n        m63: m63,\n        m64: m64,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n        l2: l2,\n        g10: g10,\n        l3: l3,\n        m69: m69,\n        g11: g11,\n        m70: m70,\n        m71: m71,\n        g12: g12,\n        m72: m72,\n        g13: g13,\n        g14: g14,\n        g15: g15,\n        m73: m73,\n        m74: m74,\n        m75: m75,\n        m76: m76,\n        m77: m77,\n        g16: g16,\n        g17: g17,\n        l4: l4,\n        m79: m79,\n        m80: m80,\n        m81: m81,\n        m82: m82,\n        m83: m83,\n        m84: m84,\n        m85: m85,\n        m86: m86,\n        m87: m87,\n        m88: m88,\n        m89: m89,\n        m90: m90,\n        m91: m91,\n        m92: m92,\n        m93: m93,\n        m94: m94,\n        m95: m95,\n        g18: g18,\n        m96: m96,\n        m97: m97,\n        m98: m98,\n        m99: m99,\n        g19: g19,\n        m100: m100,\n        m101: m101,\n        m102: m102,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<block v-if=\"sysset.showgzts\">\n\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\n\t\t\t<view class=\"follow_topbar\">\n\t\t\t\t<view class=\"headimg\"><image :src=\"sysset.logo\"/></view>\n\t\t\t\t<view class=\"info\">\n\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\n\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\n\t\t\t</view>\n\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\n\t\t\t\t<view class=\"qrcodebox\">\n\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\"/>\n\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\n\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</uni-popup>\n\t\t</block>\n\n\t\t<view style=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\" v-if=\"bboglist.length>0\">\n\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\" :vertical=\"true\">\n\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\" class=\"flex-y-center\">\n\t\t\t\t\t<image :src=\"item.headimg\" style=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\"/>\n\t\t\t\t\t<view style=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">{{item.nickname}} {{item.showtime}}购买了该商品</view>\n\t\t\t\t</swiper-item>\n\t\t\t</swiper>\n\t\t</view>\n\n\t\t<view class=\"toptabbar_tab\" v-if=\"showtoptabbar==1 && toptabbar_show==1\">\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==0?'on':''\" :style=\"{color:toptabbar_index==0?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"0\">商品<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==1?'on':''\" :style=\"{color:toptabbar_index==1?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"1\">评价<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==2?'on':''\" :style=\"{color:toptabbar_index==2?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"2\">详情<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t<view class=\"item\" v-if=\"tjdatalist.length > 0\" :class=\"toptabbar_index==3?'on':''\" :style=\"{color:toptabbar_index==3?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"3\">推荐<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t</view>\n\n\t\t<scroll-view @scroll=\"scroll\" :scrollIntoView=\"scrollToViewId\" :scrollTop=\"scrollTop\" :scroll-y=\"true\" style=\"height:100%;overflow:scroll\">\n\t\t\n\t\t<view id=\"scroll_view_tab0\">\n\t\t\t<!-- 定制：如果没有主图和视频，则商品头图不显示，页面整体上移 -->\n\t\t\t<block v-if=\"shopset.show_header_pic !== 0 \">\n\t\t\t\t<view class=\"swiper-container\" v-if=\"isplay==0\">\n\t\t\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\"   :current=\"current\" :style=\"{ height: swiperHeight + 'px' }\">\n\t\t\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\n\t\t\t\t\t\t\t<swiper-item class=\"swiper-item\">\n\t\t\t\t\t\t\t\t<view v-if=\"show_image == 1\" class=\"filter-background\"></view>\n\t\t\t\t\t\t\t\t<view class=\"swiper-item-view\" :id=\"'content-wrap' + index\" :style=\"{ height: swiperHeight + 'px' }\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item\" mode=\"widthFix\" @tap=\"previewImage\" :data-urls=\"product.pics\" :data-url=\"item\" @load=\"loadImg\"  />\n\t\t\t\t\t\t\t\t\t<view v-if=\"show_image == 1\" class=\"lock-image\" @tap=\"gotolevelup\">\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/lock.png'\" mode=\"\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</swiper-item>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</swiper>\n\t\t\t\t\t<view class=\"imageCount\" v-if=\"product.diypics\" @tap=\"goto\" :data-url=\"'/pagesExt/shop/diylight?id='+product.id\" style=\"bottom: 92rpx; width: 140rpx;\">自助试灯</view>\n\t\t\t\t\t<view class=\"imageCount\">{{current+1}}/{{(product.pics).length}}</view>\n\t\t\t\t\t<block v-if=\"product.video && show_image == 0\">\n\t\t\t\t\t\t<block v-if=\"product.video_type==1\" >\n\t\t\t\t\t\t\t<view v-if=\"product.video_feedtype==1\"  class=\"wxfeedvideo\">\n\t\t\t\t\t\t\t\t<view class=\"videop\"><image class=\"playicon\" :src=\"pre_url+'/static/img/video.png'\"/><view class=\"txt\">播放视频</view></view>\n\t\t\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN  -->\n\t\t\t\t\t\t\t\t<channel-video class=\"feedvideo\" :feed-token=\"product.video_feedtoken\" :feed-id=\"product.video_feedid\" :finder-user-name=\"product.video_finderuser\" ></channel-video>\n\t\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view v-if=\"product.video_feedtype==0\" @tap=\"goto\" :data-url=\"product.video\" class=\"provideo\">\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/video.png'\"/><view class=\"txt\">查看视频</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view v-if=\"product.video_type==0\" class=\"provideo\" @tap=\"payvideo\">\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/video.png'\"/><view class=\"txt\">{{product.video_duration}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t<view class=\"videobox\" v-if=\"isplay==1\">\n\t\t\t\t<video autoplay=\"true\" class=\"video\" id=\"video\" :src=\"product.video\"></video>\n\t\t\t\t<view class=\"parsevideo\" @tap=\"parsevideo\">退出播放</view>\n\t\t\t</view>\t\t\t\n\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"showtoptabbar==1 && couponlist.length>0\" style=\"background:#fff;padding:0 16rpx\">\n\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 广告 -->\n\t\t\t<view style=\"background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0\" v-if=\"shopset.detail_guangao1\">\n\t\t\t\t<image :src=\"shopset.detail_guangao1\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"shopset.detail_guangao1\" @tap=\"showgg1Dialog\"/>\n\t\t\t</view>\n\t\t\t<uni-popup id=\"gg1Dialog\" ref=\"gg1Dialog\" type=\"dialog\" v-if=\"shopset.detail_guangao1 && shopset.detail_guangao1_t\">\n\t\t\t\t<image :src=\"shopset.detail_guangao1_t\" @tap=\"previewImage\" :data-url=\"shopset.detail_guangao1_t\" class=\"img\" mode=\"widthFix\" style=\"width:600rpx;height:auto;border-radius:10rpx;\"/>\n\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg1Dialog\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t</view>\n\t\t\t</uni-popup>\n\t\t\t\n\t\t\t<view class=\"header\">\n\t\t\t\t<block v-if=\"!custom.product_detail_special || (custom.product_detail_special && shopset.show_product_name)\">\n\t\t\t\t\t<block v-if=\"product.price_type != 1 || product.min_price > 0\">\n\t\t\t\t\t\t<view v-if=\"sysset.price_show_type=='0' || !sysset.price_show_type \">\n\t\t\t\t\t\t\t<!-- 定制的显示方式：字号 颜色等 -->\n\t\t\t\t\t\t\t<view v-if=\"custom.show_price\">\n\t\t\t\t\t\t\t\t<view class=\"price_share custom_price\" v-if=\"shopset.hide_cost==0 || shopset.hide_sellprice==0\">\n\t\t\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"price price-row2\" v-if=\"shopset.hide_cost==0\" :style=\"{color:shopset.cost_color?shopset.cost_color:t('color1')}\"><text :style=\"{color:shopset.cost_color?shopset.cost_color:t('color1')}\"><text class=\"custom_price_tag\">{{shopset.cost_name?shopset.cost_name:'成本价：￥'}}</text>{{product.cost_price}}</text></view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"price price-row1\" v-if=\"shopset.hide_sellprice==0\">\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex-s\" v-if=\"sell_price\" :style=\"{color:shopset.sellprice_color?shopset.sellprice_color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"custom_price_tag\" >{{shopset.sellprice_name?shopset.sellprice_name:'￥'}}</text>{{sell_price}}</text>\n                        <text v-if=\"product.price_show && product.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{product.price_show_text}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex-s\" v-else :style=\"{color:shopset.sellprice_color?shopset.sellprice_color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"custom_price_tag\" >{{shopset.sellprice_name?shopset.sellprice_name:'￥'}}</text>{{product.min_price}}<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text><text v-if=\"product.product_unit\">/{{product.product_unit}}</text>\n                        <text v-if=\"product.price_show && product.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{product.price_show_text}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"market_price\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}<text v-if=\"product.max_price!=product.min_price\">起</text></view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<block v-if=\"!custom.product_commission_desc\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 系统默认显示方式 -->\n\t\t\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t\t\t<view class=\"price_share\">\n\t\t\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1 flex-s\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"showprice_dollar && product.usdmin_price !=0\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"margin-right:20rpx;\"><text style=\"font-size:36rpx\">$</text>{{product.usdmin_price}}<text v-if=\"product.usdmax_price!=product.usdmin_price\">-{{product.usdmax_price}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"font-size: 40rpx;\"><text style=\"font-size:32rpx\">￥</text>{{product.min_price}}<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text></view>\n                        <text v-if=\"product.price_show && product.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{product.price_show_text}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t<block  v-if=\"custom.product_guige_showtype && shopset.show_guigetype==2\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">￥</text>{{sell_price}}\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">￥</text>{{product.min_price}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text>\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"product.product_unit\">/{{product.product_unit}}</text>\n                          <text v-if=\"product.price_show && product.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{product.price_show_text}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"!isNull(product.min_service_fee) && product.service_fee_switch && product.service_fee > 0\" style=\"font-size: 38rpx;\">+{{product.min_service_fee}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"!isNull(product.max_service_fee) && product.max_service_fee!=product.min_service_fee\">-{{product.max_service_fee}}</text>{{t('服务费')}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"show_money_price\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"moneyprice\" :style=\"'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)'\">{{t('余额')}}</view>\t\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<!-- 营销标签 -->\n\t\t\t\t\t\t\t\t\t\t<view class=\"yingxiao_tag\" v-if=\"product.yingxiao_tag && product.yingxiao_tag.content\">\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t{{product.yingxiao_tag.content}}\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"jiantou\"></view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<!-- 营销标签 end-->\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}<text v-if=\"product.max_price!=product.min_price\">起</text></view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n              <!-- 商品处显示会员价 -->\n              <view v-if=\"product.price_show && product.price_show == 1\" style=\"line-height: 50rpx;\">\n                <text style=\"font-size:30rpx\">￥{{product.sell_putongprice}}</text>\n              </view>\n              <view v-if=\"product.priceshows && product.priceshows.length>0\">\n                <view v-for=\"(item,index) in product.priceshows\" style=\"line-height: 50rpx;\">\n                  <text style=\"font-size:30rpx\">￥{{item.sell_price}}</text>\n                  <text style=\"margin-left: 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\n                </view>\n              </view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"product.ictips\" :style=\"'color:'+t('color1')+';line-height: 50rpx;'\">{{product.ictips}}</view>\n\t\t\t\t\t\t<view v-if=\"sysset.price_show_type=='2' || sysset.price_show_type=='1'\">\n\t\t\t\t\t\t\t<view v-if=\"product.is_vip=='1'\">\n\t\t\t\t\t\t\t\t<view class=\"flex\" v-if=\"product.lvprice == '1'\">\n\t\t\t\t\t\t\t\t\t<view class=\"member flex\" :style=\"'border-color:' + t('color1')\">\n\t\t\t\t\t\t\t\t\t\t<view :style=\"{background:t('color1')}\" class=\"member_lable flex-y-center\">{{product.level_name}}</view>\n\t\t\t\t\t\t\t\t\t\t<view :style=\"'color:' + t('color1')\" class=\"member_value\">\n\t\t\t\t\t\t\t\t\t\t\t<text :style=\"product.lvprice == '1'?'font-size:36rpx':'font-size:26rpx'\">￥</text>\n\t\t\t\t\t\t\t\t\t\t\t<text :style=\"product.lvprice == '1'?'font-size:50rpx':'font-size:26rpx'\">{{product.sell_price || 0}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"price_share\" style=\"height: auto;\">\n\t\t\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1 flex-s\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"showprice_dollar &&usdmin_price\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"margin-right:20rpx;\"><text style=\"font-size:30rpx\" v-if=\"product.usdmin_price\">$</text>{{product.usdmin_price}}<text v-if=\"product.usdmax_price!=product.usdmin_price\">-{{product.usdmax_price}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"font-size: 44rpx;\"><text style=\"font-size:22rpx\">￥</text>{{product.min_price}}<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t<text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text :style=\"product.lvprice == '1'?'font-size:26rpx':'font-size:36rpx'\">￥</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text :style=\"product.lvprice == '1'?'font-size:26rpx':'font-size:50rpx'\">{{product.min_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"show_money_price\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"moneyprice\" :style=\"'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)'\">{{t('余额')}}</view>\t\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}<text v-if=\"product.max_price!=product.min_price\">起</text></view> -->\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view v-if=\"product.is_vip=='0'\">\n\t\t\t\t\t\t\t\t<view class=\"price_share\">\n\t\t\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1 flex-s\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"showprice_dollar\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"margin-right:20rpx;\"><text style=\"font-size:36rpx\">$</text>{{product.usdmin_price}}<text v-if=\"product.usdmax_price!=product.usdmin_price\">-{{product.usdmax_price}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:32rpx\">￥</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 40rpx;\">{{product.min_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t<text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">￥</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:50rpx\">{{product.min_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"show_money_price\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"moneyprice\" :style=\"'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)'\">{{t('余额')}}</view>\t\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}<text v-if=\"product.max_price!=product.min_price\">起</text></view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"flex\" v-if=\"sysset.price_show_type=='2' &&  product.lvprice ==1 \">\n\t\t\t\t\t\t\t\t\t<view class=\"member flex\" :style=\"'border-color:' + t('color1')\">\n\t\t\t\t\t\t\t\t\t\t<view :style=\"{background:t('color1')}\" class=\"member_lable flex-y-center\">{{product.level_name}}</view>\n\t\t\t\t\t\t\t\t\t\t<view :style=\"'color:' + t('color1')\" class=\"member_value\">\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:26rpx\">￥</text>\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:26rpx\">{{product.vip_price}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"sales_stock\" v-if=\"product.yuanbao\" style=\"margin: 0;font-size: 26rpx;margin-bottom: 10rpx;\">\n\t\t\t\t\t\t\t<view class=\"f2\">元宝价：{{product.yuanbao}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view v-if=\"product.mangfan_status && product.mangfan_status==1\" :style=\"{'color':product.mangfan_text_color}\">{{product.mangfan_text}}</view>\n\t\t\t\t\t\t<view class=\"title\">\n              <block v-if=\"product.labels && product.labels.length>0 \">\n                <view v-for=\"(item,index) in product.labels\" class=\"shop_label\" :style=\"'color:'+product.labelcolor+';background-color:'+product.labelbgcolor\">\n                {{item.name}}\n                </view>\n              </block>\n              {{product.name}}\n            </view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<view v-if=\"product.xunjia_text\" class=\"price_share\">\n\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">询价</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"price_share\">\n\t\t\t\t\t\t\t<view class=\"title\" style=\"display:block\">\n                <block v-if=\"product.labels && product.labels.length>0 \">\n                  <view v-for=\"(item,index) in product.labels\" class=\"shop_label\" :style=\"'color:'+product.labelcolor+';background-color:'+product.labelbgcolor\">\n                  {{item.name}}\n                  </view>\n                </block>\n                {{product.name}}\n              </view>\n\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t\t\n\t\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\"  @tap=\"copy1\" >{{product.sellpoint}}</view>\n\t\t\t\t<view class=\"sales_stock\" v-if=\"shopset.hide_sales != 1 || shopset.hide_stock != 1\">\n\t\t\t\t\t<view class=\"f1\" v-if=\"shopset.hide_sales != 1\">销量：{{product.sales}} </view>\n\t\t\t\t\t<view class=\"f2\" v-if=\"shopset.hide_stock != 1\">库存：{{product.stock}}</view>\n\t\t\t\t</view>\n\t\t\t\t<block v-if=\"custom.commission_max_times_status==0\">\n\t\t\t\t\t<block v-if=\"custom.product_commission_desc && shopset.commission_desc && shopset.showcommission==1\">\n\t\t\t\t\t\t<view style=\"display: flex;\">\n\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\" style=\"display: flex; justify-content: center;align-items: center;   \">\n\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/share.png'\" style=\"width: 30rpx;\" mode=\"widthFix\"/><text style=\"margin:0 10rpx\">分享</text></view>\n\t\t\t\t\t\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">\t{{shopset.commission_desc}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\t\n\t\t\t\t \n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"shopset.showcommission==1 && (product.commission > 0 || product.commissionScore > 0) && showjiesheng==0\">\n\t\t\t\t\t\t\t\t分享好友购买预计可得{{t('佣金')}}：\n\t\t\t\t\t\t\t\t<block v-if=\"product.commission > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.commission}}</text>{{product.commission_desc}}</block>\n\t\t\t\t\t\t\t\t<block v-if=\"product.commission > 0 && product.commissionScore > 0\">+</block>\n\t\t\t\t\t\t\t\t<block v-if=\"product.commissionScore > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.commissionScore}}</text>{{product.commission_desc_score}}</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t\t<block v-if=\"custom.commission_max_times_status && (product.commission_total1 > 0 || product.commission_total2 > 0)\">\n\t\t\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">\n\t\t\t\t\t\t\t分享好友购买预计可得\n\t\t\t\t\t\t\t<block v-if=\"product.commission_total1 > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.commission_total1}}</text>一级{{t('佣金')}}</block>\n\t\t\t\t\t\t\t<block v-if=\"product.commission_total1 > 0 && product.commission_total2 > 0\">+</block>\n\t\t\t\t\t\t\t<block v-if=\"product.commission_total2 > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.commission_total2}}</text>二级{{t('佣金')}}</block>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-if=\"custom.active_coin && product.give_active_coin > 0\">\n\t\t\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" >\n\t\t\t\t\t\t购买预计可得{{t('激活币')}}：\n\t\t\t\t\t\t<text style=\"font-weight:bold;padding:0 2px\">{{product.give_active_coin}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n        <block v-if=\"custom.member_goldmoney_silvermoney && (product.givegoldmoney > 0 || product.givesilvermoney > 0)\">\n        \t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1'),display:'inline-block',width:'auto'}\">\n        \t\t\t下单购买预计可得\n        \t\t\t<block v-if=\"product.givesilvermoney > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.givesilvermoney}}</text>{{t('银值')}}</block>\n        \t\t\t<block v-if=\"product.givegoldmoney > 0 && product.givesilvermoney > 0\">+</block>\n        \t\t\t<block v-if=\"product.givegoldmoney > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.givegoldmoney}}</text>{{t('金值')}}</block>\n        \t</view>\n        </block>\n\t\t\t\t<view style=\"margin:20rpx 0;color:#333;font-size:22rpx\" v-if=\"product.balance_price > 0\">首付款金额：{{product.advance_price}}元，尾款金额：{{product.balance_price}}元</view>\n\t\t\t\t<view style=\"margin:20rpx 0;color:#666;font-size:22rpx\" v-if=\"product.buyselect_commission > 0\">下单被选奖励预计可得{{t('佣金')}}：<text style=\"font-weight:bold;padding:0 2px\">{{product.buyselect_commission}}</text>元</view>\n\n\t\t\t\t<view class=\"upsavemoney\" :style=\"{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}\" v-if=\"product.upsavemoney > 0\">\n\t\t\t\t\t<view class=\"flex1\">升级到 {{product.nextlevelname}} 预计可节省<text style=\"font-weight:bold;padding:0 2px;color:#ca4312\">{{product.upsavemoney}}</text>元</view>\n\t\t\t\t\t<view style=\"margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312\" @tap=\"goto\" data-url=\"/pagesExt/my/levelup\">立即升级<image :src=\"pre_url+'/static/img/arrowright2.png'\" style=\"width:30rpx;height:30rpx\"/></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"upsavemoney\" :style=\"{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}\" v-if=\"product.upsavemoney2 > 0\">\n\t\t\t\t\t<view class=\"flex1\">升级到 {{product.nextlevelname2}} 预计可节省<text style=\"font-weight:bold;padding:0 2px;color:#ca4312\">{{product.upsavemoney2}}</text>元</view>\n\t\t\t\t\t<view style=\"margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312\" @tap=\"goto\" data-url=\"/pagesExt/my/levelup\">立即升级<image :src=\"pre_url+'/static/img/arrowright2.png'\" style=\"width:30rpx;height:30rpx\"/></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 分期 -->\n\t\t\t<view class=\"choose-fenqi\" v-if=\"product.product_type == 5\">\n\t\t\t\t<view class=\"f0\">分期</view>\n\t\t\t\t<view class=\"fenqi-info-view\">\n\t\t\t\t\t<view class=\"commission-fenqi\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\">不分期购买商品包含{{product.fenqigive_couponnum}}张{{t('优惠券')}}</view>\n\t\t\t\t\t<view class=\"fenqi-list-view\">\n\t\t\t\t\t\t<scroll-view scroll-x style=\"white-space: nowrap;width: 100%;\">\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in product.fenqi_data\">\n\t\t\t\t\t\t\t\t<view class=\"fenqi-options\">\n\t\t\t\t\t\t\t\t\t<view class=\"fenqi-num\">\n\t\t\t\t\t\t\t\t\t\t<text>{{item.fenqi_num}}期</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"fenqi-bili\" v-if=\"item.fenqi_num_ratio\">支付比例{{item.fenqi_num_ratio}}%</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class='fenqi-give' v-if=\"item.fenqi_give_num\">包含{{item.fenqi_give_num}}张{{t('优惠券')}}</view>\n\t\t\t\t\t\t\t\t\t<view class='fenqi-give' v-else>无赠送{{t('优惠券')}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<block v-if=\"custom.product_guige_showtype && shopset.show_guigetype==2\">\n\t\t\t\t<buydialog-show :proid=\"product.id\" :btntype=\"btntype\" @changeGuige=\"changeGuige\" :menuindex=\"menuindex\" @addcart=\"addcart\" ></buydialog-show>\n\t\t\t</block>\n\t\t\t<block v-else>\n\t\t\t\t<block  v-if=\"(!custom.product_detail_special || (custom.product_detail_special && shopset.show_guige)) && dgprodata ==''\">\n\t\t\t\t\t<view class=\"choose\" @tap=\"buydialogChange\" data-btntype=\"2\">\n\t\t\t\t\t\t<view class=\"f0\">规格</view>\n\t\t\t\t\t\t<view class=\"f1 flex1\">\n\t\t\t\t\t\t\t<block v-if=\"product.price_type == 1\">查看规格</block>\n\t\t\t\t\t\t\t<block v-else>请选择商品规格及数量</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image class=\"f2\" :src=\"pre_url+'/static/img/arrowright.png'\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</block>\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.givescore > 0\">\n\t\t\t\t<view class=\"cuxiaopoint\">\n\t\t\t\t\t<view class=\"f0\">送{{t('积分')}}</view>\n\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('积分')}}{{product.givescore}}个</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.give_commission_max > 0\">\n\t\t\t\t<view class=\"cuxiaopoint\">\n\t\t\t\t\t<view class=\"f0\">送{{t('佣金上限')}}</view>\n\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('佣金上限')}}{{product.give_commission_max}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"cuxiaolist.length>0 || couponlist.length>0 || fuwulist.length>0 || product.discount_tips!=''\">\n\t\t\t\t<view class=\"fuwupoint cuxiaoitem\" v-if=\"fuwulist.length>0\">\n\t\t\t\t\t<view class=\"f0\">服务</view>\n\t\t\t\t\t<view class=\"f1\" @tap=\"showfuwudetail\">\n\t\t\t\t\t\t<view class=\"t\" v-for=\"(item, index) in fuwulist\" :key=\"index\">{{item.name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"showfuwudetail\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"cuxiaolist.length>0\">\n\t\t\t\t\t<view class=\"f0\">促销</view>\n\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\">{{item.tip}}</text><text class=\"t1\">{{item.name}}</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"product.discount_tips!=''\">\n\t\t\t\t\t<view class=\"f0\">折扣</view>\n\t\t\t\t\t<view class=\"f1\" style=\"padding-left:10rpx\">{{product.discount_tips}}</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/pagesExt/my/levelinfo\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"couponlist.length>0 && showtoptabbar==0\">\n\t\t\t\t\t<view class=\"f0\">优惠</view>\n\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"showfuwudialog\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidefuwudetail\"></view>\n\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">服务</text>\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidefuwudetail\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in fuwulist\" :key=\"index\" class=\"service-item\">\n\t\t\t\t\t\t\t\t<view class=\"fuwudialog-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t<text class=\"f2\">{{item.desc}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"showcuxiaodialog\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidecuxiaodetail\"></view>\n\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidecuxiaodetail\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"service-item\">\n\t\t\t\t\t\t\t\t<view class=\"suffix\">\n\t\t\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text> <text style=\"color:#333;margin-left:20rpx\">{{item.name}}</text></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<couponlist :couponlist=\"couponlist\" @getcoupon=\"getcoupon\"></couponlist>\n\t\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view style=\"width:100%;height:auto;padding:20rpx 0 0\" v-if=\"shopset.detail_guangao2\">\n\t\t\t\t<image :src=\"shopset.detail_guangao2\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"shopset.detail_guangao2\" @tap=\"showgg2Dialog\"/>\n\t\t\t</view>\n\t\t\t<uni-popup id=\"gg2Dialog\" ref=\"gg2Dialog\" type=\"dialog\" v-if=\"shopset.detail_guangao2 && shopset.detail_guangao2_t\">\n\t\t\t\t<image :src=\"shopset.detail_guangao2_t\" @tap=\"previewImage\" :data-url=\"shopset.detail_guangao2_t\" class=\"img\" mode=\"widthFix\" style=\"width:600rpx;height:auto;border-radius:10rpx;\"/>\n\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg2Dialog\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t</view>\n\t\t\t</uni-popup>\n\t\t</view>\n\n\t\t<view id=\"scroll_view_tab1\">\n\n\t\t\t<view class=\"commentbox\" v-if=\"commentposition==0 && shopset.comment==1 && commentcount > 0\">\n\t\t\t\t<view class=\"title\">\n\t\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/pagesB/shop/commentlist?proid=' + product.id\">好评率 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" :src=\"pre_url+'/static/img/arrowright.png'\"/></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"comment\">\n\t\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\n\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\n\t\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\n\t\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"pre_url+'/static/img/star' + (commentlist[0].score>item2?'2native':'') + '.png'\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\n\t\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\n\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'/pagesB/shop/commentlist?proid=' + product.id\">查看全部评价</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</view>\n\n\t\t<view id=\"scroll_view_tab2\">\n\t\t\t<view v-if=\"product.choujiang && product.choujiang.custom_text\">\n\t\t\t\t<view class=\"choujiangtext\" :style=\"'background:'+product.choujiang.text_bgcolor+';color:'+product.choujiang.text_color\">{{product.choujiang.custom_text}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"shop\" v-if=\"shopset.showjd==1 && business\">\n\t\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\n\t\t\t\t<view class=\"p2 flex1\">\n\t\t\t\t\t<view class=\"t1\">{{business.name}}</view>\n\t\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"p4\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\">进入店铺</button>\n\t\t\t</view>\n\t\t\t<!-- 自提商品附近门店S -->\n\t\t\t<view v-if=\"showNearbyMendian && mendianids.length>0 && latitude && longitude \" class=\"nearby-mendian-box\">\n\t\t\t\t<view class=\"nearby-mendian-title\">\n\t\t\t\t\t<view class=\"t1\">附近{{t('门店')}}<text v-if=\"mendianids.length>1\">（{{mendianids.length}}家）</text></view>\n\t\t\t\t\t<view class=\"t2\" @tap=\"goto\" :data-url=\"'/pagesExt/business/mendian?bid='+product.bid+'&proid='+product.id\"><text>{{mendianids.length>1?'全部':'查看'}}{{t('门店')}}</text><image :src=\"pre_url+'/static/img/arrowright.png'\"></image></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"nearby-mendian-info\">\n\t\t\t\t\t<view class=\"b1\" @tap=\"goto\" :data-url=\"'/pages/shop/mendian?id='+mendian.id\"><image :src=\"mendian.pic\"></image></view>\n\t\t\t\t\t<view class=\"b2\">\n\t\t\t\t\t\t<view class=\"t1\" @tap=\"goto\" :data-url=\"'/pages/shop/mendian?id='+mendian.id\">{{mendian.name}}</view>\n\t\t\t\t\t\t<view class=\"t2 flex-y-center\">\n\t\t\t\t\t\t\t<block v-if=\"mendian.distance\">\n\t\t\t\t\t\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" class=\"nearby-tag\">最近</view> \n\t\t\t\t\t\t\t\t<view class=\"mendian-distance\">{{mendian.distance}} </view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<view class=\"mendian-address\">{{mendian.address?mendian.address:mendian.area}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"b3\">\n\t\t\t\t\t\t<view @tap=\"callMendian\" :data-tel=\"mendian.tel\"><image :src=\"pre_url+'/static/img/location/tel.png'\"></image></view>\n\t\t\t\t\t\t<!-- #ifndef MP-ALIPAY-->\n\t\t\t\t\t\t<view @tap=\"toMendian\" :data-address=\"mendian.address\" :data-longitude=\"mendian.longitude\" :data-latitude=\"mendian.latitude\"><image :src=\"pre_url+'/static/img/location/daohang.png'\"></image></view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- #ifdef MP-ALIPAY -->\n\t\t\t\t<!-- 支付宝先授权再定位 -->\n\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"showNearbyMendian && (longitude =='' || latitude =='')\">\n\t\t\t\t\t<view class=\"cuxiaopoint\">\n\t\t\t\t\t\t<view class=\"f0\">定位服务未授权，授权后查看附近门店</view>\n\t\t\t\t\t\t<button class=\"shouquan\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"toLocation\">授权</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t<!-- #endif -->\n\t\t\t<!-- 自提商品附近门店E -->\n\t\t\t\n\t\t\t<block v-if=\"!isEmpty(product.paramdata)\">\n\t\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品参数</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\n\t\t\t<view style=\"background:#fff;padding:20rpx 40rpx;\" class=\"paraminfo\">\n\t\t\t\t<view v-for=\"(item, index) in product.paramdata\" class=\"paramitem\">\n\t\t\t\t\t<view class=\"f1\">{{index}}</view>\n\t\t\t\t\t<view class=\"f2\">{{item}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\t<view v-if=\"shopset.prodetailtitle_type && shopset.prodetailtitle_type==1\" class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">{{shopset.prodetailtitle_value}}</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\n\t\t\t<view v-else-if=\"shopset.prodetailtitle_type && shopset.prodetailtitle_type==2\" class=\"detail_title\"><image :src=\"shopset.prodetailtitle_value\" mode=\"heightFix\" style=\"height:60rpx\"/></view>\n\t\t\t<view v-else-if=\"shopset.prodetailtitle_type && shopset.prodetailtitle_type==3\"></view>\n\t\t\t<view v-else class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view>\n\t\t\t<view class=\"t0\">商品描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\n\t\t\t<view class=\"detail\">\n\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\n\t\t\t\t<image v-if=\"bottomImg\" class=\"bottomimg\" :src=\"bottomImg\" mode=\"widthFix\" />\n\t\t\t</view>\n\n\t\t</view>\n\t\t<view class=\"commentbox\" v-if=\"commentposition==1\">\n\t\t\t<view class=\"title\">\n\t\t\t\t<view class=\"f1\">评价({{commentcount}}) <image v-if=\"shopset.product_comment==1\" class=\"addcommentimg\" @tap.stop=\"goto\" :data-url=\"'/pagesA/shop/productComment?proid='+product.id\" :src=\"pre_url+'/static/img/edit1.png'\"></view>\n\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/pagesB/shop/commentlist?proid=' + product.id\">好评率 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" :src=\"pre_url+'/static/img/arrowright.png'\"/></view>\n\t\t\t</view>\n\t\t\t<view class=\"comment\">\n\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\n\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\n\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"pre_url+'/static/img/star' + (commentlist[0].score>item2?'2native':'') + '.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\n\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\n\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'/pagesB/shop/commentlist?proid=' + product.id\">查看全部评价</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view id=\"scroll_view_tab3\">\n\n\t\t\t<view v-if=\"tjdatalist.length > 0\">\n\t\t\t\t<view class=\"xihuan\">\n\t\t\t\t\t<view class=\"xihuan-line\"></view>\n\t\t\t\t\t<view class=\"xihuan-text\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/xihuan.png'\" class=\"img\"/>\n\t\t\t\t\t\t<text class=\"txt\">为您推荐</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"xihuan-line\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"prolist\">\n\t\t\t\t\t<dp-product-item :data=\"tjdatalist\" @addcart=\"addcart\" :menuindex=\"menuindex\"></dp-product-item>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\n\n\t\t</scroll-view>\n\t\t<block  v-if=\"!custom.product_detail_special || (custom.product_detail_special && shopset.show_option_group)\">\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1&&!showcuxiaodialog&&!showfuwudialog\">\n\t\t\t<view class=\"f1 flex\" v-if=\"shopdetail_menudataList\">\n\t\t\t\t<block v-for=\"(item,index) in shopdetail_menudataList\" v-if=\"item.isShow == 1\">\n\t\t\t\t\t<block v-if='item.menuType == 1'>\n\t\t\t\t\t\t<block v-if=\"item.useSystem == 1\">\n\t\t\t\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\n\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\n\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<button class=\"item\" open-type=\"contact\"  v-if=\"item.pagePath == 'contact::'\" show-message-card=\"true\">\n\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\" v-else>\n\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if='item.menuType == 2'>\n\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\">\n\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/gwc.png'\" />\n\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'购物车'}}</view>\n\t\t\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\" :style=\"{background:'rgba('+t('color1rgb')+',0.8)'}\">{{cartnum}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if='item.menuType == 3'>\n\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/shoucang.png'\"\tv-if='!isfavorite' />\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.selectedIconPath ? item.selectedIconPath:pre_url+'/static/img/shoucang.png'\"\tv-else />\n\t\t\t\t\t\t\t<view class=\"t1\" v-if='item.selectedtext && item.text'>{{isfavorite ? item.selectedtext:item.text}}</view>\n\t\t\t\t\t\t\t<view class=\"t1\" v-else>{{isfavorite?'已收藏':'收藏'}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if='item.menuType == 4'>\n\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.iconPath\" />\n\t\t\t\t\t\t\t<view class=\"t1\">{{item.text}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t<!-- 为空数据默认展示 -->\n\t\t\t<view class=\"f1\" v-else>\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\n\t\t\t\t\t<view class=\"t1\">客服</view>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\n\t\t\t\t\t<view class=\"t1\">客服</view>\n\t\t\t\t</button>\n\t\t\t\t<block v-if=\"custom.shop_gwc_name\">\n\t\t\t\t\t<view class=\"item flex1\" @tap=\"goto\" data-url=\"/pages/shop/cart\" v-if=\"shopset.gwc_showst==1\">\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/gwc.png'\"/>\n\t\t\t\t\t\t<view class=\"t1\">{{shopset.gwc_name?shopset.gwc_name:'购物车'}}</view>\n\t\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\" :style=\"{background:'rgba('+t('color1rgb')+',0.8)'}\">{{cartnum}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<view class=\"item flex1\" @tap=\"goto\" data-url=\"/pages/shop/cart\">\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/gwc.png'\"/>\n\t\t\t\t\t\t<view class=\"t1\">购物车</view>\n\t\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\" :style=\"{background:'rgba('+t('color1rgb')+',0.8)'}\">{{cartnum}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"op2\" v-if=\"showjiesheng==1\">\n\t\t\t\t<view class=\"tocart2\" :style=\"{background:t('color2')}\" @tap=\"shareClick\"><text>分享赚钱</text><text style=\"font-size:24rpx\">赚￥{{product.commission}}</text></view>\n\t\t\t\t\t<block v-if=\"!product.buybtn_link_url\">\n\t\t\t\t\t\t\t<view class=\"tobuy2\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\">\n\t\t\t\t\t\t\t\t\t<text>{{product.buybtn_name?product.buybtn_name:\"立即购买\"}}</text>\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\" v-if=\"product.jiesheng_money > 0\">省￥{{product.jiesheng_money}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<view class=\"tobuy2\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"product.buybtn_link_url\">\n\t\t\t\t\t\t\t\t\t<text>{{product.buybtn_name?product.buybtn_name:\"立即购买\"}}</text>\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\" v-if=\"product.jiesheng_money > 0\">省￥{{product.jiesheng_money}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t<view class=\"op\" v-else-if=\"is_member_auto_addlogin==1\">\n\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"3\">\n\t\t\t\t\t\t{{product.buybtn_name?product.buybtn_name:\"立即购买\"}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"op\" v-else>\n\t\t\t\t<block v-if=\"product.price_type == 1\">\n\t\t\t\t\t<view v-if=\"!custom.product_xunjia_btn\" class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"showLinkChange\" data-btntype=\"2\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</view>\n\t\t\t\t\t<block v-if=\"custom.product_xunjia_btn && product.show_xunjia_btn=='1'\">\n\t\t\t\t\t\t<view v-if=\"product.xunjia_btn_url\" class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:product.xunjia_btn_bgcolor?product.xunjia_btn_bgcolor:t('color1'),color:product.xunjia_btn_color?product.xunjia_btn_color:'#FFF'}\" @tap=\"goto\" :data-url=\"product.xunjia_btn_url\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</view>\n\t\t\t\t\t\t<view v-else class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:product.xunjia_btn_bgcolor?product.xunjia_btn_bgcolor:t('color1'),color:product.xunjia_btn_color?product.xunjia_btn_color:'#FFF'}\" @tap=\"showLinkChange\" data-btntype=\"2\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</view>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<block v-if=\"custom.product_guige_showtype==1 && shopset.show_guigetype==2\">\n\t\t\t\t\t\t<view class=\"tocart flex-x-center flex-y-center\" :style=\"{background:t('color2')}\" @tap=\"addcart2\" data-btntype=\"1\" v-if=\"product.freighttype!=3 && product.freighttype!=4 && product.product_type != 9\">加入{{shopset.gwc_name?shopset.gwc_name:\"购物车\"}}</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<block v-if=\"!product.addcart_link_url\">\n\t\t\t\t\t\t\t<!-- 商品柜有数据隐藏 -->\n\t\t\t\t\t\t\t<block v-if=\"dgprodata ==''\">\n\t\t\t\t\t\t\t\t<view class=\"tocart flex-x-center flex-y-center\" :style=\"{background:t('color2')}\" @tap=\"buydialogChange\" data-btntype=\"1\" v-if=\"product.freighttype!=3 && product.freighttype!=4 && product.product_type != 9\">{{product.addcart_name?product.addcart_name:\"加入购物车\"}}</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"tocart flex-x-center flex-y-center\" :style=\"{background:t('color2')}\" @tap=\"goto\" :data-url=\"product.addcart_link_url\" v-if=\"product.freighttype!=3 && product.freighttype!=4 && product.product_type != 9\">{{product.addcart_name?product.addcart_name:\"加入购物车\"}}</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</block>\t\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\" v-if=\" product.shop_yuding &&  product.stock <= 0 && product.yuding_stock > 0 \">预定</view>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<block v-if=\"custom.product_guige_showtype==1 && shopset.show_guigetype==2\">\n\t\t\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"tobuy\" >立即购买</view>\n\t\t\t\t\t\t\t</block>\t\n\t\t\t\t\t\t\t<block v-else>\t\n\t\t\t\t\t\t\t\t<block v-if=\"!product.buybtn_link_url\">\n\t\t\t\t\t\t\t\t\t\t<!-- 商品柜购买按钮 -->\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"dgprodata !='' && devicedata !=''\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @click=\"goto\" :data-url=\"'/pagesB/shop/buy?prodata='+dgprodata+'&devicedata='+devicedata\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{product.buybtn_name?product.buybtn_name:\"立即购买\"}}\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{product.buybtn_name?product.buybtn_name:\"立即购买\"}}\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"product.buybtn_link_url\">\n\t\t\t\t\t\t\t\t\t\t\t\t{{product.buybtn_name?product.buybtn_name:\"立即购买\"}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</view>\n\t\t</block>\n\t\t<block v-if=\"product.product_type == 4 || product.product_type == 6\">\n\t\t\t<block v-if=\"JSON.parse(product.guigedata).length == 2 && product.product_type != 6\">\n\t\t\t\t<buydialog-pifa v-if=\"buydialogShow\" :proid=\"product.id\" :btntype=\"btntype\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" @addcart=\"addcart\" />\n\t\t\t</block>\n\t\t\t<block v-else>\n\t\t\t\t<buydialog-pifa2 v-if=\"buydialogShow\" :proid=\"product.id\" :btntype=\"btntype\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" @addcart=\"addcart\" />\n\t\t\t</block>\n\t\t</block>\n\t\t<block v-else>\n\t\t\t<buydialog v-if=\"buydialogShow\" :proid=\"product.id\" :btntype=\"btntype\" @buydialogChange=\"buydialogChange\" @showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" @addcart=\"addcart\"></buydialog>\n\t\t</block>\n\t\t<!-- 采购单 选择规格弹窗-->\n\t\t<block>\n\t\t\t<buydialog-purchase v-if=\"purchaseOrderShow\" :proid=\"product.id\" @buydialogChange=\"purchaseorder\" :menuindex=\"menuindex\"></buydialog-purchase>\n\t\t</block>\n\t\t<!-- END 采购单 选择规格弹窗 -->\n\t\t<view class=\"scrolltop\" v-show=\"scrolltopshow\" @tap=\"changetoptab\" data-index=\"0\"><image class=\"image\" :src=\"pre_url+'/static/img/gotop.png'\"/></view>\n\t\t\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\n\t\t\t\t<!-- <view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\n\t\t\t\t</view> -->\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"sharetypecontent\">\n\t\t\t\t\t\t<!-- #ifdef APP -->\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifdef H5 -->\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-if=\"getplatform() == 'mp'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifndef H5 -->\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-if=\"getplatform() != 'h5'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareScheme\" v-if=\"xcx_scheme\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/weixin.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">小程序链接</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\n\t\t\t<view class=\"main\">\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"posterDialog schemeDialog\" v-if=\"showScheme\">\n\t\t\t<view class=\"main\">\n\t\t\t\t<view class=\"close\" @tap=\"schemeDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\n\t\t\t\t<view class=\"schemecon\">\n\t\t\t\t\t<view style=\"line-height: 60rpx;\">{{product.name}} </view>\n\t\t\t\t\t<view >购买链接：<text style=\"color: #00A0E9;\">{{schemeurl}}</text></view>\n\t\t\t\t\t<view class=\"copybtn\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap.stop=\"copy\" :data-text=\"product.name+'购买链接：'+schemeurl\"> 一键复制 </view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\n\t\t\t<view class=\"main\">\n\t\t\t\t<view class=\"close\" @tap=\"showLinkChange\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view class=\"title\">{{sysset.name}}</view>\n\t\t\t\t\t<view class=\"row\" v-if=\"product.bid > 0\">\n\t\t\t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">店铺名称</view>\n\t\t\t\t\t\t<view class=\"f2 flex-y-center flex-x-bottom\" style=\"font-size: 28rpx;width: 100%;max-width: 470rpx;display: flex;\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+product.bid\">\n              <view style=\"width: 100%;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;\">{{business.name}}</view>\n              <view style=\"flex: 1;\"></view>\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"row\" v-if=\"business.tel\">\n\t\t\t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">联系电话</view>\n\t\t\t\t\t\t<view class=\"f2 flex-y-center flex-x-bottom\" style=\"width: 100%;max-width: 470rpx;\" @tap=\"goto\" :data-url=\"'tel::'+business.tel\" :style=\"{color:t('color1')}\">\n              {{business.tel}}\n              <image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"business.tel\"></image>\n            </view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 采购单详情页 -->\n\t\t<view class=\"suspension\" v-if=\"shopset && shopset.show_purchase_order == 1\">\n\t\t\t\t<view @tap=\"purchaseorder\" class=\"suspension-purchase\">\n\t\t\t\t\t<text class=\"suspension-text\">加入</text>\n\t\t\t\t\t<text class=\"suspension-text\">采购单</text>\n\t\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<!-- 眼镜批发 -->\n\t<uni-popup ref=\"glassesPupup\" type=\"bottom\" :safeArea='false' v-if=\"product.guige_show_type == 1 && JSON.parse(product.guigedata).length == 2\">\n\t\t<view class=\"glasses-pupup-view\"  v-if=\"show_guige_type == 1\">\n\t\t\t<view class=\"glasses-pupup-close\" @tap=\"glassesPopupClose\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" />\n\t\t\t</view>\n\t\t\t<!-- 商品信息 -->\n\t\t\t<view class=\"glasses-pupup-product flex\">\n\t\t\t\t<view class=\"glasses-product-image\">\n\t\t\t\t\t<image :src=\"nowguigeProduct.pic || product.pic\" @tap=\"previewImage\" :data-url=\"nowguigeProduct.pic || product.pic\" mode=\"scaleToFill\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"glasses-product-info flex flex-col\">\n\t\t\t\t\t<view class=\"glasses-product-name\">{{product.name}}</view>\n\t\t\t\t\t<view class=\"glasses-product-price flex\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t<view style=\"font-weight: bold;font-size: 32rpx;\">￥</view>{{nowguigeProduct.sell_price}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- <view class=\"glasses-product-tag\" :style=\"{background: 'rgba(' +t('color1rgb') + ',0.4)',color:t('color1')}\">\n\t\t\t\t\t\t{{nowguigeProduct.name}}\n\t\t\t\t\t</view> -->\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 规格类目 -->\n\t\t\t<view class=\"glasses-product-class flex flex-col\">\n\t\t\t\t<view class=\"g-productClass-top flex\">\n\t\t\t\t\t<view class=\"left-view\">\n\t\t\t\t\t\t<view class=\"left-view-text flex flex-col\">\n\t\t\t\t\t\t\t<view class=\"left-view-name\">{{guigedata[1].title}}</view>\n\t\t\t\t\t\t\t<!-- <view style=\"font-size: 20rpx;\">(c)</view> -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right-view flex\">\n\t\t\t\t\t\t<scroll-view class=\"scroll-view-class\" :scroll-x='true' :scroll-left=\"scrollLeft\" scroll-with-animation @scrolltoupper='scrollToupper'>\n\t\t\t\t\t\t\t<block v-for=\"(xItem,xIndex) in guigedata[1].items\" :key=\"xIndex\">\n\t\t\t\t\t\t\t\t<view :class=\"[xIndex == classIndex ? 'right-view-active':'','right-view-options flex']\" @click=\"classChange(xIndex)\">\n\t\t\t\t\t\t\t\t\t<view>{{xItem.title}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"options-tag\" v-if=\"xItem.num > 0\">{{xItem.num}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right-view-but flex\" @click=\"slideClass\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"g-productClass-bottom flex\">\n\t\t\t\t\t<view class=\"left-view flex\">\n\t\t\t\t\t\t<view class=\"select-view flex\" @tap=\"changeradioAll\">\n\t\t\t\t\t\t\t<image class=\"select-view-image\" :src=\"pre_url+'/static/img/duihao.png'\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"left-view-class flex\">\n\t\t\t\t\t\t\t<view class=\"title-text\">{{guigedata[0].title}}</view>\n\t\t\t\t\t\t\t<!-- <view class='left-view-sku'>(s)</view> -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right-view flex\">\n\t\t\t\t\t\t<view class=\"right-view-bg\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 规格展示区域 -->\n\t\t\t\t<view class=\"glasses-sku-view flex flex-col\">\n\t\t\t\t\t<scroll-view scroll-y style=\"width: 100%;height:100%;\">\n\t\t\t\t\t\t<block v-for=\"(item,index) in guigelist[ksk]\" :key=\"index\">\n\t\t\t\t\t\t\t<view class='glasses-sku-options flex'>\n\t\t\t\t\t\t\t\t<view class='left-sku-view flex' @tap=\"changeradio\" :data-index=\"index\">\n\t\t\t\t\t\t\t\t\t<view :class=\"[item.checked ? 'select-view select-view-active flex' : 'select-view flex']\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"select-view-image\" :src=\"pre_url+'/static/img/duihao.png'\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view :class=\"[item.checked ? 'left-view-active':'', 'left-view-class flex']\">{{item.x_name}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"right-sku-view flex\">\n\t\t\t\t\t\t\t\t\t<view class=\"right-view-bg flex\" @tap=\"changeradio\" :data-index=\"index\">\n\t\t\t\t\t\t\t\t\t\t<view :class=\"[item.checked ? 'right-view-num-active':'', item.num >0 && item.checked ? 'right-view-num-active2':'','right-view-num flex']\">\n\t\t\t\t\t\t\t\t\t\t\t{{item.y_name}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"right-view-inventory\">\n\t\t\t\t\t\t\t\t\t\t库存：{{item.stock}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"right-count-view flex\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"but-class\" @tap=\"gwcminus(item,index)\">-</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"input-view\">\n\t\t\t\t\t\t\t\t\t\t\t<input class=\"input-class\" type=\"number\" :value=\"item.num\" @blur=\"gwcinput($event,item,index)\"/>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"but-class\" @tap=\"gwcplus(item,index)\">+</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 底部按钮 -->\n\t\t\t\t<view class=\"glasses-bottom-but flex glasses-bottomclass\">\n\t\t\t\t\t<view class='bottom-but-class' :style=\"{background:t('color2')}\" @tap=\"addcart2\">加入购物车</view>\n\t\t\t\t\t<view class='bottom-but-class' :style=\"{background:t('color1')}\" @tap=\"tobuy\">立即购买</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</uni-popup>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url: app.globalData.pre_url,\n\t\t\ttextset:{},\n\t\t\tisload:false,\n\t\t\tbuydialogShow: false,\n\t\t\tbtntype:1,\n\t\t\tisfavorite: false,\n\t\t\tcurrent: 0,\n\t\t\tisplay: 0,\n\t\t\tshowcuxiaodialog: false,\n\t\t\tshowfuwudialog:false,\n\t\t\tbusiness: \"\",\n\t\t\tproduct: [],\n\t\t\tcartnum: \"\",\n\t\t\tcommentlist: \"\",\n\t\t\tcommentcount: \"\",\n\t\t\tcuxiaolist: \"\",\n\t\t\tcouponlist: \"\",\n\t\t\tfuwulist: [],\n\t\t\tpagecontent: \"\",\n\t\t\tshopset: {},\n\t\t\tsysset:{},\n\t\t\tcustom:{},\n\t\t\ttitle: \"\",\n\t\t\tbboglist: \"\",\n\t\t\tsharepic: \"\",\n\t\t\tsharetypevisible: false,\n\t\t\tshowposter: false,\n\t\t\tposterpic: \"\",\n\t\t\tscrolltopshow: false,\n\t\t\tkfurl:'',\n\t\t\tshowLinkStatus:false,\n\t\t\tshowjiesheng:0,\n\t\t\tis_member_auto_addlogin:0,\n\t\t\ttjdatalist:[],\n\t\t\tshowtoptabbar:0,\n\t\t\ttoptabbar_show:0,\n\t\t\ttoptabbar_index:0,\n      scrollToViewId: \"\",\n\t\t\tscrollTop:0,\n\t\t\tscrolltab0Height:0,\n\t\t\tscrolltab1Height:0,\n\t\t\tscrolltab2Height:0,\n\t\t\tscrolltab3Height:0,\n\t\t\txcx_scheme:false,\n\t\t\tshowScheme:false,\n\t\t\tschemeurl:'',\n\t\t\tshowprice_dollar:false,\n\t\t\tshow_money_price:false,\n\t\t\t//自提商品门店显示\n\t\t\tshowNearbyMendian:false,\n\t\t\tlongitude: '',\n\t\t\tlatitude: '',\n\t\t\tmendianids:[],\n\t\t\tmendian:{},\n\t\t\tmendian_id:0,\n\t\t\t//自提商品门店显示\n\t\t\tcommentposition:0,//评论显示位置\n\t\t\tswiperHeight: '',\n\t\t\tsell_price:0,\n\t\t\tisloadAd:0,\n\t\t\tshopdetail_menudataList:[],\n\t\t\tbottomImg:'',//公共底部图片\n\t\t\tdgprodata:'',//商品柜带购买参数\n\t\t\tdevicedata:'',//设备组合信息\n\t\t\tpurchaseOrderShow:false, //采购单\n\t\t\tshow_image:0, //产品详情大图限制观看\n\t\t\tscrollLeft:0,\n\t\t\tclassIndex:0,\n\t\t\tguigelist:{},\n\t\t\tguigedata:{},\n\t\t\tksk:0, //默认选择第一个规格数组\n\t\t\tnowguigeProduct:{}, //默认规格\n\t\t\tgwcnum:0,\n\t\t\tshow_guige_type:0,//显示规格\n\t\t};\n\t},\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.mendian_id = app.getCache('user_selected_mendianid')\n    if(this.opt.sharetypevisible){\n      this.sharetypevisible = true;\n    }\n\t\tif(this.opt.dgprodata ){\n\t\t\tthis.dgprodata = this.opt.dgprodata;\n\t\t}\n\t\tif(this.opt.devicedata){\n\t\t\tthis.devicedata = this.opt.devicedata;\n\t\t}\n\t\tthis.getdata();\n\t},\n\tonShow:function(e){\n\t\tif(this.product.product_type==1){\n\t\t\tuni.$emit('getglassrecord');\n\t\t}\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonShareAppMessage:function(){\n\t\treturn this._sharewx({title:this.product.sharetitle || this.product.name,pic:this.product.sharepic || this.product.pic});\n\t},\n\tonShareTimeline:function(){\n\t\tvar sharewxdata = this._sharewx({title:this.product.sharetitle || this.product.name,pic:this.product.sharepic || this.product.pic});\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n\tonUnload: function () {\n\t\tclearInterval(interval);\n\t},\n\n\tmethods: {\n\t\tglassesPopupClose(){\n\t\t\tthis.ksk = 0;\n\t\t\tthis.$refs.glassesPupup.close();\n\t\t},\n\t\tclassChange(index){\n\t\tthis.ksk = index;\n\t\t\tthis.classIndex = index;\n\t\t},\n\t\t// 眼镜-滑动到最左边\n\t\tscrollToupper(){\n\t\t\tthis.scrollLeft = 0;\n\t\t},\n\t\t// 眼镜-规格滑动\n\t\tslideClass(){\n\t\t\tthis.scrollLeft += 89;\n\t\t},\n\t\tshowLinkChange: function () {\n\t\t\tthis.showLinkStatus = !this.showLinkStatus;\n\t\t},\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tvar id = this.opt.id || 0;\n\t\t\tthat.loading = true;\n\t\t\tvar devicedata = that.devicedata\n\t\t\tapp.get('ApiShop/product', {id: id,longitude:that.longitude,latitude:that.latitude,mendian_id:that.mendian_id,devicedata:devicedata}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg,function(){\n\t\t\t\t\t\tapp.goback();\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.textset = app.globalData.textset;\n\t\t\t\tvar product = res.product;\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\n\t\t\t\tif(res.shopdetail_menudata){\n\t\t\t\t\tthat.shopdetail_menudataList = res.shopdetail_menudata.list;\n\t\t\t\t\tthat.bottomImg = res.shopdetail_menudata.bottomImg;\n\t\t\t\t}else{\n\t\t\t\t\tthat.shopdetail_menudataList = false;\n\t\t\t\t\tthat.bottomImg = '';\n\t\t\t\t}\n\t\t\t\tthat.business = res.business;\n\t\t\t\tthat.product = product;\n\t\t\t\tthat.cartnum = res.cartnum;\n\t\t\t\tthat.commentlist = res.commentlist;\n\t\t\t\tthat.commentcount = res.commentcount;\n\t\t\t\tthat.cuxiaolist = res.cuxiaolist;\n\t\t\t\tthat.couponlist = res.couponlist;\n\t\t\t\tthat.fuwulist = res.fuwulist;\n\t\t\t\tthat.pagecontent = pagecontent;\n\t\t\t\tif(res.shopset) that.shopset = res.shopset;\n\t\t\t\tif(res.custom) that.custom = res.custom;\n\t\t\t\tthat.sysset = res.sysset;\n\t\t\t\tthat.title = product.name;\n\t\t\t\tthat.isfavorite = res.isfavorite;\n\t\t\t\tthat.showjiesheng = res.showjiesheng || 0;\n\t\t\t\tthat.is_member_auto_addlogin = res.is_member_auto_addlogin || 0;\n\t\t\t\tthat.tjdatalist = res.tjdatalist || [];\n\t\t\t\tthat.showtoptabbar = res.showtoptabbar || 0;\n\t\t\t\tthat.bboglist = res.bboglist;\n\t\t\t\tthat.sharepic = product.pics[0];\n\t\t\t \n\t\t\t\tthat.xcx_scheme = res.xcx_scheme\n\t\t\t\tthat.showprice_dollar = res.showprice_dollar\n\t\t\t\tthat.show_money_price = res.show_money_price;\n\t\t\t\tthat.commentposition = res.commentposition;\n\t\t\t\n\t\t\t\t//图片限制观看\n\t\t\t\tif(that.opt && that.opt.show_image != 0){\n\t\t\t\t\tif(res.show_image){\n\t\t\t\t\t\tthat.show_image = res.show_image;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: product.name\n\t\t\t\t});\n\t\t\t\tif(that.product.can_ziti){\n\t\t\t\t\tthat.showNearbyMendian = true;\n\t\t\t\t\tif(res.bindmendianids.length>0){\n\t\t\t\t\t\tthat.mendian = res.mendian\n\t\t\t\t\t\tthat.mendianids = res.bindmendianids\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthat.kfurl = '/pages/kefu/index?bid='+product.bid;\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\n\t\t\t\t}\n\t\t\t\tif(that.business && that.business.kfurl){\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\n\t\t\t\t}\n\t\t\t\tif(that.product.product_type==1){\n\t\t\t\t\t//初始化时清空档案\n\t\t\t\t\tapp.setCache('_glass_record_id','');\n\t\t\t\t}\n\n\t\t\t\tif(app.globalData.platform == 'wx' && that.product.rewardedvideoad && wx.createRewardedVideoAd){\n\t\t\t\t\tif(that.isloadAd == 0){\n\t\t\t\t\t\tthat.isloadAd = 1;\n\t\t\t\t\t\tapp.showLoading();\n\t\t\t\t\t\tif(!app.globalData.rewardedVideoAd[that.product.rewardedvideoad]){\n\t\t\t\t\t\t\tapp.globalData.rewardedVideoAd[that.product.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: that.product.rewardedvideoad});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar rewardedVideoAd = app.globalData.rewardedVideoAd[that.product.rewardedvideoad];\n\t\t\t\t\t\trewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});\n\t\t\t\t\t\trewardedVideoAd.onError((err) => {\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t//app.alert(err.errMsg);\n\t\t\t\t\t\t\tconsole.log('onError event emit', err)\n\t\t\t\t\t\t\trewardedVideoAd.offLoad()\n\t\t\t\t\t\t\trewardedVideoAd.offClose();\n\t\t\t\t\t\t\tthat.loaded({title:product.sharetitle || product.name,pic:product.sharepic || product.pic,desc:product.sharedesc || product.sellpoint});\n\t\t\t\t\t\t});\n\t\t\t\t\t\trewardedVideoAd.onClose(res => {\n\t\t\t\t\t\t\tapp.globalData.rewardedVideoAd[that.product.rewardedvideoad] = null;\n\t\t\t\t\t\t\tif (res && res.isEnded) {\n\t\t\t\t\t\t\t\t//app.alert('播放结束 发放奖励');\n\t\t\t\t\t\t\t\tthat.loaded({title:product.sharetitle || product.name,pic:product.sharepic || product.pic,desc:product.sharedesc || product.sellpoint});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t//console.log('播放中途退出，不发奖励');\n\t\t\t\t\t\t\t\t//that.loaded({title:product.sharetitle || product.name,pic:product.sharepic || product.pic,desc:product.sharedesc || product.sellpoint});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\trewardedVideoAd.offLoad()\n\t\t\t\t\t\t\trewardedVideoAd.offClose();\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tthat.loaded({title:product.sharetitle || product.name,pic:product.sharepic || product.pic,desc:product.sharedesc || product.sellpoint});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t//未登录的静默注册\n\t\t\t\tif(res.need_login==1){\n\t\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\t\tthat.alilogin();\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tthat.wxlogin();\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\tthat.autoaddlogin();\n\t\t\t\t\t\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t//需要定位\n\t\t\t\t//#ifndef MP-ALIPAY\n\t\t\t\tif(res.needlocation){\n\t\t\t\t\tapp.getLocation(function(res) {\n\t\t\t\t\t\tthat.latitude = res.latitude;\n\t\t\t\t\t\tthat.longitude = res.longitude;\n\t\t\t\t\t\tthat.getdata()\n\t\t\t\t\t},function(error){\n\t\t\t\t\t\tconsole.log(error)\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\t//#endif\n\t\t\t\tsetTimeout(function(){\n\t\t\t\t\tlet view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0')\n\t\t\t\t\tview0.fields({\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t}, (res) => {\n\t\t\t\t\t\tif(res)\n\t\t\t\t\t\tthat.scrolltab0Height = res.height\n\t\t\t\t\t}).exec();\n\t\t\t\t\tlet view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1')\n\t\t\t\t\tview1.fields({\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t}, (res) => {\n\t\t\t\t\t\tif(res)\n\t\t\t\t\t\tthat.scrolltab1Height = res.height\n\t\t\t\t\t}).exec();\n\t\t\t\t\tlet view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2')\n\t\t\t\t\tview2.fields({\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t}, (res) => {\n\t\t\t\t\t\tif(res)\n\t\t\t\t\t\tthat.scrolltab2Height = res.height\n\t\t\t\t\t}).exec();\n\t\t\t\t},500)\n\t\t\t});\n\t\t},\n\t  loadImg() {\n\t\t\tthis.getCurrentSwiperHeight('.img');\n\t\t},\n\t\tswiperChange: function (e) {\n\t\t\tvar that = this;\n\t\t\tthat.current = e.detail.current;\n\t\t\t// 禁止错误滑动事件\n\t\t\tif(!e.detail.source) return that.current = 0;\n\t\t\t//动态设置swiper的高度，使用nextTick延时设置\n\t\t\tthis.$nextTick(() => {\n\t\t\t  this.getCurrentSwiperHeight('.img');\n\t\t\t});\n\t\t},\n\t\t// 动态获取内容高度\n\t  getCurrentSwiperHeight(element) {\n\t\t\t\t// #ifndef MP-ALIPAY\n\t\t\t\tlet query = uni.createSelectorQuery().in(this);\n\t\t\t\tquery.selectAll(element).boundingClientRect();\n\t\t\t\tvar imgList = this.product.pics;\n\t      query.exec((res) => {\n\t        // 切换到其他页面swiper的change事件仍会触发，这时获取的高度会是0，会导致回到使用swiper组件的页面不显示了\n\t        if (imgList.length && res[0][this.current].height) {\n\t          this.swiperHeight = res[0][this.current].height;\n\t        }\n\t      });\t\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\tvar imgList = this.product.pics;\n\t\t\t\tmy.createSelectorQuery().select(element).boundingClientRect().exec((ret) => {\n\t\t\t\t\tif (imgList.length && ret[this.current].height) {\n\t\t\t\t\t  this.swiperHeight = ret[this.current].height;\n\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t// #endif\n\t\t},\n\t\tpayvideo: function () {\n\t\t\tthis.isplay = 1;\n\t\t\tuni.createVideoContext('video').play();\n\t\t},\n\t\tparsevideo: function () {\n\t\t\tthis.isplay = 0;\n\t\t\tuni.createVideoContext('video').stop();\n\t\t},\n\t\tbuydialogChange: function (e) {\n\t\t\tif(this.product && this.product.guige_show_type == 1){\n\t\t\t\tthis.showGuigeType();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(!this.buydialogShow){\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype\n\t\t\t}\n\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t},\n\t\t//收藏操作\n\t\taddfavorite: function (item) {\n\t\t\tvar that = this;\n\t\t\tvar proid = that.product.id;\n\t\t\tapp.post('ApiShop/addfavorite', {proid: proid,type: 'shop'}, function (data) {\n\t\t\t\tif (data.status == 1) {\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\n\t\t\t\t}\n\t\t\t\tapp.success(data.msg);\n\t\t\t});\n\t\t},\n\t\t//收藏操作\n\t\taddfavorite2: function (item) {\n\t\t\tvar that = this;\n\t\t\tif(item.pagePath == 'addfavorite::'){\n\t\t\t\tvar proid = that.product.id;\n\t\t\t\tapp.post('ApiShop/addfavorite', {proid: proid,type: 'shop'}, function (data) {\n\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\tthat.isfavorite = !that.isfavorite;\n\t\t\t\t\t}\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t});\n\t\t\t}else{\n\t\t\t\tlet url = '';\n\t\t\t\tif(item.menuType == 2) {\n\t\t\t\t\tif(item.pagePath){url = item.pagePath}else{url = '/pages/shop/cart'}\n\t\t\t\t}\n\t\t\t\tif(item.menuType == 1) {\n\t\t\t\t\tif(item.pagePath){url = item.pagePath}else{url = 'pages/kefu/index'}\n\t\t\t\t}\n\t\t\t\tif(item.menuType == 3 || item.menuType == 4){\n\t\t\t\t\turl = item.pagePath\n\t\t\t\t}\n\t\t\t\t// 判断是否为基础页面\n\t\t\t\tif(url == '/pages/shop/cart') return app.goto(url);\n\t\t\t\tif(url == '/pages/my/usercenter') return app.goto(url);\n\t\t\t\tif(url == '/pages/shop/classify') return app.goto(url);\n\t\t\t\tif(url == '/pages/shop/prolist') return app.goto(url);\n\t\t\t\tif(url == '/pages/index/index') return app.goto(url);\n\t\t\t\tif(url.split('?')[1] && (url.split('?')[1].split('=')[0] == 'bid')){\n\t\t\t\t\tapp.goto(url)\n\t\t\t\t}else{\n\t\t\t\t\tif(url.indexOf('tel:') === 0){\n\t\t\t\t\t\tapp.goto(url);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tapp.goto(url + '?bid=' + that.product.bid)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tshareClick: function () {\n\t\t\tthis.sharetypevisible = true;\n\t\t},\n\t\thandleClickMask: function () {\n\t\t\tthis.sharetypevisible = false\n\t\t},\n\t\tshowPoster: function () {\n\t\t\tvar that = this;\n\t\t\tthat.showposter = true;\n\t\t\tthat.sharetypevisible = false;\n\t\t\tapp.showLoading('生成海报中');\n\t\t\tapp.post('ApiShop/getposter', {proid: that.product.id}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (data.status == 0) {\n\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t} else {\n\t\t\t\t\tthat.posterpic = data.poster;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tshareScheme: function () {\n\t\t\tvar that = this;\n\t\t\tapp.showLoading();\n\t\t\tapp.post('ApiShop/getwxScheme', {proid: that.product.id}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (data.status == 0) {\n\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t} else {\n\t\t\t\t\t\tthat.showScheme = true;\n\t\t\t\t\t\tthat.schemeurl=data.openlink\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tschemeDialogClose: function () {\n\t\t\tthis.showScheme = false;\n\t\t},\n\t\t\n\t\tposterDialogClose: function () {\n\t\t\tthis.showposter = false;\n\t\t},\n\t\tshowfuwudetail: function () {\n\t\t\tthis.showfuwudialog = true;\n\t\t},\n\t\thidefuwudetail: function () {\n\t\t\tthis.showfuwudialog = false\n\t\t},\n\t\tshowcuxiaodetail: function () {\n\t\t\tthis.showcuxiaodialog = true;\n\t\t},\n\t\thidecuxiaodetail: function () {\n\t\t\tthis.showcuxiaodialog = false\n\t\t},\n\t\tgetcoupon:function(){\n\t\t\tthis.showcuxiaodialog = false;\n\t\t\tthis.getdata();\n\t\t},\n\t\tonPageScroll: function (e) {\n\t\t\tuni.$emit('onPageScroll',e);\n\t\t\t//var that = this;\n\t\t\t//var scrollY = e.scrollTop;     \n\t\t\t//if (scrollY > 200) {\n\t\t\t//\tthat.scrolltopshow = true;\n\t\t\t//}\n\t\t\t//if(scrollY < 150) {\n\t\t\t//\tthat.scrolltopshow = false\n\t\t\t//}\n\t\t\t//if (scrollY > 100) {\n\t\t\t//\tthat.toptabbar_show = true;\n\t\t\t//}\n\t\t\t//if(scrollY < 50) {\n\t\t\t//\tthat.toptabbar_show = false\n\t\t\t//}\n\t\t},\n\t\tchangetoptab:function(e){\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tthis.scrollToViewId = 'scroll_view_tab'+index;\n\t\t\tthis.toptabbar_index = index;\n\t\t\tif(index == 0) this.scrollTop = 0;\n\t\t\tconsole.log(index);\n\t\t},\n\t\tscroll:function(e){\n\t\t\tvar scrollTop = e.detail.scrollTop;\n\t\t\t//console.log(e)\n\t\t\tvar that = this;\n\t\t\tif (scrollTop > 200) {\n\t\t\t\tthat.scrolltopshow = true;\n\t\t\t}\n\t\t\tif(scrollTop < 150) {\n\t\t\t\tthat.scrolltopshow = false\n\t\t\t}\n\t\t\tif (scrollTop > 100) {\n\t\t\t\tthat.toptabbar_show = true;\n\t\t\t}\n\t\t\tif(scrollTop < 50) {\n\t\t\t\tthat.toptabbar_show = false\n\t\t\t}\n\t\t\tvar height0 = that.scrolltab0Height;\n\t\t\tvar height1 = that.scrolltab0Height + that.scrolltab1Height;\n\t\t\tvar height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;\n\t\t\t//var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;\n\t\t\tif(scrollTop >=0 && scrollTop < height0){\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab0';\n\t\t\t\tthis.toptabbar_index = 0;\n\t\t\t}else if(scrollTop >= height0 && scrollTop < height1){\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab1';\n\t\t\t\tthis.toptabbar_index = 1;\n\t\t\t}else if(scrollTop >= height1 && scrollTop < height2){\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab2';\n\t\t\t\tthis.toptabbar_index = 2;\n\t\t\t}else if(scrollTop >= height2){\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab3';\n\t\t\t\tthis.toptabbar_index = 3;\n\t\t\t}\n\t\t},\n\t\tsharemp:function(){\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\n\t\t\tthis.sharetypevisible = false\n\t\t},\n\t\tshareapp:function(){\n\t\t\t// #ifdef APP\n\t\t\tvar that = this;\n\t\t\tthat.sharetypevisible = false;\n\t\t\tuni.showActionSheet({\n\t\t\t  itemList: ['发送给微信好友', '分享到微信朋友圈'],\n\t\t\t  success: function (res){\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\n\t\t\t\t\t\tif (res.tapIndex == 1) {\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar sharedata = {};\n\t\t\t\t\t\tsharedata.provider = 'weixin';\n\t\t\t\t\t\tsharedata.type = 0;\n\t\t\t\t\t\tsharedata.scene = scene;\n\t\t\t\t\t\tsharedata.title = that.product.sharetitle || that.product.name;\n\t\t\t\t\t\tsharedata.summary = that.product.sharedesc || that.product.sellpoint;\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/shop/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\n\t\t\t\t\t\tif(sharelist){\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/pages/shop/product'){\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.share(sharedata);\n\t\t\t\t\t}\n\t\t\t  }\n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\tcallMendian:function(e){\n\t\t\tvar tel = e.currentTarget.dataset.tel;\n\t\t\tuni.makePhoneCall({\n\t\t\t\tphoneNumber: tel,\n\t\t\t\tfail: function () {\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\ttoMendian:function(e){\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\n\t\t\tvar address = e.currentTarget.dataset.address;\n\t\t\tif(!latitude || !longitude){\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t scale: 13\n\t\t\t})\n\t\t},\n\t\tchangeGuige:function(e){\n\t\t\t this.gwcnum = e.num\n\t\t\t this.ggid = e.ggid\n\t\t\t this.stock = e.stock\n\t\t\t this.product.stock = this.stock\n\t\t\t this.sell_price= e.sell_price\n\t\t},\n\t\t//加入购物车操作\n\t\taddcart2: function () {\n\t\t\tvar that = this;\n\t\t\tvar num = that.gwcnum;\n\t\t\tvar proid = that.product.id;\n\t\t\tvar ggid = that.ggid\n\t\t\tvar stock = that.stock;\n\t\t\tif(that.product && that.product.guige_show_type == 1){\n\t\t\t\tthat.guigeTypebuy('cart');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (num < 1) num = 1;\n\t\t\tif (stock < num) {\n\t\t\t\tapp.error('库存不足');\n\t\t\t\treturn;\n\t\t\t}\t\t\t\t\n\t\t\tapp.post('ApiShop/addcart', {proid: proid,ggid: ggid,num: num}, function (res) {\n\t\t\t\tthat.cartnum = that.cartnum + num;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tapp.success('添加成功');\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\ttobuy: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar proid = that.product.id;\n\t\t\tvar ggid = that.ggid;\n\t\t\tvar stock = that.stock;\n\t\t\tvar num = that.gwcnum;\n\t\t\tif(that.product && that.product.guige_show_type == 1){\n\t\t\t\tthat.guigeTypebuy('buy');\n\t\t\t\treturn; \n\t\t\t}\n\t\t\tif (num < 1) num = 1;\n\t\t\tif ((stock < num && !that.product.shop_yuding) || (that.product.shop_yuding && stock < num && that.product.yuding_stock < num) ) {\n\t\t\t\tapp.error('库存不足');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\n\t\t\tapp.goto('/pagesB/shop/buy?prodata=' + prodata);\n\t\t},\n\t\tshowsubqrcode:function(){\n\t\t\tthis.$refs.qrcodeDialog.open();\n\t\t},\n\t\tclosesubqrcode:function(){\n\t\t\tthis.$refs.qrcodeDialog.close();\n\t\t},\n\t\taddcart:function(e){\n\t\t\tconsole.log(e)\n\t\t\tthis.cartnum = this.cartnum + e.num;\n\t\t},\n\t\tshowgg1Dialog:function(){\n\t\t\tthis.$refs.gg1Dialog.open();\n\t\t},\n\t\tclosegg1Dialog:function(){\n\t\t\tthis.$refs.gg1Dialog.close();\n\t\t},\n\t\tshowgg2Dialog:function(){\n\t\t\tthis.$refs.gg2Dialog.open();\n\t\t},\n\t\tclosegg2Dialog:function(){\n\t\t\tthis.$refs.gg2Dialog.close();\n\t\t},\n\t\tautoaddlogin: function (){\n\t\t\t\tvar that = this;\n\t\t\t\tapp.post('ApiIndex/autoaddlogin',{ },function(res2){\n\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\t//app.success(res2.msg);\n\t\t\t\t\t\t//that.getdata();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t})\n\t\t\t},\n\t\twxlogin: function (){\n\t\t\t\tvar that = this;\n\t\t\t\twx.login({success (res1){\n\t\t\t\t\tconsole.log(res1);\n\t\t\t\t\tvar code = res1.code;\n\t\t\t\t\t//用户允许授权\n\t\t\t\t\tapp.post('ApiIndex/wxbaselogin',{ code:code,pid: app.globalData.pid,},function(res2){\n\t\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\t\t//app.success(res2.msg);\n\t\t\t\t\t\t\t//that.getdata();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t})\n\t\t\t  }});\n\t\t\t},\n\t\t\talilogin:function(){\n\t\t// #ifdef MP-ALIPAY\n\t\t\t\tvar that = this;\n\t\t\t\tvar ali_appid = that.ali_appid;\n\t\t\n\t\t\t\tif(ali_appid){\n\t\t\t\t\tmy.getAuthCode ({\n\t\t\t\t\t\tappId :  ali_appid ,\n\t\t\t\t\t\tscopes : ['auth_base'],\n\t\t\t\t\t},function(res){\n\t\t\t\t\t\t//var res = JSON.stringify(res);\n\t\t\t\t\t\tif(!res.error && res.authCode){\n\t\t\t\t\t\t  app.post('ApiIndex/alipaylogin', {\n\t\t\t\t\t\t\tcode: res.authCode,\n\t\t\t\t\t\t\tpid: app.globalData.pid,\n\t\t\t\t\t\t\tsilent:1\n\t\t\t\t\t\t\t//platform:\"h5\"\n\t\t\t\t\t\t  }, function(res2) {\n\t\t\n\t\t\t\t\t\t\tif (res2.status!= 0) {\n\t\t\t\t\t\t\t  app.success(res2.msg);\n\t\t\t\t\t\t\t  //that.getdata();\n\t\t\t\t\t\t\t}  else {\n\t\t\t\t\t\t\t  app.error(res2.msg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t  });\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t  app.showLoading(false);\n\t\t\n\t\t\t\t\t\t  if(res.errorMessage){\n\t\t\t\t\t\t\tapp.alert(res.errorMessage);\n\t\t\t\t\t\t  }else if(res.errorDesc){\n\t\t\t\t\t\t\tapp.alert(res.errorDesc);\n\t\t\t\t\t\t  }else{\n\t\t\t\t\t\t\tapp.alert('授权出错');\n\t\t\t\t\t\t  }\n\t\t\t\t\t\t  return\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t  app.alert('系统未配置支付宝参数');\n\t\t\t\t  return\n\t\t\t\t}\n\t\t// #endif\n\t\t\t},\n\t\t\tcopy1: function(e) {\n\t\t\t\tvar that=this\n\t\t\t\tvar text = that.product.name+'\\n\\n'+that.product.sellpoint;\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: text,\n\t\t\t\t\tsuccess: function () {\n\t\t\t\t\t\tgetApp().error('复制成功')\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//采购单\n\t\t\tpurchaseorder: function (e) {\n\t\t\t\tthis.purchaseOrderShow = !this.purchaseOrderShow;\n\t\t\t},\n\t\t\tgotolevelup:function(){\n\t\t\t\tif (!app.globalData.mid) {\n\t\t\t\t\tvar frompage = encodeURIComponent(app._fullurl());\n\t\t\t\t\treturn app.goto('/pages/index/login?frompage=' + frompage, 'reLaunch');\n\t\t\t\t}\n\t\t\t\treturn app.goto('/pagesExt/my/levelup');\n\t\t\t},\n\t\t\ttoLocation(){\t\n\t\t\t\t//#ifdef MP-ALIPAY\n\t\t\t\tvar that = this;\n\t\t\t\tapp.getLocation(function(res) {\n\t\t\t\t\tthat.latitude = res.latitude;\n\t\t\t\t\tthat.longitude = res.longitude;\n\t\t\t\t\tthat.getdata()\n\t\t\t\t},function(error){\n\t\t\t\t\tconsole.log(error)\n\t\t\t\t})\n\t\t\t\t//#endif\n\t\t\t},\n\t\t\tshowGuigeType:function(){\n\t\t\t\tvar that = this;\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.post('ApiShop/getproductdetail',{id:that.opt.id,reset:1},function(res){\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tif(res.status != 1){\n\t\t\t\t\t\tapp.alert(res.msg)\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthat.nowguige = res.guigelist[that.ksk];\n\t\t\t\t\tthat.nowguigeProduct = that.nowguige[res.ks];\n\t\t\t\t\t\n\t\t\t\t\tthat.guigelist = res.guigelist;\n\t\t\t\t\tthat.guigedata = res.guigedata;\n\t\t\t\t\tif(that.nowguigeProduct.limit_start > 0)\n\t\t\t\t\t\tthat.gwcnum = that.nowguigeProduct.limit_start;\n\t\t\t\t\telse\n\t\t\t\t\t\tthat.gwcnum = that.nowguigeProduct.limit_start;\n\t\t\t\t\tthat.guigedata[1].items.map(item => item.num = 0); //向2规格加入初始值\n\t\t\t\t\tthat.show_guige_type = 1;\n\t\t\t\t\tthat.$refs.glassesPupup.open();\n\t\t\t\t});\n\t\t\t},\n\t\t\tchangeradio:function(e){\n\t\t\t\tvar that = this;\n\t\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\t\tvar ksk = that.ksk;\n\t\t\t\tvar gg = that.guigelist[ksk][index];\n\t\t\t\tif(gg.checked == true && gg.num > 0){\n\t\t\t\t\t//取消选中时\n\t\t\t\t\tthat.guigedata[1].items[ksk].num -= gg.num;\n\t\t\t\t\tthat.guigelist[ksk][index].num = 0;\n\t\t\t\t}else if(gg.checked == false && gg.num > 0){\n\t\t\t\t\tthat.guigedata[1].items[ksk].num += gg.num;\n\t\t\t\t}\n\t\t\t\tthat.guigelist[ksk][index].checked = !gg.checked;\n\t\t\t},\n\t\t\tchangeradioAll:function(e){\n\t\t\t\tvar that = this;\n\t\t\t\tvar ksk = that.ksk;\n\t\t\t\tObject.values(that.guigelist[ksk]).forEach(item => {\n\t\t\t\t\titem.checked = !item.checked;\n\t\t\t\t});\n\t\t\t},\n\t\t\tgwcplus: function (e,index) {\n\t\t\t\tlet gwcnum = e.num + 1;\n\t\t\t\tlet ksk = this.ksk;\n\t\t\t\tif (gwcnum > this.guigelist[ksk][index].stock) {\n\t\t\t\t\tapp.error('库存不足');\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\n\t\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.guigelist[ksk][index].checked = true;\n\t\t\t\tthis.guigelist[ksk][index].num = gwcnum;\n\t\t\t\tthis.guigedata[1].items[ksk].num += 1;\n\t\t\t},\n\t\t\tgwcminus: function (e,index) {\n\t\t\t\tif(!e.num) return;\n\t\t\t\tlet gwcnum = e.num - 1;\n\t\t\t\tlet ksk = this.ksk;\n\t\t\t\tif(gwcnum <= 0){\n\t\t\t\t\tgwcnum = 0;\n\t\t\t\t\tthis.guigelist[ksk][index].checked = false;\n\t\t\t\t}\n\t\t\t\tthis.guigelist[ksk][index].num = gwcnum;\n\t\t\t\tthis.guigedata[1].items[ksk].num -= 1;\n\t\t\t},\n\t\t\tgwcinput: function (e,item,index) {\n\t\t\t\tlet ksk = this.ksk;\n\t\t\t\tlet gwcnum = parseInt(e.detail.value);\n\t\t\t\tif(gwcnum > item.stock) {\n\t\t\t\t\tgwcnum = item.stock;\n\t\t\t\t}\n\t\t\t\tif(gwcnum <= 0){\n\t\t\t\t\tgwcnum = 0;\n\t\t\t\t\tthis.guigelist[ksk][index].checked = false;\n\t\t\t\t}else{\n\t\t\t\t\tthis.guigelist[ksk][index].checked = true;\n\t\t\t\t}\n\t\t\t\tthis.guigelist[ksk][index].num = gwcnum;\n\t\t\t\tthis.guigedata[1].items[ksk].num += gwcnum;\n\t\t\t},\n\t\t\tguigeTypebuy:function(type=\"buy\"){\n\t\t\t\tvar that = this;\n\t\t\t\tvar prodatagg = [];\n\t\t\t\tvar proid = that.product.id;\n\t\t\t\tthat.guigelist.forEach(function(subArray) {\n\t\t\t\t\tObject.values(subArray).forEach(function(item) {\n\t\t\t\t\t\t\tif (item.num > 0 && item.checked) {\n\t\t\t\t\t\t\t\t\tprodatagg.push(item);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\tif(!prodatagg.length) return app.error(\"数量不能为0\");\n\t\t\t\tlet thisprodata = [];\n\t\t\t\tlet totalNum = 0;\n\t\t\t\tfor (var i = 0; i < prodatagg.length; i++) {\n\t\t\t\t\ttotalNum += prodatagg[i].num;\n\t\t\t\t\tthisprodata.push(proid + ',' + prodatagg[i].id + ',' + prodatagg[i].num);\n\t\t\t\t}\n\t\t\t\t//起售判断\n\t\t\t\tif(that.product.limit_start > 0 && totalNum < that.product.limit_start){\n\t\t\t\t\treturn app.error('该商品' + that.product.limit_start + '件起售');\n\t\t\t\t}\n\t\t\t\tthisprodata = thisprodata.join('-');\n\t\t\t\t//购买\n\t\t\t\tif(type == 'buy'){\n\t\t\t\t\tapp.goto('/pages/shop/buy?prodata=' + thisprodata);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t//添加到购物车\n\t\t\t\tapp.post('ApiShop/addcartmore', {prodata: thisprodata,glass_record_id:''}, function (res) {\n\t\t\t\t\tif (res.status == 1) {\n\t\t\t\t\t\tapp.success('添加成功');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\t}\n\t\t\t\t\tthat.glassesPopupClose()\n\t\t\t\t});\n\t\t\t}\n\t},\n\t\n};\n</script>\n<style scoped>\npage {position: relative;width: 100%;}\n.container{height:100%}\n.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}\n.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}\n.follow_topbar .headimg image {height:64rpx; width:64rpx;}\n.follow_topbar .info {height:56rpx; padding:16rpx 0;}\n.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;} \n.follow_topbar .info {height:80rpx; float:left;}\n.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}\n.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.qrcodebox .img{width:400rpx;height:400rpx}\n.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n\n.swiper-container{position:relative;overflow: hidden;}\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\n.swiper-item-view{width: 100%;height: 750rpx;}\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\n\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\n\n.provideo{background:rgba(255,255,255,0.7);width:190rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\n.provideo image,.playicon{width:50rpx;height:50rpx;}\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\n.wxfeedvideo{background:rgba(255,255,255,0.7);width:190rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;}\n.wxfeedvideo .videop{display:flex;align-items:center;justify-content:space-between}\n.wxfeedvideo .feedvideo{position: relative;height: 54rpx;width: 100%;top: -54rpx;z-index: 9999;width: 200rpx;opacity: 0;}\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\n.videobox .video{width:100%;height:650rpx;}\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}\n\n.header {width: 100%;padding: 20rpx 3%;background: #fff;}\n.header .price_share{width:100%;min-height:100rpx;display:flex;align-items:center;justify-content:space-between}\n.header .price_share .price{display:flex;align-items:flex-end}\n.header .price_share .price .f1{font-size:50rpx;color:#51B539;font-weight:bold}\n.header .price_share .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center;min-width: 60rpx;}\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\n.header .price_share .title { display:flex;align-items:flex-end;}\n.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}\n.header .sales_stock{display:flex;justify-content:space-between;height:60rpx;line-height:60rpx;margin-top:30rpx;font-size:24rpx;color:#777777}\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\n.header .upsavemoney{display:flex;align-items:center;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:28rpx;height:70rpx;padding:0 20rpx}\n/* 定制的价格字体和颜色 */\n.header .custom_price .price{font-size: 32rpx;}\n.header .custom_price .market_price{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:20rpx;}\n.header .custom_price .custom_price_tag{font-size: 20rpx;}\n\n/* 分期 */\n.choose-fenqi{width: 100%;display: flex;align-items:center;background: #fff;margin-top: 20rpx; height: auto;padding: 25rpx 3%; color: #333;}\n.choose-fenqi .f0{color:#555;font-weight:bold;font-size:24rpx;padding-right:30rpx;white-space: nowrap;}\n.choose-fenqi .fenqi-info-view{width: 88%;}\n.choose-fenqi .fenqi-info-view .commission-fenqi{display: inline-block;margin-bottom:30rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx;}\n.choose-fenqi .fenqi-info-view .fenqi-list-view{width: 100%;}\n.fenqi-info-view .fenqi-list-view .fenqi-options{width: 200rpx;display: inline-block;margin-right: 20rpx;background:#f6f6f6;border-radius:8rpx;padding: 8rpx 0rpx;}\n.fenqi-options .fenqi-num{font-size: 24rpx;color: #333;width: 100%;text-align: center;padding: 3rpx 0rpx;display: flex;align-items: center;justify-content: center;}\n.fenqi-options .fenqi-num .fenqi-bili{font-size: 20rpx;color: #5b5b5b;margin-left: 10rpx;}\n.fenqi-options .fenqi-give{font-size: 22rpx;color: #5b5b5b;width: 100%;text-align: center;padding: 3rpx 0rpx;}\n\n\n\n.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; }\n.choose .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\n.choose .f2{ width: 32rpx; height: 32rpx;}\n\n.cuxiaodiv{background:#fff;margin-top:20rpx;padding:0 3%;}\n.cuxiaodiv .cuxiaopoint .shouquan{height:55rpx;line-height:55rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 20rpx;font-size:24rpx;font-weight:bold}\n.fuwupoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\n.fuwupoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\n.fuwupoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\n.fuwupoint .f1 .t{ padding:4rpx 20rpx 4rpx 0;color:#777;flex-shrink:0}\n.fuwupoint .f1 .t:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\n.fuwupoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\n.fuwupoint .f2 .img{width:32rpx;height:32rpx;}\n.fuwudialog-content{font-size:24rpx}\n.fuwudialog-content .f1{color:#333;height:80rpx;line-height:80rpx;font-weight:bold}\n.fuwudialog-content .f1:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\n.fuwudialog-content .f2{color:#777}\n\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\n.cuxiaopoint .f1 .t1{padding:0 4px}\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\n.cuxiaodiv .cuxiaoitem{border-bottom:1px solid #E6E6E6;}\n.cuxiaodiv .cuxiaoitem:last-child{border-bottom:0}\n\n.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}\n.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}\n.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}\n.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}\n.popup__title-text{font-size:32rpx}\n.popup__close{position:absolute;top:34rpx;right:34rpx}\n.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}\n.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\n.service-item .prefix{padding-top: 2px;}\n.service-item .suffix{padding-left: 10rpx;}\n.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}\n\n\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\n.shop .p2{padding-left:10rpx}\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\n\n.detail{min-height:200rpx;}\n\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\n\n.commentbox{width:100%;background:#fff;padding:0 3%;margin-top:20rpx}\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx;display: flex;align-items: center;}\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\n\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\n.comment .item .f1 .t3{text-align:right;}\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\n.comment .item .f2 .t2{display:flex;width:100%}\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\n\n.addcommentimg{height:36rpx;width: 36rpx;margin-left: 10rpx;}\n\n.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}\n.bottombar .f1{display:flex;align-items:center;margin-right:15rpx;}\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:82rpx;position:relative;}\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\n.bottombar .op{border-radius:36rpx;overflow:hidden;display:flex;flex: 1;}\n.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\n.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}\n\n.bottombar .op2{width:60%;overflow:hidden;display:flex;}\n.bottombar .tocart2{ flex:1;height: 80rpx;border-radius:10rpx;color: #fff; background: #fa938a; font-size: 28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin-right:10rpx;}\n.bottombar .tobuy2{ flex:1; height: 80rpx;border-radius:10rpx;color: #fff; background: #df2e24; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center}\n\n.paramitem{display:flex;border-bottom:1px solid #f5f5f5;padding:20rpx}\n.paramitem .f1{width:30%;color:#666}\n.paramitem .f2{color:#333}\n.paramitem:last-child{border-bottom:0}\n\n.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:20rpx 160rpx;margin-top:20rpx}\n.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}\n.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\n.xihuan-text .txt{color:#111;font-size:30rpx}\n.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}\n.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}\n\n.toptabbar_tab{display:flex;width:100%;height:90rpx;background: #fff;top:var(--window-top);z-index:11;position:fixed;border-bottom:1px solid #f3f3f3}\n.toptabbar_tab .item{flex:1;font-size:28rpx; text-align:center; color:#666; height: 90rpx; line-height: 90rpx;overflow: hidden;position:relative}\n.toptabbar_tab .item .after{display:none;position:absolute;left:50%;margin-left:-16rpx;bottom:10rpx;height:3px;border-radius:1.5px;width:32rpx}\n.toptabbar_tab .on{color: #323233;}\n.toptabbar_tab .on .after{display:block}\n\n.scrolltop{position:fixed;bottom:160rpx;right:20rpx;width:60rpx;height:60rpx;background:rgba(0,0,0,0.4);color:#fff;border-radius:50%;padding:12rpx 10rpx 8rpx 10rpx;z-index:9;}\n.scrolltop .image{width:100%;height:100%;}\n\n.ggdiaplog_close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n\n.schemeDialog {background:rgba(0,0,0,0.4);z-index:12;}\n.schemeDialog .main{ position: absolute;top:30%}\n.schemecon{padding: 40rpx 30rpx; }\n.copybtn{ text-align: center; margin-top: 30rpx; padding:15rpx 20rpx; border-radius: 50rpx; color:#fff}\n\n.member{position: relative;border-radius: 8rpx;border: 1rpx solid #fd4a46;overflow: hidden;box-sizing: content-box;}\n.member_lable{height: 100%;font-size: 22rpx;color: #fff;background: #fd4a46;padding: 0 15rpx;}\n.member_value{padding: 0 15rpx;font-size: 30rpx;font-weight: bold;color: #fd4a46;}\n.flex-s{display: flex;justify-content: flex-start;align-items: center;}\n.moneyprice{font-size: 26rpx;font-weight: 400;padding: 4rpx 10rpx;min-width: 90rpx;text-align: center;margin-left: 10rpx;border-radius: 20rpx;}\n\n/*附近门店S*/\n.nearby-mendian-box{margin:20rpx 0; background: #FFFFFF;width: 100%;padding: 20rpx;}\n.nearby-mendian-title{display: flex;justify-content: space-between;align-items: center;}\n.nearby-mendian-title .t1{font-size: 30rpx;font-weight: bold;}\n.nearby-mendian-title .t2{color: #999;font-size: 26rpx;}\n.nearby-mendian-title .t2 image{height: 26rpx;width: 26rpx;vertical-align: middle;}\n.nearby-mendian-info{display: flex;align-items: center;width: 100%;margin-top: 20rpx;}\n.mendian-info .b1{background-color: #fbfbfb;}\n.nearby-mendian-info .b1 image{height: 90rpx;width:90rpx;border-radius: 6rpx;border: 1px solid #e8e8e8;}\n.nearby-mendian-info .b2{flex:1;line-height: 38rpx;margin-left: 10rpx;max-width: 70%;overflow: hidden;}\n.nearby-mendian-info .b2 .t1{padding-bottom: 10rpx;}\n.nearby-mendian-info .b2 .t2{font-size: 24rpx;color: #999;}\n.nearby-mendian-info .b3{display: flex;justify-content: flex-end;flex-shrink: 0;padding-left: 10rpx;width: 130rpx;}\n.nearby-mendian-info .b3 image{width: 40rpx;height: 40rpx;margin-right: 16rpx;}\n.nearby-mendian-info .nearby-tag{padding:0 10rpx;margin-right: 10rpx;display: inline-block;font-size: 22rpx;border-radius: 8rpx;flex-shrink: 0;}\n.nearby-mendian-info .mendian-distance{flex-shrink: 0;margin-right: 10rpx;}\n.nearby-mendian-info .mendian-address{white-space: nowrap;text-overflow: ellipsis;max-width: 80%;}\n.pd10{padding-left: 10rpx;}\n/*附近门店E*/\n.bottomimg{width: 100%;}\n.shop_label{display: inline-block;padding: 0rpx 8rpx;border-radius: 8rpx;margin-right: 10rpx;height: 38rpx;line-height: 38rpx;font-size: 24rpx;font-weight: 400;}\n/* 采购单悬浮框 */\n.suspension{position: fixed;right: 10rpx; bottom: 280rpx;width: 100rpx; height: 100rpx; background-color: rgba(0, 0, 0, 0.3); color: white;border-radius: 100%;display: flex;justify-content: center;align-items: center; z-index: 10;}\n.suspension .suspension-purchase{text-align: center;}\n.suspension .suspension-text{display: block;font-size: 24rpx;padding: 2rpx;}\n/* 模糊图片的样式 */\n.filter-background {position: absolute;left: 0;top: 0;width: 100%;height: 100%;backdrop-filter: blur(8px);z-index: 1;}\n.lock-image{position: absolute; top: 50%;left: 50%;transform: translate(-50%, -50%);\tz-index: 10;}\n.lock-image image{width: 200rpx;height: 200rpx;}\n/* 营销标签 */\n.yingxiao_tag{text-align: center;color: #fff;border-radius: 8rpx;background:#FA3218;position: relative;padding: 5rpx 30rpx; margin-left: 40rpx;margin-bottom: 10rpx;}\n.yingxiao_tag .jiantou{width: 0;height: 0;border-top: 10rpx solid transparent;border-bottom: 10rpx solid transparent;border-right: 10rpx solid #FA3218;position: absolute;left: -10rpx;top: 15rpx;}\n.choujiangtext{line-height: 40rpx;padding: 20rpx 0;width: 98%;text-align: center;border-radius: 8rpx;margin:0 auto}\n/* 眼镜 */\n.glasses-pupup-view{width: 100%;background: #fff;border-radius: 30rpx 30rpx 0rpx 0rpx;padding-bottom: constant(safe-area-inset-bottom);padding-bottom: env(safe-area-inset-bottom);\nposition: relative;max-height: 94vh;padding-top: 20rpx;}\n.glasses-pupup-close{position:absolute;top:25rpx;right:25rpx;width: 38rpx;height: 38rpx;}\n.glasses-pupup-close image{width: 100%;height: 100%;}\n.glasses-pupup-product{width: 100%;justify-content: flex-start;align-items: center;padding: 0rpx 20rpx;}\n.glasses-product-image{width: 189rpx;height: 189rpx;border-radius: 4rpx;overflow: hidden;}\n.glasses-product-image image{width: 100%;height: 100%;}\n.glasses-product-info{flex: 1;padding-left: 20rpx;}\n.glasses-product-name{font-size: 26rpx;color: #212121;display: -webkit-box;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;-webkit-line-clamp: 2; /* 控制显示的行数 */}\n.glasses-product-price{align-items: center;justify-content: flex-start;font-size: 26rpx;padding-top: 10rpx;}\n.glasses-product-tag{font-size: 20rpx;display: inline-block;border-radius: 4rpx;width: min-content;padding:4rpx 6rpx;}\n.glasses-product-class{width:100%;padding-top: 20rpx;height:calc(76vh + env(safe-area-inset-bottom));}\n.g-productClass-top{width: 100%;height: 120rpx;}\n.g-productClass-top .left-view{width: 26%;height: 120rpx;position: relative;background: linear-gradient(29deg, transparent, transparent 48%, #f2f2ff 50%, #f2f2ff 100%);}\n.g-productClass-top .left-view .left-view-text{color: #4840db;text-align: right;padding-top: 10rpx;}\n.g-productClass-top .left-view .left-view-text .left-view-name{font-size: 30rpx;}\n.g-productClass-top .right-view{background: #f2f2ff;height: 120rpx;align-items: center;width: 69%;}\n.g-productClass-top .right-view .scroll-view-class{width: 100%;white-space: nowrap;height: 120rpx;}\n.g-productClass-top .right-view .right-view-options{display: inline-block;line-height:125rpx;align-items: center;font-size: 28rpx;color: #000;\nwidth: 165rpx;text-align: center;box-sizing: border-box;position: relative;}\n.g-productClass-top .right-view .right-view-options .options-tag{width: auto;border-radius: 18rpx;text-align: center;line-height: 36rpx;\nbackground: #ff0000;color: #fff;font-size: 22rpx;position: absolute;top: 10rpx;right: 10rpx;padding:0rpx 12rpx;}\n.g-productClass-top .right-view .right-view-active{border-bottom:2px #4840db solid;color: #4840db;}\n.g-productClass-top .right-view-but{background: #f2f2ff;height: 120rpx;align-items: center;width: 5%;}\n.g-productClass-top .right-view-but image{width: 100%;height:45%;}\n.g-productClass-bottom{width: 100%;height: 80rpx;justify-content: space-between;}\n.g-productClass-bottom .left-view{width: 26%;align-items: center;justify-content: flex-end;}\n.g-productClass-bottom .left-view .select-view{width: 35rpx;height: 35rpx;border: 1px #d8d8d8 solid;border-radius: 50%;align-items: center;justify-content: center;\nmargin-right: 15rpx;}\n.g-productClass-bottom .left-view .select-view .select-view-image{width: 93%;height: 93%;}\n.g-productClass-bottom .left-view .select-view-active{background-color: #3930d8;}\n.g-productClass-bottom .left-view .left-view-class{align-items: center;width: 125rpx;justify-content: flex-start;padding-left: 20rpx;}\n.g-productClass-bottom .left-view .left-view-class .title-text{font-size: 26rpx;color: #000;white-space: nowrap;}\n.g-productClass-bottom .left-view .left-view-class .left-view-sku{font-size: 20rpx;color: #666;padding-left: 8rpx;padding-top: 5rpx;}\n.g-productClass-bottom .right-view{width: 74%;align-items: center;justify-content: flex-start;}\n.g-productClass-bottom .right-view .right-view-bg{background: #f2f2ff;width: 165rpx;height: 100%;}\n/* #ifdef H5 */\n.glasses-sku-view{width: 100%;max-height:calc(54vh + env(safe-area-inset-bottom));min-height: 300rpx;height: auto;}\n/* #endif */\n/* #ifndef H5*/\n.glasses-sku-view{width: 100%;max-height:calc(47vh + env(safe-area-inset-bottom));min-height: 300rpx;height: auto;}\n/* #endif */\n.glasses-sku-options{width: 100%;height: 80rpx;align-items: center;}\n.glasses-sku-options .left-sku-view{width: 26%;align-items: center;justify-content: flex-end;}\n.glasses-sku-options .left-sku-view .select-view{width: 35rpx;height: 35rpx;border: 1px #d8d8d8 solid;border-radius: 50%;align-items: center;justify-content: center;\nmargin-right: 15rpx;}\n.glasses-sku-options .left-sku-view .select-view .select-view-image{width: 93%;height: 93%;}\n.glasses-sku-options .left-sku-view .select-view-active{background-color: #3930d8;}\n.glasses-sku-options .left-sku-view .left-view-class{width: 125rpx;height:58rpx;text-align: left;font-size: 26rpx;color: #000;white-space: nowrap;\nline-height: 58rpx;padding-left: 20rpx;}\n.glasses-sku-options .left-sku-view .left-view-active{border-radius: 30rpx 0rpx 0rpx 30rpx;background: #3930d8;color: #fff;}\n.glasses-sku-options .right-sku-view{width: 74%;align-items: center;justify-content: flex-start;height: 100%;}\n.glasses-sku-options .right-sku-view .right-view-bg{background: #f2f2ff;width: 165rpx;height: 100%;line-height:80rpx;text-align: center;align-items: center;}\n.right-sku-view .right-view-bg .right-view-num{border-radius: 0rpx 30rpx 30rpx 0rpx;width: 100%;height: 58rpx;align-items: center;text-align: center;justify-content: center;color: #666;}\n.right-sku-view .right-view-bg .right-view-num-active{border: 1px #3930d8 solid;color: #3930d8;}\n.right-sku-view .right-view-bg .right-view-num-active2{background:#3930d8 ;color: #fff;}\n.right-sku-view .right-view-bg .right-view-bg-not{width: 100%;text-align: center;font-size: 26rpx;color: #333333;}\n.glasses-sku-options .right-sku-view .right-view-inventory{width: 140rpx;height: 100%;line-height:80rpx;text-align: center;color: #000;font-size: 18rpx;white-space: nowrap;}\n.right-count-view{flex:1;align-items: center;justify-content: center;}\n.right-count-view .but-class{width: 50rpx;height: 50rpx;text-align:center;line-height: 45rpx;border: 1px #d8d8d8 solid;border-radius: 4rpx;font-size: 40rpx;}\n.right-count-view .input-view{width: 134rpx;height: 50rpx;border: 1px #d8d8d8 solid;border-radius: 4rpx;margin: 0rpx 6rpx;}\n.right-count-view .input-view .input-class{width: 100%;height: 50rpx;font-size: 24rpx;color: #666;text-align: center;}\n.glasses-bottom-but{width: 96%;height: 90rpx;border-radius: 50rpx;margin: 20rpx auto 20rpx;align-items: center;justify-content: space-between;overflow: hidden;}\n.glasses-bottom-but .bottom-but-class{flex: 1;height: 90rpx;line-height: 90rpx;color: #fff;border-radius: 0px;border: none;font-size: 28rpx;font-weight: bold;text-align: center;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&id=575548cf&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&id=575548cf&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839380266\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}