{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy.vue?ab46", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy.vue?ac16", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy.vue?5af1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy.vue?83ec", "uni-app:///pages/shop/fastbuy.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy.vue?89f9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/shop/fastbuy.vue?c91f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "cartListShow", "buydialogShow", "harr", "datalist", "cartList", "proid", "totalprice", "currentActiveIndex", "animation", "scrollToViewId", "bid", "scrollState", "productType", "ggNum", "custom", "mendianid", "pre_url", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "mendian_id", "clickRootItem", "setTimeout", "addcart", "ggid", "num", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "console", "currentTarget", "dataset", "afteraddcart2", "clearShopCartFn", "gopay", "prodata", "gotoCatproductPage", "scroll", "countH", "buydialogChange", "handleClickMask"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrKA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuIz1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;IACA;MACA;QACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAZ;QAAAa;MAAA;QACAF;QACAA;QACAA;QACAA;;QAEA;QACA;QACA;QACA;QACA;UACA;UACAnB;QACA;QACAmB;QAEA;UACAA;UACA;YACA;UACA;QACA;QACAA;MACA;IACA;IACAG;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAL;MACAC;QAAAjB;QAAAsB;QAAAC;MAAA;QACAP;QACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAO;MACAC;MACAC;MACA;QAAAC;UAAAC;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACAT;QACAJ;MACA;IACA;IACAc;MACA;MACAb;QAAAZ;MAAA;QACAW;MACA;IACA;IACAe;MACA;MACA;QACAd;QACA;MACA;MACA;MACA;QACAe;MACA;MACAf;IACA;IACAgB;MACA;MACA;QACAhB;MACA;QACAA;MACA;IACA;IACAiB;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACA;YACA;YACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxTA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/shop/fastbuy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/shop/fastbuy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fastbuy.vue?vue&type=template&id=4e919f0a&\"\nvar renderjs\nimport script from \"./fastbuy.vue?vue&type=script&lang=js&\"\nexport * from \"./fastbuy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fastbuy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/shop/fastbuy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy.vue?vue&type=template&id=4e919f0a&\"", "var components\ntry {\n  components = {\n    buydialogPifa: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa/buydialog-pifa\" */ \"@/components/buydialog-pifa/buydialog-pifa.vue\"\n      )\n    },\n    buydialogPifa2: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog-pifa2/buydialog-pifa2\" */ \"@/components/buydialog-pifa2/buydialog-pifa2.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = index === _vm.currentActiveIndex ? _vm.t(\"color1\") : null\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var l3 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (detail, indexs) {\n        var $orig = _vm.__get_orig(detail)\n        var l2 = _vm.__map(detail.prolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 =\n            _vm.custom.product_show_sellpoint && item.sellpoint\n              ? _vm.t(\"color2\")\n              : null\n          var m3 =\n            item.price_type != 1 || item.sell_price > 0 ? _vm.t(\"color1\") : null\n          var m4 =\n            item.price_type != 1 || item.sell_price > 0\n              ? !_vm.isNull(item.service_fee) &&\n                item.service_fee_switch &&\n                item.service_fee > 0\n              : null\n          var m5 =\n            (item.price_type != 1 || item.sell_price > 0) && m4\n              ? _vm.t(\"color1\")\n              : null\n          var m6 =\n            (item.price_type != 1 || item.sell_price > 0) && m4\n              ? _vm.t(\"服务费\")\n              : null\n          var m7 =\n            (item.price_type != 1 || item.sell_price > 0) &&\n            _vm.custom.product_weight &&\n            item.product_type == 2 &&\n            item.unit_price\n              ? _vm.t(\"color1\")\n              : null\n          var g0 = item.priceshows && item.priceshows.length > 0\n          var g1 =\n            _vm.custom.product_show_fwlist &&\n            item.fwlist &&\n            item.fwlist.length > 0\n          var l1 = g1\n            ? _vm.__map(item.fwlist, function (fw, fwidx) {\n                var $orig = _vm.__get_orig(fw)\n                var m8 = _vm.t(\"color2rgb\")\n                var m9 = _vm.t(\"color2\")\n                return {\n                  $orig: $orig,\n                  m8: m8,\n                  m9: m9,\n                }\n              })\n            : null\n          var m10 = !item.price_type ? _vm.t(\"color1rgb\") : null\n          var m11 = !item.price_type ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n            g0: g0,\n            g1: g1,\n            l1: l1,\n            m10: m10,\n            m11: m11,\n          }\n        })\n        return {\n          $orig: $orig,\n          l2: l2,\n        }\n      })\n    : null\n  var m12 = _vm.isload ? _vm.t(\"color1\") : null\n  var m13 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m14 = _vm.isload && _vm.cartList.total > 0 ? _vm.t(\"color1\") : null\n  var m15 = _vm.isload ? _vm.t(\"color1\") : null\n  var m16 = _vm.isload ? _vm.t(\"color1\") : null\n  var m17 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var g2 = _vm.isload && _vm.cartListShow ? _vm.cartList.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l3: l3,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"view-show\">\n\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/pages/shop/search?bid='+bid\" class=\"search-container\">\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t\t<view class=\"search-text\">搜索感兴趣的商品</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"content\" style=\"overflow:hidden;display:flex\" :style=\"{height:'calc(100% - '+(menuindex>-1?294:194)+'rpx)'}\">\n\t\t\t\t<scroll-view class=\"nav_left\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\" >\n\t\t\t\t\t<view class=\"nav_left_items\" :class=\"index===currentActiveIndex?'active':''\" :style=\"{color:index===currentActiveIndex?t('color1'):'#333'}\" @tap=\"clickRootItem\" :data-root-item-id=\"item.id\" :data-root-item-index=\"index\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"nav_right\">\n\t\t\t\t\t<view class=\"nav_right-content\">\n\t\t\t\t\t\t<scroll-view @scroll=\"scroll\" class=\"detail-list\" :scrollIntoView=\"scrollToViewId\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :show-scrollbar=\"false\">\n\t\t\t\t\t\t\t<view v-for=\"(detail, indexs) in datalist\" :key=\"indexs\" class=\"classification-detail-item\">\n\t\t\t\t\t\t\t\t<view class=\"head\" :data-id=\"detail.id\" :id=\"'detail-' + detail.id\">\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{detail.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"show-all\" @tap=\"gotoCatproductPage\" :data-id=\"detail.id\">查看全部<text class=\"iconfont iconjiantou\"></text></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"product-itemlist\">\n\t\t\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in detail.prolist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/pages/shop/product?id='+item.id\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-pic\">\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p5\" :style=\"{color:t('color2')}\" v-if=\"custom.product_show_sellpoint && item.sellpoint\"><text>{{item.sellpoint}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p2\" v-if=\"item.price_type != 1 || item.sell_price > 0\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:20rpx;padding-right:1px\">￥</text>{{item.sell_price}}\n                          <text v-if=\"item.price_show && item.price_show_text\" style=\"margin: 0 15rpx;font-size: 24rpx;font-weight: 400;\">{{item.price_show_text}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- 服务费 -->\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"!isNull(item.service_fee) && item.service_fee_switch && item.service_fee > 0\" class=\"t1-m\" :style=\"{color:t('color1')}\">+{{item.service_fee}}{{t('服务费')}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- 称重商品单价 -->\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"custom.product_weight && item.product_type==2 && item.unit_price\" class=\"t1-m\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t(约{{item.unit_price}}元/斤)\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n                      <!-- 商品处显示会员价 -->\n                      <view v-if=\"item.price_show && item.price_show == 1\" style=\"line-height: 46rpx;\">\n                        <text style=\"font-size:26rpx\">￥{{item.sell_putongprice}}</text>\n                      </view>\n                      <view v-if=\"item.priceshows && item.priceshows.length>0\">\n                        <view v-for=\"(item2,index2) in item.priceshows\" style=\"line-height: 46rpx;\">\n                          <text style=\"font-size:26rpx\">￥{{item2.sell_price}}</text>\n                          <text style=\"margin-left: 15rpx;font-size: 22rpx;font-weight: 400;\">{{item2.price_show_text}}</text>\n                        </view>\n                      </view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p6\" v-if=\"custom.product_show_fwlist && item.fwlist && item.fwlist.length>0\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p6-m\" :style=\"'background:rgba('+t('color2rgb')+',0.15);color:'+t('color2')+';'\" v-for=\"(fw,fwidx) in item.fwlist\" :key=\"fwidx\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{fw}}\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"item.limit_start>0\"><text style=\"overflow:hidden;\">{{item.limit_start}}件起售</text></view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p4\" v-if=\"!item.price_type\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" @click.stop=\"buydialogChange\" :data-proid=\"item.id\" :data-indexs='indexs'><text class=\"iconfont icon_gouwuche\"></text></view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<block v-if=\"productType == 4\">\n\t\t\t<block v-if=\"ggNum == 2\">\n\t\t\t\t<buydialog-pifa v-if=\"buydialogShow\" :proid=\"proid\" btntype=\"1\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart\"  :menuindex=\"menuindex\" :needaddcart=\"false\" />\n\t\t\t</block>\n\t\t\t<block v-else>\n\t\t\t\t<buydialog-pifa2 v-if=\"buydialogShow\" :proid=\"proid\" btntype=\"1\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart2\" :menuindex=\"menuindex\" :needaddcart=\"false\" />\n\t\t\t</block>\n\t\t</block>\n\t\t<block v-else>\n\t\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart\" :menuindex=\"menuindex\" btntype=\"1\" :needaddcart=\"false\"></buydialog>\n\t\t</block>\n\t\t<view style=\"height:auto;position:relative\">\n\t\t\t<view style=\"width:100%;height:100rpx\"></view>\n\t\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t<view class=\"cart_ico\" :style=\"{background:'linear-gradient(0deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"handleClickMask\"><image class=\"img\" :src=\"pre_url+'/static/img/cart.png'\" /><view class=\"cartnum\" :style=\"{background:t('color1')}\" v-if=\"cartList.total>0\">{{cartList.total}}</view></view>\n\t\t\t\t<view class=\"text1\">合计</view>\n\t\t\t\t<view class=\"text2 flex1\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx\">￥</text>{{cartList.totalprice}}</view>\n\t\t\t\t<view class=\"op\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"gopay\">去结算</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"cartListShow\" class=\"popup__container\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\"></view>\n\t\t\t<view class=\"popup__modal\" style=\"min-height:400rpx;padding:0\">\n\t\t\t\t<view class=\"popup__title\" style=\"border-bottom:1px solid #EFEFEF\">\n\t\t\t\t\t<text class=\"popup__title-text\" style=\"color:#323232;font-weight:bold;font-size:32rpx\">购物车</text>\n\t\t\t\t\t<view class=\"popup__close flex-y-center\" @tap.stop=\"clearShopCartFn\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/>清空</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\" style=\"padding:0\">\n\t\t\t\t\t<scroll-view scroll-y class=\"prolist\">\n\t\t\t\t\t\t<block v-for=\"(cart, index) in cartList.list\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"proitem\">\n\t\t\t\t\t\t\t\t<image :src=\"cart.guige.pic?cart.guige.pic:cart.product.pic\" class=\"pic flex0\"></image>\n\t\t\t\t\t\t\t\t<view class=\"con\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{cart.product.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"cart.guige.name!='默认规格'\">{{cart.guige.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f3\" style=\"color:#ff5555;margin-top:10rpx;font-size:28rpx\">￥{{cart.guige.sell_price}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"addnum\">\n\t\t\t\t\t\t\t\t\t<view class=\"minus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" @tap=\"addcart\" data-num=\"-1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"/></view>\n\t\t\t\t\t\t\t\t\t<text class=\"i\">{{cart.num}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"plus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\" @tap=\"addcart\" data-num=\"1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"/></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"!cartList.list.length\">\n\t\t\t\t\t\t\t<text class=\"nopro\">暂时没有商品喔~</text>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tcartListShow:false,\n\t\t\tbuydialogShow:false,\n\t\t\tharr:[],\n      datalist: [],\n\t\t\tcartList:{},\n\t\t\tproid:'',\n\t\t\ttotalprice:'0.00',\n      currentActiveIndex: 0,\n      animation: true,\n      scrollToViewId: \"\",\n\t\t\tbid:'',\n\t\t\tscrollState:true,\n\t\t\tproductType:'',\n\t\t\tggNum:'',\n\t\t\tcustom:{},\n\t\t\tmendianid:0,\n\t\t\tpre_url: app.globalData.pre_url,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.bid = this.opt.bid ? this.opt.bid  : '';\n\t\t//读全局缓存的地区信息\n\t\tvar locationCache =  app.getLocationCache();\n\t\tif(locationCache){\n\t\t\tif(locationCache.latitude){\n\t\t\t\tthis.latitude = locationCache.latitude\n\t\t\t\tthis.longitude = locationCache.longitude\n\t\t\t}\n\t\t\tif(locationCache.area){\n\t\t\t\tthis.area = locationCache.area\n\t\t\t}\n\t\t\tif(locationCache.mendian_id){\n\t\t\t\tthis.mendianid = locationCache.mendian_id\n\t\t\t}\n\t\t}\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : 0;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiShop/fastbuy', {bid:bid,mendian_id:that.mendianid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.datalist = res.data;\n\t\t\t\tthat.cartList = res.cartList;\n\t\t\t\tthat.custom = res.custom\n\n\t\t\t\t//计算每个高度\n\t\t\t\tvar harr = [];\n\t\t\t\tvar clientwidth = uni.getSystemInfoSync().windowWidth;\n\t\t\t\tvar datalist = res.data;\n\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\n\t\t\t\t\tvar child = datalist[i].prolist;\n\t\t\t\t\tharr.push(Math.ceil(child.length) * 200 / 750 * clientwidth);\n\t\t\t\t}\n\t\t\t\tthat.harr = harr;\n\n\t\t\t\tif(that.opt.cid){\n\t\t\t\t\tthat.scrollToViewId = 'detail-' + that.opt.cid;\n\t\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\n\t\t\t\t\t\tif(datalist[i].id == that.opt.cid) that.currentActiveIndex = i;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    clickRootItem: function (t) {\n\t\t\tthis.scrollState=false;\n      var e = t.currentTarget.dataset;\n      this.scrollToViewId = 'detail-' + e.rootItemId;\n      this.currentActiveIndex = e.rootItemIndex;\n\t\t\tsetTimeout(()=>{\n\t\t\t\tthis.scrollState=true;\n\t\t\t},500)\n    },\n\t\taddcart:function(e){\n\t\t\tvar that = this;\n\t\t\tvar ks = that.ks;\n\t\t\tvar num = e.currentTarget.dataset.num;\n\t\t\tvar proid = e.currentTarget.dataset.proid;\n\t\t\tvar ggid = e.currentTarget.dataset.ggid;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiShop/addcart', {proid: proid,ggid: ggid,num: num}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    //加入购物车弹窗后\n    afteraddcart: function (e) {\n\t\t\te.hasoption = false;\n\t\t\tconsole.log('111');\n      this.addcart({currentTarget:{dataset:e}});\n    },\n\t\t// 批发商品\n\t\tafteraddcart2:function(){\n\t\t\tlet that = this;\n\t\t\tsetTimeout(() => {\n\t\t\t\tthat.getdata();\n\t\t\t},300)\n\t\t},\n    clearShopCartFn: function () {\n      var that = this;\n      app.post(\"ApiShop/cartclear\", {bid:that.opt.bid}, function (res) {\n        that.getdata();\n      });\n    },\n    gopay: function () {\n      var cartList = this.cartList.list;\n      if (cartList.length == 0) {\n        app.alert('请先添加商品到购物车');\n        return;\n      }\n      var prodata = [];\n      for (var i = 0; i < cartList.length; i++) {\n        prodata.push(cartList[i].proid + ',' + cartList[i].ggid + ',' + cartList[i].num);\n      }\n      app.goto('/pagesB/shop/buy?frompage=fastbuy&prodata=' + prodata.join('-'));\n    },\n    gotoCatproductPage: function (t) {\n      var e = t.currentTarget.dataset;\n\t\t\tif(this.bid){\n\t\t\t\tapp.goto('/pages/shop/prolist?bid='+this.bid+'&cid2=' + e.id);\n\t\t\t}else{\n\t\t\t\tapp.goto('/pages/shop/prolist?cid=' + e.id);\n\t\t\t}\n    },\n    scroll: function (e) {\n\t\tif(this.scrollState){\n\t\t\tvar scrollTop = e.detail.scrollTop;\n\t\t\tvar harr = this.harr;\n\t\t\tvar countH = 0;\n\t\t\tfor (var i = 0; i < harr.length; i++) {\n\t\t\t  if (scrollTop >= countH && scrollTop < countH + harr[i]) {\n\t\t\t    this.currentActiveIndex = i;\n\t\t\t    break;\n\t\t\t  }\n\t\t\t  countH += harr[i];\n\t\t\t}\n\t\t}\n    },\n\t\tbuydialogChange: function (e) {\n\t\t\tif(!this.buydialogShow){\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid;\n\t\t\t\tlet index = e.currentTarget.dataset.indexs;\n\t\t\t\tthis.datalist[index].prolist.forEach(item => {\n\t\t\t\t\tif(item.id == this.proid){\n\t\t\t\t\t\tthis.productType = item.product_type;\n\t\t\t\t\t\tthis.ggNum = item.gg_num;\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t},\n\t\thandleClickMask:function(){\n\t\t\tthis.cartListShow = !this.cartListShow;\n\t\t}\n  }\n};\n</script>\n<style>\npage {position: relative;width: 100%;height: 100%;}\n.container{height:100%;overflow:hidden}\n.view-show{background-color: white;line-height: 1;width: 100%;height: 100%;}\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\n\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\n.nav_left .nav_left_items{line-height:50rpx;color:#333333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\n.nav_left .nav_left_items.active{background: #fff;color:#333333;font-size:30rpx;font-weight:bold}\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\n.nav_left .nav_left_items.active .before{display:block}\n\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 20rpx 20rpx}\n.nav_right-content{background: #ffffff;padding:20rpx;height:100%;position:relative}\n.detail-list {height:100%;overflow:scroll}\n.classification-detail-item {width: 100%;overflow: visible;background:#fff}\n.classification-detail-item .head {height: 82rpx;width: 100%;display: flex;align-items:center;justify-content:space-between;}\n.classification-detail-item .head .txt {color:#222222;font-weight:bold;font-size:30rpx;}\n.classification-detail-item .head .show-all {font-size: 26rpx;color:#949494;display:flex;align-items:center}\n\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;font-weight:bold;}\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.product-itemlist .product-info .p2 .t1-m {font-size: 26rpx;font-weight: bold;padding-left: 6rpx;}\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999;margin-right:10rpx}\n.product-itemlist .product-info .p3-2{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#777}\n.product-itemlist .product-info .p4{width:56rpx;height:56rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:32rpx;height:56rpx;line-height:56rpx}\n.product-itemlist .product-info .p5{font-size:24rpx;font-weight: bold;margin: 6rpx 0;}\n.product-itemlist .product-info .p6{font-size:24rpx;display: flex;flex-wrap: wrap;margin-top: 6rpx;}\n.product-itemlist .product-info .p6-m{text-align: center;padding:6rpx 10rpx;border-radius: 6rpx;margin: 6rpx;}\n\n.prolist {max-height: 620rpx;min-height: 320rpx;overflow: hidden;padding:0rpx 20rpx;font-size: 28rpx;border-bottom: 1px solid #e6e6e6;}\n.prolist .nopro {text-align: center;font-size: 26rpx;display: block;margin: 80rpx auto;}\n.prolist .proitem{position: relative;padding:10rpx 0;display:flex;border-bottom:1px solid #eee}\n.prolist .proitem .pic{width: 120rpx;height: 120rpx;margin-right: 20rpx;}\n.prolist .proitem .con{padding-right:180rpx;padding-top:10rpx}\n.prolist .proitem .con .f1{color:#323232;font-size:26rpx;line-height:32rpx;margin-bottom: 10rpx;margin-top: -6rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\n.prolist .proitem .con .f2{font-size: 24rpx;line-height:28rpx;color: #999;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;}\n.prolist .proitem .addnum {position: absolute;right: 20rpx;bottom:50rpx;font-size: 30rpx;color: #666;width: auto;display:flex;align-items:center}\n.prolist .proitem .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.prolist .proitem .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.prolist .proitem .addnum .img{width:24rpx;height:24rpx}\n.prolist .proitem .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\n.prolist .tips {font-size: 22rpx;color: #666;text-align: center;line-height: 56rpx;background: #f5f5f5;}\n\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\n.footer .cart_ico{width:64rpx;height:64rpx;border-radius: 10rpx;display:flex;align-items:center;justify-content:center;position:relative}\n.footer .cart_ico .img{width:36rpx;height:36rpx;}\n.footer .cart_ico .cartnum{position:absolute;top:-17rpx;right:-17rpx;width:34rpx;height:34rpx;border:1px solid #fff;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden;font-size:20rpx;font-weight:bold;color:#fff}\n.footer .text1 {height: 100rpx;line-height: 100rpx;color:#555555;font-weight:bold;font-size: 30rpx;margin-left:40rpx;margin-right:10rpx}\n.footer .text2 {font-size: 32rpx;font-weight:bold}\n.footer .op{width: 200rpx;height: 72rpx;line-height:72rpx;border-radius: 36rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center}\n::-webkit-scrollbar{width: 0;height: 0;color: transparent;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fastbuy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839380755\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}