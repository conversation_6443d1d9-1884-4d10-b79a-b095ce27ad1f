{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/index.vue?8b69", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/index.vue?caba", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/index.vue?8452", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/index.vue?b6e3", "uni-app:///pages/index/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/index.vue?1df2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/index.vue?2dd6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "platform", "homeNavigationCustom", "navigationBarBackgroundColor", "navigationBarTextStyle", "statusBarHeight", "navigationMenu", "id", "pageinfo", "pagecontent", "sysset", "title", "oglist", "guanggaopic", "guang<PERSON><PERSON>l", "guanggaotype", "guanggaoparam", "copyright", "copyright_link", "latitude", "longitude", "area", "screenWidth", "business", "xixie", "xdata", "display_buy", "cartnum", "cartprice", "code", "agent_juli", "mid", "showlevel", "show_location", "curent_address", "arealist", "show_nearbyarea", "ischangeaddress", "nearbyplacelist", "myaddresslist", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placekeyword", "suggestionplacelist", "show_mendian", "mendianid", "mendian", "mendianlist", "mendianindex", "isshowmendianmodal", "needRefresh<PERSON><PERSON><PERSON><PERSON>", "headericonlist", "cacheExpireTime", "locationCache", "trid", "show_indextip", "indextipstatus", "mendian_upgrade", "mendian_change", "mendian_show_address", "navigationHieght", "ggsx", "onLoad", "console", "that", "uni", "onShow", "onPullDownRefresh", "onPageScroll", "methods", "wxNavigationBarMenu", "toBusiness", "app", "getdata", "mendian_isinit", "select_bid", "pid", "mendian_id", "mode", "showsubqrcode", "closesubqrcode", "changePopupAddress", "setMendianData", "checkLocation", "checkAreaByShowlevel", "initCityAreaList", "url", "method", "header", "success", "item2", "newchildren", "item1", "newlist", "areachange", "area_name", "showarea", "address", "closeNearbyBox", "showNearbyBox", "nearbylist", "changeAddress", "add<PERSON>y<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "cancelChangeAddress", "refreshAddress", "showAllAddress", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chooseSuggestionAddress", "refreshNearbyPlace", "placekeywordInput", "searchPlace", "region", "keyword", "showMendianModal", "hideMendianModal", "<PERSON><PERSON><PERSON><PERSON>", "pagescroll", "closeindextip", "callphone", "phoneNumber", "fail", "openLocation", "name", "scale"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgUv1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;IACA;MACA;IACA;MACA;IACA;IACA;;IAYA;MACA;MACAC;QACAC;UACAD;QACA;MACA;IACA;EACA;EACAE;IACAH;IACA;IACA;MACA;MACAC;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;EACA;EACAG;IACA;MACA;IACA;EACA;EACAC;IACAH;EACA;EACAI;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAC;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAD;MACA;QACA;MACA;MAEA;QACAhE;MACA;MACAwD;MACAA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;QACA;UACAU;QACA;MACA;MACAF;QACAhE;QACAY;QACAC;QACAC;QACAqD;QACAC;QACAC;QACAH;QACApB;QACAwB;MACA;QACAd;QACA;UACA;UACAQ,0FACA;UACA;QACA;QACA;UACA;UACAR;UAEA,iBACAA;UAEAA;UACA;YACAA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAC;YACArD;UACA;UACA;YACAoD;UACA;UAEA;YACAA;YACAA;YACAA;YACAA;YACAA;YACA;cACAA;cACAA;cACAA;cACAA;cACAA;cACAA;cACA;gBACA;gBACAA;cACA;gBACAA;cACA;cACA;gBACAA;gBACAA;gBACAA;gBACAA;cACA;cACAQ;YACA;UACA;YACAA;YACAA;UACA;UACA;UACA;YACA;cACA;cACAR;cACAA;YACA;cACA;cACAA;YACA;UACA;UAEAA;UACA;YACAQ;cACAR;cACAA;cACA;gBACA;gBACAA;cACA;gBACAA;cACA;YACA;cACAD;YACA;UACA;UACA;YACAC;YACA,sBACAQ;YAEA;YACAR;UACA;YACA;YACAA;UACA;UAEA;YACA,4CACAA,+DACA;cACA;cACAA;YACA;UACA;UAEA;YACAA;UACA;UAEAA;UACAA;QACA;UACA;YACAQ;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAO;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAX;UACAR;UACAA;UACA;UACAQ;YAAApD;YAAAC;UAAA;YACA;cACAgC;cACAA;cACAA;cACAA;cACAA;cACA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;kBACAA;kBACAA;gBACA;gBACAW;gBACAA;gBACAQ;gBACAR;cACA;gBACAX;gBACAA;gBACAW;gBACAA;gBACAQ;gBACAR;gBACAA;cACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;YACA9D;UACA;YACAA;UACA;YACAA;UACA;UACA0C;UACAQ;QACA;MACA;IACA;IACAa;MACA;QACA;MACA;MACA;MACA;MACA;QACApB;UACAqB;UACA1F;UACA2F;UACAC;YAAA;UAAA;UACAC;YACA;cACA;cACA;cACA;gBACA;gBACA;kBACA;kBACA;kBACA;oBACA;oBACAC;oBACAC;kBACA;kBACAC;gBACA;kBACAA;gBACA;;gBACAC;cACA;cACA7B;YACA;cACAA;YACA;UACA;QACA;MACA;IACA;IACA8B;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA;MACAhC;MACAA;MACA;MACA;MACAX;MACAA;MACA;QACA;QACAmB;UACAyB;QACA;UACAjC;UACA;YACAA;YACAA;YACAX;YACAA;YACAmB;YACAR;UACA;YACAQ;UACA;QACA;MACA;IACA;IACA0B;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACApC;QACAA;MACA;IACA;IACAqC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA9B;IACA;IACA+B;MACA;QACA;MACA;MACA;MACAvC;MACAQ;QACAgC;MACA;QACAxC;QACA;UACAA;QACA;MACA;IACA;IACAyC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA1C;MACAQ;QACA;QACA;QACA;QACAA;UACApD;UACAC;UACAmF;QACA;UACAxC;UACA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACAX;YACAA;YACAA;YACAA;YACAA;YACAmB;YACAR;YACAA;UACA;QACA;MACA;QACAD;MACA;IACA;IACA4C;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA5C;MACAA;MACAA;MAEA;MACAX;MACAA;MACAA;MACAA;MACAmB;MAEAR;MACAA;MACAA;IACA;IACA6C;MACA;QACA;MACA;MACA;MACA;MACA;MACA7C;MACAA;MACAA;MACA;MACAX;MACAA;MACAA;MACAA;MACAmB;MACAR;MACAA;MACAA;IACA;IACA8C;MACA;QACA;MACA;MACA;MACA;MACA;MACA9C;MACAA;MACAA;MACA;MACAX;MACAA;MACAA;MACAA;MACAmB;MACAR;MACAA;MACAA;IACA;IACA+C;MAAA;MAAA;MACA;QACA;MACA;MACA;MACA;QACA3F;QACAC;MACA;MACA;QACA2C;QACAQ;UACApD;UACAC;UACAmF;QACA;UACAxC;UACA;YACA;YACAA;YACAQ;UACA;QACA;MACA;IACA;IACAwC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;QACAjD;QACA;MACA;MACA;MAEA;MACA;QACA;UACA;UACA;YACAkD;UACA;YACAA;UACA;QACA;MACA;MACAlD;MACAQ;QACApD;QACAC;QACA6F;QACAC;MACA;QACAnD;QACA;UACAA;QACA;MACA;IACA;IACA;;IAEA;IACAoD;MACA;QACA;MACA;MACA;MACA;QACApD;MACA;QACAQ;UACApD;UACAC;QACA;UACA2C;UACA;YACAA;YACAA;UACA;YACAQ;UACA;QACA;MACA;IACA;IACA6C;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACAtD;MACAA;MACAA;MACAA;MACAQ;MACAA;MACAA;MACAR;MACAA;IACA;IACA;IACAuD;MACAtD;IACA;IACAuD;MACA;MACAxD;MACAQ;IACA;IACAiD;MACA;MACAxD;QACAyD;QACAC,uBACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA3D;QACA7C;QACAC;QACAwG;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1mCA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    dpGuanggao: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-guanggao/dp-guanggao\" */ \"@/components/dp-guanggao/dp-guanggao.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.show_nearbyarea && _vm.mendian_upgrade && _vm.show_mendian == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m1 =\n    !_vm.show_nearbyarea &&\n    _vm.sysset.agent_card == 1 &&\n    _vm.sysset.agent_card_info\n      ? _vm.t(\"color2\")\n      : null\n  var m2 = !_vm.show_nearbyarea && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m3 = !_vm.show_nearbyarea && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = !_vm.show_nearbyarea ? _vm.oglist && _vm.oglist.length > 0 : null\n  var m4 =\n    _vm.show_location == 1 &&\n    _vm.sysset.loc_area_type == 1 &&\n    _vm.show_nearbyarea\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.show_location == 1 &&\n    _vm.sysset.loc_area_type == 1 &&\n    _vm.show_nearbyarea\n      ? _vm.t(\"color1\")\n      : null\n  var g1 =\n    _vm.show_location == 1 &&\n    _vm.sysset.loc_area_type == 1 &&\n    _vm.show_nearbyarea\n      ? _vm.suggestionplacelist.length\n      : null\n  var g2 =\n    _vm.show_location == 1 &&\n    _vm.sysset.loc_area_type == 1 &&\n    _vm.show_nearbyarea\n      ? _vm.myaddresslist.length\n      : null\n  var m6 =\n    _vm.show_location == 1 &&\n    _vm.sysset.loc_area_type == 1 &&\n    _vm.show_nearbyarea\n      ? _vm.t(\"color1\")\n      : null\n  var l0 =\n    _vm.show_mendian && _vm.isshowmendianmodal\n      ? _vm.__map(_vm.mendianlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m7 = item.id == _vm.mendian.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m7: m7,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        m4: m4,\n        m5: m5,\n        g1: g1,\n        g2: g2,\n        m6: m6,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<scroll-view :scroll-y=\"sysset.mode==3?(isshowmendianmodal?false:true):true\" class=\"pageContainer\" :style=\"{backgroundColor:pageinfo.bgcolor}\" @scroll=\"pagescroll\">\r\n\t\t<block v-if=\"!show_nearbyarea\">\r\n\t\t\t<!-- ！！！！！其他内容请放在自定义导航后面！！！！ -->\r\n\t\t\t<!-- #ifdef MP-WEIXIN  -->\r\n\t\t\t<block v-if=\"platform=='wx' && (homeNavigationCustom>1)\">\r\n\t\t\t\t<view class=\"navigation\"\r\n\t\t\t\t\t:style=\"{'background':navigationBarBackgroundColor}\">\r\n\t\t\t\t\t<!-- <view :style=\"{height:statusBarHeight+'px'}\"></view> -->\r\n\t\t\t\t\t<view class='navcontent' :style=\"{color:navigationBarTextStyle,marginTop:navigationMenu.top+'px',width:(navigationMenu.left-5)+'px'}\">\r\n\t\t\t\t\t\t<!-- 标题+搜索框 -->\r\n\t\t\t\t\t\t<view class=\"header-location-top\" :style=\"{height:navigationMenu.height+'px',background:navigationBarBackgroundColor}\">\r\n\t\t\t\t\t\t\t<block v-if=\"homeNavigationCustom==2\">\r\n\t\t\t\t\t\t\t\t<view class=\"topinfo\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"topinfoicon\" :src=\"sysset.logo\" />\r\n\t\t\t\t\t\t\t\t\t<view class=\"topinfotxt\" :style=\"{color:navigationBarTextStyle}\">{{sysset.name}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"topsearch\" :style=\"{width:(screenWidth-210)+'px'}\" @tap=\"goto\"\r\n\t\t\t\t\t\t\t\t\tdata-url=\"/pages/shop/search\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/search.png'\" />\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx;color:#999\">搜索感兴趣的商品</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 定位模式门店模式 显示定位或门店 -->\r\n\t\t\t\t\t\t\t<block v-if=\"(homeNavigationCustom==3 || homeNavigationCustom==5 || homeNavigationCustom==7) && show_location==1\">\r\n\t\t\t\t\t\t\t\t<!-- 显示定位：城市|地标 Start -->\r\n\t\t\t\t\t\t\t\t<!-- 当前城市 -->\r\n\t\t\t\t\t\t\t\t<view v-if=\"sysset.loc_area_type==0 && curent_address\" :class=\"homeNavigationCustom>3?'header-location-weixin-fixedwidth':'header-location-weixin'\">\r\n\t\t\t\t\t\t\t\t\t<!-- <image class=\"header-icon\" :src=\"pre_url+'/static/img/location/address-dark.png'\"> -->\r\n\t\t\t\t\t\t\t\t\t<uni-data-picker class=\"header-address header-area-picker\" :localdata=\"arealist\" popup-title=\"地区\" @change=\"areachange\"  :placeholder=\"'地区'\">\r\n\t\t\t\t\t\t\t\t\t\t<view>{{curent_address?curent_address:'请选择定位'}}</view>\r\n\t\t\t\t\t\t\t\t\t</uni-data-picker>\r\n\t\t\t\t\t\t\t\t\t<image class=\"header-more\" :src=\"pre_url+'/static/img/location/down-'+navigationBarTextStyle+'.png'\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- 当前地址（商圈地址等） -->\r\n\t\t\t\t\t\t\t\t<view v-if=\"sysset.loc_area_type==1 && curent_address\" :class=\"homeNavigationCustom>3?'header-location-weixin-fixedwidth':'header-location-weixin'\">\r\n\t\t\t\t\t\t\t\t\t<!-- <image class=\"header-icon\" :src=\"pre_url+'/static/img/location/address-dark.png'\"> -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center\" @tap=\"showNearbyBox\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"header-address\">{{curent_address?curent_address:'请选择定位'}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"header-more\" :src=\"pre_url+'/static/img/location/down-'+navigationBarTextStyle+'.png'\">\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"header-location-title\" v-if=\"homeNavigationCustom==5\">{{sysset.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"header-location-search\" :style=\"{height:navigationMenu.height+'px'}\" v-if=\"homeNavigationCustom==7\" @tap=\"goto\"\r\n\t\t\t\t\t\t\t\t\tdata-url=\"/pages/shop/search\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/search.png'\" />\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx;color:#999\">搜索感兴趣的商品</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<!-- 显示定位：城市|地标 End -->\r\n\t\t\t\t\t\t\t<!-- 显示门店 Start -->\r\n\t\t\t\t\t\t\t<block v-if=\"(homeNavigationCustom==4 || homeNavigationCustom==6 || homeNavigationCustom==8) && show_mendian==1\">\r\n\t\t\t\t\t\t\t\t<view :class=\"homeNavigationCustom>4?'header-location-weixin-fixedwidth':'header-location-weixin'\">\r\n\t\t\t\t\t\t\t\t\t<!-- <image class=\"header-icon\" :src=\"pre_url+'/static/img/location/mendian.png'\"> -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center header-location-f1\" @tap=\"showMendianModal\" >\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"header-address\">{{locationCache.mendian_name?locationCache.mendian_name:'请选择门店'}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"header-more\" :src=\"pre_url+'/static/img/location/down-'+navigationBarTextStyle+'.png'\">\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"header-location-title\" v-if=\"homeNavigationCustom==6\">{{sysset.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"header-location-search\" :style=\"{height:navigationMenu.height+'px'}\" v-if=\"homeNavigationCustom==8\" @tap=\"goto\"\r\n\t\t\t\t\t\t\t\t\tdata-url=\"/pages/shop/search\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/search.png'\" />\r\n\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx;color:#999\">搜索感兴趣的商品</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<!-- 显示门店 Start -->\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\t\t\t\t\r\n\t\t\t\t<view style=\"width:100%;\" :style=\"{height:(44+statusBarHeight)+'px'}\"></view>\r\n\t\t\t</block>\r\n\t\t\t<!-- 公众号关注组件 -->\r\n\t\t\t<block v-if=\"sysset.official_account_status==1\">\r\n\t\t\t\t<official-account></official-account>\r\n\t\t\t</block>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- ！！！！！其他内容请放在自定义导航后面！！！！ -->\r\n\t\t\r\n\t\t<view class=\"mendianupbg\" v-if=\"mendian_upgrade && show_mendian==1\" :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\"></view>\r\n\t\t<view class=\"mendianup\" v-if=\"mendian_upgrade && show_mendian==1\">\r\n\t\t\t<view class=\"left\" >\r\n\t\t\t\t<view v-if=\"mendian_change\" class=\"f1\"  @tap=\"goto\" data-url='/pagesB/mendianup/list' >{{locationCache.mendian_name}}\r\n\t\t\t\t\t\t<view class=\"t1\">切换<text class=\"iconfont iconjiantou\" style=\"font-size: 40rpx;\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"f1\">{{locationCache.mendian_name}}</view>\r\n\t\t\t\t<view class=\"f2\" v-if=\"mendian_show_address\" @tap=\"openLocation\" :data-latitude=\"mendian.latitude\" :data-longitude=\"mendian.longitude\" :data-address=\"locationCache.mendian_address\">\r\n\t\t\t\t\t<view>{{locationCache.mendian_address}}</view>\r\n\t\t\t  </view>\r\n\t\t\t\t<view class=\"f2\" v-if=\"mendian_show_address && mendian.tel\" @tap.stop=\"callphone\" :data-phone=\"mendian.tel\">\r\n\t\t\t\t\t<view><text style=\"margin-right:10rpx\">{{mendian.tel}}</text><image :src=\"pre_url+'/static/img/telwhite.png'\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"right\">\r\n\t\t\t\t<view class=\"f1\"><image :src=\"locationCache.headimg?locationCache.headimg:locationCache.mendian_pic\" ></view>\r\n\t\t\t\t<view class=\"f2\">{{locationCache.mendian_name}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"height: 150rpx;\" v-if=\"mendian_upgrade && show_mendian==1\"></view>\r\n\t\t\r\n\t\t<!-- 代理卡片 -->\r\n\t\t\t<block v-if=\"sysset.agent_card == 1 && sysset.agent_card_info\">\r\n\t\t\t\t<view style=\"height: 10rpx;\"></view>\r\n\t\t\t\t<view class=\"agent-card\">\r\n\t\t\t\t\t<view class=\"flex-y-center row1\">\r\n\t\t\t\t\t\t<image class=\"logo\" :src=\"sysset.agent_card_info.logo\" />\r\n\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"title limitText\">{{sysset.agent_card_info.shopname}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex right\" @tap=\"showMap\" :data-name=\"sysset.agent_card_info.shopname\" :data-address=\"sysset.agent_card_info.address\" :data-longitude=\"sysset.agent_card_info.longitude\" :data-latitude=\"sysset.agent_card_info.latitude\"><image class=\"img\" :src=\"pre_url+'/static/img/b_addr.png'\"></image>导航到店{{agent_juli}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"limitText grey-text\">{{sysset.agent_card_info.address}}</view>\r\n\t\t\t\t\t\t\t<view class=\"grey-text flex-y-center\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/my.png'\"></image>\r\n\t\t\t\t\t\t\t\t<view>{{sysset.agent_card_info.name}}</view>\r\n\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/tel.png'\" style=\"margin-left: 30rpx;\"></image>\r\n\t\t\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'tel::'+sysset.agent_card_info.tel\"\r\n\t\t\t\t\t\t\t\t\tstyle=\"position: relative;\">{{sysset.agent_card_info.tel}}\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"goto\" :data-url=\"'tel::'+sysset.agent_card_info.tel\">拨打</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-y-center flex-x-center agent-card-b\" :style=\"{background:t('color2')}\">\r\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pagesExt/agent/card'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shop.png'\"></image>店铺信息\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/commission/poster'\">\r\n\t\t\t\t\t\t\t<image class=\"img img2\" :src=\"pre_url+'/static/img/card.png'\"></image>店铺海报\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<!-- 代理卡片 end-->\r\n\r\n\t\t\t<block v-if=\"sysset.mode == 1\">\r\n\t\t\t\t<view class=\"header\" :style=\"{'background':navigationBarBackgroundColor}\">\r\n\t\t\t\t\t<view class=\"header_title flex-y-center flex-bt\">\r\n\t\t\t\t\t\t<view class=\"flex-y-center \" @click=\"toBusiness\" :data-url=\"'/pagesExt/business/clist2'\">\r\n\t\t\t\t\t\t\t{{sysset.name}}\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"header_detail\" ></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"header_address flex\">\r\n\t\t\t\t\t\t<view class=\"flex1\">{{sysset.address}}</view>\r\n\t\t\t\t\t\t<text style=\"margin-left: 20rpx;flex-shrink: 0;\">距离 {{sysset.juli}} km</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"topbannerbg\" :style=\"sysset.banner_show && business.pic?'background:url('+business.pic+') center no-repeat;background-size:cover;':''\"></view>\r\n\t\t\t</block>\r\n\r\n\t\t\t<block v-if=\"sysset.showgzts\">\r\n\t\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\r\n\t\t\t\t<view class=\"follow_topbar\">\r\n\t\t\t\t\t<view class=\"headimg\">\r\n\t\t\t\t\t\t<image :src=\"sysset.logo\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\r\n\t\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\r\n\t\t\t\t\t<view class=\"qrcodebox\">\r\n\t\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\" />\r\n\t\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\r\n\t\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t<dp :pagecontent=\"pagecontent\" :menuindex=\"menuindex\" :latitude=\"latitude\" :longitude=\"longitude\" @getdata=\"getdata\" :navigationHieght='navigationHieght'></dp>\r\n\r\n\t\t\t<view :class=\"sysset.ddbb_position == 'bottom' ? 'bobaobox_bottom' : 'bobaobox'\" v-if=\"oglist && oglist.length>0\">\r\n\t\t\t\t<swiper style=\"position:relative;height:54rpx;width:450rpx;\" autoplay=\"true\" :interval=\"5000\"\r\n\t\t\t\t\tvertical=\"true\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in oglist\" :key=\"index\" @tap=\"goto\" :data-url=\"item.tourl\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image :src=\"item.headimg\"\r\n\t\t\t\t\t\t\tstyle=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view style=\"width:400rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">\r\n\t\t\t\t\t\t\t<text style=\"padding-right:2px\">{{item.nickname}}</text>\r\n\t\t\t\t\t\t\t<text style=\"padding-right:4px\">{{item.showtime}}</text>\r\n\t\t\t\t\t\t\t<text style=\"padding-right:2px\" v-if=\"item.type=='collage' && item.buytype=='2'\">发起拼团</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"item.type =='business'\">入驻</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"item.type == 'maidan'\"></text>\r\n\t\t\t\t\t\t\t<text v-else>购买了</text>\r\n\t\t\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t<view v-if=\"copyright!=''\" class=\"copyright\" @tap=\"goto\" :data-url=\"copyright_link\">{{copyright}}</view>\r\n\t\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n    <block v-if=\"ggsx\">\r\n\t\t\t<dp-guanggao :guanggaopic=\"guanggaopic\" :guanggaourl=\"guanggaourl\" :guanggaotype=\"guanggaotype\" :param=\"guanggaoparam\" ></dp-guanggao>\r\n    </block>\r\n\t\t</block>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t\r\n\t\t<!-- 附近商圈地址 -->\r\n\t\t<!-- #ifdef MP-WEIXIN  -->\r\n\t\t<block v-if=\"show_location==1 && sysset.loc_area_type==1 && show_nearbyarea\">\r\n\t\t\t<view :style=\"{height:(34+statusBarHeight)+'px'}\"></view>\r\n\t\t\t<view class=\"header-nearby-box\">\r\n\t\t\t\t<view class=\"header-nearby-body\">\r\n\t\t\t\t\t<view class=\"header-nearby-search\">\r\n\t\t\t\t\t\t<view class=\"header-nearby-close\" @tap=\"closeNearbyBox\"><image :src=\"pre_url+'/static/img/location/close-dark.png'\"></image></view>\r\n\t\t\t\t\t\t<view class=\"header-nearby-input\" >\r\n\t\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"商圈/大厦/住宅\" placeholder-style=\"font-size:26rpx\" :value=\"placekeyword\" @input=\"placekeywordInput\" @confirm=\"searchPlace\"/>\r\n\t\t\t\t\t\t\t<button class=\"searchbtn\" :style=\"{borderColor:t('color1'),color:'#FFF',backgroundColor:t('color1')}\" @tap=\"searchPlace\">搜索</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"suggestion-box\" v-if=\"suggestionplacelist.length>0\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in suggestionplacelist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"suggestion-place\" @tap=\"chooseSuggestionAddress\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"s-title\">{{item.title}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"s-info flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"s-area\">{{item.city}} {{item.district}} </text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"s-address\">{{item.address}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"header-nearby-content flex-bt\">\r\n\t\t\t\t\t\t<view>已选：{{curent_address}}</view>\r\n\t\t\t\t\t\t<view class=\"flex-xy-center\" @tap=\"refreshAddress\">\r\n\t\t\t\t\t\t\t<image class=\"header-nearby-imgicon\" :src=\"pre_url+'/static/img/location/location-dark.png'\">\r\n\t\t\t\t\t\t\t<text class=\"header-nearby-tip\">重新定位</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"header-nearby-content\" style=\"margin-top: 20rpx;\">\r\n\t\t\t\t\t\t<view class=\"header-nearby-title flex-y-center\">\r\n\t\t\t\t\t\t\t<image class=\"header-nearby-imgicon\" :src=\"pre_url+'/static/img/location/home-dark.png'\"></image>\r\n\t\t\t\t\t\t\t<text>我的地址</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in myaddresslist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"header-nearby-info\" v-if=\"index>3?(isshowalladdress?1==1:1==2):1==1\" @tap=\"chooseMyAddress\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"header-nearby-txt\">{{item.name}} {{item.tel}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<view class=\"header-nearby-all flex-y-center\" @tap=\"showAllAddress\">\r\n\t\t\t\t\t\t\t<block v-if=\"myaddresslist.length>0\">\r\n\t\t\t\t\t\t\t\t<text>{{isshowalladdress?'收起全部地址':'展开更多地址'}} </text><image :src=\"pre_url+'/static/img/location/'+(isshowalladdress?'up-grey.png':'down-grey.png')\"></image>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<text v-else>-暂无地址-</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 附近地址 -->\r\n\t\t\t\t\t<view class=\"header-nearby-content\" style=\"margin-top: 20rpx;\">\r\n\t\t\t\t\t\t<view class=\"header-nearby-title flex-y-center\">\r\n\t\t\t\t\t\t\t<image class=\"header-nearby-imgicon\" :src=\"pre_url+'/static/img/location/address-dark.png'\"></image>\r\n\t\t\t\t\t\t\t<text>附近地址</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in nearbyplacelist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"header-nearby-info\"  @tap=\"chooseNearbyAddress\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"header-nearby-txt\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"header-location-bottom flex-xy-center\" :style=\"{color:t('color1')}\" @tap=\"addMyAddress\">\r\n\t\t\t\t\t<text class=\"location-add-address\">+</text><text style=\"padding-top: 10rpx;\">新增收货地址</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<!-- 附近商圈地址 -->\r\n\t\t<!-- 门店选择start -->\r\n\t\t<block v-if=\"show_mendian\">\r\n\t\t\t<view class=\"popup__container popup_mendian\" v-if=\"isshowmendianmodal\" style=\"z-index: 999999;\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideMendianModal\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择门店</text>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideMendianModal\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in mendianlist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"mendian-info\" @tap=\"changeMendian\" :data-index=\"index\" :data-id=\"item.id\" :style=\"{background:(item.id==mendian.id?'rgba('+t('color1rgb')+',0.1)':'')}\">\r\n\t\t\t\t\t\t\t\t<view class=\"b1\"><image :src=\"item.pic\"></image></view>\r\n\t\t\t\t\t\t\t\t<view class=\"b2\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t2 flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"mendian-distance\">{{item.distance}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.address || item.area\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"line\" v-if=\"item.distance\"> </view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"mendian-address\"> {{item.address?item.address:item.area}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<!-- 门店选择end -->\r\n\t\t<!-- #endif  -->\r\n    \r\n    <block v-if=\"show_indextip && !indextipstatus\">\r\n    <view  @tap=\"closeindextip\" style=\"position: fixed;right: 0rpx;top: 0rpx;width: 100%;height: 100%;background-color: #000;opacity: 0.45;z-index: 998;\"></view>\r\n    <image @tap=\"closeindextip\" :src=\"pre_url+'/static/img/indextip.png'\" style=\"position: fixed;right: 20rpx;top: 0rpx;width: 360rpx;height: 424rpx;z-index: 999;\"></image>\r\n    </block>\r\n\t\t<wxxieyi></wxxieyi>\r\n\t</scroll-view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tplatform: app.globalData.platform,\r\n\t\t\t\thomeNavigationCustom: app.globalData.homeNavigationCustom,\r\n\t\t\t\tnavigationBarBackgroundColor: app.globalData.navigationBarBackgroundColor,\r\n\t\t\t\tnavigationBarTextStyle: app.globalData.navigationBarTextStyle,\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tnavigationMenu:{},\r\n\t\t\t\tid: 0,\r\n\t\t\t\tpageinfo: [],\r\n\t\t\t\tpagecontent: [],\r\n\t\t\t\tsysset: {},\r\n\t\t\t\ttitle: \"\",\r\n\t\t\t\toglist: [],\r\n\t\t\t\tguanggaopic: \"\",\r\n\t\t\t\tguanggaourl: \"\",\r\n\t\t\t\tguanggaotype: 1,\r\n\t\t\t\tguanggaoparam:{},\r\n\t\t\t\tcopyright: '',\r\n\t\t\t\tcopyright_link:'',\r\n\t\t\t\tlatitude: '',\r\n\t\t\t\tlongitude: '',\r\n\t\t\t\tarea:'',\r\n\r\n\t\t\t\tscreenWidth: 375,\r\n\t\t\t\tbusiness: [],  \r\n\t\t\t\txixie:false,\r\n\t\t\t\txdata:'',\r\n\t\t\t\tdisplay_buy:'',\r\n\t\t\t\tcartnum:0,\r\n\t\t\t\tcartprice:0,\r\n\t\t\t\tcode:'',\r\n\t\t\t\tagent_juli:'',\r\n\t\t\t\t\r\n\t\t\t\t//定位模式\r\n\t\t\t\tmid:app.globalData.mid,\r\n\t\t\t\tshowlevel:2,\r\n\t\t\t\tshow_location:0,\r\n\t\t\t\tcurent_address:'',//当前位置: 城市或者收货地址\r\n\t\t\t\tarealist:[],\r\n\t\t\t\tshow_nearbyarea:false,\r\n\t\t\t\tischangeaddress:false,\r\n\t\t\t\tnearbyplacelist:[],\r\n\t\t\t\tmyaddresslist:[],\r\n\t\t\t\tisshowalladdress:false,\r\n\t\t\t\tplacekeyword:'',\r\n\t\t\t\tsuggestionplacelist:[],\r\n\t\t\t\t\r\n\t\t\t\t//门店模式 显示最近的一个门店\r\n\t\t\t\tshow_mendian:0,\r\n\t\t\t\tmendianid:0,\r\n\t\t\t\tmendian:{},\r\n\t\t\t\tmendianlist:[],\r\n\t\t\t\tmendianindex:-1,\r\n\t\t\t\tisshowmendianmodal:false,\r\n\t\t\t\tneedRefreshMyaddress:false,\r\n\t\t\t\theadericonlist:[],\r\n\t\t\t\tcacheExpireTime:10,//缓存过期时间10分钟\r\n\t\t\t\tlocationCache:{},\r\n        trid:0,\r\n        show_indextip:false,\r\n        indextipstatus:false,\r\n\t\t\t\t\r\n\t\t\t\tmendian_upgrade:false,\r\n\t\t\t\tmendian_change:true,\r\n\t\t\t\tmendian_show_address:false,\r\n\t\t\t\tnavigationHieght:0,\r\n        ggsx: false,//广告热加载\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\t\t\tthis.statusBarHeight = sysinfo.statusBarHeight;\r\n\t\t\tthis.wxNavigationBarMenu();\r\n\t\t\tthis.screenWidth = sysinfo.screenWidth;\r\n\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\tif(locationCache){\r\n\t\t\t\tif(locationCache.latitude){\r\n\t\t\t\t\tthis.latitude = locationCache.latitude\r\n\t\t\t\t\tthis.longitude = locationCache.longitude\r\n\t\t\t\t}\r\n\t\t\t\tif(locationCache.area){\r\n\t\t\t\t\tthis.area = locationCache.area\r\n\t\t\t\t\tthis.curent_address = locationCache.address\r\n\t\t\t\t}\r\n\t\t\t\tif(locationCache.mendian_id){\r\n\t\t\t\t\tthis.mendianid = locationCache.mendian_id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log(locationCache);\r\n      if(this.opt && this.opt.trid){\r\n        this.trid = this.opt.trid;\r\n      }else{\r\n        this.trid = app.globalData.trid\r\n      }\r\n\t\t\t// this.getdata();\r\n\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tvar that = this;\r\n\t\t\tuni.onNetworkStatusChange((res) => {\r\n\t\t\t\tconsole.log('-----onNetworkStatusChange---')\r\n\t\t\t\tconsole.log(res)\r\n\t\t\t\tif(res.isConnected == true){\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t\tif(this.platform=='wx' && (this.homeNavigationCustom>1)){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\tuni.createSelectorQuery().select('.navigation').boundingClientRect((rect) => {\r\n\t\t\t\t\t\tthat.navigationHieght = rect.height\r\n\t\t\t\t\t}).exec()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow:function(opt){\r\n\t\t\tconsole.log('index onshow')\r\n\t\t\tthis.mid = app.globalData.mid\r\n\t\t\tif(this.needRefreshMyaddress){\r\n\t\t\t\tthis.getMyAddress()\r\n\t\t\t\tthat.needRefreshMyaddress = false\r\n\t\t\t}\r\n      var indextipstatus = app.globalData.indextipstatus\r\n      if(!indextipstatus){\r\n        this.indextipstatus = false;\r\n      }else{\r\n        this.indextipstatus = true;\r\n      }\r\n\t\t\tthis.getdata()\r\n\t\t},\r\n\t\tonPullDownRefresh: function(e) {\r\n\t\t\tif(!this.show_nearbyarea && !this.isshowmendianmodal){\r\n\t\t\t\tthis.getdata();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tuni.$emit('onPageScroll',e);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\twxNavigationBarMenu:function(){\r\n\t\t\t\tvar homeNavigationCustom = this.homeNavigationCustom\r\n\t\t\t\tif(this.platform=='wx' && (homeNavigationCustom>1)){\r\n\t\t\t\t\t//胶囊菜单信息\r\n\t\t\t\t\tthis.navigationMenu = wx.getMenuButtonBoundingClientRect()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoBusiness(){\r\n\t\t\t\tvar url = '/pagesExt/business/clist2';\r\n\t\t\t\tvar backurl = encodeURIComponent('/pages/index/index');\r\n\t\t\t\tapp.goto(url+'?isindex=1&backurl='+backurl);\r\n\t\t\t},\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar opt = this.opt\r\n\t\t\t\tvar id = 0;\r\n\t\t\t\tif (opt.select_bid) {\r\n\t\t\t\t\tvar select_bid = opt.select_bid;\r\n\t\t\t\t\tapp.setCache('select_bid', select_bid);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar select_bid = app.getCache('select_bid');\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (opt && opt.id) {\r\n\t\t\t\t\tid = opt.id;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.checkAreaByShowlevel();\r\n\t\t\t\tvar locationCache =  app.getLocationCache();\r\n\t\t\t\tvar mendian_isinit = 0;\r\n\t\t\t\tif(locationCache){\r\n\t\t\t\t\tif(locationCache.latitude){\r\n\t\t\t\t\t\tthis.latitude = locationCache.latitude\r\n\t\t\t\t\t\tthis.longitude = locationCache.longitude\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(locationCache.area){\r\n\t\t\t\t\t\tthis.area = locationCache.area\r\n\t\t\t\t\t\tthis.curent_address = locationCache.address\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(locationCache.mendian_id){\r\n\t\t\t\t\t\tthis.mendianid = locationCache.mendian_id\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(locationCache.mendian_isinit){\r\n\t\t\t\t\t\tmendian_isinit = locationCache.mendian_isinit\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tapp.get('ApiIndex/index', {\r\n\t\t\t\t\tid: id,\r\n\t\t\t\t\tlatitude: that.latitude,\r\n\t\t\t\t\tlongitude: that.longitude,\r\n\t\t\t\t\tarea:that.area,\r\n\t\t\t\t\tselect_bid: select_bid,\r\n\t\t\t\t\tpid: app.globalData.pid,\r\n\t\t\t\t\tmendian_id:that.mendianid,\r\n\t\t\t\t\tmendian_isinit:mendian_isinit,\r\n\t\t\t\t\ttrid: that.trid,\r\n\t\t\t\t\tmode:that.opt.mode?that.opt.mode:''\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (data.status == 2) {\r\n\t\t\t\t\t\t//付费查看\r\n\t\t\t\t\t\tapp.goto('/pagesExt/pay/pay?fromPage=index&id=' + data.payorderid + '&pageid=' + that.id,\r\n\t\t\t\t\t\t\t'redirect');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tvar pagecontent = data.pagecontent;\r\n\t\t\t\t\t\tthat.title = data.pageinfo.title;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif(data.oglist)\r\n\t\t\t\t\t\t\tthat.oglist = data.oglist;\r\n\r\n\t\t\t\t\t\tthat.guanggaopic = data.guanggaopic;\r\n            if(data.pageinfo){\r\n              that.ggsx = true;\r\n            }\r\n\t\t\t\t\t\tthat.guanggaourl = data.guanggaourl;\r\n\t\t\t\t\t\tthat.guanggaotype = data.guanggaotype;\r\n\t\t\t\t\t\tthat.guanggaoparam = data.guanggaoparam;\r\n\t\t\t\t\t\tthat.pageinfo = data.pageinfo;\r\n\t\t\t\t\t\tthat.pagecontent = data.pagecontent;\r\n\t\t\t\t\t\tthat.copyright = data.copyright;\r\n\t\t\t\t\t\tthat.copyright_link = data.copyright_link || '';\r\n\t\t\t\t\t\tthat.sysset = data.sysset;\r\n\t\t\t\t\t\tthat.mendian_change = data.mendian_change;\r\n\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\ttitle: data.pageinfo.title\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif(that.sysset.mode==2){\r\n\t\t\t\t\t\t\tthat.show_location = data.show_location;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t\tif(that.sysset.mode==3){\r\n\t\t\t\t\t\t\tthat.show_mendian = data.show_mendian;\r\n\t\t\t\t\t\t\tthat.area = ''\r\n\t\t\t\t\t\t\tthat.curent_address=''\r\n\t\t\t\t\t\t\tthat.locationCache.area = ''\r\n\t\t\t\t\t\t\tthat.locationCache.address = ''\r\n\t\t\t\t\t\t\tif(data.mendian){\r\n\t\t\t\t\t\t\t\tthat.mendian = data.mendian\r\n\t\t\t\t\t\t\t\tthat.mendianid = data.mendian.id\r\n\t\t\t\t\t\t\t\tthat.locationCache.mendian_id = that.mendian.id\r\n\t\t\t\t\t\t\t\tthat.locationCache.mendian_name = that.mendian.name\r\n\t\t\t\t\t\t\t\tthat.locationCache.latitude = that.latitude\r\n\t\t\t\t\t\t\t\tthat.locationCache.longitude = that.longitude\r\n\t\t\t\t\t\t\t\tif(that.latitude=='' || that.longitude==''){\r\n\t\t\t\t\t\t\t\t\t//第一次初始化的默认门店\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.mendian_isinit = 1;\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.mendian_isinit = 0;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif(data.mendian_upgrade){\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.mendian_address = that.mendian.address\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.mendian_name = that.mendian.name\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.mendian_pic = that.mendian.pic\r\n\t\t\t\t\t\t\t\t\tthat.locationCache.headimg = that.mendian.headimg\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tapp.setLocationCacheData(that.locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tapp.setLocationCache('mendian_id',0)\r\n\t\t\t\t\t\t\t\tapp.setLocationCache('mendian_name','')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t//头部定位\r\n\t\t\t\t\t\tif(that.latitude && that.longitude){\r\n\t\t\t\t\t\t\tif(that.sysset.mode==2 && that.sysset.loc_area_type==0){\r\n\t\t\t\t\t\t\t\t//当前城市\r\n\t\t\t\t\t\t\t\tthat.checkLocation()\r\n\t\t\t\t\t\t\t\tthat.initCityAreaList()\r\n\t\t\t\t\t\t\t}else if(that.sysset.mode==2 && that.sysset.loc_area_type==1){\r\n\t\t\t\t\t\t\t\t//附近地址\r\n\t\t\t\t\t\t\t\tthat.checkLocation()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t\tif (that.latitude == '' && that.longitude == '' && data.needlocation) {\r\n\t\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t\t\tif(that.sysset.mode==2){\r\n\t\t\t\t\t\t\t\t\t//当前城市\r\n\t\t\t\t\t\t\t\t\tthat.checkLocation()\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t\t\t}\t\t\r\n\t\t\t\t\t\t\t},function(res){\r\n\t\t\t\t\t\t\t\tconsole.error(res);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (data.sysset.mode == 1 && data.business) {\r\n\t\t\t\t\t\t\tthat.business = data.business;\r\n\t\t\t\t\t\t\tif (select_bid == '')\r\n\t\t\t\t\t\t\t\tapp.setCache('select_bid', data.business.id);\r\n\r\n\t\t\t\t\t\t\tvar juli = app.getDistance(that.longitude, that.latitude, that.business.longitude, that.business.latitude);\t\t\r\n\t\t\t\t\t\t\tthat.sysset.juli = \tjuli?juli:0;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tvar juli = app.getDistance(that.longitude, that.latitude, data.sysset.longitude, data.sysset.latitude);\r\n\t\t\t\t\t\t\tthat.sysset.juli = \tjuli?juli:0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif(that.sysset.agent_card){\r\n\t\t\t\t\t\t\tif(that.sysset.agent_card_info.juli != '')\r\n\t\t\t\t\t\t\t\tthat.agent_juli = that.sysset.agent_card_info.juli + 'km';\r\n\t\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\t\tlet juli = app.getDistance(that.longitude, that.latitude, that.sysset.agent_card_info.longitude, that.sysset.agent_card_info.latitude);\r\n\t\t\t\t\t\t\t\tthat.agent_juli = juli?juli + 'km':'';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif(data.show_indextip){\r\n              that.show_indextip = data.show_indextip;\r\n            }\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.mendian_upgrade = data.mendian_upgrade\r\n\t\t\t\t\t\tthat.mendian_show_address = data.mendian_show_address\r\n\t\t\t \t} else {\r\n\t\t\t\t\t\tif (data.msg) {\r\n\t\t\t\t\t\t\tapp.alert(data.msg, function() {\r\n\t\t\t\t\t\t\t\tif (data.url) app.goto(data.url);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else if (data.url) {\r\n\t\t\t\t\t\t\tapp.goto(data.url);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.alert('您无查看权限');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowsubqrcode: function() {\r\n\t\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t\t},\r\n\t\t\tclosesubqrcode: function() {\r\n\t\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t\t},\r\n\t\t\tchangePopupAddress:function(status){\r\n\t\t\t\t\tthis.xdata.popup_address = status;\r\n\t\t\t},\r\n\t\t\tsetMendianData:function(data){\r\n\t\t\t\t\tthis.mendian_data = data;\r\n\t\t\t},\r\n\t\t\t//头部定位start 只处理微信小程序，其他端的组件实现\r\n\t\t\tcheckLocation:function(){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tif(that.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar loc_area_type = that.sysset.loc_area_type;\r\n\t\t\t\tvar loc_range_type = that.sysset.loc_range_type;\r\n\t\t\t\tvar loc_range = that.sysset.loc_range;\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\t//缓存为空 或 显示城市和当前地址切换 或 同城和自定义范围切换 或 显示距离发生变化\r\n\t\t\t\tif(!locationCache || !locationCache.address || (locationCache.loc_area_type!=loc_area_type || locationCache.loc_range_type!=loc_range_type || locationCache.loc_range!=loc_range)){\r\n\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t\t//如果从当前地址切到当前城市，则重新定位用户位置\r\n\t\t\t\t\t\t\tapp.post('ApiAddress/getAreaByLocation', {latitude:that.latitude,longitude:that.longitude}, function(res) {\r\n\t\t\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\t\t\tlocationCache.loc_area_type = loc_area_type\r\n\t\t\t\t\t\t\t\t\tlocationCache.loc_range_type = loc_range_type\r\n\t\t\t\t\t\t\t\t\tlocationCache.loc_range = loc_range\r\n\t\t\t\t\t\t\t\t\tlocationCache.latitude = that.latitude\r\n\t\t\t\t\t\t\t\t\tlocationCache.longitude = that.longitude\r\n\t\t\t\t\t\t\t\t\tif(loc_area_type==0){\r\n\t\t\t\t\t\t\t\t\t\tif(that.showlevel==1){\r\n\t\t\t\t\t\t\t\t\t\t\tlocationCache.address = res.province\r\n\t\t\t\t\t\t\t\t\t\t\tlocationCache.area = res.province+','+res.city\r\n\t\t\t\t\t\t\t\t\t\t}else if(that.showlevel==2){\r\n\t\t\t\t\t\t\t\t\t\t\tlocationCache.address = res.city\r\n\t\t\t\t\t\t\t\t\t\t\tlocationCache.area = res.province+','+res.city\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\tlocationCache.address = res.city\r\n\t\t\t\t\t\t\t\t\t\t\tlocationCache.area = res.province+','+res.city+','+res.district\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tthat.area = locationCache.area\r\n\t\t\t\t\t\t\t\t\t\tthat.curent_address = locationCache.address\r\n\t\t\t\t\t\t\t\t\t\tapp.setLocationCacheData(locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\t\t\t\t}else if(loc_area_type==1){\r\n\t\t\t\t\t\t\t\t\t\tlocationCache.address = res.landmark\r\n\t\t\t\t\t\t\t\t\t\tlocationCache.area = res.province+','+res.city+','+res.district\r\n\t\t\t\t\t\t\t\t\t\tthat.area = locationCache.area\r\n\t\t\t\t\t\t\t\t\t\tthat.curent_address = locationCache.address\r\n\t\t\t\t\t\t\t\t\t\tapp.setLocationCacheData(locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\t\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcheckAreaByShowlevel:function(){\r\n\t\t\t\tvar that  = this\r\n\t\t\t\tif(that.sysset.mode==2 && that.sysset.loc_range_type==0){\r\n\t\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\t\tif(locationCache && locationCache.area){\r\n\t\t\t\t\t\tvar area = '';\r\n\t\t\t\t\t\tvar areaArr = locationCache.area.split(',');\r\n\t\t\t\t\t\tvar showlevel = that.showlevel\r\n\t\t\t\t\t\tif(showlevel==1 && areaArr.length>0){\r\n\t\t\t\t\t\t\tarea = areaArr[0]\r\n\t\t\t\t\t\t}else if(showlevel==2 && areaArr.length>1){\r\n\t\t\t\t\t\t\tarea = areaArr[0] + ','+areaArr[1]\r\n\t\t\t\t\t\t}else if(showlevel==3 && areaArr.length>2){\r\n\t\t\t\t\t\t\tarea = areaArr[0] + ','+areaArr[1] + ','+areaArr[2]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.locationCache.area = area;\r\n\t\t\t\t\t\tapp.setLocationCache('area',area,that.cacheExpireTime)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinitCityAreaList:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t//地区加载\r\n\t\t\t\tif(that.arealist.length==0){\r\n\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\r\n\t\t\t\t\t\tdata: {},\r\n\t\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\t\tif(that.showlevel<3){\r\n\t\t\t\t\t\t\t\tvar newlist = [];\r\n\t\t\t\t\t\t\t\tvar arealist = res2.data\r\n\t\t\t\t\t\t\t\tfor(var i in arealist){\r\n\t\t\t\t\t\t\t\t\tvar item1 = arealist[i]\r\n\t\t\t\t\t\t\t\t\tif(that.showlevel==2){\r\n\t\t\t\t\t\t\t\t\t\tvar children = item1.children //市\r\n\t\t\t\t\t\t\t\t\t\tvar newchildren = [];\r\n\t\t\t\t\t\t\t\t\t\tfor(var j in children){\r\n\t\t\t\t\t\t\t\t\t\t\tvar item2 = children[j]\r\n\t\t\t\t\t\t\t\t\t\t\titem2.children = []; //去掉三级-县的数据\r\n\t\t\t\t\t\t\t\t\t\t\tnewchildren.push(item2)\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\titem1.children = newchildren\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\titem1.children = []; ////去掉二级-市的数据\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tnewlist.push(item1)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.arealist = newlist\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tthat.arealist = res2.data\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tareachange:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tconst value = e.detail.value\r\n\t\t\t\tvar area_name = [];\r\n\t\t\t\tvar showarea = ''\r\n\t\t\t\tfor(var i=0;i<that.showlevel;i++){\r\n\t\t\t\t\tarea_name.push(value[i].text)\r\n\t\t\t\t\tshowarea = value[i].text\r\n\t\t\t\t}\r\n\t\t\t\tthat.area = area_name.join(',')\r\n\t\t\t\tthat.curent_address = showarea\r\n\t\t\t\t//全局缓存\r\n\t\t\t\tvar locationCache = app.getLocationCache()\r\n\t\t\t\tlocationCache.area = that.area\r\n\t\t\t\tlocationCache.address = showarea\r\n\t\t\t\tif(that.sysset.loc_area_type==0){\r\n\t\t\t\t\t//获取地址中心地标\r\n\t\t\t\t\tapp.post('ApiAddress/addressToZuobiao', {\r\n\t\t\t\t\t\taddress:area_name.join('')\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tthat.latitude = resp.latitude\r\n\t\t\t\t\t\t\tthat.longitude = resp.longitude\r\n\t\t\t\t\t\t\tlocationCache.latitude = that.latitude\r\n\t\t\t\t\t\t\tlocationCache.longitude = that.longitude\r\n\t\t\t\t\t\t\tapp.setLocationCacheData(locationCache,that.cacheExpireTime)\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.error('地址解析错误');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcloseNearbyBox:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\tshowNearbyBox:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tthis.show_nearbyarea = true\r\n\t\t\t\tthis.placekeyword = ''\r\n\t\t\t\tthis.suggestionplacelist = []\r\n\t\t\t\tvar nearbylist = app.getLocationCache('poilist');\r\n\t\t\t\tif(!nearbylist){\r\n\t\t\t\t\tnearbylist = [];\r\n\t\t\t\t}\r\n\t\t\t\tif(nearbylist && nearbylist.length>0){\r\n\t\t\t\t\tthis.nearbyplacelist = nearbylist\r\n\t\t\t\t}\r\n\t\t\t\t//获取我的收货地址\r\n\t\t\t\tif(app.globalData.mid){\r\n\t\t\t\t\tthat.loading = true\r\n\t\t\t\t\tthat.getMyAddress()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangeAddress:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.ischangeaddress = true\r\n\t\t\t},\r\n\t\t\taddMyAddress:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.needRefreshMyaddress = true;\r\n\t\t\t\tapp.goto(\"/pagesB/address/addressadd?type=1\")\r\n\t\t\t},\r\n\t\t\tgetMyAddress:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true\r\n\t\t\t\tapp.post('ApiAddress/address', {\r\n\t\t\t\t\ttype:1\r\n\t\t\t\t}, function(resp) {\r\n\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\tthat.myaddresslist = resp.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcancelChangeAddress:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.ischangeaddress = false\r\n\t\t\t},\r\n\t\t\trefreshAddress:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tthat.loading = true\r\n\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\t//请求当前地址[取商圈地址]\r\n\t\t\t\t\tapp.post('ApiAddress/getAreaByLocation', {\r\n\t\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\t\ttype:1\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tthat.latitude = latitude\r\n\t\t\t\t\t\t\tthat.longitude = longitude\r\n\t\t\t\t\t\t\tvar data = resp.data\r\n\t\t\t\t\t\t\tthat.curent_address = data.address_reference.landmark\r\n\t\t\t\t\t\t\tthat.nearbyplacelist = data.pois\r\n\t\t\t\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\t\t\t\tlocationCache.area = data.address_component.province+','+data.address_component.city+','+data.address_component.district\r\n\t\t\t\t\t\t\tlocationCache.address = that.curent_address\r\n\t\t\t\t\t\t\tlocationCache.latitude = latitude\r\n\t\t\t\t\t\t\tlocationCache.longitude = longitude\r\n\t\t\t\t\t\t\tlocationCache.poilist = that.nearbyplacelist\r\n\t\t\t\t\t\t\tapp.setLocationCacheData(locationCache)\r\n\t\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t},function(res){\r\n\t\t\t\t\tconsole.error(res);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowAllAddress:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.isshowalladdress = this.isshowalladdress?false:true\r\n\t\t\t},\r\n\t\t\tchooseMyAddress:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tvar info = that.myaddresslist[index]\r\n\t\t\t\tthat.curent_address = info.address\r\n\t\t\t\tthat.latitude = info.latitude\r\n\t\t\t\tthat.longitude = info.longitude\r\n\t\t\t\t\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\tlocationCache.area = info.province+','+info.city+','+info.district\r\n\t\t\t\tlocationCache.address = info.address\r\n\t\t\t\tlocationCache.latitude = info.latitude\r\n\t\t\t\tlocationCache.longitude = info.longitude\r\n\t\t\t\tapp.setLocationCacheData(locationCache,that.cacheExpireTime)\r\n\t\t\t\t\r\n\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\tthat.getdata()\r\n\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\tchooseNearbyAddress:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tvar info = that.nearbyplacelist[index]\r\n\t\t\t\tthat.curent_address = info.title\r\n\t\t\t\tthat.latitude = info.location.lat\r\n\t\t\t\tthat.longitude = info.location.lng\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\tlocationCache.area = info.ad_info.province+','+info.ad_info.city+','+info.ad_info.district\r\n\t\t\t\tlocationCache.address = info.title\r\n\t\t\t\tlocationCache.latitude = info.location.lat\r\n\t\t\t\tlocationCache.longitude = info.location.lng\r\n\t\t\t\tapp.setLocationCacheData(locationCache,that.cacheExpireTime)\r\n\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\tthat.getdata()\r\n\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\tchooseSuggestionAddress:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tvar info = that.suggestionplacelist[index]\r\n\t\t\t\tthat.curent_address = info.title\r\n\t\t\t\tthat.latitude = info.location.lat\r\n\t\t\t\tthat.longitude = info.location.lng\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\tlocationCache.area = info.province+','+info.city+','+info.district\r\n\t\t\t\tlocationCache.address = info.title\r\n\t\t\t\tlocationCache.latitude = info.location.lat\r\n\t\t\t\tlocationCache.longitude = info.location.lng\r\n\t\t\t\tapp.setLocationCacheData(locationCache,that.cacheExpireTime)\r\n\t\t\t\tthat.refreshNearbyPlace();\r\n\t\t\t\tthat.getdata()\r\n\t\t\t\tthat.show_nearbyarea = false\r\n\t\t\t},\r\n\t\t\trefreshNearbyPlace:function(latitude='',longitude=''){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tif(latitude=='' && longitude==''){\r\n\t\t\t\t\tlatitude = that.latitude\r\n\t\t\t\t\tlongitude = that.longitude\r\n\t\t\t\t}\r\n\t\t\t\tif(latitude && longitude){\r\n\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\tapp.post('ApiAddress/getAreaByLocation', {\r\n\t\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\t\ttype:1\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tvar data = resp.data\r\n\t\t\t\t\t\t\tthat.nearbyplacelist = data.pois\r\n\t\t\t\t\t\t\tapp.setLocationCache('poilist',that.nearbyplacelist,that.cacheExpireTime);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tplacekeywordInput:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.placekeyword = e.detail.value\r\n\t\t\t},\r\n\t\t\tsearchPlace:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this\r\n\t\t\t\tif(that.placekeyword==''){\r\n\t\t\t\t\tthat.suggestionplacelist = []\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar locationCache = app.getLocationCache();\r\n\t\t\t\t\r\n\t\t\t\tvar region = '';\r\n\t\t\t\tif(locationCache){\r\n\t\t\t\t\tif(locationCache.area){\r\n\t\t\t\t\t\tvar areaArr = locationCache.area.split(',')\r\n\t\t\t\t\t\tif(areaArr.length==2){\r\n\t\t\t\t\t\t\tregion = areaArr[1]\r\n\t\t\t\t\t\t}else if(areaArr.length==1){\r\n\t\t\t\t\t\t\tregion = areaArr[0]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loading = true\r\n\t\t\t\tapp.post('ApiAddress/suggestionPlace', {\r\n\t\t\t\t\tlatitude: locationCache.latitude,\r\n\t\t\t\t\tlongitude: locationCache.longitude,\r\n\t\t\t\t\tregion:region,\r\n\t\t\t\t\tkeyword:that.placekeyword\r\n\t\t\t\t}, function(resp) {\r\n\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\tthat.suggestionplacelist = resp.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//头部定位end\r\n\t\t\t\r\n\t\t\t//门店模式start\r\n\t\t\tshowMendianModal:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif(that.mendianlist.length>0){\r\n\t\t\t\t\tthat.isshowmendianmodal = true\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.post('ApiMendian/mendianlist', {\r\n\t\t\t\t\t\tlatitude: that.latitude,\r\n\t\t\t\t\t\tlongitude: that.longitude,\r\n\t\t\t\t\t}, function(resp) {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tif(resp.status==1){\r\n\t\t\t\t\t\t\tthat.mendianlist = resp.data\r\n\t\t\t\t\t\t\tthat.isshowmendianmodal = true\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.error(resp.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thideMendianModal:function(){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.isshowmendianmodal = false\r\n\t\t\t},\r\n\t\t\tchangeMendian:function(e){\r\n\t\t\t\tif(this.platform!='wx'){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar mendianid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tthat.mendianid = mendianid\r\n\t\t\t\tthat.mendian = that.mendianlist[index]\r\n\t\t\t\tthat.locationCache.mendian_id = that.mendian.id\r\n\t\t\t\tthat.locationCache.mendian_name = that.mendian.name\r\n\t\t\t\tapp.setLocationCache('mendian_id',that.mendian.id,that.cacheExpireTime)\r\n\t\t\t\tapp.setLocationCache('mendian_name',that.mendian.name,that.cacheExpireTime)\r\n\t\t\t\tapp.setLocationCache('mendian_isinit',0)\r\n\t\t\t\tthat.isshowmendianmodal = false\r\n\t\t\t\tthat.getdata()\r\n\t\t\t},\r\n\t\t\t//门店模式end\r\n\t\t\tpagescroll:function(e){\r\n\t\t\t\tuni.$emit('onPageScroll',e.detail);\r\n\t\t\t},\r\n      closeindextip:function(e){\r\n        var that = this;\r\n        that.indextipstatus = true;\r\n        app.globalData.indextipstatus = true;\r\n      },\r\n\t\t\tcallphone:function(e) {\r\n\t\t\t\tvar phone = e.currentTarget.dataset.phone;\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\tfail: function () {\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\topenLocation:function(e){\r\n\t\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\r\n\t\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\r\n\t\t\t\tvar address = e.currentTarget.dataset.address;\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t latitude:latitude,\r\n\t\t\t\t longitude:longitude,\r\n\t\t\t\t name:address,\r\n\t\t\t\t scale: 13\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t}\r\n}\r\n</script>\r\n<style>\r\n\t@import \"./location.css\";\r\n\t.pageContainer{\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\t/* #ifndef MP-ALIPAY */\r\n\t\theight: auto;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef MP-ALIPAY */\r\n\t\theight: 100%;\r\n\t\t/* #endif */\r\n\t\t\r\n\t}\r\n\t.topR {\r\n\t\tflex: 1;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.topR .btn-text {\r\n\t\tmargin: 0 10rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.follow_topbar {\r\n\t\theight: 88rpx;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 640px;\r\n\t\tbackground: rgba(0, 0, 0, 0.8);\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tz-index: 13;\r\n\t}\r\n\r\n\t.follow_topbar .headimg {\r\n\t\theight: 64rpx;\r\n\t\twidth: 64rpx;\r\n\t\tmargin: 6px;\r\n\t\tfloat: left;\r\n\t}\r\n\r\n\t.follow_topbar .headimg image {\r\n\t\theight: 64rpx;\r\n\t\twidth: 64rpx;\r\n\t}\r\n\r\n\t.follow_topbar .info {\r\n\t\theight: 56rpx;\r\n\t\tpadding: 16rpx 0;\r\n\t}\r\n\r\n\t.follow_topbar .info .i {\r\n\t\theight: 28rpx;\r\n\t\tline-height: 28rpx;\r\n\t\tcolor: #ccc;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.follow_topbar .info {\r\n\t\theight: 80rpx;\r\n\t\tfloat: left;\r\n\t}\r\n\r\n\t.follow_topbar .sub {\r\n\t\theight: 48rpx;\r\n\t\twidth: auto;\r\n\t\tbackground: #FC4343;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin: 20rpx 16rpx 20rpx 0;\r\n\t\tfloat: right;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\tline-height: 52rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.qrcodebox {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 50rpx;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 20rpx\r\n\t}\r\n\r\n\t.qrcodebox .img {\r\n\t\twidth: 400rpx;\r\n\t\theight: 400rpx\r\n\t}\r\n\r\n\t.qrcodebox .txt {\r\n\t\tcolor: #666;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.qrcodebox .close {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: -100rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -25rpx;\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.6);\r\n\t\tborder-radius: 50%;\r\n\t\tpadding: 8rpx\r\n\t}\r\n\r\n\t.bobaobox {\r\n\t\tposition: fixed;\r\n\t\ttop: calc(var(--window-top) + 180rpx);\r\n\t\tleft: 20rpx;\r\n\t\tz-index: 10;\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\tborder-radius: 30rpx;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 0 10rpx\r\n\t}\r\n\t.bobaobox_bottom {\r\n\t\tposition: fixed;\r\n\t\tbottom: calc(env(safe-area-inset-bottom) + 150rpx);\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\twidth:470rpx;\r\n\t\tmargin:0 auto;\r\n\t\tz-index: 10;\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\tborder-radius: 30rpx;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 0 10rpx\r\n\t}\r\n\t@supports (bottom: env(safe-area-inset-bottom)){\r\n\t\t.bobaobox_bottom {\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: calc(env(safe-area-inset-bottom) + 150rpx);\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\twidth:470rpx;\r\n\t\t\tmargin:0 auto;\r\n\t\t\tz-index: 10;\r\n\t\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tpadding: 0 10rpx\r\n\t\t}\r\n\t}\r\n\r\n\t.navigation {\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tposition: fixed;\r\n\t\tz-index: 99;\r\n\t\tpadding-bottom:10px\r\n\t}\r\n\r\n\t.navcontent {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding-left: 10px\r\n\t}\r\n\r\n\t.navcontent .topinfo {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.navcontent .topinfoicon {\r\n\t\twidth: 17px;\r\n\t\theight: 17px;\r\n\t\tborder-radius: 4px\r\n\t}\r\n\r\n\t.navcontent .topinfotxt {\r\n\t\tmargin-left: 6px;\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 600;\r\n\t\tmax-width: 70px;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.navcontent .topsearch {\r\n\t\theight: 32px;\r\n\t\tbackground: #f2f2f2;\r\n\t\tborder-radius: 16px;\r\n\t\tcolor: #232323;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 14px;\r\n\t\tflex: 1;\r\n\t\tmargin-left: 12rpx;\r\n\t}\r\n\r\n\t.navcontent .topsearch image {\r\n\t\twidth: 14px;\r\n\t\theight: 15px;\r\n\t\tmargin-right: 6px\r\n\t}\r\n\r\n\t.limitText {\r\n\t\tflex: 1;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.agent-card {\r\n\t\theight: auto;\r\n\t\tposition: relative;\r\n\t\tcolor: #333;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 0 20rpx 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tborder-radius: 0 10rpx 10rpx 10rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 0 8rpx 0px rgb(0 0 0 / 30%);\r\n\t}\r\n\r\n\t.agent-card .row1 {\r\n\t\tpadding: 20rpx 10rpx 20rpx 20rpx;\r\n\t}\r\n\r\n\t.agent-card .logo {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.agent-card .text {\r\n\t\tflex: 1;\r\n\t\tmargin-left: 20rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 180%;\r\n\t}\r\n\t.agent-card .right {\r\n\t\talign-items: center;\r\n\t\tpadding-right: 10rpx;\r\n\t}\r\n\r\n\t.agent-card .title {\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.agent-card .btn {\r\n\t\tposition: absolute;\r\n\t\tright: -100rpx;\r\n\t\tpadding: 0 14rpx;\r\n\t\ttop: 0;\r\n\t\tborder: 1px solid #B6C26E;\r\n\t\tborder-radius: 10rpx;\r\n\t\tcolor: #B6C26E;\r\n\t}\r\n\r\n\t.agent-card .img {\r\n\t\tmargin-right: 6rpx;\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx\r\n\t}\r\n\r\n\t.agent-card .img2 {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx\r\n\t}\r\n\r\n\t.grey-text {\r\n\t\tcolor: #999;\r\n\t\tfont-weight: normal;\r\n\t}\r\n\r\n\t.agent-card-b view {\r\n\t\tline-height: 72rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #444;\r\n\t\twidth: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.agent-card-b view:first-child::after {\r\n\t\tcontent: '';\r\n\t\twidth: 1px;\r\n\t\theight: 28rpx;\r\n\t\tborder-right: 1px solid #444;\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t}\r\n\t\r\n\t/* 多商户切换 */\r\n\t.header{\r\n\t\tposition: relative;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t.header_title{\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 700;\r\n\t\tfont-size: 35rpx;\r\n\t}\r\n\t.header_detail{\r\n\t\theight: 25rpx;\r\n\t\twidth: 25rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\t\r\n\t.header_address{\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\t.mendianupbg{position: absolute;height: 300rpx; width: 100%;top:0 }\r\n\t.mendianup{ display: flex;justify-content: space-between; padding:20rpx;align-items: center;position: absolute; height:150rpx;color: #fff; width: 100%;}\t\r\n\t.mendianup .left{ width: 80%;}\r\n\t.mendianup .left .f1{ font-size: 36rpx;display: flex;align-items: center;line-height:50rpx;}\r\n\t.mendianup .left .f1 .t1{ font-size: 24rpx;display: flex;align-items: center;margin-left: 20rpx; }\r\n\t.mendianup .right{ display: flex;align-items: center;flex-direction: column;}\r\n\t.mendianup .right .f1 image{ width: 70rpx; height:70rpx; border-radius:50%;}\r\n\t.mendianup .left .f2 image{width:24rpx; height:24rpx;}\r\n\t.mendianup .left .f2{display: flex;align-items: center;}\r\n\t\r\n\t/deep/ .header-location-weixin .header-address .uni-data-tree-dialog{padding-bottom: 180rpx !important;}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839391497\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}