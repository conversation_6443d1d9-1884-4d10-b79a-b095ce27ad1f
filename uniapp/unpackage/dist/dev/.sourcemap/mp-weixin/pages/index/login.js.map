{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/login.vue?d98c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/login.vue?9dbe", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/login.vue?e5c6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/login.vue?e4d9", "uni-app:///pages/index/login.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/login.vue?de7c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/login.vue?6c83"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "platform2", "platform", "platformname", "platformimg", "logintype", "logintype_1", "logintype_2", "logintype_3", "logintype_4", "logintype_6", "logintype_7", "isioslogin", "isgooglelogin", "google_client_id", "needsms", "logo", "name", "xystatus", "xyagree_type", "xyname", "xycontent", "xyname2", "xycontent2", "<PERSON><PERSON><PERSON><PERSON>", "showxieyi2", "isagree", "smsdjs", "tel", "<PERSON><PERSON>", "frompage", "wxloginclick", "iosloginclick", "googleloginclick", "login_bind", "login_setnickname", "login_mast", "reg_invite_code", "reg_invite_code_text", "reg_invite_code_show", "yqcode", "parent", "tmplids", "default_headimg", "headimg", "nickname", "loginset_type", "loginset_data", "alih5", "ali_appid", "alih5loginclick", "rs_notlogin_to_business", "reading_completed", "checknickname", "wxformimgurl", "wxtext", "onLoad", "onShow", "uni", "onPullDownRefresh", "methods", "getHeight", "setTimeout", "ceshixian", "heziheight", "that", "xieyiTolower", "getdata", "app", "pid", "formSubmit", "pwd", "smscode", "regbid", "console", "getPhoneNumber", "success", "iv", "encryptedData", "code", "setnicknameregister", "nosetnicknameregister", "setRegisterInvite", "setRegisterInvitePass", "bindregister", "nobindregister", "register", "url", "authlogin", "weixinlogin", "ioslogin", "googlelogin", "changelogintype", "promptRead", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "showxieyiFun2", "hidexieyi2", "telinput", "yqinput", "uploadHeadimg", "count", "sizeType", "sourceType", "filePath", "fail", "onChooseAvatar", "time", "clearInterval", "alih5login", "ttgetUserinfo", "noLogin", "getBaiduPhoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsTv1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACA;IACA;MACA;MACA;IACA;IAOA;IACA;IACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACA;QACA;QACAJ;UACAK;QACA;QACAL;UACAM;UACAD;UACA;YACAE;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAF;MACAG;QAAAC;QAAAhB;MAAA;QACAY;QACA;UACAG;UAAA;QACA;QACA;QACAH;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QAWAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;QACA;QACAA;QACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;UACAA;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;QACA;QACA;QACA;QAEAA;MA+DA;IACA;IACAK;MACA;MACA;MACA;QACAF;QACA;MACA;MACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MAEA;QACA;UACAA;UACA;QACA;MACA;MAEA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MAEAA;MACAA;QAAAxC;QAAA2C;QAAAC;QAAAnE;QAAAgE;QAAA7B;QAAAiC;MAAA;QACAL;QACA;UACAA;UACA;YACAH;UACA;UACAA;YACAH;cACAM;cACAM;YACA;UACA;QACA;UACAN;QACA;MACA;IACA;IACAO;MACA;MACAD;MACA;QACAN;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA9E;QAAAsF;UACAF;UACA;UACA;UACAN;YAAAxB;YAAAC;YAAAgC;YAAAC;YAAAC;YAAAV;YAAA7B;YAAAiC;UAAA;YACA;cACAL;cACA;gBACAH;cACA;cACAA;gBACAH;kBACAM;gBACA;cACA;YACA;cACAA;YACA;YACA;UACA;QACA;MAAA;IACA;IACAY;MACA;MACA;MACA;MACA;QACAZ;QACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACAR;MACA;MACA;MACA;QACAN;QACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAe;MACA;QACA;QACA;QACA;MACA;QACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAhB;QACA;MACA;MACA;QACAA;QACA;MACA;MACAH;IACA;IACAoB;MACA;IACA;IACAC;MACA;MACA;MACA;QACAC;QACA;UACAA;QACA;MACA;QACAA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACAnB;QAAAxB;QAAAC;QAAAjB;QAAA4C;QAAAH;QAAA7B;QAAAiC;MAAA;QACA;UACA;YACAL;UACA;YACAA;UACA;UAEA;YACAH;UACA;UACAA;YACAH;cACAM;YACA;UACA;QACA;UACAA;QACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;QACApB;QACA;MACA;MACA;QACAH;MACA;QACAA;MACA;IACA;IACAwB;MACA;MACA;QACAxB;QACAA;QACA;MACA;MACAS;MACAN;MACAH;MACAG;QACAM;QACAN;QACA;UACAA;UACAN;YACAY;YACAA;YACAN;UACA;QAEA;UACA;UACAH;UACAA;UACAA;UACAA;UACAA;QACA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;UACA;UACAA;UACAA;UACAA;UACAA;QACA;UACAG;QACA;QAAA;QACAA;MACA;QAAAtC;QAAAU;MAAA;IACA;IACAkD,+BAqDA;IACAC;MACA;MACA;QACA1B;QACAA;QACA;MACA;IAgDA;IACA2B;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACApB;IACA;IACAqB;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACAhC;MACA;IACA;IACAiC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACAlC;MACA;IACA;IACAmC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA5C;QACA6C;QACAC;QACAC;QACA7B;UACA;UACA;UACAR;UACAV;YACA6B;YACAmB;YACAzF;YACA2D;cACA;gBACA;cACA;gBACA;cACA;cACAR;cACA;gBACAH;cACA;gBACAG;cACA;YACA;YACAuC;cACAvC;cACAA;YACA;UACA;QACA;QACAuC;QAAA;MAEA;IACA;IACAC;MACA;MACA;MACAxC;MACAV;QACA6B;QACAmB;QACAzF;QACA2D;UACAR;UACA;UACA;YACAH;UACA;YACAG;UACA;QACA;QACAuC;UACAvC;UACAA;QACA;MACA;IACA;IACAI;MACA;MACA;MACAP;MACA;MACA;QACAG;QACAH;QACA;MACA;MACA;QACAG;QACAH;QACA;MACA;MACA;QACA;UACAG;UACAH;UACA;QACA;MACA;MACAG;QAAAxC;MAAA;QACA;UACAwC;UAAA;QACA;MACA;MACA;MACA;QACAyC;QACA;UACA5C;UACAA;UACA6C;QACA;UACA7C;QACA;MACA;IACA;IACA8C,mCAkEA;IACAC,yCAaA;IACAC;MACA;MACA;QACA;QACA;QACA;UACA7C;UAAA;QACA;MACA;MACAH;IACA;IACAiD,sDAkDA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChwCA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=c3d8bbfa&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=c3d8bbfa&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    _vm.logintype > 0 &&\n    (_vm.logintype == 1 || _vm.logintype == 2 || _vm.logintype == 3) &&\n    _vm.xystatus == 1 &&\n    _vm.xyname2 &&\n    !_vm.loginset_data.xycolor\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload &&\n    _vm.logintype > 0 &&\n    (_vm.logintype == 1 || _vm.logintype == 2 || _vm.logintype == 3) &&\n    _vm.loginset_data.btntype == 1 &&\n    _vm.logintype == 3\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.logintype > 0 &&\n    (_vm.logintype == 1 || _vm.logintype == 2 || _vm.logintype == 3) &&\n    _vm.loginset_data.btntype == 1 &&\n    !(_vm.logintype == 3)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.logintype > 0 &&\n    _vm.logintype == 4 &&\n    _vm.loginset_data.btntype == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.logintype > 0 &&\n    _vm.logintype == 5 &&\n    _vm.loginset_data.btntype == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m5 =\n    _vm.isload &&\n    _vm.logintype > 0 &&\n    _vm.logintype == 6 &&\n    _vm.loginset_data.btntype == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m6 =\n    _vm.isload && _vm.showxieyi && _vm.reading_completed\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload && _vm.showxieyi && _vm.reading_completed\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m8 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1\") : null\n  var m9 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n    <!-- s -->\n    <view v-if=\"logintype>0\" style=\"width:100%;height: 100%;\">\n      <view class=\"bg_div1\" :style=\"loginset_data.bgtype==1?'background:'+loginset_data.bgcolor:'background:url('+loginset_data.bgimg+') no-repeat center;background-size:100% 100%'\">\n        <view style=\"overflow: hidden;\">\n          <view v-if=\"loginset_type==1\" style=\"width: 700rpx;margin: 0 auto;\">\n            <view class=\"title1\" :style=\"'color:'+loginset_data.titlecolor+';text-align: '+loginset_data.titletype\">\n              {{loginset_data.title}}\n            </view>\n            <view class=\"subhead1\" v-if=\"loginset_data.subhead\" :style=\"'color:'+loginset_data.subheadcolor+';text-align: '+loginset_data.titletype\">\n              {{loginset_data.subhead}}\n            </view>\n          </view>\n          \n          <view v-if=\"loginset_type==2\" class=\"logo2\">\n              <img :src=\"loginset_data.logo\" style=\"width: 100%;height: 100%;\">\n          </view>\n\n          <view class=\"content_div1\">\n            <form @submit=\"formSubmit\">\n              <view class=\"card_div1\" :style=\"'background:'+loginset_data.cardcolor\">\n\n                <view v-if=\"loginset_type==2\">\n                  <view class=\"title1\" :style=\"'color:'+loginset_data.titlecolor+';text-align:'+loginset_data.titletype+';margin-top: 20rpx;font-size: 40rpx;'\">\n                    {{loginset_data.title}}\n                  </view>\n                  <view class=\"subhead1\" v-if=\"loginset_data.subhead\" :style=\"'color:'+loginset_data.subheadcolor+';text-align:'+loginset_data.titletype\">\n                    {{loginset_data.subhead}}\n                  </view>\n                </view>\n                \n                <view v-if=\"logintype==1 || logintype==2\" class=\"tel1\">\n                   <input type=\"text\" name=\"tel\" value=\"\" @input=\"telinput\" class=\"input_val\" placeholder=\"请输入手机号码\" placeholder-style=\"color:#CACACA;line-height: 100rpx;\"  />\n                </view>\n                <view v-if=\"logintype==1\" class=\"tel1\">\n                   <input type=\"text\" name=\"pwd\" value=\"\" :password=\"true\"  placeholder=\"请输入密码\" placeholder-style=\"color:#CACACA;line-height: 100rpx;\" class=\"input_val\"/>\n                </view>\n\n                <view v-if=\"logintype==2\" class=\"tel1\">\n                  <input type=\"text\" name=\"smscode\" value=\"\" placeholder=\"请输入验证码\" placeholder-style=\"color:#CACACA;line-height: 100rpx;\" class=\"inputcode\" />\n                  <text class=\"code1\" :style=\"'color:'+loginset_data.codecolor\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</text>\n                </view>\n                <!-- logintype_2手机验证码登录 -->\n                <view v-if=\"logintype_2 && logintype==2 && reg_invite_code && !parent\" class=\"tel1\">\n                   <input type=\"text\" name=\"yqcode\" @input=\"yqinput\" :value=\"yqcode\" :placeholder=\"'请输入邀请人'+reg_invite_code_text\" placeholder-style=\"color:#CACACA;line-height: 100rpx;\" class=\"input_val\"/>\n                </view>\n                <view v-if=\"reg_invite_code && parent && reg_invite_code_show == 1 && logintype!=6\" class=\"tel1\" style=\"display: flex;padding-top: 8rpx;align-items: center;\">\n                  <view style=\"white-space: nowrap;\">邀请人：</view>\n                  <image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;margin-right: 20rpx;\"></image> \n                  <view class=\"overflow_ellipsis\">{{parent.nickname}} </view>\n                </view>\n\n                <view v-if=\"(logintype==1 || logintype==2 || logintype==3) && xystatus==1\" class=\"xycss1\">\n                  <checkbox-group @change=\"isagreeChange\" style=\"display: inline-block;position: relative;\">\n                      <checkbox style=\"transform: scale(0.6)\"  value=\"1\" :checked=\"isagree\"/>\n                      <text :style=\"'color:'+loginset_data.xytipcolor\">{{loginset_data.xytipword}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<view @click=\"promptRead\" v-if=\"xyagree_type == 1 && !reading_completed\" style=\"height:60rpx;width: 60rpx;position: absolute;top:0;\"></view>\n                  </checkbox-group>\n                  <text @tap=\"showxieyiFun\" :style=\"'color:'+loginset_data.xycolor\">{{xyname}}</text>\n                  <text @tap=\"showxieyiFun\"  v-if=\"xyname2\" :style=\"'color:'+loginset_data.xytipcolor\">和</text>\n                  <text @tap=\"showxieyiFun2\" v-if=\"xyname2\" :style=\"loginset_data.xycolor?'color:'+loginset_data.xycolor:'color:'+t('color1')\">{{xyname2}}</text>\n                </view>\n                \n                <view v-if=\"logintype==1 || logintype==2 || logintype==3\" style=\"margin-top: 40rpx;\">\n                  <block  v-if=\"loginset_data.btntype==1\">\n                    <button v-if=\"logintype==3\" @tap=\"authlogin\" :data-type=\"alih5?1:0\" class=\"btn1\"  :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\">\n                      <block v-if=\"!alih5\">\n                        授权登录\n                      </block>\n                      <block v-else>\n                        授权登录\n                      </block>\n                    </button>\n                    <button v-else class=\"btn1\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\" form-type=\"submit\">登录</button>\n                  </block>\n                  <block  v-if=\"loginset_data.btntype==2\">\n                    <button v-if=\"logintype==3\" @tap=\"authlogin\" :data-type=\"alih5?1:0\" class=\"btn1\"  :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\">\n                      <block v-if=\"!alih5\">\n                        授权登录\n                      </block>\n                      <block v-else>\n                        授权登录\n                      </block>\n                    </button>\n                    <button v-else class=\"btn1\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\" form-type=\"submit\">登录</button>\n                  </block>\n\n                  <button class=\"btn1\" @tap=\"noLogin\" v-if=\"!login_mast\" style=\"font-size: 28rpx;height: 50rpx;line-height: 50rpx;\">暂不登录</button>\n\n                  <button v-if=\"platform2 == 'ios' && logintype_4==true\" class=\"ioslogin-btn\" @tap=\"ioslogin\" style=\"width:100%\"><image :src=\"pre_url+'/static/img/apple.png'\" />通过Apple登录</button>\n                  <!-- #ifdef APP-PLUS -->\n                  <button v-if=\"logintype_6==true\" class=\"googlelogin-btn\" @tap=\"googlelogin\" style=\"width:100%\">Google登录</button>\n                  <!-- #endif -->\n                  <!-- #ifdef H5 -->\n                  <div v-if=\"logintype_6==true\" class=\"googlelogin-btn2\" id=\"googleloginBtn\" data-shape=\"circle\" style=\"width:100%\">Google登录</div>\n                  <!-- #endif -->\n                  \n                  <view v-if=\"logintype==1\" style=\"line-height: 50rpx;float: 24rpx;overflow: hidden;\">\n                    <text @tap=\"goto\" :data-url=\"'reg?frompage='+opt.frompage\" data-opentype=\"redirect\" :style=\"'color: '+loginset_data.regpwdbtncolor+';float:left'\">注册账号</text>\n                    <text @tap=\"goto\" data-url=\"getpwd\" data-opentype=\"redirect\" v-if=\"needsms\" :style=\"'color: '+loginset_data.regpwdbtncolor+';float:right'\">忘记密码</text>\n                  </view>\n                </view>\n                \n                <!-- 绑定手机号s -->\n                <block v-if=\"logintype==4\">\n                    <!-- #ifdef MP-WEIXIN -->\n                    <block  v-if=\"loginset_data.btntype==1\">\n                      <button open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\"  class=\"btn1\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\" >授权绑定手机号</button>\n                    </block>\n                    <block  v-if=\"loginset_data.btntype==2\">\n                      <button open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\"  class=\"btn1\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\" >授权绑定手机号</button>\n                    </block>\n                    <button class=\"btn1\" @tap=\"nobindregister\" v-if=\"login_bind==1\" style=\"background-color:#EEEEEE;font-size: 28rpx;\">暂不绑定</button>\n                    <!-- #endif -->\r\n                    <!-- #ifdef MP-BAIDU -->\r\n                    <block  v-if=\"loginset_data.btntype==1\">\r\n                      <button open-type=\"getPhoneNumber\" @getphonenumber=\"getBaiduPhoneNumber\"  class=\"btn1\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\" >授权绑定手机号</button>\r\n                    </block>\r\n                    <block  v-if=\"loginset_data.btntype==2\">\r\n                      <button open-type=\"getPhoneNumber\" @getphonenumber=\"getBaiduPhoneNumber\"  class=\"btn1\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\" >授权绑定手机号</button>\r\n                    </block>\r\n                    <button class=\"btn1\" @tap=\"nobindregister\" v-if=\"login_bind==1\" style=\"background-color:#EEEEEE;font-size: 28rpx;\">暂不绑定</button>\r\n                    <!-- #endif -->\n                    <!-- #ifndef MP-WEIXIN || MP-BAIDU -->\n                    <form @submit=\"bindregister\">\n                      <view style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\">绑定手机号</view>\n                      <view  class=\"tel1\" style=\"margin-top: 30rpx;\">\n                         <input type=\"text\" name=\"tel\" value=\"\" @input=\"telinput\" class=\"input_val\" placeholder=\"请输入手机号码\" placeholder-style=\"color:#CACACA;line-height: 100rpx;\"  />\n                      </view>\n                      <view  class=\"tel1\">\n                        <input type=\"text\" name=\"smscode\" value=\"\" placeholder=\"请输入验证码\" placeholder-style=\"color:#CACACA;line-height: 100rpx;\" class=\"inputcode\" />\n                        <text class=\"code1\" :style=\"'color:'+loginset_data.codecolor\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</text>\n                      </view>\n                      <block v-if=\"loginset_data.btntype==1\">\n                        <button class=\"btn1\" form-type=\"submit\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\">确定</button>\n                      </block>\n                      <block v-if=\"loginset_data.btntype==2\">\n                        <button class=\"btn1\" form-type=\"submit\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\">确定</button>\n                      </block>\n                      <button class=\"btn1\" @tap=\"nobindregister\" v-if=\"login_bind==1\" style=\"background-color:#EEEEEE ;font-size: 28rpx;\">暂不绑定</button>\n                    </form>\n                    <!-- #endif -->\n                </block>\n                <!-- 绑定手机号e -->\n\n                <!-- 设置头像昵称s -->\n                <block v-if=\"logintype==5\">\n                  <form @submit=\"setnicknameregister\">\n                    <view style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\">请设置头像昵称</view>\n                    <view class=\"loginform\" style=\"padding: 0;\">\n                      <!--  #ifdef MP-WEIXIN -->\n                      <view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n                        <view class=\"flex1\">头像</view>\n                        <button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" style=\"width:100rpx;height:100rpx;\" hover-class=\"none\">\n                          <image :src=\"headimg || default_headimg\" style=\"width:100%;height:100%;border-radius:50%\"></image>\n                        </button> \n                      </view>\n                      <view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n                        <view class=\"flex1\">昵称</view>\n                        <input type=\"nickname\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\n                      </view>\n                      <!-- #endif -->\n\t\t\t\t\t\t\t\t\t\t\t<!--  #ifdef MP-TOUTIAO -->\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex1\">头像</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<button  @tap=\"ttgetUserinfo\" style=\"width:100rpx;height:100rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" style=\"width:100%;height:100%;border-radius:50%\"></image>\n\t\t\t\t\t\t\t\t\t\t\t\t</button> \n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex1\">昵称</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<input @tap=\"ttgetUserinfo\" type=\"nickname\" class=\"input\" :value=\"nickname\" placeholder=\"请输入昵称\" name=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<!-- #endif -->\n                      <!--  #ifndef MP-WEIXIN  -->\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"platform !='wx' && platform !='toutiao'\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\" >\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex1\">头像</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" style=\"width:100rpx;height:100rpx;border-radius:50%\" @tap=\"uploadHeadimg\"></image>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex1\">昵称</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" :value=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t </view>\n                      <!-- #endif -->\n                      <block v-if=\"loginset_data.btntype==1\">\n                        <button class=\"btn1\" form-type=\"submit\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\">确定</button>\n                      </block>\n                      <block v-if=\"loginset_data.btntype==2\">\n                        <button class=\"btn1\" form-type=\"submit\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\">确定</button>\n                      </block>\n                      <button class=\"btn1\" @tap=\"nosetnicknameregister\" v-if=\"login_setnickname==1\" style=\"background-color:#EEEEEE ;font-size: 28rpx;\">暂不设置</button>\n                    </view>\n                  </form>\n                </block>\n                <!-- 设置头像昵称e -->\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 填写邀请码s -->\n\t\t\t\t\t\t\t\t<block v-if=\"logintype==6\">\n\t\t\t\t\t\t\t\t  <form @submit=\"setRegisterInvite\">\n\t\t\t\t\t\t\t\t    <view v-if=\"reg_invite_code && ((parent && reg_invite_code_show == 1) || !parent)\" style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\">请填写邀请码</view>\n\t\t\t\t\t\t\t\t    <view class=\"loginform\" style=\"padding: 0;\">\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"reg_invite_code && !parent\" class=\"form-item\">\n\t\t\t\t\t\t\t\t\t\t\t   <input type=\"text\" name=\"yqcode\" @input=\"yqinput\" :value=\"yqcode\" :placeholder=\"'请输入邀请人'+reg_invite_code_text\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" class=\"input\"/>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"reg_invite_code && parent && reg_invite_code_show == 1\" class=\"form-item\" style=\"display: flex;padding-top: 8rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t  <view style=\"white-space: nowrap;\">邀请人：</view>\n\t\t\t\t\t\t\t\t\t\t\t  <image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> \n\t\t\t\t\t\t\t\t\t\t\t  <view class=\"overflow_ellipsis\">{{parent.nickname}} </view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t      <block v-if=\"loginset_data.btntype==1\">\n\t\t\t\t\t\t\t\t        <button class=\"btn1\" form-type=\"submit\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\">确定</button>\n\t\t\t\t\t\t\t\t      </block>\n\t\t\t\t\t\t\t\t      <block v-if=\"loginset_data.btntype==2\">\n\t\t\t\t\t\t\t\t        <button class=\"btn1\" form-type=\"submit\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\">确定</button>\n\t\t\t\t\t\t\t\t      </block>\n\t\t\t\t\t\t\t\t      <button class=\"btn1\" @tap=\"setRegisterInvitePass\" v-if=\"reg_invite_code==1\" style=\"background-color:#EEEEEE ;font-size: 28rpx;\">暂不设置</button>\n\t\t\t\t\t\t\t\t    </view>\n\t\t\t\t\t\t\t\t  </form>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<!-- 填写邀请码e -->\n                \n                <!-- s -->\n                <block v-if=\"logintype==1 || logintype==2\">\n                  <view v-if=\"(logintype==1 && (logintype_2 || logintype_3)) || (logintype==2 && (logintype_1 || logintype_3))\" class=\"other_login\">\n                    <view style=\"display: flex;width: 100%;\">\n                      <view class=\"other_line\"></view>\n                      <view style=\"margin: 0 20rpx;color: #888888;white-space:nowrap;\">其他登录方式</view>\n                      <view  class=\"other_line\"></view>\n                    </view>\n                    <view class=\"other_content\">\n                      <!-- #ifdef MP-BAIDU -->\n                      <button v-if=\"logintype_3\" @login=\"weixinlogin\" open-type=\"login\" style=\"width:50%;margin: 0 auto;\">\n                        <view style=\"width:100rpx;height:104rpx;margin: 0 auto;\"><img :src=\"pre_url+'/static/img/login-'+platformimg+'.png'\" style=\"width:100rpx;height:100rpx;\"></view>\n                        <view style=\"font-size: 24rpx;line-height: 40rpx;\">{{platformname}}登录</view>\n                      </button>\n                      <!-- #endif -->\n                      <!-- #ifndef MP-BAIDU -->\r\n\t\t\t\t\t  <!-- 微信小程序登录 -->\n                      <button v-if=\"logintype_3 && platform == 'wx'\" @tap=\"weixinlogin\" style=\"width:50%;margin: 0 auto;\" hover-class=\"none\">\n                        <view style=\"width:100rpx;height:104rpx;margin: 0 auto;\"><img :src=\"wxformimgurl?wxformimgurl:pre_url+'/static/img/login-'+platformimg+'.png'\" style=\"width:100rpx;height:100rpx;\"></view>\n                        <view style=\"font-size: 24rpx;line-height: 40rpx;color: #888888;\">{{wxtext || '授权登录'}}</view>\n                      </button>\r\n\t\t\t\t\t  <!-- 微信小程序登录 end -->\n\t\t\t\t\t\t<button v-if=\"logintype_3 && platform != 'wx'\" @tap=\"weixinlogin\" style=\"width:50%;margin: 0 auto;\" hover-class=\"none\">\n\t\t\t\t\t\t  <view style=\"width:100rpx;height:104rpx;margin: 0 auto;\"><img :src=\"wxformimgurl?wxformimgurl:pre_url+'/static/img/login-'+platformimg+'.png'\" style=\"width:100rpx;height:100rpx;\"></view>\n\t\t\t\t\t\t  <view style=\"font-size: 24rpx;line-height: 40rpx;color: #888888;\">{{wxtext || platformname+'登录'}}</view>\n\t\t\t\t\t\t</button>\n                      <button v-if=\"logintype_7 && alih5\" @tap=\"alih5login\" style=\"width:50%;margin: 0 auto;\" hover-class=\"none\">\n                        <view style=\"width:100rpx;height:104rpx;margin: 0 auto;\"><img :src=\"pre_url+'/static/img/login-alipay.png'\" style=\"width:100rpx;height:100rpx;\"></view>\n                        <view style=\"font-size: 24rpx;line-height: 40rpx;color: #888888;\">支付宝登录</view>\n                      </button>\n                      <!-- #endif -->\n                      <view v-if=\"logintype==1 && logintype_2\" @tap=\"changelogintype\" data-type=\"2\" style=\"width:50%;margin: 0 auto;\">\n                        <view style=\"width:100rpx;height:104rpx;margin: 0 auto;\"><img :src=\"pre_url+'/static/img/reg-tellogin.png'\" style=\"width:100rpx;height:100rpx;\"></view>\n                        <view style=\"font-size: 24rpx;color: #888888;line-height: 40rpx;\">手机号快捷登录</view>\n                      </view>\n                      <view v-if=\"logintype==2 && logintype_1\" @tap=\"changelogintype\" data-type=\"1\" style=\"width:50%;margin: 0 auto;\">\n                        <view style=\"width:100rpx;height:104rpx;margin: 0 auto;\"><img :src=\"pre_url+'/static/img/reg-tellogin.png'\" style=\"width:100rpx;height:100rpx;\"></view>\n                        <view style=\"font-size: 24rpx;color: #888888;line-height: 40rpx;\">密码登录</view>\n                      </view>\n                    </view>\n                  </view>\n                </block>\n                <!-- e -->\n                \n              </view>\n            </form>\n          </view>\n        </view>\n      </view>\n    </view>\n    <!-- e -->\n\n\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\n\t\t\t<view class=\"xieyibox-content\">\n\t\t\t\t<scroll-view scroll-y style=\"height: 100%;\" @scrolltolower='xieyiTolower' class=\"myElement2\">\n\t\t\t\t\t<parse :content=\"xycontent\"></parse>\n\t\t\t\t\t<view style=\"width: 100%;height:1px;\" class=\"myElement\"></view>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"xieyibut-view flex-y-center\">\n\t\t\t\t\t<view class=\"but-class\" style=\"background:#A9A9A9\"  @tap=\"closeXieyi\">不同意并退出</view>\n\t\t\t\t\t<view class=\"but-class\" :style=\"{background:reading_completed ? 'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)':'#A9A9A9'}\"  @tap=\"hidexieyi\">已阅读并同意</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"showxieyi2\" class=\"xieyibox\">\n\t\t\t<view class=\"xieyibox-content\">\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\n\t\t\t\t\t<parse :content=\"xycontent2\"></parse>\n\t\t\t\t</view>\n\t\t\t\t<view  class=\"xieyibut-view flex-y-center\">\n\t\t\t\t\t<view class=\"but-class\" style=\"background:#A9A9A9\"  @tap=\"closeXieyi\">不同意并退出</view>\n\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi2\">已阅读并同意</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tplatform2:app.globalData.platform2,\n\t\t\t\n\t\t\tplatform:'',\n\t\t\tplatformname:'',\n\t\t\tplatformimg:'weixin',\n\t\t\tlogintype:0,//1注册登录，2手机验证码登录，3授权登录，4绑定手机号，5设置头像昵称，6输入邀请码\n\t\t\tlogintype_1:true,//注册登录\n\t\t\tlogintype_2:false,//手机验证码登录\n\t\t\tlogintype_3:false,//授权登录\n\t\t\tlogintype_4:false,//Apple登录\n\t\t\tlogintype_6:false,//Google登录\n      logintype_7:false,//支付宝内h5\n\t\t\tisioslogin:false,\n\t\t\tisgooglelogin:false,\n\t\t\tgoogle_client_id:'',\n\t\t\tneedsms:false,\n\t\t\tlogo:'',\n\t\t\tname:'',\n\t\t\txystatus:0,\n\t\t\txyagree_type:0,\n\t\t\txyname:'',\n\t\t\txycontent:'',\n\t\t\txyname2:'',\n\t\t\txycontent2:'',\n\t\t\tshowxieyi:false,\n\t\t\tshowxieyi2:false,\n\t\t\tisagree:false,\n      smsdjs: '',\n\t\t\ttel:'',\n      hqing: 0,\n\t\t\tfrompage:'/pages/my/usercenter',\n\t\t\twxloginclick:false,\n\t\t\tiosloginclick:false,\n\t\t\tgoogleloginclick:false,\n\t\t\tlogin_bind:0,\n\t\t\tlogin_setnickname:0,\n\t\t\tlogin_mast:false,\r\n\t\t\treg_invite_code:0,//邀请码 1开启 0关闭 2强制邀请\r\n\t\t\treg_invite_code_text:'',//邀请码文字描述\n\t\t\treg_invite_code_show:1,//有邀请人时登录、注册页面是否显示邀请码和邀请人\n\t\t\tyqcode:'',//邀请码\n      parent:{},\n\t\t\ttmplids:[],\n\t\t\tdefault_headimg:app.globalData.pre_url + '/static/img/touxiang.png',\n\t\t\theadimg:'',\n\t\t\tnickname:'',\n      loginset_type:0,\n      loginset_data:'',\n\n      alih5:false,\n      ali_appid:'',\n      alih5loginclick:false,\n      rs_notlogin_to_business:'',\n      reading_completed:false,\n\t\t\tchecknickname:0,\r\n\t\t\twxformimgurl:\"\",//设计登陆中的微信图标\r\n\t\t\twxtext:''//设计登陆中的微信登录文字\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tif(this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);\n    if(app.globalData.qrcode){\n      var frompage = '/pagesA/qrcode/index?code='+app.globalData.qrcode;\n      this.frompage = decodeURIComponent(frompage);\n    }\n\t \n\t\t// #ifdef H5\n\t\tif (navigator.userAgent.indexOf('AlipayClient') > -1) {\n\t\t  this.alih5 = true;\n\t\t}\n\t\t// #endif\n\t\tif(this.opt.logintype) this.logintype = this.opt.logintype;\n\t\tif(this.opt.login_bind) this.login_bind = this.opt.login_bind;\n\t\tif(this.opt.checknickname) this.checknickname = this.opt.checknickname;\n\t\tthis.getdata();\n  },\n\tonShow:function() {\n\t\tif(app.globalData.platform=='wx' && app.globalData.hide_home_button==1){\n\t\t\tuni.hideHomeButton();\n\t\t}\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetHeight() {\n\t\t\tlet that = this;\n\t\t\tsetTimeout(() => {\n\t\t\t\tlet ceshixian = '';\n\t\t\t\tlet heziheight = '';\n\t\t\t\tuni.createSelectorQuery().select('.myElement').boundingClientRect(rect => {\n\t\t\t\t\tceshixian = rect.top;\n\t\t\t\t}).exec()\n\t\t\t\tuni.createSelectorQuery().select('.myElement2').boundingClientRect(rect => {\n\t\t\t\t\theziheight = rect.height;\n\t\t\t\t\tceshixian = ceshixian - rect.top;\n\t\t\t\t\tif(Number(ceshixian) <= Number(heziheight)){\n\t\t\t\t\t\tthat.reading_completed = true;\n\t\t\t\t\t}\n\t\t\t\t}).exec()\n\t\t\t},800)\n\t\t},\n\t\txieyiTolower(){\n\t\t\tthis.reading_completed = true;\n\t\t},\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiIndex/login', {pid:app.globalData.pid,checknickname:that.checknickname}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif(res.status == 0){\n\t\t\t\t\tapp.alert(res.mg);return;\n\t\t\t\t}\n\t\t\t\t//暂不登录\n\t\t\t\tthat.rs_notlogin_to_business = res.rs_notlogin_to_business;\n        if(res.loginset_type){\n          that.loginset_type = res.loginset_type\n        }\n        if(res.loginset_data){\n          that.loginset_data = res.loginset_data\n        }\n\t\t\t\tthat.logintype_1 = res.logintype_1;\n\t\t\t\tthat.logintype_2 = res.logintype_2;\n\t\t\t\tthat.logintype_3 = res.logintype_3;\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif(that.platform2 == 'ios'){\n\t\t\t\t\tif (plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {\n\t\t\t\t\t\tconsole.log('已安装微信')\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthat.logintype_3 = false;\n\t\t\t\t\t\tconsole.log('未安装微信')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\tthat.logintype_4 = res.logintype_4;\n\t\t\t\tthat.logintype_6 = res.logintype_6 || false;\n        that.logintype_7 = res.logintype_7 || false;\n\t\t\t\tthat.google_client_id = res.google_client_id || '';\n\t\t\t\tthat.login_mast = res.login_mast;\n\t\t\t\tthat.needsms = res.needsms;\n        that.reg_invite_code = res.reg_invite_code;\n        that.reg_invite_code_text = res.reg_invite_code_text;\r\n\t\t\t\tthat.reg_invite_code_show = res.reg_invite_code_show;\n        that.parent = res.parent;\n\t\t\t\tif(that.logintype==0){\n\t\t\t\t\tif(that.logintype_1){\n\t\t\t\t\t\tthat.logintype = 1;//注册登录\n\t\t\t\t\t}else if(that.logintype_2){\n\t\t\t\t\t\tthat.logintype = 2;//手机验证码登录\n\t\t\t\t\t}else if(that.logintype_3){\n\t\t\t\t\t\tthat.logintype = 3;//授权登录\n\t\t\t\t\t}else if(that.logintype_7 && that.alih5 ){\n            that.logintype = 3;\n          }\n\t\t\t\t}\n\t\t\t\tthat.xystatus = res.xystatus;\n\t\t\t\tthat.xyagree_type = res.xyagree_type ? res.xyagree_type:0;\n\t\t\t\tif(!that.xyagree_type) that.reading_completed = true;\n\t\t\t\tthat.xyname = res.xyname;\n\t\t\t\tthat.xycontent = res.xycontent;\n\t\t\t\tthat.xyname2 = res.xyname2;\n\t\t\t\tthat.xycontent2 = res.xycontent2;\n\t\t\t\tthat.logo = res.logo;\n\t\t\t\tthat.name = res.name;\n\t\t\t\tthat.platform = res.platform;\n\t\t\t\tif(that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app'){\n\t\t\t\t\tthat.platformname = '微信';\n\t\t\t\t\tthat.platformimg = 'weixin';\r\n\t\t\t\t\t//重置微信图标和文字\r\n\t\t\t\t\tif(that.loginset_data.wxicon !=''){\r\n\t\t\t\t\t\tthat.wxformimgurl = that.loginset_data.wxicon\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.loginset_data.wxtext !=''){\r\n\t\t\t\t\t\tthat.wxtext = that.loginset_data.wxtext\r\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'toutiao'){\n\t\t\t\t\tthat.platformname = '抖音';\n\t\t\t\t\tthat.platformimg = 'toutiao';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'alipay'){\n\t\t\t\t\tthat.platformname = '支付宝';\n\t\t\t\t\tthat.platformimg = 'alipay';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'qq'){\n\t\t\t\t\tthat.platformname = 'QQ';\n\t\t\t\t\tthat.platformimg = 'qq';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'baidu'){\n\t\t\t\t\tthat.platformname = '百度';\n\t\t\t\t\tthat.platformimg = 'baidu';\n\t\t\t\t}\n        if(res.ali_appid){\n          that.ali_appid = res.ali_appid\n        }\r\n\t\tif(res.nickname)that.nickname = res.nickname;\r\n\t\tif(res.headimg)that.headimg = res.headimg;\r\n\t\t\n\t\t\t\tthat.loaded();\n\t\t\t\t// #ifdef H5\n\t\t\t\tif(that.logintype_6){\n\t\t\t\t\tvar hm = document.createElement('script');\n\t\t\t\t\thm.src=\"https://accounts.google.com/gsi/client\";\n\t\t\t\t\tdocument.body.appendChild(hm);\n\t\t\t\t\tsetTimeout(function(){\n\t\t\t\t\t\tgoogle.accounts.id.initialize({\n\t\t\t\t\t\t\tclient_id: that.google_client_id,\n\t\t\t\t\t\t\tcallback: function(response){\n\t\t\t\t\t\t\t\tconsole.log(response);\n\t\t\t\t\t\t\t\tvar credential = response.credential;\n\t\t\t\t\t\t\t\tvar base64Url = credential.split('.')[1];\n\t\t\t\t\t\t\t\tvar base64 = base64Url.replace(/-/g,'+').replace(/_/g,'/');\n\t\t\t\t\t\t\t\tvar jsonPayload = decodeURIComponent(\n\t\t\t\t\t\t\t\t\twindow.atob(base64).split('').map(function(c){\n\t\t\t\t\t\t\t\t\t\treturn '%'+('00'+c.charCodeAt(0).toString(16)).slice(-2);\n\t\t\t\t\t\t\t\t\t}).join('')\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\tvar resdata = JSON.parse(jsonPayload);\n\t\t\t\t\t\t\t\tresdata.openId = resdata.sub;\n\t\t\t\t\t\t\t\tconsole.log(resdata);\n\t\t\t\t\t\t\t\tapp.showLoading('提交中');\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/googlelogin',{userInfo:resdata,pid:app.globalData.pid},function(res2){\n\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\tconsole.log(res2);\n\t\t\t\t\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('frompage')\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(that.frompage)\n\t\t\t\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 3) {\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 5;\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\n\t\t\t\t\t\t\t\t\t\tthat.login_setnickname = res2.login_setnickname;\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 2) {\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 4;\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tgoogle.accounts.id.renderButton(\n\t\t\t\t\t\t\tdocument.getElementById(\"googleloginBtn\"),\n\t\t\t\t\t\t\t{ theme: \"outline\", size: \"large\",width:'300'}  // customization attributes\n\t\t\t\t\t\t);\n\t\t\t\t\t\tgoogle.accounts.id.prompt();\n\t\t\t\t\t},500);\n\t\t\t\t}else if(that.logintype_7 && that.alih5){\n          const oScript = document.createElement('script');\n          oScript.type = 'text/javascript';\n          oScript.src = 'https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js';\n          document.body.appendChild(oScript);\n        }\n\t\t\t\t// #endif\n\t\t\t});\n\t\t},\n    formSubmit: function (e) {\n\t\t\tvar that = this;\n      var formdata = e.detail.value;\n      if (formdata.tel == ''){\n        app.alert('请输入手机号');\n        return;\n      }\n\t\t\tif(!app.isPhone(formdata.tel)){\n\t\t\t\treturn app.alert(\"请输入正确的手机号\");\n\t\t\t}\n\t\t\tif(that.logintype == 1){\n\t\t\t\tif (formdata.pwd == '') {\n\t\t\t\t\tapp.alert('请输入密码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(that.logintype == 2){\n\t\t\t\tif (formdata.smscode == '') {\n\t\t\t\t\tapp.alert('请输入短信验证码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n      if(that.logintype == 1 || that.logintype == 2){\n        if (that.xystatus == 1 && !that.isagree) {\n        \tapp.error('请先阅读并同意用户注册协议');\n        \treturn false;\n        }\n      }\n\n\t\t\tif(that.logintype == 4){\n\t\t\t\tif (typeof(formdata.pwd) != 'undefined' && formdata.pwd == '') {\n\t\t\t\t\tapp.alert('请输入密码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (typeof(formdata.smscode) != 'undefined' && formdata.smscode == '') {\n\t\t\t\t\tapp.alert('请输入短信验证码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tapp.showLoading('提交中');\n      app.post(\"ApiIndex/loginsub\", {tel:formdata.tel,pwd:formdata.pwd,smscode:formdata.smscode,logintype:that.logintype,pid:app.globalData.pid,yqcode:formdata.yqcode,regbid:app.globalData.regbid}, function (res) {\n\t\t\t\tapp.showLoading(false);\n        if (res.status == 1) {\n          app.success(res.msg);\n\t\t\t\t\tif(res.tmplids){\n\t\t\t\t\t\tthat.tmplids = res.tmplids;\n\t\t\t\t\t}\n\t\t\t\t\tthat.subscribeMessage(function () {\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t\tconsole.log(that.frompage,'001')\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n        } else {\n          app.error(res.msg);\n        }\n      });\n    },\n\t\tgetPhoneNumber: function (e) {\n\t\t\tvar that = this\n\t\t\tconsole.log(e);\n\t\t\tif(e.detail.errMsg == \"getPhoneNumber:fail user deny\"){\n\t\t\t\tapp.error('请同意授权获取手机号');return;\n\t\t\t}\n\t\t\tif(!e.detail.iv || !e.detail.encryptedData){\n\t\t\t\tapp.error('请同意授权获取手机号');return;\n\t\t\t}\n\t\t\twx.login({success (res1){\n\t\t\t\tconsole.log(res1);\n\t\t\t\tvar code = res1.code;\n\t\t\t\t//用户允许授权\n\t\t\t\tapp.post('ApiIndex/wxRegister',{ headimg:that.headimg,nickname:that.nickname,iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid,yqcode:that.yqcode,regbid:app.globalData.regbid},function(res2){\n\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t\tif(res2.tmplids){\n\t\t\t\t\t\t\tthat.tmplids = res2.tmplids;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.subscribeMessage(function () {\n\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t})\n\t\t\t}});\n\t\t},\n\t\tsetnicknameregister:function(e){\n\t\t\t//console.log(e);\n\t\t\t//return;\n\t\t\tthis.nickname = e.detail.value.nickname;\n\t\t\tif(this.nickname == '' || this.headimg == ''){\n\t\t\t\tapp.alert('请设置头像和昵称');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(this.login_bind!=0){\n\t\t\t\tthis.logintype = 4;\n\t\t\t\tthis.isioslogin = false;\n\t\t\t\tthis.isgooglelogin = false;\n\t\t\t}else{\n\t\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t\t}\n\t\t},\n\t\tnosetnicknameregister:function(){\n\t\t\tthis.nickname = '';\n\t\t\tthis.headimg = '';\n\t\t\tif(this.login_bind!=0){\n\t\t\t\tthis.logintype = 4;\n\t\t\t\tthis.isioslogin = false;\n\t\t\t\tthis.isgooglelogin = false;\n\t\t\t}else{\n\t\t\t\tthis.register('','','','');\n\t\t\t}\n\t\t},\n\t\tsetRegisterInvite:function(e){\n\t\t\tconsole.log(e);\n\t\t\t//return;\n\t\t\tthis.yqcode = e.detail.value.yqcode;\n\t\t\tif(this.yqcode == '' && !app.globalData.pid){\n\t\t\t\tapp.alert('请输入邀请码');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(this.login_setnickname!=0){\n\t\t\t\tthis.logintype = 5;\n\t\t\t\tthis.isioslogin = false;\n\t\t\t\tthis.isgooglelogin = false;\n\t\t\t}else{\n\t\t\t\tif(this.login_bind!=0){\n\t\t\t\t\tthis.logintype = 4;\n\t\t\t\t\tthis.isioslogin = false;\n\t\t\t\t\tthis.isgooglelogin = false;\n\t\t\t\t}else{\n\t\t\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tsetRegisterInvitePass:function(){\n\t\t\tif(this.login_setnickname!=0){\n\t\t\t\tthis.logintype = 5;\n\t\t\t\tthis.isioslogin = false;\n\t\t\t\tthis.isgooglelogin = false;\n\t\t\t}else{\n\t\t\t\tif(this.login_bind!=0){\n\t\t\t\t\tthis.logintype = 4;\n\t\t\t\t\tthis.isioslogin = false;\n\t\t\t\t\tthis.isgooglelogin = false;\n\t\t\t\t}else{\n\t\t\t\t\tthis.register('','','','');\n\t\t\t\t}\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tbindregister:function(e){\n\t\t\tvar that = this;\n\t\t\tvar formdata = e.detail.value;\n      if (formdata.tel == ''){\n        app.alert('请输入手机号');\n        return;\n      }\n\t\t\tif (formdata.smscode == '') {\n\t\t\t\tapp.alert('请输入短信验证码');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.register(this.headimg,this.nickname,formdata.tel,formdata.smscode);\n\t\t},\n\t\tnobindregister:function(){\n\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t},\n\t\tregister:function(headimg,nickname,tel,smscode){\n\t\t\tvar that = this;\n\t\t\tvar url = '';\n\t\t\tif(that.platform == 'app') {\n\t\t\t\turl = 'ApiIndex/appwxRegister';\n\t\t\t\tif(that.isioslogin){\n\t\t\t\t\turl = 'ApiIndex/iosRegister';\n\t\t\t\t}\n\t\t\t} else if(that.platform=='mp' || that.platform=='h5') {\n\t\t\t\turl = 'ApiIndex/shouquanRegister';\n\t\t\t} else  {\n\t\t\t\turl = 'ApiIndex/'+that.platform+'Register';\n\t\t\t}\n\t\t\tif(that.isgooglelogin){\n\t\t\t\turl = 'ApiIndex/googleRegister';\n\t\t\t}\n\t\t\tapp.post(url,{headimg:headimg,nickname:nickname,tel:tel,smscode:smscode,pid:app.globalData.pid,yqcode:that.yqcode,regbid:app.globalData.regbid},function(res2){\n\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\tif(that.checknickname == 1){\n\t\t\t\t\t\tapp.success('设置成功');\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\tif(res2.tmplids){\n\t\t\t\t\t\tthat.tmplids = res2.tmplids;\n\t\t\t\t\t}\n\t\t\t\t\tthat.subscribeMessage(function () {\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t});\n\t\t},\n\t\tauthlogin:function(e){\n\t\t\tvar that = this;\n      var type = e.currentTarget.dataset.type;\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\n\t\t\t\tapp.error('请先阅读并同意用户注册协议');\n\t\t\t\treturn false;\n\t\t\t}\n      if(!type){\n        that.weixinlogin();\n      }else{\n        that.alih5login();\n      }\n\t\t},\n\t\tweixinlogin:function(){\n\t\t\tvar that = this;\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\n\t\t\t\tthat.showxieyi = true;\n\t\t\t\tthat.wxloginclick = true;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconsole.log('weixinlogin')\n\t\t\tapp.showLoading('授权中');\n\t\t\tthat.wxloginclick = false;\n\t\t\tapp.authlogin(function(res){\n\t\t\t\tconsole.log(res);\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tconsole.log('frompage')\n\t\t\t\t\t\tconsole.log(that.frompage)\n\t\t\t\t\t\tapp.goto( that.frompage,'redirect');\n\t\t\t\t\t}, 1000);\n\t\t\t\t\t\n\t\t\t\t} else if (res.status == 4) {\n\t\t\t\t\t//填写邀请码\n\t\t\t\t\tthat.logintype = 6;\n\t\t\t\t\tthat.login_setnickname = res.login_setnickname;\n\t\t\t\t\tthat.login_bind = res.login_bind;//1可选设置\n\t\t\t\t\tthat.isioslogin = false;\n\t\t\t\t\tthat.isgooglelogin = false;\n\t\t\t\t} else if (res.status == 3) {\n\t\t\t\t\t//设置头像昵称\n\t\t\t\t\tthat.logintype = 5;\n\t\t\t\t\tthat.login_setnickname = res.login_setnickname;\n\t\t\t\t\tthat.login_bind = res.login_bind;//1可选设置\n\t\t\t\t\tthat.isioslogin = false;\n\t\t\t\t\tthat.isgooglelogin = false;\n\t\t\t\t} else if (res.status == 2) {\n\t\t\t\t\t//绑定手机\n\t\t\t\t\tthat.logintype = 4;\n\t\t\t\t\tthat.login_bind = res.login_bind;\n\t\t\t\t\tthat.isioslogin = false;\n\t\t\t\t\tthat.isgooglelogin = false;\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t};\n\t\t\t\tapp.showLoading(false);\n\t\t\t},{frompage:that.frompage,yqcode:that.yqcode});\n\t\t},\n\t\tioslogin:function(){\n\t\t\t//#ifdef APP-PLUS\n\t\t\tvar that = this;\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\n\t\t\t\tthat.showxieyi = true;\n\t\t\t\tthat.iosloginclick = true;\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tuni.login({  \n\t\t\t\tprovider: 'apple',  \n\t\t\t\tsuccess: function (loginRes) {  \n\t\t\t\t\tconsole.log(loginRes);\n\t\t\t\t\t// 登录成功  \n\t\t\t\t\tuni.getUserInfo({  \n\t\t\t\t\t\tprovider: 'apple',  \n\t\t\t\t\t\tsuccess(res) { \n\t\t\t\t\t\t\t// 获取用户信息成功\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tif(res.userInfo && res.userInfo.openId){\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/ioslogin',{userInfo:res.userInfo,pid:app.globalData.pid,regbid:app.globalData.regbid},function(res2){\n\t\t\t\t\t\t\t\t\tconsole.log(res2);\n\t\t\t\t\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('frompage')\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(that.frompage)\n\t\t\t\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 3) {\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 5;\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = true;\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = false;\n\t\t\t\t\t\t\t\t\t\tthat.login_setnickname = res2.login_setnickname\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 2) {\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 4;\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = true;\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = false;\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}  \n\t\t\t\t\t})  \n\t\t\t\t},  \n\t\t\t\tfail: function (err) {  \n\t\t\t\t\tconsole.log(err);\n\t\t\t\t\tapp.error('登录失败');\n\t\t\t\t}  \n\t\t\t});\n\t\t\t//#endif\n\t\t},\n\t\tgooglelogin:function(){\n\t\t\tvar that = this;\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\n\t\t\t\tthat.showxieyi = true;\n\t\t\t\tthat.googleloginclick = true;\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t// #ifdef APP-PLUS\n\t\t\tuni.login({  \n\t\t\t\tprovider: 'google',  \n\t\t\t\tsuccess: function (loginRes) {  \n\t\t\t\t\tconsole.log(loginRes);\n\t\t\t\t\t// 登录成功  \n\t\t\t\t\tuni.getUserInfo({  \n\t\t\t\t\t\tprovider: 'google',  \n\t\t\t\t\t\tsuccess(res) { \n\t\t\t\t\t\t\t// 获取用户信息成功\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\t//alert(JSON.stringify(res));\n\t\t\t\t\t\t\t//if(res.userInfo && res.userInfo.openId){\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/googlelogin',{userInfo:res.userInfo,pid:app.globalData.pid,regbid:app.globalData.regbid},function(res2){\n\t\t\t\t\t\t\t\t\tconsole.log(res2);\n\t\t\t\t\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('frompage')\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(that.frompage)\n\t\t\t\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 3) {\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 5;\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\n\t\t\t\t\t\t\t\t\t\tthat.login_setnickname = res2.login_setnickname\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 2) {\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 4;\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t//}\n\t\t\t\t\t\t}  \n\t\t\t\t\t})  \n\t\t\t\t},  \n\t\t\t\tfail: function (err) {  \n\t\t\t\t\tconsole.log(err);\n\t\t\t\t\tapp.error('登录失败');\n\t\t\t\t}  \n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\tchangelogintype:function(e){\n\t\t\tvar logintype = e.currentTarget.dataset.type\n\t\t\tthis.logintype = logintype;\n\t\t},\n\t\tpromptRead(){\n\t\t\tif(this.xyagree_type) app.error('请滑动到底部，阅读完协议！');\n\t\t\tthis.showxieyi = true;\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.getHeight()\n\t\t\t})\n\t\t},\n    isagreeChange: function (e) {\n      var val = e.detail.value;\n      if (val.length > 0) {\n        this.isagree = true;\n      } else {\n        this.isagree = false;\n      }\n      console.log(this.isagree);\n    },\n    showxieyiFun: function () {\n      this.showxieyi = true;\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.getHeight()\n\t\t\t})\n    },\n\t\t// 不同意协议\n\t\tcloseXieyi(){\n\t\t\tthis.showxieyi = false;\n\t\t\tthis.showxieyi2 = false;\n\t\t\tthis.isagree = false;\n\t\t},\n    hidexieyi: function () {\n\t\t\tif(!this.reading_completed) return app.error('请滑动到底部，阅读完协议！')\n      this.showxieyi = false;\n\t\t\tthis.isagree = true;\n\t\t\tif(this.wxloginclick){\n\t\t\t\tthis.weixinlogin();\n\t\t\t}\n\t\t\tif(this.iosloginclick){\n\t\t\t\tthis.ioslogin();\n\t\t\t}\n\t\t\tif(this.googleloginclick){\n\t\t\t\tthis.googlelogin();\n\t\t\t}\n      if(this.alih5loginclick){\n        that.alih5login();\n      }\n    },\n    showxieyiFun2: function () {\n      this.showxieyi2 = true;\n    },\n    hidexieyi2: function () {\n      this.showxieyi2 = false;\n\t\t\tthis.isagree = true;\n\t\t\tif(this.wxloginclick){\n\t\t\t\tthis.weixinlogin();\n\t\t\t}\n\t\t\tif(this.iosloginclick){\n\t\t\t\tthis.ioslogin();\n\t\t\t}\n\t\t\tif(this.googleloginclick){\n\t\t\t\tthis.googlelogin();\n\t\t\t}\n      if(this.alih5loginclick){\n        that.alih5login();\n      }\n    },\n    telinput: function (e) {\n      this.tel = e.detail.value\n    },\n\t\tyqinput: function (e) {\n      this.yqcode = e.detail.value\n    },\n\t\tuploadHeadimg:function(){\n\t\t\tvar that = this;\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tvar tempFilePaths = res.tempFilePaths;\n\t\t\t\t\tvar tempFilePath = tempFilePaths[0];\n\t\t\t\t\tapp.showLoading('上传中');\n\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\n\t\t\t\t\t\tfilePath: tempFilePath,\n\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif(typeof res.data == 'string'){\n\t\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tvar data = res.data;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\tthat.headimg = data.url;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: function(res) { //alert(res.errMsg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tonChooseAvatar:function(e){\n\t\t\t// console.log(e)\n\t\t\tvar that = this;\n\t\t\tapp.showLoading('上传中');\n\t\t\tuni.uploadFile({\n\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\n\t\t\t\tfilePath: e.detail.avatarUrl,\n\t\t\t\tname: 'file',\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\tthat.headimg = data.url;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: function(res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    smscode: function () {\n      var that = this;\n      if (that.hqing == 1) return;\n      that.hqing = 1;\n      var tel = that.tel;\n      if (tel == '') {\n        app.alert('请输入手机号码');\n        that.hqing = 0;\n        return false;\n      }\n      if (!app.isPhone(tel)) {\n        app.alert(\"手机号码有误，请重填\");\n        that.hqing = 0;\n        return false;\n      }\r\n\t\t\tif(that.logintype == 1 || that.logintype == 2){\r\n\t\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\t\tapp.error('请先阅读并同意用户注册协议');\r\n\t\t\t\t\tthat.hqing = 0;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\n      app.post(\"ApiIndex/sendsms\", {tel: tel}, function (data) {\n        if (data.status != 1) {\n          app.alert(data.msg);return;\n        }\n      });\n      var time = 120;\n      var interval1 = setInterval(function () {\n        time--;\n        if (time < 0) {\n          that.smsdjs = '重新获取';\n          that.hqing = 0;\n          clearInterval(interval1);\n        } else if (time >= 0) {\n          that.smsdjs = time + '秒';\n        }\n      }, 1000);\n    },\n    alih5login:function(){\n      // #ifdef H5\n      var that = this;\n      var ali_appid = that.ali_appid;\n    \n      if (that.xystatus == 1 && !that.isagree) {\n      \tthat.showxieyi = true;\n      \tthat.alih5loginclick = true;\n      \treturn;\n      }\n      that.alih5loginclick = false;\n      if(ali_appid){\n        app.showLoading('登录中');\n        ap.getAuthCode ({\n            appId :  ali_appid ,\n            scopes : ['auth_base'],\n        },function(res){\n           //var res = JSON.stringify(res);\n            if(!res.error && res.authCode){\n                app.post('ApiIndex/alipaylogin', {\n                \tcode: res.authCode,\n                \tpid: app.globalData.pid,\n                  platform:\"h5\"\n                }, function(res2) {\n                  app.showLoading(false);\n                  \n                \tif (res2.status == 1) {\n                \t\tapp.success(res2.msg);\n                \t\tsetTimeout(function () {\n                \t\t\tconsole.log('frompage')\n                \t\t\tconsole.log(that.frompage)\n                \t\t\tapp.goto(that.frompage,'redirect');\n                \t\t}, 1000);\n                \t} else if (res2.status == 3) {\n                \t\tthat.logintype = 5;\n                \t\tthat.isioslogin = true;\n                \t\tthat.isgooglelogin = false;\n                \t\tthat.login_setnickname = res2.login_setnickname\n                \t\tthat.login_bind = res2.login_bind\n                \t} else if (res2.status == 2) {\n                \t\tthat.logintype = 4;\n                \t\tthat.isioslogin = true;\n                \t\tthat.isgooglelogin = false;\n                \t\tthat.login_bind = res2.login_bind\n                \t} else {\n                \t\tapp.error(res2.msg);\n                \t}\n                });\n            }else{\n              app.showLoading(false);\n              \n              if(res.errorMessage){\n                app.alert(res.errorMessage);\n              }else if(res.errorDesc){\n                app.alert(res.errorDesc);\n              }else{\n                app.alert('授权出错');\n              }\n              return\n            }\n        });\n      }else{\n        app.alert('系统未配置支付宝参数');\n        return\n      }\n      // #endif\n    },\n\t\tttgetUserinfo(){\n\t\t\t// #ifdef MP-TOUTIAO\n\t\t\tvar that = this;\n\t\t\ttt.getUserProfile({\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tthat.nickname = res.userInfo.nickName;\n\t\t\t\t\tthat.headimg = res.userInfo.avatarUrl;\n\t\t\t\t},\n\t\t\t\tfail(res) {\n\t\t\t\t\tconsole.log(\"getUserProfile 调用失败\", res);\n\t\t\t\t},\n\t\t\t});\n      // #endif\n\t\t},\n\t\tnoLogin(){\n\t\t\tvar that = this;\n\t\t\tif(that.rs_notlogin_to_business==1){\n\t\t\t\tvar url = that.frompage.split('?')[0];\n\t\t\t\tvar params = app.getparams(that.frompage);\n\t\t\t\tif(url=='/restaurant/shop/index' && params.bid && params.bid > 0){\n\t\t\t\t\tapp.goto('/pagesExt/business/index?id='+params.bid);return;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthat.goback();\n\t\t},\r\n    getBaiduPhoneNumber: function (e) {\r\n      // #ifdef MP-BAIDU\r\n    \tvar that = this\r\n    \tconsole.log(e);\r\n    \tif(e.detail.errMsg == \"getPhoneNumber:fail auth deny\"){\r\n    \t\tapp.error('请同意授权获取手机号');return;\r\n    \t}\r\n    \tif(!e.detail.iv || !e.detail.encryptedData){\r\n    \t\tapp.error('请同意授权获取手机号');return;\r\n    \t}\r\n      uni.getLoginCode({\r\n      \tsuccess: res => {\r\n      \t\tconsole.log(res);\r\n      \t\tvar code = res.code;\r\n      \t\t//用户允许授权\r\n      \t\tvar postdata = { \r\n      \t\t  headimg:that.headimg,\r\n      \t\t  nickname:that.nickname,\r\n      \t\t  code:code,\r\n      \t\t  iv: e.detail.iv,\r\n      \t\t  encryptedData:e.detail.encryptedData,\r\n      \t\t  pid:app.globalData.pid,\r\n      \t\t  yqcode:that.yqcode,\r\n      \t\t  regbid:app.globalData.regbid,\r\n      \t\t}\r\n      \t\tapp.post('ApiIndex/baiduRegister',postdata,function(res2){\r\n      \t\t\tif (res2.status == 1) {\r\n      \t\t\t\tapp.success(res2.msg);\r\n      \t\t\t\tif(res2.tmplids){\r\n      \t\t\t\t\tthat.tmplids = res2.tmplids;\r\n      \t\t\t\t}\r\n      \t\t\t\tthat.subscribeMessage(function () {\r\n      \t\t\t\t\tsetTimeout(function () {\r\n      \t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n      \t\t\t\t\t}, 1000);\r\n      \t\t\t\t});\r\n      \t\t\t} else {\r\n      \t\t\t\tapp.error(res2.msg);\r\n      \t\t\t}\r\n      \t\t\treturn;\r\n      \t\t})\r\n      \t},\r\n      \tfail: err => {\r\n      \t\ttypeof callback == \"function\" && callback({\r\n      \t\t\tstatus: 0,\r\n      \t\t\tmsg: err.errMsg\r\n      \t\t});\r\n      \t}\r\n      });\r\n      // #endif\r\n    },\n  }\n};\n</script>\n\n<style>\npage{background:#ffffff;width: 100%;height:100%;}\n.container{width:100%;height:100%;}\n\n.text-center { text-align: center;}\n.title{margin:70rpx 50rpx 50rpx 40rpx;height:60rpx;line-height:60rpx;font-size: 48rpx;font-weight: bold;color: #000000;}\n.loginform{ width:100%;padding:0 50rpx;border-radius:5px;}\n.loginform .form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:88rpx;line-height:88rpx;border-bottom:1px solid #F0F3F6;margin-top:20rpx;background: #fff;border-radius: 8rpx;padding: 0 20rpx;}\n\n.loginform .form-item .img{width:44rpx;height:44rpx;margin-right:30rpx}\n.loginform .form-item .input{flex:1;color: #000;background: none;}\n.loginform .form-item .code{font-size:30rpx}\n.xieyi-item{display:flex;align-items:center;margin-top:30rpx}\n.xieyi-item{font-size:24rpx;color:#B2B5BE}\n.xieyi-item .checkbox{transform: scale(0.6);}\n\n.authlogin{display:flex;flex-direction:column;align-items:center}\n.authlogin-logo{width:180rpx;height:180rpx;margin-top:120rpx}\n.authlogin-name{color:#999999;font-size:30rpx;margin-top:60rpx;}\n.authlogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#51B1F5;border-radius:48rpx;color:#fff;margin-top:100rpx}\n.authlogin-btn2{width:580rpx;height:96rpx;line-height:96rpx;background:#EEEEEE;border-radius:48rpx;color:#A9A9A9;margin-top:20rpx}\n.ioslogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#fff;border-radius:48rpx;color:#fff;border:1px solid #555;color:#333;font-weight:bold;margin-top:30rpx;font-size:30rpx;display:flex;justify-content:center;align-items:center}\n.ioslogin-btn image{width:26rpx;height:26rpx;margin-right:16rpx;}\n\n.googlelogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#fff;border-radius:48rpx;color:#fff;border:1px solid #555;color:#333;font-weight:bold;margin-top:30rpx;font-size:30rpx;display:flex;justify-content:center;align-items:center}\n\n.googlelogin-btn2{margin-top:30rpx;display:flex;justify-content:center;align-items:center}\n\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\n.xieyibox-content{width:90%;margin:0 auto;/*  #ifdef  MP-TOUTIAO */height:60%;/*  #endif  *//*  #ifndef  MP-TOUTIAO */height:80%;/*  #endif  */margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px;}\n.xieyibox-content .xieyibut-view{height: 60rpx;position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;justify-content: space-around;}\n.xieyibox-content .xieyibut-view .but-class{text-align:center; width: auto;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;padding:0rpx 25rpx;}\n\n.bg_div1{width:100%;min-height: 100%;overflow: hidden;}\n.content_div1{width: 700rpx; margin: 0 auto;margin-bottom: 60rpx;}\n.title1{opacity: 1;font-size: 50rpx;font-weight: bold;line-height: 90rpx;text-align: left;margin-top: 80rpx;}\n.subhead1{font-size: 28rpx;font-weight: 500;line-height: 40rpx;}\n.card_div1{width: 100%;padding:40rpx;border-radius: 24rpx;margin-top: 40rpx;}\n.tel1{width:100%;height:100rpx;border-radius: 100rpx;line-height: 100rpx;background-color: #F5F7FA;padding:0 40rpx;margin: 20rpx 0;margin-top: 30rpx;}\n.code1{height: 100rpx;font-size: 24rpx;line-height: 100rpx;float: right;}\n.btn1{width:100%;height:100rpx;border-radius: 100rpx;line-height: 100rpx;margin: 20rpx 0;text-align: center;font-weight: bold;color: #A9A9A9;font-size: 30rpx}\n.other_line{width: 106rpx;height: 2rpx;background: #D8D8D8;margin-top: 20rpx;}\n.logo2{width: 200rpx;height: 200rpx;margin: 0 auto;margin-top:40rpx;margin-bottom: 40rpx;border-radius: 12rpx;overflow: hidden;}\n.inputcode{width:300rpx;height:100rpx;line-height: 100rpx;display: inline-block;background: none;}\n.input_val{width:100%;height:100rpx;line-height: 100rpx;background: none;}\n.xycss1{line-height: 60rpx;font-size: 24rpx;overflow: hidden;}\n.other_login{width:420rpx;margin: 60rpx auto;}\n.overflow_ellipsis{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 360rpx;}\n.other_content{overflow: hidden;width: 100%;margin-top: 60rpx;text-align: center;display: flex;justify-content:center;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839380224\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}