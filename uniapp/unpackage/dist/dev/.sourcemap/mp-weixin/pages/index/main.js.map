{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/main.vue?20de", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/main.vue?a6bd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/main.vue?fcf6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/main.vue?a9cd", "uni-app:///pages/index/main.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/main.vue?3bf9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/main.vue?0624"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "id", "pageinfo", "pagecontent", "title", "oglist", "guanggaopic", "guang<PERSON><PERSON>l", "guanggaotype", "guanggaoparam", "latitude", "longitude", "area", "mendianid", "sysset", "showlevel", "show_location", "curent_address", "arealist", "show_nearbyarea", "ischangeaddress", "nearbyplacelist", "myaddresslist", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placekeyword", "suggestionplacelist", "sharegive", "giveid", "isshare", "isend", "fzcode", "htsignatureurl", "onLoad", "onPullDownRefresh", "onShareAppMessage", "app", "that", "callback", "onShareTimeline", "console", "imageUrl", "query", "onPageScroll", "uni", "onShow", "methods", "getdata", "mendian_id", "mode", "deviceid", "hidehtqm", "sharecallback", "toshare", "itemList", "success", "scene", "sharedata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,iNAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACct1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAEA;MACA;MACA;MACA;QACAC;QAAA;MACA;MACA;MACA;QACA;QACAC;QACA;UAAAhC;UAAAiC;YAAAD;UAAA;QAAA;MACA;QACA;UAAAhC;QAAA;MACA;IACA;MACA;IACA;EACA;EACAkC;IACA;MAAAlC;IAAA;IACA;IACAmC;IACAA;IACA;MACAnC;MACAoC;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAD;IACA;EACA;EACAE;IACAC;MACA;MACA;MACA;MACA;QACA7C;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;MACA;MACAmC;MACAD;QAAAlC;QAAAS;QAAAC;QAAAC;QAAAmC;QAAAC;QAAAC;QAAAnB;MAAA;QACAM;QACAA;QACAO;QACA;UACA;UACAR;UACA;QACA;QACA;UACA;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAD;UACA;UACAQ;YACAvC;UACA;UACAgC;YAAAhC;UAAA;UACA;YACA+B;cACAC;cACAA;cACAA;YACA;UACA;QACA;UACA;YACAD;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAe;MACAX;MACA;IACA;IACAY;MACA;MACAhB;QAAAR;MAAA;QACA;UACAQ;QACA;UACAA;QACA;MACA;IACA;IACAiB;MACA;MACAb;MACA;MACAH;MACA;MACA;QACA;UAAAhC;UAAAiC;YAAAD;UAAA;QAAA;QACAD;MACA;QACAQ;UACAU;UACAC;YACA;cACA;cACA;gBACAC;cACA;cACA;cACAC;cACAA;cACAA;cACAA;cACAA;cACAA;cACAA;cACAb;YACA;UACA;QACA;MACA;QACAR;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClOA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/main.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./main.vue?vue&type=template&id=3120bf0f&\"\nvar renderjs\nimport script from \"./main.vue?vue&type=script&lang=js&\"\nexport * from \"./main.vue?vue&type=script&lang=js&\"\nimport style0 from \"./main.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/main.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=template&id=3120bf0f&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    dpGuanggao: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-guanggao/dp-guanggao\" */ \"@/components/dp-guanggao/dp-guanggao.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    dpSharegive: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-sharegive/dp-sharegive\" */ \"@/components/dp-sharegive/dp-sharegive.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" :style=\"{backgroundColor:pageinfo.bgcolor}\" v-if=\"isload\">\r\n\t<block>\r\n\t\t<dp :pagecontent=\"pagecontent\" :menuindex=\"menuindex\" :latitude=\"latitude\" :longitude=\"longitude\" @getdata=\"getdata\" :htsignatureurl=\"htsignatureurl\"></dp>\r\n\t\t<dp-guanggao :guanggaopic=\"guanggaopic\" :guanggaourl=\"guanggaourl\" :guanggaotype=\"guanggaotype\" :param=\"guanggaoparam\"></dp-guanggao>\r\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<dp-sharegive v-if=\"isshare\" :sharegive=\"sharegive\"  @toshare=\"toshare\" ></dp-sharegive>\r\n\t</block>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\tid: 0,\r\n\t\t\tpageinfo: [],\r\n\t\t\tpagecontent: [],\r\n\t\t\ttitle: \"\",\r\n\t\t\toglist: \"\", \r\n\t\t\tguanggaopic: \"\",\r\n\t\t\tguanggaourl: \"\",\r\n\t\t\tguanggaotype: \"1\",\r\n\t\t\tguanggaoparam:{},\r\n\t\t\tlatitude:'',\r\n\t\t\tlongitude:'',\r\n\t\t\tarea:'',\r\n\t\t\tmendianid:0,\r\n\t\t\t\r\n\t\t\tsysset:{},\r\n\t\t\tshowlevel:2,\r\n\t\t\tshow_location:0,\r\n\t\t\tcurent_address:'',//当前位置: 城市或者收货地址\r\n\t\t\tarealist:[],\r\n\t\t\tshow_nearbyarea:false,\r\n\t\t\tischangeaddress:false,\r\n\t\t\tnearbyplacelist:[],\r\n\t\t\tmyaddresslist:[],\r\n\t\t\tisshowalladdress:false,\r\n\t\t\tplacekeyword:'',\r\n\t\t\tsuggestionplacelist:[],\r\n\t\t\tsharegive:[],\r\n\t\t\tgiveid:0,\r\n\t\t\tisshare:false,\r\n\t\t\tisend:false,\r\n\t\t\tfzcode:'',//活码code，用于分账\r\n      htsignatureurl:''\r\n\t\t}\r\n\t},\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t},\r\n\tonPullDownRefresh:function(e){\r\n\t\tthis.isload = false;\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(e){\r\n\r\n\t\tif(e.target){\r\n\t\t\tvar id = e.target.dataset.id;\r\n\t\t\tvar isanswer =  e.target.dataset.isanswer;\r\n\t\t\tif(id && !isanswer){\r\n\t\t\t\tapp.error('请先回答问题');return;\r\n\t\t\t}\r\n\t\t\tvar callback=e.target.dataset.callback\r\n\t\t\tif(callback){\r\n\t\t\t\tvar that=this;\r\n\t\t\t\tthat.giveid = e.target.dataset.id;\r\n\t\t\t\treturn this._sharewx({title:this.title,callback:function(){that.sharecallback();}});\r\n\t\t\t}else{\r\n\t\t\t\treturn this._sharewx({title:this.title});\r\n\t\t\t}\r\n\t\t}else{\r\n\t\t\treturn this._sharewx();\r\n\t\t}\t\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.title});\r\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\tconsole.log(sharewxdata)\r\n\t\tconsole.log(query)\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n\tonPageScroll: function (e) {\r\n\t\tuni.$emit('onPageScroll',e);\r\n\t},\r\n\tonShow() {\r\n\t\tif(app.globalData.platform=='wx' && app.globalData.hide_home_button==1){\r\n\t\t  uni.hideHomeButton();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar opt = this.opt\r\n\t\t\tvar id = 0;\r\n\t\t\tif (opt && opt.id) {\r\n\t\t\t  id = opt.id;\r\n\t\t\t}\r\n\t\t\t//读全局缓存的地区信息\r\n\t\t\tvar locationCache =  app.getLocationCache();\r\n\t\t\tif(locationCache){\r\n\t\t\t\tif(locationCache.latitude){\r\n\t\t\t\t\tthis.latitude = locationCache.latitude\r\n\t\t\t\t\tthis.longitude = locationCache.longitude\r\n\t\t\t\t}\r\n\t\t\t\tif(locationCache.area){\r\n\t\t\t\t\tthis.area = locationCache.area\r\n\t\t\t\t\tthis.curent_address = locationCache.address\r\n\t\t\t\t}\r\n\t\t\t\tif(locationCache.mendian_id){\r\n\t\t\t\t\tthis.mendianid = locationCache.mendian_id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiIndex/index', {id: id,latitude:that.latitude,longitude:that.longitude,area:that.area,mendian_id:that.mendianid,mode:that.opt.mode?that.opt.mode:'',deviceid:that.opt.deviceid,fzcode:this.opt.fzcode}, function (data) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.isload = true;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t  if (data.status == 2) {\r\n\t\t\t    //付费查看\r\n\t\t\t    app.goto('/pagesExt/pay/pay?id='+data.payorderid, 'redirect');\r\n\t\t\t    return;\r\n\t\t\t  }\r\n\t\t\t  if (data.status == 1) {\r\n\t\t\t    var pagecontent = data.pagecontent;\r\n\t\t\t\t\tthat.title = data.pageinfo.title;\r\n\t\t\t\t\tthat.oglist = data.oglist;\r\n\t\t\t\t\tthat.guanggaopic = data.guanggaopic;\r\n\t\t\t\t\tthat.guanggaourl = data.guanggaourl;\r\n\t\t\t\t\tthat.guanggaotype = data.guanggaotype;\r\n\t\t\t\t\tthat.guanggaoparam = data.guanggaoparam;\r\n\t\t\t\t\tthat.pageinfo = data.pageinfo;\r\n\t\t\t\t\tthat.pagecontent = data.pagecontent;\r\n\t\t\t\t\tthat.sharegive = data.sharegive;\r\n\t\t\t\t\tthat.isshare = data.isshare;\r\n\t\t\t\t\tthat.isend = data.isend;\r\n\t\t\t\t\tif(data.isend){\r\n\t\t\t\t\t\tapp.alert('活动已结束')\t\r\n\t\t\t\t\t}\r\n\t\t\t    uni.setNavigationBarTitle({\r\n\t\t\t      title: data.pageinfo.title\r\n\t\t\t    });\r\n\t\t\t\t\tthat.loaded({title:that.title});\r\n\t\t\t\t\tif(that.latitude=='' && that.longitude=='' && data.needlocation){\r\n\t\t\t\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t  } else {\r\n\t\t\t    if (data.msg) {\r\n\t\t\t      app.alert(data.msg, function () {\r\n\t\t\t        if (data.url) app.goto(data.url);\r\n\t\t\t      });\r\n\t\t\t    } else if (data.url) {\r\n\t\t\t      app.goto(data.url);\r\n\t\t\t    } else {\r\n\t\t\t      app.alert('您无查看权限');\r\n\t\t\t    }\r\n\t\t\t  }\r\n\t\t\t});\r\n\t\t},\r\n    hidehtqm:function (signatureurl){\r\n      console.log(signatureurl)\r\n      this.htsignatureurl = signatureurl ?? '';\r\n    },\r\n\t\tsharecallback:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post(\"ApiShareGive/give\", {giveid:that.giveid}, function (res) {\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t} else if (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\ttoshare:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tconsole.log(this.opt.id)\r\n\t\t\tvar giveid = e.giveid\r\n\t\t\tthat.giveid = giveid\r\n\t\t\tvar platform = app.getplatform()\r\n\t\t\tif(platform == 'mp' || platform == 'h5'){\r\n\t\t\t\tthis._sharemp({title:this.title,callback:function(){that.sharecallback();}})\r\n\t\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\t}else if(platform == 'app'){\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n\t\t\t\t\tsuccess: function (res){\r\n\t\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\t\tsharedata.title = this.title;\r\n\t\t\t\t\t\t\tsharedata.summary = '';\r\n\t\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/index/main?main=id_'+that.opt.id;\r\n\t\t\t\t\t\t\tsharedata.imageUrl = '';\r\n\t\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\tapp.error('该终端不支持此操作');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n<style>\r\n\t.container{width: 100%;min-height: 100vh;overflow: hidden;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839380806\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}