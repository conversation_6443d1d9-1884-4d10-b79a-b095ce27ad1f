{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/reg.vue?06bd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/reg.vue?4c75", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/reg.vue?47ab", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/reg.vue?d7c0", "uni-app:///pages/index/reg.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/reg.vue?dad1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pages/index/reg.vue?94f9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "platform2", "platform", "platformname", "platformimg", "logintype", "logintype_1", "logintype_2", "logintype_3", "logintype_7", "logo", "name", "xystatus", "xyname", "xycontent", "xyname2", "xycontent2", "needsms", "<PERSON><PERSON><PERSON><PERSON>", "showxieyi2", "isagree", "smsdjs", "tel", "<PERSON><PERSON>", "frompage", "wxloginclick", "login_bind", "login_setnickname", "reg_invite_code", "reg_invite_code_text", "reg_invite_code_type", "reg_invite_code_show", "yqcode", "parent", "has_custom", "show_custom_field", "regiondata", "editorFormdata", "test", "formfields", "custom_formdata", "items", "formvaldata", "submitDisabled", "tmplids", "default_headimg", "headimg", "nickname", "loginset_type", "loginset_data", "alih5", "ali_appid", "alih5loginclick", "tel_placeholder", "showicon", "set", "onLoad", "onShow", "uni", "onPullDownRefresh", "methods", "<PERSON><PERSON><PERSON><PERSON>", "getdata", "that", "app", "pid", "regbid", "wxregyqcode", "url", "method", "header", "success", "formSubmit", "formdata", "pwd", "smscode", "postdata", "setTimeout", "getPhoneNumber", "console", "iv", "encryptedData", "code", "setnicknameregister", "nosetnicknameregister", "setRegisterInvite", "setRegisterInvitePass", "bindregister", "nobindregister", "register", "weixinlogin", "yqinput", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "showxieyiFun2", "hidexieyi2", "telinput", "uploadHeadimg", "count", "sizeType", "sourceType", "filePath", "fail", "onChooseAvatar", "time", "clearInterval", "onchange", "setfield", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "check<PERSON>ust<PERSON><PERSON><PERSON><PERSON><PERSON>s", "value", "editorChooseImage", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeimg", "alih5login", "getBaiduPhoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAAi0B,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Vr1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACA;IAMA;IACA;MACA;MACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;QAAAC;MAAA;QACAJ;QACA;UACAC;UAAA;QACA;QACA;UACAD;QACA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QAUAA;QAEAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;UACAA;UACAA;UACAL;YACAU;YACAzE;YACA0E;YACAC;cAAA;YAAA;YACAC;cACAR;YACA;UACA;QACA;QACA;UACAA;QACA;QAEA;UACAA;QACA;QACAA;MASA;IACA;IACAS;MACA;MACA;MACA;QACAR;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;QACAS;MACA;MACA;QAAAnD;QAAAoD;QAAAC;QAAAV;QAAAjC;QAAAkC;MAAA;MACA;MACA;QACA;QACA;QACA;UACA;QACA;QACAU;QACAA;MACA;MAEA;QACAZ;QACA;MACA;MACAA;MACAA;QACAA;QACA;UACAA;UACA;YACAD;UACA;UACAA;YACAc;cACA;gBACAb;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAc;MACA;MACA;QACAd;QAAA;MACA;MACA1E;QAAAiF;UACAQ;UACA;UACA;UACAf;YAAAlB;YAAAC;YAAAiC;YAAAC;YAAAC;YAAAjB;YAAAC;UAAA;YACA;cACAF;cACAa;gBACAb;cACA;YACA;cACAA;YACA;YACA;UACA;QACA;MAAA;IACA;IACAmB;MACA;MACA;MACA;MACA;QACAnB;QACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACAN;MACA;MACA;MACA;QACAf;QACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAsB;MACA;QACA;QACA;QACA;MACA;QACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAvB;QACA;MACA;MACA;QACAA;QACA;MACA;MACAD;IACA;IACAyB;MACA;IACA;IACAC;MACA;MACA;MACA;QACArB;MACA;QACAA;MACA;QACAA;MACA;MACAJ;QAAAlB;QAAAC;QAAAzB;QAAAqD;QAAAV;QAAAjC;QAAAkC;MAAA;QACA;UACAF;UACAa;YACAb;UACA;QACA;UACAA;QACA;QACA;MACA;IACA;IACA0B;MACA;MACA;QACA3B;QACAA;QACA;MACA;MACAA;MACAC;MACAA;QACAA;QACA;UACAA;UACAa;YACAb;UACA;QACA;UACA;UACAD;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;UACAC;QACA;MACA;QAAAxC;QAAAQ;MAAA;IACA;IACA2D;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACAb;IACA;IACAc;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA/B;MACA;IACA;IACAgC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACAjC;MACA;IACA;IACAkC;MACA;IACA;IACAC;MACA;MACAxC;QACAyC;QACAC;QACAC;QACA9B;UACA;UACA;UACAP;UACAN;YACAU;YACAkC;YACA3F;YACA4D;cACAQ;cACAf;cACA;cACA;gBACAD;cACA;gBACAC;cACA;YACA;YACAuC;cACAvC;cACAA;YACA;UACA;QACA;QACAuC;QAAA;MAEA;IACA;IACAC;MACAzB;MACA;MACAf;MACAN;QACAU;QACAkC;QACA3F;QACA4D;UACAP;UACA;UACA;YACAD;UACA;YACAC;UACA;QACA;QACAuC;UACAvC;UACAA;QACA;MACA;IACA;IACAW;MACA;MACA;MACAZ;MACA;MACA;QACAC;QACAD;QACA;MACA;MACA;QACAC;QACAD;QACA;MACA;MACAC;QAAA1C;MAAA;QACA;UACA0C;QACA;MACA;MACA;MACA;QACAyC;QACA;UACA1C;UACAA;UACA2C;QACA;UACA3C;QACA;MACA;IACA;IACA;IACA4C;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;MACAxE;MACA0B;MACA;MACA;MACA;IACA;IACA+C;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACAC;QACA;QAEA;UAEA/C;UAAA;QACA;QACA;UACA;YACA+C;UACA;YACAA;UACA;QACA;QACA;UACAA;QACA;QACA;UACA;YACA/C;YAAA;UACA;QACA;QACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;QACA;QACAS;MACA;MACA;IACA;IACAuC;MACA;MACA;MACA;MACA;MACA;MACAhD;QACA3B;QACA0C;QACAhB;QACAA;QAEA;QACAA;MAEA;IACA;IACAkD;MACA;MACA;MACA;MACA;MACA;MACAjD;QACA3B;QACA0C;QACAhB;QACAA;QAEA;QACAA;MAEA;IACA;IACAmD;MACA;MACA;MACA;MACA;MACA;MACA;MACA7E;MACA0B;MACAA;MACAA;IACA;IACAoD,mCAoEA;IACAC,sDAkDA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnoCA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/reg.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/reg.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./reg.vue?vue&type=template&id=18f2162e&\"\nvar renderjs\nimport script from \"./reg.vue?vue&type=script&lang=js&\"\nexport * from \"./reg.vue?vue&type=script&lang=js&\"\nimport style0 from \"./reg.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/reg.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reg.vue?vue&type=template&id=18f2162e&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 =\n    _vm.isload &&\n    _vm.logintype != 4 &&\n    _vm.logintype != 5 &&\n    _vm.logintype != 6 &&\n    _vm.show_custom_field &&\n    _vm.show_custom_field\n      ? _vm.__map(_vm.formfields.content, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var l0 =\n            item.key == \"checkbox\"\n              ? _vm.__map(item.val2, function (item1, idx1) {\n                  var $orig = _vm.__get_orig(item1)\n                  var m0 =\n                    _vm.custom_formdata[\"form\" + idx] &&\n                    _vm.inArray(item1, _vm.custom_formdata[\"form\" + idx])\n                  return {\n                    $orig: $orig,\n                    m0: m0,\n                  }\n                })\n              : null\n          return {\n            $orig: $orig,\n            l0: l0,\n          }\n        })\n      : null\n  var m1 =\n    _vm.isload &&\n    _vm.logintype != 4 &&\n    _vm.logintype != 5 &&\n    _vm.logintype != 6 &&\n    _vm.xystatus == 1 &&\n    _vm.xyname2 &&\n    !_vm.loginset_data.xycolor\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.logintype != 4 &&\n    _vm.logintype != 5 &&\n    _vm.logintype != 6 &&\n    _vm.loginset_data.btntype == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m3 = _vm.isload && _vm.logintype == 4 ? _vm.t(\"color1\") : null\n  var m4 = _vm.isload && _vm.logintype == 4 ? _vm.t(\"color1rgb\") : null\n  var m5 = _vm.isload && _vm.logintype == 5 ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && _vm.logintype == 5 ? _vm.t(\"color1rgb\") : null\n  var m7 =\n    _vm.isload && _vm.logintype == 6 && _vm.loginset_data.btntype == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m8 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1\") : null\n  var m9 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1rgb\") : null\n  var m10 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1\") : null\n  var m11 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reg.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reg.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n    <!-- s -->\n    <view style=\"width:100%;height: 100%;\">\n      <view class=\"bg_div1\" :style=\"loginset_data.bgtype==1?'background:'+loginset_data.bgcolor:'background:url('+loginset_data.bgimg+') no-repeat center;background-size:100% 100%'\">\n        <view style=\"overflow: hidden;\">\n          <view class=\"content_div1\">\n            <view class=\"card_div1\" :style=\"'background:'+loginset_data.cardcolor\">\n              <block v-if=\"logintype!=4 && logintype!=5 && logintype!=6\">\n                <form @submit=\"formSubmit\" @reset=\"formReset\">\n                  <view class=\"title1\" :style=\"'color:'+loginset_data.titlecolor+';text-align:'+loginset_data.titletype+';margin-top: 20rpx;font-size: 40rpx;'\">\n                    注册账号\n                  </view>\n                  <view class=\"regform\" style=\"margin-top: 20rpx;\">\n                  <!-- 系统注册S -->\n                  <block v-if=\"!show_custom_field\">\n                    <view class=\"form-item\">\n                      <image :src=\"pre_url+'/static/img/reg-tel.png'\" class=\"img\"/>\n                      <input type=\"text\" class=\"input\" :placeholder=\"tel_placeholder\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\n                    </view>\n                    <view class=\"form-item\" v-if=\"needsms\">\n                      <image :src=\"pre_url+'/static/img/reg-code.png'\" class=\"img\"/>\n                      <input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\"/>\n                      <view class=\"code\" :style=\"'color:'+loginset_data.codecolor\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\n                    </view>\n                    <view class=\"form-item\">\n                      <image :src=\"pre_url+'/static/img/reg-pwd.png'\" class=\"img\"/>\n                      <input type=\"text\" class=\"input\" placeholder=\"6-16位字母数字组合密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"pwd\" value=\"\" :password=\"true\"/>\n                    </view>\n                    <view class=\"form-item\">\n                      <image :src=\"pre_url+'/static/img/reg-pwd.png'\" class=\"img\"/>\n                      <input type=\"text\" class=\"input\" placeholder=\"再次输入登录密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"repwd\" value=\"\" :password=\"true\"/>\n                    </view>\n                    <view class=\"form-item\" v-if=\"reg_invite_code && !parent\">\n                      <image :src=\"pre_url+'/static/img/reg-yqcode.png'\" class=\"img\"/>\n                      <input type=\"text\" class=\"input\" :placeholder=\"'请输入邀请人'+reg_invite_code_text\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"yqcode\" @input=\"yqinput\" :value=\"yqcode\"/>\n                    </view>\n                    <view class=\"form-item\" v-if=\"reg_invite_code && parent && reg_invite_code_show == 1\" style=\"color:#666;\">\n                      <block v-if=\"reg_invite_code_type == 0 \">\n                      邀请人：<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> {{parent.nickname}} \n                      </block>\n                      <block v-else>\n                      邀请码：{{parent.yqcode}} \n                      </block>\n                    </view>\n                  </block>\n                  <!-- 系统注册E -->\n                  <!-- 自定义注册S -->\n                  <block v-if=\"show_custom_field\">\n                    <view class=\"dp-form-item\">\n                      <image v-if=\"showicon\" :src=\"pre_url+'/static/img/reg-tel.png'\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n                      <view class=\"label\">手机号<text style=\"color:red\"> * </text></view>\n                      <input type=\"text\" class=\"input\" :placeholder=\"tel_placeholder\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\n                    </view>\n                    <view class=\"dp-form-item\" v-if=\"needsms\">\n                      <image v-if=\"showicon\" :src=\"pre_url+'/static/img/reg-code.png'\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n                      <text class=\"label\">验证码<text :style=\"'color:'+loginset_data.cardcolor\"> * </text></text>\n                      <view class=\"tel1\" style=\"display: flex;\">\n                        <input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\" style=\"border: 0;padding: 0;\"/>\n                        <view class=\"code\" :style=\"'color:'+loginset_data.codecolor\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\n                      </view>\n                    </view>\n                    <view class=\"dp-form-item\">\n                      <image v-if=\"showicon\" :src=\"pre_url+'/static/img/reg-pwd.png'\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n                      <view class=\"label\">密码<text style=\"color:red\"> * </text></view>\n                      <input type=\"text\" class=\"input\" placeholder=\"6-16位字母数字组合密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"pwd\" value=\"\" :password=\"true\"/>\n                    </view>\n                    <view class=\"dp-form-item\">\n                      <image v-if=\"showicon\" :src=\"pre_url+'/static/img/reg-pwd.png'\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n                      <!-- 跟随背景颜色展位保持对齐 -->\n                      <view class=\"label\">确认密码<text :style=\"'color:'+loginset_data.cardcolor\"> * </text></view>\n                      <input type=\"text\" class=\"input\" placeholder=\"再次输入登录密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"repwd\" value=\"\" :password=\"true\"/>\n                    </view>\n                    <view class=\"dp-form-item\" v-if=\"reg_invite_code && !parent\">\n                      <image v-if=\"showicon\" :src=\"pre_url+'/static/img/reg-yqcode.png'\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n                      <text class=\"label\">邀请码<text :style=\"'color:'+loginset_data.cardcolor\"> * </text></text>\n                      <input type=\"text\" class=\"input\" :placeholder=\"'请输入邀请人'+reg_invite_code_text\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"yqcode\" :value=\"yqcode\"/>\n                    </view>\n                    <view class=\"dp-form-item\" v-if=\"reg_invite_code && parent && reg_invite_code_show == 1\" style=\"color:#666;\">\n                      <image v-if=\"showicon\" :src=\"pre_url+'/static/img/reg-yqcode.png'\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n                      <block v-if=\"reg_invite_code_type == 0 \">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"label\">邀请人</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> \n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"overflow_ellipsis\">{{parent.nickname}} </view>\n                      </block>\n                      <block v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"label\">邀请码</view>{{parent.yqcode}} \n                      </block>\n                    </view>\n                    <view class=\"custom_field\" v-if=\"show_custom_field\">\n                      <view :class=\"'dp-form-item'\" v-for=\"(item,idx) in formfields.content\"  :key=\"idx\">\n                        <image v-if=\"item.val8 !== undefined && item.val8 !== null && showicon\" :src=\"item.val8\" style=\"max-width: 60rpx;height: 60rpx;\" mode=\"widthFix\"></image>\n                        <view class=\"label\">{{item.val1}}<text :style=\"{color:item.val3==1 ? 'red' : loginset_data.cardcolor}\"> *</text></view>\n                        <block v-if=\"item.key=='input' || item.key=='realname' || item.key=='usercard'\">\n                          <text v-if=\"item.val5\" style=\"margin-right:10rpx\">{{item.val5}}</text>\n                          <input :type=\"item.input_type\" :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" :value=\"custom_formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\n                        </block>\n                        <block v-if=\"item.key=='textarea'\">\n                          <textarea :name=\"'form'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\"  :value=\"custom_formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\n                        </block>\n                        <block v-if=\"item.key=='radio' || item.key=='sex'\">\n                          <radio-group class=\"flex\" :name=\"'form'+idx\" style=\"flex-wrap:wrap\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\n                            <label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n                                <radio class=\"radio\" :value=\"item1\" style=\"transform: scale(0.8);\" :checked=\"custom_formdata['form'+idx] && custom_formdata['form'+idx]==item1 ? true : false\"/>{{item1}}\n                            </label>\n                          </radio-group>\n                        </block>\n                        <block v-if=\"item.key=='checkbox'\">\n                          <checkbox-group :name=\"'form'+idx\" class=\"flex\" style=\"flex-wrap:wrap\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\n                            <label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n                              <checkbox class=\"checkbox\" style=\"transform: scale(0.8);\" :value=\"item1\" :checked=\"custom_formdata['form'+idx] && inArray(item1,custom_formdata['form'+idx]) ? true : false\"/>{{item1}}\n                            </label>\n                          </checkbox-group>\n                        </block>\n                        <block v-if=\"item.key=='selector'\">\n                          <picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\n                            <view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\"> {{item.val2[editorFormdata[idx]]}}</view>\n                            <view v-else style=\"color: #b2b5be;\">请选择</view>\n                          </picker>\n                        </block>\n                        <block v-if=\"item.key=='time'\">\n                          <picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" :value=\"custom_formdata['form'+idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\n                            <view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\n                            <view v-else style=\"color: #b2b5be;\">请选择</view>\n                          </picker>\n                        </block>\n                        <block v-if=\"item.key=='date' || item.key=='birthday'\">\n                          <picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" :value=\"custom_formdata['form'+idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\n                            <view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\n                            <view v-else style=\"color: #b2b5be;\">请选择</view>\n                          </picker>\n                        </block>\n                      \n                        <block v-if=\"item.key=='region'\">\n                            <uni-data-picker :localdata=\"items\" popup-title=\"请选择省市区\" :placeholder=\"custom_formdata['form'+idx] || '请选择省市区'\" @change=\"onchange\" :data-formidx=\"'form'+idx\"></uni-data-picker>\n                            <input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"regiondata ? regiondata : custom_formdata['form'+idx]\"/>\n                        </block>\n                        <block v-if=\"item.key=='upload'\">\n                          <input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\n                          <view class=\"flex\" style=\"flex-wrap:wrap;\">\n                            <view class=\"dp-form-imgbox\" v-if=\"editorFormdata[idx]\">\n                              <view class=\"dp-form-imgbox-close\" @tap=\"removeimg\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image></view>\n                              <view class=\"dp-form-imgbox-img\"><image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"widthFix\" :data-idx=\"idx\"/></view>\n                            </view>\n                            <view v-else class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"></view>\n                          </view>\n                        </block>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- #ifdef H5 || MP-WEIXIN -->\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='upload_file'\">\n\t\t\t\t\t\t\t\t\t\t\t\t  <input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\n\t\t\t\t\t\t\t\t\t\t\t\t  <view class=\"flex\" style=\"flex-wrap:wrap;\">\n\t\t\t\t\t\t\t\t\t\t\t\t    <view class=\"dp-form-imgbox\" v-if=\"editorFormdata[idx]\">\n\t\t\t\t\t\t\t\t\t\t\t\t      <view class=\"dp-form-imgbox-close\" @tap=\"removeimg\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image></view>\n\t\t\t\t\t\t\t\t\t\t\t\t      <view class=\"dp-form-imgbox-img\">已上传</view>\n\t\t\t\t\t\t\t\t\t\t\t\t    </view>\n\t\t\t\t\t\t\t\t\t\t\t\t    <view v-else class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseFile\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"></view>\n\t\t\t\t\t\t\t\t\t\t\t\t  </view>\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- #endif -->\n                      </view>\n                    </view>\n                    <view style=\"display:none\">{{test}}</view>\n                  </block>\n                  <!-- 自定义注册E -->\n                  \n                  <view v-if=\"xystatus==1\" class=\"xycss1\">\n                    <checkbox-group @change=\"isagreeChange\" style=\"display: inline-block;\">\n\t\t\t\t\t\t\t\t\t\t\t<checkbox style=\"transform: scale(0.6)\"  value=\"1\" :checked=\"isagree\"/>\n\t\t\t\t\t\t\t\t\t\t\t<text :style=\"'color:'+loginset_data.xytipcolor\">{{loginset_data.xytipword}}</text>\n                    </checkbox-group>\n                    <text @tap=\"showxieyiFun\" :style=\"'color:'+loginset_data.xycolor\">{{xyname}}</text>\n                    <text @tap=\"showxieyiFun\"  v-if=\"xyname2\" :style=\"'color:'+loginset_data.xytipcolor\">和</text>\n                    <text @tap=\"showxieyiFun2\" v-if=\"xyname2\" :style=\"loginset_data.xycolor?'color:'+loginset_data.xycolor:'color:'+t('color1')\">{{xyname2}}</text>\n                  </view>\n                  \n                  <block v-if=\"loginset_data.btntype==1\">\n                    <button class=\"btn1\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\" form-type=\"submit\">注册</button>\n                  </block>\n                  <block v-if=\"loginset_data.btntype==2\">\n                    <button class=\"btn1\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\" form-type=\"submit\">注册</button>\n                  </block>\n\n                </view>\n                </form>\n                \n                <view class=\"tologin\" @tap=\"goto\" data-url=\"login\" data-opentype=\"redirect\" :style=\"'color: '+loginset_data.regpwdbtncolor\">已有账号? 前去登录</view>\n                \n                <block v-if=\"logintype_2 || logintype_3\">\n                  <view style=\"display: flex;width: 420rpx;margin: 60rpx auto;\">\n                    <view class=\"other_line\"></view>\n                    <view style=\"margin: 0 20rpx;color: #888888;\">其他登录方式</view>\n                    <view  class=\"other_line\"></view>\n                  </view>\n                  <view class=\"othertype\">\n                    <view class=\"othertype-item\" v-if=\"logintype_3\" @tap=\"weixinlogin\">\n                      <image class=\"img\" :src=\"pre_url+'/static/img/login-'+platformimg+'.png'\"/>\n                      <text class=\"txt\" style=\"color: #888888;\">授权登录</text>\n                    </view>\n                    <view class=\"othertype-item\" v-if=\"logintype_7 && alih5\" @tap=\"alih5login\">\n                      <image class=\"img\" :src=\"pre_url+'/static/img/login-alipay.png'\"/>\n                      <text class=\"txt\" style=\"color: #888888;\">支付宝登录</text>\n                    </view>\n                    <view class=\"othertype-item\" v-if=\"logintype_2\" @tap=\"goto\" data-url=\"login?logintype=2\" data-opentype=\"redirect\">\n                      <image class=\"img\" :src=\"pre_url+'/static/img/reg-tellogin.png'\"/>\n                      <text class=\"txt\" style=\"color: #888888;\">手机号登录</text>\n                    </view>\n                  </view>\n                </block>\n              </block>\n\n              <!-- 绑定手机号 -->\n              <block v-if=\"logintype==4\">\n                  <!-- #ifdef MP-WEIXIN -->\n                  <view class=\"authlogin\">\n                    <view class=\"logo2\">\n                        <img :src=\"loginset_data.logo\" style=\"width: 100%;height: 100%;\">\n                    </view>\n                    <view style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\"> 授权登录{{name}}</view>\n                    <button class=\"authlogin-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">授权绑定手机号</button>\n                    <button class=\"authlogin-btn2\" @tap=\"nobindregister\" v-if=\"login_bind==1\">暂不绑定</button>\n                  </view>\n                  <!-- #endif -->\n                  <!-- #ifdef MP-BAIDU -->\n                  <view class=\"authlogin\">\n                    <view class=\"logo2\">\n                        <img :src=\"loginset_data.logo\" style=\"width: 100%;height: 100%;\">\n                    </view>\n                    <view style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\"> 授权登录{{name}}</view>\n                    <button class=\"authlogin-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getBaiduPhoneNumber\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">授权绑定手机号</button>\n                    <button class=\"authlogin-btn2\" @tap=\"nobindregister\" v-if=\"login_bind==1\">暂不绑定</button>\n                  </view>\n                  <!-- #endif -->\n                  <!-- #ifndef MP-WEIXIN || MP-BAIDU -->\n                  <form @submit=\"bindregister\" @reset=\"formReset\">\n                    <view style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\">绑定手机号</view>\n                    <view class=\"regform\">\n                      <view class=\"form-item\">\n                        <image :src=\"pre_url+'/static/img/reg-tel.png'\" class=\"img\"/>\n                        <input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\n                      </view>\n                      <view class=\"form-item\">\n                        <image :src=\"pre_url+'/static/img/reg-code.png'\" class=\"img\"/>\n                        <input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\"/>\n                        <view class=\"code\" :style=\"'color:'+loginset_data.codecolor\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\n                      </view>\n                      <button class=\"form-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定</button>\n                      <button class=\"form-btn2\" @tap=\"nobindregister\" v-if=\"login_bind==1\">暂不绑定</button>\n                    </view>\n                  </form>\n                  <!-- #endif -->\n              </block>\n              \n              <!-- 设置头像昵称 -->\n              <block v-if=\"logintype==5\">\n                <form @submit=\"setnicknameregister\" @reset=\"formReset\">\n                  <view style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\">请设置头像昵称</view>\n                  <view class=\"regform\">\n                    <!--  #ifdef MP-WEIXIN -->\n                    <view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n                      <view class=\"flex1\">头像</view>\n                      <button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" style=\"width:100rpx;height:100rpx;\">\n                        <image :src=\"headimg || default_headimg\" style=\"width:100%;height:100%;border-radius:50%\"></image>\n                      </button> \n                    </view>\n                    <view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n                      <view class=\"flex1\">昵称</view>\n                      <input type=\"nickname\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\n                    </view>\n                    <!-- #endif -->\n                    <!--  #ifndef MP-WEIXIN -->\n                    <view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n                      <view class=\"flex1\">头像</view>\n                      <image :src=\"headimg || default_headimg\" style=\"width:100rpx;height:100rpx;border-radius:50%\" @tap=\"uploadHeadimg\"></image>\n                    </view>\n                    <view class=\"form-item\">\n                      <view class=\"flex1\">昵称</view>\n                      <input type=\"text\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" value=\"\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\n                    </view>\n                    <!-- #endif -->\n                    <button class=\"form-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定</button>\n                    <button class=\"form-btn2\" @tap=\"nosetnicknameregister\" v-if=\"login_setnickname==1\">暂不设置</button>\n                  </view>\n                </form>\n              </block>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 填写邀请码s -->\n\t\t\t\t\t\t\t<block v-if=\"logintype==6\">\n\t\t\t\t\t\t\t  <form @submit=\"setRegisterInvite\" @reset=\"formReset\">\n\t\t\t\t\t\t\t    <view v-if=\"reg_invite_code && ((parent && reg_invite_code_show == 1) || !parent)\" style=\"font-size: 30rpx;font-weight: bold;line-height: 68rpx;\">请填写邀请码</view>\n\t\t\t\t\t\t\t    <view class=\"loginform\" style=\"padding: 0;\">\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"reg_invite_code && !parent\" class=\"form-item\">\n\t\t\t\t\t\t\t\t\t\t   <input type=\"text\" name=\"yqcode\" @input=\"yqinput\" :value=\"yqcode\" :placeholder=\"'请输入邀请人'+reg_invite_code_text\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" class=\"input\"/>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"reg_invite_code && parent && reg_invite_code_show == 1\" class=\"form-item\" style=\"display: flex;padding-top: 8rpx;align-items: center;\">\n\t\t\t\t\t\t\t\t\t\t  <view style=\"white-space: nowrap;\">邀请人：</view>\n\t\t\t\t\t\t\t\t\t\t  <image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;margin-right: 20rpx;\"></image> \n\t\t\t\t\t\t\t\t\t\t  <view class=\"overflow_ellipsis\">{{parent.nickname}} </view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t      <block v-if=\"loginset_data.btntype==1\">\n\t\t\t\t\t\t\t        <button class=\"btn1\" form-type=\"submit\" :style=\"'background:rgba('+t('color1rgb')+');color: '+loginset_data.btnwordcolor\">确定</button>\n\t\t\t\t\t\t\t      </block>\n\t\t\t\t\t\t\t      <block v-if=\"loginset_data.btntype==2\">\n\t\t\t\t\t\t\t        <button class=\"btn1\" form-type=\"submit\" :style=\"'background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor\">确定</button>\n\t\t\t\t\t\t\t      </block>\n\t\t\t\t\t\t\t      <button class=\"btn1\" @tap=\"setRegisterInvitePass\" v-if=\"reg_invite_code==1\" style=\"background-color:#EEEEEE ;font-size: 28rpx;\">暂不设置</button>\n\t\t\t\t\t\t\t    </view>\n\t\t\t\t\t\t\t  </form>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<!-- 填写邀请码e -->\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <!-- e -->\n\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\n\t\t\t<view class=\"xieyibox-content\">\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\n\t\t\t\t\t<parse :content=\"xycontent\" @navigate=\"navigate\"></parse>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"xieyibut-view flex-y-center\">\n\t\t\t\t\t<view class=\"but-class\" style=\"background:#A9A9A9\"  @tap=\"closeXieyi\">不同意并退出</view>\n\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi\">已阅读并同意</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"showxieyi2\" class=\"xieyibox\">\n\t\t\t<view class=\"xieyibox-content\">\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\n\t\t\t\t\t<parse :content=\"xycontent2\" @navigate=\"navigate\"></parse>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"xieyibut-view flex-y-center\">\n\t\t\t\t\t<view class=\"but-class\" style=\"background:#A9A9A9\"  @tap=\"closeXieyi\">不同意并退出</view>\n\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi2\">已阅读并同意</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tplatform2:app.globalData.platform2,\n\t\t\t\n\t\t\tplatform:'',\n\t\t\tplatformname:'',\n\t\t\tplatformimg:'weixin',\n\t\t\tlogintype:0,\n\t\t\tlogintype_1:true,\n\t\t\tlogintype_2:false,\n\t\t\tlogintype_3:false,\n      logintype_7:false,//支付宝内h5\n\t\t\tlogo:'',\n\t\t\tname:'',\n\t\t\txystatus:0,\n\t\t\txyname:'',\n\t\t\txycontent:'',\n\t\t\txyname2:'',\n\t\t\txycontent2:'',\n\t\t\tneedsms:false,\n\t\t\tshowxieyi:false,\n\t\t\tshowxieyi2:false,\n\t\t\tisagree:false,\n      smsdjs: '',\n\t\t\ttel:'',\n      hqing: 0,\n\t\t\tfrompage:'/pages/my/usercenter',\n\t\t\twxloginclick:false,\n\t\t\tlogin_bind:0,\n\t\t\tlogin_setnickname:0,\n      reg_invite_code:0,//邀请码 1开启 0关闭 2强制邀请\n      reg_invite_code_text:'',//邀请码文字描述\n\t\t\treg_invite_code_type:0,//类型 1邀请码 0手机号\n\t\t\treg_invite_code_show:1,//有邀请人时登录、注册页面是否显示邀请码和邀请人\n\t\t\tyqcode:'',\n      parent:{},\n\t\t\t//自定义表单Start\n\t\t\thas_custom:0,\n\t\t\tshow_custom_field:false,\n\t\t\tregiondata:'',\n\t\t\teditorFormdata:{},\n\t\t\ttest:'',\n\t\t\tformfields:[],\n\t\t\tcustom_formdata:[],\n\t\t\titems: [],\n\t\t\tformvaldata:{},\n\t\t\tsubmitDisabled:false,\n\t\t\t//自定义表单End\n\t\t\ttmplids:[],\n\t\t\tdefault_headimg:app.globalData.pre_url + '/static/img/touxiang.png',\n\t\t\theadimg:'',\n\t\t\tnickname:'',\n      loginset_type:0,\n      loginset_data:'',\n      \n      alih5:false,\n      ali_appid:'',\n      alih5loginclick:false,\n      tel_placeholder:'请输入手机号',\n      showicon:0,//注册自定义是否显示图标\n\t\t\tset:{}\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n    // #ifdef H5\n    if (navigator.userAgent.indexOf('AlipayClient') > -1) {\n      this.alih5 = true;\n    }\n    // #endif\n\t\tif(this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);\n    if(app.globalData.qrcode){\n      var frompage = '/pagesA/qrcode/index?code='+app.globalData.qrcode;\n      this.frompage = decodeURIComponent(frompage);\n    }\n\t\tif(this.opt.logintype) this.logintype = this.opt.logintype;\n\t\tif(this.opt.login_bind) this.login_bind = this.opt.login_bind;\n\t\tthis.getdata();\n  },\n\tonShow:function() {\n\t\tif(app.globalData.platform=='wx' && app.globalData.hide_home_button==1){\n\t\t\tuni.hideHomeButton();\n\t\t}\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\t// 不同意协议\n\t\tcloseXieyi(){\n\t\t\tthis.showxieyi = false;\n\t\t\tthis.showxieyi2 = false;\n\t\t\tthis.isagree = false;\n\t\t},\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiIndex/reg', {pid:app.globalData.pid,regbid:app.globalData.regbid,wxregyqcode:app.globalData.wxregyqcode,}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif(res.status == 0){\n\t\t\t\t\tapp.alert(res.msg);return;\n\t\t\t\t}\n        if(res.loginset_type){\n          that.loginset_type = res.loginset_type\n        }\n        if(res.loginset_data){\n          that.loginset_data = res.loginset_data\n        }\n\t\t\t\tthat.logintype_2 = res.logintype_2;\n\t\t\t\tthat.logintype_3 = res.logintype_3;\n\t\t\t\tthat.logintype_3 = res.logintype_3;\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif(that.platform2 == 'ios'){\n\t\t\t\t\tif (plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {\n\t\t\t\t\t\t\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthat.logintype_3 = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// #endif\n        that.logintype_7 = res.logintype_7 || false;\n\n\t\t\t\tthat.xystatus = res.xystatus;\n\t\t\t\tthat.xyname = res.xyname;\n\t\t\t\tthat.xycontent = res.xycontent;\n\t\t\t\tthat.xyname2 = res.xyname2;\n\t\t\t\tthat.xycontent2 = res.xycontent2;\n\t\t\t\tthat.logo = res.logo;\n\t\t\t\tthat.name = res.name;\n\t\t\t\tthat.needsms = res.needsms;\n\t\t\t\tthat.platform = res.platform;\n        that.reg_invite_code = res.reg_invite_code;\n        that.reg_invite_code_text = res.reg_invite_code_text;\n        that.reg_invite_code_type = res.reg_invite_code_type;\n\t\t\t\tthat.reg_invite_code_show = res.reg_invite_code_show;\n        that.parent = res.parent;\n\t\t\t\tif(that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app'){\n\t\t\t\t\tthat.platformname = '微信';\n\t\t\t\t\tthat.platformimg = 'weixin';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'toutiao'){\n\t\t\t\t\tthat.platformname = '快捷';\n\t\t\t\t\tthat.platformimg = 'toutiao';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'alipay'){\n\t\t\t\t\tthat.platformname = '支付宝';\n\t\t\t\t\tthat.platformimg = 'alipay';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'qq'){\n\t\t\t\t\tthat.platformname = 'QQ';\n\t\t\t\t\tthat.platformimg = 'qq';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'baidu'){\n\t\t\t\t\tthat.platformname = '百度';\n\t\t\t\t\tthat.platformimg = 'baidu';\n\t\t\t\t}\n\t\t\t\tif(res.tel_placeholder){\n\t\t\t\t\tthat.tel_placeholder = res.tel_placeholder\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t//自定义表单\n\t\t\t\tif(res.has_custom){\n\t\t\t\t\tthat.formfields = res.custom_form_field;\n\t\t\t\t\tthat.has_custom = res.has_custom\n\t\t\t\t\tthat.show_custom_field = true\n\t\t\t\t\tuni.request({\n\t\t\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\n\t\t\t\t\t\tdata: {},\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\theader: { 'content-type': 'application/json' },\n\t\t\t\t\t\tsuccess: function(res2) {\n\t\t\t\t\t\t\tthat.items = res2.data\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tif(res.showicon){\n\t\t\t\t\tthat.showicon = res.showicon;\n\t\t\t\t}\n\t\t\t\t\n        if(res.ali_appid){\n          that.ali_appid = res.ali_appid\n        }\n\t\t\t\tthat.loaded();\n        // #ifdef H5\n        if(that.logintype_7 && that.alih5){\n          const oScript = document.createElement('script');\n          oScript.type = 'text/javascript';\n          oScript.src = 'https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js';\n          document.body.appendChild(oScript);\n        }\n        // #endif\n\t\t\t});\n\t\t},\n    formSubmit: function (e) {\n\t\t\tvar that = this;\n      var formdata = e.detail.value;\n      if (formdata.tel == ''){\n        app.alert('请输入手机号');\n        return;\n      }\n\t  \n\t\t\tif(!app.isPhone(formdata.tel)){\n\t\t\t\tapp.alert('请输入正确的手机号');\n\t\t\t\treturn;\n\t\t\t}\n\t  \n      if (formdata.pwd == '') {\n        app.alert('请输入密码');\n        return;\n      }\n      if (formdata.pwd.length < 6) {\n        app.alert('新密码不小于6位');\n        return;\n      }\n      if (formdata.repwd == '') {\n        app.alert('请再次输入新密码');\n        return;\n      }\n      if (formdata.pwd != formdata.repwd) {\n        app.alert('两次密码不一致');\n        return;\n      }\n\t\t\tif(that.needsms){\n\t\t\t\tif (formdata.smscode == '') {\n\t\t\t\t\tapp.alert('请输入短信验证码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}else{\n\t\t\t\tformdata.smscode = '';\n\t\t\t}\n\t\t\tvar postdata = {tel:formdata.tel,pwd:formdata.pwd,smscode:formdata.smscode,pid:app.globalData.pid,yqcode:formdata.yqcode,regbid:app.globalData.regbid}\n\t\t\t//如果有自定义表单则验证表单内容\n\t\t\tif(that.show_custom_field){\n\t\t\t\tvar customformdata = {};\n\t\t\t\tvar customData = that.checkCustomFormFields();\n\t\t\t\tif(!customData){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tpostdata['customformdata'] = customData\n\t\t\t\tpostdata['customformid'] = that.formfields.id\n\t\t\t}\n\t\t\t\n      if (that.xystatus == 1 && !that.isagree) {\n        app.error('请先阅读并同意用户注册协议');\n        return false;\n      }\n\t\t\tapp.showLoading('提交中');\n      app.post(\"ApiIndex/regsub\", postdata, function (data) {\n\t\t\t\tapp.showLoading(false);\n        if (data.status == 1) {\n          app.success(data.msg);\n\t\t\t\t\tif(data.tmplids){\n\t\t\t\t\t\tthat.tmplids = data.tmplids;\n\t\t\t\t\t}\n\t\t\t\t\tthat.subscribeMessage(function () {\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tif(that.opt.fromapp==1 && data.toappurl){\n\t\t\t\t\t\t\t\tapp.goto(data.toappurl,'redirect');\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n        } else {\n          app.error(data.msg);\n        }\n      });\n    },\n\t\tgetPhoneNumber: function (e) {\n\t\t\tvar that = this\n\t\t\tif(e.detail.errMsg == \"getPhoneNumber:fail user deny\"){\n\t\t\t\tapp.error('请同意授权获取手机号');return;\n\t\t\t}\n\t\t\twx.login({success (res1){\n\t\t\t\tconsole.log(res1);\n\t\t\t\tvar code = res1.code;\n\t\t\t\t//用户允许授权\n\t\t\t\tapp.post('ApiIndex/wxRegister',{ headimg:that.headimg,nickname:that.nickname,iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid,regbid:app.globalData.regbid},function(res2){\n\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t})\n\t\t\t}});\n\t\t},\n\t\tsetnicknameregister:function(e){\n\t\t\t//console.log(e);\n\t\t\t//return;\n\t\t\tthis.nickname = e.detail.value.nickname;\n\t\t\tif(this.nickname == '' || this.headimg == ''){\n\t\t\t\tapp.alert('请设置头像和昵称');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(this.login_bind!=0){\n\t\t\t\tthis.logintype = 4;\n\t\t\t}else{\n\t\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t\t}\n\t\t},\n\t\tnosetnicknameregister:function(){\n\t\t\tthis.nickname = '';\n\t\t\tthis.headimg = '';\n\t\t\tif(this.login_bind!=0){\n\t\t\t\tthis.logintype = 4;\n\t\t\t}else{\n\t\t\t\tthis.register('','','','');\n\t\t\t}\n\t\t},\n\t\tsetRegisterInvite:function(e){\n\t\t\tconsole.log(e);\n\t\t\t//return;\n\t\t\tthis.yqcode = e.detail.value.yqcode;\n\t\t\tif(this.yqcode == '' && !app.globalData.pid){\n\t\t\t\tapp.alert('请输入邀请码');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(this.login_setnickname!=0){\n\t\t\t\tthis.logintype = 5;\n\t\t\t\tthis.isioslogin = false;\n\t\t\t\tthis.isgooglelogin = false;\n\t\t\t}else{\n\t\t\t\tif(this.login_bind!=0){\n\t\t\t\t\tthis.logintype = 4;\n\t\t\t\t\tthis.isioslogin = false;\n\t\t\t\t\tthis.isgooglelogin = false;\n\t\t\t\t}else{\n\t\t\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tsetRegisterInvitePass:function(){\n\t\t\tif(this.login_setnickname!=0){\n\t\t\t\tthis.logintype = 5;\n\t\t\t\tthis.isioslogin = false;\n\t\t\t\tthis.isgooglelogin = false;\n\t\t\t}else{\n\t\t\t\tif(this.login_bind!=0){\n\t\t\t\t\tthis.logintype = 4;\n\t\t\t\t\tthis.isioslogin = false;\n\t\t\t\t\tthis.isgooglelogin = false;\n\t\t\t\t}else{\n\t\t\t\t\tthis.register('','','','');\n\t\t\t\t}\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tbindregister:function(e){\n\t\t\tvar that = this;\n\t\t\tvar formdata = e.detail.value;\n      if (formdata.tel == ''){\n        app.alert('请输入手机号');\n        return;\n      }\n\t\t\tif (formdata.smscode == '') {\n\t\t\t\tapp.alert('请输入短信验证码');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.register(this.headimg,this.nickname,formdata.tel,formdata.smscode);\n\t\t},\n\t\tnobindregister:function(){\n\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t},\n\t\tregister:function(headimg,nickname,tel,smscode){\n\t\t\tvar that = this;\n\t\t\tvar url = '';\n\t\t\tif(that.platform == 'app') {\n\t\t\t\turl = 'ApiIndex/appwxRegister';\n\t\t\t} else if(that.platform=='mp' || that.platform=='h5') {\n\t\t\t\turl = 'ApiIndex/shouquanRegister';\n\t\t\t} else {\n\t\t\t\turl = 'ApiIndex/'+that.platform+'Register';\n\t\t\t}\n\t\t\tapp.post(url,{headimg:headimg,nickname:nickname,tel:tel,smscode:smscode,pid:app.globalData.pid,yqcode:that.yqcode,regbid:app.globalData.regbid},function(res2){\n\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t});\n\t\t},\n\t\tweixinlogin:function(){\n\t\t\tvar that = this;\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\n\t\t\t\tthat.showxieyi = true;\n\t\t\t\tthat.wxloginclick = true;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.wxloginclick = false;\n\t\t\tapp.showLoading('授权中');\n\t\t\tapp.authlogin(function(res){\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else if (res.status == 4) {\n\t\t\t\t\t//填写邀请码\n\t\t\t\t\tthat.logintype = 6;\n\t\t\t\t\tthat.login_setnickname = res.login_setnickname;\n\t\t\t\t\tthat.login_bind = res.login_bind;//1可选设置\n\t\t\t\t} else if (res.status == 3) {\n\t\t\t\t\tthat.logintype = 5;\n\t\t\t\t\tthat.login_setnickname = res.login_setnickname\n\t\t\t\t\tthat.login_bind = res.login_bind\n\t\t\t\t} else if (res.status == 2) {\n\t\t\t\t\tthat.logintype = 4;\n\t\t\t\t\tthat.login_bind = res.login_bind\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t},{frompage:that.frompage,yqcode:that.yqcode});\n\t\t},\n\t\tyqinput: function (e) {\n\t\t  this.yqcode = e.detail.value\n\t\t},\n    isagreeChange: function (e) {\n      var val = e.detail.value;\n      if (val.length > 0) {\n        this.isagree = true;\n      } else {\n        this.isagree = false;\n      }\n      console.log(this.isagree);\n    },\n    showxieyiFun: function () {\n      this.showxieyi = true;\n    },\n    hidexieyi: function () {\n      this.showxieyi = false;\n\t\t\tthis.isagree = true;\n\t\t\tif(this.wxloginclick){\n\t\t\t\tthis.weixinlogin();\n\t\t\t}\n      if(this.alih5loginclick){\n        that.alih5login();\n      }\n    },\n    showxieyiFun2: function () {\n      this.showxieyi2 = true;\n    },\n    hidexieyi2: function () {\n      this.showxieyi2 = false;\n\t\t\tthis.isagree = true;\n\t\t\tif(this.wxloginclick){\n\t\t\t\tthis.weixinlogin();\n\t\t\t}\n\t\t\tif(this.iosloginclick){\n\t\t\t\tthis.ioslogin();\n\t\t\t}\n      if(this.alih5loginclick){\n        that.alih5login();\n      }\n    },\n    telinput: function (e) {\n      this.tel = e.detail.value\n    },\n\t\tuploadHeadimg:function(){\n\t\t\tvar that = this;\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tvar tempFilePaths = res.tempFilePaths;\n\t\t\t\t\tvar tempFilePath = tempFilePaths[0];\n\t\t\t\t\tapp.showLoading('上传中');\n\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\n\t\t\t\t\t\tfilePath: tempFilePath,\n\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\tthat.headimg = data.url;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: function(res) { //alert(res.errMsg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tonChooseAvatar:function(e){\n\t\t\tconsole.log(e)\n\t\t\tvar that = this;\n\t\t\tapp.showLoading('上传中');\n\t\t\tuni.uploadFile({\n\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\n\t\t\t\tfilePath: e.detail.avatarUrl,\n\t\t\t\tname: 'file',\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\tthat.headimg = data.url;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: function(res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    smscode: function () {\n      var that = this;\n      if (that.hqing == 1) return;\n      that.hqing = 1;\n      var tel = that.tel;\n      if (tel == '') {\n        app.alert('请输入手机号码');\n        that.hqing = 0;\n        return false;\n      }\n      if (!app.isPhone(tel)) {\n        app.alert(\"手机号码有误，请重填\");\n        that.hqing = 0;\n        return false;\n      }\n      app.post(\"ApiIndex/sendsms\", {tel: tel}, function (data) {\n        if (data.status != 1) {\n          app.alert(data.msg);\n        } \n      });\n      var time = 120;\n      var interval1 = setInterval(function () {\n        time--;\n        if (time < 0) {\n          that.smsdjs = '重新获取';\n          that.hqing = 0;\n          clearInterval(interval1);\n        } else if (time >= 0) {\n          that.smsdjs = time + '秒';\n        }\n      }, 1000);\n    },\n\t\t//自定义表单\n\t\tonchange(e) {\n\t\t  const value = e.detail.value\n\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;\n\t\t},\n\t\tsetfield:function(e){\n\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\tvar value = e.detail.value;\n\t\t\tthis.formvaldata[field] = value;\n\t\t},\n\t\teditorBindPickerChange:function(e){\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar val = e.detail.value;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\t\n\t\t\tif(!editorFormdata) editorFormdata = {};\n\t\t\teditorFormdata[idx] = val;\n\t\t\tthat.editorFormdata = editorFormdata\n\t\t\tthis.test = Math.random();\n\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\tthis.formvaldata[field] = val;\n\t\t},\n\t\tcheckCustomFormFields:function(e){\n\t\t\tvar that = this;\n\t\t\tvar subdata = this.formvaldata;\n\t\t\tvar formcontent = that.formfields.content;\n\t\t\tvar formid = that.formfields.id;\n\t\t\tvar formdata = {};\n\t\t\tfor (var i = 0; i < formcontent.length;i++){\n\t\t\t\t// console.log(subdata['form' + i]);\n\t\t\t\tvar value = subdata['form' + i];\n\t\t\t\tif (formcontent[i].key == 'region') {\n\t\t\t\t\t\tvalue = that.regiondata;\t\t\t\t\t \n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif ((formcontent[i].key == 'region' && value === '') && formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === null || subdata['form' + i] === undefined || subdata['form' + i].length==0)){\n\t\t\t\t\t \n\t\t\t\t\tapp.alert(formcontent[i].val1+' 必填');return false;\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].key =='switch'){\n\t\t\t\t\t\tif (subdata['form' + i]==false){\n\t\t\t\t\t\t\t\tvalue = '否'\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tvalue = '是'\n\t\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].key == 'selector') {\n\t\t\t\t\t\tvalue = formcontent[i].val2[subdata['form' + i]]\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].key == 'usercard' && subdata['form' + i]!='') {\n\t\t\t\t\tif(!app.isIdCard(subdata['form' + i])){\n\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].key == 'input' && formcontent[i].val4 && subdata['form' + i]!==''){\n\t\t\t\t\tif(formcontent[i].val4 == '2'){ //手机号\n\t\t\t\t\t\tif (!app.isPhone(subdata['form' + i])) {\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(formcontent[i].val4 == '3'){ //身份证号\n\t\t\t\t\t\tif (!app.isIdCard(subdata['form' + i])) {\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(formcontent[i].val4 == '4'){ //邮箱\n\t\t\t\t\t\tif (!/^(.+)@(.+)$/.test(subdata['form' + i])) {\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tformdata['form' + i] = value;\n\t\t\t}\n\t\t\treturn formdata;\n\t\t},\n\t\teditorChooseImage: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\tapp.chooseImage(function(data){\n\t\t\t\teditorFormdata[idx] = data[0];\n\t\t\t\tconsole.log(editorFormdata)\n\t\t\t\tthat.editorFormdata = editorFormdata\n\t\t\t\tthat.test = Math.random();\n\t\t\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\t\tthat.formvaldata[field] = data[0];\n\t\t\n\t\t\t})\n\t\t},\n\t\teditorChooseFile: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\tapp.chooseFile(function(data){\n\t\t\t\teditorFormdata[idx] = data;\n\t\t\t\tconsole.log(editorFormdata)\n\t\t\t\tthat.editorFormdata = editorFormdata\n\t\t\t\tthat.test = Math.random();\n\t\t\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\t\tthat.formvaldata[field] = data;\n\t\t\n\t\t\t})\n\t\t},\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\teditorFormdata[idx] = '';\n\t\t\tthat.editorFormdata = editorFormdata\n\t\t\tthat.test = Math.random();\n\t\t\tthat.formvaldata[field] = '';\n\t\t},\n    alih5login:function(){\n      // #ifdef H5\n      var that = this;\n      var ali_appid = that.ali_appid;\n\n      if (that.xystatus == 1 && !that.isagree) {\n      \tthat.showxieyi = true;\n      \tthat.alih5loginclick = true;\n      \treturn;\n      }\n      that.alih5loginclick = false;\n      \n      if(ali_appid){\n        app.showLoading('登录中');\n        ap.getAuthCode ({\n            appId :  ali_appid ,\n            scopes : ['auth_base'],\n        },function(res){\n           //var res = JSON.stringify(res);\n            if(!res.error && res.authCode){\n                app.post('ApiIndex/alipaylogin', {\n                \tcode: res.authCode,\n                \tpid: app.globalData.pid,\n                  platform:\"h5\",\n                  regbid:app.globalData.regbid\n                }, function(res2) {\n                  app.showLoading(false);\n                  \n                \tif (res2.status == 1) {\n                \t\tapp.success(res2.msg);\n                \t\tsetTimeout(function () {\n                \t\t\tconsole.log('frompage')\n                \t\t\tconsole.log(that.frompage)\n                \t\t\tapp.goto(that.frompage,'redirect');\n                \t\t}, 1000);\n                \t} else if (res2.status == 3) {\n                \t\tthat.logintype = 5;\n                \t\tthat.isioslogin = true;\n                \t\tthat.isgooglelogin = false;\n                \t\tthat.login_setnickname = res2.login_setnickname\n                \t\tthat.login_bind = res2.login_bind\n                \t} else if (res2.status == 2) {\n                \t\tthat.logintype = 4;\n                \t\tthat.isioslogin = true;\n                \t\tthat.isgooglelogin = false;\n                \t\tthat.login_bind = res2.login_bind\n                \t} else {\n                \t\tapp.error(res2.msg);\n                \t}\n                });\n            }else{\n              app.showLoading(false);\n              \n              if(res.errorMessage){\n                app.alert(res.errorMessage);\n              }else if(res.errorDesc){\n                app.alert(res.errorDesc);\n              }else{\n                app.alert('授权出错');\n              }\n              return\n            }\n        });\n      }else{\n        app.alert('系统未配置支付宝参数');\n        return\n      }\n      // #endif\n    },\n    getBaiduPhoneNumber: function (e) {\n      // #ifdef MP-BAIDU\n    \tvar that = this\n    \tconsole.log(e);\n    \tif(e.detail.errMsg == \"getPhoneNumber:fail auth deny\"){\n    \t\tapp.error('请同意授权获取手机号');return;\n    \t}\n    \tif(!e.detail.iv || !e.detail.encryptedData){\n    \t\tapp.error('请同意授权获取手机号');return;\n    \t}\n      uni.getLoginCode({\n      \tsuccess: res => {\n      \t\tconsole.log(res);\n      \t\tvar code = res.code;\n      \t\t//用户允许授权\n      \t\tvar postdata = { \n      \t\t  headimg:that.headimg,\n      \t\t  nickname:that.nickname,\n      \t\t  code:code,\n      \t\t  iv: e.detail.iv,\n      \t\t  encryptedData:e.detail.encryptedData,\n      \t\t  pid:app.globalData.pid,\n      \t\t  yqcode:that.yqcode,\n      \t\t  regbid:app.globalData.regbid,\n      \t\t}\n      \t\tapp.post('ApiIndex/baiduRegister',postdata,function(res2){\n      \t\t\tif (res2.status == 1) {\n      \t\t\t\tapp.success(res2.msg);\n      \t\t\t\tif(res2.tmplids){\n      \t\t\t\t\tthat.tmplids = res2.tmplids;\n      \t\t\t\t}\n      \t\t\t\tthat.subscribeMessage(function () {\n      \t\t\t\t\tsetTimeout(function () {\n      \t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n      \t\t\t\t\t}, 1000);\n      \t\t\t\t});\n      \t\t\t} else {\n      \t\t\t\tapp.error(res2.msg);\n      \t\t\t}\n      \t\t\treturn;\n      \t\t})\n      \t},\n      \tfail: err => {\n      \t\ttypeof callback == \"function\" && callback({\n      \t\t\tstatus: 0,\n      \t\t\tmsg: err.errMsg\n      \t\t});\n      \t}\n      });\n      // #endif\n    },\n  }\n};\n</script>\n\n<style>\npage{background:#ffffff;width: 100%;height:100%;}\n.container{width:100%;height:100%;}\n.title{margin:70rpx 50rpx 50rpx 40rpx;height:60rpx;line-height:60rpx;font-size: 48rpx;font-weight: bold;color: #000000;}\n.regform{ width:100%;border-radius:5px;}\n.regform .form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:88rpx;line-height:88rpx;border-bottom:1px solid #F0F3F6;margin-top:20rpx;background: #fff;border-radius: 8rpx;padding: 0 20rpx;}\n.regform .form-item:last-child{border:0}\n.regform .form-item .img{width:44rpx;height:44rpx;margin-right:30rpx}\n.regform .form-item .input{flex:1;color: #000;}\n.regform .form-item .code{font-size:30rpx}\n.regform .xieyi-item{display:flex;align-items:center;margin-top:50rpx}\n.regform .xieyi-item{font-size:24rpx;color:#B2B5BE}\n.regform .xieyi-item .checkbox{transform: scale(0.6);}\n.regform .form-btn{margin-top:20rpx;width:100%;height:96rpx;line-height:96rpx;color:#fff;font-size:30rpx;border-radius: 48rpx;}\n.regform .form-btn2{width:100%;height:80rpx;line-height:80rpx;background:#EEEEEE;border-radius:40rpx;color:#A9A9A9;margin-top:30rpx;font-size: 28rpx;}\n.tologin{color:#737785;font-size:26rpx;display:flex;width:100%;margin-top:30rpx}\n\n.othertip{height:auto;overflow: hidden;display:flex;align-items:center;width:580rpx;padding:20rpx 20rpx;margin:0 auto;margin-top:60rpx;}\n.othertip-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #F2F2F2}\n.othertip-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\n.othertip-text .txt{color:#A3A3A3;font-size:22rpx}\n\n.othertype{width:70%;margin:20rpx 15%;display:flex;justify-content:center;}\n.othertype-item{width:50%;display:flex;flex-direction:column;align-items:center;}\n.othertype-item .img{width:88rpx;height:88rpx;margin-bottom:20rpx}\n.othertype-item .txt{color:#A3A3A3;font-size:24rpx}\n\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\n.xieyibox-content .xieyibut-view{height: 60rpx;position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;justify-content: space-around;}\n.xieyibox-content .xieyibut-view .but-class{text-align:center; width: auto;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;padding:0rpx 25rpx;}\n\n.authlogin{display:flex;flex-direction:column;align-items:center}\n.authlogin-logo{width:180rpx;height:180rpx;margin-top:120rpx}\n.authlogin-name{color:#999999;font-size:30rpx;margin-top:60rpx;}\n.authlogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#51B1F5;border-radius:48rpx;color:#fff;margin-top:100rpx}\n.authlogin-btn2{width:580rpx;height:96rpx;line-height:96rpx;background:#EEEEEE;border-radius:48rpx;color:#A9A9A9;margin-top:20rpx}\n\n\n/* 自定义字段显示 */\n.dp-form-item{width: 100%;display:flex;align-items: center;border-bottom:1px solid #F0F3F6;padding: 10rpx 0;}\n/* .dp-form-item:last-child{border:0} */\n.dp-form-item .label{line-height: 50rpx;width:156rpx;margin-right: 10px;flex-shrink:0;text-align: right;color: #666666;font-size: 28rpx;}\n.dp-form-item .input{height: 88rpx;line-height: 88rpx;overflow: hidden;flex:1;border-radius:2px;}\n.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\n.dp-form-item .radio{height: 88rpx;line-height: 88rpx;display:flex;align-items:center}\n.dp-form-item .radio2{display:flex;align-items:center;}\n.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\n.dp-form-item .checkbox{height: 88rpx;line-height: 88rpx;display:flex;align-items:center}\n.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\n.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\n.dp-form-item .layui-form-switch{}\n.dp-form-item .picker{height: 88rpx;line-height:88rpx;flex:1;}\n\n.dp-form-item2{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;flex-direction:column;align-items: flex-start;}\n.dp-form-item2:last-child{border:0}\n.dp-form-item2 .label{height:88rpx;line-height: 88rpx;width:100%;margin-right: 10px;}\n.dp-form-item2 .input{height: 88rpx;line-height: 88rpx;overflow: hidden;width:100%;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff}\n.dp-form-item2 .textarea{height:180rpx;line-height:40rpx;overflow: hidden;width:100%;border:1px solid #eee;border-radius:2px;padding:8rpx}\n.dp-form-item2 .radio{height: 88rpx;line-height: 88rpx;display:flex;align-items:center;}\n.dp-form-item2 .radio2{display:flex;align-items:center;}\n.dp-form-item2 .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\n.dp-form-item2 .checkbox{height: 88rpx;line-height: 88rpx;display:flex;align-items:center;}\n.dp-form-item2 .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\n.dp-form-item2 .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\n.dp-form-item2 .layui-form-switch{}\n.dp-form-item2 .picker{height: 88rpx;line-height:88rpx;flex:1;width:100%;}\n.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}\n\n.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\n.dp-form-imgbox-close .image{width:100%;height:100%}\n.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.dp-form-imgbox-img>.image{max-width:100%;}\n.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}\n\n.textarea{}\n.bg_div1{width:100%;min-height: 100%;overflow: hidden;}\n.content_div1{width: 700rpx; margin: 0 auto;margin-bottom: 60rpx;}\n.title1{opacity: 1;font-size: 50rpx;font-weight: bold;line-height: 90rpx;text-align: left;margin-top: 80rpx;}\n.subhead1{font-size: 28rpx;font-weight: 500;line-height: 40rpx;}\n.card_div1{width: 100%;padding:40rpx;border-radius: 24rpx;margin-top: 40rpx;}\n.tel1{width:100%;height:88rpx;border-radius: 88rpx;line-height: 88rpx;background-color: #F5F7FA;padding:0 40rpx;margin: 20rpx 0;margin-top: 30rpx;}\n.code1{height: 88rpx;font-size: 24rpx;line-height: 88rpx;float: right;}\n.btn1{width:100%;height:88rpx;border-radius: 88rpx;line-height: 88rpx;margin: 20rpx 0;text-align: center;font-weight: bold;}\n.other_line{width: 106rpx;height: 2rpx;background: #D8D8D8;margin-top: 20rpx;}\n.logo2{width: 200rpx;height: 200rpx;margin: 0 auto;margin-top:40rpx;margin-bottom: 40rpx;border-radius: 12rpx;overflow: hidden;}\n.inputcode{width:300rpx;height:88rpx;line-height: 88rpx;display: inline-block;}\n.input_val{width:100%;height:88rpx;line-height: 88rpx;}\n.xycss1{line-height: 60rpx;font-size: 24rpx;overflow: hidden;margin-top: 20rpx;}\n.other_login{width:420rpx;margin: 60rpx auto;}\n.overflow_ellipsis{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 380rpx;}\n.other_content{overflow: hidden;width: 100%;margin-top: 60rpx;text-align: center;display: flex;justify-content:center;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reg.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reg.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753839379629\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}