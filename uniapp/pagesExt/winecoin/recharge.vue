<template>
<view class="container">
	<block v-if="isload">
		<!-- 当前酒币余额 -->
		<view class="wine-balance" :style="{background: balanceBg}">
			<view class="balance-info">
				<view class="balance-label">我的酒币</view>
				<view class="balance-amount">{{userinfo.wine_coin || '0.00'}}</view>
				<view class="balance-tips" @tap="goto" data-url="/pagesExt/winecoin/log">
					<text>充值记录</text>
					<text class="iconfont iconjiantou" style="font-size:20rpx"></text>
				</view>
			</view>
		</view>
		
		<!-- 充值金额选择 -->
		<view class="recharge-content">
			<!-- 自定义金额输入 -->
			<view class="amount-section" v-if="rechargeSet.can_input == 1">
				<view class="section-title">充值金额（元）</view>
				<view class="amount-input-box">
					<text class="currency-symbol">¥</text>
					<input 
						type="digit" 
						name="amount" 
						:value="rechargeAmount" 
						placeholder="请输入充值金额" 
						placeholder-style="color:#999;font-size:40rpx" 
						@input="onAmountInput" 
						style="font-size:60rpx"
					/>
				</view>
				<view class="amount-tips" v-if="rechargeSet.min_amount > 0">
					最低充值{{rechargeSet.min_amount}}元
				</view>
			</view>
			
			<!-- 预设金额选项 -->
			<view class="preset-amounts" v-if="rechargeSet.give_sets && rechargeSet.give_sets.length > 0">
				<view class="section-title" v-if="rechargeSet.can_input == 0">选择充值金额</view>
				<view class="amount-grid">
					<view 
						v-for="(item, index) in rechargeSet.give_sets" 
						:key="index" 
						v-if="item.money > 0" 
						class="amount-item" 
						:class="selectedAmount == item.money ? 'selected' : ''" 
						@tap="selectAmount" 
						:data-amount="item.money"
						:data-give="item.give"
					>
						<text class="amount-money">{{rechargeSet.can_input == 1 ? '满' : '充'}}{{item.money}}元</text>
						<text class="amount-give" v-if="item.give > 0">赠{{item.give}}酒币</text>
						<text class="amount-give" v-if="item.give_score > 0">+{{item.give_score}}积分</text>
					</view>
				</view>
			</view>
			
			<!-- 充值说明 -->
			<view class="recharge-desc" v-if="rechargeSet.recharge_desc">
				<view class="desc-title">充值说明</view>
				<text class="desc-content">{{rechargeSet.recharge_desc}}</text>
			</view>
		</view>
		
		<!-- 充值按钮 -->
		<view class="recharge-actions">
			<view class="recharge-btn" @tap="goRecharge" :style="{background: btnBg}">
				立即充值
			</view>
		</view>
	</block>
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			userinfo: {},
			rechargeSet: {},
			rechargeAmount: '', // 用户输入的充值金额
			selectedAmount: 0,  // 选中的预设金额
			selectedGive: 0,    // 选中金额对应的赠送
			balanceBg: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
			btnBg: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%)'
		}
	},
	
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	
	onPullDownRefresh: function () {
		this.getdata();
	},
	
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			
			app.get('ApiWineCoin/recharge', {}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					that.isload = true;
					uni.setNavigationBarTitle({
						title: '酒币充值'
					});
					
					that.userinfo = res.userinfo;
					that.rechargeSet = res.recharge_set;
					
					// 检查是否允许充值
					if (that.rechargeSet.can_recharge == 0) {
						app.goto('/pagesExt/winecoin/log', 'redirect');
						return;
					}
					
					// 检查配置是否合理
					if (that.rechargeSet.can_input == 0 && (!that.rechargeSet.give_sets || that.rechargeSet.give_sets.length == 0)) {
						app.alert('后台未设置充值金额也未开启输入金额，请联系客服');
						return;
					}
					
					that.loaded();
				} else {
					app.error(res.msg || '获取数据失败');
				}
			});
		},
		
		onAmountInput: function(e) {
			var amount = e.detail.value;
			this.rechargeAmount = amount;
			
			// 清除预设选择
			this.selectedAmount = 0;
			this.selectedGive = 0;
			
			// 检查金额有效性
			if (parseFloat(amount) < 0) {
				app.error('金额必须大于0');
				return;
			}
			
			// 检查是否满足预设赠送条件
			if (this.rechargeSet.give_sets && this.rechargeSet.give_sets.length > 0) {
				var maxGiveAmount = 0;
				var maxGive = 0;
				
				for (var i in this.rechargeSet.give_sets) {
					var giveSet = this.rechargeSet.give_sets[i];
					if (parseFloat(amount) >= giveSet.money && giveSet.money > maxGiveAmount) {
						maxGiveAmount = giveSet.money;
						maxGive = giveSet.give;
					}
				}
				
				this.selectedGive = maxGive;
			}
		},
		
		selectAmount: function(e) {
			var amount = parseFloat(e.currentTarget.dataset.amount);
			var give = parseFloat(e.currentTarget.dataset.give || 0);
			
			this.selectedAmount = amount;
			this.selectedGive = give;
			this.rechargeAmount = amount.toString();
		},
		
		goRecharge: function() {
			var amount = parseFloat(this.rechargeAmount);
			
			// 验证金额
			if (!amount || amount <= 0) {
				app.error('请输入有效的充值金额');
				return;
			}
			
			if (this.rechargeSet.min_amount && amount < this.rechargeSet.min_amount) {
				app.error('充值金额不能低于' + this.rechargeSet.min_amount + '元');
				return;
			}
			
			if (this.rechargeSet.max_amount && amount > this.rechargeSet.max_amount) {
				app.error('充值金额不能超过' + this.rechargeSet.max_amount + '元');
				return;
			}
			
			// 跳转到支付页面
			var payParams = {
				type: 'winecoin_recharge',
				amount: amount,
				give: this.selectedGive || 0,
				title: '酒币充值',
				desc: '充值' + amount + '元' + (this.selectedGive > 0 ? '，赠送' + this.selectedGive + '酒币' : '')
			};
			
			var payUrl = '/pagesExt/pay/pay?' + this.buildQueryString(payParams);
			app.goto(payUrl);
		},
		
		buildQueryString: function(params) {
			var query = [];
			for (var key in params) {
				query.push(key + '=' + encodeURIComponent(params[key]));
			}
			return query.join('&');
		},
		
		loaded: function() {
			this.loading = false;
		}
	}
}
</script>

<style>
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 酒币余额显示 */
.wine-balance {
	padding: 40rpx 30rpx;
	margin: 20rpx;
	border-radius: 24rpx;
	color: #fff;
}

.balance-info {
	text-align: center;
}

.balance-label {
	font-size: 28rpx;
	opacity: 0.9;
	margin-bottom: 16rpx;
}

.balance-amount {
	font-size: 72rpx;
	font-weight: 700;
	margin-bottom: 20rpx;
}

.balance-tips {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	opacity: 0.8;
}

.balance-tips text:first-child {
	margin-right: 8rpx;
}

/* 充值内容 */
.recharge-content {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.section-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
	padding: 32rpx 24rpx 16rpx;
}

/* 金额输入 */
.amount-section {
	padding: 0 24rpx;
}

.amount-input-box {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
}

.currency-symbol {
	color: #64748B;
	font-size: 48rpx;
	margin-right: 16rpx;
}

.amount-input-box input {
	flex: 1;
	color: #1E293B;
	font-weight: 600;
}

.amount-tips {
	color: #64748B;
	font-size: 24rpx;
	padding-bottom: 24rpx;
}

/* 预设金额 */
.preset-amounts {
	padding: 0 24rpx 24rpx;
}

.amount-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.amount-item {
	flex: 1;
	min-width: calc(50% - 8rpx);
	padding: 32rpx 16rpx;
	border: 2rpx solid #E2E8F0;
	border-radius: 12rpx;
	text-align: center;
	background: #fff;
	transition: all 0.3s ease;
}

.amount-item.selected {
	border-color: #FFD700;
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
}

.amount-money {
	display: block;
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.amount-give {
	display: block;
	color: #EF4444;
	font-size: 24rpx;
	line-height: 1.4;
}

/* 充值说明 */
.recharge-desc {
	padding: 24rpx;
	border-top: 1rpx solid #F1F5F9;
}

.desc-title {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
}

.desc-content {
	color: #64748B;
	font-size: 24rpx;
	line-height: 1.6;
}

/* 充值按钮 */
.recharge-actions {
	padding: 40rpx 20rpx;
}

.recharge-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}
</style> 