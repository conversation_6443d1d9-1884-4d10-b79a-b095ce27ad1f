<template>
<view class="container">
	<block v-if="isload">
		<!-- 酒币钱包卡片 -->
		<view class="wine-wallet-card">
			<view class="wallet-bg" :style="{background: walletBg}">
				<view class="wallet-header">
					<view class="wallet-title">
						<image class="wallet-icon" :src="pre_url+'/static/img/winecoin-icon.png'"></image>
						<text>酒币钱包</text>
					</view>
					<view class="wallet-help" @tap="goto" data-url="/pagesExt/winecoin/help">
						<text class="iconfont iconbangzhu"></text>
					</view>
				</view>
				
				<view class="balance-section">
					<view class="balance-label">我的酒币</view>
					<view class="balance-amount">{{userinfo.wine_coin || '0.00'}}</view>
				</view>
				
				<view class="quick-actions">
					<view class="action-item recharge-item" @tap="goto" data-url="/pagesExt/winecoin/recharge">
						<view class="action-symbol">+</view>
						<text>充值</text>
					</view>
					<view class="action-item withdraw-item" @tap="goto" data-url="/pagesExt/winecoin/withdraw">
						<view class="action-symbol">-</view>
						<text>提现</text>
					</view>
					<view class="action-item transfer-item" @tap="goto" data-url="/pagesExt/winecoin/transfer">
						<view class="action-symbol">⇄</view>
						<text>转账</text>
					</view>
					<view class="action-item record-item" @tap="goto" data-url="/pagesExt/winecoin/log">
						<view class="action-symbol">≡</view>
						<text>明细</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="feature-menu">
			<view class="menu-item" @tap="goto" data-url="/pagesExt/winecoin/statistics">
				<view class="menu-icon statistics-icon">
					<text class="menu-symbol">📊</text>
				</view>
				<view class="menu-content">
					<text class="menu-title">收支统计</text>
					<text class="menu-desc">查看酒币收支详情</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @tap="goto" data-url="/pagesExt/winecoin/payment">
				<view class="menu-icon payment-icon">
					<text class="menu-symbol">💳</text>
				</view>
				<view class="menu-content">
					<text class="menu-title">酒币支付</text>
					<text class="menu-desc">使用酒币购买商品</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
		</view>

		<!-- 最近交易 -->
		<view class="recent-transactions" v-if="recentLogs.length > 0">
			<view class="section-header">
				<text class="section-title">最近交易</text>
				<text class="section-more" @tap="goto" data-url="/pagesExt/winecoin/log">查看全部</text>
			</view>
			
			<view class="transaction-list">
				<view class="transaction-item" v-for="(item, index) in recentLogs" :key="index" @tap="viewDetail" :data-id="item.id">
					<view class="transaction-icon">
						<text class="transaction-symbol">{{getTransactionIcon(item.type)}}</text>
					</view>
					<view class="transaction-info">
						<text class="transaction-title">{{getTransactionTitle(item.type)}}</text>
						<text class="transaction-time">{{item.createtime}}</text>
					</view>
					<view class="transaction-amount" :class="item.amount > 0 ? 'positive' : 'negative'">
						<text>{{item.amount > 0 ? '+' : ''}}{{item.amount}}</text>
					</view>
				</view>
			</view>
		</view>
	</block>
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			pre_url: app.globalData.pre_url,
			userinfo: {},
			recentLogs: [],
			wineCoinRate: 1, // 酒币兑换比例 1酒币=1元
			walletBg: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)'
		}
	},
	
	computed: {
		wineCoinValue() {
			const balance = parseFloat(this.userinfo.wine_coin || 0);
			return (balance * this.wineCoinRate).toFixed(2);
		}
	},
	
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	
	onShow: function() {
		// 页面显示时刷新数据
		if (this.isload) {
			this.getdata();
		}
	},
	
	onPullDownRefresh: function () {
		this.getdata();
	},
	
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			
			app.get('ApiWineCoin/wallet', {}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					that.isload = true;
					uni.setNavigationBarTitle({
						title: '酒币钱包'
					});
					
					that.userinfo = res.userinfo;
					that.recentLogs = res.recent_logs || [];
					that.wineCoinRate = res.wine_coin_rate || 1;
					
					that.loaded();
				} else {
					app.error(res.msg || '获取数据失败');
				}
			});
		},
		
		getTransactionIcon: function(type) {
			const symbolMap = {
				'recharge': '+',
				'withdraw': '-', 
				'transfer_in': '⬅',
				'transfer_out': '➡',
				'payment': '💳',
				'refund': '↩',
				'admin_recharge': '⭐',
				'admin_deduct': '⚠'
			};
			return symbolMap[type] || '📝';
		},
		
		getTransactionTitle: function(type) {
			const titleMap = {
				'recharge': '酒币充值',
				'withdraw': '酒币提现',
				'transfer_in': '转账收入', 
				'transfer_out': '转账支出',
				'payment': '酒币支付',
				'refund': '退款',
				'admin_recharge': '系统充值',
				'admin_deduct': '系统扣除'
			};
			return titleMap[type] || '其他交易';
		},
		
		viewDetail: function(e) {
			const id = e.currentTarget.dataset.id;
			app.goto('/pagesExt/winecoin/detail?id=' + id);
		},
		
		loaded: function() {
			this.loading = false;
		}
	}
}
</script>

<style>
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 酒币钱包卡片 */
.wine-wallet-card {
	margin: 20rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
}

.wallet-bg {
	padding: 40rpx 30rpx;
	position: relative;
}

.wallet-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.wallet-title {
	display: flex;
	align-items: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}

.wallet-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 16rpx;
}

.wallet-help {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	color: #fff;
	font-size: 32rpx;
}

.balance-section {
	text-align: center;
	margin-bottom: 40rpx;
}

.balance-label {
	color: rgba(255, 255, 255, 0.8);
	font-size: 28rpx;
	margin-bottom: 16rpx;
}

.balance-amount {
	color: #fff;
	font-size: 72rpx;
	font-weight: 700;
	line-height: 1;
	margin-bottom: 8rpx;
}

.quick-actions {
	display: flex;
	justify-content: space-around;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #fff;
	font-size: 24rpx;
	padding: 16rpx 12rpx;
	border-radius: 16rpx;
	min-width: 120rpx;
	transition: all 0.3s ease;
}

.action-symbol {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12rpx;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
}

/* 不同功能的特色背景 */
.recharge-item:active {
	background: rgba(16, 185, 129, 0.2);
}

.withdraw-item:active {
	background: rgba(239, 68, 68, 0.2);
}

.transfer-item:active {
	background: rgba(59, 130, 246, 0.2);
}

.record-item:active {
	background: rgba(156, 163, 175, 0.2);
}

/* 功能菜单 */
.feature-menu {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	background: #F3F4F6;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.statistics-icon {
	background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.payment-icon {
	background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
}

.menu-symbol {
	font-size: 32rpx;
}

.menu-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.menu-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.menu-desc {
	color: #64748B;
	font-size: 24rpx;
}

.menu-arrow {
	color: #CBD5E1;
	font-size: 24rpx;
}

/* 最近交易 */
.recent-transactions {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 24rpx 16rpx;
}

.section-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
}

.section-more {
	color: #3B82F6;
	font-size: 24rpx;
}

.transaction-list {
	padding: 0 24rpx 16rpx;
}

.transaction-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #F1F5F9;
}

.transaction-item:last-child {
	border-bottom: none;
}

.transaction-icon {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	background: #F1F5F9;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.transaction-symbol {
	font-size: 28rpx;
	color: #64748B;
	font-weight: bold;
}

.transaction-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.transaction-title {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.transaction-time {
	color: #64748B;
	font-size: 24rpx;
}

.transaction-amount {
	font-size: 32rpx;
	font-weight: 600;
}

.transaction-amount.positive {
	color: #10B981;
}

.transaction-amount.negative {
	color: #EF4444;
}
</style> 