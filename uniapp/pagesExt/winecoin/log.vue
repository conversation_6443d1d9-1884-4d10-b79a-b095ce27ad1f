<template>
<view class="container">
	<block v-if="isload">
		<!-- 筛选选项 -->
		<scroll-view class="filter-tabs-container" scroll-x="true" show-scrollbar="false">
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="selectedType == '' ? 'active' : ''" 
					@tap="selectType" 
					data-type=""
				>
					全部
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'recharge' ? 'active' : ''" 
					@tap="selectType" 
					data-type="recharge"
				>
					充值
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'withdraw' ? 'active' : ''" 
					@tap="selectType" 
					data-type="withdraw"
				>
					提现
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'transfer_in' ? 'active' : ''" 
					@tap="selectType" 
					data-type="transfer_in"
				>
					转入
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'transfer_out' ? 'active' : ''" 
					@tap="selectType" 
					data-type="transfer_out"
				>
					转出
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'payment' ? 'active' : ''" 
					@tap="selectType" 
					data-type="payment"
				>
					支付
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'refund' ? 'active' : ''" 
					@tap="selectType" 
					data-type="refund"
				>
					退款
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'bonus' ? 'active' : ''" 
					@tap="selectType" 
					data-type="bonus"
				>
					奖励
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'fee' ? 'active' : ''" 
					@tap="selectType" 
					data-type="fee"
				>
					手续费
				</view>
				<view 
					class="filter-tab" 
					:class="selectedType == 'activity' ? 'active' : ''" 
					@tap="selectType" 
					data-type="activity"
				>
					活动
				</view>
			</view>
		</scroll-view>
		
		<!-- 流水列表 -->
		<view class="log-list" v-if="logList.length > 0">
			<view 
				class="log-item" 
				v-for="(item, index) in logList" 
				:key="index"
				@tap="viewDetail" 
				:data-id="item.id"
			>
				<view class="log-icon">
					<text class="log-symbol">{{getLogIcon(item.type)}}</text>
				</view>
				<view class="log-info">
					<view class="log-title">
						<text class="title-text">{{item.type_desc}}</text>
						<text class="other-user" v-if="item.other_user">{{getOtherUserDesc(item.type, item.other_user)}}</text>
					</view>
					<view class="log-time">{{item.createtime}}</view>
					<view class="log-remark" v-if="item.remark">{{item.remark}}</view>
				</view>
				<view class="log-amount">
					<text class="amount-value" :class="item.amount >= 0 ? 'positive' : 'negative'">
						{{item.amount >= 0 ? '+' : ''}}{{item.amount}}
					</text>
					<text class="balance-after">余额: {{item.balance_after}}</text>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="isload && logList.length === 0">
			<image class="empty-icon" :src="pre_url+'/static/img/empty-transaction.png'"></image>
			<text class="empty-text">暂无交易记录</text>
			<text class="empty-desc">快去充值酒币吧~</text>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="logList.length > 0">
			<text v-if="!loadingMore && hasMore" @tap="loadMore">点击加载更多</text>
			<text v-if="loadingMore">加载中...</text>
			<text v-if="!hasMore && logList.length > 0">没有更多数据了</text>
		</view>
	</block>
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			loadingMore: false,
			isload: false,
			pre_url: app.globalData.pre_url,
			logList: [],
			selectedType: '', // 选中的类型筛选
			currentPage: 1,
			pageSize: 20,
			totalCount: 0,
			hasMore: true
		}
	},
	
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		
		// 获取传入的类型参数
		if (opt.type) {
			this.selectedType = opt.type;
		}
		
		this.getdata();
	},
	
	onPullDownRefresh: function () {
		this.refresh();
	},
	
	onReachBottom: function () {
		if (this.hasMore && !this.loadingMore) {
			this.loadMore();
		}
	},
	
	methods: {
		getdata: function() {
			this.currentPage = 1;
			this.loadData();
		},
		
		refresh: function() {
			this.currentPage = 1;
			this.logList = [];
			this.hasMore = true;
			this.loadData();
		},
		
		loadData: function() {
			var that = this;
			var isRefresh = that.currentPage === 1;
			
			if (isRefresh) {
				that.loading = true;
			} else {
				that.loadingMore = true;
			}
			
			var params = {
				page: that.currentPage,
				limit: that.pageSize
			};
			
			if (that.selectedType) {
				params.type = that.selectedType;
			}
			
			app.get('ApiWineCoin/log', params, function (res) {
				if (isRefresh) {
					that.loading = false;
				} else {
					that.loadingMore = false;
				}
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					if (!that.isload) {
						that.isload = true;
						uni.setNavigationBarTitle({
							title: '酒币明细'
						});
					}
					
					that.totalCount = res.count || 0;
					var newLogs = res.logs || [];
					
					if (isRefresh) {
						that.logList = newLogs;
					} else {
						that.logList = that.logList.concat(newLogs);
					}
					
					// 判断是否还有更多数据
					that.hasMore = that.logList.length < that.totalCount;
					
					that.loaded();
				} else {
					app.error(res.msg || '获取数据失败');
				}
			});
		},
		
		selectType: function(e) {
			var type = e.currentTarget.dataset.type;
			if (this.selectedType !== type) {
				this.selectedType = type;
				this.refresh();
			}
		},
		
		loadMore: function() {
			if (this.hasMore && !this.loadingMore) {
				this.currentPage++;
				this.loadData();
			}
		},
		
		getLogIcon: function(type) {
			var symbolMap = {
				'recharge': '+',
				'withdraw': '-',
				'transfer_in': '⬅',
				'transfer_out': '➡',
				'payment': '💳',
				'refund': '↩',
				'fee': '💰',
				'business_income': '🏪',
				'bonus': '🎁',
				'penalty': '⚠️',
				'adjustment': '🔧',
				'manual': '✋',
				'activity': '🎉',
				'settlement': '📊',
				'admin_recharge': '⭐',
				'admin_deduct': '⚠',
				'commission': '💎',
				'activity_reward': '🏆'
			};
			return symbolMap[type] || '📝';
		},
		
		getOtherUserDesc: function(type, otherUser) {
			if (type === 'transfer_in') {
				return '来自: ' + otherUser;
			} else if (type === 'transfer_out') {
				return '转给: ' + otherUser;
			}
			return '';
		},
		
		viewDetail: function(e) {
			var id = e.currentTarget.dataset.id;
			app.goto('/pagesExt/winecoin/detail?id=' + id);
		},
		
		loaded: function() {
			this.loading = false;
		}
	}
}
</script>

<style>
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 筛选选项 */
.filter-tabs-container {
	margin: 20rpx;
	border-radius: 16rpx;
	background: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-tabs {
	display: flex;
	padding: 20rpx;
	white-space: nowrap;
	min-width: fit-content;
}

.filter-tab {
	flex: none;
	min-width: 120rpx;
	text-align: center;
	padding: 16rpx 24rpx;
	margin-right: 16rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	color: #64748B;
	transition: all 0.3s ease;
	white-space: nowrap;
}

.filter-tab:last-child {
	margin-right: 0;
}

.filter-tab.active {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	color: #fff;
	font-weight: 600;
}

/* 流水列表 */
.log-list {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.log-item {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}

.log-item:last-child {
	border-bottom: none;
}

.log-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: #F8FAFC;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.log-symbol {
	font-size: 32rpx;
	color: #64748B;
	font-weight: bold;
}

.log-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.log-title {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.title-text {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-right: 16rpx;
}

.other-user {
	color: #64748B;
	font-size: 24rpx;
	background: #F1F5F9;
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
}

.log-time {
	color: #64748B;
	font-size: 24rpx;
	margin-bottom: 4rpx;
}

.log-remark {
	color: #94A3B8;
	font-size: 22rpx;
	line-height: 1.4;
}

.log-amount {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.amount-value {
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.amount-value.positive {
	color: #10B981;
}

.amount-value.negative {
	color: #EF4444;
}

.balance-after {
	color: #64748B;
	font-size: 22rpx;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 32rpx;
	opacity: 0.6;
}

.empty-text {
	color: #64748B;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 16rpx;
}

.empty-desc {
	color: #94A3B8;
	font-size: 24rpx;
}

/* 加载更多 */
.load-more {
	text-align: center;
	padding: 40rpx;
	color: #64748B;
	font-size: 26rpx;
}
</style> 