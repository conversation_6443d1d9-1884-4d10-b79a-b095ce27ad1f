<template>
<view class="container">
	<block v-if="isload">
		<!-- 当前酒币余额 -->
		<view class="wine-balance" :style="{background: balanceBg}">
			<view class="balance-info">
				<view class="balance-label">可转账酒币</view>
				<view class="balance-amount">{{userinfo.wine_coin || '0.00'}}</view>
				<view class="balance-tips">今日已转账: {{transferSet.today_transferred || '0.00'}}</view>
			</view>
		</view>
		
		<!-- 转账表单 -->
		<view class="transfer-form">
			<!-- 收款人选择 -->
			<view class="form-section">
				<view class="section-title">收款人</view>
				<view class="recipient-selector" @tap="showUserSearch" v-if="!selectedUser">
					<text class="placeholder">点击选择收款人</text>
					<text class="iconfont iconjiantou"></text>
				</view>
				<view class="selected-user" v-if="selectedUser">
					<image class="user-avatar" :src="selectedUser.headimg || (pre_url + '/static/img/default-avatar.png')"></image>
					<view class="user-info">
						<text class="user-name">{{selectedUser.nickname}}</text>
						<text class="user-id">ID: {{selectedUser.id}}</text>
					</view>
					<view class="change-user" @tap="showUserSearch">
						<text class="iconfont iconbianji"></text>
					</view>
				</view>
			</view>
			
			<!-- 转账金额 -->
			<view class="form-section">
				<view class="section-title">转账金额</view>
				<view class="amount-input-box">
					<input 
						type="digit" 
						name="amount" 
						:value="transferAmount" 
						placeholder="请输入转账金额" 
						placeholder-style="color:#999;" 
						@input="onAmountInput"
					/>
					<text class="unit">酒币</text>
				</view>
				<view class="amount-tips">
					<text>最小转账: {{transferSet.min_amount}}酒币</text>
					<text>最大转账: {{transferSet.max_amount}}酒币</text>
				</view>
			</view>
			
			<!-- 转账备注 -->
			<view class="form-section">
				<view class="section-title">转账备注（可选）</view>
				<view class="remark-input-box">
					<textarea 
						name="remark" 
						:value="transferRemark" 
						placeholder="请输入转账备注" 
						placeholder-style="color:#999;" 
						@input="onRemarkInput"
						maxlength="100"
					></textarea>
				</view>
			</view>
			
			<!-- 转账说明 -->
			<view class="transfer-desc" v-if="transferSet.transfer_desc">
				<view class="desc-title">转账说明</view>
				<text class="desc-content">{{transferSet.transfer_desc}}</text>
			</view>
		</view>
		
		<!-- 转账按钮 -->
		<view class="transfer-actions">
			<view class="transfer-btn" @tap="goTransfer" :style="{background: btnBg}">
				确认转账
			</view>
		</view>
		
		<!-- 用户搜索弹窗 -->
		<view class="user-search-modal" v-if="showSearchModal" @tap="hideUserSearch">
			<view class="modal-content" @tap.stop="">
				<view class="modal-header">
					<text class="modal-title">选择收款人</text>
					<text class="modal-close" @tap="hideUserSearch">×</text>
				</view>
				
				<view class="search-box">
					<input 
						type="text" 
						:value="searchKeyword" 
						placeholder="输入用户ID、昵称或手机号" 
						@input="onSearchInput"
						@confirm="searchUsers"
					/>
					<view class="search-btn" @tap="searchUsers">
						<text class="iconfont iconsousuo"></text>
					</view>
				</view>
				
				<view class="search-results" v-if="searchResults.length > 0">
					<view 
						class="search-result-item" 
						v-for="(user, index) in searchResults" 
						:key="index"
						@tap="selectUser"
						:data-user="JSON.stringify(user)"
					>
						<image class="result-avatar" :src="user.headimg || (pre_url + '/static/img/default-avatar.png')"></image>
						<view class="result-info">
							<text class="result-name">{{user.nickname}}</text>
							<text class="result-id">ID: {{user.id}}</text>
							<text class="result-tel" v-if="user.tel">{{user.tel}}</text>
						</view>
					</view>
				</view>
				
				<view class="search-empty" v-if="searchKeyword && searchResults.length === 0 && !searchLoading">
					<text>未找到相关用户</text>
				</view>
				
				<view class="search-loading" v-if="searchLoading">
					<text>搜索中...</text>
				</view>
			</view>
		</view>
	</block>
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			pre_url: app.globalData.pre_url,
			userinfo: {},
			transferSet: {},
			selectedUser: null,
			transferAmount: '',
			transferRemark: '',
			showSearchModal: false,
			searchKeyword: '',
			searchResults: [],
			searchLoading: false,
			balanceBg: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
			btnBg: 'linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)'
		}
	},
	
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	
	onPullDownRefresh: function () {
		this.getdata();
	},
	
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			
			app.get('ApiWineCoin/transfer', {}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					that.isload = true;
					uni.setNavigationBarTitle({
						title: '酒币转账'
					});
					
					that.userinfo = res.userinfo;
					that.transferSet = res.transfer_set;
					
					// 检查是否允许转账
					if (that.transferSet.can_transfer == 0) {
						app.alert('当前不支持转账功能');
						return;
					}
					
					that.loaded();
				} else {
					app.error(res.msg || '获取数据失败');
				}
			});
		},
		
		showUserSearch: function() {
			this.showSearchModal = true;
			this.searchKeyword = '';
			this.searchResults = [];
		},
		
		hideUserSearch: function() {
			this.showSearchModal = false;
			this.searchKeyword = '';
			this.searchResults = [];
		},
		
		onSearchInput: function(e) {
			this.searchKeyword = e.detail.value;
		},
		
		searchUsers: function() {
			var keyword = this.searchKeyword.trim();
			if (!keyword) {
				app.error('请输入搜索关键词');
				return;
			}
			
			var that = this;
			that.searchLoading = true;
			
			app.get('ApiWineCoin/searchUser', {keyword: keyword}, function (res) {
				that.searchLoading = false;
				
				if (res.status == 1) {
					that.searchResults = res.users || [];
				} else {
					app.error(res.msg || '搜索失败');
				}
			});
		},
		
		selectUser: function(e) {
			var userStr = e.currentTarget.dataset.user;
			this.selectedUser = JSON.parse(userStr);
			this.hideUserSearch();
		},
		
		onAmountInput: function(e) {
			this.transferAmount = e.detail.value;
		},
		
		onRemarkInput: function(e) {
			this.transferRemark = e.detail.value;
		},
		
		goTransfer: function() {
			// 验证收款人
			if (!this.selectedUser) {
				app.error('请选择收款人');
				return;
			}
			
			// 验证金额
			var amount = parseFloat(this.transferAmount);
			if (!amount || amount <= 0) {
				app.error('请输入有效的转账金额');
				return;
			}
			
			if (amount < this.transferSet.min_amount) {
				app.error('转账金额不能低于' + this.transferSet.min_amount + '酒币');
				return;
			}
			
			if (amount > this.transferSet.max_amount) {
				app.error('转账金额不能超过' + this.transferSet.max_amount + '酒币');
				return;
			}
			
			// 验证余额
			var currentBalance = parseFloat(this.userinfo.wine_coin);
			if (amount > currentBalance) {
				app.error('酒币余额不足');
				return;
			}
			
			// 验证每日限额
			var todayTransferred = parseFloat(this.transferSet.today_transferred || 0);
			if (todayTransferred + amount > this.transferSet.daily_limit) {
				app.error('超过每日转账限额');
				return;
			}
			
			// 确认转账
			var that = this;
			uni.showModal({
				title: '确认转账',
				content: '向 ' + this.selectedUser.nickname + ' 转账 ' + amount + ' 酒币？',
				success: function(res) {
					if (res.confirm) {
						that.doTransfer();
					}
				}
			});
		},
		
		doTransfer: function() {
			var that = this;
			that.loading = true;
			
			var params = {
				to_user_id: that.selectedUser.id,
				amount: that.transferAmount,
				remark: that.transferRemark
			};
			
			app.post('ApiWineCoin/doTransfer', params, function (res) {
				that.loading = false;
				
				if (res.status == 1) {
					app.success('转账成功');
					// 刷新数据
					that.getdata();
					// 清空表单
					that.selectedUser = null;
					that.transferAmount = '';
					that.transferRemark = '';
				} else {
					app.error(res.msg || '转账失败');
				}
			});
		},
		
		loaded: function() {
			this.loading = false;
		}
	}
}
</script>

<style>
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 酒币余额显示 */
.wine-balance {
	padding: 40rpx 30rpx;
	margin: 20rpx;
	border-radius: 24rpx;
	color: #fff;
}

.balance-info {
	text-align: center;
}

.balance-label {
	font-size: 28rpx;
	opacity: 0.9;
	margin-bottom: 16rpx;
}

.balance-amount {
	font-size: 72rpx;
	font-weight: 700;
	margin-bottom: 16rpx;
}

.balance-tips {
	font-size: 24rpx;
	opacity: 0.8;
}

/* 转账表单 */
.transfer-form {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.form-section {
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}

.form-section:last-child {
	border-bottom: none;
}

.section-title {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 20rpx;
}

/* 收款人选择 */
.recipient-selector {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	border: 2rpx dashed #CBD5E1;
}

.placeholder {
	color: #64748B;
	font-size: 28rpx;
}

.selected-user {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	border: 2rpx solid #10B981;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.user-name {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 4rpx;
}

.user-id {
	color: #64748B;
	font-size: 24rpx;
}

.change-user {
	padding: 16rpx;
	color: #64748B;
	font-size: 32rpx;
}

/* 金额输入 */
.amount-input-box {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
}

.amount-input-box input {
	flex: 1;
	font-size: 48rpx;
	font-weight: 600;
	color: #1E293B;
}

.unit {
	color: #64748B;
	font-size: 28rpx;
	margin-left: 16rpx;
}

.amount-tips {
	display: flex;
	justify-content: space-between;
	color: #64748B;
	font-size: 24rpx;
}

/* 备注输入 */
.remark-input-box {
	background: #F8FAFC;
	border-radius: 12rpx;
	padding: 24rpx;
}

.remark-input-box textarea {
	width: 100%;
	min-height: 120rpx;
	font-size: 28rpx;
	color: #1E293B;
	line-height: 1.5;
}

/* 转账说明 */
.transfer-desc {
	padding: 24rpx;
	border-top: 1rpx solid #F1F5F9;
}

.desc-title {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
}

.desc-content {
	color: #64748B;
	font-size: 24rpx;
	line-height: 1.6;
}

/* 转账按钮 */
.transfer-actions {
	padding: 40rpx 20rpx;
}

.transfer-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
}

/* 用户搜索弹窗 */
.user-search-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	width: 90%;
	max-height: 80%;
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}

.modal-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
}

.modal-close {
	color: #64748B;
	font-size: 48rpx;
	padding: 8rpx;
}

/* 搜索框 */
.search-box {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}

.search-box input {
	flex: 1;
	padding: 20rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	font-size: 28rpx;
	margin-right: 16rpx;
}

.search-btn {
	width: 72rpx;
	height: 72rpx;
	background: #3B82F6;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 32rpx;
}

/* 搜索结果 */
.search-results {
	max-height: 400rpx;
	overflow-y: scroll;
}

.search-result-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}

.search-result-item:last-child {
	border-bottom: none;
}

.result-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.result-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.result-name {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 4rpx;
}

.result-id {
	color: #64748B;
	font-size: 24rpx;
	margin-bottom: 4rpx;
}

.result-tel {
	color: #94A3B8;
	font-size: 22rpx;
}

/* 搜索状态 */
.search-empty,
.search-loading {
	text-align: center;
	padding: 80rpx 40rpx;
	color: #64748B;
	font-size: 28rpx;
}
</style> 