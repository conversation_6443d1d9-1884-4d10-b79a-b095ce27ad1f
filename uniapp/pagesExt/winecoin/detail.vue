<template>
<view class="container">
	<block v-if="isload">
		<!-- 交易基本信息 -->
		<view class="main-card">
			<view class="transaction-header">
				<view class="transaction-icon" :class="iconClass">
					<text class="transaction-symbol">{{transactionSymbol}}</text>
				</view>
				<view class="transaction-info">
					<text class="transaction-title">{{detail.type_desc}}</text>
					<text class="transaction-time">{{detail.createtime}}</text>
				</view>
				<view class="transaction-amount" :class="amountClass">
					<text class="amount-value">{{amountText}}</text>
					<text class="amount-unit">酒币</text>
				</view>
			</view>
		</view>

		<!-- 交易详情 -->
		<view class="detail-card">
			<view class="card-title">交易详情</view>
			<view class="detail-list">
				<view class="detail-item">
					<text class="label">交易金额</text>
					<text class="value" :class="amountClass">
						{{amountText}} 酒币
					</text>
				</view>
				<view class="detail-item">
					<text class="label">交易后余额</text>
					<text class="value">{{detail.balance_after}} 酒币</text>
				</view>
				<view class="detail-item">
					<text class="label">交易时间</text>
					<text class="value">{{detail.createtime}}</text>
				</view>
				<view class="detail-item" v-if="detail.remark">
					<text class="label">备注说明</text>
					<text class="value">{{detail.remark}}</text>
				</view>
			</view>
		</view>

		<!-- 对方用户信息 (转账时显示) -->
		<view class="detail-card" v-if="otherUser">
			<view class="card-title">{{transferUserTitle}}</view>
			<view class="user-info">
				<image class="user-avatar" :src="userAvatar" mode="aspectFill"></image>
				<view class="user-details">
					<text class="user-name">{{otherUser.nickname}}</text>
					<text class="user-id">ID: {{otherUser.id}}</text>
				</view>
			</view>
		</view>

		<!-- 关联信息 -->
		<view class="detail-card" v-if="relationInfo">
			<view class="card-title">{{relationInfo.type_name}}</view>
			
			<!-- 订单信息 -->
			<view class="relation-content" v-if="relationInfo.type == 'order'">
				<view class="detail-list">
					<view class="detail-item">
						<text class="label">订单号</text>
						<text class="value">{{relationInfo.data.order_sn}}</text>
					</view>
					<view class="detail-item">
						<text class="label">商品名称</text>
						<text class="value">{{relationInfo.data.product_name}}</text>
					</view>
					<view class="detail-item">
						<text class="label">订单金额</text>
						<text class="value">¥{{relationInfo.data.amount}}</text>
					</view>
					<view class="detail-item" v-if="relationInfo.data.pay_time">
						<text class="label">支付时间</text>
						<text class="value">{{relationInfo.data.pay_time}}</text>
					</view>
					<view class="detail-item">
						<text class="label">订单状态</text>
						<text class="value status">{{relationInfo.data.status}}</text>
					</view>
				</view>
				<view class="action-btn" @tap="viewOrder" :data-id="relationInfo.data.order_id">
					查看订单详情
				</view>
			</view>

			<!-- 充值订单信息 -->
			<view class="relation-content" v-if="relationInfo.type == 'recharge'">
				<view class="detail-list">
					<view class="detail-item">
						<text class="label">充值订单号</text>
						<text class="value">{{relationInfo.data.order_sn}}</text>
					</view>
					<view class="detail-item">
						<text class="label">充值金额</text>
						<text class="value">{{relationInfo.data.amount}} 酒币</text>
					</view>
					<view class="detail-item">
						<text class="label">支付方式</text>
						<text class="value">{{relationInfo.data.pay_type}}</text>
					</view>
					<view class="detail-item">
						<text class="label">状态</text>
						<text class="value status">{{relationInfo.data.status}}</text>
					</view>
				</view>
			</view>

			<!-- 提现信息 -->
			<view class="relation-content" v-if="relationInfo.type == 'withdraw'">
				<view class="detail-list">
					<view class="detail-item">
						<text class="label">提现金额</text>
						<text class="value">{{relationInfo.data.amount}} 酒币</text>
					</view>
					<view class="detail-item">
						<text class="label">手续费</text>
						<text class="value">{{relationInfo.data.fee}} 酒币</text>
					</view>
					<view class="detail-item">
						<text class="label">实际到账</text>
						<text class="value">{{relationInfo.data.actual_amount}} 酒币</text>
					</view>
					<view class="detail-item">
						<text class="label">状态</text>
						<text class="value status">{{relationInfo.data.status}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 帮助说明 -->
		<view class="help-card">
			<view class="help-title">💡 温馨提示</view>
			<view class="help-content">
				<text>• 酒币交易记录将永久保存，可随时查询</text>
				<text>• 如有疑问，请联系客服获取帮助</text>
				<text>• 转账和提现操作不可撤销，请谨慎操作</text>
			</view>
		</view>
	</block>
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp()

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			detail: {},
			relationInfo: null,
			otherUser: null,
			defaultAvatar: app.globalData.pre_url + '/static/img/default_avatar.png'
		}
	},

	computed: {
		transactionSymbol() {
			return this.getTransactionSymbol(this.detail.type || '');
		},
		iconClass() {
			return this.getIconClass(this.detail.type || '');
		},
		amountClass() {
			return this.detail.amount >= 0 ? 'positive' : 'negative';
		},
		amountText() {
			const amount = this.detail.amount || 0;
			return (amount >= 0 ? '+' : '') + amount;
		},
		transferUserTitle() {
			return this.detail.type == 'transfer_in' ? '转账方' : '收款方';
		},
		userAvatar() {
			return (this.otherUser && this.otherUser.headimg) ? this.otherUser.headimg : this.defaultAvatar;
		}
	},

	onLoad: function(option) {
		this.id = option.id || 0;
		if (!this.id) {
			app.error('参数错误');
			return;
		}
		this.loadData();
	},

	onPullDownRefresh: function() {
		this.loadData();
	},

	methods: {
		loadData: function() {
			var that = this;
			that.loading = true;

			app.get('ApiWineCoin/detail', {id: that.id}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();

				if (res.status == 1) {
					that.isload = true;
					uni.setNavigationBarTitle({
						title: '交易详情'
					});
					
					that.detail = res.log;
					that.relationInfo = res.relation_info;
					that.otherUser = res.other_user;
					
					that.loaded();
				} else {
					app.error(res.msg || '获取详情失败');
				}
			});
		},

		getTransactionSymbol: function(type) {
			var symbolMap = {
				'recharge': '+',
				'withdraw': '-',
				'transfer_in': '⬅',
				'transfer_out': '➡',
				'payment': '💳',
				'refund': '↩',
				'fee': '💰',
				'business_income': '🏪',
				'bonus': '🎁',
				'penalty': '⚠️',
				'adjustment': '🔧',
				'manual': '✋',
				'activity': '🎉',
				'settlement': '📊',
				'withdraw_return': '↺',
				'business_recharge': '🏢',
				'business_expense': '🏢',
				'commission': '💎',
				'gift': '🎁',
				'compensation': '💊',
				'admin_recharge': '⭐',
				'admin_deduct': '⚠',
				'activity_reward': '🏆',
				'consume': '🛒',
				'order': '📦'
			};
			return symbolMap[type] || '📝';
		},

		getIconClass: function(type) {
			if (['recharge', 'transfer_in', 'bonus', 'gift', 'commission', 'activity_reward', 'compensation', 'refund'].includes(type)) {
				return 'positive';
			} else if (['withdraw', 'transfer_out', 'payment', 'fee', 'penalty', 'admin_deduct'].includes(type)) {
				return 'negative';
			}
			return 'neutral';
		},

		viewOrder: function(e) {
			var orderId = e.currentTarget.dataset.id;
			if (orderId) {
				app.goto('/pages/order/detail?id=' + orderId);
			}
		},

		loaded: function() {
			this.loading = false;
		}
	}
}
</script>

<style>
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 主卡片 */
.main-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx;
	color: #fff;
}

.transaction-header {
	display: flex;
	align-items: center;
}

.transaction-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 32rpx;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
}

.transaction-icon.positive {
	background: rgba(16, 185, 129, 0.3);
}

.transaction-icon.negative {
	background: rgba(239, 68, 68, 0.3);
}

.transaction-icon.neutral {
	background: rgba(156, 163, 175, 0.3);
}

.transaction-symbol {
	font-size: 56rpx;
	font-weight: bold;
}

.transaction-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.transaction-title {
	font-size: 36rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.transaction-time {
	font-size: 28rpx;
	opacity: 0.8;
}

.transaction-amount {
	text-align: right;
}

.amount-value {
	display: block;
	font-size: 48rpx;
	font-weight: 700;
	line-height: 1;
}

.amount-unit {
	font-size: 24rpx;
	opacity: 0.8;
	margin-top: 8rpx;
}

/* 详情卡片 */
.detail-card {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.card-title {
	background: #F8FAFC;
	padding: 24rpx 32rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #334155;
	border-bottom: 1rpx solid #E2E8F0;
}

.detail-list {
	padding: 0 32rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 0;
	border-bottom: 1rpx solid #F1F5F9;
}

.detail-item:last-child {
	border-bottom: none;
}

.label {
	font-size: 28rpx;
	color: #64748B;
}

.value {
	font-size: 28rpx;
	color: #1E293B;
	text-align: right;
	flex: 1;
	margin-left: 32rpx;
}

.value.positive {
	color: #10B981;
	font-weight: 600;
}

.value.negative {
	color: #EF4444;
	font-weight: 600;
}

.value.status {
	background: #E0F2FE;
	color: #0369A1;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
}

/* 用户信息 */
.user-info {
	display: flex;
	align-items: center;
	padding: 32rpx;
}

.user-avatar {
	width: 96rpx;
	height: 96rpx;
	border-radius: 50%;
	margin-right: 24rpx;
}

.user-details {
	display: flex;
	flex-direction: column;
}

.user-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 8rpx;
}

.user-id {
	font-size: 24rpx;
	color: #64748B;
}

/* 关联内容 */
.relation-content {
	padding-bottom: 32rpx;
}

.action-btn {
	margin: 32rpx;
	margin-top: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	text-align: center;
	padding: 24rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 600;
}

/* 帮助卡片 */
.help-card {
	background: #FEF3C7;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	border: 1rpx solid #FDE68A;
}

.help-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #92400E;
	margin-bottom: 16rpx;
}

.help-content {
	display: flex;
	flex-direction: column;
}

.help-content text {
	font-size: 24rpx;
	color: #A16207;
	line-height: 1.6;
	margin-bottom: 8rpx;
}

.help-content text:last-child {
	margin-bottom: 0;
}
</style> 