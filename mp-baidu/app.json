{"pages": ["pages/index/index", "pages/index/main", "pages/index/webView", "pages/index/webView2", "pages/index/reg", "pages/index/login", "pages/shop/product", "pages/shop/cart", "pages/shop/prolist", "pages/shop/search", "pages/shop/category1", "pages/shop/category2", "pages/shop/category3", "pages/shop/category4", "pages/shop/classify", "pages/shop/fastbuy", "pages/shop/fastbuy2", "pages/shop/mendian", "pages/my/usercenter", "pages/my/levelinfo", "pages/my/levelup", "pages/my/scorelog", "pages/my/favorite", "pages/my/history", "pages/my/set", "pages/pay/pay", "pages/pay/daifu", "pages/pay/transfer", "pages/order/orderlist", "pages/money/withdraw", "pages/money/recharge", "pages/coupon/couponlist", "pages/coupon/mycoupon", "pages/coupon/coupongive", "pages/shop/classify2", "pages/shop/buy", "pages/address/address", "pages/kefu/index", "pages/index/getpwd", "pages/maidan/pay", "pages/maidan/maidanlog", "pages/maidan/maidandetail"], "subPackages": [{"root": "activity", "pages": ["commission/index", "commission/bindex", "commission/commissionlog", "commission/commissionlog<PERSON><PERSON>ian", "commission/order<PERSON><PERSON><PERSON>", "commission/withdraw", "commission/myteam", "commission/downorder", "commission/poster", "commission/fhlog", "commission/fhorder", "commission/fenhong", "commission/teamfenhong", "commission/areafenhong", "commission/touzifenhong", "commission/order<PERSON>eji", "commission/myteamline", "commission/mysameline", "commission/commissionrecord", "commission/commissionranking", "commission/signature", "scoreshop/index", "scoreshop/prolist", "scoreshop/product", "scoreshop/cart", "scoreshop/buy", "scoreshop/orderlist", "scoreshop/orderdetail", "scoreshop/refund", "kanjia/index", "kanjia/product", "kanjia/join", "kanjia/helplist", "kanjia/buy", "kanjia/orderlist", "kanjia/orderdetail", "kanjia/refund", "collage/index", "collage/product", "collage/team", "collage/buy", "collage/orderlist", "collage/orderdetail", "collage/comment", "collage/commentlist", "collage/refund", "seckill/index", "seckill/product", "seckill/buy", "seckill/orderlist", "seckill/orderdetail", "seckill/comment", "seckill/commentlist", "seckill/refund", "tuangou/prolist", "tuangou/product", "tuangou/buy", "tuang<PERSON>/orderlist", "tuangou/orderdetail", "tuangou/comment", "tuangou/commentlist", "tuangou/refund", "xydzp/index", "xydzp/myprize", "ggk/index", "ggk/myprize", "luntan/index", "luntan/ltlist", "luntan/detail", "luntan/fatie", "luntan/pinglun", "luntan/fatielog", "luntan/focuslog", "peisong/dating", "peisong/orderlist", "peisong/orderdetail", "peisong/my", "peisong/moneylog", "peisong/withdraw", "peisong/setinfo", "yuyue/product", "yuyue/product2", "yuyue/prolist", "yuyue/orderlist", "yuyue/orderdetail", "yuyue/peolist", "yuyue/peolist2", "yuyue/j<PERSON>derlist", "yuyue/jdorderdetail", "yuyue/my", "yuyue/dating", "yuyue/login", "yuyue/apply", "shortvideo/index", "shortvideo/detail", "shortvideo/uploadvideo", "shortvideo/myupload", "kecheng/list", "kecheng/product", "kecheng/mldetail", "kecheng/orderlist", "kecheng/tiku", "kecheng/complete", "kecheng/recordlog", "kecheng/error", "kecheng/category3", "luckycollage/classify", "luckycollage/prolist", "luckycollage/product", "luckycollage/team", "luckycollage/buy", "luckycollage/orderlist", "luckycollage/orderdetail", "luckycollage/comment", "luckycollage/commentlist", "luckycollage/refund", "luckycollage/index", "luckycollage/product2", "toupiao/index", "toupiao/detail", "toupiao/phb", "toupiao/baoming", "toupiao/shuoming", "yuebao/yuebaolog", "y<PERSON><PERSON>/withdraw", "yx/kouling", "yx/riddle", "yx/jidian", "workorder/index", "workorder/detail", "workorder/record", "express/index", "express/mail", "express/addressadd", "express/address", "express/logistics", "express/kddetail"]}, {"root": "pagesExt", "pages": ["my/levelinfo", "my/levelup", "my/scorelog", "my/scoreTransfer", "my/scoreWithdraw", "my/gongxianLog", "my/favorite", "my/history", "my/paypwd", "my/set", "my/setpwd", "my/setnickname", "my/setrealname", "my/settel", "my/setsex", "my/setbirthday", "my/setaliaccount", "my/setbankinfo", "my/sethuifuinfo", "my/bscore", "my/bscorelog", "my/setother", "my/setadapayinfo", "money/moneylog", "money/withdraw", "money/recharge", "money/rechargeToMember", "order/orderlist", "order/refundlist", "order/refundDetail", "order/comment", "order/commentdp", "order/commentps", "order/detail", "order/logistics", "order/refundSelect", "order/refund", "order/invoice", "business/index", "business/main", "business/clist", "business/blist", "business/apply", "business/commentlist", "business/clist2", "business/mendian", "lipin/index", "lipin/prodh", "lipin/dhlog", "sign/index", "sign/signrecord", "agent/card", "agent/cardEdit", "shop/imgsearch", "shop/imgsearchList", "shop/diylight", "coupon/record", "coupon/couponlist", "coupon/mycoupon", "coupon/coupondetail", "coupon/coupongive", "pay/pay", "pay/daifu", "pay/transfer", "yuanbao/yuanbaolog", "yuanbao/yuanbaoTransfer", "renovation/form", "renovation/result", "counsel/index", "counsel/detail", "legal/index", "legal/detail", "invite_free/index", "cycle/product", "cycle/planDetail", "cycle/buy", "cycle/checkDate", "cycle/planList", "cycle/orderList", "cycle/orderDetail", "cycle/planWrite", "cycle/logistics", "cycle/refund", "cycle/comment", "cycle/commentps", "cycle/commentlist", "cycle/prolist", "yueke/prolist", "yueke/product", "yueke/workerinfo", "yueke/buy", "yueke/orderlist", "yueke/orderdetail", "yueke/workerlogin", "yueke/workerorderlist", "yueke/workerorderdetail", "mingpian/index", "mingpian/edit", "mingpian/favorite", "mingpian/readlog", "mingpian/favoritelog", "archives/index", "archives/detail", "othermoney/moneylog", "othermoney/withdraw", "othermoney/frozen_moneylog", "glass/index", "glass/add", "glass/set", "fifa/index", "fifa/detail", "project/record", "project/myrecord", "project/detail", "my/fuchi", "certificate_poster/index", "certificate_poster/record", "certificate_poster/detail", "hbtk/index", "hbtk/orderlist", "hbtk/orderdetail", "paotui/index", "paotui/address", "paotui/orderlist", "paotui/orderdetail", "article/artlist", "article/detail", "article/pinglun", "article/record", "article/fujian", "article/show", "luntan/class", "luntan/class2", "luntan/list", "commission/fenhongranking", "commission/myyeji", "commission/teamyeji", "commission/tjbusinessList", "mapmark/edit", "mapmark/detail", "videospider/analysis", "videospider/detail", "imgai/detail", "imgai/create", "checkdate/checkDate", "winecoin/index", "winecoin/recharge", "winecoin/transfer", "winecoin/log", "winecoin/detail"]}, {"root": "pagesZ", "pages": ["order/refundDetail", "cerberuse/index", "cerberuse/detail", "cerberuse/buy", "cerberuse/orderlist", "cerberuse/orderdetail", "cerberuse/refund"]}, {"root": "admin", "pages": ["index/login", "index/index", "index/setpage", "index/setnotice", "index/setpwd", "index/setinfo", "index/recharge", "hexiao/hexiao", "hexiao/record", "hexiao/recordgroup", "finance/index", "finance/commissionlog", "finance/comwithdrawdetail", "finance/comwithdrawlog", "finance/moneylog", "finance/scorelog", "finance/bscorelog", "finance/rechargelog", "finance/withdrawdetail", "finance/withdrawlog", "finance/bmoneylog", "finance/bwithdraw", "finance/bwithdrawlog", "finance/txset", "finance/yuebaowithdrawlog", "finance/yuebaowithdrawdetail", "finance/yuebaolog", "finance/mdmoneylog", "finance/mdwithdraw", "finance/mdwithdrawlog", "finance/mdtxset", "couponmoney/record", "couponmoney/withdraw", "couponmoney/withdrawlog", "kefu/index", "kefu/message", "member/index", "member/detail", "member/richinfo", "member/history", "member/code", "member/codebuy", "order/collageorder", "order/collageorderdetail", "order/cycleorder", "order/cycleorderdetail", "order/cycleplanlist", "order/cycleplandetail", "order/luckycollageorder", "order/luckycollageorderdetail", "order/kanjiaorder", "order/kanjiaorderdetail", "order/seckillorder", "order/seckillorderdetail", "order/yuyueorder", "order/yuyueorderdetail", "order/scoreshoporder", "order/scoreshoporderdetail", "order/shoporder", "order/shoporderdetail", "order/shopRefundOrder", "order/shopRefundOrderDetail", "order/weightOrderFahuo", "order/tuangouorder", "order/tuangouorderdetail", "order/yuekeorder", "order/yuekeorderdetail", "order/dkorder", "order/dkaddress", "order/dkaddressadd", "order/dkfastbuy", "order/dksearch", "order/maidanlog", "order/addmember", "workorder/category", "workorder/record", "workorder/formlog", "workorder/formdetail", "workorder/myformdetail", "workorder/jindu", "workorder/updatecate", "yuyue/selectworker", "form/formlog", "form/formdetail", "shortvideo/uploadvideo", "shortvideo/myupload", "product/edit", "product/index", "product/category2/index", "product/category2/edit", "index/businessqr", "scoreproduct/edit", "scoreproduct/index", "restaurant/product/edit", "restaurant/product/index", "restaurant/category/edit", "restaurant/category/index", "restaurant/tableEdit", "restaurant/table", "restaurant/tableWaiter", "restaurant/tableWaiterDetail", "restaurant/tableWaiterPay", "restaurant/tableCategoryEdit", "restaurant/tableCategory", "restaurant/takeawayorder", "restaurant/takeawayorderdetail", "restaurant/shoporder", "restaurant/shoporderdetail", "restaurant/shoporderEdit", "restaurant/bookingorder", "restaurant/bookingorderdetail", "restaurant/depositorder", "restaurant/depositorderdetail", "restaurant/booking", "restaurant/bookingTableList", "restaurant/queue", "restaurant/queueCategory", "restaurant/queueCategoryEdit", "health/record", "health/recordlog", "health/result", "yingxiao/queueFree", "product/editstock", "business/index"]}, {"root": "adminExt", "pages": ["order/maidanlog", "order/maidandetail", "order/maidanindex", "mendian/list", "mendian/detail", "mendian/withdrawlog", "mendian/withdrawdetail", "hotel/orderlist", "hotel/orderdetail", "hotel/refundyajin", "hotel/refundyajinDetail", "huodongbaoming/order", "huodongbaoming/orderdetail", "shop/shopstock", "shop/shopstockgoods", "coupon/index", "coupon/edit", "coupon/prolist", "coupon/restaurantList", "coupon/category", "bonuspoolgold/goldlog", "bonuspoolgold/goldwithdraw", "finance/transfermendianmoney", "set/qrcodeShop", "queuefree/queueFreeSet"]}, {"root": "restaurant", "pages": ["takeaway/blist", "takeaway/index", "takeaway/product", "takeaway/commentlist", "takeaway/buy", "takeaway/orderlist", "takeaway/comment", "takeaway/commentdp", "takeaway/commentps", "takeaway/orderdetail", "takeaway/logistics", "takeaway/refund", "takeaway/search", "shop/index", "shop/search", "shop/product", "shop/commentlist", "shop/buy", "shop/enterorder", "shop/orderlist", "shop/comment", "shop/commentdp", "shop/commentps", "shop/orderdetail", "shop/logistics", "shop/refund", "shop/taocan", "booking/add", "booking/tableList", "booking/orderlist", "booking/detail", "queue/index", "queue/quhao", "queue/record", "deposit/orderlog", "deposit/orderdetail", "deposit/add", "admin/quhao", "admin/outfood", "cashdesk/bind"]}, {"root": "carhailing", "pages": ["prolist", "product", "buy", "orderlist", "orderdetail", "refund", "classify"]}, {"root": "pagesA", "pages": ["commission/baodanfreezelog", "tour/index", "tour/product", "tour/buy", "tour/orderlist", "tour/orderdetail", "tour/refund", "tour/apply", "tour/tuiguang", "tour/lookupimg", "tour/upimg", "tour/lookalbum", "business/poster", "rechargeyj/log", "rechargeyj/withdraw", "rechargeyj/index", "my/memberCode", "health/index", "health/main", "health/question", "health/recordlog", "health/result", "health/record", "giftbag/index", "giftbag/list", "giftbag/detail", "giftbag/buy", "giftbag/orderlist", "giftbag/orderdetail", "shortvideo/looklog", "sxpay/apply", "sxpay/myapply", "friend/index", "friend/add", "friend/scan", "friend/search", "friend/transferlog", "score/transfer", "yuyuecar/car", "yuyuecar/caradd", "yuyuecar/uppic", "qrcode/index", "agent/priceRate", "agent/memberPriceRate", "shop/productComment", "commission/teamshouyi", "my/xiaofeimoneylog", "my/invitecashbacklog", "my/fhcopieslog", "bonuspool/withdraw", "bonuspool/commissionlog", "formdata/index", "formdata/detail", "cashier/orderlist", "cashier/orderdetail", "workorder/category", "workorder/detail", "workorder/workjindu", "overdraft/moneylog", "overdraft/recharge", "overdraft/detail", "xianxia/mycoupon", "xianxia/myteam", "xianxia/commissionlog", "xianxia/commissionloginfo", "xianxia/membercommissionlog", "xianxia/objection", "regionpartner/apply", "regionpartner/partner", "regionpartner/fhlog", "dscj/index", "dscj/prize", "dscj/myrecord", "hongbaoEveryday/index", "hongbaoEveryday/log", "hongbaoEveryday/eduLog", "hongbaoEveryday/withdraw", "cashback/index", "cashback/recordlog", "my/withdrawXiaoetong", "shop/addressorder", "searchmember/searchmember", "searchmember/memberdetail", "searchmember/couponrecord", "searchmember/couponused", "searchmember/jdorderlist", "searchmember/jdorderdetail", "searchmember/formlog", "searchmember/formdetail", "certificate/index", "certificate/edit", "certificate/detail", "certificate/mylist", "commission/regionagent", "commission/ranking", "commission/rankingdetail", "commission/rankingorder", "hikvision/detail", "hikvision/hikvisionlist", "businessFenxiao/mendian", "businessFenxiao/shuju", "businessFenxiao/mingxi", "businessFenxiao/mendianlist", "businessFenxiao/bonuslog", "commission/fenhong_huiben", "queueFree/index", "w", "mendian/list", "commission/business_teamfenhong", "handwork/handlist", "handwork/handDetail", "handwork/hand", "handwork/list", "baomingxcx/index", "livepay/mobile_recharge", "livepay/ordinary_recharge", "livepay/record_recharge", "levelupauth/salelist", "levelupauth/give", "levelupauth/buy", "levelupauth/buyorderlist", "levelupauth/orderdetail", "levelupauth/saleorderlist", "levelupauth/leveldetail", "commission/commissionbutie", "commission/commissionbutielog", "score/scoreranking", "my/tongzhenglog", "my/tongzhengreleaselog", "my/tongzhengtransfer", "my/tongzhengorderlog", "lottery/index", "lottery/myprize", "giftpack/list", "giftpack/detail", "giftpack/orderlist", "giftpack/orderdetail", "banklist/bank", "banklist/bankadd", "douyin_groupbuy/index", "douyin_groupbuy/selmendian", "douyin_groupbuy/buy", "taocan/index", "taocan/product", "taocan/libao", "taocan/buy", "taocan/orderlist", "taocan/orderdetail", "taocan/refund", "taocan/comment", "helpnew/lists", "helpnew/detail", "taocan/fenhong", "form/formlog", "form/formlog2", "form/formdetail", "mendianup/apply", "business/businessindex", "mendiancenter/hexiaouser", "mendiancenter/addhexiao", "mendiancenter/addhexiaouser", "mendiancenter/orderlist", "mendiancenter/orderdetail", "mendiancenter/moneylog", "mendiancenter/withdraw", "mendiancenter/withdrawlog", "mendiancenter/hxorderlist", "mendiancenter/mdtxset", "mendiancenter/set", "mendiancenter/hexiao", "mendiancenter/my"]}, {"root": "pagesB", "pages": ["shop/classify2", "shop/buy", "mendianup/list", "mendianup/searchlist", "posscore/detail", "pai<PERSON><PERSON><PERSON>/withdraw", "paimingfenhong/user", "my/commissionwithdrawscorelog", "huo<PERSON><PERSON>ming/prolist", "huodongbaoming/prolist2", "huodongbaoming/product", "huo<PERSON>baoming/proorderlist", "huodongbaoming/buy", "huo<PERSON><PERSON><PERSON>/orderlist", "huodongbaoming/orderdetail", "daily/daily", "daily/wqlist", "businessmiandan/orderlist", "shoporderranking/index", "shoporderranking/log", "shoporderranking/detail", "order/uploadcard", "shop/categorypage", "membercard/receive", "membercard/share", "membercard/jiangli", "collage/jtteam", "team/teamyjranking", "sign/pmlist", "mendianup/fahuolog", "mendianup/fahuoinfo", "mendianup/hexiaolog", "form/formlist", "kecheng/classify", "ciruikang/stocklog", "service_fee/recharge", "service_fee/servicefee_log", "form/submember", "pay/pay", "my/rewardToMember", "my/mangfanlog", "my/freezecreditlog", "my/freezeexchange", "my/exchangelog", "my/greenscorelog", "my/greenscoreWithdraw", "greenscore/greenscorelognew", "greenscore/commissiontogreenscore", "greenscore/hongbao", "admin/tradereport", "business/scorewithdraw", "business/scorewithdrawlog", "business/mybusiness", "channels_live/index", "admin/pickupdevice", "admin/pickupdevicegoods", "admin/pickupdeviceaddstock", "admin/pickupdeviceaddstocklog", "wxchannels/downorder", "my/activecoinlog", "collage/classify", "collage/prolist", "my/scoreweightlog", "my/commissionmaxtoscore", "my/commissionmaxlog", "my/shopfavorite", "my/staffcommissionlog", "yuyue/selectbusiness", "fishpond/list", "fishpond/detail", "fishpond/buy", "fishpond/orderlist", "fishpond/orderdetail", "fishpond/savebasan", "fishpond/refund", "message", "teamsaleyeji/fhorderlist", "purchaseorder/index", "purchaseorder/posterpurchaseorder", "purchaseorder/sharerecord", "workorder/index", "workorder/detail", "workorder/record", "express/index", "express/mail", "express/addressadd", "express/address", "express/logistics", "express/kddetail", "my/silvermoneylog", "my/goldmoneylog", "admin/depositlog", "admin/expend", "material/index", "material/detail", "admin/expendEdit", "my/linghuoxinsign", "address/address", "address/addressadd", "kefu/index", "index/getpwd", "index/bind", "kecheng/lecturerapply", "kecheng/lecturercenter", "kecheng/lecturermldetail", "kecheng/lecturersend", "kecheng/lecturerorderlist", "my/setpaycode", "scoreshop/classify2", "kecheng/lecturerlist", "maidan/pay", "maidan/maidanlog", "maidan/maidandetail", "shop/commentlist", "inviteredpacket/redpacketlist", "inviteredpacket/redpacketdetail", "yueke/studyrecord", "yueke/workerstudyrecord", "yueke/worker<PERSON><PERSON>e", "yueke/refund", "yueke/apply", "yueke/my", "yueke/moneylog", "yueke/setpwd", "yueke/setinfo", "yueke/withdraw", "wx/mpbind", "certificatequery/index", "certificatequery/record", "teamyejitongji/myteam", "teamyejitongji/goodsinfo", "form/signature", "wxchannels/sharerapply", "wxchannels/sharercenter", "wxchannels/sharercommissionlog", "wxchannels/sharercommissionrecord", "wxchannels/sharerwithdraw", "my/scorefreezelog", "mingpian/checkapplylog", "my/memberscorewithdraw", "my/setcustomaccount"]}, {"root": "hotel", "pages": ["index/index", "index/hotellist", "index/hoteldetails", "index/buy", "index/signature", "order/orderlist", "order/orderdetail", "order/comment", "order/refund", "order/commentlist"]}, {"root": "pagesC", "pages": ["productthali/index", "productthali/detail", "productthali/buy", "productthali/orderlist", "productthali/orderdetail", "productthali/refund", "transferorderparent/orderlist", "transferorderparent/orderdetail", "invoicebaoxiao/index", "invoicebaoxiao/recordlist", "invoicebaoxiao/recorddetail", "invoicebaoxiao/adminrecordlist", "invoicebaoxiao/adminrecorddetail", "invoicebaoxiao/adminedit", "my/saveinfo", "allinpay/yunstMember", "bonuspoolgold/bonuspool", "bonuspoolgold/withdraw", "bonuspoolgold/buygold", "bonuspoolgold/goldlog", "registervehicle/registerVehicle", "registervehicle/systemMsg", "registervehicle/msgDetails", "registervehicle/vehicleList", "registervehicle/vehicleSettime", "mingpian/list", "qrcodevar/index", "qrcodevar/bindlog", "releasePoints/details", "my/dedamountlog", "releasePoints/releasegreenscore", "manrenchoujiang/list", "manrenchoujiang/details", "transferorderparent/tongji", "releasePoints/cashbacklog", "releasePoints/ogdetails", "my/shopscorelog", "commission/teamfenhongfreight", "mendian/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mendian/memberleveluplist", "shop/brandlist"]}, {"root": "yuyue", "pages": ["yuyue/selectpeople", "yuyue/product", "yuyue/product2", "yuyue/prolist", "yuyue/buy", "yuyue/buy2", "yuyue/orderlist", "yuyue/orderdetail", "yuyue/logistics", "yuyue/comment", "yuyue/commentps", "yuyue/commentlist", "yuyue/peolist", "yuyue/peolist2", "yuyue/peodetail", "yuyue/peodetail2", "yuyue/j<PERSON>derlist", "yuyue/jdorderdetail", "yuyue/my", "yuyue/dating", "yuyue/moneylog", "yuyue/search", "yuyue/refund", "yuyue/setinfo", "yuyue/withdraw", "yuyue/login", "yuyue/setpwd", "yuyue/apply", "yuyue/sets"]}], "window": {"navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#F8F8F8", "navigationBarTitleText": ""}, "usingComponents": {}}